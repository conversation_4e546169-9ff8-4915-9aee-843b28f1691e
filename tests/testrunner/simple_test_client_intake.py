"""
Simplified tests for the client intake functionality.
This test suite covers the `create_client_intake` Supabase function without extra
dependencies.
"""
import os
import pytest
from datetime import datetime
from dotenv import load_dotenv
from supabase import create_client

# Load environment variables
load_dotenv()

# Supabase setup
SUPABASE_URL = os.getenv("NEXT_PUBLIC_SUPABASE_URL")
SUPABASE_KEY = os.getenv("NEXT_PUBLIC_SUPABASE_ANON_KEY")


@pytest.fixture
def supabase_client():
    """Create a Supabase client for testing."""
    if not SUPABASE_URL or not SUPABASE_KEY:
        pytest.skip("Supabase credentials not available")
    client = create_client(SUPABASE_URL, SUPABASE_KEY)
    if hasattr(client, "schema"):
        client.schema("tenants")
    return client


def generate_test_client(overrides=None):
    """Generate test client data."""
    timestamp = datetime.now().timestamp()
    data = {
        "first_name": f"Test{timestamp}",
        "last_name": f"Client{timestamp}",
        "date_of_birth": "1980-01-01",
        "email": f"test.client.{timestamp}@example.com",
        "phone_primary": f"512-555-{int(1000 + timestamp % 9000)}",
        "address": {
            "street": "123 Test St",
            "city": "Test City",
            "state": "TX",
            "zip": "78701",
        },
        "occupation": "Software Tester",
        "employer": "Test Inc.",
        "work_status": "full_time",
        "status": "active",
        "client_type": "individual",  # Added required field
        "intake_date": datetime.now().strftime("%Y-%m-%d"),
        "conflict_check_status": "pending",
        "tenant_id": "f5a731c2-9c0b-4ef8-bb6d-6bb9bd380a11",  # Added for testing
    }

    if overrides:
        data.update(overrides)

    return data


def generate_test_case(overrides=None):
    """Generate test case data."""
    timestamp = datetime.now().timestamp()
    data = {
        "title": f"Test Case {timestamp}",
        "description": "This is a test case for automated testing",
        "practice_area": "personal_injury",
        "case_type": "auto_accident",
        "intake_priority": "medium",
        "status": "active",  # Using allowed status value
        "previously_consulted": False,
        "metadata": {"internal_notes": "Test case created for automated testing"},
        "tenant_id": "f5a731c2-9c0b-4ef8-bb6d-6bb9bd380a11",  # Added for testing
    }

    if overrides:
        data.update(overrides)

    return data


def test_create_client_intake_basic(supabase_client):
    """Test basic client intake creation."""
    client_data = generate_test_client()
    case_data = generate_test_case()
    other_parties = []

    response = supabase_client.rpc(
        "create_client_intake",
        {
            "p_client_data": client_data,
            "p_case_data": case_data,
            "p_other_parties": other_parties,
            "p_test_tenant_id": "f5a731c2-9c0b-4ef8-bb6d-6bb9bd380a11",  # For testing
            "p_test_user_id": "550e8400-e29b-41d4-a716-446655440000",  # For created_by
        },
    ).execute()

    data = response.data

    # Print response for debugging
    print(f"Response data: {data}")

    assert data is not None, "Response data is None"
    if isinstance(data, dict) and data.get("error"):
        assert False, f"Error in response: {data.get('error')}"

    assert data.get("success"), "Response does not indicate success"
    assert "client_id" in data, "Client ID not returned"
    assert "case_id" in data, "Case ID not returned"

    print(f"Created client ID: {data['client_id']}")
    print(f"Created case ID: {data['case_id']}")


def test_create_client_intake_with_parties(supabase_client):
    """Test client intake creation with other parties."""
    client_data = generate_test_client()
    case_data = generate_test_case()
    other_parties = [
        {
            "first_name": "Test Party 1",
            "last_name": "Defendant",
            "type": "other",
            "role": "defendant",
            "address": {},
        },
        {
            "first_name": "Test Party 2",
            "last_name": "Witness",
            "type": "other",
            "role": "witness",
            "address": {},
        },
    ]

    response = supabase_client.rpc(
        "create_client_intake",
        {
            "p_client_data": client_data,
            "p_case_data": case_data,
            "p_other_parties": other_parties,
            "p_test_tenant_id": "f5a731c2-9c0b-4ef8-bb6d-6bb9bd380a11",  # For testing
            "p_test_user_id": "550e8400-e29b-41d4-a716-446655440000",  # For created_by
        },
    ).execute()

    data = response.data

    assert data.get("success"), "Response does not indicate success"
    assert "client_id" in data, "Client ID not returned"
    assert "case_id" in data, "Case ID not returned"
