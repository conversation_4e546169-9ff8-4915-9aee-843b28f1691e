#!/usr/bin/env python3
"""
Test runner for auth-service integration tests.

This script runs the auth-service flow tests without requiring
the full test infrastructure, making it suitable for CI/CD environments
where some dependencies might not be available.

Usage:
    python tests/integration/run_auth_service_tests.py
"""

import sys
import asyncio
import time
from typing import Dict, Any
from uuid import uuid4

# Add project root to path
sys.path.append('.')

async def run_auth_service_tests():
    """Run all auth-service integration tests."""
    from tests.integration.test_auth_service_flow import TestAuthServiceFlow

    print("🧪 Running Auth-Service Integration Tests")
    print("=" * 50)

    test_instance = TestAuthServiceFlow()

    # Test fixtures
    firm_id = 'test-firm-123'
    test_token = 'ya29.test'
    auth_service_response = {
        'access_token': test_token,
        'expires_at': int(time.time()) + 3600  # 1 hour from now
    }
    google_freebusy_response = {
        'timeMin': '2024-01-01T10:00:00Z',
        'timeMax': '2024-01-01T11:00:00Z',
        'calendars': {
            'primary': {
                'busy': []  # No busy slots = free
            }
        }
    }
    google_event_response = {
        'id': 'test-event-123',
        'htmlLink': 'https://calendar.google.com/event?eid=test-event-123',
        'summary': 'Test Meeting',
        'description': 'Test meeting description',
        'location': 'Test Location',
        'start': {'dateTime': '2024-01-01T10:00:00Z', 'timeZone': 'UTC'},
        'end': {'dateTime': '2024-01-01T11:00:00Z', 'timeZone': 'UTC'},
        'attendees': [
            {
                'email': '<EMAIL>',
                'displayName': 'Test User',
                'responseStatus': 'needsAction'
            }
        ],
        'status': 'confirmed',
        'created': '2024-01-01T09:00:00Z',
        'updated': '2024-01-01T09:00:00Z'
    }
    event_create_data = {
        'summary': 'Test Meeting',
        'description': 'Test meeting description',
        'location': 'Test Location',
        'start': {'dateTime': '2024-01-01T10:00:00Z', 'timeZone': 'UTC'},
        'end': {'dateTime': '2024-01-01T11:00:00Z', 'timeZone': 'UTC'},
        'attendees': [{'email': '<EMAIL>', 'displayName': 'Test User'}]
    }
    freebusy_request_data = {
        'timeMin': '2024-01-01T10:00:00Z',
        'timeMax': '2024-01-01T11:00:00Z',
        'timeZone': 'UTC',
        'items': [{'id': 'primary'}]
    }

    # Mock Supabase client
    from unittest.mock import AsyncMock
    mock_supabase_client = AsyncMock()
    mock_result = AsyncMock()
    mock_result.data = [{"id": str(uuid4())}]
    mock_result.error = None
    (
        mock_supabase_client.schema.return_value.table.return_value.insert
        .return_value.execute.return_value
    ) = mock_result

    # Test definitions
    tests = [
        {
            'name': 'test_happy_path_flow',
            'description': 'Auth-service → Google freeBusy happy path',
            'kwargs': {
                'firm_id': firm_id,
                'test_token': test_token,
                'auth_service_response': auth_service_response,
                'google_freebusy_response': google_freebusy_response,
                'freebusy_request_data': freebusy_request_data
            }
        },
        {
            'name': 'test_booking_flow',
            'description': 'Auth-service → Google events.insert flow',
            'kwargs': {
                'firm_id': firm_id,
                'test_token': test_token,
                'auth_service_response': auth_service_response,
                'google_event_response': google_event_response,
                'event_create_data': event_create_data
            }
        },
        {
            'name': 'test_database_booking_simulation',
            'description': 'Database booking insertion simulation',
            'kwargs': {
                'firm_id': firm_id,
                'google_event_response': google_event_response,
                'mock_supabase_client': mock_supabase_client
            }
        },
        {
            'name': 'test_auth_service_error_handling',
            'description': 'Auth-service error handling',
            'kwargs': {
                'firm_id': firm_id,
                'freebusy_request_data': freebusy_request_data
            }
        },
        {
            'name': 'test_no_network_access_outside_mocks',
            'description': 'Network isolation verification',
            'kwargs': {
                'firm_id': firm_id,
                'test_token': test_token,
                'auth_service_response': auth_service_response,
                'google_freebusy_response': google_freebusy_response,
                'freebusy_request_data': freebusy_request_data
            }
        },
        {
            'name': 'test_performance_benchmark',
            'description': 'Performance benchmark (< 2s latency)',
            'kwargs': {
                'firm_id': firm_id,
                'test_token': test_token,
                'auth_service_response': auth_service_response,
                'google_freebusy_response': google_freebusy_response,
                'freebusy_request_data': freebusy_request_data
            }
        }
    ]

    # Run tests
    passed = 0
    failed = 0
    total_start_time = time.perf_counter()

    for test_info in tests:
        test_name = test_info['name']
        description = test_info['description']
        kwargs = test_info['kwargs']

        print(f"\n🔍 Running: {test_name}")
        print(f"   {description}")

        start_time = time.perf_counter()
        try:
            test_method = getattr(test_instance, test_name)
            await test_method(**kwargs)
            elapsed = time.perf_counter() - start_time
            print(f"   ✅ PASSED ({elapsed:.3f}s)")
            passed += 1
        except Exception as e:
            elapsed = time.perf_counter() - start_time
            print(f"   ❌ FAILED ({elapsed:.3f}s): {e}")
            failed += 1
            # Print traceback for debugging
            import traceback
            traceback.print_exc()

    # Summary
    total_elapsed = time.perf_counter() - total_start_time
    total_tests = passed + failed

    print("\n" + "=" * 50)
    print("📊 Test Results Summary")
    print(f"   Total tests: {total_tests}")
    print(f"   Passed: {passed}")
    print(f"   Failed: {failed}")
    print(f"   Success rate: {(passed/total_tests)*100:.1f}%")
    print(f"   Total time: {total_elapsed:.3f}s")

    if failed == 0:
        print(
            "\n🎉 All tests passed! The auth-service integration is working correctly."
        )
        return 0
    else:
        print(f"\n⚠️  {failed} test(s) failed. Please check the implementation.")
        return 1

if __name__ == "__main__":
    exit_code = asyncio.run(run_auth_service_tests())
    sys.exit(exit_code)
