#!/usr/bin/env python3
"""
<PERSON><PERSON><PERSON> to automatically fix E501 line length violations in Python files.
This script focuses on common patterns that can be safely auto-fixed.
"""

import re
import os
import sys
from pathlib import Path


def fix_long_strings(content: str) -> str:
    """Fix long string literals by breaking them into multiple lines."""
    lines = content.split('\n')
    fixed_lines = []
    
    for line in lines:
        if len(line) <= 88:
            fixed_lines.append(line)
            continue
            
        # Check if it's a simple f-string that can be broken
        if 'f"' in line and line.count('"') == 2:
            # Try to break f-strings at logical points
            match = re.match(r'(\s*)(.*f"[^"]*?)(\{[^}]+\})([^"]*".*)', line)
            if match:
                indent, prefix, var, suffix = match.groups()
                if len(prefix) + len(var) <= 85:
                    new_line = f'{indent}{prefix}"\n{indent}f"{var}{suffix}'
                    if all(len(l) <= 88 for l in new_line.split('\n')):
                        fixed_lines.extend(new_line.split('\n'))
                        continue
        
        # Check if it's a logger statement that can be broken
        if any(
            pattern in line
            for pattern in ['logger.info(', 'logger.error(', 'logger.warning(']
        ):
            # Try to break logger statements
            match = re.match(
                r'(\s*)(logger\.\w+\(\s*)(f?"[^"]*?)(\{[^}]*\})([^"]*".*\))', line
            )
            if match:
                indent, logger_part, prefix, var, suffix = match.groups()
                new_line = (
                    f'{indent}{logger_part}\n{indent}    {prefix}"\n'
                    f'{indent}    f"{var}{suffix}'
                )
                if all(len(l) <= 88 for l in new_line.split('\n')):
                    fixed_lines.extend(new_line.split('\n'))
                    continue
        
        # For other long lines, just add them as-is for now
        fixed_lines.append(line)
    
    return '\n'.join(fixed_lines)


def fix_long_function_calls(content: str) -> str:
    """Fix long function calls by breaking arguments across lines."""
    lines = content.split('\n')
    fixed_lines = []
    
    for line in lines:
        if len(line) <= 88:
            fixed_lines.append(line)
            continue
            
        # Check if it's a function call with multiple arguments
        if '(' in line and ')' in line and ',' in line:
            # Simple case: break after commas
            indent = len(line) - len(line.lstrip())
            if line.strip().endswith(','):
                fixed_lines.append(line)
                continue
                
            # Try to break at commas
            parts = line.split(',')
            if len(parts) > 1:
                base_indent = ' ' * indent
                first_part = parts[0] + ','
                if len(first_part) <= 88:
                    fixed_lines.append(first_part)
                    for part in parts[1:-1]:
                        new_line = base_indent + '    ' + part.strip() + ','
                        if len(new_line) <= 88:
                            fixed_lines.append(new_line)
                        else:
                            fixed_lines.append(line)  # Give up on this line
                            break
                    else:
                        # Last part
                        last_part = base_indent + '    ' + parts[-1].strip()
                        if len(last_part) <= 88:
                            fixed_lines.append(last_part)
                        else:
                            fixed_lines.append(line)
                    continue
        
        fixed_lines.append(line)
    
    return '\n'.join(fixed_lines)


def fix_file(file_path: Path) -> bool:
    """Fix E501 violations in a single file."""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            original_content = f.read()
        
        # Apply fixes
        content = original_content
        content = fix_long_strings(content)
        content = fix_long_function_calls(content)
        
        # Only write if content changed
        if content != original_content:
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(content)
            return True
        
        return False
    except Exception as e:
        print(f"Error processing {file_path}: {e}")
        return False


def main():
    """Main function to fix E501 violations in target directories."""
    target_dirs = [
        "src/pi_lawyer/shared/core",
        "src/pi_lawyer/state", 
        "src/pi_lawyer/services"
    ]
    
    total_files = 0
    fixed_files = 0
    
    for target_dir in target_dirs:
        if not os.path.exists(target_dir):
            print(f"Directory {target_dir} does not exist, skipping...")
            continue
            
        for file_path in Path(target_dir).rglob("*.py"):
            total_files += 1
            if fix_file(file_path):
                fixed_files += 1
                print(f"Fixed: {file_path}")
    
    print(f"\nProcessed {total_files} files, fixed {fixed_files} files")


if __name__ == "__main__":
    main()
