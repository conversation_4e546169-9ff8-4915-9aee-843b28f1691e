{"name": "task-e2e-tests", "version": "1.0.0", "description": "End-to-end tests for Task CRUD integration", "private": true, "scripts": {"test": "playwright test", "test:headed": "playwright test --headed", "test:debug": "playwright test --debug", "test:ui": "playwright test --ui", "test:report": "playwright show-report", "install-deps": "playwright install"}, "devDependencies": {"@playwright/test": "^1.40.0", "@types/node": "^20.0.0", "typescript": "^5.0.0"}, "dependencies": {"jsonwebtoken": "^9.0.2", "@types/jsonwebtoken": "^9.0.5"}}