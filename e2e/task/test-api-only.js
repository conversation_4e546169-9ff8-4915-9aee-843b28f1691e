#!/usr/bin/env node

/**
 * Simple API test for Task CRUD E2E functionality
 * 
 * This script tests the basic API functionality without requiring browser dependencies.
 * It's useful for verifying that the server and API endpoints are working correctly.
 */

const jwt = require('jsonwebtoken');

// Test configuration
const BASE_URL = 'http://localhost:8000';
const JWT_SECRET = 'test-jwt-secret-for-e2e-tests-only';

// Test user
const TEST_USER = {
  id: 'test-user-001',
  email: '<EMAIL>',
  role: 'user',
  firm_id: 'test-firm-001',
  permissions: ['task:read', 'task:write', 'task:create', 'task:update', 'task:delete']
};

// Generate JWT token
function generateTestJWT(user) {
  const claims = {
    sub: user.id,
    email: user.email,
    role: user.role,
    tenant_id: user.firm_id,
    permissions: user.permissions
  };

  return jwt.sign(claims, JWT_SECRET, { 
    expiresIn: '1h',
    algorithm: 'HS256'
  });
}

// Make API request
async function makeRequest(endpoint, data) {
  const token = generateTestJWT(TEST_USER);
  
  try {
    const response = await fetch(`${BASE_URL}${endpoint}`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${token}`
      },
      body: JSON.stringify(data)
    });

    const result = await response.json();
    return {
      status: response.status,
      data: result
    };
  } catch (error) {
    return {
      status: 0,
      error: error.message
    };
  }
}

// Test health endpoint
async function testHealth() {
  console.log('🔍 Testing health endpoint...');
  
  try {
    const response = await fetch(`${BASE_URL}/health`);
    const data = await response.json();
    
    if (response.status === 200) {
      console.log('✅ Health check passed');
      console.log(`   Status: ${data.status}`);
      console.log(`   Service: ${data.service}`);
      return true;
    } else {
      console.log('❌ Health check failed');
      return false;
    }
  } catch (error) {
    console.log('❌ Health check failed:', error.message);
    return false;
  }
}

// Test task creation
async function testTaskCreation() {
  console.log('🔍 Testing task creation...');
  
  const taskData = {
    agent: 'task_crud',
    messages: [
      {
        role: 'user',
        content: 'Create a new task with the following details: {"title": "Test Task", "description": "This is a test task"}'
      }
    ],
    stream: false,
    config: {
      firm_id: TEST_USER.firm_id,
      user_id: TEST_USER.id
    },
    state: {
      firm_id: TEST_USER.firm_id,
      user_id: TEST_USER.id,
      task_data: {
        title: 'Test Task',
        description: 'This is a test task'
      }
    }
  };

  const result = await makeRequest('/copilotkit', taskData);
  
  if (result.status === 200) {
    console.log('✅ Task creation request successful');
    console.log(`   Response: ${JSON.stringify(result.data, null, 2)}`);
    return true;
  } else {
    console.log('❌ Task creation failed');
    console.log(`   Status: ${result.status}`);
    console.log(`   Error: ${result.error || JSON.stringify(result.data)}`);
    return false;
  }
}

// Test authentication
async function testAuthentication() {
  console.log('🔍 Testing authentication...');
  
  const taskData = {
    agent: 'task_crud',
    messages: [
      {
        role: 'user',
        content: 'List all my tasks'
      }
    ],
    stream: false
  };

  // Test without authentication
  try {
    const response = await fetch(`${BASE_URL}/copilotkit`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
        // No Authorization header
      },
      body: JSON.stringify(taskData)
    });

    if (response.status === 401 || response.status === 403) {
      console.log('✅ Authentication properly rejected unauthorized request');
      return true;
    } else {
      console.log('❌ Authentication failed - unauthorized request was allowed');
      return false;
    }
  } catch (error) {
    console.log('❌ Authentication test failed:', error.message);
    return false;
  }
}

// Main test runner
async function runTests() {
  console.log('🚀 Starting Task CRUD API Tests');
  console.log('================================');
  
  const tests = [
    { name: 'Health Check', fn: testHealth },
    { name: 'Authentication', fn: testAuthentication },
    { name: 'Task Creation', fn: testTaskCreation }
  ];

  let passed = 0;
  let failed = 0;

  for (const test of tests) {
    try {
      const result = await test.fn();
      if (result) {
        passed++;
      } else {
        failed++;
      }
    } catch (error) {
      console.log(`❌ ${test.name} threw an error:`, error.message);
      failed++;
    }
    console.log('');
  }

  console.log('📊 Test Results');
  console.log('===============');
  console.log(`✅ Passed: ${passed}`);
  console.log(`❌ Failed: ${failed}`);
  console.log(`📈 Total: ${passed + failed}`);

  if (failed === 0) {
    console.log('🎉 All tests passed!');
    process.exit(0);
  } else {
    console.log('💥 Some tests failed!');
    process.exit(1);
  }
}

// Check if fetch is available (Node.js 18+)
if (typeof fetch === 'undefined') {
  console.log('❌ This script requires Node.js 18+ with built-in fetch support');
  process.exit(1);
}

// Run the tests
runTests().catch(error => {
  console.error('💥 Test runner failed:', error);
  process.exit(1);
});
