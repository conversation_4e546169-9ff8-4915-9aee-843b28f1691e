{"compilerOptions": {"target": "ES2020", "module": "commonjs", "lib": ["ES2020"], "strict": true, "esModuleInterop": true, "skipLibCheck": true, "forceConsistentCasingInFileNames": true, "moduleResolution": "node", "resolveJsonModule": true, "declaration": false, "outDir": "./dist", "rootDir": "./", "types": ["node", "@playwright/test"]}, "include": ["**/*.ts", "**/*.js"], "exclude": ["node_modules", "dist", "test-results"]}