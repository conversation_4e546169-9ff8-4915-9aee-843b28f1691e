import { APIRequestContext, expect } from '@playwright/test';
import { TestUser, getAuthHeader } from './auth';

/**
 * API utilities for Task CRUD E2E tests
 * 
 * This module provides utilities for making API requests to the task endpoints
 * and validating responses.
 */

export interface TaskData {
  title: string;
  description?: string;
  status?: 'todo' | 'in_progress' | 'done';
  priority?: 'low' | 'medium' | 'high' | 'urgent';
  due_date?: string;
  assigned_to?: string;
  related_case?: string;
}

export interface InvokeRequest {
  agent: string;
  messages: Array<{
    role: string;
    content: string;
  }>;
  stream?: boolean;
  config?: {
    firm_id: string;
    user_id: string;
  };
  state?: {
    firm_id: string;
    user_id: string;
    [key: string]: any;
  };
}

export interface InvokeResponse {
  messages?: Array<{
    role: string;
    content: string;
    metadata?: any;
  }>;
  done?: boolean;
  threadId?: string;
  error?: string;
  [key: string]: any;
}

/**
 * Create test task data
 */
export function createTestTaskData(overrides: Partial<TaskData> = {}): TaskData {
  return {
    title: 'Test Task',
    description: 'This is a test task for E2E testing',
    status: 'todo',
    priority: 'medium',
    due_date: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString(), // 7 days from now
    ...overrides
  };
}

/**
 * Format task operation for the agent
 */
export function formatTaskOperation(operation: string, taskData?: TaskData, taskId?: string): string {
  switch (operation.toLowerCase()) {
    case 'create':
      return `Create a new task with the following details: ${JSON.stringify(taskData)}`;
    case 'read':
    case 'list':
      return 'List all my tasks';
    case 'update':
      return `Update task ${taskId} with the following details: ${JSON.stringify(taskData)}`;
    case 'delete':
      return `Delete task ${taskId}`;
    default:
      return operation;
  }
}

/**
 * Make a request to the /copilotkit endpoint
 */
export async function invokeTaskAgent(
  request: APIRequestContext,
  user: TestUser,
  operation: string,
  taskData?: TaskData,
  taskId?: string,
  options: {
    stream?: boolean;
    expectSuccess?: boolean;
  } = {}
): Promise<InvokeResponse> {
  const { stream = false, expectSuccess = true } = options;

  const invokeRequest: InvokeRequest = {
    agent: 'task_crud',
    messages: [
      {
        role: 'user',
        content: formatTaskOperation(operation, taskData, taskId)
      }
    ],
    stream,
    config: {
      firm_id: user.firm_id,
      user_id: user.id
    },
    state: {
      firm_id: user.firm_id,
      user_id: user.id,
      ...(taskData && { task_data: taskData }),
      ...(taskId && { task_id: taskId })
    }
  };

  const response = await request.post('/copilotkit', {
    headers: {
      'Content-Type': 'application/json',
      ...getAuthHeader(user)
    },
    data: invokeRequest
  });

  if (expectSuccess) {
    expect(response.status()).toBe(200);
  }

  const responseData = await response.json();
  return responseData;
}

/**
 * Assert that a task operation was successful
 */
export function assertTaskOperationSuccess(response: InvokeResponse, operation: string) {
  expect(response.error).toBeUndefined();
  expect(response.messages).toBeDefined();
  expect(response.messages!.length).toBeGreaterThan(0);
  
  const lastMessage = response.messages![response.messages!.length - 1];
  expect(lastMessage.role).toBe('assistant');
  
  // Check that the response indicates success
  const content = lastMessage.content.toLowerCase();
  switch (operation.toLowerCase()) {
    case 'create':
      expect(content).toContain('created');
      break;
    case 'update':
      expect(content).toContain('updated');
      break;
    case 'delete':
      expect(content).toContain('deleted');
      break;
    case 'read':
    case 'list':
      // For read operations, we just check that we got a response
      expect(content.length).toBeGreaterThan(0);
      break;
  }
}

/**
 * Assert that a task operation failed with an error
 */
export function assertTaskOperationError(response: InvokeResponse, expectedErrorType?: string) {
  // Check if there's an explicit error field
  if (response.error) {
    expect(response.error).toBeDefined();
    if (expectedErrorType) {
      expect(response.error.toLowerCase()).toContain(expectedErrorType.toLowerCase());
    }
    return;
  }
  
  // Check if the last message indicates an error
  expect(response.messages).toBeDefined();
  expect(response.messages!.length).toBeGreaterThan(0);
  
  const lastMessage = response.messages![response.messages!.length - 1];
  expect(lastMessage.role).toBe('assistant');
  
  const content = lastMessage.content.toLowerCase();
  expect(content).toMatch(/(error|failed|unable|cannot|not found|unauthorized|forbidden)/);
  
  if (expectedErrorType) {
    expect(content).toContain(expectedErrorType.toLowerCase());
  }
}
