import jwt from 'jsonwebtoken';

/**
 * Authentication utilities for Task CRUD E2E tests
 * 
 * This module provides utilities for generating JWT tokens
 * and handling authentication in E2E tests.
 */

export interface TestUser {
  id: string;
  email: string;
  role: 'user' | 'admin' | 'superadmin';
  firm_id: string;
  permissions: string[];
}

export interface JWTClaims {
  sub: string;
  email: string;
  role: string;
  tenant_id: string;
  permissions: string[];
  iat?: number;
  exp?: number;
}

/**
 * Test users for different scenarios
 */
export const TEST_USERS: Record<string, TestUser> = {
  regularUser: {
    id: 'test-user-001',
    email: '<EMAIL>',
    role: 'user',
    firm_id: 'test-firm-001',
    permissions: ['task:read', 'task:write', 'task:create', 'task:update', 'task:delete']
  },
  adminUser: {
    id: 'test-admin-001',
    email: '<EMAIL>',
    role: 'admin',
    firm_id: 'test-firm-001',
    permissions: ['task:read', 'task:write', 'task:create', 'task:update', 'task:delete', 'task:admin']
  },
  otherFirmUser: {
    id: 'test-user-002',
    email: '<EMAIL>',
    role: 'user', 
    firm_id: 'test-firm-002',
    permissions: ['task:read', 'task:write', 'task:create', 'task:update', 'task:delete']
  }
};

/**
 * Generate a JWT token for testing
 */
export function generateTestJWT(user: TestUser, options: {
  expiresIn?: string;
  secret?: string;
} = {}): string {
  const {
    expiresIn = '1h',
    secret = process.env.SUPABASE_JWT_SECRET || 'test-jwt-secret-for-e2e-tests-only'
  } = options;

  const claims: JWTClaims = {
    sub: user.id,
    email: user.email,
    role: user.role,
    tenant_id: user.firm_id,
    permissions: user.permissions
  };

  return jwt.sign(claims, secret, {
    expiresIn,
    algorithm: 'HS256'
  } as jwt.SignOptions);
}

/**
 * Get authorization header for a test user
 */
export function getAuthHeader(user: TestUser): Record<string, string> {
  const token = generateTestJWT(user);
  return {
    'Authorization': `Bearer ${token}`
  };
}

/**
 * Generate an expired JWT token for testing authentication failures
 */
export function generateExpiredJWT(user: TestUser): string {
  const secret = process.env.SUPABASE_JWT_SECRET || 'test-jwt-secret-for-e2e-tests-only';
  
  const claims: JWTClaims = {
    sub: user.id,
    email: user.email,
    role: user.role,
    tenant_id: user.firm_id,
    permissions: user.permissions,
    iat: Math.floor(Date.now() / 1000) - 3600, // 1 hour ago
    exp: Math.floor(Date.now() / 1000) - 1800  // 30 minutes ago (expired)
  };

  return jwt.sign(claims, secret, {
    algorithm: 'HS256',
    noTimestamp: true
  } as jwt.SignOptions);
}

/**
 * Generate an invalid JWT token for testing authentication failures
 */
export function generateInvalidJWT(): string {
  return 'invalid.jwt.token';
}

/**
 * Verify a JWT token (for testing purposes)
 */
export function verifyTestJWT(token: string): JWTClaims {
  const secret = process.env.SUPABASE_JWT_SECRET || 'test-jwt-secret-for-e2e-tests-only';
  return jwt.verify(token, secret) as JWTClaims;
}
