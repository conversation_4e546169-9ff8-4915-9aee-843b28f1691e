import { Page } from '@playwright/test';

/**
 * Mock utilities for Task CRUD E2E tests
 * 
 * This module provides utilities for mocking database operations
 * and external services during E2E testing.
 */

export interface MockTask {
  id: string;
  tenant_id: string;
  title: string;
  description?: string;
  status: 'todo' | 'in_progress' | 'done';
  priority?: 'low' | 'medium' | 'high' | 'urgent';
  due_date?: string;
  assigned_to?: string;
  related_case?: string;
  created_by: string;
  created_at: string;
  updated_by?: string;
  updated_at?: string;
}

export class DatabaseMocks {
  private tasks: MockTask[] = [];
  private nextId = 1;

  /**
   * Add a mock task to the database
   */
  addTask(task: Partial<MockTask>): MockTask {
    const mockTask: MockTask = {
      id: `task-${this.nextId++}`,
      tenant_id: task.tenant_id || 'test-firm-001',
      title: task.title || 'Mock Task',
      description: task.description,
      status: task.status || 'todo',
      priority: task.priority,
      due_date: task.due_date,
      assigned_to: task.assigned_to,
      related_case: task.related_case,
      created_by: task.created_by || 'test-user-001',
      created_at: new Date().toISOString(),
      updated_by: task.updated_by,
      updated_at: task.updated_at,
      ...task
    };

    this.tasks.push(mockTask);
    return mockTask;
  }

  /**
   * Get tasks for a specific tenant
   */
  getTasksForTenant(tenantId: string): MockTask[] {
    return this.tasks.filter(task => task.tenant_id === tenantId);
  }

  /**
   * Get a specific task by ID
   */
  getTask(id: string): MockTask | undefined {
    return this.tasks.find(task => task.id === id);
  }

  /**
   * Update a task
   */
  updateTask(id: string, updates: Partial<MockTask>): MockTask | undefined {
    const taskIndex = this.tasks.findIndex(task => task.id === id);
    if (taskIndex === -1) return undefined;

    this.tasks[taskIndex] = {
      ...this.tasks[taskIndex],
      ...updates,
      updated_at: new Date().toISOString()
    };

    return this.tasks[taskIndex];
  }

  /**
   * Delete a task
   */
  deleteTask(id: string): boolean {
    const taskIndex = this.tasks.findIndex(task => task.id === id);
    if (taskIndex === -1) return false;

    this.tasks.splice(taskIndex, 1);
    return true;
  }

  /**
   * Clear all mock data
   */
  clear(): void {
    this.tasks = [];
    this.nextId = 1;
  }

  /**
   * Get all tasks (for debugging)
   */
  getAllTasks(): MockTask[] {
    return [...this.tasks];
  }
}

/**
 * Setup database mocks for a test page
 */
export async function setupDatabaseMocks(page: Page, mocks: DatabaseMocks): Promise<void> {
  // Intercept database-related API calls and return mock data
  await page.route('**/api/tasks**', async (route) => {
    const request = route.request();
    const method = request.method();
    const url = new URL(request.url());

    // Extract tenant ID from headers or query params
    const authHeader = request.headers()['authorization'];
    // In a real implementation, you'd decode the JWT to get tenant_id
    const tenantId = 'test-firm-001'; // Simplified for testing

    switch (method) {
      case 'GET':
        const tasks = mocks.getTasksForTenant(tenantId);
        await route.fulfill({
          status: 200,
          contentType: 'application/json',
          body: JSON.stringify({ tasks })
        });
        break;

      case 'POST':
        const createData = request.postDataJSON();
        const newTask = mocks.addTask({
          ...createData,
          tenant_id: tenantId
        });
        await route.fulfill({
          status: 201,
          contentType: 'application/json',
          body: JSON.stringify({ task: newTask })
        });
        break;

      case 'PUT':
      case 'PATCH':
        const taskId = url.pathname.split('/').pop();
        const updateData = request.postDataJSON();
        const updatedTask = mocks.updateTask(taskId!, updateData);
        
        if (updatedTask) {
          await route.fulfill({
            status: 200,
            contentType: 'application/json',
            body: JSON.stringify({ task: updatedTask })
          });
        } else {
          await route.fulfill({
            status: 404,
            contentType: 'application/json',
            body: JSON.stringify({ error: 'Task not found' })
          });
        }
        break;

      case 'DELETE':
        const deleteTaskId = url.pathname.split('/').pop();
        const deleted = mocks.deleteTask(deleteTaskId!);
        
        if (deleted) {
          await route.fulfill({
            status: 200,
            contentType: 'application/json',
            body: JSON.stringify({ message: 'Task deleted successfully' })
          });
        } else {
          await route.fulfill({
            status: 404,
            contentType: 'application/json',
            body: JSON.stringify({ error: 'Task not found' })
          });
        }
        break;

      default:
        await route.continue();
    }
  });

  // Setup some default test data
  mocks.addTask({
    title: 'Existing Test Task',
    description: 'This task exists for testing purposes',
    status: 'todo',
    priority: 'medium',
    tenant_id: 'test-firm-001',
    created_by: 'test-user-001'
  });
}
