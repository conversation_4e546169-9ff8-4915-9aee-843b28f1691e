import { test, expect } from '@playwright/test';
import { 
  TEST_USERS, 
  generateExpiredJWT, 
  generateInvalidJWT,
  getAuthHeader 
} from '../utils/auth';
import { 
  invokeTaskAgent, 
  createTestTaskData 
} from '../utils/api';
import { DatabaseMocks, setupDatabaseMocks } from '../utils/mocks';

/**
 * E2E tests for Task CRUD Authentication and Authorization
 * 
 * These tests verify that the task CRUD agent properly handles
 * authentication and authorization through JWT tokens.
 */

test.describe('Task CRUD Authentication', () => {
  let dbMocks: DatabaseMocks;

  test.beforeEach(async ({ page }) => {
    dbMocks = new DatabaseMocks();
    await setupDatabaseMocks(page, dbMocks);
  });

  test.afterEach(async () => {
    if (dbMocks) {
      dbMocks.clear();
    }
  });

  test('should reject requests without authentication', async ({ request }) => {
    const taskData = createTestTaskData({
      title: 'Unauthorized task creation attempt'
    });

    const response = await request.post('/copilotkit', {
      headers: {
        'Content-Type': 'application/json'
        // No Authorization header
      },
      data: {
        agent: 'task_crud',
        messages: [
          {
            role: 'user',
            content: `Create a new task with the following details: ${JSON.stringify(taskData)}`
          }
        ],
        stream: false
      }
    });

    expect(response.status()).toBe(401);
  });

  test('should reject requests with invalid JWT', async ({ request }) => {
    const taskData = createTestTaskData({
      title: 'Invalid JWT task creation attempt'
    });

    const response = await request.post('/copilotkit', {
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${generateInvalidJWT()}`
      },
      data: {
        agent: 'task_crud',
        messages: [
          {
            role: 'user',
            content: `Create a new task with the following details: ${JSON.stringify(taskData)}`
          }
        ],
        stream: false
      }
    });

    expect(response.status()).toBe(401);
  });

  test('should reject requests with expired JWT', async ({ request }) => {
    const user = TEST_USERS.regularUser;
    const expiredToken = generateExpiredJWT(user);
    const taskData = createTestTaskData({
      title: 'Expired JWT task creation attempt'
    });

    const response = await request.post('/copilotkit', {
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${expiredToken}`
      },
      data: {
        agent: 'task_crud',
        messages: [
          {
            role: 'user',
            content: `Create a new task with the following details: ${JSON.stringify(taskData)}`
          }
        ],
        stream: false
      }
    });

    expect(response.status()).toBe(401);
  });

  test('should enforce tenant isolation - users cannot access other tenants tasks', async ({ request }) => {
    const user1 = TEST_USERS.regularUser; // firm-001
    const user2 = TEST_USERS.otherFirmUser; // firm-002
    
    // Add tasks for both tenants
    dbMocks.addTask({
      title: 'User1 task',
      tenant_id: user1.firm_id,
      created_by: user1.id
    });
    
    dbMocks.addTask({
      title: 'User2 task',
      tenant_id: user2.firm_id,
      created_by: user2.id
    });

    // User1 should only see their own tasks
    const response1 = await invokeTaskAgent(request, user1, 'list');
    expect(response1.error).toBeUndefined();
    
    const content1 = response1.messages![response1.messages!.length - 1].content.toLowerCase();
    expect(content1).toContain('user1 task');
    expect(content1).not.toContain('user2 task');

    // User2 should only see their own tasks
    const response2 = await invokeTaskAgent(request, user2, 'list');
    expect(response2.error).toBeUndefined();
    
    const content2 = response2.messages![response2.messages!.length - 1].content.toLowerCase();
    expect(content2).toContain('user2 task');
    expect(content2).not.toContain('user1 task');
  });

  test('should allow admin users to perform all operations', async ({ request }) => {
    const adminUser = TEST_USERS.adminUser;
    const taskData = createTestTaskData({
      title: 'Admin created task'
    });

    // Admin should be able to create tasks
    const createResponse = await invokeTaskAgent(
      request,
      adminUser,
      'create',
      taskData
    );
    expect(createResponse.error).toBeUndefined();

    // Admin should be able to list tasks
    const listResponse = await invokeTaskAgent(request, adminUser, 'list');
    expect(listResponse.error).toBeUndefined();
  });

  test('should validate user permissions for task operations', async ({ request }) => {
    const user = TEST_USERS.regularUser;
    
    // User with proper permissions should be able to create tasks
    const taskData = createTestTaskData({
      title: 'Permission test task'
    });

    const response = await invokeTaskAgent(
      request,
      user,
      'create',
      taskData
    );

    expect(response.error).toBeUndefined();
    expect(response.messages).toBeDefined();
    expect(response.messages!.length).toBeGreaterThan(0);
  });

  test('should maintain session context across multiple requests', async ({ request }) => {
    const user = TEST_USERS.regularUser;
    
    // Create a task
    const taskData = createTestTaskData({
      title: 'Session context test task'
    });

    const createResponse = await invokeTaskAgent(
      request,
      user,
      'create',
      taskData
    );
    expect(createResponse.error).toBeUndefined();

    // List tasks in the same session
    const listResponse = await invokeTaskAgent(request, user, 'list');
    expect(listResponse.error).toBeUndefined();
    
    const content = listResponse.messages![listResponse.messages!.length - 1].content.toLowerCase();
    expect(content).toContain('session context test task');
  });

  test('should handle concurrent requests from different users', async ({ request }) => {
    const user1 = TEST_USERS.regularUser;
    const user2 = TEST_USERS.otherFirmUser;
    
    const taskData1 = createTestTaskData({ title: 'User1 concurrent task' });
    const taskData2 = createTestTaskData({ title: 'User2 concurrent task' });

    // Make concurrent requests
    const [response1, response2] = await Promise.all([
      invokeTaskAgent(request, user1, 'create', taskData1),
      invokeTaskAgent(request, user2, 'create', taskData2)
    ]);

    // Both should succeed
    expect(response1.error).toBeUndefined();
    expect(response2.error).toBeUndefined();

    // Verify tenant isolation is maintained
    const user1Tasks = dbMocks.getTasksForTenant(user1.firm_id);
    const user2Tasks = dbMocks.getTasksForTenant(user2.firm_id);

    expect(user1Tasks.some(task => task.title === 'User1 concurrent task')).toBe(true);
    expect(user2Tasks.some(task => task.title === 'User2 concurrent task')).toBe(true);
    
    // Cross-tenant tasks should not be visible
    expect(user1Tasks.some(task => task.title === 'User2 concurrent task')).toBe(false);
    expect(user2Tasks.some(task => task.title === 'User1 concurrent task')).toBe(false);
  });

  test('should validate JWT claims and tenant context', async ({ request }) => {
    const user = TEST_USERS.regularUser;
    
    const response = await invokeTaskAgent(request, user, 'list');
    expect(response.error).toBeUndefined();

    // The response should be scoped to the user's tenant
    const tasks = dbMocks.getTasksForTenant(user.firm_id);
    const otherTenantTasks = dbMocks.getTasksForTenant(TEST_USERS.otherFirmUser.firm_id);
    
    // Should have access to own tenant's tasks
    expect(tasks.length).toBeGreaterThanOrEqual(0);
    
    // Should not have access to other tenant's tasks through this user's context
    const content = response.messages![response.messages!.length - 1].content.toLowerCase();
    otherTenantTasks.forEach(task => {
      expect(content).not.toContain(task.title.toLowerCase());
    });
  });
});
