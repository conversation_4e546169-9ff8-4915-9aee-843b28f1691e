import { test, expect } from '@playwright/test';
import { TEST_USERS } from '../utils/auth';
import { 
  invokeTaskAgent, 
  createTestTaskData, 
  assertTaskOperationSuccess,
  assertTaskOperationError 
} from '../utils/api';
import { DatabaseMocks, setupDatabaseMocks } from '../utils/mocks';

/**
 * E2E tests for Task Updates via /copilotkit endpoint
 * 
 * These tests verify that the task CRUD agent can successfully
 * update tasks through the LangGraph workflow.
 */

test.describe('Task Updates', () => {
  let dbMocks: DatabaseMocks;
  let testTaskId: string;

  test.beforeEach(async ({ page }) => {
    dbMocks = new DatabaseMocks();
    await setupDatabaseMocks(page, dbMocks);
    
    // Add a test task to update
    const user = TEST_USERS.regularUser;
    const testTask = dbMocks.addTask({
      title: 'Original task title',
      description: 'Original description',
      status: 'todo',
      priority: 'medium',
      tenant_id: user.firm_id,
      created_by: user.id
    });
    testTaskId = testTask.id;
  });

  test.afterEach(async () => {
    if (dbMocks) {
      dbMocks.clear();
    }
  });

  test('should update task title and description', async ({ request }) => {
    const user = TEST_USERS.regularUser;
    const updateData = createTestTaskData({
      title: 'Updated task title',
      description: 'Updated description with more details'
    });

    const response = await invokeTaskAgent(
      request,
      user,
      'update',
      updateData,
      testTaskId
    );

    assertTaskOperationSuccess(response, 'update');
    
    // Verify the task was updated in our mock database
    const updatedTask = dbMocks.getTask(testTaskId);
    expect(updatedTask).toBeDefined();
    expect(updatedTask!.title).toBe(updateData.title);
    expect(updatedTask!.description).toBe(updateData.description);
    expect(updatedTask!.updated_at).toBeDefined();
  });

  test('should update task status', async ({ request }) => {
    const user = TEST_USERS.regularUser;
    const updateData = createTestTaskData({
      status: 'in_progress'
    });

    const response = await invokeTaskAgent(
      request,
      user,
      'update',
      updateData,
      testTaskId
    );

    assertTaskOperationSuccess(response, 'update');
    
    // Verify the status was updated
    const updatedTask = dbMocks.getTask(testTaskId);
    expect(updatedTask).toBeDefined();
    expect(updatedTask!.status).toBe('in_progress');
  });

  test('should update task priority', async ({ request }) => {
    const user = TEST_USERS.regularUser;
    const updateData = createTestTaskData({
      priority: 'urgent'
    });

    const response = await invokeTaskAgent(
      request,
      user,
      'update',
      updateData,
      testTaskId
    );

    assertTaskOperationSuccess(response, 'update');
    
    // Verify the priority was updated
    const updatedTask = dbMocks.getTask(testTaskId);
    expect(updatedTask).toBeDefined();
    expect(updatedTask!.priority).toBe('urgent');
  });

  test('should update task due date', async ({ request }) => {
    const user = TEST_USERS.regularUser;
    const newDueDate = new Date(Date.now() + 5 * 24 * 60 * 60 * 1000).toISOString(); // 5 days from now
    
    const updateData = createTestTaskData({
      due_date: newDueDate
    });

    const response = await invokeTaskAgent(
      request,
      user,
      'update',
      updateData,
      testTaskId
    );

    assertTaskOperationSuccess(response, 'update');
    
    // Verify the due date was updated
    const updatedTask = dbMocks.getTask(testTaskId);
    expect(updatedTask).toBeDefined();
    expect(updatedTask!.due_date).toBe(newDueDate);
  });

  test('should complete a task', async ({ request }) => {
    const user = TEST_USERS.regularUser;

    const response = await invokeTaskAgent(
      request,
      user,
      `Mark task ${testTaskId} as completed`
    );

    assertTaskOperationSuccess(response, 'update');
    
    // Verify the task was marked as done
    const updatedTask = dbMocks.getTask(testTaskId);
    expect(updatedTask).toBeDefined();
    expect(updatedTask!.status).toBe('done');
  });

  test('should update task with natural language', async ({ request }) => {
    const user = TEST_USERS.regularUser;

    const response = await invokeTaskAgent(
      request,
      user,
      `Change the priority of task ${testTaskId} to high and mark it as in progress`
    );

    assertTaskOperationSuccess(response, 'update');
    
    // Verify the updates were applied
    const updatedTask = dbMocks.getTask(testTaskId);
    expect(updatedTask).toBeDefined();
    expect(updatedTask!.priority).toBe('high');
    expect(updatedTask!.status).toBe('in_progress');
  });

  test('should handle non-existent task update gracefully', async ({ request }) => {
    const user = TEST_USERS.regularUser;
    const nonExistentTaskId = 'task-999999';
    
    const updateData = createTestTaskData({
      title: 'This should fail'
    });

    const response = await invokeTaskAgent(
      request,
      user,
      'update',
      updateData,
      nonExistentTaskId,
      { expectSuccess: false }
    );

    assertTaskOperationError(response, 'not found');
  });

  test('should enforce tenant isolation during updates', async ({ request }) => {
    const user1 = TEST_USERS.regularUser; // firm-001
    const user2 = TEST_USERS.otherFirmUser; // firm-002
    
    // Add a task for user2's firm
    const user2Task = dbMocks.addTask({
      title: 'User2 task',
      tenant_id: user2.firm_id,
      created_by: user2.id
    });
    
    const updateData = createTestTaskData({
      title: 'Attempted unauthorized update'
    });

    // User1 should not be able to update user2's task
    const response = await invokeTaskAgent(
      request,
      user1,
      'update',
      updateData,
      user2Task.id,
      { expectSuccess: false }
    );

    assertTaskOperationError(response, 'not found');
    
    // Verify the task was not updated
    const unchangedTask = dbMocks.getTask(user2Task.id);
    expect(unchangedTask).toBeDefined();
    expect(unchangedTask!.title).toBe('User2 task'); // Should remain unchanged
  });

  test('should update multiple fields simultaneously', async ({ request }) => {
    const user = TEST_USERS.regularUser;
    const updateData = createTestTaskData({
      title: 'Completely updated task',
      description: 'New description with updated details',
      status: 'in_progress',
      priority: 'high',
      due_date: new Date(Date.now() + 2 * 24 * 60 * 60 * 1000).toISOString()
    });

    const response = await invokeTaskAgent(
      request,
      user,
      'update',
      updateData,
      testTaskId
    );

    assertTaskOperationSuccess(response, 'update');
    
    // Verify all fields were updated
    const updatedTask = dbMocks.getTask(testTaskId);
    expect(updatedTask).toBeDefined();
    expect(updatedTask!.title).toBe(updateData.title);
    expect(updatedTask!.description).toBe(updateData.description);
    expect(updatedTask!.status).toBe(updateData.status);
    expect(updatedTask!.priority).toBe(updateData.priority);
    expect(updatedTask!.due_date).toBe(updateData.due_date);
  });
});
