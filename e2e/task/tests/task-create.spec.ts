import { test, expect } from '@playwright/test';
import { TEST_USERS } from '../utils/auth';
import { 
  invokeTaskAgent, 
  createTestTaskData, 
  assertTaskOperationSuccess,
  assertTaskOperationError 
} from '../utils/api';
import { DatabaseMocks, setupDatabaseMocks } from '../utils/mocks';

/**
 * E2E tests for Task Creation via /copilotkit endpoint
 * 
 * These tests verify that the task CRUD agent can successfully
 * create tasks through the LangGraph workflow.
 */

test.describe('Task Creation', () => {
  let dbMocks: DatabaseMocks;

  test.beforeEach(async ({ page }) => {
    dbMocks = new DatabaseMocks();
    await setupDatabaseMocks(page, dbMocks);
  });

  test.afterEach(async () => {
    if (dbMocks) {
      dbMocks.clear();
    }
  });

  test('should create a basic task successfully', async ({ request }) => {
    const user = TEST_USERS.regularUser;
    const taskData = createTestTaskData({
      title: 'Review contract documents',
      description: 'Review the client contract for any legal issues'
    });

    const response = await invokeTaskAgent(
      request,
      user,
      'create',
      taskData
    );

    assertTaskOperationSuccess(response, 'create');
    
    // Verify the task was created in our mock database
    const tasks = dbMocks.getTasksForTenant(user.firm_id);
    expect(tasks.length).toBeGreaterThan(0);
    
    const createdTask = tasks.find(task => task.title === taskData.title);
    expect(createdTask).toBeDefined();
    expect(createdTask!.description).toBe(taskData.description);
    expect(createdTask!.status).toBe('todo');
  });

  test('should create a task with priority and due date', async ({ request }) => {
    const user = TEST_USERS.regularUser;
    const dueDate = new Date(Date.now() + 3 * 24 * 60 * 60 * 1000).toISOString(); // 3 days from now
    
    const taskData = createTestTaskData({
      title: 'Urgent: File motion',
      description: 'File motion for summary judgment',
      priority: 'urgent',
      due_date: dueDate
    });

    const response = await invokeTaskAgent(
      request,
      user,
      'create',
      taskData
    );

    assertTaskOperationSuccess(response, 'create');
    
    // Verify the task was created with correct priority and due date
    const tasks = dbMocks.getTasksForTenant(user.firm_id);
    const createdTask = tasks.find(task => task.title === taskData.title);
    
    expect(createdTask).toBeDefined();
    expect(createdTask!.priority).toBe('urgent');
    expect(createdTask!.due_date).toBe(dueDate);
  });

  test('should create a task with case assignment', async ({ request }) => {
    const user = TEST_USERS.regularUser;
    const taskData = createTestTaskData({
      title: 'Prepare deposition questions',
      description: 'Prepare questions for plaintiff deposition',
      related_case: 'case-123'
    });

    const response = await invokeTaskAgent(
      request,
      user,
      'create',
      taskData
    );

    assertTaskOperationSuccess(response, 'create');
    
    // Verify the task was created with case assignment
    const tasks = dbMocks.getTasksForTenant(user.firm_id);
    const createdTask = tasks.find(task => task.title === taskData.title);
    
    expect(createdTask).toBeDefined();
    expect(createdTask!.related_case).toBe('case-123');
  });

  test('should handle missing required fields gracefully', async ({ request }) => {
    const user = TEST_USERS.regularUser;
    
    // Try to create a task without a title
    const taskData = createTestTaskData({
      title: '', // Empty title
      description: 'Task without title'
    });

    const response = await invokeTaskAgent(
      request,
      user,
      'create',
      taskData,
      undefined,
      { expectSuccess: false }
    );

    // Should either succeed with a default title or fail gracefully
    if (response.error) {
      assertTaskOperationError(response, 'title');
    } else {
      // If it succeeds, verify a title was generated
      const tasks = dbMocks.getTasksForTenant(user.firm_id);
      const createdTask = tasks[tasks.length - 1]; // Get the last created task
      expect(createdTask.title).toBeTruthy();
      expect(createdTask.title.length).toBeGreaterThan(0);
    }
  });

  test('should create task with natural language input', async ({ request }) => {
    const user = TEST_USERS.regularUser;

    // Use natural language instead of structured data
    const response = await invokeTaskAgent(
      request,
      user,
      'Create a high priority task to call the client about their medical records due tomorrow'
    );

    assertTaskOperationSuccess(response, 'create');
    
    // Verify a task was created
    const tasks = dbMocks.getTasksForTenant(user.firm_id);
    expect(tasks.length).toBeGreaterThan(0);
    
    const createdTask = tasks[tasks.length - 1]; // Get the last created task
    expect(createdTask.title).toContain('call');
    expect(createdTask.title).toContain('client');
  });

  test('should enforce tenant isolation during creation', async ({ request }) => {
    const user1 = TEST_USERS.regularUser; // firm-001
    const user2 = TEST_USERS.otherFirmUser; // firm-002
    
    const taskData = createTestTaskData({
      title: 'Tenant isolation test task'
    });

    // Create task as user1
    await invokeTaskAgent(request, user1, 'create', taskData);
    
    // Verify user2 cannot see user1's task
    const user1Tasks = dbMocks.getTasksForTenant(user1.firm_id);
    const user2Tasks = dbMocks.getTasksForTenant(user2.firm_id);
    
    expect(user1Tasks.length).toBeGreaterThan(0);
    expect(user2Tasks.length).toBe(0);
    
    // Verify the task belongs to the correct tenant
    const createdTask = user1Tasks.find(task => task.title === taskData.title);
    expect(createdTask).toBeDefined();
    expect(createdTask!.tenant_id).toBe(user1.firm_id);
  });
});
