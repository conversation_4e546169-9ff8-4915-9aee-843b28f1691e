import { test, expect } from '@playwright/test';
import { TEST_USERS } from '../utils/auth';
import { 
  invokeTaskAgent, 
  assertTaskOperationSuccess,
  assertTaskOperationError 
} from '../utils/api';
import { DatabaseMocks, setupDatabaseMocks } from '../utils/mocks';

/**
 * E2E tests for Task Deletion via /copilotkit endpoint
 * 
 * These tests verify that the task CRUD agent can successfully
 * delete tasks through the LangGraph workflow.
 */

test.describe('Task Deletion', () => {
  let dbMocks: DatabaseMocks;
  let testTaskId: string;

  test.beforeEach(async ({ page }) => {
    dbMocks = new DatabaseMocks();
    await setupDatabaseMocks(page, dbMocks);
    
    // Add a test task to delete
    const user = TEST_USERS.regularUser;
    const testTask = dbMocks.addTask({
      title: 'Task to be deleted',
      description: 'This task will be deleted in tests',
      status: 'todo',
      priority: 'medium',
      tenant_id: user.firm_id,
      created_by: user.id
    });
    testTaskId = testTask.id;
  });

  test.afterEach(async () => {
    if (dbMocks) {
      dbMocks.clear();
    }
  });

  test('should delete a task successfully', async ({ request }) => {
    const user = TEST_USERS.regularUser;

    // Verify the task exists before deletion
    const taskBeforeDeletion = dbMocks.getTask(testTaskId);
    expect(taskBeforeDeletion).toBeDefined();

    const response = await invokeTaskAgent(
      request,
      user,
      'delete',
      undefined,
      testTaskId
    );

    assertTaskOperationSuccess(response, 'delete');
    
    // Verify the task was deleted from our mock database
    const taskAfterDeletion = dbMocks.getTask(testTaskId);
    expect(taskAfterDeletion).toBeUndefined();
  });

  test('should delete task with natural language', async ({ request }) => {
    const user = TEST_USERS.regularUser;

    const response = await invokeTaskAgent(
      request,
      user,
      `Delete task ${testTaskId}`
    );

    assertTaskOperationSuccess(response, 'delete');
    
    // Verify the task was deleted
    const taskAfterDeletion = dbMocks.getTask(testTaskId);
    expect(taskAfterDeletion).toBeUndefined();
  });

  test('should handle non-existent task deletion gracefully', async ({ request }) => {
    const user = TEST_USERS.regularUser;
    const nonExistentTaskId = 'task-999999';

    const response = await invokeTaskAgent(
      request,
      user,
      'delete',
      undefined,
      nonExistentTaskId,
      { expectSuccess: false }
    );

    assertTaskOperationError(response, 'not found');
  });

  test('should enforce tenant isolation during deletion', async ({ request }) => {
    const user1 = TEST_USERS.regularUser; // firm-001
    const user2 = TEST_USERS.otherFirmUser; // firm-002
    
    // Add a task for user2's firm
    const user2Task = dbMocks.addTask({
      title: 'User2 task - should not be deletable by user1',
      tenant_id: user2.firm_id,
      created_by: user2.id
    });
    
    // User1 should not be able to delete user2's task
    const response = await invokeTaskAgent(
      request,
      user1,
      'delete',
      undefined,
      user2Task.id,
      { expectSuccess: false }
    );

    assertTaskOperationError(response, 'not found');
    
    // Verify the task was not deleted
    const unchangedTask = dbMocks.getTask(user2Task.id);
    expect(unchangedTask).toBeDefined();
    expect(unchangedTask!.title).toBe('User2 task - should not be deletable by user1');
  });

  test('should delete multiple tasks in sequence', async ({ request }) => {
    const user = TEST_USERS.regularUser;
    
    // Add additional tasks
    const task2 = dbMocks.addTask({
      title: 'Second task to delete',
      tenant_id: user.firm_id,
      created_by: user.id
    });
    
    const task3 = dbMocks.addTask({
      title: 'Third task to delete',
      tenant_id: user.firm_id,
      created_by: user.id
    });

    // Delete first task
    const response1 = await invokeTaskAgent(
      request,
      user,
      'delete',
      undefined,
      testTaskId
    );
    assertTaskOperationSuccess(response1, 'delete');

    // Delete second task
    const response2 = await invokeTaskAgent(
      request,
      user,
      'delete',
      undefined,
      task2.id
    );
    assertTaskOperationSuccess(response2, 'delete');

    // Delete third task
    const response3 = await invokeTaskAgent(
      request,
      user,
      'delete',
      undefined,
      task3.id
    );
    assertTaskOperationSuccess(response3, 'delete');

    // Verify all tasks were deleted
    expect(dbMocks.getTask(testTaskId)).toBeUndefined();
    expect(dbMocks.getTask(task2.id)).toBeUndefined();
    expect(dbMocks.getTask(task3.id)).toBeUndefined();
  });

  test('should confirm deletion with task details', async ({ request }) => {
    const user = TEST_USERS.regularUser;

    const response = await invokeTaskAgent(
      request,
      user,
      'delete',
      undefined,
      testTaskId
    );

    assertTaskOperationSuccess(response, 'delete');
    
    // Check that the response mentions the deleted task
    const lastMessage = response.messages![response.messages!.length - 1];
    const content = lastMessage.content.toLowerCase();
    
    // Should mention deletion and possibly the task title
    expect(content).toContain('deleted');
    expect(content).toMatch(/(task|successfully)/);
  });

  test('should handle deletion of completed tasks', async ({ request }) => {
    const user = TEST_USERS.regularUser;
    
    // Add a completed task
    const completedTask = dbMocks.addTask({
      title: 'Completed task to delete',
      status: 'done',
      tenant_id: user.firm_id,
      created_by: user.id
    });

    const response = await invokeTaskAgent(
      request,
      user,
      'delete',
      undefined,
      completedTask.id
    );

    assertTaskOperationSuccess(response, 'delete');
    
    // Verify the completed task was deleted
    const taskAfterDeletion = dbMocks.getTask(completedTask.id);
    expect(taskAfterDeletion).toBeUndefined();
  });

  test('should handle deletion of high priority tasks', async ({ request }) => {
    const user = TEST_USERS.regularUser;
    
    // Add a high priority task
    const urgentTask = dbMocks.addTask({
      title: 'Urgent task to delete',
      priority: 'urgent',
      tenant_id: user.firm_id,
      created_by: user.id
    });

    const response = await invokeTaskAgent(
      request,
      user,
      'delete',
      undefined,
      urgentTask.id
    );

    assertTaskOperationSuccess(response, 'delete');
    
    // Verify the urgent task was deleted
    const taskAfterDeletion = dbMocks.getTask(urgentTask.id);
    expect(taskAfterDeletion).toBeUndefined();
  });
});
