import { test, expect } from '@playwright/test';
import { TEST_USERS } from '../utils/auth';
import { 
  invokeTaskAgent, 
  assertTaskOperationSuccess 
} from '../utils/api';
import { DatabaseMocks, setupDatabaseMocks } from '../utils/mocks';

/**
 * E2E tests for Task Reading via /copilotkit endpoint
 * 
 * These tests verify that the task CRUD agent can successfully
 * read and list tasks through the LangGraph workflow.
 */

test.describe('Task Reading', () => {
  let dbMocks: DatabaseMocks;

  test.beforeEach(async ({ page }) => {
    dbMocks = new DatabaseMocks();
    await setupDatabaseMocks(page, dbMocks);
    
    // Add some test tasks
    const user = TEST_USERS.regularUser;
    dbMocks.addTask({
      title: 'Review contract',
      description: 'Review the client contract',
      status: 'todo',
      priority: 'high',
      tenant_id: user.firm_id,
      created_by: user.id
    });
    
    dbMocks.addTask({
      title: 'File motion',
      description: 'File motion for summary judgment',
      status: 'in_progress',
      priority: 'urgent',
      tenant_id: user.firm_id,
      created_by: user.id
    });
    
    dbMocks.addTask({
      title: 'Call client',
      description: 'Call client about medical records',
      status: 'done',
      priority: 'medium',
      tenant_id: user.firm_id,
      created_by: user.id
    });
    
    // Add a task for a different tenant to test isolation
    dbMocks.addTask({
      title: 'Other firm task',
      description: 'This should not be visible',
      status: 'todo',
      tenant_id: TEST_USERS.otherFirmUser.firm_id,
      created_by: TEST_USERS.otherFirmUser.id
    });
  });

  test.afterEach(async () => {
    if (dbMocks) {
      dbMocks.clear();
    }
  });

  test('should list all tasks for user', async ({ request }) => {
    const user = TEST_USERS.regularUser;

    const response = await invokeTaskAgent(
      request,
      user,
      'list'
    );

    assertTaskOperationSuccess(response, 'read');
    
    // Verify the response contains task information
    const lastMessage = response.messages![response.messages!.length - 1];
    const content = lastMessage.content.toLowerCase();
    
    // Should contain information about the user's tasks
    expect(content).toContain('review contract');
    expect(content).toContain('file motion');
    expect(content).toContain('call client');
    
    // Should NOT contain the other firm's task
    expect(content).not.toContain('other firm task');
  });

  test('should read tasks with natural language query', async ({ request }) => {
    const user = TEST_USERS.regularUser;

    const response = await invokeTaskAgent(
      request,
      user,
      'Show me all my tasks'
    );

    assertTaskOperationSuccess(response, 'read');
    
    // Verify we get a meaningful response
    const lastMessage = response.messages![response.messages!.length - 1];
    expect(lastMessage.content.length).toBeGreaterThan(0);
  });

  test('should filter tasks by status', async ({ request }) => {
    const user = TEST_USERS.regularUser;

    const response = await invokeTaskAgent(
      request,
      user,
      'Show me all my todo tasks'
    );

    assertTaskOperationSuccess(response, 'read');
    
    const lastMessage = response.messages![response.messages!.length - 1];
    const content = lastMessage.content.toLowerCase();
    
    // Should contain todo tasks
    expect(content).toContain('review contract');
    
    // Should not contain completed tasks
    expect(content).not.toContain('call client');
  });

  test('should filter tasks by priority', async ({ request }) => {
    const user = TEST_USERS.regularUser;

    const response = await invokeTaskAgent(
      request,
      user,
      'Show me all my urgent tasks'
    );

    assertTaskOperationSuccess(response, 'read');
    
    const lastMessage = response.messages![response.messages!.length - 1];
    const content = lastMessage.content.toLowerCase();
    
    // Should contain urgent tasks
    expect(content).toContain('file motion');
    
    // Should not contain non-urgent tasks
    expect(content).not.toContain('call client');
  });

  test('should handle empty task list gracefully', async ({ request }) => {
    // Clear all tasks for this test
    dbMocks.clear();
    
    const user = TEST_USERS.regularUser;

    const response = await invokeTaskAgent(
      request,
      user,
      'list'
    );

    assertTaskOperationSuccess(response, 'read');
    
    const lastMessage = response.messages![response.messages!.length - 1];
    const content = lastMessage.content.toLowerCase();
    
    // Should indicate no tasks found
    expect(content).toMatch(/(no tasks|empty|none found)/);
  });

  test('should enforce tenant isolation when reading', async ({ request }) => {
    const user1 = TEST_USERS.regularUser; // firm-001
    const user2 = TEST_USERS.otherFirmUser; // firm-002

    // User1 should see their tasks
    const response1 = await invokeTaskAgent(request, user1, 'list');
    assertTaskOperationSuccess(response1, 'read');
    
    const content1 = response1.messages![response1.messages!.length - 1].content.toLowerCase();
    expect(content1).toContain('review contract');
    expect(content1).not.toContain('other firm task');

    // User2 should only see their task
    const response2 = await invokeTaskAgent(request, user2, 'list');
    assertTaskOperationSuccess(response2, 'read');
    
    const content2 = response2.messages![response2.messages!.length - 1].content.toLowerCase();
    expect(content2).toContain('other firm task');
    expect(content2).not.toContain('review contract');
  });

  test('should read tasks with complex filtering', async ({ request }) => {
    const user = TEST_USERS.regularUser;

    const response = await invokeTaskAgent(
      request,
      user,
      'Show me all high priority tasks that are not completed'
    );

    assertTaskOperationSuccess(response, 'read');
    
    const lastMessage = response.messages![response.messages!.length - 1];
    const content = lastMessage.content.toLowerCase();
    
    // Should contain high priority, non-completed tasks
    expect(content).toContain('review contract'); // high priority, todo
    
    // Should not contain completed tasks even if high priority
    expect(content).not.toContain('call client'); // medium priority, done
  });
});
