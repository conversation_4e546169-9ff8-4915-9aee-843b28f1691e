import { FullConfig } from '@playwright/test';

/**
 * Global setup for Task CRUD E2E tests
 * 
 * This setup runs once before all tests and prepares the test environment.
 * It ensures that the test server is running and the database is in a clean state.
 */
async function globalSetup(config: FullConfig) {
  console.log('🚀 Setting up Task CRUD E2E test environment...');
  
  // Set environment variables for testing
  process.env.APP_ENV = 'test';
  process.env.SUPABASE_JWT_SECRET = 'test-jwt-secret-for-e2e-tests-only';
  process.env.SUPABASE_URL = 'http://localhost:54321';
  process.env.SUPABASE_ANON_KEY = 'test-anon-key';
  process.env.SUPABASE_SERVICE_ROLE_KEY = 'test-service-role-key';
  
  // Log configuration
  console.log('📋 Test configuration:');
  console.log(`   Base URL: ${config.projects[0].use.baseURL}`);
  console.log(`   Environment: ${process.env.APP_ENV}`);
  console.log(`   JWT Secret: ${process.env.SUPABASE_JWT_SECRET ? 'Set' : 'Not set'}`);
  
  // Wait a moment for any async setup to complete
  await new Promise(resolve => setTimeout(resolve, 1000));
  
  console.log('✅ Task CRUD E2E test environment setup complete');
}

export default globalSetup;
