#!/bin/bash

# Task CRUD E2E Test Runner
# 
# This script provides a convenient way to run Task CRUD E2E tests
# with different configurations and options.

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Function to check if dependencies are installed
check_dependencies() {
    print_status "Checking dependencies..."
    
    if ! command -v npm &> /dev/null; then
        print_error "npm is not installed"
        exit 1
    fi
    
    if [ ! -d "node_modules" ]; then
        print_status "Installing npm dependencies..."
        npm install
    fi
    
    if [ ! -d "node_modules/@playwright" ]; then
        print_status "Installing Playwright browsers..."
        npx playwright install
    fi
}

# Function to run specific test suites
run_tests() {
    local test_type="$1"
    
    case "$test_type" in
        "create")
            print_status "Running task creation tests..."
            npx playwright test tests/task-create.spec.ts
            ;;
        "read")
            print_status "Running task read tests..."
            npx playwright test tests/task-read.spec.ts
            ;;
        "update")
            print_status "Running task update tests..."
            npx playwright test tests/task-update.spec.ts
            ;;
        "delete")
            print_status "Running task deletion tests..."
            npx playwright test tests/task-delete.spec.ts
            ;;
        "auth")
            print_status "Running authentication tests..."
            npx playwright test tests/task-auth.spec.ts
            ;;
        "all")
            print_status "Running all task CRUD tests..."
            npx playwright test
            ;;
        "headed")
            print_status "Running tests in headed mode..."
            npx playwright test --headed
            ;;
        "debug")
            print_status "Running tests in debug mode..."
            npx playwright test --debug
            ;;
        "ui")
            print_status "Running tests with UI mode..."
            npx playwright test --ui
            ;;
        *)
            print_error "Unknown test type: $test_type"
            echo "Available options: create, read, update, delete, auth, all, headed, debug, ui"
            exit 1
            ;;
    esac
}

# Main execution
main() {
    print_status "Task CRUD E2E Test Runner"
    print_status "=========================="
    
    # Check dependencies
    check_dependencies
    
    # Get test type from argument or default to 'all'
    local test_type="${1:-all}"
    
    # Run tests
    run_tests "$test_type"
    
    print_status "Tests completed!"
}

# Run main function with all arguments
main "$@"
