# Task CRUD E2E Tests

This directory contains end-to-end tests for the Task CRUD functionality using <PERSON><PERSON>. These tests verify that the Task CRUD agent works correctly through the `/copilotkit` API endpoint.

## Overview

The Task CRUD E2E tests cover the complete workflow of task management operations:

- **Task Creation**: Creating new tasks with various parameters
- **Task Reading**: Listing and filtering tasks
- **Task Updates**: Modifying existing tasks
- **Task Deletion**: Removing tasks
- **Authentication**: JWT-based authentication and tenant isolation

## Test Structure

```
e2e/task/
├── package.json              # Dependencies and scripts
├── playwright.config.ts      # Playwright configuration
├── tsconfig.json            # TypeScript configuration
├── run-tests.sh             # Test runner script
├── setup/                   # Global setup and teardown
│   ├── global-setup.ts
│   └── global-teardown.ts
├── utils/                   # Test utilities
│   ├── auth.ts              # JWT token generation and auth helpers
│   ├── api.ts               # API request helpers for /copilotkit endpoint
│   └── mocks.ts             # Database and mocking utilities
└── tests/                   # Test specifications
    ├── task-create.spec.ts  # Task creation tests
    ├── task-read.spec.ts    # Task reading/listing tests
    ├── task-update.spec.ts  # Task update tests
    ├── task-delete.spec.ts  # Task deletion tests
    └── task-auth.spec.ts    # Authentication and authorization tests
```

## Running Tests

### Prerequisites

1. Install dependencies:
```bash
cd e2e/task
npm install
npx playwright install
```

2. Ensure the test server is running on `http://localhost:8000`

### Running All Tests

```bash
# From the project root
npm run test:e2e:task

# Or from the e2e/task directory
npm run test
```

### Running Specific Test Suites

```bash
# Using the test runner script
./run-tests.sh create    # Task creation tests
./run-tests.sh read      # Task reading tests
./run-tests.sh update    # Task update tests
./run-tests.sh delete    # Task deletion tests
./run-tests.sh auth      # Authentication tests
./run-tests.sh all       # All tests

# Using Playwright directly
npx playwright test tests/task-create.spec.ts
npx playwright test tests/task-read.spec.ts
npx playwright test tests/task-update.spec.ts
npx playwright test tests/task-delete.spec.ts
npx playwright test tests/task-auth.spec.ts
```

### Debug Mode

```bash
# Run tests in headed mode (visible browser)
./run-tests.sh headed

# Run tests in debug mode (step through)
./run-tests.sh debug

# Run tests with UI mode
./run-tests.sh ui
```

## Test Features

### Authentication Testing

- **JWT Token Generation**: Creates valid JWT tokens for test users
- **Token Validation**: Tests expired and invalid tokens
- **Tenant Isolation**: Ensures users can only access their own tenant's data
- **Permission Validation**: Verifies user permissions for different operations

### Task Operations Testing

- **Create Tasks**: Tests task creation with various parameters (title, description, priority, due date, etc.)
- **Read Tasks**: Tests task listing and filtering by status, priority, and other criteria
- **Update Tasks**: Tests updating task properties including status transitions
- **Delete Tasks**: Tests task deletion with proper validation

### Natural Language Processing

- **Natural Language Input**: Tests the agent's ability to understand natural language commands
- **Intent Detection**: Verifies that the agent correctly interprets user intentions
- **Response Validation**: Ensures the agent provides meaningful responses

### Database Mocking

- **In-Memory Database**: Uses mock database for fast, isolated testing
- **Tenant Isolation**: Simulates multi-tenant database behavior
- **CRUD Operations**: Mocks all database operations for tasks

## Test Users

The tests use predefined test users with different roles and tenants:

- **regularUser**: Standard user in firm-001 with basic task permissions
- **adminUser**: Admin user in firm-001 with elevated permissions
- **otherFirmUser**: User in firm-002 for testing tenant isolation

## API Endpoint

The tests interact with the `/copilotkit` endpoint using the following structure:

```typescript
{
  agent: 'task_crud',
  messages: [
    {
      role: 'user',
      content: 'Create a new task to review the contract'
    }
  ],
  stream: false,
  config: {
    firm_id: 'test-firm-001',
    user_id: 'test-user-001'
  },
  state: {
    firm_id: 'test-firm-001',
    user_id: 'test-user-001',
    task_data: { /* task data */ }
  }
}
```

## Environment Variables

The tests use the following environment variables:

- `APP_ENV=test`: Sets the application to test mode
- `SUPABASE_JWT_SECRET`: Secret for JWT token generation
- `SUPABASE_URL`: Supabase URL for testing
- `SUPABASE_ANON_KEY`: Supabase anonymous key
- `SUPABASE_SERVICE_ROLE_KEY`: Supabase service role key

## CI Integration

The tests are integrated into the CI pipeline through:

1. **Root package.json**: Includes `test:e2e` and `test:e2e:task` scripts
2. **GitHub Actions**: Should run these tests as part of the CI workflow
3. **Test Reports**: Generates HTML, JSON, and JUnit reports for CI consumption

## Troubleshooting

### Common Issues

1. **Server Not Running**: Ensure the FastAPI server is running on port 8000
2. **JWT Secret Mismatch**: Verify the JWT secret matches between test and server
3. **Port Conflicts**: Check that port 8000 is available
4. **Dependencies**: Run `npm install` and `npx playwright install`

### Debug Commands

```bash
# Check server health
curl http://localhost:8000/health

# Verify JWT token generation
node -e "console.log(require('./utils/auth').generateTestJWT(require('./utils/auth').TEST_USERS.regularUser))"

# Check test dependencies
npm list
```

## Contributing

When adding new tests:

1. Follow the existing test structure and naming conventions
2. Use the provided utilities for authentication and API calls
3. Ensure proper cleanup in `afterEach` hooks
4. Add appropriate assertions for both success and failure cases
5. Test tenant isolation for multi-tenant operations
6. Update this README if adding new test categories or utilities
