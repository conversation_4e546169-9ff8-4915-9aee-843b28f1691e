# Calendar CRUD E2E Tests

This directory contains comprehensive end-to-end tests for the Calendar CRUD integration using Playwright. The tests verify the complete calendar functionality through the `/invoke` endpoint with proper authentication, database operations, and provider mocking.

## Overview

The E2E tests cover:

- **Calendar Event Creation** - Creating events with various configurations
- **Calendar Event Updates** - Modifying existing events
- **Calendar Event Deletion** - Removing events from calendars
- **Authentication & Authorization** - JWT token validation and tenant isolation
- **Provider Capabilities** - Testing different calendar providers (Google, Outlook, Calendly)
- **Error Handling** - Validation, conflicts, and edge cases

## Test Structure

```
e2e/calendar/
├── tests/                          # Test specifications
│   ├── calendar-create.spec.ts     # Event creation tests
│   ├── calendar-update.spec.ts     # Event update tests
│   ├── calendar-delete.spec.ts     # Event deletion tests
│   ├── calendar-auth.spec.ts       # Authentication tests
│   └── calendar-providers.spec.ts  # Provider capability tests
├── utils/                          # Test utilities
│   ├── auth.ts                     # JWT token generation and auth helpers
│   ├── api.ts                      # API request helpers for /invoke endpoint
│   └── mocks.ts                    # Database and provider mocking utilities
├── setup/                          # Global setup and teardown
│   ├── global-setup.ts             # Test environment initialization
│   └── global-teardown.ts          # Test environment cleanup
├── playwright.config.ts            # Playwright configuration
├── package.json                    # Dependencies and scripts
└── README.md                       # This file
```

## Prerequisites

1. **Node.js** (v18 or higher)
2. **Python** (for the FastAPI server)
3. **Calendar CRUD Agent** implementation
4. **JWT Secret** configured for testing

## Installation

1. Install dependencies:
```bash
cd e2e/calendar
npm install
```

2. Install Playwright browsers:
```bash
npm run install-deps
```

## Configuration

The tests use the following environment variables:

- `BASE_URL` - API server URL (default: http://localhost:8000)
- `SUPABASE_JWT_SECRET` - JWT secret for token generation (default: test secret)
- `APP_ENV` - Set to 'test' for test mode
- `CI` - Set to 'true' in CI environments

## Running Tests

### All Tests
```bash
npm test
```

### Specific Test Suites
```bash
# Authentication tests only
npx playwright test calendar-auth

# Provider capability tests only
npx playwright test calendar-providers

# Event creation tests only
npx playwright test calendar-create
```

### Debug Mode
```bash
npm run test:debug
```

### Headed Mode (with browser UI)
```bash
npm run test:headed
```

### Test UI
```bash
npm run test:ui
```

## Test Features

### Authentication Testing
- **JWT Token Generation** - Creates valid tokens for different user roles
- **Token Validation** - Tests expired, invalid, and malformed tokens
- **Tenant Isolation** - Ensures users can only access their firm's data
- **Permission Checking** - Validates role-based access control

### Database Mocking
- **Event Storage** - Mocks calendar event CRUD operations
- **Provider Configuration** - Simulates provider connection status
- **Tenant Data** - Isolates data by firm/tenant ID
- **Conflict Detection** - Tests event scheduling conflicts

### Provider Mocking
- **Google Calendar API** - Mocks Google Calendar responses
- **Outlook API** - Mocks Microsoft Graph API responses
- **Calendly API** - Mocks Calendly webhook and API responses
- **Capability Matrix** - Tests provider-specific limitations

### Test Data
- **Predefined Users** - Superadmin, admin, regular user, and cross-tenant user
- **Event Templates** - Reusable event data with various configurations
- **Time Scenarios** - Past, present, future, and conflicting time slots

## Test Scenarios

### Calendar Event Creation
- ✅ Basic event creation with all user roles
- ✅ Events with attendees, location, and descriptions
- ✅ Different calendar providers (Google, Outlook, Calendly)
- ✅ Tenant isolation enforcement
- ✅ Authentication requirement validation
- ✅ Invalid data handling
- ✅ Conflict detection
- ✅ Recurring events

### Calendar Event Updates
- ✅ Title and description updates
- ✅ Time and duration changes
- ✅ Attendee list modifications
- ✅ Location updates
- ✅ Cross-tenant access prevention
- ✅ Non-existent event handling
- ✅ Validation error handling
- ✅ Permission-based updates
- ✅ Concurrent update scenarios

### Calendar Event Deletion
- ✅ Single event deletion by all user roles
- ✅ Tenant isolation for deletions
- ✅ Non-existent event deletion attempts
- ✅ Complex event deletion (with attendees, etc.)
- ✅ Provider-specific deletion capabilities
- ✅ Bulk deletion scenarios

### Authentication & Authorization
- ✅ Successful authentication for all user roles
- ✅ Missing token rejection
- ✅ Invalid token rejection
- ✅ Expired token rejection
- ✅ Tenant isolation enforcement
- ✅ JWT claims validation
- ✅ Permission validation
- ✅ Concurrent authentication
- ✅ Malformed header handling

### Provider Capabilities
- ✅ Google Calendar full CRUD operations
- ✅ Outlook Calendar full CRUD operations
- ✅ Calendly limited capabilities (read-only, scheduling links)
- ✅ Free/busy checking across providers
- ✅ Provider authentication status
- ✅ Provider-specific error handling
- ✅ Capability validation before operations
- ✅ Mixed provider scenarios
- ✅ Provider fallback handling

## Reporting

Test results are generated in multiple formats:

- **HTML Report** - Interactive test results viewer
- **JSON Report** - Machine-readable test results
- **JUnit XML** - CI/CD integration format

View the HTML report:
```bash
npm run test:report
```

## CI/CD Integration

The tests are designed for CI/CD environments:

- **Parallel Execution** - Tests run in parallel for speed
- **Retry Logic** - Failed tests are retried in CI
- **Environment Detection** - Adjusts behavior for CI environments
- **Artifact Collection** - Screenshots, videos, and traces on failure

## Troubleshooting

### Common Issues

1. **Server Not Starting**
   - Ensure the FastAPI server is properly configured
   - Check that port 8000 is available
   - Verify environment variables are set

2. **Authentication Failures**
   - Check JWT secret configuration
   - Verify token generation logic
   - Ensure proper header formatting

3. **Mock Issues**
   - Verify route patterns match actual API calls
   - Check mock response formats
   - Ensure proper cleanup between tests

### Debug Tips

1. **Use Debug Mode** - Run with `npm run test:debug` to step through tests
2. **Check Traces** - View Playwright traces for detailed execution logs
3. **Enable Logging** - Set debug environment variables for verbose output
4. **Isolate Tests** - Run individual test files to isolate issues

## Contributing

When adding new tests:

1. Follow the existing test structure and naming conventions
2. Use the provided utilities for authentication and API calls
3. Ensure proper cleanup in test teardown
4. Add appropriate assertions and error handling
5. Update this README with new test scenarios

## Architecture

The E2E tests follow these principles:

- **Isolation** - Each test is independent and can run in any order
- **Mocking** - External dependencies are mocked for reliability
- **Reusability** - Common utilities are shared across tests
- **Maintainability** - Clear structure and comprehensive documentation
- **Scalability** - Easy to add new test scenarios and providers
