import { APIRequestContext, expect } from '@playwright/test';
import { TestUser, getAuthHeader } from './auth';

/**
 * API utilities for Calendar CRUD E2E tests
 * 
 * This module provides utilities for making API requests
 * to the /invoke endpoint and handling responses.
 */

export interface InvokeRequest {
  agent: string;
  messages: Array<{
    role: 'user' | 'assistant' | 'system';
    content: string;
  }>;
  stream?: boolean;
  config?: Record<string, any>;
  state?: Record<string, any>;
}

export interface InvokeResponse {
  messages: Array<{
    role: 'user' | 'assistant' | 'system';
    content: string;
  }>;
  done: boolean;
  threadId?: string;
  error?: string;
}

export interface CalendarEventData {
  title: string;
  description?: string;
  start_time: string;
  end_time: string;
  attendees?: Array<{
    email: string;
    name?: string;
  }>;
  location?: string;
  provider?: 'google' | 'outlook' | 'calendly';
}

/**
 * Make a request to the /invoke endpoint
 */
export async function invokeCalendarAgent(
  request: APIRequestContext,
  user: TestUser,
  operation: string,
  eventData?: CalendarEventData,
  options: {
    stream?: boolean;
    expectSuccess?: boolean;
  } = {}
): Promise<InvokeResponse> {
  const { stream = false, expectSuccess = true } = options;

  const invokeRequest: InvokeRequest = {
    agent: 'calendar_crud',
    messages: [
      {
        role: 'user',
        content: formatCalendarOperation(operation, eventData)
      }
    ],
    stream,
    config: {
      firm_id: user.firm_id,
      user_id: user.id
    },
    state: {
      firm_id: user.firm_id,
      user_id: user.id,
      ...(eventData && { event_data: eventData })
    }
  };

  const response = await request.post('/invoke', {
    headers: {
      ...getAuthHeader(user),
      'Content-Type': 'application/json'
    },
    data: invokeRequest
  });

  if (expectSuccess) {
    expect(response.status()).toBe(200);
  }

  const responseData = await response.json();
  return responseData as InvokeResponse;
}

/**
 * Format calendar operation for the LangGraph agent
 */
function formatCalendarOperation(operation: string, eventData?: CalendarEventData): string {
  switch (operation) {
    case 'create_event':
      if (!eventData) throw new Error('Event data required for create operation');
      return `Create a calendar event with the following details:
Title: ${eventData.title}
Description: ${eventData.description || 'No description'}
Start: ${eventData.start_time}
End: ${eventData.end_time}
${eventData.attendees ? `Attendees: ${eventData.attendees.map(a => a.email).join(', ')}` : ''}
${eventData.location ? `Location: ${eventData.location}` : ''}
${eventData.provider ? `Provider: ${eventData.provider}` : ''}`;

    case 'update_event':
      if (!eventData) throw new Error('Event data required for update operation');
      return `Update the calendar event with the following details:
Title: ${eventData.title}
Description: ${eventData.description || 'No description'}
Start: ${eventData.start_time}
End: ${eventData.end_time}
${eventData.attendees ? `Attendees: ${eventData.attendees.map(a => a.email).join(', ')}` : ''}
${eventData.location ? `Location: ${eventData.location}` : ''}`;

    case 'delete_event':
      if (!eventData) throw new Error('Event data required for delete operation');
      return `Delete the calendar event: ${eventData.title}`;

    case 'list_events':
      return 'List all calendar events for this week';

    case 'check_free_busy':
      if (!eventData) throw new Error('Event data required for free/busy check');
      return `Check availability from ${eventData.start_time} to ${eventData.end_time}`;

    default:
      return operation;
  }
}

/**
 * Create test event data
 */
export function createTestEventData(overrides: Partial<CalendarEventData> = {}): CalendarEventData {
  const now = new Date();
  const startTime = new Date(now.getTime() + 24 * 60 * 60 * 1000); // Tomorrow
  const endTime = new Date(startTime.getTime() + 60 * 60 * 1000); // 1 hour later

  return {
    title: 'Test Calendar Event',
    description: 'This is a test event created by E2E tests',
    start_time: startTime.toISOString(),
    end_time: endTime.toISOString(),
    attendees: [
      { email: '<EMAIL>', name: 'Test Attendee 1' },
      { email: '<EMAIL>', name: 'Test Attendee 2' }
    ],
    location: 'Test Conference Room',
    provider: 'google',
    ...overrides
  };
}

/**
 * Assert that a calendar operation was successful
 */
export function assertCalendarOperationSuccess(response: InvokeResponse, operation: string) {
  expect(response.error).toBeUndefined();
  expect(response.done).toBe(true);
  expect(response.messages).toBeDefined();
  expect(response.messages.length).toBeGreaterThan(0);
  
  const lastMessage = response.messages[response.messages.length - 1];
  expect(lastMessage.role).toBe('assistant');
  expect(lastMessage.content).toContain('success');
}

/**
 * Assert that a calendar operation failed with expected error
 */
export function assertCalendarOperationError(response: InvokeResponse, expectedError?: string) {
  if (response.error) {
    expect(response.error).toBeDefined();
    if (expectedError) {
      expect(response.error).toContain(expectedError);
    }
  } else {
    const lastMessage = response.messages[response.messages.length - 1];
    expect(lastMessage.content).toContain('error');
    if (expectedError) {
      expect(lastMessage.content).toContain(expectedError);
    }
  }
}
