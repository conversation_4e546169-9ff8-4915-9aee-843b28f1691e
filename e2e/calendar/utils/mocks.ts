import { Page, Route } from '@playwright/test';

/**
 * Mock utilities for Calendar CRUD E2E tests
 * 
 * This module provides utilities for mocking database operations,
 * provider APIs, and other external dependencies.
 */

export interface MockCalendarEvent {
  id: string;
  title: string;
  description?: string;
  start_time: string;
  end_time: string;
  attendees: Array<{
    email: string;
    name?: string;
  }>;
  location?: string;
  provider: 'google' | 'outlook' | 'calendly';
  firm_id: string;
  created_by: string;
  created_at: string;
  updated_at: string;
}

export interface MockProvider {
  name: string;
  capabilities: string[];
  connected: boolean;
  auth_status: 'connected' | 'expired' | 'error';
}

/**
 * Mock database responses for calendar operations
 */
export class DatabaseMocks {
  private events: MockCalendarEvent[] = [];
  private providers: MockProvider[] = [];

  constructor() {
    this.setupDefaultProviders();
  }

  private setupDefaultProviders() {
    this.providers = [
      {
        name: 'google',
        capabilities: ['create', 'read', 'update', 'delete', 'free_busy'],
        connected: true,
        auth_status: 'connected'
      },
      {
        name: 'outlook',
        capabilities: ['create', 'read', 'update', 'delete', 'free_busy'],
        connected: true,
        auth_status: 'connected'
      },
      {
        name: 'calendly',
        capabilities: ['read', 'create_link'],
        connected: true,
        auth_status: 'connected'
      }
    ];
  }

  /**
   * Add a mock event to the database
   */
  addEvent(event: Omit<MockCalendarEvent, 'id' | 'created_at' | 'updated_at'>): MockCalendarEvent {
    const now = new Date().toISOString();
    const mockEvent: MockCalendarEvent = {
      ...event,
      id: `mock-event-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
      created_at: now,
      updated_at: now
    };
    
    this.events.push(mockEvent);
    return mockEvent;
  }

  /**
   * Get events for a firm
   */
  getEvents(firmId: string): MockCalendarEvent[] {
    return this.events.filter(event => event.firm_id === firmId);
  }

  /**
   * Update an event
   */
  updateEvent(eventId: string, updates: Partial<MockCalendarEvent>): MockCalendarEvent | null {
    const eventIndex = this.events.findIndex(event => event.id === eventId);
    if (eventIndex === -1) return null;

    this.events[eventIndex] = {
      ...this.events[eventIndex],
      ...updates,
      updated_at: new Date().toISOString()
    };

    return this.events[eventIndex];
  }

  /**
   * Delete an event
   */
  deleteEvent(eventId: string): boolean {
    const eventIndex = this.events.findIndex(event => event.id === eventId);
    if (eventIndex === -1) return false;

    this.events.splice(eventIndex, 1);
    return true;
  }

  /**
   * Get providers for a firm
   */
  getProviders(firmId: string): MockProvider[] {
    return this.providers;
  }

  /**
   * Clear all mock data
   */
  clear() {
    this.events = [];
    this.setupDefaultProviders();
  }
}

/**
 * Setup database mocks for a page
 */
export async function setupDatabaseMocks(page: Page, mocks: DatabaseMocks) {
  // Mock Supabase database calls
  await page.route('**/rest/v1/**', async (route: Route) => {
    const url = route.request().url();
    const method = route.request().method();
    
    // Parse the URL to determine the table and operation
    if (url.includes('/tenants.calendar_events')) {
      await handleCalendarEventsMock(route, method, mocks);
    } else if (url.includes('/tenants.calendar_providers')) {
      await handleProvidersMock(route, method, mocks);
    } else {
      // Pass through other requests
      await route.continue();
    }
  });
}

/**
 * Handle calendar events mock responses
 */
async function handleCalendarEventsMock(route: Route, method: string, mocks: DatabaseMocks) {
  const url = route.request().url();
  const headers = route.request().headers();
  
  // Extract firm_id from headers or URL
  const firmId = extractFirmId(headers, url);
  
  switch (method) {
    case 'GET':
      const events = mocks.getEvents(firmId);
      await route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify(events)
      });
      break;
      
    case 'POST':
      const createData = route.request().postDataJSON();
      const newEvent = mocks.addEvent({
        ...createData,
        firm_id: firmId
      });
      await route.fulfill({
        status: 201,
        contentType: 'application/json',
        body: JSON.stringify([newEvent])
      });
      break;
      
    case 'PATCH':
      const updateData = route.request().postDataJSON();
      const eventId = extractEventId(url);
      const updatedEvent = mocks.updateEvent(eventId, updateData);
      
      if (updatedEvent) {
        await route.fulfill({
          status: 200,
          contentType: 'application/json',
          body: JSON.stringify([updatedEvent])
        });
      } else {
        await route.fulfill({
          status: 404,
          contentType: 'application/json',
          body: JSON.stringify({ error: 'Event not found' })
        });
      }
      break;
      
    case 'DELETE':
      const deleteEventId = extractEventId(url);
      const deleted = mocks.deleteEvent(deleteEventId);
      
      await route.fulfill({
        status: deleted ? 204 : 404,
        contentType: 'application/json',
        body: deleted ? '' : JSON.stringify({ error: 'Event not found' })
      });
      break;
      
    default:
      await route.continue();
  }
}

/**
 * Handle providers mock responses
 */
async function handleProvidersMock(route: Route, method: string, mocks: DatabaseMocks) {
  const headers = route.request().headers();
  const url = route.request().url();
  const firmId = extractFirmId(headers, url);
  
  if (method === 'GET') {
    const providers = mocks.getProviders(firmId);
    await route.fulfill({
      status: 200,
      contentType: 'application/json',
      body: JSON.stringify(providers)
    });
  } else {
    await route.continue();
  }
}

/**
 * Extract firm ID from request headers or URL
 */
function extractFirmId(headers: Record<string, string>, url: string): string {
  // Try to extract from Authorization header (JWT)
  const authHeader = headers['authorization'];
  if (authHeader) {
    // In a real implementation, you'd decode the JWT
    // For testing, we'll use a default firm ID
    return 'test-firm-001';
  }
  
  // Fallback to default
  return 'test-firm-001';
}

/**
 * Extract event ID from URL
 */
function extractEventId(url: string): string {
  const match = url.match(/eq\.([^&]+)/);
  return match ? match[1] : 'unknown-event-id';
}

/**
 * Setup provider mocks for external calendar APIs
 */
export async function setupProviderMocks(page: Page) {
  // Mock Google Calendar API
  await page.route('**/calendar/v3/**', async (route: Route) => {
    const url = route.request().url();
    const method = route.request().method();

    if (url.includes('/events') && method === 'POST') {
      // Mock Google Calendar event creation
      await route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify({
          id: `google-event-${Date.now()}`,
          summary: 'Test Event',
          start: { dateTime: new Date().toISOString() },
          end: { dateTime: new Date(Date.now() + 3600000).toISOString() }
        })
      });
    } else {
      await route.continue();
    }
  });

  // Mock Calendly API
  await page.route('**/calendly.com/api/**', async (route: Route) => {
    const url = route.request().url();
    const method = route.request().method();

    if (url.includes('/scheduled_events') && method === 'GET') {
      // Mock Calendly events list
      await route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify({
          collection: [],
          pagination: { count: 0 }
        })
      });
    } else {
      await route.continue();
    }
  });

  // Mock Outlook API
  await page.route('**/graph.microsoft.com/**', async (route: Route) => {
    const url = route.request().url();
    const method = route.request().method();

    if (url.includes('/events') && method === 'POST') {
      // Mock Outlook event creation
      await route.fulfill({
        status: 201,
        contentType: 'application/json',
        body: JSON.stringify({
          id: `outlook-event-${Date.now()}`,
          subject: 'Test Event',
          start: { dateTime: new Date().toISOString() },
          end: { dateTime: new Date(Date.now() + 3600000).toISOString() }
        })
      });
    } else {
      await route.continue();
    }
  });
}
