{"compilerOptions": {"target": "ES2020", "lib": ["ES2020", "DOM"], "module": "commonjs", "moduleResolution": "node", "strict": true, "esModuleInterop": true, "skipLibCheck": true, "forceConsistentCasingInFileNames": true, "resolveJsonModule": true, "declaration": false, "outDir": "./dist", "rootDir": "./", "baseUrl": "./", "paths": {"@/*": ["./*"]}, "types": ["node", "@playwright/test"]}, "include": ["**/*.ts", "**/*.js"], "exclude": ["node_modules", "dist", "test-results"]}