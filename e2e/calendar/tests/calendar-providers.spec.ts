import { test, expect } from '@playwright/test';
import { TEST_USERS } from '../utils/auth';
import { 
  invokeCalendarAgent, 
  createTestEventData, 
  assertCalendarOperationSuccess,
  assertCalendarOperationError 
} from '../utils/api';
import { DatabaseMocks, setupDatabaseMocks, setupProviderMocks } from '../utils/mocks';

/**
 * E2E tests for Calendar Provider Capabilities via /invoke endpoint
 * 
 * These tests verify that the calendar CRUD agent properly handles
 * different calendar providers and their specific capabilities.
 */

test.describe('Calendar Provider Capabilities', () => {
  let dbMocks: DatabaseMocks;

  test.beforeEach(async ({ page }) => {
    dbMocks = new DatabaseMocks();
    await setupDatabaseMocks(page, dbMocks);
    await setupProviderMocks(page);
  });

  test.afterEach(async () => {
    dbMocks.clear();
  });

  test('should handle Google Calendar provider capabilities', async ({ request }) => {
    // Arrange
    const user = TEST_USERS.admin;
    const eventData = createTestEventData({
      title: 'Google Calendar Test Event',
      provider: 'google'
    });

    // Act - Create event with Google provider
    const createResponse = await invokeCalendarAgent(
      request,
      user,
      'create_event',
      eventData
    );

    // Assert
    assertCalendarOperationSuccess(createResponse, 'create_event');
    
    const events = dbMocks.getEvents(user.firm_id);
    expect(events).toHaveLength(1);
    expect(events[0].provider).toBe('google');

    // Act - Update event (Google supports updates)
    const updateData = createTestEventData({
      title: 'Updated Google Event',
      start_time: eventData.start_time,
      end_time: eventData.end_time,
      provider: 'google'
    });

    const updateResponse = await invokeCalendarAgent(
      request,
      user,
      'update_event',
      updateData
    );

    // Assert
    assertCalendarOperationSuccess(updateResponse, 'update_event');

    // Act - Delete event (Google supports deletion)
    const deleteResponse = await invokeCalendarAgent(
      request,
      user,
      'delete_event',
      eventData
    );

    // Assert
    assertCalendarOperationSuccess(deleteResponse, 'delete_event');
  });

  test('should handle Outlook provider capabilities', async ({ request }) => {
    // Arrange
    const user = TEST_USERS.admin;
    const eventData = createTestEventData({
      title: 'Outlook Calendar Test Event',
      provider: 'outlook'
    });

    // Act - Create event with Outlook provider
    const createResponse = await invokeCalendarAgent(
      request,
      user,
      'create_event',
      eventData
    );

    // Assert
    assertCalendarOperationSuccess(createResponse, 'create_event');
    
    const events = dbMocks.getEvents(user.firm_id);
    expect(events).toHaveLength(1);
    expect(events[0].provider).toBe('outlook');

    // Act - Update event (Outlook supports updates)
    const updateData = createTestEventData({
      title: 'Updated Outlook Event',
      start_time: eventData.start_time,
      end_time: eventData.end_time,
      provider: 'outlook'
    });

    const updateResponse = await invokeCalendarAgent(
      request,
      user,
      'update_event',
      updateData
    );

    // Assert
    assertCalendarOperationSuccess(updateResponse, 'update_event');
  });

  test('should handle Calendly provider limitations', async ({ request }) => {
    // Arrange
    const user = TEST_USERS.admin;
    const eventData = createTestEventData({
      title: 'Calendly Test Event',
      provider: 'calendly'
    });

    // Act - Try to create event with Calendly (limited capabilities)
    const createResponse = await invokeCalendarAgent(
      request,
      user,
      'create_event',
      eventData,
      { expectSuccess: false }
    );

    // Assert - Calendly has limited create capabilities
    // It should either succeed with a scheduling link or explain limitations
    if (createResponse.error) {
      assertCalendarOperationError(createResponse, 'unsupported');
    } else {
      // If it succeeds, it should mention scheduling links
      expect(createResponse.messages[createResponse.messages.length - 1].content)
        .toMatch(/scheduling|link|calendly/i);
    }

    // Act - Try to update Calendly event (not supported)
    const updateData = createTestEventData({
      title: 'Updated Calendly Event',
      provider: 'calendly'
    });

    const updateResponse = await invokeCalendarAgent(
      request,
      user,
      'update_event',
      updateData,
      { expectSuccess: false }
    );

    // Assert - Should fail or explain limitations
    assertCalendarOperationError(updateResponse, 'unsupported');
  });

  test('should check free/busy across providers', async ({ request }) => {
    // Arrange
    const user = TEST_USERS.admin;
    const now = new Date();
    const checkStart = new Date(now.getTime() + 24 * 60 * 60 * 1000);
    const checkEnd = new Date(checkStart.getTime() + 2 * 60 * 60 * 1000);

    const freeBusyData = createTestEventData({
      title: 'Free/Busy Check',
      start_time: checkStart.toISOString(),
      end_time: checkEnd.toISOString()
    });

    // Act - Check free/busy (should work with all providers)
    const response = await invokeCalendarAgent(
      request,
      user,
      'check_free_busy',
      freeBusyData
    );

    // Assert
    assertCalendarOperationSuccess(response, 'check_free_busy');
    
    const lastMessage = response.messages[response.messages.length - 1];
    expect(lastMessage.content).toMatch(/free|busy|available/i);
  });

  test('should handle provider authentication status', async ({ request }) => {
    // Arrange
    const user = TEST_USERS.admin;
    const eventData = createTestEventData({
      title: 'Auth Status Test Event'
    });

    // Test with different providers
    const providers = ['google', 'outlook'] as const;

    for (const provider of providers) {
      // Act
      const testEventData = { ...eventData, provider };
      const response = await invokeCalendarAgent(
        request,
        user,
        'create_event',
        testEventData
      );

      // Assert - Should succeed if provider is connected
      assertCalendarOperationSuccess(response, 'create_event');
    }
  });

  test('should handle provider-specific error scenarios', async ({ request }) => {
    // Arrange
    const user = TEST_USERS.user;

    // Test Google Calendar rate limiting scenario
    const googleEventData = createTestEventData({
      title: 'Google Rate Limit Test',
      provider: 'google'
    });

    // Act - This would normally trigger rate limiting in real scenarios
    const googleResponse = await invokeCalendarAgent(
      request,
      user,
      'create_event',
      googleEventData
    );

    // Assert - Should handle gracefully
    if (googleResponse.error) {
      expect(googleResponse.error).toMatch(/rate|limit|quota/i);
    } else {
      assertCalendarOperationSuccess(googleResponse, 'create_event');
    }
  });

  test('should validate provider capabilities before operations', async ({ request }) => {
    // Arrange
    const user = TEST_USERS.admin;

    // Test operations that require specific capabilities
    const testCases = [
      { provider: 'google', operation: 'create_event', shouldSucceed: true },
      { provider: 'google', operation: 'update_event', shouldSucceed: true },
      { provider: 'google', operation: 'delete_event', shouldSucceed: true },
      { provider: 'outlook', operation: 'create_event', shouldSucceed: true },
      { provider: 'outlook', operation: 'update_event', shouldSucceed: true },
      { provider: 'calendly', operation: 'update_event', shouldSucceed: false },
      { provider: 'calendly', operation: 'delete_event', shouldSucceed: false }
    ];

    for (const testCase of testCases) {
      const eventData = createTestEventData({
        title: `${testCase.provider} ${testCase.operation} test`,
        provider: testCase.provider as any
      });

      // Create event first if testing update/delete
      if (testCase.operation !== 'create_event' && testCase.shouldSucceed) {
        await invokeCalendarAgent(request, user, 'create_event', eventData);
      }

      // Act
      const response = await invokeCalendarAgent(
        request,
        user,
        testCase.operation as any,
        eventData,
        { expectSuccess: testCase.shouldSucceed }
      );

      // Assert
      if (testCase.shouldSucceed) {
        assertCalendarOperationSuccess(response, testCase.operation);
      } else {
        assertCalendarOperationError(response, 'unsupported');
      }

      // Clean up for next test
      dbMocks.clear();
    }
  });

  test('should handle mixed provider scenarios', async ({ request }) => {
    // Arrange
    const user = TEST_USERS.admin;

    // Create events with different providers
    const googleEvent = createTestEventData({
      title: 'Google Event',
      provider: 'google'
    });

    const outlookEvent = createTestEventData({
      title: 'Outlook Event', 
      provider: 'outlook'
    });

    // Act - Create events with different providers
    await invokeCalendarAgent(request, user, 'create_event', googleEvent);
    await invokeCalendarAgent(request, user, 'create_event', outlookEvent);

    // Assert - Both should be created successfully
    const events = dbMocks.getEvents(user.firm_id);
    expect(events).toHaveLength(2);
    
    const providers = events.map(e => e.provider);
    expect(providers).toContain('google');
    expect(providers).toContain('outlook');
  });

  test('should handle provider fallback scenarios', async ({ request }) => {
    // Arrange
    const user = TEST_USERS.admin;
    const eventData = createTestEventData({
      title: 'Fallback Test Event'
      // No provider specified - should use default
    });

    // Act
    const response = await invokeCalendarAgent(
      request,
      user,
      'create_event',
      eventData
    );

    // Assert - Should succeed with default provider
    assertCalendarOperationSuccess(response, 'create_event');
    
    const events = dbMocks.getEvents(user.firm_id);
    expect(events).toHaveLength(1);
    expect(events[0].provider).toBeDefined();
  });
});
