import { test, expect } from '@playwright/test';
import { TEST_USERS } from '../utils/auth';
import { 
  invokeCalendarAgent, 
  createTestEventData, 
  assertCalendarOperationSuccess,
  assertCalendarOperationError 
} from '../utils/api';
import { DatabaseMocks, setupDatabaseMocks } from '../utils/mocks';

/**
 * E2E tests for Calendar Event Creation via /invoke endpoint
 * 
 * These tests verify that the calendar CRUD agent can successfully
 * create calendar events through the LangGraph workflow.
 */

test.describe('Calendar Event Creation', () => {
  let dbMocks: DatabaseMocks;

  test.beforeEach(async ({ page }) => {
    dbMocks = new DatabaseMocks();
    await setupDatabaseMocks(page, dbMocks);
  });

  test.afterEach(async () => {
    dbMocks.clear();
  });

  test('should create a basic calendar event as superadmin', async ({ request }) => {
    // Arrange
    const user = TEST_USERS.superadmin;
    const eventData = createTestEventData({
      title: 'Superadmin Test Meeting',
      description: 'Test meeting created by superadmin',
      provider: 'google'
    });

    // Act
    const response = await invokeCalendarAgent(
      request, 
      user, 
      'create_event', 
      eventData
    );

    // Assert
    assertCalendarOperationSuccess(response, 'create_event');
    
    // Verify the event was created in our mock database
    const events = dbMocks.getEvents(user.firm_id);
    expect(events).toHaveLength(1);
    expect(events[0].title).toBe(eventData.title);
    expect(events[0].firm_id).toBe(user.firm_id);
    expect(events[0].created_by).toBe(user.id);
  });

  test('should create a calendar event with attendees', async ({ request }) => {
    // Arrange
    const user = TEST_USERS.admin;
    const eventData = createTestEventData({
      title: 'Team Meeting with Attendees',
      attendees: [
        { email: '<EMAIL>', name: 'John Doe' },
        { email: '<EMAIL>', name: 'Jane Smith' },
        { email: '<EMAIL>', name: 'Bob Johnson' }
      ]
    });

    // Act
    const response = await invokeCalendarAgent(
      request,
      user,
      'create_event',
      eventData
    );

    // Assert
    assertCalendarOperationSuccess(response, 'create_event');
    
    const events = dbMocks.getEvents(user.firm_id);
    expect(events).toHaveLength(1);
    expect(events[0].attendees).toHaveLength(3);
    expect(events[0].attendees[0].email).toBe('<EMAIL>');
  });

  test('should enforce tenant isolation', async ({ request }) => {
    // Arrange
    const user1 = TEST_USERS.user;
    const user2 = TEST_USERS.otherFirmUser;
    
    const eventData1 = createTestEventData({ title: 'Firm 1 Event' });
    const eventData2 = createTestEventData({ title: 'Firm 2 Event' });

    // Act - Create events for different firms
    await invokeCalendarAgent(request, user1, 'create_event', eventData1);
    await invokeCalendarAgent(request, user2, 'create_event', eventData2);

    // Assert - Each firm should only see their own events
    const firm1Events = dbMocks.getEvents(user1.firm_id);
    const firm2Events = dbMocks.getEvents(user2.firm_id);
    
    expect(firm1Events).toHaveLength(1);
    expect(firm2Events).toHaveLength(1);
    expect(firm1Events[0].title).toBe('Firm 1 Event');
    expect(firm2Events[0].title).toBe('Firm 2 Event');
  });

  test('should reject creation without authentication', async ({ request }) => {
    // Arrange
    const eventData = createTestEventData();

    // Act - Make request without authentication
    const response = await request.post('/invoke', {
      data: {
        agent: 'calendar_crud',
        messages: [{ role: 'user', content: 'Create a calendar event' }]
      }
    });

    // Assert
    expect(response.status()).toBe(401);
  });

  test('should handle invalid event data gracefully', async ({ request }) => {
    // Arrange
    const user = TEST_USERS.user;
    const invalidEventData = createTestEventData({
      title: '', // Invalid: empty title
      start_time: 'invalid-date', // Invalid: malformed date
      end_time: 'invalid-date'
    });

    // Act
    const response = await invokeCalendarAgent(
      request,
      user,
      'create_event',
      invalidEventData,
      { expectSuccess: false }
    );

    // Assert
    assertCalendarOperationError(response, 'validation');
  });

  test('should handle conflicting events', async ({ request }) => {
    // Arrange
    const user = TEST_USERS.user;
    const now = new Date();
    const startTime = new Date(now.getTime() + 24 * 60 * 60 * 1000);
    const endTime = new Date(startTime.getTime() + 60 * 60 * 1000);

    const event1 = createTestEventData({
      title: 'First Event',
      start_time: startTime.toISOString(),
      end_time: endTime.toISOString()
    });

    const event2 = createTestEventData({
      title: 'Conflicting Event',
      start_time: new Date(startTime.getTime() + 30 * 60 * 1000).toISOString(), // 30 min overlap
      end_time: new Date(endTime.getTime() + 30 * 60 * 1000).toISOString()
    });

    // Act - Create first event
    const response1 = await invokeCalendarAgent(request, user, 'create_event', event1);
    assertCalendarOperationSuccess(response1, 'create_event');

    // Act - Try to create conflicting event
    const response2 = await invokeCalendarAgent(
      request, 
      user, 
      'create_event', 
      event2,
      { expectSuccess: false }
    );

    // Assert - Should detect conflict
    assertCalendarOperationError(response2, 'conflict');
  });
});
