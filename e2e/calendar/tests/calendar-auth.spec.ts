import { test, expect } from '@playwright/test';
import { 
  TEST_USERS, 
  generateExpiredJWT, 
  generateInvalidJWT,
  getAuthHeader 
} from '../utils/auth';
import { 
  invokeCalendarAgent, 
  createTestEventData 
} from '../utils/api';
import { DatabaseMocks, setupDatabaseMocks } from '../utils/mocks';

/**
 * E2E tests for Calendar CRUD Authentication and Authorization
 * 
 * These tests verify that the calendar CRUD agent properly handles
 * authentication and authorization through JWT tokens.
 */

test.describe('Calendar CRUD Authentication', () => {
  let dbMocks: DatabaseMocks;

  test.beforeEach(async ({ page }) => {
    dbMocks = new DatabaseMocks();
    await setupDatabaseMocks(page, dbMocks);
  });

  test.afterEach(async () => {
    dbMocks.clear();
  });

  test('should authenticate superadmin user successfully', async ({ request }) => {
    // Arrange
    const user = TEST_USERS.superadmin;
    const eventData = createTestEventData({
      title: 'Superadmin Authentication Test'
    });

    // Act
    const response = await invokeCalendarAgent(
      request,
      user,
      'create_event',
      eventData
    );

    // Assert
    expect(response.error).toBeUndefined();
    expect(response.done).toBe(true);
    
    // Verify the event was created (authentication succeeded)
    const events = dbMocks.getEvents(user.firm_id);
    expect(events).toHaveLength(1);
    expect(events[0].created_by).toBe(user.id);
  });

  test('should authenticate admin user successfully', async ({ request }) => {
    // Arrange
    const user = TEST_USERS.admin;
    const eventData = createTestEventData({
      title: 'Admin Authentication Test'
    });

    // Act
    const response = await invokeCalendarAgent(
      request,
      user,
      'create_event',
      eventData
    );

    // Assert
    expect(response.error).toBeUndefined();
    expect(response.done).toBe(true);
    
    const events = dbMocks.getEvents(user.firm_id);
    expect(events).toHaveLength(1);
  });

  test('should reject requests without authentication token', async ({ request }) => {
    // Arrange
    const eventData = createTestEventData({
      title: 'No Auth Test'
    });

    // Act - Make request without Authorization header
    const response = await request.post('/invoke', {
      headers: {
        'Content-Type': 'application/json'
      },
      data: {
        agent: 'calendar_crud',
        messages: [
          {
            role: 'user',
            content: `Create a calendar event: ${eventData.title}`
          }
        ]
      }
    });

    // Assert
    expect(response.status()).toBe(401);
    
    const responseData = await response.json();
    expect(responseData.detail).toContain('Authentication required');
  });

  test('should reject requests with invalid JWT token', async ({ request }) => {
    // Arrange
    const invalidToken = generateInvalidJWT();
    const eventData = createTestEventData({
      title: 'Invalid Token Test'
    });

    // Act
    const response = await request.post('/invoke', {
      headers: {
        'Authorization': `Bearer ${invalidToken}`,
        'Content-Type': 'application/json'
      },
      data: {
        agent: 'calendar_crud',
        messages: [
          {
            role: 'user',
            content: `Create a calendar event: ${eventData.title}`
          }
        ]
      }
    });

    // Assert
    expect(response.status()).toBe(401);
    
    const responseData = await response.json();
    expect(responseData.detail).toContain('Invalid authentication token');
  });

  test('should reject requests with expired JWT token', async ({ request }) => {
    // Arrange
    const user = TEST_USERS.user;
    const expiredToken = generateExpiredJWT(user);
    const eventData = createTestEventData({
      title: 'Expired Token Test'
    });

    // Act
    const response = await request.post('/invoke', {
      headers: {
        'Authorization': `Bearer ${expiredToken}`,
        'Content-Type': 'application/json'
      },
      data: {
        agent: 'calendar_crud',
        messages: [
          {
            role: 'user',
            content: `Create a calendar event: ${eventData.title}`
          }
        ]
      }
    });

    // Assert
    expect(response.status()).toBe(401);
    
    const responseData = await response.json();
    expect(responseData.detail).toContain('Invalid authentication token');
  });

  test('should enforce tenant isolation between firms', async ({ request }) => {
    // Arrange
    const user1 = TEST_USERS.user; // firm-001
    const user2 = TEST_USERS.otherFirmUser; // firm-002

    const eventData1 = createTestEventData({ title: 'Firm 1 Event' });
    const eventData2 = createTestEventData({ title: 'Firm 2 Event' });

    // Act - Create events for different firms
    await invokeCalendarAgent(request, user1, 'create_event', eventData1);
    await invokeCalendarAgent(request, user2, 'create_event', eventData2);

    // Assert - Each firm should only see their own events
    const firm1Events = dbMocks.getEvents(user1.firm_id);
    const firm2Events = dbMocks.getEvents(user2.firm_id);

    expect(firm1Events).toHaveLength(1);
    expect(firm2Events).toHaveLength(1);
    expect(firm1Events[0].firm_id).toBe(user1.firm_id);
    expect(firm2Events[0].firm_id).toBe(user2.firm_id);
    expect(firm1Events[0].title).toBe('Firm 1 Event');
    expect(firm2Events[0].title).toBe('Firm 2 Event');
  });

  test('should handle malformed Authorization header', async ({ request }) => {
    // Arrange
    const eventData = createTestEventData({
      title: 'Malformed Header Test'
    });

    const malformedHeaders = [
      'Bearer', // Missing token
      'InvalidScheme token', // Wrong scheme
      'Bearer ', // Empty token
      'Bearer token1 token2', // Multiple tokens
    ];

    for (const authHeader of malformedHeaders) {
      // Act
      const response = await request.post('/invoke', {
        headers: {
          'Authorization': authHeader,
          'Content-Type': 'application/json'
        },
        data: {
          agent: 'calendar_crud',
          messages: [
            {
              role: 'user',
              content: `Create a calendar event: ${eventData.title}`
            }
          ]
        }
      });

      // Assert
      expect(response.status()).toBe(401);
    }
  });
});
