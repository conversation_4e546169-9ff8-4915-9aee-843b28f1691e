import { test, expect } from '@playwright/test';
import { TEST_USERS } from '../utils/auth';
import { 
  invokeCalendarAgent, 
  createTestEventData, 
  assertCalendarOperationSuccess,
  assertCalendarOperationError 
} from '../utils/api';
import { DatabaseMocks, setupDatabaseMocks } from '../utils/mocks';

/**
 * E2E tests for Calendar Event Updates via /invoke endpoint
 * 
 * These tests verify that the calendar CRUD agent can successfully
 * update calendar events through the LangGraph workflow.
 */

test.describe('Calendar Event Updates', () => {
  let dbMocks: DatabaseMocks;

  test.beforeEach(async ({ page }) => {
    dbMocks = new DatabaseMocks();
    await setupDatabaseMocks(page, dbMocks);
  });

  test.afterEach(async () => {
    dbMocks.clear();
  });

  test('should update event title and description', async ({ request }) => {
    // Arrange - Create an initial event
    const user = TEST_USERS.admin;
    const initialEventData = createTestEventData({
      title: 'Original Meeting Title',
      description: 'Original description'
    });

    // Create the event first
    await invokeCalendarAgent(request, user, 'create_event', initialEventData);
    const events = dbMocks.getEvents(user.firm_id);
    expect(events).toHaveLength(1);

    // Prepare update data
    const updateData = createTestEventData({
      title: 'Updated Meeting Title',
      description: 'Updated description with more details',
      start_time: initialEventData.start_time,
      end_time: initialEventData.end_time
    });

    // Act - Update the event
    const response = await invokeCalendarAgent(
      request,
      user,
      'update_event',
      updateData
    );

    // Assert
    assertCalendarOperationSuccess(response, 'update_event');
    
    const updatedEvents = dbMocks.getEvents(user.firm_id);
    expect(updatedEvents).toHaveLength(1);
    expect(updatedEvents[0].title).toBe('Updated Meeting Title');
    expect(updatedEvents[0].description).toBe('Updated description with more details');
  });

  test('should update event time and duration', async ({ request }) => {
    // Arrange
    const user = TEST_USERS.user;
    const now = new Date();
    const originalStart = new Date(now.getTime() + 24 * 60 * 60 * 1000);
    const originalEnd = new Date(originalStart.getTime() + 60 * 60 * 1000);

    const initialEventData = createTestEventData({
      title: 'Time Update Test',
      start_time: originalStart.toISOString(),
      end_time: originalEnd.toISOString()
    });

    // Create the event
    await invokeCalendarAgent(request, user, 'create_event', initialEventData);

    // Prepare new time (2 hours later, 2 hours duration)
    const newStart = new Date(originalStart.getTime() + 2 * 60 * 60 * 1000);
    const newEnd = new Date(newStart.getTime() + 2 * 60 * 60 * 1000);

    const updateData = createTestEventData({
      title: 'Time Update Test',
      start_time: newStart.toISOString(),
      end_time: newEnd.toISOString()
    });

    // Act
    const response = await invokeCalendarAgent(
      request,
      user,
      'update_event',
      updateData
    );

    // Assert
    assertCalendarOperationSuccess(response, 'update_event');
    
    const updatedEvents = dbMocks.getEvents(user.firm_id);
    expect(updatedEvents).toHaveLength(1);
    expect(updatedEvents[0].start_time).toBe(newStart.toISOString());
    expect(updatedEvents[0].end_time).toBe(newEnd.toISOString());
  });

  test('should enforce tenant isolation for updates', async ({ request }) => {
    // Arrange
    const user1 = TEST_USERS.user;
    const user2 = TEST_USERS.otherFirmUser;

    // Create event for firm 1
    const eventData = createTestEventData({ title: 'Firm 1 Event' });
    await invokeCalendarAgent(request, user1, 'create_event', eventData);

    // Try to update from firm 2
    const updateData = createTestEventData({
      title: 'Hacked Event Title',
      start_time: eventData.start_time,
      end_time: eventData.end_time
    });

    // Act
    const response = await invokeCalendarAgent(
      request,
      user2,
      'update_event',
      updateData,
      { expectSuccess: false }
    );

    // Assert - Should fail due to tenant isolation
    assertCalendarOperationError(response, 'not found');
    
    // Verify original event is unchanged
    const firm1Events = dbMocks.getEvents(user1.firm_id);
    expect(firm1Events).toHaveLength(1);
    expect(firm1Events[0].title).toBe('Firm 1 Event');
  });

  test('should handle non-existent event updates', async ({ request }) => {
    // Arrange
    const user = TEST_USERS.user;
    const updateData = createTestEventData({
      title: 'Non-existent Event Update'
    });

    // Act - Try to update an event that doesn't exist
    const response = await invokeCalendarAgent(
      request,
      user,
      'update_event',
      updateData,
      { expectSuccess: false }
    );

    // Assert
    assertCalendarOperationError(response, 'not found');
  });

  test('should validate update data', async ({ request }) => {
    // Arrange
    const user = TEST_USERS.user;
    const initialEventData = createTestEventData({
      title: 'Validation Test Event'
    });

    // Create the event
    await invokeCalendarAgent(request, user, 'create_event', initialEventData);

    // Prepare invalid update data
    const invalidUpdateData = createTestEventData({
      title: '', // Invalid: empty title
      start_time: 'invalid-date',
      end_time: 'also-invalid'
    });

    // Act
    const response = await invokeCalendarAgent(
      request,
      user,
      'update_event',
      invalidUpdateData,
      { expectSuccess: false }
    );

    // Assert
    assertCalendarOperationError(response, 'validation');
  });
});
