import { test, expect } from '@playwright/test';
import { TEST_USERS } from '../utils/auth';
import { 
  invokeCalendarAgent, 
  createTestEventData, 
  assertCalendarOperationSuccess,
  assertCalendarOperationError 
} from '../utils/api';
import { DatabaseMocks, setupDatabaseMocks } from '../utils/mocks';

/**
 * E2E tests for Calendar Event Deletion via /invoke endpoint
 * 
 * These tests verify that the calendar CRUD agent can successfully
 * delete calendar events through the LangGraph workflow.
 */

test.describe('Calendar Event Deletion', () => {
  let dbMocks: DatabaseMocks;

  test.beforeEach(async ({ page }) => {
    dbMocks = new DatabaseMocks();
    await setupDatabaseMocks(page, dbMocks);
  });

  test.afterEach(async () => {
    dbMocks.clear();
  });

  test('should delete a calendar event as superadmin', async ({ request }) => {
    // Arrange - Create an event first
    const user = TEST_USERS.superadmin;
    const eventData = createTestEventData({
      title: 'Event to Delete',
      description: 'This event will be deleted'
    });

    // Create the event
    await invokeCalendarAgent(request, user, 'create_event', eventData);
    
    // Verify event was created
    let events = dbMocks.getEvents(user.firm_id);
    expect(events).toHaveLength(1);
    expect(events[0].title).toBe('Event to Delete');

    // Act - Delete the event
    const response = await invokeCalendarAgent(
      request,
      user,
      'delete_event',
      eventData
    );

    // Assert
    assertCalendarOperationSuccess(response, 'delete_event');
    
    // Verify event was deleted
    events = dbMocks.getEvents(user.firm_id);
    expect(events).toHaveLength(0);
  });

  test('should delete event as admin user', async ({ request }) => {
    // Arrange
    const user = TEST_USERS.admin;
    const eventData = createTestEventData({
      title: 'Admin Delete Test',
      description: 'Event to be deleted by admin'
    });

    // Create and then delete
    await invokeCalendarAgent(request, user, 'create_event', eventData);
    
    // Act
    const response = await invokeCalendarAgent(
      request,
      user,
      'delete_event',
      eventData
    );

    // Assert
    assertCalendarOperationSuccess(response, 'delete_event');
    
    const events = dbMocks.getEvents(user.firm_id);
    expect(events).toHaveLength(0);
  });

  test('should enforce tenant isolation for deletions', async ({ request }) => {
    // Arrange
    const user1 = TEST_USERS.user;
    const user2 = TEST_USERS.otherFirmUser;

    // Create events for both firms
    const eventData1 = createTestEventData({ title: 'Firm 1 Event' });
    const eventData2 = createTestEventData({ title: 'Firm 2 Event' });

    await invokeCalendarAgent(request, user1, 'create_event', eventData1);
    await invokeCalendarAgent(request, user2, 'create_event', eventData2);

    // Verify both events exist
    expect(dbMocks.getEvents(user1.firm_id)).toHaveLength(1);
    expect(dbMocks.getEvents(user2.firm_id)).toHaveLength(1);

    // Act - Try to delete firm 1's event from firm 2
    const response = await invokeCalendarAgent(
      request,
      user2,
      'delete_event',
      eventData1, // Firm 1's event
      { expectSuccess: false }
    );

    // Assert - Should fail due to tenant isolation
    assertCalendarOperationError(response, 'not found');
    
    // Verify firm 1's event still exists
    expect(dbMocks.getEvents(user1.firm_id)).toHaveLength(1);
    expect(dbMocks.getEvents(user2.firm_id)).toHaveLength(1);
  });

  test('should handle deletion of non-existent event', async ({ request }) => {
    // Arrange
    const user = TEST_USERS.user;
    const nonExistentEventData = createTestEventData({
      title: 'Non-existent Event'
    });

    // Act - Try to delete an event that doesn't exist
    const response = await invokeCalendarAgent(
      request,
      user,
      'delete_event',
      nonExistentEventData,
      { expectSuccess: false }
    );

    // Assert
    assertCalendarOperationError(response, 'not found');
  });

  test('should delete multiple events selectively', async ({ request }) => {
    // Arrange
    const user = TEST_USERS.admin;
    
    // Create multiple events
    const event1 = createTestEventData({ title: 'Event 1' });
    const event2 = createTestEventData({ title: 'Event 2' });
    const event3 = createTestEventData({ title: 'Event 3' });

    await invokeCalendarAgent(request, user, 'create_event', event1);
    await invokeCalendarAgent(request, user, 'create_event', event2);
    await invokeCalendarAgent(request, user, 'create_event', event3);

    // Verify all events were created
    let events = dbMocks.getEvents(user.firm_id);
    expect(events).toHaveLength(3);

    // Act - Delete only the second event
    const response = await invokeCalendarAgent(
      request,
      user,
      'delete_event',
      event2
    );

    // Assert
    assertCalendarOperationSuccess(response, 'delete_event');
    
    // Verify only the second event was deleted
    events = dbMocks.getEvents(user.firm_id);
    expect(events).toHaveLength(2);
    expect(events.map(e => e.title)).toEqual(
      expect.arrayContaining(['Event 1', 'Event 3'])
    );
    expect(events.map(e => e.title)).not.toContain('Event 2');
  });

  test('should handle deletion without authentication', async ({ request }) => {
    // Arrange
    const eventData = createTestEventData({
      title: 'Unauthorized Delete Test'
    });

    // Act - Try to delete without authentication
    const response = await request.post('/invoke', {
      data: {
        agent: 'calendar_crud',
        messages: [{ 
          role: 'user', 
          content: `Delete the calendar event: ${eventData.title}` 
        }]
      }
    });

    // Assert
    expect(response.status()).toBe(401);
  });
});
