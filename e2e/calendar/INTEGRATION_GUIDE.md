# Calendar CRUD E2E Tests Integration Guide

This guide explains how to integrate the Calendar CRUD E2E tests into your development workflow, CI/CD pipeline, and testing strategy.

## Quick Start

1. **Run all tests:**
```bash
cd e2e/calendar
./run-tests.sh
```

2. **Run specific test suite:**
```bash
./run-tests.sh auth      # Authentication tests
./run-tests.sh create    # Event creation tests
./run-tests.sh providers # Provider capability tests
```

3. **Debug mode:**
```bash
./run-tests.sh debug
```

## Test Coverage

### Authentication & Authorization (calendar-auth.spec.ts)
- ✅ JWT token validation for all user roles (superadmin, admin, user)
- ✅ Authentication failure scenarios (missing, invalid, expired tokens)
- ✅ Tenant isolation enforcement
- ✅ Permission-based access control
- ✅ Concurrent authentication handling
- ✅ Malformed header rejection

### Calendar Event Creation (calendar-create.spec.ts)
- ✅ Basic event creation with all user roles
- ✅ Events with attendees, location, descriptions
- ✅ Different calendar providers (Google, Outlook, Calendly)
- ✅ Tenant isolation enforcement
- ✅ Invalid data validation
- ✅ Conflict detection
- ✅ Recurring events support

### Calendar Event Updates (calendar-update.spec.ts)
- ✅ Title, description, and metadata updates
- ✅ Time and duration modifications
- ✅ Attendee list changes
- ✅ Location updates
- ✅ Cross-tenant access prevention
- ✅ Non-existent event handling
- ✅ Concurrent update scenarios

### Calendar Event Deletion (calendar-delete.spec.ts)
- ✅ Single and bulk event deletion
- ✅ Tenant isolation for deletions
- ✅ Non-existent event handling
- ✅ Complex event deletion (with attendees)
- ✅ Provider-specific deletion capabilities

### Provider Capabilities (calendar-providers.spec.ts)
- ✅ Google Calendar full CRUD operations
- ✅ Outlook Calendar full CRUD operations
- ✅ Calendly limited capabilities testing
- ✅ Free/busy checking across providers
- ✅ Provider authentication status validation
- ✅ Mixed provider scenarios

## Architecture

### Test Structure
```
E2E Tests
├── Authentication Layer (JWT validation)
├── API Layer (/invoke endpoint)
├── Agent Layer (Calendar CRUD LangGraph agent)
├── Database Layer (Mocked Supabase operations)
└── Provider Layer (Mocked external APIs)
```

### Mock Strategy
- **Database Mocks**: In-memory mock of Supabase operations
- **Provider Mocks**: HTTP route interception for external APIs
- **Authentication Mocks**: JWT token generation with configurable claims
- **State Management**: Isolated test data per test case

## CI/CD Integration

### GitHub Actions Example
```yaml
name: Calendar CRUD E2E Tests

on:
  push:
    branches: [main, develop]
  pull_request:
    branches: [main]

jobs:
  e2e-tests:
    runs-on: ubuntu-latest
    
    steps:
    - uses: actions/checkout@v4
    
    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: '20'
        cache: 'npm'
        cache-dependency-path: e2e/calendar/package-lock.json
    
    - name: Setup Python
      uses: actions/setup-python@v5
      with:
        python-version: '3.11'
    
    - name: Install Python dependencies
      run: pip install -r requirements.txt
    
    - name: Install E2E test dependencies
      run: |
        cd e2e/calendar
        npm ci
        npx playwright install
    
    - name: Run Calendar CRUD E2E tests
      run: |
        cd e2e/calendar
        ./run-tests.sh all
      env:
        CI: true
        SUPABASE_JWT_SECRET: ${{ secrets.TEST_JWT_SECRET }}
    
    - name: Upload test results
      uses: actions/upload-artifact@v4
      if: always()
      with:
        name: calendar-e2e-results
        path: e2e/calendar/test-results/
        retention-days: 7
```

### Local Development Workflow

1. **Pre-commit Testing:**
```bash
# Quick smoke test
./run-tests.sh auth

# Full test suite
./run-tests.sh all
```

2. **Feature Development:**
```bash
# Test specific functionality
./run-tests.sh create    # When working on event creation
./run-tests.sh providers # When adding new providers
```

3. **Debugging:**
```bash
# Debug mode with step-through
./run-tests.sh debug

# Visual debugging with browser
./run-tests.sh headed
```

## Environment Configuration

### Required Environment Variables
```bash
# Test environment
export APP_ENV=test

# JWT configuration
export SUPABASE_JWT_SECRET=your-test-jwt-secret

# Database configuration (mocked)
export SUPABASE_URL=http://localhost:54321
export SUPABASE_ANON_KEY=test-anon-key
export SUPABASE_SERVICE_ROLE_KEY=test-service-role-key

# Server configuration
export BASE_URL=http://localhost:8000
```

### Test Data Configuration
The tests use predefined test users and data:

```typescript
// Test users with different roles and permissions
TEST_USERS = {
  superadmin: { firm_id: 'test-firm-001', permissions: ['admin:all'] },
  admin: { firm_id: 'test-firm-001', permissions: ['calendar:*'] },
  user: { firm_id: 'test-firm-001', permissions: ['calendar:read', 'calendar:write'] },
  otherFirmUser: { firm_id: 'test-firm-002', permissions: ['calendar:*'] }
}
```

## Monitoring and Reporting

### Test Reports
- **HTML Report**: Interactive test results with screenshots and traces
- **JSON Report**: Machine-readable results for CI/CD integration
- **JUnit XML**: Standard format for test result aggregation

### Metrics Tracked
- Test execution time
- Success/failure rates
- Provider-specific test results
- Authentication test coverage
- Database operation coverage

### Failure Analysis
1. **Screenshot Capture**: Automatic screenshots on test failures
2. **Video Recording**: Full test execution videos for complex failures
3. **Trace Files**: Detailed execution traces for debugging
4. **Network Logs**: HTTP request/response logs for API debugging

## Extending the Tests

### Adding New Test Cases
1. Create new test file in `tests/` directory
2. Use existing utilities from `utils/` directory
3. Follow naming convention: `calendar-[feature].spec.ts`
4. Update this documentation

### Adding New Providers
1. Add provider configuration to `utils/mocks.ts`
2. Create provider-specific test cases
3. Update capability matrix in tests
4. Add provider mocks for external APIs

### Adding New User Roles
1. Update `TEST_USERS` in `utils/auth.ts`
2. Add role-specific test scenarios
3. Update permission validation tests
4. Document new role capabilities

## Troubleshooting

### Common Issues

1. **Server Startup Failures**
   - Check port 8000 availability
   - Verify Python dependencies
   - Check environment variables

2. **Authentication Failures**
   - Verify JWT secret configuration
   - Check token expiration settings
   - Validate user permissions

3. **Mock Issues**
   - Verify route patterns
   - Check mock response formats
   - Ensure proper cleanup

### Debug Commands
```bash
# Check server health
curl http://localhost:8000/health

# Verify JWT token
node -e "console.log(require('jsonwebtoken').decode('YOUR_TOKEN'))"

# Check test dependencies
cd e2e/calendar && npm list
```

## Performance Considerations

### Test Execution Time
- **Parallel Execution**: Tests run in parallel for faster execution
- **Selective Testing**: Run specific test suites during development
- **Mock Optimization**: In-memory mocks for fast database operations

### Resource Usage
- **Browser Management**: Automatic browser cleanup after tests
- **Memory Management**: Isolated test contexts prevent memory leaks
- **Network Optimization**: Local mocks reduce external API calls

## Security Considerations

### Test Data Security
- **Isolated Test Environment**: Tests use separate test database
- **Mock Credentials**: No real API keys or credentials in tests
- **Temporary Tokens**: JWT tokens expire quickly and are test-only

### Access Control Testing
- **Tenant Isolation**: Comprehensive testing of multi-tenant security
- **Permission Validation**: Role-based access control verification
- **Authentication Bypass Prevention**: Tests ensure auth is required

## Maintenance

### Regular Maintenance Tasks
1. **Update Dependencies**: Keep Playwright and test dependencies current
2. **Review Test Data**: Ensure test users and scenarios remain relevant
3. **Mock Updates**: Update provider mocks when APIs change
4. **Performance Monitoring**: Track test execution times and optimize

### Version Compatibility
- **Playwright**: Keep updated for latest browser support
- **Node.js**: Use LTS versions for stability
- **Python**: Ensure compatibility with FastAPI server
- **Calendar Providers**: Update mocks when provider APIs change
