#!/bin/bash

# Calendar CRUD E2E Test Runner
# 
# This script runs the Playwright-powered E2E tests for the Calendar CRUD integration.
# It handles environment setup, server startup, test execution, and cleanup.

set -e  # Exit on any error

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
BASE_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(cd "$BASE_DIR/../.." && pwd)"
SERVER_PID=""
TEST_MODE="${1:-all}"

# Logging functions
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Cleanup function
cleanup() {
    log_info "Cleaning up..."
    
    if [ ! -z "$SERVER_PID" ]; then
        log_info "Stopping FastAPI server (PID: $SERVER_PID)"
        kill $SERVER_PID 2>/dev/null || true
        wait $SERVER_PID 2>/dev/null || true
    fi
    
    # Kill any remaining processes on port 8000
    lsof -ti:8000 | xargs kill -9 2>/dev/null || true
    
    log_success "Cleanup completed"
}

# Set up trap for cleanup
trap cleanup EXIT INT TERM

# Check prerequisites
check_prerequisites() {
    log_info "Checking prerequisites..."
    
    # Check Node.js
    if ! command -v node &> /dev/null; then
        log_error "Node.js is not installed"
        exit 1
    fi
    
    # Check Python
    if ! command -v python &> /dev/null && ! command -v python3 &> /dev/null; then
        log_error "Python is not installed"
        exit 1
    fi
    
    # Check if we're in the right directory
    if [ ! -f "$BASE_DIR/package.json" ]; then
        log_error "package.json not found. Are you in the right directory?"
        exit 1
    fi
    
    log_success "Prerequisites check passed"
}

# Install dependencies
install_dependencies() {
    log_info "Installing dependencies..."
    
    cd "$BASE_DIR"
    
    # Install npm dependencies
    if [ ! -d "node_modules" ]; then
        log_info "Installing npm packages..."
        npm install
    fi
    
    # Install Playwright browsers
    log_info "Installing Playwright browsers..."
    npx playwright install
    
    log_success "Dependencies installed"
}

# Start the FastAPI server
start_server() {
    log_info "Starting FastAPI server..."
    
    cd "$PROJECT_ROOT"
    
    # Set environment variables for testing
    export APP_ENV=test
    export SUPABASE_JWT_SECRET=test-jwt-secret-for-e2e-tests-only
    export SUPABASE_URL=http://localhost:54321
    export SUPABASE_ANON_KEY=test-anon-key
    export SUPABASE_SERVICE_ROLE_KEY=test-service-role-key
    
    # Start the server in the background
    python minimal_fastapi_server.py &
    SERVER_PID=$!
    
    log_info "FastAPI server started with PID: $SERVER_PID"
    
    # Wait for server to be ready
    log_info "Waiting for server to be ready..."
    for i in {1..30}; do
        if curl -s http://localhost:8000/health > /dev/null 2>&1; then
            log_success "Server is ready"
            return 0
        fi
        sleep 2
    done
    
    log_error "Server failed to start within 60 seconds"
    exit 1
}

# Run the tests
run_tests() {
    log_info "Running Calendar CRUD E2E tests..."
    
    cd "$BASE_DIR"
    
    case "$TEST_MODE" in
        "all")
            log_info "Running all tests..."
            npx playwright test
            ;;
        "auth")
            log_info "Running authentication tests..."
            npx playwright test calendar-auth
            ;;
        "create")
            log_info "Running event creation tests..."
            npx playwright test calendar-create
            ;;
        "update")
            log_info "Running event update tests..."
            npx playwright test calendar-update
            ;;
        "delete")
            log_info "Running event deletion tests..."
            npx playwright test calendar-delete
            ;;
        "providers")
            log_info "Running provider capability tests..."
            npx playwright test calendar-providers
            ;;
        "debug")
            log_info "Running tests in debug mode..."
            npx playwright test --debug
            ;;
        "headed")
            log_info "Running tests in headed mode..."
            npx playwright test --headed
            ;;
        "ui")
            log_info "Opening Playwright UI..."
            npx playwright test --ui
            ;;
        *)
            log_error "Unknown test mode: $TEST_MODE"
            log_info "Available modes: all, auth, create, update, delete, providers, debug, headed, ui"
            exit 1
            ;;
    esac
    
    local exit_code=$?
    
    if [ $exit_code -eq 0 ]; then
        log_success "All tests passed!"
    else
        log_error "Some tests failed (exit code: $exit_code)"
    fi
    
    return $exit_code
}

# Generate test report
generate_report() {
    log_info "Generating test report..."
    
    cd "$BASE_DIR"
    
    if [ -d "test-results" ]; then
        npx playwright show-report
    else
        log_warning "No test results found"
    fi
}

# Main execution
main() {
    log_info "Starting Calendar CRUD E2E Test Runner"
    log_info "Test mode: $TEST_MODE"
    log_info "Base directory: $BASE_DIR"
    log_info "Project root: $PROJECT_ROOT"
    
    check_prerequisites
    install_dependencies
    start_server
    
    local test_exit_code=0
    run_tests || test_exit_code=$?
    
    if [ "$TEST_MODE" != "ui" ] && [ "$TEST_MODE" != "debug" ]; then
        generate_report
    fi
    
    if [ $test_exit_code -eq 0 ]; then
        log_success "Calendar CRUD E2E tests completed successfully!"
    else
        log_error "Calendar CRUD E2E tests failed!"
    fi
    
    exit $test_exit_code
}

# Show usage if help is requested
if [ "$1" = "--help" ] || [ "$1" = "-h" ]; then
    echo "Calendar CRUD E2E Test Runner"
    echo ""
    echo "Usage: $0 [TEST_MODE]"
    echo ""
    echo "Test modes:"
    echo "  all        Run all tests (default)"
    echo "  auth       Run authentication tests only"
    echo "  create     Run event creation tests only"
    echo "  update     Run event update tests only"
    echo "  delete     Run event deletion tests only"
    echo "  providers  Run provider capability tests only"
    echo "  debug      Run tests in debug mode"
    echo "  headed     Run tests with browser UI"
    echo "  ui         Open Playwright test UI"
    echo ""
    echo "Examples:"
    echo "  $0                 # Run all tests"
    echo "  $0 auth           # Run authentication tests"
    echo "  $0 debug          # Run in debug mode"
    echo "  $0 ui             # Open test UI"
    exit 0
fi

# Run main function
main
