"""
Pydantic schemas for subscription API.

This module defines the request and response models for subscription-related API endpoints.
"""

from datetime import datetime
from typing import List, Optional, Dict, Any
from uuid import UUID

from pydantic import BaseModel, Field


class FeatureListResponse(BaseModel):
    """Response model for tenant features endpoint."""
    
    tenant_id: UUID = Field(..., description="The tenant ID")
    features: List[str] = Field(..., description="List of enabled feature codes")
    
    class Config:
        """Pydantic configuration."""
        json_encoders = {
            UUID: str
        }
        schema_extra = {
            "example": {
                "tenant_id": "123e4567-e89b-12d3-a456-************",
                "features": [
                    "voice_intake",
                    "calendar_booking",
                    "document_analysis",
                    "ai_research",
                    "case_management"
                ]
            }
        }


class SubscriptionPlanSchema(BaseModel):
    """Schema for subscription plan data."""
    
    id: UUID
    name: str
    code: str
    description: Optional[str] = None
    is_active: bool
    is_public: bool
    base_price_monthly: float
    base_price_yearly: float
    features: Dict[str, Any]
    created_at: datetime
    updated_at: datetime
    
    class Config:
        """Pydantic configuration."""
        from_attributes = True
        json_encoders = {
            UUID: str
        }


class SubscriptionAddonSchema(BaseModel):
    """Schema for subscription addon data."""
    
    id: UUID
    name: str
    code: str
    description: Optional[str] = None
    category: str
    is_active: bool
    price_monthly: float
    price_yearly: float
    features: Dict[str, Any]
    created_at: datetime
    updated_at: datetime
    
    class Config:
        """Pydantic configuration."""
        from_attributes = True
        json_encoders = {
            UUID: str
        }


class TenantSubscriptionSchema(BaseModel):
    """Schema for tenant subscription data."""
    
    id: UUID
    tenant_id: UUID
    plan_id: UUID
    status: str
    billing_cycle: str
    trial_start: Optional[datetime] = None
    trial_end: Optional[datetime] = None
    current_period_start: datetime
    current_period_end: datetime
    canceled_at: Optional[datetime] = None
    payment_provider: Optional[str] = None
    payment_provider_subscription_id: Optional[str] = None
    subscription_metadata: Dict[str, Any] = Field(alias="metadata")
    created_at: datetime
    updated_at: datetime
    
    class Config:
        """Pydantic configuration."""
        from_attributes = True
        json_encoders = {
            UUID: str
        }


class TenantAddonSchema(BaseModel):
    """Schema for tenant addon data."""
    
    id: UUID
    tenant_id: UUID
    subscription_id: UUID
    addon_id: UUID
    status: str
    quantity: int
    current_period_start: datetime
    current_period_end: datetime
    canceled_at: Optional[datetime] = None
    addon_metadata: Dict[str, Any] = Field(alias="metadata")
    created_at: datetime
    updated_at: datetime
    
    class Config:
        """Pydantic configuration."""
        from_attributes = True
        json_encoders = {
            UUID: str
        }


class ErrorResponse(BaseModel):
    """Standard error response model."""
    
    error: str = Field(..., description="Error message")
    detail: Optional[str] = Field(None, description="Additional error details")
    
    class Config:
        """Pydantic configuration."""
        schema_extra = {
            "example": {
                "error": "Tenant not found",
                "detail": "No active subscription found for tenant"
            }
        }
