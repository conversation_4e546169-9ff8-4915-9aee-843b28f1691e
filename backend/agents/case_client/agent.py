"""
Case and Client CRUD Agent Implementation

This module implements the Case and Client CRUD Agent, which is responsible for:
1. Creating new cases and clients
2. Reading cases and clients with filtering
3. Updating case and client properties
4. Deleting cases and clients with proper validation

The Case and Client CRUD Agent serves as an interactive agent for case and
client management operations,
providing a natural language interface for case and client-related operations.
"""

import logging
from typing import Dict, Any, Optional, List
from uuid import UUID

from langchain_core.runnables import RunnableConfig
from langchain_core.messages import HumanMessage, AIMessage
from langgraph.graph import StateGraph, END

from backend.agents.case_client.router import case_client_router
from backend.agents.case_client.nodes import (
    create_case,
    read_case,
    update_case,
    delete_case,
    create_client,
    read_client,
    update_client,
    delete_client,
)

# Set up logging
logger = logging.getLogger(__name__)

class CaseClientAgent:
    """
    Case and Client CRUD Agent for managing cases and clients.

    This agent provides a natural language interface for case and client
    management operations,
    including creating, reading, updating, and deleting cases and clients.
    """

    def __init__(self, agent_name: str = "caseCrudAgent", node_name: str = ""):
        """
        Initialize the Case and Client CRUD Agent.

        Args:
            agent_name: Name of the agent for LLM selection
            node_name: Name of the node for LLM selection
        """
        self.agent_name = agent_name
        self.node_name = node_name
        self.graph = self._build_graph()

    def _build_graph(self) -> StateGraph:
        """
        Build the agent graph.

        Returns:
            StateGraph: The agent graph
        """
        # Create the graph
        graph = StateGraph(input=Dict, output=Dict)

        # Add nodes
        graph.add_node("router", case_client_router)
        graph.add_node("create_case", create_case)
        graph.add_node("read_case", read_case)
        graph.add_node("update_case", update_case)
        graph.add_node("delete_case", delete_case)
        graph.add_node("create_client", create_client)
        graph.add_node("read_client", read_client)
        graph.add_node("update_client", update_client)
        graph.add_node("delete_client", delete_client)

        # Add edges
        graph.add_edge("router", "create_case")
        graph.add_edge("router", "read_case")
        graph.add_edge("router", "update_case")
        graph.add_edge("router", "delete_case")
        graph.add_edge("router", "create_client")
        graph.add_edge("router", "read_client")
        graph.add_edge("router", "update_client")
        graph.add_edge("router", "delete_client")

        # Add edges from operation nodes to END
        graph.add_edge("create_case", END)
        graph.add_edge("read_case", END)
        graph.add_edge("update_case", END)
        graph.add_edge("delete_case", END)
        graph.add_edge("create_client", END)
        graph.add_edge("read_client", END)
        graph.add_edge("update_client", END)
        graph.add_edge("delete_client", END)

        # Set the entry point
        graph.set_entry_point("router")

        # Compile the graph
        return graph.compile()

    async def invoke(
        self, state: Dict[str, Any], config: Optional[RunnableConfig] = None
    ) -> Dict[str, Any]:
        """
        Invoke the agent.

        Args:
            state: Agent state
            config: Runnable configuration

        Returns:
            Updated state
        """
        logger.info("Invoking Case and Client CRUD Agent")

        # Set default config if not provided
        if config is None:
            config = {}

        # Extract configurable parameters
        configurable = config.get("configurable", {})
        tenant_id = configurable.get("tenant_id")
        user_id = configurable.get("user_id")
        thread_id = configurable.get("thread_id")

        # Log the invocation
        logger.info(
            f"Invoking Case and Client CRUD Agent for tenant {tenant_id}, "
            f"user {user_id}, thread {thread_id}"
        )

        # Add tenant_id and user_id to state if not present
        if tenant_id and "tenant_id" not in state:
            state["tenant_id"] = tenant_id

        if user_id and "user_id" not in state:
            state["user_id"] = user_id

        # Execute the graph
        result = await self.graph.ainvoke(state, config)

        return result
