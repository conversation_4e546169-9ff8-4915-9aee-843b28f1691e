"""
Database utilities for the Case and Client CRUD Agent.

This module provides functions for interacting with the database for case and
client operations.
"""

import logging
from typing import Dict, Any, List, Optional
from uuid import UUID
from datetime import datetime, date, timezone

from sqlalchemy import select, update, delete
from sqlalchemy.ext.asyncio import AsyncSession

from backend.models import Case, Client
from backend.db.session import get_db

# Set up logging
logger = logging.getLogger(__name__)

# Case database functions
async def create_case_db(
    tenant_id: UUID,
    title: str,
    description: Optional[str] = None,
    status: str = "active",
    case_type: Optional[str] = None,
    sensitive: bool = False,
    client_id: Optional[UUID] = None,
    created_by: Optional[UUID] = None,
    case_metadata: Optional[Dict[str, Any]] = None,
) -> Optional[Dict[str, Any]]:
    """
    Create a new case in the database.

    Args:
        tenant_id: Tenant ID
        title: Case title
        description: Case description
        status: Case status
        case_type: Case type
        sensitive: Whether the case contains sensitive information
        client_id: Client ID
        created_by: User ID of the creator
        metadata: Additional metadata

    Returns:
        The created case
    """
    logger.info(f"Creating case: {title}")

    async with get_db() as session:
        # Create the case
        case = Case(
            tenant_id=tenant_id,
            title=title,
            description=description,
            status=status,
            case_type=case_type,
            sensitive=sensitive,
            client_id=client_id,
            created_by=created_by,
            created_at=datetime.now(timezone.utc),
            case_metadata=case_metadata or {},
        )

        # Add the case to the session
        session.add(case)

        # Commit the transaction
        await session.commit()

        # Refresh the case to get the generated ID
        await session.refresh(case)

        # Convert to dictionary
        return {
            "id": str(case.id),
            "tenant_id": str(case.tenant_id),
            "title": case.title,
            "description": case.description,
            "status": case.status,
            "case_type": case.case_type,
            "sensitive": case.sensitive,
            "client_id": str(case.client_id) if case.client_id else None,
            "created_by": str(case.created_by) if case.created_by else None,
            "created_at": case.created_at.isoformat(),
            "metadata": case.metadata,
        }

async def get_case_db(
    session: AsyncSession,
    case_id: UUID,
    tenant_id: UUID,
) -> Optional[Case]:
    """
    Get a case by ID.

    Args:
        session: Database session
        case_id: Case ID
        tenant_id: Tenant ID

    Returns:
        The case if found, None otherwise
    """
    # Query the case
    result = await session.execute(
        select(Case)
        .where(Case.id == case_id)
        .where(Case.tenant_id == tenant_id)
    )

    # Get the case
    return result.scalar_one_or_none()

async def get_case_by_id(
    case_id: UUID,
    tenant_id: UUID,
) -> Optional[Dict[str, Any]]:
    """
    Get a case by ID.

    Args:
        case_id: Case ID
        tenant_id: Tenant ID

    Returns:
        The case if found, None otherwise
    """
    logger.info(f"Getting case: {case_id}")

    async with get_db() as session:
        # Get the case
        case = await get_case_db(session, case_id, tenant_id)

        # Return None if not found
        if not case:
            return None

        # Convert to dictionary
        return {
            "id": str(case.id),
            "tenant_id": str(case.tenant_id),
            "title": case.title,
            "description": case.description,
            "status": case.status,
            "case_type": case.case_type,
            "sensitive": case.sensitive,
            "client_id": str(case.client_id) if case.client_id else None,
            "created_by": str(case.created_by) if case.created_by else None,
            "created_at": case.created_at.isoformat(),
            "updated_at": case.updated_at.isoformat() if case.updated_at else None,
            "case_metadata": case.case_metadata,
        }

async def list_cases_db(
    tenant_id: UUID,
    status: Optional[str] = None,
    client_id: Optional[UUID] = None,
    limit: int = 50,
    offset: int = 0,
) -> List[Dict[str, Any]]:
    """
    List cases with optional filtering.

    Args:
        tenant_id: Tenant ID
        status: Filter by status
        client_id: Filter by client ID
        limit: Maximum number of cases to return
        offset: Offset for pagination

    Returns:
        List of cases
    """
    logger.info(f"Listing cases for tenant: {tenant_id}")

    async with get_db() as session:
        # Build the query
        query = select(Case).where(Case.tenant_id == tenant_id)

        # Apply filters
        if status:
            query = query.where(Case.status == status)

        if client_id:
            query = query.where(Case.client_id == client_id)

        # Apply pagination
        query = query.limit(limit).offset(offset)

        # Execute the query
        result = await session.execute(query)

        # Get the cases
        cases = result.scalars().all()

        # Convert to dictionaries
        return [
            {
                "id": str(case.id),
                "tenant_id": str(case.tenant_id),
                "title": case.title,
                "description": case.description,
                "status": case.status,
                "case_type": case.case_type,
                "sensitive": case.sensitive,
                "client_id": str(case.client_id) if case.client_id else None,
                "created_by": str(case.created_by) if case.created_by else None,
                "created_at": case.created_at.isoformat(),
                "updated_at": case.updated_at.isoformat() if case.updated_at else None,
                "case_metadata": case.case_metadata,
            }
            for case in cases
        ]

async def update_case_db(
    case_id: UUID,
    tenant_id: UUID,
    title: Optional[str] = None,
    description: Optional[str] = None,
    status: Optional[str] = None,
    case_type: Optional[str] = None,
    sensitive: Optional[bool] = None,
    client_id: Optional[UUID] = None,
    updated_by: Optional[UUID] = None,
    case_metadata: Optional[Dict[str, Any]] = None,
) -> Optional[Dict[str, Any]]:
    """
    Update a case.

    Args:
        case_id: Case ID
        tenant_id: Tenant ID
        title: Case title
        description: Case description
        status: Case status
        case_type: Case type
        sensitive: Whether the case contains sensitive information
        client_id: Client ID
        updated_by: User ID of the updater
        metadata: Additional metadata

    Returns:
        The updated case if found, None otherwise
    """
    logger.info(f"Updating case: {case_id}")

    async with get_db() as session:
        # Get the case
        case = await get_case_db(session, case_id, tenant_id)

        # Return None if not found
        if not case:
            return None

        # Update the case
        if title is not None:
            case.title = title

        if description is not None:
            case.description = description

        if status is not None:
            case.status = status

        if case_type is not None:
            case.case_type = case_type

        if sensitive is not None:
            case.sensitive = sensitive

        if client_id is not None:
            case.client_id = client_id

        if updated_by is not None:
            case.updated_by = updated_by

        if case_metadata is not None:
            case.case_metadata = case_metadata

        # Set the updated_at timestamp
        case.updated_at = datetime.now(timezone.utc)

        # Commit the transaction
        await session.commit()

        # Refresh the case
        await session.refresh(case)

        # Convert to dictionary
        return {
            "id": str(case.id),
            "tenant_id": str(case.tenant_id),
            "title": case.title,
            "description": case.description,
            "status": case.status,
            "case_type": case.case_type,
            "sensitive": case.sensitive,
            "client_id": str(case.client_id) if case.client_id else None,
            "created_by": str(case.created_by) if case.created_by else None,
            "created_at": case.created_at.isoformat(),
            "updated_at": case.updated_at.isoformat() if case.updated_at else None,
            "updated_by": str(case.updated_by) if case.updated_by else None,
            "case_metadata": case.case_metadata,
        }

async def delete_case_db(
    case_id: UUID,
    tenant_id: UUID,
) -> bool:
    """
    Delete a case.

    Args:
        case_id: Case ID
        tenant_id: Tenant ID

    Returns:
        True if the case was deleted, False otherwise
    """
    logger.info(f"Deleting case: {case_id}")

    async with get_db() as session:
        # Get the case
        case = await get_case_db(session, case_id, tenant_id)

        # Return False if not found
        if not case:
            return False

        # Delete the case
        await session.delete(case)

        # Commit the transaction
        await session.commit()

        return True

# Client database functions
async def create_client_db(
    tenant_id: UUID,
    first_name: str,
    last_name: str,
    middle_name: Optional[str] = None,
    date_of_birth: Optional[date] = None,
    ssn_last_four: Optional[str] = None,
    email: Optional[str] = None,
    phone_primary: Optional[str] = None,
    phone_secondary: Optional[str] = None,
    preferred_contact_method: Optional[str] = None,
    address: Optional[Dict[str, Any]] = None,
    occupation: Optional[str] = None,
    employer: Optional[str] = None,
    notes: Optional[str] = None,
    emergency_contact: Optional[Dict[str, Any]] = None,
    created_by: Optional[UUID] = None,
    client_metadata: Optional[Dict[str, Any]] = None,
) -> Optional[Dict[str, Any]]:
    """
    Create a new client in the database.

    Args:
        tenant_id: Tenant ID
        first_name: Client's first name
        last_name: Client's last name
        middle_name: Client's middle name
        date_of_birth: Client's date of birth
        ssn_last_four: Last four digits of SSN
        email: Client's email address
        phone_primary: Client's primary phone number
        phone_secondary: Client's secondary phone number
        preferred_contact_method: Client's preferred contact method
        address: Client's address
        occupation: Client's occupation
        employer: Client's employer
        notes: Notes about the client
        emergency_contact: Client's emergency contact information
        created_by: User ID of the creator
        metadata: Additional metadata

    Returns:
        The created client
    """
    logger.info(f"Creating client: {first_name} {last_name}")

    async with get_db() as session:
        # Create the client
        client = Client(
            tenant_id=tenant_id,
            first_name=first_name,
            last_name=last_name,
            middle_name=middle_name,
            date_of_birth=date_of_birth,
            ssn_last_four=ssn_last_four,
            email=email,
            phone_primary=phone_primary,
            phone_secondary=phone_secondary,
            preferred_contact_method=preferred_contact_method,
            address=address or {},
            occupation=occupation,
            employer=employer,
            notes=notes,
            emergency_contact=emergency_contact,
            created_by=created_by,
            created_at=datetime.now(timezone.utc),
            client_metadata=client_metadata or {},
        )

        # Add the client to the session
        session.add(client)

        # Commit the transaction
        await session.commit()

        # Refresh the client to get the generated ID
        await session.refresh(client)

        # Convert to dictionary
        return {
            "id": str(client.id),
            "tenant_id": str(client.tenant_id),
            "first_name": client.first_name,
            "last_name": client.last_name,
            "middle_name": client.middle_name,
            "date_of_birth": (
                client.date_of_birth.isoformat() if client.date_of_birth else None
            ),
            "ssn_last_four": client.ssn_last_four,
            "email": client.email,
            "phone_primary": client.phone_primary,
            "phone_secondary": client.phone_secondary,
            "preferred_contact_method": client.preferred_contact_method,
            "address": client.address,
            "occupation": client.occupation,
            "employer": client.employer,
            "notes": client.notes,
            "emergency_contact": client.emergency_contact,
            "created_by": str(client.created_by) if client.created_by else None,
            "created_at": client.created_at.isoformat(),
            "client_metadata": client.client_metadata,
        }

async def get_client_db(
    session: AsyncSession,
    client_id: UUID,
    tenant_id: UUID,
) -> Optional[Client]:
    """
    Get a client by ID.

    Args:
        session: Database session
        client_id: Client ID
        tenant_id: Tenant ID

    Returns:
        The client if found, None otherwise
    """
    # Query the client
    result = await session.execute(
        select(Client)
        .where(Client.id == client_id)
        .where(Client.tenant_id == tenant_id)
    )

    # Get the client
    return result.scalar_one_or_none()

async def get_client_by_id(
    client_id: UUID,
    tenant_id: UUID,
) -> Optional[Dict[str, Any]]:
    """
    Get a client by ID.

    Args:
        client_id: Client ID
        tenant_id: Tenant ID

    Returns:
        The client if found, None otherwise
    """
    logger.info(f"Getting client: {client_id}")

    async with get_db() as session:
        # Get the client
        client = await get_client_db(session, client_id, tenant_id)

        # Return None if not found
        if not client:
            return None

        # Convert to dictionary
        return {
            "id": str(client.id),
            "tenant_id": str(client.tenant_id),
            "first_name": client.first_name,
            "last_name": client.last_name,
            "middle_name": client.middle_name,
            "date_of_birth": (
                client.date_of_birth.isoformat() if client.date_of_birth else None
            ),
            "ssn_last_four": client.ssn_last_four,
            "email": client.email,
            "phone_primary": client.phone_primary,
            "phone_secondary": client.phone_secondary,
            "preferred_contact_method": client.preferred_contact_method,
            "address": client.address,
            "occupation": client.occupation,
            "employer": client.employer,
            "notes": client.notes,
            "emergency_contact": client.emergency_contact,
            "created_by": str(client.created_by) if client.created_by else None,
            "created_at": client.created_at.isoformat(),
            "updated_at": client.updated_at.isoformat() if client.updated_at else None,
            "client_metadata": client.client_metadata,
        }

async def list_clients_db(
    tenant_id: UUID,
    is_active: Optional[bool] = None,
    search_term: Optional[str] = None,
    limit: int = 50,
    offset: int = 0,
) -> List[Dict[str, Any]]:
    """
    List clients with optional filtering.

    Args:
        tenant_id: Tenant ID
        is_active: Filter by active status
        search_term: Search term for name, email, or phone
        limit: Maximum number of clients to return
        offset: Offset for pagination

    Returns:
        List of clients
    """
    logger.info(f"Listing clients for tenant: {tenant_id}")

    async with get_db() as session:
        # Build the query
        query = select(Client).where(Client.tenant_id == tenant_id)

        # Apply filters
        if is_active is not None:
            query = query.where(Client.is_active == is_active)

        # Apply search term if provided
        if search_term:
            search_term_lower = f"%{search_term.lower()}%"
            query = query.where(
                (Client.first_name.ilike(search_term_lower)) |
                (Client.last_name.ilike(search_term_lower)) |
                (Client.email.ilike(search_term_lower)) |
                (Client.phone_primary.ilike(search_term_lower))
            )

        # Apply pagination
        query = query.limit(limit).offset(offset)

        # Execute the query
        result = await session.execute(query)

        # Get the clients
        clients = result.scalars().all()

        # Convert to dictionaries
        return [
            {
                "id": str(client.id),
                "tenant_id": str(client.tenant_id),
                "first_name": client.first_name,
                "last_name": client.last_name,
                "middle_name": client.middle_name,
                "date_of_birth": (
                    client.date_of_birth.isoformat() if client.date_of_birth else None
                ),
                "ssn_last_four": client.ssn_last_four,
                "email": client.email,
                "phone_primary": client.phone_primary,
                "phone_secondary": client.phone_secondary,
                "preferred_contact_method": client.preferred_contact_method,
                "address": client.address,
                "occupation": client.occupation,
                "employer": client.employer,
                "notes": client.notes,
                "created_by": (
                    str(client.created_by) if client.created_by else None
                ),
                "created_at": client.created_at.isoformat(),
                "updated_at": (
                    client.updated_at.isoformat() if client.updated_at else None
                ),
                "client_metadata": client.client_metadata,
            }
            for client in clients
        ]

async def update_client_db(
    client_id: UUID,
    tenant_id: UUID,
    first_name: Optional[str] = None,
    last_name: Optional[str] = None,
    middle_name: Optional[str] = None,
    date_of_birth: Optional[date] = None,
    ssn_last_four: Optional[str] = None,
    email: Optional[str] = None,
    phone_primary: Optional[str] = None,
    phone_secondary: Optional[str] = None,
    preferred_contact_method: Optional[str] = None,
    address: Optional[Dict[str, Any]] = None,
    occupation: Optional[str] = None,
    employer: Optional[str] = None,
    notes: Optional[str] = None,
    emergency_contact: Optional[Dict[str, Any]] = None,
    is_active: Optional[bool] = None,
    updated_by: Optional[UUID] = None,
    client_metadata: Optional[Dict[str, Any]] = None,
) -> Optional[Dict[str, Any]]:
    """
    Update a client.

    Args:
        client_id: Client ID
        tenant_id: Tenant ID
        first_name: Client's first name
        last_name: Client's last name
        middle_name: Client's middle name
        date_of_birth: Client's date of birth
        ssn_last_four: Last four digits of SSN
        email: Client's email address
        phone_primary: Client's primary phone number
        phone_secondary: Client's secondary phone number
        preferred_contact_method: Client's preferred contact method
        address: Client's address
        occupation: Client's occupation
        employer: Client's employer
        notes: Notes about the client
        emergency_contact: Client's emergency contact information
        is_active: Whether the client is active
        updated_by: User ID of the updater
        metadata: Additional metadata

    Returns:
        The updated client if found, None otherwise
    """
    logger.info(f"Updating client: {client_id}")

    async with get_db() as session:
        # Get the client
        client = await get_client_db(session, client_id, tenant_id)

        # Return None if not found
        if not client:
            return None

        # Update the client
        if first_name is not None:
            client.first_name = first_name

        if last_name is not None:
            client.last_name = last_name

        if middle_name is not None:
            client.middle_name = middle_name

        if date_of_birth is not None:
            client.date_of_birth = date_of_birth

        if ssn_last_four is not None:
            client.ssn_last_four = ssn_last_four

        if email is not None:
            client.email = email

        if phone_primary is not None:
            client.phone_primary = phone_primary

        if phone_secondary is not None:
            client.phone_secondary = phone_secondary

        if preferred_contact_method is not None:
            client.preferred_contact_method = preferred_contact_method

        if address is not None:
            client.address = address

        if occupation is not None:
            client.occupation = occupation

        if employer is not None:
            client.employer = employer

        if notes is not None:
            client.notes = notes

        if emergency_contact is not None:
            client.emergency_contact = emergency_contact

        if is_active is not None:
            client.is_active = is_active

        if updated_by is not None:
            client.updated_by = updated_by

        if client_metadata is not None:
            client.client_metadata = client_metadata

        # Set the updated_at timestamp
        client.updated_at = datetime.now(timezone.utc)

        # Commit the transaction
        await session.commit()

        # Refresh the client
        await session.refresh(client)

        # Convert to dictionary
        return {
            "id": str(client.id),
            "tenant_id": str(client.tenant_id),
            "first_name": client.first_name,
            "last_name": client.last_name,
            "middle_name": client.middle_name,
            "date_of_birth": (
                client.date_of_birth.isoformat() if client.date_of_birth else None
            ),
            "ssn_last_four": client.ssn_last_four,
            "email": client.email,
            "phone_primary": client.phone_primary,
            "phone_secondary": client.phone_secondary,
            "preferred_contact_method": client.preferred_contact_method,
            "address": client.address,
            "occupation": client.occupation,
            "employer": client.employer,
            "notes": client.notes,
            "emergency_contact": client.emergency_contact,
            "is_active": client.is_active,
            "created_by": str(client.created_by) if client.created_by else None,
            "created_at": client.created_at.isoformat(),
            "updated_at": client.updated_at.isoformat() if client.updated_at else None,
            "updated_by": str(client.updated_by) if client.updated_by else None,
            "client_metadata": client.client_metadata,
        }

async def delete_client_db(
    client_id: UUID,
    tenant_id: UUID,
) -> bool:
    """
    Delete a client.

    Args:
        client_id: Client ID
        tenant_id: Tenant ID

    Returns:
        True if the client was deleted, False otherwise
    """
    logger.info(f"Deleting client: {client_id}")

    async with get_db() as session:
        # Get the client
        client = await get_client_db(session, client_id, tenant_id)

        # Return False if not found
        if not client:
            return False

        # Delete the client
        await session.delete(client)

        # Commit the transaction
        await session.commit()

        return True