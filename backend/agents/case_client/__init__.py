"""
Case and Client CRUD Agent Package

This package provides the Case and Client CRUD Agent implementation, which is
responsible for:
1. Creating new cases and clients
2. Reading cases and clients with filtering
3. Updating case and client properties
4. Deleting cases and clients with proper validation

The Case and Client CRUD Agent serves as an interactive agent for case and
client management operations,
providing a natural language interface for case and client-related operations.

Usage:
    from backend.agents.case_client import CaseClientAgent, case_client_router

    # Create a case client agent
    agent = CaseClientAgent()

    # Execute the agent
    result = await agent.invoke(
        {"messages": [HumanMessage(content="Create a new case for <PERSON>")]},
        {"configurable": {"thread_id": "123", "tenant_id": "456"}}
    )
"""

from backend.agents.case_client.agent import CaseClientAgent
from backend.agents.case_client.router import case_client_router
from backend.agents.case_client.nodes import (
    create_case,
    read_case,
    update_case,
    delete_case,
    create_client,
    read_client,
    update_client,
    delete_client,
)

__all__ = [
    "CaseClientAgent",
    "case_client_router",
    "create_case",
    "read_case",
    "update_case",
    "delete_case",
    "create_client",
    "read_client",
    "update_client",
    "delete_client",
]
