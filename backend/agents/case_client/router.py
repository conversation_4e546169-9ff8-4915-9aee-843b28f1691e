"""
Case and Client CRUD Agent Router

This module provides the router node for the Case and Client CRUD Agent,
which determines the appropriate operation based on user intent.
"""

import logging
from typing import Dict, Any, Literal, TypedDict, Annotated, cast
from langchain_core.runnables import RunnableConfig
from langchain_core.messages import HumanMessage

from shared.core.base_agent import BaseAgent
from shared.core.llm.selector import resolve_llm

# Set up logging
logger = logging.getLogger(__name__)

# Define the router output type
class RouterOutput(TypedDict):
    next: Literal[
        "create_case", "read_case", "update_case", "delete_case",
        "create_client", "read_client", "update_client", "delete_client"
    ]

async def case_client_router(
    state: Dict[str, Any], config: RunnableConfig
) -> RouterOutput:
    """
    Router node for the Case and Client CRUD Agent.

    This node determines the appropriate operation based on user intent.

    Args:
        state: Agent state
        config: Runnable configuration

    Returns:
        RouterOutput: Next node to execute
    """
    # Get the user input
    messages = state.get("messages", [])
    if not messages:
        return {"next": "read_case"}  # Default to read_case if no messages

    # Find the last human message
    user_input = ""
    for message in reversed(messages):
        if isinstance(message, HumanMessage) or (
            hasattr(message, "type") and message.type == "human"
        ):
            user_input = message.content
            break

    if not user_input:
        return {"next": "read_case"}  # Default to read_case if no human message found

    # Get the LLM client using the selector
    llm = await resolve_llm(state, "caseCrudAgent", "router")

    # Get the prompt template
    prompt_template = (
        "You are a case and client management assistant. Your job is to help "
        "users manage their cases and clients.\n"
        "\n"
        "Based on the user's request, determine which operation they want to perform:\n"
        "- create_case: Create a new case\n"
        "- read_case: Read or list cases\n"
        "- update_case: Update an existing case\n"
        "- delete_case: Delete a case\n"
        "- create_client: Create a new client\n"
        "- read_client: Read or list clients\n"
        "- update_client: Update an existing client\n"
        "- delete_client: Delete a client\n"
        "\n"
        "User request: {input}\n"
        "\n"
        "Operation:"
    )

    # Format the prompt
    prompt = prompt_template.format(input=user_input)

    try:
        # Get the LLM client using the selector
        llm = await resolve_llm(state, "caseCrudAgent", "router")

        # Call the LLM
        response = await llm.chat_completion(
            messages=[
                {"role": "user", "content": prompt_template.format(input=user_input)}
            ],
            temperature=0.2,
            max_tokens=100
        )

        # Extract the content from the response
        content = response.get("choices", [{}])[0].get("message", {}).get("content", "")

        # Parse the response
        content = content.strip().lower()

        # Map the response to a node
        if "create_case" in content:
            response = "create_case"
        elif "update_case" in content:
            response = "update_case"
        elif "delete_case" in content:
            response = "delete_case"
        elif "create_client" in content:
            response = "create_client"
        elif "update_client" in content:
            response = "update_client"
        elif "delete_client" in content:
            response = "delete_client"
        elif "read_client" in content or "client" in content:
            response = "read_client"
        else:
            # Default to read_case
            response = "read_case"

        # Parse the response
        operation = response.strip().lower()

        # Map the operation to the next node
        operation_map = {
            "create_case": "create_case",
            "read_case": "read_case",
            "update_case": "update_case",
            "delete_case": "delete_case",
            "create_client": "create_client",
            "read_client": "read_client",
            "update_client": "update_client",
            "delete_client": "delete_client",
        }

        # Default to read_case if the operation is not recognized
        next_node = operation_map.get(operation, "read_case")

        logger.info(f"Routing to {next_node} based on user input: {user_input}")

        return {"next": next_node}
    except Exception as e:
        logger.error(f"Error in case_client_router: {str(e)}")
        return {"next": "read_case"}  # Default to read_case on error
