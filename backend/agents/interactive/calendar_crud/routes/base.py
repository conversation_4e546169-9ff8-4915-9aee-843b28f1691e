"""
Base router for Calendar CRUD API.

This module provides a base router with common functionality for all calendar routes,
including error handling, capability checking, and authentication.
"""

import logging
from typing import Any, Callable, Dict, List, Optional, Type, TypeVar, Union, cast

from fastapi import APIRouter, Depends, HTTPException, Request, Response
from pydantic import BaseModel

from ..middleware.auth_middleware import get_firm_id, verify_tenant_access
from ..providers.base import ProviderCapability
from ..providers.exceptions import (
    AuthenticationError,
    CalendarProviderError,
    RateLimitError,
    ResourceNotFoundError,
    UnsupportedOperationError,
    ValidationError,
)
from ..providers.factory import get_provider_instance

# Configure logging
logger = logging.getLogger(__name__)

# Type variable for generic function return
T = TypeVar("T")


class BaseRouter:
    """
    Base router with common functionality for all calendar routes.
    
    This class provides a wrapper around FastAPI's APIRouter with additional
    functionality for error handling, capability checking, and authentication.
    
    Attributes:
        router: The FastAPI APIRouter instance
        prefix: The URL prefix for all routes in this router
        tags: The OpenAPI tags for all routes in this router
    """

    def __init__(self, prefix: str, tags: List[str]):
        """
        Initialize the base router.
        
        Args:
            prefix: The URL prefix for all routes in this router
            tags: The OpenAPI tags for all routes in this router
        """
        self.router = APIRouter(prefix=prefix, tags=tags)
        self.prefix = prefix
        self.tags = tags

    def add_endpoint(
        self,
        path: str,
        endpoint: Callable[..., Any],
        methods: List[str],
        response_model: Optional[Type[T]] = None,
        status_code: int = 200,
        summary: Optional[str] = None,
        description: Optional[str] = None,
        dependencies: Optional[List[Any]] = None,
        capability: Optional[ProviderCapability] = None,
        response_description: str = "Successful response",
        responses: Optional[Dict[Union[int, str], Dict[str, Any]]] = None,
    ) -> None:
        """
        Add an endpoint to the router with standard error handling.
        
        Args:
            path: The URL path for the endpoint
            endpoint: The endpoint function
            methods: The HTTP methods for the endpoint
            response_model: The Pydantic model for the response
            status_code: The HTTP status code for a successful response
            summary: The summary for the endpoint in the OpenAPI docs
            description: The description for the endpoint in the OpenAPI docs
            dependencies: The dependencies for the endpoint
            capability: The provider capability required for the endpoint
            response_description: The description for the response in the OpenAPI docs
            responses: Additional responses for the endpoint in the OpenAPI docs
        """
        # Add standard error responses if not provided
        if responses is None:
            responses = {}
            
        if 400 not in responses:
            responses[400] = {"description": "Bad Request - Invalid parameters"}
        if 401 not in responses:
            responses[401] = {"description": "Unauthorized - Authentication required"}
        if 403 not in responses and capability is not None:
            responses[403] = {
                "description": "Forbidden - Operation not supported by provider"
            }
        if 404 not in responses:
            responses[404] = {"description": "Not Found - Resource not found"}
        if 429 not in responses:
            responses[429] = {"description": "Too Many Requests - Rate limit exceeded"}
        if 500 not in responses:
            responses[500] = {"description": "Internal Server Error"}
            
        # Add the endpoint to the router
        self.router.add_api_route(
            path=path,
            endpoint=self._wrap_endpoint(endpoint, capability),
            methods=methods,
            response_model=response_model,
            status_code=status_code,
            summary=summary,
            description=description,
            dependencies=dependencies or [],
            response_description=response_description,
            responses=responses,
        )

    def _wrap_endpoint(
        self,
        endpoint: Callable[..., Any],
        capability: Optional[ProviderCapability] = None,
    ) -> Callable[..., Any]:
        """
        Wrap endpoint with standard error handling and capability checking.
        
        Args:
            endpoint: The endpoint function to wrap
            capability: The provider capability required for the endpoint
            
        Returns:
            The wrapped endpoint function
        """
        async def wrapped_endpoint(*args: Any, **kwargs: Any) -> Any:
            try:
                # Check if provider capability is required
                if capability and "provider" in kwargs:
                    provider_id = kwargs["provider"]
                    firm_id = kwargs.get("firm_id")
                    
                    # Get provider instance
                    provider = get_provider_instance(provider_id, firm_id)
                    
                    # Check if provider supports the required capability
                    if not provider.has_capability(capability):
                        raise UnsupportedOperationError(
                            f"The operation '{capability.value}' is not supported by "
                            f"the provider '{provider.provider_name}'"
                        )

                # Call the original endpoint
                return await endpoint(*args, **kwargs)
                
            except ValidationError as e:
                logger.warning(f"Validation error: {str(e)}")
                raise HTTPException(status_code=400, detail=str(e))
                
            except AuthenticationError as e:
                logger.warning(f"Authentication error: {str(e)}")
                raise HTTPException(status_code=401, detail=str(e))
                
            except UnsupportedOperationError as e:
                logger.warning(f"Unsupported operation: {str(e)}")
                raise HTTPException(status_code=403, detail=str(e))
                
            except ResourceNotFoundError as e:
                logger.warning(f"Resource not found: {str(e)}")
                raise HTTPException(status_code=404, detail=str(e))
                
            except RateLimitError as e:
                logger.warning(f"Rate limit exceeded: {str(e)}")
                headers = {}
                if hasattr(e, "retry_after") and e.retry_after is not None:
                    headers["Retry-After"] = str(e.retry_after)
                raise HTTPException(
                    status_code=429,
                    detail=str(e),
                    headers=headers,
                )
                
            except CalendarProviderError as e:
                logger.error(f"Calendar provider error: {str(e)}")
                raise HTTPException(status_code=500, detail=str(e))
                
            except Exception as e:
                logger.exception(f"Unexpected error in calendar endpoint: {str(e)}")
                raise HTTPException(status_code=500, detail="Internal server error")

        return wrapped_endpoint
