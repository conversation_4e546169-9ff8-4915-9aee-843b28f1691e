"""
Calendar routes for Calendar CRUD API.

This module provides routes for managing calendars.
"""

import logging
from typing import Any, Dict, List, Optional

from fastapi import Depends, HTTPException, Query

from ..middleware.auth_middleware import get_firm_id, verify_tenant_access
from ..providers.base import ProviderCapability
from ..providers.exceptions import (
    AuthenticationError,
    ResourceNotFoundError,
    ValidationError,
)
from ..providers.factory import get_provider_instance
from ..schemas.calendars import (
    Calendar,
    CalendarDetailsResponse,
    CalendarQueryParams,
    CalendarsResponse,
)
from .base import BaseRouter

# Configure logging
logger = logging.getLogger(__name__)

# Create router
calendars_router = BaseRouter(prefix="/calendar", tags=["Calendars"])


@calendars_router.router.get(
    "/calendars",
    response_model=CalendarsResponse,
    summary="List calendars",
    description="Lists available calendars for a provider",
)
async def list_calendars(
    provider: str = Query(..., description="The calendar provider ID"),
    firm_id: str = Depends(get_firm_id),
    _: None = Depends(verify_tenant_access),
) -> CalendarsResponse:
    """
    List calendars.
    
    This endpoint lists all available calendars for a provider.
    
    Args:
        provider: The calendar provider ID
        firm_id: The firm/tenant ID
        _: Dependency to verify tenant access
        
    Returns:
        CalendarsResponse: The list of calendars
        
    Raises:
        HTTPException: If the provider is not found or listing calendars fails
    """
    try:
        logger.info(f"Listing calendars for provider {provider} for firm {firm_id}")
        
        # Get provider instance
        provider_instance = get_provider_instance(provider, firm_id)
        
        # Check if provider is connected
        if not await provider_instance.is_connected():
            logger.warning(f"Provider {provider} is not connected for firm {firm_id}")
            raise HTTPException(
                status_code=401,
                detail=(
                    f"Provider {provider} is not connected. Please connect the "
                    "provider first."
                ),
            )
            
        # Check if provider supports listing calendars
        if not provider_instance.has_capability(ProviderCapability.LIST_CALENDARS):
            logger.warning(f"Provider {provider} does not support listing calendars")
            raise HTTPException(
                status_code=403,
                detail=f"Provider {provider} does not support listing calendars",
            )
            
        # Get calendars
        provider_calendars = await provider_instance.get_calendars()
        
        # Convert to response model
        calendars = []
        for cal in provider_calendars:
            calendars.append(
                Calendar(
                    id=cal.id,
                    name=cal.name,
                    description=cal.description,
                    timezone=cal.timezone,
                    primary=cal.primary,
                    access_role=cal.access_role,
                    background_color=cal.background_color,
                    foreground_color=cal.foreground_color,
                )
            )
            
        return CalendarsResponse(calendars=calendars)
        
    except ValidationError as e:
        logger.warning(f"Validation error: {str(e)}")
        raise HTTPException(status_code=400, detail=str(e))
    except AuthenticationError as e:
        logger.error(f"Authentication error: {str(e)}")
        raise HTTPException(status_code=401, detail=str(e))
    except ResourceNotFoundError as e:
        logger.warning(f"Resource not found: {str(e)}")
        raise HTTPException(status_code=404, detail=str(e))
    except Exception as e:
        logger.exception(f"Unexpected error: {str(e)}")
        raise HTTPException(status_code=500, detail="Internal server error")


@calendars_router.router.get(
    "/calendars/{calendar_id}",
    response_model=CalendarDetailsResponse,
    summary="Get calendar details",
    description="Gets details of a specific calendar",
)
async def get_calendar_details(
    calendar_id: str,
    provider: str = Query(..., description="The calendar provider ID"),
    firm_id: str = Depends(get_firm_id),
    _: None = Depends(verify_tenant_access),
) -> CalendarDetailsResponse:
    """
    Get calendar details.
    
    This endpoint gets details of a specific calendar.
    
    Args:
        calendar_id: The calendar ID
        provider: The calendar provider ID
        firm_id: The firm/tenant ID
        _: Dependency to verify tenant access
        
    Returns:
        CalendarDetailsResponse: The calendar details
        
    Raises:
        HTTPException: If the calendar is not found or getting details fails
    """
    try:
        logger.info(
            f"Getting details for calendar {calendar_id} from provider "
            f"{provider} for firm {firm_id}"
        )
        
        # Get provider instance
        provider_instance = get_provider_instance(provider, firm_id)
        
        # Check if provider is connected
        if not await provider_instance.is_connected():
            logger.warning(f"Provider {provider} is not connected for firm {firm_id}")
            raise HTTPException(
                status_code=401,
                detail=(
                    f"Provider {provider} is not connected. Please connect the "
                    "provider first."
                ),
            )
            
        # Check if provider supports listing calendars
        if not provider_instance.has_capability(ProviderCapability.LIST_CALENDARS):
            logger.warning(f"Provider {provider} does not support listing calendars")
            raise HTTPException(
                status_code=403,
                detail=f"Provider {provider} does not support listing calendars",
            )
            
        # Get calendar details
        cal = await provider_instance.get_calendar(calendar_id)
        
        # Convert to response model
        calendar = Calendar(
            id=cal.id,
            name=cal.name,
            description=cal.description,
            timezone=cal.timezone,
            primary=cal.primary,
            access_role=cal.access_role,
            background_color=cal.background_color,
            foreground_color=cal.foreground_color,
        )
            
        return CalendarDetailsResponse(calendar=calendar)
        
    except ValidationError as e:
        logger.warning(f"Validation error: {str(e)}")
        raise HTTPException(status_code=400, detail=str(e))
    except AuthenticationError as e:
        logger.error(f"Authentication error: {str(e)}")
        raise HTTPException(status_code=401, detail=str(e))
    except ResourceNotFoundError as e:
        logger.warning(f"Resource not found: {str(e)}")
        raise HTTPException(status_code=404, detail=str(e))
    except Exception as e:
        logger.exception(f"Unexpected error: {str(e)}")
        raise HTTPException(status_code=500, detail="Internal server error")
