"""
Provider routes for Calendar CRUD API.

This module provides routes for managing calendar providers.
"""

import logging
from typing import Any, Dict, List, Optional

from fastapi import Depends, HTTPException, Query

from ..middleware.auth_middleware import get_firm_id, verify_tenant_access
from ..providers.base import ProviderCapability
from ..providers.exceptions import AuthenticationError, ValidationError
from ..providers.factory import get_provider_instance, list_providers
from ..schemas.providers import (
    ProviderCapabilities,
    ProviderDetails,
    ProviderDetailsResponse,
    ProviderRateLimits,
    ProviderRetryStrategy,
    ProviderStatus,
    ProvidersResponse,
)
from .base import BaseRouter

# Configure logging
logger = logging.getLogger(__name__)

# Create router
providers_router = BaseRouter(prefix="/calendar", tags=["Calendar Providers"])


@providers_router.router.get(
    "/providers",
    response_model=ProvidersResponse,
    summary="List calendar providers",
    description="Lists available calendar providers and their connection status",
)
async def list_calendar_providers(
    firm_id: str = Depends(get_firm_id),
    _: None = Depends(verify_tenant_access),
) -> ProvidersResponse:
    """
    List calendar providers.
    
    This endpoint lists all available calendar providers and their connection status
    for the authenticated tenant.
    
    Args:
        firm_id: The firm/tenant ID
        _: Dependency to verify tenant access
        
    Returns:
        ProvidersResponse: The list of providers
        
    Raises:
        HTTPException: If listing providers fails
    """
    try:
        logger.info(f"Listing providers for firm {firm_id}")
        
        # Get all available providers
        providers = list_providers()
        
        # Build response
        provider_statuses = []
        for provider_id in providers:
            # Get provider instance
            provider = get_provider_instance(provider_id, firm_id)
            
            # Check if provider is connected
            connected = await provider.is_connected()
            
            # Get provider capabilities
            capabilities = ProviderCapabilities(
                create_event=provider.has_capability(ProviderCapability.CREATE_EVENT),
                read_event=provider.has_capability(ProviderCapability.READ_EVENT),
                update_event=provider.has_capability(ProviderCapability.UPDATE_EVENT),
                delete_event=provider.has_capability(ProviderCapability.DELETE_EVENT),
                free_busy=provider.has_capability(ProviderCapability.FREE_BUSY),
                list_calendars=provider.has_capability(ProviderCapability.LIST_CALENDARS),
                webhook_support=provider.has_capability(ProviderCapability.WEBHOOK_SUPPORT),
            )
            
            # Add provider status to list
            provider_statuses.append(
                ProviderStatus(
                    provider_id=provider_id,
                    name=provider.provider_name,
                    connected=connected,
                    capabilities=capabilities,
                    icon=f"{provider_id}.svg",
                )
            )
            
        return ProvidersResponse(providers=provider_statuses)
        
    except Exception as e:
        logger.exception(f"Unexpected error: {str(e)}")
        raise HTTPException(status_code=500, detail="Internal server error")


@providers_router.router.get(
    "/providers/{provider_id}",
    response_model=ProviderDetailsResponse,
    summary="Get provider details",
    description="Gets detailed information about a calendar provider",
)
async def get_provider_details(
    provider_id: str,
    firm_id: str = Depends(get_firm_id),
    _: None = Depends(verify_tenant_access),
) -> ProviderDetailsResponse:
    """
    Get provider details.
    
    This endpoint gets detailed information about a calendar provider,
    including its capabilities, rate limits, and retry strategy.
    
    Args:
        provider_id: The provider ID
        firm_id: The firm/tenant ID
        _: Dependency to verify tenant access
        
    Returns:
        ProviderDetailsResponse: The provider details
        
    Raises:
        HTTPException: If the provider is not found or getting details fails
    """
    try:
        logger.info(f"Getting details for provider {provider_id} for firm {firm_id}")
        
        # Get provider instance
        provider = get_provider_instance(provider_id, firm_id)
        
        # Check if provider is connected
        connected = await provider.is_connected()
        
        # Get provider capabilities
        capabilities = ProviderCapabilities(
            create_event=provider.has_capability(ProviderCapability.CREATE_EVENT),
            read_event=provider.has_capability(ProviderCapability.READ_EVENT),
            update_event=provider.has_capability(ProviderCapability.UPDATE_EVENT),
            delete_event=provider.has_capability(ProviderCapability.DELETE_EVENT),
            free_busy=provider.has_capability(ProviderCapability.FREE_BUSY),
            list_calendars=provider.has_capability(ProviderCapability.LIST_CALENDARS),
            webhook_support=provider.has_capability(ProviderCapability.WEBHOOK_SUPPORT),
        )
        
        # Get provider rate limits
        rate_limits = ProviderRateLimits(
            requests_per_day=provider.rate_limits.get("requests_per_day"),
            requests_per_100_seconds=provider.rate_limits.get("requests_per_100_seconds"),
            requests_per_minute=provider.rate_limits.get("requests_per_minute"),
        )
        
        # Get provider retry strategy
        retry_strategy = ProviderRetryStrategy(
            max_retries=provider.retry_strategy.get("max_retries", 3),
            initial_backoff_seconds=provider.retry_strategy.get(
                "initial_backoff_seconds", 1
            ),
            max_backoff_seconds=provider.retry_strategy.get("max_backoff_seconds", 60),
            jitter_factor=provider.retry_strategy.get("jitter_factor", 0.1),
            circuit_breaker_threshold=provider.retry_strategy.get(
                "circuit_breaker_threshold", 0.5
            ),
            circuit_breaker_reset_seconds=provider.retry_strategy.get(
                "circuit_breaker_reset_seconds", 300
            ),
        )
        
        # Build provider details
        provider_details = ProviderDetails(
            provider_id=provider_id,
            name=provider.provider_name,
            connected=connected,
            capabilities=capabilities,
            icon=f"{provider_id}.svg",
            auth_type=provider.auth_type,
            scopes=provider.scopes,
            rate_limits=rate_limits,
            retry_strategy=retry_strategy,
        )
        
        return ProviderDetailsResponse(provider=provider_details)
        
    except ValidationError as e:
        logger.warning(f"Validation error: {str(e)}")
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        logger.exception(f"Unexpected error: {str(e)}")
        raise HTTPException(status_code=500, detail="Internal server error")
