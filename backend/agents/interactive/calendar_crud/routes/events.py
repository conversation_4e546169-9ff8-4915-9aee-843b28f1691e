"""
Event routes for Calendar CRUD API.

This module provides routes for managing calendar events.
"""

import logging
from datetime import datetime
from typing import Any, Dict, List, Optional, Union

from fastapi import Depends, HTTPException, Path, Query

from ..middleware.auth_middleware import get_firm_id, verify_tenant_access
from ..providers.base import ProviderCapability
from ..providers.exceptions import (
    AuthenticationError,
    ResourceNotFoundError,
    UnsupportedOperationError,
    ValidationError,
)
from ..providers.factory import get_provider_instance
from ..schemas.events import (
    CalendarEvent,
    CreateEventRequest,
    DeleteEventQueryParams,
    DeleteEventResponse,
    EventQueryParams,
    EventsQueryParams,
    EventsResponse,
    SchedulingLinkResponse,
    UpdateEventRequest,
)
from .base import BaseRouter

# Configure logging
logger = logging.getLogger(__name__)

# Create router
events_router = BaseRouter(prefix="/calendar", tags=["Calendar Events"])


@events_router.router.get(
    "/events",
    response_model=EventsResponse,
    summary="List events",
    description="Lists events within a time range",
)
async def list_events(
    provider: str = Query(..., description="The calendar provider ID"),
    calendar_id: str = Query(..., description="The calendar ID"),
    start_time: datetime = Query(..., description="The start of the time range"),
    end_time: datetime = Query(..., description="The end of the time range"),
    max_results: int = Query(
        100, description="The maximum number of events to return", ge=1, le=1000
    ),
    firm_id: str = Depends(get_firm_id),
    _: None = Depends(verify_tenant_access),
) -> EventsResponse:
    """
    List events.

    This endpoint lists all events within a time range for a calendar.

    Args:
        provider: The calendar provider ID
        calendar_id: The calendar ID
        start_time: The start of the time range
        end_time: The end of the time range
        max_results: The maximum number of events to return
        firm_id: The firm/tenant ID
        _: Dependency to verify tenant access

    Returns:
        EventsResponse: The list of events

    Raises:
        HTTPException: If the calendar is not found or listing events fails
    """
    try:
        logger.info(
            f"Listing events for calendar {calendar_id} from provider "
            f"{provider} for firm {firm_id}"
        )

        # Validate time range
        if end_time <= start_time:
            raise ValidationError("end_time must be after start_time")

        # Get provider instance
        provider_instance = get_provider_instance(provider, firm_id)

        # Check if provider is connected
        if not await provider_instance.is_connected():
            logger.warning(f"Provider {provider} is not connected for firm {firm_id}")
            raise HTTPException(
                status_code=401,
                detail=(
                    f"Provider {provider} is not connected. Please connect the "
                    "provider first."
                ),
            )

        # Get events
        events = await provider_instance.get_events(
            calendar_id=calendar_id,
            start_time=start_time,
            end_time=end_time,
            max_results=max_results,
        )

        return EventsResponse(events=events)

    except ValidationError as e:
        logger.warning(f"Validation error: {str(e)}")
        raise HTTPException(status_code=400, detail=str(e))
    except AuthenticationError as e:
        logger.error(f"Authentication error: {str(e)}")
        raise HTTPException(status_code=401, detail=str(e))
    except ResourceNotFoundError as e:
        logger.warning(f"Resource not found: {str(e)}")
        raise HTTPException(status_code=404, detail=str(e))
    except Exception as e:
        logger.exception(f"Unexpected error: {str(e)}")
        raise HTTPException(status_code=500, detail="Internal server error")


@events_router.router.get(
    "/events/{event_id}",
    response_model=CalendarEvent,
    summary="Get event",
    description="Gets details of a specific event",
)
async def get_event(
    event_id: str = Path(..., description="The event ID"),
    provider: str = Query(..., description="The calendar provider ID"),
    calendar_id: str = Query(..., description="The calendar ID"),
    firm_id: str = Depends(get_firm_id),
    _: None = Depends(verify_tenant_access),
) -> CalendarEvent:
    """
    Get event.

    This endpoint gets details of a specific event.

    Args:
        event_id: The event ID
        provider: The calendar provider ID
        calendar_id: The calendar ID
        firm_id: The firm/tenant ID
        _: Dependency to verify tenant access

    Returns:
        CalendarEvent: The event details

    Raises:
        HTTPException: If the event is not found or getting details fails
    """
    try:
        logger.info(
            f"Getting details for event {event_id} from calendar "
            f"{calendar_id} from provider {provider} for firm {firm_id}"
        )

        # Get provider instance
        provider_instance = get_provider_instance(provider, firm_id)

        # Check if provider is connected
        if not await provider_instance.is_connected():
            logger.warning(f"Provider {provider} is not connected for firm {firm_id}")
            raise HTTPException(
                status_code=401,
                detail=(
                    f"Provider {provider} is not connected. Please connect the "
                    "provider first."
                ),
            )

        # Get event
        event = await provider_instance.get_event(
            calendar_id=calendar_id,
            event_id=event_id,
        )

        return event

    except ValidationError as e:
        logger.warning(f"Validation error: {str(e)}")
        raise HTTPException(status_code=400, detail=str(e))
    except AuthenticationError as e:
        logger.error(f"Authentication error: {str(e)}")
        raise HTTPException(status_code=401, detail=str(e))
    except ResourceNotFoundError as e:
        logger.warning(f"Resource not found: {str(e)}")
        raise HTTPException(status_code=404, detail=str(e))
    except Exception as e:
        logger.exception(f"Unexpected error: {str(e)}")
        raise HTTPException(status_code=500, detail="Internal server error")


@events_router.router.post(
    "/events",
    response_model=Union[CalendarEvent, SchedulingLinkResponse],
    status_code=201,
    summary="Create event",
    description="Creates a new event",
)
async def create_event(
    request: CreateEventRequest,
    firm_id: str = Depends(get_firm_id),
    _: None = Depends(verify_tenant_access),
) -> Union[CalendarEvent, SchedulingLinkResponse]:
    """
    Create event.

    This endpoint creates a new event in a calendar.

    Args:
        request: The create event request
        firm_id: The firm/tenant ID
        _: Dependency to verify tenant access

    Returns:
        Union[CalendarEvent, SchedulingLinkResponse]: The created event or
            scheduling link

    Raises:
        HTTPException: If creating the event fails
    """
    try:
        logger.info(
            f"Creating event in calendar {request.calendar_id} from provider "
            f"{request.provider} for firm {firm_id}"
        )

        # Get provider instance
        provider_instance = get_provider_instance(request.provider, firm_id)

        # Check if provider is connected
        if not await provider_instance.is_connected():
            logger.warning(
                f"Provider {request.provider} is not connected for firm {firm_id}"
            )
            raise HTTPException(
                status_code=401,
                detail=(
                    f"Provider {request.provider} is not connected. Please "
                    "connect the provider first."
                ),
            )

        # Check if provider supports creating events
        if not provider_instance.has_capability(ProviderCapability.CREATE_EVENT):
            logger.warning(
                f"Provider {request.provider} does not support creating events"
            )
            raise HTTPException(
                status_code=403,
                detail=(
                    f"Provider {request.provider} does not support creating events"
                ),
            )

        # Handle Calendly differently
        if request.provider == "calendly":
            # For Calendly, we need to generate a scheduling link
            scheduling_link = await provider_instance.create_scheduling_link(
                calendar_id=request.calendar_id,
                event=request.event,
            )

            return SchedulingLinkResponse(
                scheduling_url=scheduling_link.scheduling_url,
                expires_at=scheduling_link.expires_at,
                event_type_id=scheduling_link.event_type_id,
            )
        else:
            # For other providers, create the event directly
            event = await provider_instance.create_event(
                calendar_id=request.calendar_id,
                event=request.event,
            )

            return event

    except ValidationError as e:
        logger.warning(f"Validation error: {str(e)}")
        raise HTTPException(status_code=400, detail=str(e))
    except AuthenticationError as e:
        logger.error(f"Authentication error: {str(e)}")
        raise HTTPException(status_code=401, detail=str(e))
    except UnsupportedOperationError as e:
        logger.warning(f"Unsupported operation: {str(e)}")
        raise HTTPException(status_code=403, detail=str(e))
    except ResourceNotFoundError as e:
        logger.warning(f"Resource not found: {str(e)}")
        raise HTTPException(status_code=404, detail=str(e))
    except Exception as e:
        logger.exception(f"Unexpected error: {str(e)}")
        raise HTTPException(status_code=500, detail="Internal server error")


@events_router.router.put(
    "/events/{event_id}",
    response_model=CalendarEvent,
    summary="Update event",
    description="Updates an existing event",
)
async def update_event(
    event_id: str = Path(..., description="The event ID"),
    request: UpdateEventRequest = None,
    firm_id: str = Depends(get_firm_id),
    _: None = Depends(verify_tenant_access),
) -> CalendarEvent:
    """
    Update event.

    This endpoint updates an existing event in a calendar.

    Args:
        event_id: The event ID
        request: The update event request
        firm_id: The firm/tenant ID
        _: Dependency to verify tenant access

    Returns:
        CalendarEvent: The updated event

    Raises:
        HTTPException: If updating the event fails
    """
    try:
        logger.info(
            f"Updating event {event_id} in calendar {request.calendar_id} from "
            f"provider {request.provider} for firm {firm_id}"
        )

        # Get provider instance
        provider_instance = get_provider_instance(request.provider, firm_id)

        # Check if provider is connected
        if not await provider_instance.is_connected():
            logger.warning(
                f"Provider {request.provider} is not connected for firm {firm_id}"
            )
            raise HTTPException(
                status_code=401,
                detail=(
                    f"Provider {request.provider} is not connected. Please "
                    "connect the provider first."
                ),
            )

        # Check if provider supports updating events
        if not provider_instance.has_capability(ProviderCapability.UPDATE_EVENT):
            logger.warning(
                f"Provider {request.provider} does not support updating events"
            )
            raise HTTPException(
                status_code=403,
                detail=(
                    f"Provider {request.provider} does not support updating events"
                ),
            )

        # Update event
        event = await provider_instance.update_event(
            calendar_id=request.calendar_id,
            event_id=event_id,
            event=request.event,
        )

        return event

    except ValidationError as e:
        logger.warning(f"Validation error: {str(e)}")
        raise HTTPException(status_code=400, detail=str(e))
    except AuthenticationError as e:
        logger.error(f"Authentication error: {str(e)}")
        raise HTTPException(status_code=401, detail=str(e))
    except UnsupportedOperationError as e:
        logger.warning(f"Unsupported operation: {str(e)}")
        raise HTTPException(status_code=403, detail=str(e))
    except ResourceNotFoundError as e:
        logger.warning(f"Resource not found: {str(e)}")
        raise HTTPException(status_code=404, detail=str(e))
    except Exception as e:
        logger.exception(f"Unexpected error: {str(e)}")
        raise HTTPException(status_code=500, detail="Internal server error")


@events_router.router.delete(
    "/events/{event_id}",
    response_model=DeleteEventResponse,
    summary="Delete event",
    description="Deletes an event",
)
async def delete_event(
    event_id: str = Path(..., description="The event ID"),
    provider: str = Query(..., description="The calendar provider ID"),
    calendar_id: str = Query(..., description="The calendar ID"),
    firm_id: str = Depends(get_firm_id),
    _: None = Depends(verify_tenant_access),
) -> DeleteEventResponse:
    """
    Delete event.

    This endpoint deletes an event from a calendar.

    Args:
        event_id: The event ID
        provider: The calendar provider ID
        calendar_id: The calendar ID
        firm_id: The firm/tenant ID
        _: Dependency to verify tenant access

    Returns:
        DeleteEventResponse: The delete response

    Raises:
        HTTPException: If deleting the event fails
    """
    try:
        logger.info(
            f"Deleting event {event_id} from calendar {calendar_id} from "
            f"provider {provider} for firm {firm_id}"
        )

        # Get provider instance
        provider_instance = get_provider_instance(provider, firm_id)

        # Check if provider is connected
        if not await provider_instance.is_connected():
            logger.warning(
                f"Provider {provider} is not connected for firm {firm_id}"
            )
            raise HTTPException(
                status_code=401,
                detail=(
                    f"Provider {provider} is not connected. Please connect the "
                    "provider first."
                ),
            )

        # Check if provider supports deleting events
        if not provider_instance.has_capability(ProviderCapability.DELETE_EVENT):
            logger.warning(
                f"Provider {provider} does not support deleting events"
            )
            raise HTTPException(
                status_code=403,
                detail=(
                    f"Provider {provider} does not support deleting events"
                ),
            )

        # Delete event
        success = await provider_instance.delete_event(
            calendar_id=calendar_id,
            event_id=event_id,
        )

        return DeleteEventResponse(
            success=success,
            message=(
                "Event deleted successfully"
                if success
                else "Failed to delete event"
            ),
        )

    except ValidationError as e:
        logger.warning(f"Validation error: {str(e)}")
        raise HTTPException(status_code=400, detail=str(e))
    except AuthenticationError as e:
        logger.error(f"Authentication error: {str(e)}")
        raise HTTPException(status_code=401, detail=str(e))
    except UnsupportedOperationError as e:
        logger.warning(f"Unsupported operation: {str(e)}")
        raise HTTPException(status_code=403, detail=str(e))
    except ResourceNotFoundError as e:
        logger.warning(f"Resource not found: {str(e)}")
        raise HTTPException(status_code=404, detail=str(e))
    except Exception as e:
        logger.exception(f"Unexpected error: {str(e)}")
        raise HTTPException(status_code=500, detail="Internal server error")