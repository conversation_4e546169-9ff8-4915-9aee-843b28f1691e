"""
Calendar CRUD Agent Implementation

This module implements the Calendar CRUD Agent, which is responsible for:
1. Creating new calendar events with title, description, start/end times, and attendees
2. Reading calendar events with filtering by date range and calendar
3. Updating existing calendar events
4. Deleting calendar events
5. Checking free/busy times for scheduling

The Calendar CRUD Agent serves as an interactive agent for calendar
management operations,
providing a natural language interface for calendar-related operations.
"""

from typing import Dict, Any, Optional, List, cast
from langchain_core.runnables import RunnableConfig
from langchain_core.messages import HumanMessage, AIMessage

from shared.core.base_agent import BaseAgent
from shared.core.tools.executor import get_tool_executor

from backend.agents.interactive.calendar_crud.config import (
    CalendarCrudAgentConfig,
    get_calendar_crud_agent_config
)
from backend.agents.interactive.calendar_crud.nodes import (
    create_event,
    read_event,
    update_event,
    delete_event,
    check_free_busy,
    parse_date
)
from backend.agents.interactive.calendar_crud.router import calendar_crud_router

class CalendarCrudAgent(BaseAgent):
    """
    Calendar CRUD Agent for managing calendar operations.

    This agent provides natural language interface for calendar operations,
    including creating, reading, updating, and deleting calendar events,
    as well as checking free/busy times for scheduling.
    """

    def __init__(
        self,
        config: Optional[CalendarCrudAgentConfig] = None,
        tool_executor = None,
        db_client = None,
        agent_name: str = "calendarCrudAgent",
        node_name: str = ""
    ):
        """
        Initialize the calendar CRUD agent.

        Args:
            config: Agent configuration
            tool_executor: Tool executor for executing tools
            db_client: Database client for database operations
            agent_name: Name of the agent for LLM selection
            node_name: Name of the node for LLM selection
        """
        super().__init__(
            config or get_calendar_crud_agent_config(),
            agent_name=agent_name,
            node_name=node_name,
        )
        self.tool_executor = tool_executor or get_tool_executor()
        self.db_client = db_client

        # Set up nodes
        self.nodes = {
            "create_event": create_event,
            "read_event": read_event,
            "update_event": update_event,
            "delete_event": delete_event,
            "check_free_busy": check_free_busy,
            "parse_date": parse_date
        }

    async def initialize(
        self, state: Dict[str, Any], config: RunnableConfig
    ) -> Dict[str, Any]:
        """
        Initialize the agent.

        This method is called before the agent executes. It should initialize
        any agent-specific state or resources.

        Args:
            state: Current state
            config: Runnable configuration

        Returns:
            Updated state
        """
        # No initialization needed for this agent
        return state

    async def execute(
        self, state: Dict[str, Any], config: RunnableConfig
    ) -> Dict[str, Any]:
        """
        Execute the agent.

        This method is called to execute the agent's main logic.

        Args:
            state: Current state
            config: Runnable configuration

        Returns:
            Updated state
        """
        # Determine the operation to perform
        router_result = await calendar_crud_router(state, config)
        operation = router_result.get("next", "create_event")

        # Parse dates if needed
        state = await parse_date(state, config)

        # Execute the operation
        if operation == "create_event":
            result = await create_event(state, config)
        elif operation == "read_event":
            result = await read_event(state, config)
        elif operation == "update_event":
            result = await update_event(state, config)
        elif operation == "delete_event":
            result = await delete_event(state, config)
        elif operation == "check_free_busy":
            result = await check_free_busy(state, config)
        else:
            # Default to create_event if operation is not recognized
            result = await create_event(state, config)

        return result

    async def cleanup(
        self, state: Dict[str, Any], config: RunnableConfig
    ) -> Dict[str, Any]:
        """
        Clean up the agent.

        This method is called after the agent executes. It should clean up
        any agent-specific resources.

        Args:
            state: Current state
            config: Runnable configuration

        Returns:
            Updated state
        """
        # No cleanup needed for this agent
        return state

    async def invoke(
        self, inputs: Dict[str, Any], config: Optional[RunnableConfig] = None
    ) -> Dict[str, Any]:
        """
        Invoke the agent with the given inputs.

        Args:
            inputs: Agent inputs
            config: Runnable configuration

        Returns:
            Dict[str, Any]: Agent outputs
        """
        # Get the messages from the inputs
        messages = inputs.get("messages", [])

        # Get the configurable parameters
        configurable = inputs.get("configurable", {})
        thread_id = configurable.get("thread_id")
        tenant_id = configurable.get("tenant_id")
        user_id = configurable.get("user_id")

        # Create the initial state
        state = {
            "messages": messages,
            "thread_id": thread_id,
            "tenant_id": tenant_id,
            "user_id": user_id
        }

        # Initialize the agent
        state = await self.initialize(state, config or {})

        # Execute the agent
        state = await self.execute(state, config or {})

        # Clean up the agent
        state = await self.cleanup(state, config or {})

        return state
