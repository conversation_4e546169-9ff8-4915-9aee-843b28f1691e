"""
Authentication models for Calendar CRUD Agent.

This module contains Pydantic models for authentication and authorization,
including JWT token validation and user context.
"""

from typing import Dict, Any, Optional, List
from pydantic import BaseModel, Field
from datetime import datetime


class JWTClaims(BaseModel):
    """JWT claims extracted from a Supabase JWT token."""

    sub: str = Field(..., description="Subject (user ID)")
    email: Optional[str] = Field(None, description="User email")
    role: str = Field(..., description="User role")
    tenant_id: Optional[str] = Field(None, description="Tenant/Firm ID")
    exp: datetime = Field(..., description="Expiration time")
    iat: datetime = Field(..., description="Issued at time")

    # Additional Supabase claims
    aud: Optional[str] = Field(None, description="Audience")
    iss: Optional[str] = Field(None, description="Issuer")

    # Custom claims
    permissions: Optional[List[str]] = Field(None, description="User permissions")
    metadata: Optional[Dict[str, Any]] = Field(None, description="Additional metadata")


class UserContext(BaseModel):
    """User context extracted from JWT token."""

    user_id: str = Field(..., description="User ID")
    email: Optional[str] = Field(None, description="User email")
    role: str = Field(..., description="User role")
    firm_id: Optional[str] = Field(None, description="Firm/Tenant ID")
    permissions: List[str] = Field(default_factory=list, description="User permissions")
    is_authenticated: bool = Field(
        True, description="Whether the user is authenticated"
    )


class TokenResponse(BaseModel):
    """Response model for token operations."""

    access_token: str = Field(..., description="JWT access token")
    token_type: str = Field("bearer", description="Token type")
    expires_in: int = Field(..., description="Token expiration time in seconds")


class OAuthProvider(BaseModel):
    """OAuth provider information."""

    provider_id: str = Field(
        ..., description="Provider ID (e.g., 'google', 'microsoft')"
    )
    name: str = Field(..., description="Provider name")
    icon: str = Field(..., description="Provider icon URL")
    is_connected: bool = Field(False, description="Whether the provider is connected")
    connected_at: Optional[datetime] = Field(
        None, description="When the provider was connected"
    )
    scopes: List[str] = Field(default_factory=list, description="OAuth scopes granted")
    metadata: Dict[str, Any] = Field(
        default_factory=dict, description="Additional metadata"
    )
