"""
Calendar CRUD Agent Graph

This module defines the LangGraph workflow for the Calendar CRUD Agent.
It connects the various nodes to create a complete calendar management workflow
with intent detection and routing to appropriate operations.

The graph supports multiple calendar operations:
1. Create Event: Creates new calendar events with conflict checking
2. Read Event: Retrieves and lists calendar events with filtering
3. Update Event: Updates existing calendar events
4. Delete Event: Deletes calendar events
5. Check Free/Busy: Checks availability for scheduling

The workflow starts with intent detection (DetectIntentNode) which routes to the
appropriate operation node based on user input analysis.
"""

import logging
from typing import Dict, Any, cast
from langchain_core.runnables import RunnableConfig
from langgraph.graph import StateGraph, END

from shared.core.llm.voyage import VoyageClient

from backend.agents.interactive.calendar_crud.router import calendar_crud_router
from backend.agents.interactive.calendar_crud.nodes import (
    create_event,
    read_event,
    update_event,
    delete_event,
    check_free_busy,
    parse_date
)

# Configure logger
logger = logging.getLogger(__name__)


def create_calendar_graph(*, voyage: VoyageClient) -> StateGraph:
    """
    Create the calendar CRUD workflow graph.

    This function builds a LangGraph StateGraph that implements the calendar
    CRUD workflow.
    It connects the various nodes to create a complete calendar management process
    with intent detection and routing to appropriate operations.

    Args:
        voyage: VoyageClient instance for LLM operations

    Returns:
        StateGraph: Compiled calendar CRUD workflow graph
    """
    logger.info("Creating calendar CRUD StateGraph workflow")
    
    # Create the graph with Dict[str, Any] as the state type
    # This follows the pattern used in other agents in the codebase
    workflow = StateGraph(Dict[str, Any])

    # Add the DetectIntentNode (router) for intent classification
    workflow.add_node("detect_intent", calendar_crud_router)
    
    # Add the date parser node for preprocessing natural language dates
    workflow.add_node("parse_date", parse_date)

    # Add operation nodes for calendar CRUD operations
    workflow.add_node("create_event", create_event)
    workflow.add_node("read_event", read_event)
    workflow.add_node("update_event", update_event)
    workflow.add_node("delete_event", delete_event)
    workflow.add_node("check_free_busy", check_free_busy)

    # Set the entry point to intent detection
    workflow.set_entry_point("detect_intent")

    # Add edge from intent detection to date parsing
    # All operations may need date parsing, so we do it after intent detection
    workflow.add_edge("detect_intent", "parse_date")

    # Add conditional edges from date parsing to operation nodes based on
    # detected intent
    workflow.add_conditional_edges(
        "parse_date",
        _route_to_operation,
        {
            "create_event": "create_event",
            "read_event": "read_event", 
            "update_event": "update_event",
            "delete_event": "delete_event",
            "check_free_busy": "check_free_busy",
        }
    )

    # Add edges from all operation nodes to END
    workflow.add_edge("create_event", END)
    workflow.add_edge("read_event", END)
    workflow.add_edge("update_event", END)
    workflow.add_edge("delete_event", END)
    workflow.add_edge("check_free_busy", END)

    # Compile the workflow
    compiled_workflow = workflow.compile()
    
    logger.info("Calendar CRUD StateGraph workflow created and compiled successfully")
    return compiled_workflow


def _route_to_operation(state: Dict[str, Any]) -> str:
    """
    Route to the appropriate operation based on the detected intent.
    
    This function examines the state to determine which operation node
    should be executed next based on the intent detected by the router.
    
    Args:
        state: Current workflow state containing routing information
        
    Returns:
        str: Name of the next operation node to execute
    """
    # Check if the router set a 'next' field in the state
    next_operation = state.get("next")
    
    if next_operation:
        logger.info(f"Routing to operation: {next_operation}")
        return next_operation
    
    # Check for operation in metadata or other state fields
    operation = state.get("operation")
    if operation:
        logger.info(f"Routing based on operation field: {operation}")
        return operation
    
    # Check for calendar-specific routing information
    calendar_operation = state.get("calendar_operation")
    if calendar_operation:
        logger.info(f"Routing based on calendar_operation: {calendar_operation}")
        return calendar_operation
    
    # Default to create_event if no routing information is found
    logger.warning("No routing information found in state, defaulting to create_event")
    return "create_event"


async def _error_handler(
    state: Dict[str, Any], config: RunnableConfig
) -> Dict[str, Any]:
    """
    Handle errors that occur during workflow execution.
    
    This function provides centralized error handling for the calendar CRUD workflow.
    It logs errors and returns an appropriate error response to the user.
    
    Args:
        state: Current workflow state
        config: Runnable configuration
        
    Returns:
        Dict[str, Any]: Updated state with error information
    """
    error_msg = state.get("error", "An unknown error occurred")
    logger.error(f"Calendar CRUD workflow error: {error_msg}")
    
    # Get existing messages or initialize empty list
    messages = state.get("messages", [])
    
    # Add error message to the conversation
    error_response = {
        "type": "ai",
        "content": (
            f"I apologize, but I encountered an error while processing your "
            f"calendar request: {error_msg}. Please try again or contact "
            f"support if the issue persists."
        )
    }
    
    return {
        **state,
        "messages": messages + [error_response],
        "status": "error",
        "error": error_msg
    }


def get_workflow_info() -> Dict[str, Any]:
    """
    Get information about the calendar CRUD workflow.
    
    Returns:
        Dict[str, Any]: Workflow information including nodes, edges, and capabilities
    """
    return {
        "name": "calendar_crud_workflow",
        "description": "LangGraph workflow for calendar CRUD operations",
        "version": "1.0.0",
        "nodes": [
            "detect_intent",
            "parse_date", 
            "create_event",
            "read_event",
            "update_event",
            "delete_event",
            "check_free_busy"
        ],
        "entry_point": "detect_intent",
        "capabilities": [
            "Intent detection and routing",
            "Natural language date parsing",
            "Calendar event creation with conflict checking",
            "Calendar event reading and filtering",
            "Calendar event updates",
            "Calendar event deletion",
            "Free/busy time checking"
        ],
        "supported_providers": [
            "google",
            "outlook",
            "calendly"
        ]
    }
