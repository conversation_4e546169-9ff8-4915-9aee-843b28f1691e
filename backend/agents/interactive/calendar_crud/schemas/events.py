"""
Event schemas for Calendar CRUD API.

This module defines the request and response schemas for event
endpoints in the Calendar CRUD API.
"""

# We'll use the existing models from the providers module for our API
# This ensures compatibility between the API and the provider implementations

from datetime import datetime
from typing import Any, Dict, List, Optional, Union

from pydantic import BaseModel, Field, field_validator


class Attendee(BaseModel):
    """
    Schema for an event attendee.

    Attributes:
        email: The attendee's email address
        name: The attendee's name
        response_status: The attendee's response status
        optional: Whether the attendee is optional
    """

    email: str = Field(
        ...,
        description="The attendee's email address",
    )
    name: Optional[str] = Field(
        None,
        description="The attendee's name",
    )
    response_status: Optional[str] = Field(
        None,
        description="The attendee's response status",
        examples=["needsAction", "declined", "tentative", "accepted"],
    )
    optional: Optional[bool] = Field(
        False,
        description="Whether the attendee is optional",
    )


class EventTime(BaseModel):
    """
    Schema for an event time.

    Attributes:
        event_time: The date and time
        timezone: The timezone
    """

    event_time: datetime = Field(
        ...,
        description="The date and time",
    )
    timezone: Optional[str] = Field(
        None,
        description="The timezone",
        examples=["America/New_York", "Europe/London"],
    )


class CalendarEvent(BaseModel):
    """
    Schema for a calendar event.

    Attributes:
        id: The event ID
        calendar_id: The calendar ID
        provider_id: The provider ID
        summary: The event summary
        description: The event description
        location: The event location
        start_time: The event start time
        end_time: The event end time
        attendees: The event attendees
        status: The event status
        html_link: The event HTML link
        created_at: When the event was created
        updated_at: When the event was last updated
        organizer: The event organizer
        recurrence: The event recurrence rule
        reminders: The event reminders
        conference_data: The event conference data
        visibility: The event visibility
        transparency: The event transparency
        color_id: The event color ID
    """

    id: str = Field(
        ...,
        description="The event ID",
    )
    calendar_id: str = Field(
        ...,
        description="The calendar ID",
    )
    provider_id: str = Field(
        ...,
        description="The provider ID",
        examples=["google", "calendly"],
    )
    summary: str = Field(
        ...,
        description="The event summary",
    )
    description: Optional[str] = Field(
        None,
        description="The event description",
    )
    location: Optional[str] = Field(
        None,
        description="The event location",
    )
    start_time: datetime = Field(
        ...,
        description="The event start time",
    )
    end_time: datetime = Field(
        ...,
        description="The event end time",
    )
    attendees: Optional[List[Attendee]] = Field(
        None,
        description="The event attendees",
    )
    status: Optional[str] = Field(
        None,
        description="The event status",
        examples=["confirmed", "tentative", "cancelled"],
    )
    html_link: Optional[str] = Field(
        None,
        description="The event HTML link",
    )
    created_at: Optional[datetime] = Field(
        None,
        description="When the event was created",
    )
    updated_at: Optional[datetime] = Field(
        None,
        description="When the event was last updated",
    )
    organizer: Optional[Dict[str, str]] = Field(
        None,
        description="The event organizer",
    )
    recurrence: Optional[List[str]] = Field(
        None,
        description="The event recurrence rule",
    )
    reminders: Optional[
        Dict[str, Union[List[Dict[str, Union[str, int]]], bool]]
    ] = Field(
        None,
        description="The event reminders",
    )
    conference_data: Optional[Dict[str, Union[str, Dict[str, str]]]] = Field(
        None,
        description="The event conference data",
    )
    visibility: Optional[str] = Field(
        None,
        description="The event visibility",
        examples=["default", "public", "private", "confidential"],
    )
    transparency: Optional[str] = Field(
        None,
        description="The event transparency",
        examples=["opaque", "transparent"],
    )
    color_id: Optional[str] = Field(
        None,
        description="The event color ID",
    )

    @field_validator("end_time")
    def validate_end_time(cls, v: datetime, info) -> datetime:
        """Validate that end_time is after start_time."""
        values = info.data
        if "start_time" in values and v <= values["start_time"]:
            raise ValueError("end_time must be after start_time")
        return v


class CalendarEventCreate(BaseModel):
    """
    Schema for creating a calendar event.

    Attributes:
        summary: The event summary
        description: The event description
        location: The event location
        start_time: The event start time
        end_time: The event end time
        attendees: The event attendees
        reminders: The event reminders
        conference_data: The event conference data
        visibility: The event visibility
        transparency: The event transparency
        color_id: The event color ID
    """

    summary: str = Field(
        ...,
        description="The event summary",
    )
    description: Optional[str] = Field(
        None,
        description="The event description",
    )
    location: Optional[str] = Field(
        None,
        description="The event location",
    )
    start_time: datetime = Field(
        ...,
        description="The event start time",
    )
    end_time: datetime = Field(
        ...,
        description="The event end time",
    )
    attendees: Optional[List[Attendee]] = Field(
        None,
        description="The event attendees",
    )
    reminders: Optional[
        Dict[str, Union[List[Dict[str, Union[str, int]]], bool]]
    ] = Field(
        None,
        description="The event reminders",
    )
    conference_data: Optional[Dict[str, Union[str, Dict[str, str]]]] = Field(
        None,
        description="The event conference data",
    )
    visibility: Optional[str] = Field(
        None,
        description="The event visibility",
        examples=["default", "public", "private", "confidential"],
    )
    transparency: Optional[str] = Field(
        None,
        description="The event transparency",
        examples=["opaque", "transparent"],
    )
    color_id: Optional[str] = Field(
        None,
        description="The event color ID",
    )

    @field_validator("end_time")
    def validate_end_time(cls, v: datetime, info) -> datetime:
        """Validate that end_time is after start_time."""
        values = info.data
        if "start_time" in values and v <= values["start_time"]:
            raise ValueError("end_time must be after start_time")
        return v


class CalendarEventUpdate(BaseModel):
    """
    Schema for updating a calendar event.

    Attributes:
        summary: The event summary
        description: The event description
        location: The event location
        start_time: The event start time
        end_time: The event end time
        attendees: The event attendees
        status: The event status
        reminders: The event reminders
        conference_data: The event conference data
        visibility: The event visibility
        transparency: The event transparency
        color_id: The event color ID
    """

    summary: Optional[str] = Field(
        None,
        description="The event summary",
    )
    description: Optional[str] = Field(
        None,
        description="The event description",
    )
    location: Optional[str] = Field(
        None,
        description="The event location",
    )
    start_time: Optional[datetime] = Field(
        None,
        description="The event start time",
    )
    end_time: Optional[datetime] = Field(
        None,
        description="The event end time",
    )
    attendees: Optional[List[Attendee]] = Field(
        None,
        description="The event attendees",
    )
    status: Optional[str] = Field(
        None,
        description="The event status",
        examples=["confirmed", "tentative", "cancelled"],
    )
    reminders: Optional[
        Dict[str, Union[List[Dict[str, Union[str, int]]], bool]]
    ] = Field(
        None,
        description="The event reminders",
    )
    conference_data: Optional[Dict[str, Union[str, Dict[str, str]]]] = Field(
        None,
        description="The event conference data",
    )
    visibility: Optional[str] = Field(
        None,
        description="The event visibility",
        examples=["default", "public", "private", "confidential"],
    )
    transparency: Optional[str] = Field(
        None,
        description="The event transparency",
        examples=["opaque", "transparent"],
    )
    color_id: Optional[str] = Field(
        None,
        description="The event color ID",
    )

    @field_validator("end_time")
    def validate_end_time(cls, v: Optional[datetime], info) -> Optional[datetime]:
        """Validate that end_time is after start_time if both are provided."""
        values = info.data
        if (
            v is not None
            and "start_time" in values
            and values["start_time"] is not None
            and v <= values["start_time"]
        ):
            raise ValueError("end_time must be after start_time")
        return v


class EventsQueryParams(BaseModel):
    """
    Query parameters for listing events.

    Attributes:
        provider: The calendar provider ID
        calendar_id: The calendar ID
        start_time: The start of the time range
        end_time: The end of the time range
        max_results: The maximum number of events to return
    """

    provider: str = Field(
        ...,
        description="The calendar provider ID",
        examples=["google", "calendly"],
    )
    calendar_id: str = Field(
        ...,
        description="The calendar ID",
    )
    start_time: datetime = Field(
        ...,
        description="The start of the time range",
    )
    end_time: datetime = Field(
        ...,
        description="The end of the time range",
    )
    max_results: Optional[int] = Field(
        100,
        description="The maximum number of events to return",
        ge=1,
        le=1000,
    )

    @field_validator("end_time")
    def validate_end_time(cls, v: datetime, info) -> datetime:
        """Validate that end_time is after start_time."""
        values = info.data
        if "start_time" in values and v <= values["start_time"]:
            raise ValueError("end_time must be after start_time")
        return v


class EventQueryParams(BaseModel):
    """
    Query parameters for getting an event.

    Attributes:
        provider: The calendar provider ID
        calendar_id: The calendar ID
    """

    provider: str = Field(
        ...,
        description="The calendar provider ID",
        examples=["google", "calendly"],
    )
    calendar_id: str = Field(
        ...,
        description="The calendar ID",
    )


class CreateEventRequest(BaseModel):
    """
    Request schema for creating an event.

    Attributes:
        provider: The calendar provider ID
        calendar_id: The calendar ID
        event: The event to create
    """

    provider: str = Field(
        ...,
        description="The calendar provider ID",
        examples=["google", "calendly"],
    )
    calendar_id: str = Field(
        ...,
        description="The calendar ID",
    )
    event: CalendarEventCreate = Field(
        ...,
        description="The event to create",
    )


class UpdateEventRequest(BaseModel):
    """
    Request schema for updating an event.

    Attributes:
        provider: The calendar provider ID
        calendar_id: The calendar ID
        event: The event updates
    """

    provider: str = Field(
        ...,
        description="The calendar provider ID",
        examples=["google", "calendly"],
    )
    calendar_id: str = Field(
        ...,
        description="The calendar ID",
    )
    event: CalendarEventUpdate = Field(
        ...,
        description="The event updates",
    )


class DeleteEventQueryParams(BaseModel):
    """
    Query parameters for deleting an event.

    Attributes:
        provider: The calendar provider ID
        calendar_id: The calendar ID
    """

    provider: str = Field(
        ...,
        description="The calendar provider ID",
        examples=["google", "calendly"],
    )
    calendar_id: str = Field(
        ...,
        description="The calendar ID",
    )


class DeleteEventResponse(BaseModel):
    """
    Response schema for deleting an event.

    Attributes:
        success: Whether the deletion was successful
        message: A message describing the result
    """

    success: bool = Field(
        ...,
        description="Whether the deletion was successful",
    )
    message: str = Field(
        ...,
        description="A message describing the result",
    )


class EventsResponse(BaseModel):
    """
    Response schema for listing events.

    Attributes:
        events: The list of events
    """

    events: List[CalendarEvent] = Field(
        ...,
        description="The list of events",
    )


class SchedulingLinkResponse(BaseModel):
    """
    Response schema for Calendly scheduling link.

    Attributes:
        scheduling_url: The scheduling URL
        expires_at: When the link expires
        event_type_id: The event type ID
    """

    scheduling_url: str = Field(
        ...,
        description="The scheduling URL",
    )
    expires_at: datetime = Field(
        ...,
        description="When the link expires",
    )
    event_type_id: str = Field(
        ...,
        description="The event type ID",
    )