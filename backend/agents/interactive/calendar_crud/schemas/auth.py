"""
Authentication schemas for Calendar CRUD API.

This module defines the request and response schemas for authentication
endpoints in the Calendar CRUD API.
"""

from datetime import datetime
from typing import Dict, List, Optional, Union

from pydantic import BaseModel, Field, HttpUrl, field_validator


class ConnectRequest(BaseModel):
    """
    Request schema for initiating OAuth flow.

    Attributes:
        provider: The calendar provider ID (e.g., "google", "calendly")
        redirect_uri: The URI to redirect to after authentication
    """

    provider: str = Field(
        ...,
        description="The calendar provider ID (e.g., 'google', 'calendly')",
        examples=["google", "calendly"],
    )
    redirect_uri: HttpUrl = Field(
        ...,
        description="The URI to redirect to after authentication",
        examples=["https://example.com/callback"],
    )

    @field_validator("provider")
    def validate_provider(cls, v: str) -> str:
        """Validate that the provider is supported."""
        supported_providers = ["google", "calendly"]
        if v not in supported_providers:
            raise ValueError(
                f"Provider must be one of: {', '.join(supported_providers)}"
            )
        return v


class ConnectResponse(BaseModel):
    """
    Response schema for initiating OAuth flow.

    Attributes:
        auth_url: The URL to redirect the user to for authentication
    """

    auth_url: HttpUrl = Field(
        ...,
        description="The URL to redirect the user to for authentication",
    )


class OAuthCallbackParams(BaseModel):
    """
    Query parameters for OAuth callback.

    Attributes:
        code: The authorization code from the provider
        state: The state parameter for CSRF protection
        error: The error message if authentication failed
        error_description: The error description if authentication failed
    """

    code: Optional[str] = Field(
        None,
        description="The authorization code from the provider",
    )
    state: Optional[str] = Field(
        None,
        description="The state parameter for CSRF protection",
    )
    error: Optional[str] = Field(
        None,
        description="The error message if authentication failed",
    )
    error_description: Optional[str] = Field(
        None,
        description="The error description if authentication failed",
    )


class TokenInfo(BaseModel):
    """
    Information about an OAuth token.

    Attributes:
        access_token: The access token
        refresh_token: The refresh token
        expires_at: The expiration timestamp
        scope: The token scope
    """

    access_token: str = Field(
        ...,
        description="The access token",
    )
    refresh_token: Optional[str] = Field(
        None,
        description="The refresh token",
    )
    expires_at: datetime = Field(
        ...,
        description="The expiration timestamp",
    )
    scope: str = Field(
        ...,
        description="The token scope",
    )


class DisconnectRequest(BaseModel):
    """
    Request schema for disconnecting a provider.

    Attributes:
        provider: The calendar provider ID (e.g., "google", "calendly")
    """

    provider: str = Field(
        ...,
        description="The calendar provider ID (e.g., 'google', 'calendly')",
        examples=["google", "calendly"],
    )

    @field_validator("provider")
    def validate_provider(cls, v: str) -> str:
        """Validate that the provider is supported."""
        supported_providers = ["google", "calendly"]
        if v not in supported_providers:
            raise ValueError(
                f"Provider must be one of: {', '.join(supported_providers)}"
            )
        return v


class DisconnectResponse(BaseModel):
    """
    Response schema for disconnecting a provider.

    Attributes:
        success: Whether the disconnection was successful
        message: A message describing the result
    """

    success: bool = Field(
        ...,
        description="Whether the disconnection was successful",
    )
    message: str = Field(
        ...,
        description="A message describing the result",
    )
