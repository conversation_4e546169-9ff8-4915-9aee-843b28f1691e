"""
Calendar CRUD Agent Router

This module provides the router node for the Calendar CRUD Agent,
which determines the appropriate operation based on user intent.
"""

import logging
import json
from typing import Dict, Any, Literal, TypedDict, Annotated, cast
from langchain_core.runnables import RunnableConfig
from langchain_core.messages import HumanMessage

from shared.core.llm.voyage import Voyage<PERSON><PERSON>
from shared.core.tools.executor import get_tool_executor

# Set up logging
logger = logging.getLogger(__name__)

# Define the router output type
class RouterOutput(TypedDict):
    next: Literal[
        "create_event", "read_event", "update_event", "delete_event", "check_free_busy"
    ]

async def calendar_crud_router(
    state: Dict[str, Any], config: RunnableConfig
) -> RouterOutput:
    """
    Router node for the Calendar CRUD Agent.
    
    This node determines the appropriate operation based on user intent.
    
    Args:
        state: Agent state
        config: Runnable configuration
        
    Returns:
        RouterOutput: Next node to execute
    """
    # Get the user input
    messages = state.get("messages", [])
    if not messages:
        return {"next": "create_event"}  # Default to create_event if no messages
    
    # Find the last human message
    user_input = ""
    for message in reversed(messages):
        if isinstance(message, HumanMessage) or (
            hasattr(message, "type") and message.type == "human"
        ):
            user_input = message.content
            break
    
    if not user_input:
        return {
            "next": "create_event"
        }  # Default to create_event if no human message found
    
    # Get the LLM client
    llm_client = VoyageClient()
    
    # Get the prompt template
    prompt_template = (
        "You are a calendar management assistant. Your job is to help users "
        "manage their calendar events.\n"
        "\n"
        "Based on the user's request, determine which operation they want to perform:\n"
        "- create_event: Create a new calendar event\n"
        "- read_event: Read or list calendar events\n"
        "- update_event: Update an existing calendar event\n"
        "- delete_event: Delete a calendar event\n"
        "- check_free_busy: Check free/busy times for scheduling\n"
        "\n"
        "User request: {input}\n"
        "\n"
        "Operation:"
    )
    
    # Format the prompt
    prompt = prompt_template.format(input=user_input)
    
    try:
        # Call the LLM
        response = await llm_client.invoke(prompt)
        
        # Parse the response
        operation = response.strip().lower()
        
        # Map the operation to the next node
        operation_map = {
            "create_event": "create_event",
            "read_event": "read_event",
            "update_event": "update_event",
            "delete_event": "delete_event",
            "check_free_busy": "check_free_busy",
        }
        
        # Default to create_event if the operation is not recognized
        next_node = operation_map.get(operation, "create_event")
        
        logger.info(f"Routing to {next_node} based on user input: {user_input}")
        
        return {"next": next_node}
    except Exception as e:
        logger.error(f"Error in calendar_crud_router: {str(e)}")
        return {"next": "create_event"}  # Default to create_event on error
