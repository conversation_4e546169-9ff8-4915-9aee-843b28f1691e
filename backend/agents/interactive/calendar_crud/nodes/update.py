"""
Update Event Node

This module provides a node for updating calendar events.
It extracts event details from user input and updates an existing event.
"""

import logging
import json
from typing import Dict, Any, Optional, List, cast
from datetime import datetime
from langchain_core.runnables import RunnableConfig
from langchain_core.messages import HumanMessage, AIMessage

from pi_lawyer.agents.base_agent import BaseAgent
from shared.core.llm.voyage import Voyage<PERSON>lient
from shared.core.tools.executor import get_tool_executor

from backend.agents.interactive.calendar_crud.providers.factory import (
    get_provider_instance,
)
from backend.agents.interactive.calendar_crud.providers.models import (
    CalendarEventUpdate,
    CalendarEventCreate,
    Attendee,
)
from backend.agents.interactive.calendar_crud.providers.exceptions import (
    UnsupportedOperationError,
)
from backend.agents.interactive.calendar_crud.utils.conflict import detect_conflicts

# Set up logging
logger = logging.getLogger(__name__)


class UpdateEventNode(BaseAgent):
    """
    Node for updating calendar events.

    This node extracts event details from user input and updates an existing event.
    """

    def __init__(
        self,
        agent_name: str = "calendarCrudAgent",
        node_name: str = "update_event",
    ):
        """
        Initialize the update event node.

        Args:
            agent_name: Name of the agent
            node_name: Name of the node
        """
        super().__init__(agent_name=agent_name, node_name=node_name)

    async def initialize(
        self, state: Dict[str, Any], config: RunnableConfig
    ) -> Dict[str, Any]:
        """Initialize the node."""
        return state

    async def cleanup(
        self, state: Dict[str, Any], config: RunnableConfig
    ) -> Dict[str, Any]:
        """Clean up the node."""
        return state

    async def execute(
        self, state: Dict[str, Any], config: RunnableConfig
    ) -> Dict[str, Any]:
        """
        Update an existing calendar event.

        This node extracts event details from user input and updates an existing event.

        Args:
            state: Agent state
            config: Runnable configuration

        Returns:
            Dict[str, Any]: Updated state
        """
        # Get the user input
        messages = state.get("messages", [])

        # Find the last human message
        user_input = ""
        for message in reversed(messages):
            if isinstance(message, HumanMessage) or (
                hasattr(message, "type") and message.type == "human"
            ):
                user_input = message.content
                break

        if not user_input:
            return {
                "messages": messages
                + [
                    AIMessage(
                        content="I couldn't understand your request. Please provide "
                        "details for the event you want to update."
                    )
                ]
            }

        # Get the tenant ID and user ID from the state
        tenant_id = state.get("tenant_id")
        user_id = state.get("user_id")

        if not tenant_id or not user_id:
            return {
                "messages": messages
                + [
                    AIMessage(
                        content="Error: Missing tenant ID or user ID. Please try "
                        "again later."
                    )
                ]
            }

        # Get the LLM client
        llm_client = VoyageClient()

        # Extract event details using LLM
        prompt_template = (
            "You are a calendar management assistant. Your job is to help "
            "users update existing calendar events.\n"
            "\n"
            "Extract the following information from the user's request:\n"
            "- Event ID\n"
            "- Calendar ID (if provided, otherwise use \"primary\")\n"
            "- Provider (if provided, otherwise use \"google\")\n"
            "- Fields to update (summary, description, start/end times, "
            "location, attendees)\n"
            "\n"
            "User request: {input}\n"
            "\n"
            "Provide the extracted information in the following JSON format:\n"
            "```json\n"
            "{{\n"
            "  \"event_id\": \"abc123\",\n"
            "  \"calendar_id\": \"primary\",\n"
            "  \"provider\": \"google\",\n"
            "  \"updates\": {{\n"
            "    \"summary\": \"Updated Meeting with Client\",\n"
            "    \"description\": \"Discuss revised project requirements\",\n"
            "    \"start_time\": \"2023-06-01T10:00:00\",\n"
            "    \"end_time\": \"2023-06-01T11:00:00\",\n"
            "    \"location\": \"Conference Room\",\n"
            "    \"attendees\": [\"<EMAIL>\", \"<EMAIL>\"]\n"
            "  }}\n"
            "}}\n"
            "```\n"
            "\n"
            "JSON:"
        )

        # Format the prompt
        prompt = prompt_template.format(input=user_input)

        try:
            # Call the LLM
            response = await llm_client.invoke(prompt)

            # Parse the response
            response_text = response.strip()

            # Extract JSON from the response
            if "```json" in response_text:
                json_str = response_text.split("```json")[1].split("```")[0].strip()
            elif "```" in response_text:
                json_str = response_text.split("```")[1].strip()
            else:
                json_str = response_text

            event_data = json.loads(json_str)

            # Get the provider
            provider = event_data.get("provider", "google")

            # Get the provider instance
            provider_instance = get_provider_instance(provider, tenant_id)

            # Check if the provider supports updating events
            supports_update = False
            try:
                supports_update = await provider_instance.supports("update_event")
            except:
                # If it's not an async method, try the sync version
                supports_update = provider_instance.supports("update_event")

            if not supports_update:
                # Special message for Calendly
                if provider.lower() == "calendly":
                    return {
                        "messages": messages
                        + [
                            AIMessage(
                                content="Calendly events can only be cancelled via "
                                "link. Updates are not supported through the "
                                "Calendly API."
                            )
                        ]
                    }
                else:
                    return {
                        "messages": messages
                        + [
                            AIMessage(
                                content=f"The {provider} provider does not support "
                                "updating events."
                            )
                        ]
                    }

            # Create attendees list if provided
            attendees = None
            if "attendees" in event_data.get("updates", {}):
                attendees = [
                Attendee(email=email) for email in event_data["updates"]["attendees"]
            ]

            # Create the update object
            update_data = event_data.get("updates", {})
            if attendees is not None:
                update_data["attendees"] = attendees

            # Create a CalendarEventCreate object for conflict checking
            calendar_id = event_data.get("calendar_id", "primary")
            event_id = event_data.get("event_id")

            # Check for conflicts if start_time or end_time is being updated
            if "start_time" in update_data or "end_time" in update_data:
                # Get the current event to get any missing fields
                current_event = await provider_instance.get_event(
                    calendar_id=calendar_id,
                    event_id=event_id
                )

                # Create a CalendarEventCreate object with the updated fields
                conflict_check_event = CalendarEventCreate(
                    summary=update_data.get("summary", current_event.summary),
                    description=update_data.get(
                        "description", current_event.description
                    ),
                    start_time=update_data.get("start_time", current_event.start_time),
                    end_time=update_data.get("end_time", current_event.end_time),
                    location=update_data.get("location", current_event.location),
                    attendees=update_data.get("attendees", current_event.attendees)
                )

                # Check for conflicts
                conflict_result = await detect_conflicts(
                    tenant_id=tenant_id,
                    provider=provider,
                    calendar_id=calendar_id,
                    event=conflict_check_event,
                    buffer_minutes=0,  # No buffer for now
                    exclude_event_id=event_id  # Exclude the current event
                )

                # If conflicts were found, warn the user
                if conflict_result["has_conflicts"]:
                    conflict_warning = (
                        "⚠️ Warning: This update creates conflicts with existing "
                        "events:\n"
                        f"\n{conflict_result['message']}\n"
                        "\n"
                        "I've updated the event anyway, but you may want to "
                        "reschedule to avoid conflicts."
                    )
                else:
                    conflict_warning = ""
            else:
                conflict_warning = ""

            # Update the event
            event = await provider_instance.update_event(
                calendar_id=calendar_id,
                event_id=event_id,
                event=CalendarEventUpdate(**update_data)
            )

            # Format the response
            response_message = f"""Event updated successfully!

Summary: {event.summary}
Description: {event.description}
Start: {event.start_time}
End: {event.end_time}
Location: {event.location or "Not specified"}
"""

            if event.attendees:
                response_message += "\nAttendees:\n"
                for attendee in event.attendees:
                    response_message += f"- {attendee.email}\n"

            if hasattr(event, "provider_event_link") and event.provider_event_link:
                response_message += f"\nEvent Link: {event.provider_event_link}"

            # Add conflict warning if conflicts were found
            if "conflict_warning" in locals() and conflict_warning:
                response_message += f"\n\n{conflict_warning}"

            return {
                "messages": messages + [AIMessage(content=response_message)],
                "event": event.dict() if hasattr(event, "dict") else event
            }
        except UnsupportedOperationError:
            # Special message for Calendly
            if "calendly" in user_input.lower():
                return {
                    "messages": messages
                    + [
                        AIMessage(
                            content="Calendly events can only be cancelled via link. "
                            "Updates are not supported through the Calendly API."
                        )
                    ]
                }
            else:
                return {
                    "messages": messages
                    + [
                        AIMessage(
                            content="This calendar provider does not support "
                            "updating events."
                        )
                    ]
                }
        except Exception as e:
            logger.error(f"Error updating event: {str(e)}")
            return {
                "messages": messages
                + [AIMessage(content=f"Error updating event: {str(e)}")]
            }


# Create a function wrapper for backward compatibility
async def update_event(
    state: Dict[str, Any], config: RunnableConfig
) -> Dict[str, Any]:
    """
    Function wrapper for the UpdateEventNode class.

    This maintains backward compatibility with existing code that calls
    update_event as a function.
    """
    node = UpdateEventNode()
    return await node.execute(state, config)
