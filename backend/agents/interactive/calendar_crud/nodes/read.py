"""
Read Event Node

This module provides a node for reading calendar events.
It extracts query parameters from user input and retrieves events.
"""

import logging
import json
from typing import Dict, Any, Optional, List, cast
from datetime import datetime, timedelta
from langchain_core.runnables import RunnableConfig
from langchain_core.messages import HumanMessage, AIMessage

from pi_lawyer.agents.base_agent import BaseAgent
from shared.core.llm.voyage import VoyageClient
from shared.core.tools.executor import get_tool_executor

from backend.agents.interactive.calendar_crud.providers.factory import (
    get_provider_instance,
)
from backend.agents.interactive.calendar_crud.providers.exceptions import (
    UnsupportedOperationError,
)

# Set up logging
logger = logging.getLogger(__name__)


class ReadEventNode(BaseAgent):
    """
    Node for reading calendar events.

    This node extracts query parameters from user input and retrieves events.
    """

    def __init__(
        self, agent_name: str = "calendarCrudAgent", node_name: str = "read_event"
    ):
        """
        Initialize the read event node.

        Args:
            agent_name: Name of the agent
            node_name: Name of the node
        """
        super().__init__(agent_name=agent_name, node_name=node_name)

    async def initialize(
        self, state: Dict[str, Any], config: RunnableConfig
    ) -> Dict[str, Any]:
        """Initialize the node."""
        return state

    async def cleanup(
        self, state: Dict[str, Any], config: RunnableConfig
    ) -> Dict[str, Any]:
        """Clean up the node."""
        return state

    async def execute(
        self, state: Dict[str, Any], config: RunnableConfig
    ) -> Dict[str, Any]:
        """
        Read calendar events.

        This node extracts query parameters from user input and retrieves events.

        Args:
            state: Agent state
            config: Runnable configuration

        Returns:
            Dict[str, Any]: Updated state
        """
        # Get the user input
        messages = state.get("messages", [])

        # Find the last human message
        user_input = ""
        for message in reversed(messages):
            if isinstance(message, HumanMessage) or (
                hasattr(message, "type") and message.type == "human"
            ):
                user_input = message.content
                break

        if not user_input:
            return {
                "messages": messages
                + [
                    AIMessage(
                        content="I couldn't understand your request. Please provide "
                        "details for the events you want to view."
                    )
                ]
            }

        # Get the tenant ID and user ID from the state
        tenant_id = state.get("tenant_id")
        user_id = state.get("user_id")

        if not tenant_id or not user_id:
            return {
                "messages": messages
                + [
                    AIMessage(
                        content="Error: Missing tenant ID or user ID. Please try "
                        "again later."
                    )
                ]
            }

        # Get the LLM client
        llm_client = VoyageClient()

        # Extract query parameters using LLM
        prompt_template = (
            "You are a calendar management assistant. Your job is to help "
            "users read or list calendar events.\n"
            "\n"
            "Extract the following information from the user's request:\n"
            "- Date range (start and end dates)\n"
            "- Calendar ID (if provided, otherwise use \"primary\")\n"
            "- Provider (if provided, otherwise use \"google\")\n"
            "- Any filters or search terms\n"
            "\n"
            "User request: {input}\n"
            "\n"
            "Provide the extracted information in the following JSON format:\n"
            "```json\n"
            "{{\n"
            "  \"start_time\": \"2023-06-01T00:00:00\",\n"
            "  \"end_time\": \"2023-06-07T23:59:59\",\n"
            "  \"calendar_id\": \"primary\",\n"
            "  \"provider\": \"google\",\n"
            "  \"query\": \"client meeting\"\n"
            "}}\n"
            "```\n"
            "\n"
            "JSON:"
        )

        # Format the prompt
        prompt = prompt_template.format(input=user_input)

        try:
            # Call the LLM
            response = await llm_client.invoke(prompt)

            # Parse the response
            response_text = response.strip()

            # Extract JSON from the response
            if "```json" in response_text:
                json_str = response_text.split("```json")[1].split("```")[0].strip()
            elif "```" in response_text:
                json_str = response_text.split("```")[1].strip()
            else:
                json_str = response_text

            query_data = json.loads(json_str)

            # Get the provider
            provider = query_data.get("provider", "google")

            # Get the provider instance
            provider_instance = get_provider_instance(provider, tenant_id)

            # Check if the provider supports reading events
            supports_read = False
            try:
                supports_read = await provider_instance.supports("read_event")
            except:
                # If it's not an async method, try the sync version
                supports_read = provider_instance.supports("read_event")

            if not supports_read:
                return {
                    "messages": messages
                    + [
                        AIMessage(
                            content=f"The {provider} provider does not support "
                            "reading events."
                        )
                    ]
                }

            # Set default date range if not provided
            if not query_data.get("start_time"):
                # Default to today
                today = datetime.now()
                start_time = datetime(
                    today.year, today.month, today.day, 0, 0, 0
                ).isoformat()
                query_data["start_time"] = start_time

            if not query_data.get("end_time"):
                # Default to 7 days from start
                start_time = datetime.fromisoformat(
                    query_data["start_time"].replace("Z", "+00:00")
                )
                end_time = (start_time + timedelta(days=7)).isoformat()
                query_data["end_time"] = end_time

            # Get the events
            events = await provider_instance.list_events(
                calendar_id=query_data.get("calendar_id", "primary"),
                start_time=query_data.get("start_time"),
                end_time=query_data.get("end_time"),
                query=query_data.get("query", "")
            )

            # Format the response
            if not events:
                response_message = "No events found for the specified criteria."
            else:
                response_message = f"Found {len(events)} events:\n\n"

                for i, event in enumerate(events, 1):
                    response_message += f"{i}. {event.summary}\n"
                    response_message += f"   Date: {event.start_time.split('T')[0]}\n"
                    response_message += (
                        f"   Time: {event.start_time.split('T')[1][:5]} - "
                        f"{event.end_time.split('T')[1][:5]}\n"
                    )

                    if event.location:
                        response_message += f"   Location: {event.location}\n"

                    if i < len(events):
                        response_message += "\n"

            return {
                "messages": messages + [AIMessage(content=response_message)],
                "events": [
                    event.dict() if hasattr(event, "dict") else event
                    for event in events
                ]
            }
        except UnsupportedOperationError:
            return {
                "messages": messages
                + [
                    AIMessage(
                        content="This calendar provider does not support "
                        "reading events."
                    )
                ]
            }
        except Exception as e:
            logger.error(f"Error reading events: {str(e)}")
            return {
                "messages": messages
                + [AIMessage(content=f"Error reading events: {str(e)}")]
            }


# Create a function wrapper for backward compatibility
async def read_event(
    state: Dict[str, Any], config: RunnableConfig
) -> Dict[str, Any]:
    """
    Function wrapper for the ReadEventNode class.

    This maintains backward compatibility with existing code that calls
    read_event as a function.
    """
    node = ReadEventNode()
    return await node.execute(state, config)
