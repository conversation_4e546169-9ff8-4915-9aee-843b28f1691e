"""
Check Free/Busy Node

This module provides a node for checking free/busy times for scheduling.
It extracts query parameters from user input and checks availability.
"""

import logging
import json
from typing import Dict, Any, Optional, List, cast
from datetime import datetime, timedelta
from langchain_core.runnables import RunnableConfig
from langchain_core.messages import HumanMessage, AIMessage

from pi_lawyer.agents.base_agent import BaseAgent
from shared.core.llm.voyage import VoyageClient
from shared.core.tools.executor import get_tool_executor

from backend.agents.interactive.calendar_crud.providers.factory import (
    get_provider_instance,
)
from backend.agents.interactive.calendar_crud.providers.models import FreeBusyRequest
from backend.agents.interactive.calendar_crud.providers.exceptions import (
    UnsupportedOperationError,
)

# Set up logging
logger = logging.getLogger(__name__)


class CheckFreeBusyNode(BaseAgent):
    """
    Node for checking free/busy times for scheduling.

    This node extracts query parameters from user input and checks availability.
    """

    def __init__(
        self,
        agent_name: str = "calendarCrudAgent",
        node_name: str = "check_free_busy",
    ):
        """
        Initialize the check free/busy node.

        Args:
            agent_name: Name of the agent
            node_name: Name of the node
        """
        super().__init__(agent_name=agent_name, node_name=node_name)

    async def initialize(
        self, state: Dict[str, Any], config: RunnableConfig
    ) -> Dict[str, Any]:
        """Initialize the node."""
        return state

    async def cleanup(
        self, state: Dict[str, Any], config: RunnableConfig
    ) -> Dict[str, Any]:
        """Clean up the node."""
        return state

    async def execute(
        self, state: Dict[str, Any], config: RunnableConfig
    ) -> Dict[str, Any]:
        """
        Check free/busy times for scheduling.

        This node extracts query parameters from user input and checks availability.

        Args:
            state: Agent state
            config: Runnable configuration

        Returns:
            Dict[str, Any]: Updated state
        """
        # Get the user input
        messages = state.get("messages", [])

        # Find the last human message
        user_input = ""
        for message in reversed(messages):
            if isinstance(message, HumanMessage) or (
                hasattr(message, "type") and message.type == "human"
            ):
                user_input = message.content
                break

        if not user_input:
            return {
                "messages": messages
                + [
                    AIMessage(
                        content="I couldn't understand your request. Please provide "
                        "details for the time range you want to check."
                    )
                ]
            }

        # Get the tenant ID and user ID from the state
        tenant_id = state.get("tenant_id")
        user_id = state.get("user_id")

        if not tenant_id or not user_id:
            return {
                "messages": messages
                + [
                    AIMessage(
                        content="Error: Missing tenant ID or user ID. Please try "
                        "again later."
                    )
                ]
            }

        # Get the LLM client
        llm_client = VoyageClient()

        # Extract query parameters using LLM
        prompt_template = (
            "You are a calendar management assistant. Your job is to help "
            "users check free/busy times for scheduling.\n"
            "\n"
            "Extract the following information from the user's request:\n"
            "- Date range (start and end dates)\n"
            "- Calendar IDs (if provided, otherwise use [\"primary\"])\n"
            "- Provider (if provided, otherwise use \"google\")\n"
            "\n"
            "User request: {input}\n"
            "\n"
            "Provide the extracted information in the following JSON format:\n"
            "```json\n"
            "{{\n"
            "  \"start_time\": \"2023-06-01T09:00:00\",\n"
            "  \"end_time\": \"2023-06-01T17:00:00\",\n"
            "  \"calendar_ids\": [\"primary\"],\n"
            "  \"provider\": \"google\"\n"
            "}}\n"
            "```\n"
            "\n"
            "JSON:"
        )

        # Format the prompt
        prompt = prompt_template.format(input=user_input)

        try:
            # Call the LLM
            response = await llm_client.invoke(prompt)

            # Parse the response
            response_text = response.strip()

            # Extract JSON from the response
            if "```json" in response_text:
                json_str = response_text.split("```json")[1].split("```")[0].strip()
            elif "```" in response_text:
                json_str = response_text.split("```")[1].strip()
            else:
                json_str = response_text

            query_data = json.loads(json_str)

            # Get the provider
            provider = query_data.get("provider", "google")

            # Get the provider instance
            provider_instance = get_provider_instance(provider, tenant_id)

            # Check if the provider supports free/busy checking
            supports_free_busy = False
            try:
                supports_free_busy = await provider_instance.supports("free_busy")
            except:
                # If it's not an async method, try the sync version
                supports_free_busy = provider_instance.supports("free_busy")

            if not supports_free_busy:
                # Special handling for Calendly
                if provider.lower() == "calendly":
                    return {
                        "messages": messages
                        + [
                            AIMessage(
                                content="Free/busy checking is not available for "
                                "Calendly. Please use the Calendly scheduling link "
                                "to check availability."
                            )
                        ]
                    }
                else:
                    return {
                        "messages": messages
                        + [
                            AIMessage(
                                content=f"The {provider} provider does not support "
                                "free/busy checking."
                            )
                        ]
                    }

            # Set default date range if not provided
            if not query_data.get("start_time"):
                # Default to today
                today = datetime.now()
                start_time = datetime(
                    today.year, today.month, today.day, 9, 0, 0
                ).isoformat()
                query_data["start_time"] = start_time

            if not query_data.get("end_time"):
                # Default to 8 hours from start
                start_time = datetime.fromisoformat(
                    query_data["start_time"].replace("Z", "+00:00")
                )
                end_time = (start_time + timedelta(hours=8)).isoformat()
                query_data["end_time"] = end_time

            # Set default calendar IDs if not provided
            if not query_data.get("calendar_ids"):
                query_data["calendar_ids"] = ["primary"]

            # Check free/busy
            free_busy_result = await provider_instance.check_free_busy(
                FreeBusyRequest(
                    start_time=query_data.get("start_time"),
                    end_time=query_data.get("end_time"),
                    calendar_ids=query_data.get("calendar_ids")
                )
            )

            # Format the response
            if not free_busy_result.busy_slots:
                response_message = (
                    f"You are free during the entire period from "
                    f"{query_data['start_time']} to {query_data['end_time']}."
                )
            else:
                response_message = (
                    f"Here's your availability from {query_data['start_time']} "
                    f"to {query_data['end_time']}:\n\n"
                )

                response_message += "Busy times:\n"
                for i, slot in enumerate(free_busy_result.busy_slots, 1):
                    start = (
                        slot.start_time.split("T")[1][:5]
                        if "T" in slot.start_time
                        else slot.start_time
                    )
                    end = (
                        slot.end_time.split("T")[1][:5]
                        if "T" in slot.end_time
                        else slot.end_time
                    )
                    date = (
                        slot.start_time.split("T")[0] if "T" in slot.start_time else ""
                    )

                    response_message += f"{i}. {date} {start} - {end}\n"

                # Calculate free slots
                free_slots = []
                start_time = datetime.fromisoformat(
                    query_data["start_time"].replace("Z", "+00:00")
                )
                end_time = datetime.fromisoformat(
                    query_data["end_time"].replace("Z", "+00:00")
                )

                current_time = start_time
                for busy_slot in free_busy_result.busy_slots:
                    busy_start = datetime.fromisoformat(
                        busy_slot.start_time.replace("Z", "+00:00")
                    )
                    busy_end = datetime.fromisoformat(
                        busy_slot.end_time.replace("Z", "+00:00")
                    )

                    if current_time < busy_start:
                        free_slots.append({
                            "start_time": current_time.isoformat(),
                            "end_time": busy_start.isoformat()
                        })

                    current_time = max(current_time, busy_end)

                if current_time < end_time:
                    free_slots.append({
                        "start_time": current_time.isoformat(),
                        "end_time": end_time.isoformat()
                    })

                if free_slots:
                    response_message += "\nFree times:\n"
                    for i, slot in enumerate(free_slots, 1):
                        start = (
                            slot["start_time"].split("T")[1][:5]
                            if "T" in slot["start_time"]
                            else slot["start_time"]
                        )
                        end = (
                            slot["end_time"].split("T")[1][:5]
                            if "T" in slot["end_time"]
                            else slot["end_time"]
                        )
                        date = (
                            slot["start_time"].split("T")[0]
                            if "T" in slot["start_time"]
                            else ""
                        )

                        response_message += f"{i}. {date} {start} - {end}\n"

            return {
                "messages": messages + [AIMessage(content=response_message)],
                "free_busy_result": (
                    free_busy_result.dict()
                    if hasattr(free_busy_result, "dict")
                    else free_busy_result
                )
            }
        except UnsupportedOperationError:
            # Special handling for Calendly
            if "calendly" in user_input.lower():
                return {
                    "messages": messages
                    + [
                        AIMessage(
                            content="Free/busy checking is not available for Calendly. "
                            "Please use the Calendly scheduling link to check "
                            "availability."
                        )
                    ]
                }
            else:
                return {
                    "messages": messages
                    + [
                        AIMessage(
                            content="This calendar provider does not support "
                            "free/busy checking."
                        )
                    ]
                }
        except Exception as e:
            logger.error(f"Error checking free/busy: {str(e)}")
            return {
                "messages": messages
                + [AIMessage(content=f"Error checking free/busy: {str(e)}")]
            }


# Create a function wrapper for backward compatibility
async def check_free_busy(
    state: Dict[str, Any], config: RunnableConfig
) -> Dict[str, Any]:
    """
    Function wrapper for the CheckFreeBusyNode class.

    This maintains backward compatibility with existing code that calls
    check_free_busy as a function.
    """
    node = CheckFreeBusyNode()
    return await node.execute(state, config)
