"""
Date Parser Node

This module provides a node for parsing natural language dates into
standardized formats.
It supports various date formats, relative dates, and natural language expressions.
"""

import logging
import re
from typing import Dict, Any, Optional, List, Union, Tuple
from datetime import datetime, timedelta
import dateparser
from dateparser.search import search_dates
from langchain_core.runnables import RunnableConfig
from langchain_core.messages import HumanMessage, AIMessage

from shared.core.llm.voyage import VoyageClient
from shared.core.tools.executor import get_tool_executor

# Set up logging
logger = logging.getLogger(__name__)

def extract_date_range(text: str) -> Tuple[Optional[datetime], Optional[datetime]]:
    """
    Extract date range from text using regex and dateparser.
    
    Args:
        text: Text containing date range
        
    Returns:
        Tuple[Optional[datetime], Optional[datetime]]: Start and end dates
    """
    # Try to find date range patterns
    range_patterns = [
        r"from\s+(.+?)\s+to\s+(.+?)(?:\s|$|\.)",
        r"between\s+(.+?)\s+and\s+(.+?)(?:\s|$|\.)",
        r"(.+?)\s+(?:to|through|until|till)\s+(.+?)(?:\s|$|\.)",
    ]
    
    for pattern in range_patterns:
        match = re.search(pattern, text, re.IGNORECASE)
        if match:
            start_text, end_text = match.groups()
            start_date = dateparser.parse(start_text)
            end_date = dateparser.parse(end_text)
            return start_date, end_date
    
    # Try to find single date with time range
    time_range_patterns = [
        r"on\s+(.+?)\s+(?:from|between)\s+(.+?)\s+(?:to|and)\s+(.+?)(?:\s|$|\.)",
        r"(.+?)\s+(?:from|between)\s+(.+?)\s+(?:to|and)\s+(.+?)(?:\s|$|\.)",
    ]
    
    for pattern in time_range_patterns:
        match = re.search(pattern, text, re.IGNORECASE)
        if match:
            date_text, start_time_text, end_time_text = match.groups()
            
            # Parse the date
            base_date = dateparser.parse(date_text)
            if not base_date:
                continue
            
            # Parse start and end times
            start_time = dateparser.parse(start_time_text)
            end_time = dateparser.parse(end_time_text)
            
            if not start_time or not end_time:
                continue
            
            # Combine date and times
            start_date = datetime.combine(base_date.date(), start_time.time())
            end_date = datetime.combine(base_date.date(), end_time.time())
            
            return start_date, end_date
    
    # Try to find single date
    date_patterns = [
        r"on\s+(.+?)(?:\s|$|\.)",
        r"for\s+(.+?)(?:\s|$|\.)",
        r"at\s+(.+?)(?:\s|$|\.)",
    ]
    
    for pattern in date_patterns:
        match = re.search(pattern, text, re.IGNORECASE)
        if match:
            date_text = match.group(1)
            date = dateparser.parse(date_text)
            if date:
                # For a single date, set end time to end of day
                end_date = datetime.combine(date.date(), datetime.max.time())
                return date, end_date
    
    # Try to find any dates in the text
    found_dates = search_dates(text)
    if found_dates and len(found_dates) >= 1:
        if len(found_dates) >= 2:
            # If we found multiple dates, use the first two as start and end
            start_date = found_dates[0][1]
            end_date = found_dates[1][1]
            return start_date, end_date
        else:
            # If we found only one date, use it as start and set end to end of day
            start_date = found_dates[0][1]
            end_date = datetime.combine(start_date.date(), datetime.max.time())
            return start_date, end_date
    
    # If all else fails, return None
    return None, None

async def parse_date(state: Dict[str, Any], config: RunnableConfig) -> Dict[str, Any]:
    """
    Parse natural language dates into standardized formats.
    
    This node extracts date information from user input and converts it to ISO format.
    
    Args:
        state: Agent state
        config: Runnable configuration
        
    Returns:
        Dict[str, Any]: Updated state with parsed dates
    """
    # Get the user input
    messages = state.get("messages", [])
    
    # Find the last human message
    user_input = ""
    for message in reversed(messages):
        if isinstance(message, HumanMessage) or (
            hasattr(message, "type") and message.type == "human"
        ):
            user_input = message.content
            break
    
    if not user_input:
        return state
    
    try:
        # Try to extract date range using regex and dateparser
        start_date, end_date = extract_date_range(user_input)
        
        # If that fails, use LLM to extract dates
        if not start_date or not end_date:
            # Get the LLM client
            llm_client = VoyageClient()
            
            # Extract date information using LLM
            prompt_template = (
                "You are a calendar management assistant. Your job is to help "
                "users parse dates and times.\n"
                "\n"
                "Extract the date and time information from the user's request. "
                "If a specific time is not mentioned, use 9:00 AM for start time "
                "and 5:00 PM for end time.\n"
                "\n"
                "User request: {input}\n"
                "\n"
                "Provide the extracted information in the following JSON format:\n"
                "```json\n"
                "{\n"
                "  \"start_time\": \"2023-06-01T09:00:00\",\n"
                "  \"end_time\": \"2023-06-01T17:00:00\"\n"
                "}\n"
                "```\n"
                "\n"
                "JSON:"
            )
            
            # Format the prompt
            prompt = prompt_template.format(input=user_input)
            
            # Call the LLM
            response = await llm_client.invoke(prompt)
            
            # Parse the response
            response_text = response.strip()
            
            # Extract JSON from the response
            if "```json" in response_text:
                json_str = response_text.split("```json")[1].split("```")[0].strip()
            elif "```" in response_text:
                json_str = response_text.split("```")[1].strip()
            else:
                json_str = response_text
            
            import json
            date_data = json.loads(json_str)
            
            # Convert to datetime objects
            start_date = dateparser.parse(date_data.get("start_time"))
            end_date = dateparser.parse(date_data.get("end_time"))
        
        # Update the state with parsed dates
        if start_date and end_date:
            return {
                **state,
                "parsed_dates": {
                    "start_time": start_date.isoformat(),
                    "end_time": end_date.isoformat()
                }
            }
    except Exception as e:
        logger.error(f"Error parsing dates: {str(e)}")
    
    # Return original state if parsing fails
    return state
