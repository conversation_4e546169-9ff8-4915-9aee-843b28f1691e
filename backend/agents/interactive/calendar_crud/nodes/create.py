"""
Create Event Node

This module provides a node for creating calendar events.
It extracts event details from user input and creates a new event.
"""

import logging
import json
from typing import Dict, Any, Optional, List, cast
from datetime import datetime
from langchain_core.runnables import RunnableConfig
from langchain_core.messages import HumanMessage, AIMessage

from pi_lawyer.agents.base_agent import BaseAgent
from shared.core.llm.voyage import VoyageClient
from shared.core.tools.executor import get_tool_executor

from backend.agents.interactive.calendar_crud.providers.factory import (
    get_provider_instance,
)
from backend.agents.interactive.calendar_crud.providers.models import (
    CalendarEventCreate,
    Attendee,
)
from backend.agents.interactive.calendar_crud.providers.exceptions import (
    UnsupportedOperationError,
)
from backend.agents.interactive.calendar_crud.utils.conflict import detect_conflicts

# Set up logging
logger = logging.getLogger(__name__)


class CreateEventNode(BaseAgent):
    """
    Node for creating calendar events.

    This node extracts event details from user input and creates a new event.
    """

    def __init__(
        self,
        agent_name: str = "calendarCrudAgent",
        node_name: str = "create_event",
    ):
        """
        Initialize the create event node.

        Args:
            agent_name: Name of the agent
            node_name: Name of the node
        """
        super().__init__(agent_name=agent_name, node_name=node_name)

    async def initialize(
        self, state: Dict[str, Any], config: RunnableConfig
    ) -> Dict[str, Any]:
        """Initialize the node."""
        return state

    async def cleanup(
        self, state: Dict[str, Any], config: RunnableConfig
    ) -> Dict[str, Any]:
        """Clean up the node."""
        return state

    async def execute(
        self, state: Dict[str, Any], config: RunnableConfig
    ) -> Dict[str, Any]:
        """
        Create a new calendar event.

        This node extracts event details from user input and creates a new event.

        Args:
            state: Agent state
            config: Runnable configuration

        Returns:
            Dict[str, Any]: Updated state
        """
        # Get the user input
        messages = state.get("messages", [])

        # Find the last human message
        user_input = ""
        for message in reversed(messages):
            if isinstance(message, HumanMessage) or (
                hasattr(message, "type") and message.type == "human"
            ):
                user_input = message.content
                break

        if not user_input:
            return {
                "messages": messages
                + [
                    AIMessage(
                        content="I couldn't understand your request. Please provide "
                        "details for the event you want to create."
                    )
                ]
            }

        # Get the tenant ID and user ID from the state
        tenant_id = state.get("tenant_id")
        user_id = state.get("user_id")

        if not tenant_id or not user_id:
            return {
                "messages": messages
                + [
                    AIMessage(
                        content="Error: Missing tenant ID or user ID. Please try "
                        "again later."
                    )
                ]
            }

        # Get the LLM client
        llm_client = VoyageClient()

        # Extract event details using LLM
        prompt_template = (
            "You are a calendar management assistant. Your job is to help "
            "users create new calendar events.\n"
            "\n"
            "Extract the following information from the user's request:\n"
            "- Event title/summary\n"
            "- Event description\n"
            "- Start date and time\n"
            "- End date and time\n"
            "- Location (if provided)\n"
            "- Attendees (if provided)\n"
            "- Calendar ID (if provided, otherwise use \"primary\")\n"
            "- Provider (if provided, otherwise use \"google\")\n"
            "\n"
            "User request: {input}\n"
            "\n"
            "Provide the extracted information in the following JSON format:\n"
            "```json\n"
            "{{\n"
            "  \"summary\": \"Meeting with Client\",\n"
            "  \"description\": \"Discuss project requirements\",\n"
            "  \"start_time\": \"2023-06-01T09:00:00\",\n"
            "  \"end_time\": \"2023-06-01T10:00:00\",\n"
            "  \"location\": \"Office\",\n"
            "  \"attendees\": [\"<EMAIL>\"],\n"
            "  \"calendar_id\": \"primary\",\n"
            "  \"provider\": \"google\"\n"
            "}}\n"
            "```\n"
            "\n"
            "JSON:"
        )

        # Format the prompt
        prompt = prompt_template.format(input=user_input)

        try:
            # Call the LLM
            response = await llm_client.invoke(prompt)

            # Parse the response
            response_text = response.strip()

            # Extract JSON from the response
            if "```json" in response_text:
                json_str = (
                    response_text.split("```json")[1].split("```")[0].strip()
                )
            elif "```" in response_text:
                json_str = response_text.split("```")[1].strip()
            else:
                json_str = response_text

            event_data = json.loads(json_str)

            # Get the provider
            provider = event_data.get("provider", "google")

            # Get the provider instance
            provider_instance = get_provider_instance(provider, tenant_id)

            # Check if the provider supports creating events
            supports_create = False
            try:
                supports_create = await provider_instance.supports("create_event")
            except:
                # If it's not an async method, try the sync version
                supports_create = provider_instance.supports("create_event")

            if not supports_create:
                # For Calendly, generate a scheduling link instead
                if provider.lower() == "calendly":
                    scheduling_link = await provider_instance.get_scheduling_link()

                    response_message = (
                        "I've generated a Calendly scheduling link for you:\n"

                        f"\n{scheduling_link}\n"
                        "\n"
                        "You can share this link with attendees to schedule a "
                        "meeting based on your availability."
                    )

                    return {
                        "messages": messages + [AIMessage(content=response_message)],
                        "scheduling_link": scheduling_link
                    }
                else:
                    return {
                        "messages": messages
                        + [
                            AIMessage(
                                content=f"The {provider} provider does not support "
                                "creating events."
                            )
                        ]
                    }

            # Create attendees list
            attendees = [
                Attendee(email=email) for email in event_data.get("attendees", [])
            ]

            # Create the event object
            calendar_id = event_data.get("calendar_id", "primary")
            event_obj = CalendarEventCreate(
                summary=event_data.get("summary", "New Event"),
                description=event_data.get("description", ""),
                start_time=event_data.get("start_time"),
                end_time=event_data.get("end_time"),
                location=event_data.get("location", ""),
                attendees=attendees,
            )

            # Check for conflicts
            conflict_result = await detect_conflicts(
                tenant_id=tenant_id,
                provider=provider,
                calendar_id=calendar_id,
                event=event_obj,
                buffer_minutes=0  # No buffer for now
            )

            # If conflicts were found, warn the user
            if conflict_result["has_conflicts"]:
                conflict_warning = (
                    "⚠️ Warning: This event conflicts with existing events:\n"

                    f"\n{conflict_result['message']}\n"
                    "\n"
                    "I've created the event anyway, but you may want to reschedule "
                    "to avoid conflicts."
                )
            else:
                conflict_warning = ""

            # Create the event
            event = await provider_instance.create_event(
                calendar_id=calendar_id,
                event=event_obj
            )

            # Format the response
            response_message = f"""Event created successfully!

Summary: {event.summary}
Description: {event.description}
Start: {event.start_time}
End: {event.end_time}
Location: {event.location or "Not specified"}
"""

            if event.attendees:
                response_message += "\nAttendees:\n"
                for attendee in event.attendees:
                    response_message += f"- {attendee.email}\n"

            if hasattr(event, "provider_event_link") and event.provider_event_link:
                response_message += f"\nEvent Link: {event.provider_event_link}"

            # Add conflict warning if conflicts were found
            if conflict_result["has_conflicts"]:
                response_message += f"\n\n{conflict_warning}"

            # Convert event to dict and ensure provider is included
            event_dict = event.model_dump() if hasattr(event, "model_dump") else (
                event.dict() if hasattr(event, "dict") else event
            )

            # Make sure provider is included in the event dict
            if "provider" not in event_dict and hasattr(event, "provider"):
                event_dict["provider"] = event.provider

            return {
                "messages": messages + [AIMessage(content=response_message)],
                "event": event_dict
            }
        except UnsupportedOperationError:
            return {
                "messages": messages
                + [
                    AIMessage(
                        content="This calendar provider does not support creating "
                        "events directly."
                    )
                ]
            }
        except Exception as e:
            logger.error(f"Error creating event: {str(e)}")
            return {
                "messages": messages
                + [AIMessage(content=f"Error creating event: {str(e)}")]
            }


# Create a function wrapper for backward compatibility
async def create_event(
    state: Dict[str, Any], config: RunnableConfig
) -> Dict[str, Any]:
    """
    Function wrapper for the CreateEventNode class.

    This maintains backward compatibility with existing code that calls
    create_event as a function.
    """
    node = CreateEventNode()
    return await node.execute(state, config)
