"""
Delete Event Node

This module provides a node for deleting calendar events.
It extracts event details from user input and deletes an existing event.
"""

import logging
import json
from typing import Dict, Any, Optional, List, cast
from datetime import datetime
from langchain_core.runnables import RunnableConfig
from langchain_core.messages import HumanMessage, AIMessage

from pi_lawyer.agents.base_agent import BaseAgent
from shared.core.llm.voyage import VoyageClient
from shared.core.tools.executor import get_tool_executor

from backend.agents.interactive.calendar_crud.providers.factory import (
    get_provider_instance,
)
from backend.agents.interactive.calendar_crud.providers.exceptions import (
    UnsupportedOperationError,
)

# Set up logging
logger = logging.getLogger(__name__)


class DeleteEventNode(BaseAgent):
    """
    Node for deleting calendar events.

    This node extracts event details from user input and deletes an existing event.
    """

    def __init__(
        self,
        agent_name: str = "calendarCrudAgent",
        node_name: str = "delete_event",
    ):
        """
        Initialize the delete event node.

        Args:
            agent_name: Name of the agent
            node_name: Name of the node
        """
        super().__init__(agent_name=agent_name, node_name=node_name)

    async def initialize(
        self, state: Dict[str, Any], config: RunnableConfig
    ) -> Dict[str, Any]:
        """Initialize the node."""
        return state

    async def cleanup(
        self, state: Dict[str, Any], config: RunnableConfig
    ) -> Dict[str, Any]:
        """Clean up the node."""
        return state

    async def execute(
        self, state: Dict[str, Any], config: RunnableConfig
    ) -> Dict[str, Any]:
        """
        Delete an existing calendar event.

        This node extracts event details from user input and deletes an existing event.

        Args:
            state: Agent state
            config: Runnable configuration

        Returns:
            Dict[str, Any]: Updated state
        """
        # Get the user input
        messages = state.get("messages", [])

        # Find the last human message
        user_input = ""
        for message in reversed(messages):
            if isinstance(message, HumanMessage) or (
                hasattr(message, "type") and message.type == "human"
            ):
                user_input = message.content
                break

        if not user_input:
            return {
                "messages": messages
                + [
                    AIMessage(
                        content="I couldn't understand your request. Please provide "
                        "details for the event you want to delete."
                    )
                ]
            }

        # Get the tenant ID and user ID from the state
        tenant_id = state.get("tenant_id")
        user_id = state.get("user_id")

        if not tenant_id or not user_id:
            return {
                "messages": messages
                + [
                    AIMessage(
                        content="Error: Missing tenant ID or user ID. Please try "
                        "again later."
                    )
                ]
            }

        # Get the LLM client
        llm_client = VoyageClient()

        # Extract event details using LLM
        prompt_template = (
            "You are a calendar management assistant. Your job is to help "
            "users delete calendar events.\n"
            "\n"
            "Extract the following information from the user's request:\n"
            "- Event ID\n"
            "- Calendar ID (if provided, otherwise use \"primary\")\n"
            "- Provider (if provided, otherwise use \"google\")\n"
            "\n"
            "User request: {input}\n"
            "\n"
            "Provide the extracted information in the following JSON format:\n"
            "```json\n"
            "{{\n"
            "  \"event_id\": \"abc123\",\n"
            "  \"calendar_id\": \"primary\",\n"
            "  \"provider\": \"google\"\n"
            "}}\n"
            "```\n"
            "\n"
            "JSON:"
        )

        # Format the prompt
        prompt = prompt_template.format(input=user_input)

        try:
            # Call the LLM
            response = await llm_client.invoke(prompt)

            # Parse the response
            response_text = response.strip()

            # Extract JSON from the response
            if "```json" in response_text:
                json_str = response_text.split("```json")[1].split("```")[0].strip()
            elif "```" in response_text:
                json_str = response_text.split("```")[1].strip()
            else:
                json_str = response_text

            event_data = json.loads(json_str)

            # Get the provider
            provider = event_data.get("provider", "google")

            # Get the provider instance
            provider_instance = get_provider_instance(provider, tenant_id)

            # Check if the provider supports deleting events
            supports_delete = False
            try:
                supports_delete = await provider_instance.supports("delete_event")
            except:
                # If it's not an async method, try the sync version
                supports_delete = provider_instance.supports("delete_event")

            if not supports_delete:
                # Special message for Calendly
                if provider.lower() == "calendly":
                    return {
                        "messages": messages
                        + [
                            AIMessage(
                                content=(
                                    "Calendly events can only be cancelled via "
                                    "link. Deletion is not supported through the "
                                    "Calendly API."
                                )
                            )
                        ]
                    }
                else:
                    return {
                        "messages": messages
                        + [
                            AIMessage(
                                content=f"The {provider} provider does not support "
                                "deleting events."
                            )
                        ]
                    }

            # Delete the event
            success = await provider_instance.delete_event(
                calendar_id=event_data.get("calendar_id", "primary"),
                event_id=event_data.get("event_id")
            )

            # Format the response
            if success:
                response_message = "Event deleted successfully!"
            else:
                response_message = (
                    "Failed to delete the event. Please check the event ID and "
                    "try again."
                )

            return {
                "messages": messages + [AIMessage(content=response_message)],
                "success": success
            }
        except UnsupportedOperationError:
            # Special message for Calendly
            if "calendly" in user_input.lower():
                return {
                    "messages": messages
                    + [
                        AIMessage(
                            content="Calendly events can only be cancelled via link. "
                            "Deletion is not supported through the Calendly API."
                        )
                    ]
                }
            else:
                return {
                    "messages": messages
                    + [
                        AIMessage(
                            content="This calendar provider does not support "
                            "deleting events."
                        )
                    ]
                }
        except Exception as e:
            logger.error(f"Error deleting event: {str(e)}")
            return {
                "messages": messages
                + [AIMessage(content=f"Error deleting event: {str(e)}")]
            }


# Create a function wrapper for backward compatibility
async def delete_event(
    state: Dict[str, Any], config: RunnableConfig
) -> Dict[str, Any]:
    """
    Function wrapper for the DeleteEventNode class.

    This maintains backward compatibility with existing code that calls
    delete_event as a function.
    """
    node = DeleteEventNode()
    return await node.execute(state, config)
