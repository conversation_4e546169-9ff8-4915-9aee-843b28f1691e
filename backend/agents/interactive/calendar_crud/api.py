"""
Main API module for Calendar CRUD.

This module initializes the FastAPI application and registers all routes
and middleware for the Calendar CRUD API.
"""

import logging
from typing import List, Optional

from fastapi import Fast<PERSON><PERSON>, Request
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse

try:
    from .middleware.auth_middleware import AuthMiddleware
except ImportError:
    # For testing, use the mock implementation
    from .tests.mocks.auth_middleware import MockAuthMiddleware as AuthMiddleware
from .middleware.error_middleware import ErrorMiddleware

# Configure logging
logger = logging.getLogger(__name__)


def create_app() -> FastAPI:
    """
    Create and configure the FastAPI application.

    Returns:
        The configured FastAPI application
    """
    # Create FastAPI app
    app = FastAPI(
        title="Calendar CRUD API",
        description="API for managing calendar events across multiple providers",
        version="1.0.0",
        docs_url="/calendar/docs",
        redoc_url="/calendar/redoc",
        openapi_url="/calendar/openapi.json",
    )

    # Add CORS middleware
    app.add_middleware(
        CORSMiddleware,
        allow_origins=["*"],  # In production, this should be restricted
        allow_credentials=True,
        allow_methods=["*"],
        allow_headers=["*"],
    )

    # Add error handling middleware
    app.add_middleware(ErrorMiddleware)

    # Add authentication middleware
    app.add_middleware(AuthMiddleware)

    # Add global exception handler
    @app.exception_handler(Exception)
    async def global_exception_handler(
        request: Request, exc: Exception
    ) -> JSONResponse:
        """Handle any unhandled exceptions."""
        logger.exception(f"Unhandled exception: {str(exc)}")
        return JSONResponse(
            status_code=500,
            content={"detail": "Internal server error"},
        )

    # Register routes
    from .routes.auth import auth_router
    from .routes.providers import providers_router
    from .routes.calendars import calendars_router
    from .routes.events import events_router
    from .routes.scheduling import scheduling_router
    from .routes.webhooks import webhooks_router

    app.include_router(auth_router.router)
    app.include_router(providers_router.router)
    app.include_router(calendars_router.router)
    app.include_router(events_router.router)
    app.include_router(scheduling_router.router)
    app.include_router(webhooks_router.router)

    return app


# Create the FastAPI application
app = create_app()
