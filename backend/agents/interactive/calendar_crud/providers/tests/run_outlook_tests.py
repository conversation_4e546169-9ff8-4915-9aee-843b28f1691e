#!/usr/bin/env python3
"""
Simple test runner for Outlook provider tests.

This script runs the Outlook provider tests without relying on pyte<PERSON>'s conftest system.
"""

import sys
import asyncio
import unittest.mock as mock
from datetime import datetime, timed<PERSON>ta
from typing import Dict, Any, List

# Add the project root to the path
sys.path.insert(0, '/mnt/persist/workspace')

from backend.agents.interactive.calendar_crud.providers.outlook.provider import (
    OutlookProvider,
)
from backend.agents.interactive.calendar_crud.providers.outlook.client import (
    OutlookClient,
)
from backend.agents.interactive.calendar_crud.providers.models import (
    CalendarEvent,
    CalendarEventCreate,
    CalendarEventUpdate,
    FreeBusyRequest,
    FreeBusyResponse,
    TimeSlot,
    Attendee,
    EventStatus
)
from backend.agents.interactive.calendar_crud.providers.exceptions import (
    CalendarProviderError,
    AuthenticationError,
    ResourceNotFoundError,
    PermissionError,
    RateLimitError,
    ValidationError
)
from backend.agents.interactive.calendar_crud.providers.base import ProviderCapability


class OutlookProviderTests:
    """Test cases for the OutlookProvider class."""

    def __init__(self):
        self.passed = 0
        self.failed = 0
        self.errors = []

    def assert_equal(self, actual, expected, message=""):
        if actual != expected:
            error_msg = f"AssertionError: {actual} != {expected}. {message}"
            self.errors.append(error_msg)
            self.failed += 1
            return False
        self.passed += 1
        return True

    def assert_true(self, condition, message=""):
        if not condition:
            error_msg = f"AssertionError: Expected True. {message}"
            self.errors.append(error_msg)
            self.failed += 1
            return False
        self.passed += 1
        return True

    def assert_in(self, item, container, message=""):
        if item not in container:
            error_msg = f"AssertionError: {item} not in {container}. {message}"
            self.errors.append(error_msg)
            self.failed += 1
            return False
        self.passed += 1
        return True

    def assert_raises(self, exception_type, func, *args, **kwargs):
        try:
            if asyncio.iscoroutinefunction(func):
                asyncio.run(func(*args, **kwargs))
            else:
                func(*args, **kwargs)
            error_msg = (
                f"AssertionError: Expected {exception_type.__name__} to be raised"
            )
            self.errors.append(error_msg)
            self.failed += 1
            return False
        except exception_type:
            self.passed += 1
            return True
        except Exception as e:
            error_msg = (
                f"AssertionError: Expected {exception_type.__name__}, got "
                f"{type(e).__name__}: {e}"
            )
            self.errors.append(error_msg)
            self.failed += 1
            return False

    async def assert_raises_async(self, exception_type, func, *args, **kwargs):
        try:
            await func(*args, **kwargs)
            error_msg = (
                f"AssertionError: Expected {exception_type.__name__} to be raised"
            )
            self.errors.append(error_msg)
            self.failed += 1
            return False
        except exception_type:
            self.passed += 1
            return True
        except Exception as e:
            error_msg = (
                f"AssertionError: Expected {exception_type.__name__}, got "
                f"{type(e).__name__}: {e}"
            )
            self.errors.append(error_msg)
            self.failed += 1
            return False

    def test_provider_properties(self):
        """Test provider basic properties."""
        provider = OutlookProvider()
        self.assert_equal(provider.provider_id, "outlook")
        self.assert_equal(provider.provider_name, "Microsoft Outlook")

        # Check capabilities
        expected_capabilities = [
            ProviderCapability.CREATE_EVENT,
            ProviderCapability.READ_EVENT,
            ProviderCapability.UPDATE_EVENT,
            ProviderCapability.DELETE_EVENT,
            ProviderCapability.FREE_BUSY,
            ProviderCapability.LIST_CALENDARS,
            ProviderCapability.WEBHOOK_SUPPORT
        ]
        self.assert_equal(provider.capabilities, expected_capabilities)
        print("✓ test_provider_properties passed")

    async def test_get_calendars_success(self):
        """Test successful calendar list retrieval."""
        provider = OutlookProvider()
        mock_client = mock.AsyncMock(spec=OutlookClient)

        # Mock the client response
        mock_response = {
            "value": [
                {
                    "id": "calendar_1",
                    "name": "Primary Calendar",
                    "description": "Main calendar",
                    "isDefaultCalendar": True,
                    "canEdit": True,
                    "canShare": True,
                    "canViewPrivateItems": True,
                    "color": "blue"
                }
            ]
        }

        mock_client.get_calendars.return_value = mock_response
        provider.client = mock_client

        # Test the method
        result = await provider.get_calendars("test_firm")

        # Verify the result
        self.assert_equal(len(result), 1)
        self.assert_equal(result[0]["id"], "calendar_1")
        self.assert_equal(result[0]["name"], "Primary Calendar")
        self.assert_true(result[0]["primary"])
        self.assert_equal(result[0]["access_role"], "owner")

        # Verify client was called correctly
        mock_client.get_calendars.assert_called_once_with("test_firm")
        print("✓ test_get_calendars_success passed")

    async def test_get_calendars_validation_error(self):
        """Test get_calendars with invalid parameters."""
        provider = OutlookProvider()
        await self.assert_raises_async(ValidationError, provider.get_calendars, "")
        print("✓ test_get_calendars_validation_error passed")

    async def test_create_event_success(self):
        """Test successful event creation."""
        provider = OutlookProvider()
        mock_client = mock.AsyncMock(spec=OutlookClient)

        sample_outlook_event = {
            "id": "outlook_event_123",
            "subject": "Test Meeting",
            "body": {
                "contentType": "text",
                "content": "This is a test meeting"
            },
            "start": {
                "dateTime": "2024-01-15T10:00:00Z",
                "timeZone": "UTC"
            },
            "end": {
                "dateTime": "2024-01-15T11:00:00Z",
                "timeZone": "UTC"
            },
            "location": {
                "displayName": "Conference Room A"
            },
            "attendees": [],
            "organizer": {
                "emailAddress": {
                    "address": "<EMAIL>",
                    "name": "Jane Smith"
                }
            },
            "webLink": "https://outlook.office365.com/calendar/item/123",
            "createdDateTime": "2024-01-10T09:00:00Z",
            "lastModifiedDateTime": "2024-01-10T09:30:00Z",
            "showAs": "busy"
        }

        sample_calendar_event_create = CalendarEventCreate(
            summary="Test Event",
            description="Test event description",
            location="Test Location",
            start_time=datetime(2024, 1, 15, 10, 0, 0),
            end_time=datetime(2024, 1, 15, 11, 0, 0),
            attendees=[
                Attendee(email="<EMAIL>", name="Test User")
            ],
            status=EventStatus.CONFIRMED
        )

        # Mock the client response
        mock_client.create_event.return_value = sample_outlook_event
        provider.client = mock_client

        # Test the method
        result = await provider.create_event(
            "test_firm", "calendar_1", sample_calendar_event_create
        )

        # Verify the result
        self.assert_equal(result.id, "outlook_event_123")
        self.assert_equal(result.summary, "Test Meeting")
        self.assert_equal(result.calendar_id, "calendar_1")
        self.assert_equal(result.provider_id, "outlook")

        # Verify client was called correctly
        mock_client.create_event.assert_called_once()
        print("✓ test_create_event_success passed")

    async def test_create_event_validation_errors(self):
        """Test create_event with various validation errors."""
        provider = OutlookProvider()
        valid_event = CalendarEventCreate(
            summary="Test Event",
            start_time=datetime(2024, 1, 15, 10, 0, 0),
            end_time=datetime(2024, 1, 15, 11, 0, 0)
        )

        # Test missing firm_id
        await self.assert_raises_async(
            ValidationError, provider.create_event, "", "calendar_1", valid_event
        )

        # Test missing calendar_id
        await self.assert_raises_async(
            ValidationError, provider.create_event, "test_firm", "", valid_event
        )

        # Test missing event
        await self.assert_raises_async(
            ValidationError, provider.create_event, "test_firm", "calendar_1", None
        )

        # Test event without summary
        invalid_event = CalendarEventCreate(
            summary="",
            start_time=datetime(2024, 1, 15, 10, 0, 0),
            end_time=datetime(2024, 1, 15, 11, 0, 0)
        )
        await self.assert_raises_async(
            ValidationError,
            provider.create_event,
            "test_firm",
            "calendar_1",
            invalid_event,
        )
        print("✓ test_create_event_validation_errors passed")

    async def test_check_availability_success(self):
        """Test successful availability checking."""
        provider = OutlookProvider()
        mock_client = mock.AsyncMock(spec=OutlookClient)

        # Mock the client response
        mock_response = {
            "value": [
                {
                    "schedules": ["calendar_1"],
                    "availabilityView": [0, 0, 2, 2, 0],  # Free, Free, Busy, Busy, Free
                    "workingHours": {
                        "daysOfWeek": [
                            "monday",
                            "tuesday",
                            "wednesday",
                            "thursday",
                            "friday",
                        ],
                        "startTime": "08:00:00.0000000",
                        "endTime": "17:00:00.0000000"
                    }
                }
            ]
        }

        mock_client.get_schedule.return_value = mock_response
        provider.client = mock_client

        # Test the method
        start_time = datetime(2024, 1, 15, 8, 0, 0)
        end_time = datetime(2024, 1, 15, 17, 0, 0)
        result = await provider.check_availability(
            "test_firm",
            ["calendar_1"],
            start_time,
            end_time
        )

        # Verify the result
        self.assert_true(isinstance(result, FreeBusyResponse))
        self.assert_equal(result.time_min, start_time)
        self.assert_equal(result.time_max, end_time)
        self.assert_in("calendar_1", result.calendars)

        # Verify client was called correctly
        mock_client.get_schedule.assert_called_once_with(
            firm_id="test_firm",
            calendar_ids=["calendar_1"],
            start_time=start_time.isoformat(),
            end_time=end_time.isoformat(),
            timezone="UTC"
        )
        print("✓ test_check_availability_success passed")

    async def test_check_free_busy_validation_errors(self):
        """Test check_free_busy with various validation errors."""
        provider = OutlookProvider()
        valid_request = FreeBusyRequest(
            calendar_ids=["calendar_1"],
            start_time=datetime(2024, 1, 15, 8, 0, 0),
            end_time=datetime(2024, 1, 15, 17, 0, 0)
        )

        # Test missing firm_id
        await self.assert_raises_async(
            ValidationError, provider.check_free_busy, "", valid_request
        )

        # Test missing request
        await self.assert_raises_async(
            ValidationError, provider.check_free_busy, "test_firm", None
        )

        # Test empty calendar_ids - this will be caught by the provider validation
        # since Pydantic already validates this at the model level
        try:
            invalid_request = FreeBusyRequest(
                calendar_ids=[],
                start_time=datetime(2024, 1, 15, 8, 0, 0),
                end_time=datetime(2024, 1, 15, 17, 0, 0)
            )
            # If we get here, the model validation didn't catch it, so test the provider
            await self.assert_raises_async(
                ValidationError, provider.check_free_busy, "test_firm", invalid_request
            )
        except Exception:
            # Pydantic caught the validation error, which is expected
            self.passed += 1
        print("✓ test_check_free_busy_validation_errors passed")

    async def run_all_tests(self):
        """Run all tests."""
        print("Running Outlook Provider Tests...")
        print("=" * 50)

        # Run synchronous tests
        self.test_provider_properties()

        # Run asynchronous tests
        await self.test_get_calendars_success()
        await self.test_get_calendars_validation_error()
        await self.test_create_event_success()
        await self.test_create_event_validation_errors()
        await self.test_check_availability_success()
        await self.test_check_free_busy_validation_errors()

        print("=" * 50)
        print(f"Tests completed: {self.passed} passed, {self.failed} failed")

        if self.errors:
            print("\nErrors:")
            for error in self.errors:
                print(f"  - {error}")

        return self.failed == 0


async def main():
    """Main test runner."""
    tests = OutlookProviderTests()
    success = await tests.run_all_tests()

    if success:
        print("\n🎉 All tests passed!")
        return 0
    else:
        print("\n❌ Some tests failed!")
        return 1


if __name__ == "__main__":
    exit_code = asyncio.run(main())
    sys.exit(exit_code)
