"""
Capability utilities for calendar providers.

This module provides utility functions for working with provider capabilities,
including loading the capability matrix, checking capabilities, and handling
capability-aware operations.
"""

import os
import json
import logging
from typing import Dict, Any, List, Optional, Set, Union

from .base import CalendarProvider, ProviderCapability
from .exceptions import UnsupportedOperationError, ConfigurationError

# Configure logging
logger = logging.getLogger(__name__)

# Path to the capability matrix JSON file
CAPABILITY_MATRIX_PATH = os.path.join(os.path.dirname(__file__), "capabilities.json")


def load_capability_matrix() -> Dict[str, Any]:
    """
    Load the provider capability matrix from the JSON file.
    
    Returns:
        Dict[str, Any]: The provider capability matrix
        
    Raises:
        ConfigurationError: If the capability matrix file is not found or is invalid
    """
    try:
        with open(CAPABILITY_MATRIX_PATH, "r") as f:
            return json.load(f)
    except FileNotFoundError:
        logger.error(f"Capability matrix file not found: {CAPABILITY_MATRIX_PATH}")
        raise ConfigurationError(
            f"Capability matrix file not found: {CAPABILITY_MATRIX_PATH}"
        )
    except json.JSONDecodeError as e:
        logger.error(f"Invalid capability matrix file: {str(e)}")
        raise ConfigurationError(f"Invalid capability matrix file: {str(e)}")


def get_provider_capabilities(provider_id: str) -> Dict[str, bool]:
    """
    Get the capabilities of a provider from the capability matrix.
    
    Args:
        provider_id: The provider ID
        
    Returns:
        Dict[str, bool]: Dictionary mapping capability names to boolean values
        
    Raises:
        ConfigurationError: If the provider is not found in the capability matrix
    """
    matrix = load_capability_matrix()
    
    if provider_id not in matrix:
        logger.error(f"Provider not found in capability matrix: {provider_id}")
        raise ConfigurationError(
            f"Provider not found in capability matrix: {provider_id}"
        )
    
    return matrix[provider_id]["capabilities"]


def check_capability(
    provider: Union[CalendarProvider, str], 
    capability: Union[ProviderCapability, str]
) -> bool:
    """
    Check if a provider supports a capability.
    
    This function can be called with either a provider instance or a provider ID,
    and either a ProviderCapability enum value or a capability name string.
    
    Args:
        provider: The provider instance or provider ID
        capability: The capability to check
        
    Returns:
        bool: True if the provider supports the capability, False otherwise
    """
    # Convert provider to provider ID if it's a provider instance
    provider_id = (
        provider.provider_id
        if isinstance(provider, CalendarProvider)
        else provider
    )
    
    # Convert capability to string if it's a ProviderCapability enum
    capability_name = (
        capability.value
        if isinstance(capability, ProviderCapability)
        else capability
    )
    
    # Get provider capabilities from the matrix
    capabilities = get_provider_capabilities(provider_id)
    
    # Check if the capability is supported
    return capabilities.get(capability_name, False)


def require_capability(
    provider: Union[CalendarProvider, str], 
    capability: Union[ProviderCapability, str]
) -> None:
    """
    Require that a provider supports a capability.
    
    This function raises an UnsupportedOperationError if the provider
    does not support the specified capability.
    
    Args:
        provider: The provider instance or provider ID
        capability: The capability to require
        
    Raises:
        UnsupportedOperationError: If the provider does not support the capability
    """
    # Get provider ID and name for error message
    if isinstance(provider, CalendarProvider):
        provider_id = provider.provider_id
        provider_name = provider.provider_name
    else:
        provider_id = provider
        matrix = load_capability_matrix()
        provider_name = matrix.get(provider_id, {}).get("name", provider_id)
    
    # Convert capability to string if it's a ProviderCapability enum
    capability_name = (
        capability.value
        if isinstance(capability, ProviderCapability)
        else capability
    )
    
    # Check if the capability is supported
    if not check_capability(provider_id, capability_name):
        logger.warning(
            f"Unsupported operation: {capability_name} is not supported by "
            f"{provider_name}"
        )
        raise UnsupportedOperationError(
            f"The operation '{capability_name}' is not supported by the "
            f"provider '{provider_name}'"
        )
