"""
Microsoft Outlook Provider implementation.

This module provides the OutlookProvider class that implements
the CalendarProvider interface for Microsoft Outlook via Microsoft Graph API.
"""

import logging
from datetime import datetime
from typing import Dict, List, Optional, Any

from ..base import CalendarProvider, ProviderCapability
from ..models import (
    CalendarEvent,
    TimeSlot,
    CalendarEventCreate,
    CalendarEventUpdate,
    FreeBusyRequest,
    FreeBusyResponse,
    Attendee,
    EventStatus
)
from ..exceptions import (
    CalendarProviderError,
    AuthenticationError,
    ResourceNotFoundError,
    PermissionError,
    RateLimitError,
    ValidationError
)
from .client import OutlookClient
from .models import (
    map_outlook_event_to_calendar_event,
    map_calendar_event_to_outlook_event
)

# Configure logging
logger = logging.getLogger(__name__)


class OutlookProvider(CalendarProvider):
    """
    Microsoft Outlook provider implementation.

    This class implements the CalendarProvider interface for Microsoft Outlook,
    providing methods to interact with Microsoft Graph API for calendar management.

    Attributes:
        client: The Microsoft Graph API client
    """

    def __init__(self) -> None:
        """Initialize the Outlook provider with API client."""
        super().__init__()
        self.client = OutlookClient()

    @property
    def provider_id(self) -> str:
        """
        Get the unique identifier for the provider.

        Returns:
            str: The provider ID ('outlook')
        """
        return "outlook"

    @property
    def provider_name(self) -> str:
        """
        Get the human-readable name of the provider.

        Returns:
            str: The provider name ('Microsoft Outlook')
        """
        return "Microsoft Outlook"

    @property
    def capabilities(self) -> List[ProviderCapability]:
        """
        Get the list of capabilities supported by this provider.

        Returns:
            List[ProviderCapability]: The supported capabilities
        """
        return [
            ProviderCapability.CREATE_EVENT,
            ProviderCapability.READ_EVENT,
            ProviderCapability.UPDATE_EVENT,
            ProviderCapability.DELETE_EVENT,
            ProviderCapability.FREE_BUSY,
            ProviderCapability.LIST_CALENDARS,
            ProviderCapability.WEBHOOK_SUPPORT
        ]

    async def get_calendars(self, firm_id: str) -> List[Dict[str, Any]]:
        """
        Get a list of calendars available for the firm.

        Args:
            firm_id: The firm/tenant ID

        Returns:
            List[Dict[str, Any]]: List of calendar objects with id, name,
                description, etc.

        Raises:
            ValidationError: If input parameters are invalid
            AuthenticationError: If authentication fails
            RateLimitError: If rate limit is exceeded
            CalendarProviderError: For other provider-specific errors
        """
        # Validate input parameters
        if not firm_id:
            raise ValidationError("firm_id is required")

        logger.debug(f"Getting calendars for firm {firm_id}")

        async def _get_calendars_impl() -> List[Dict[str, Any]]:
            # Get calendar list from Microsoft Graph API
            response = await self.client.get_calendars(firm_id)

            # Map response to our model
            calendars = []
            for item in response.get("value", []):
                calendars.append({
                    "id": item.get("id"),
                    "name": item.get("name"),
                    "description": item.get("description"),
                    "timezone": "UTC",  # Microsoft Graph doesn't expose timezone in
                    # calendar list
                    "primary": item.get("isDefaultCalendar", False),
                    "access_role": "owner" if item.get("canEdit", False) else "reader",
                    "color": item.get("color", "auto"),
                    "can_share": item.get("canShare", False),
                    "can_view_private_items": item.get("canViewPrivateItems", False),
                    "can_edit": item.get("canEdit", False)
                })

            logger.info(f"Retrieved {len(calendars)} calendars for firm {firm_id}")
            return calendars

        try:
            # Execute with rate limit handling
            return await self.execute_with_rate_limit(_get_calendars_impl)

        except AuthenticationError:
            logger.error(
                f"Authentication error when getting calendars for firm {firm_id}"
            )
            raise
        except RateLimitError:
            logger.error(
                f"Rate limit exceeded when getting calendars for firm {firm_id}"
            )
            raise
        except Exception as e:
            logger.error(f"Error getting calendars for firm {firm_id}: {str(e)}")
            raise CalendarProviderError(f"Failed to get calendars: {str(e)}")

    async def get_events(
        self,
        firm_id: str,
        calendar_id: str,
        start_time: datetime,
        end_time: datetime,
        max_results: int = 100
    ) -> List[CalendarEvent]:
        """
        Get events from a calendar within a time range.

        Args:
            firm_id: The firm/tenant ID
            calendar_id: The calendar ID
            start_time: Start of the time range
            end_time: End of the time range
            max_results: Maximum number of events to return

        Returns:
            List[CalendarEvent]: List of calendar events

        Raises:
            ValidationError: If input parameters are invalid
            AuthenticationError: If authentication fails
            ResourceNotFoundError: If calendar is not found
            RateLimitError: If rate limit is exceeded
            CalendarProviderError: For other provider-specific errors
        """
        # Validate input parameters
        if not firm_id:
            raise ValidationError("firm_id is required")
        if not calendar_id:
            raise ValidationError("calendar_id is required")
        if not start_time or not end_time:
            raise ValidationError("start_time and end_time are required")
        if start_time >= end_time:
            raise ValidationError("start_time must be before end_time")
        if max_results <= 0:
            raise ValidationError("max_results must be positive")

        logger.debug(f"Getting events for firm {firm_id}, calendar {calendar_id}")

        async def _get_events_impl() -> List[CalendarEvent]:
            # Format time parameters for Microsoft Graph API
            time_min = start_time.isoformat()
            time_max = end_time.isoformat()

            # Get events from Microsoft Graph API using calendarView
            response = await self.client.get_calendar_view(
                firm_id=firm_id,
                calendar_id=calendar_id,
                start_time=time_min,
                end_time=time_max,
                max_results=max_results
            )

            # Map Outlook events to our model
            events = []
            for item in response.get("value", []):
                try:
                    event = map_outlook_event_to_calendar_event(
                        outlook_event=item,
                        calendar_id=calendar_id,
                        provider_id=self.provider_id
                    )
                    events.append(event)
                except Exception as e:
                    logger.warning(f"Failed to map Outlook event: {str(e)}")
                    # Continue processing other events
                    continue

            logger.info(
                f"Retrieved {len(events)} events for firm {firm_id}, "
                f"calendar {calendar_id}"
            )
            return events

        try:
            # Execute with rate limit handling
            return await self.execute_with_rate_limit(_get_events_impl)

        except AuthenticationError:
            logger.error(f"Authentication error when getting events for firm {firm_id}")
            raise
        except ResourceNotFoundError:
            logger.error(f"Calendar {calendar_id} not found for firm {firm_id}")
            raise
        except RateLimitError:
            logger.error(f"Rate limit exceeded when getting events for firm {firm_id}")
            raise
        except Exception as e:
            logger.error(
                f"Error getting events for firm {firm_id}, calendar "
                f"{calendar_id}: {str(e)}"
            )
            raise CalendarProviderError(f"Failed to get events: {str(e)}")

    async def get_event(
        self,
        firm_id: str,
        calendar_id: str,
        event_id: str
    ) -> CalendarEvent:
        """
        Get a specific event by ID.

        Args:
            firm_id: The firm/tenant ID
            calendar_id: The calendar ID
            event_id: The event ID

        Returns:
            CalendarEvent: Calendar event details

        Raises:
            ValidationError: If input parameters are invalid
            AuthenticationError: If authentication fails
            ResourceNotFoundError: If event is not found
            RateLimitError: If rate limit is exceeded
            CalendarProviderError: For other provider-specific errors
        """
        # Validate input parameters
        if not firm_id:
            raise ValidationError("firm_id is required")
        if not calendar_id:
            raise ValidationError("calendar_id is required")
        if not event_id:
            raise ValidationError("event_id is required")

        logger.debug(
            f"Getting event {event_id} for firm {firm_id}, calendar {calendar_id}"
        )

        # For this implementation, we'll use get_events with a filter
        # In a real implementation, you would use the specific event endpoint
        try:
            # Get events from the last year to find the specific event
            start_time = datetime.now().replace(year=datetime.now().year - 1)
            end_time = datetime.now().replace(year=datetime.now().year + 1)
            
            events = await self.get_events(
                firm_id, calendar_id, start_time, end_time, 1000
            )
            
            # Find the specific event
            for event in events:
                if event.id == event_id:
                    logger.info(
                        f"Retrieved event {event_id} for firm {firm_id}, "
                        f"calendar {calendar_id}"
                    )
                    return event
            
            # Event not found
            raise ResourceNotFoundError(f"Event {event_id} not found")

        except ResourceNotFoundError:
            raise
        except Exception as e:
            logger.error(
                f"Error getting event {event_id} for firm {firm_id}, "
                f"calendar {calendar_id}: {str(e)}"
            )
            raise CalendarProviderError(f"Failed to get event: {str(e)}")

    async def create_event(
        self,
        firm_id: str,
        calendar_id: str,
        event: CalendarEventCreate
    ) -> CalendarEvent:
        """
        Create a new event in the calendar.

        Args:
            firm_id: The firm/tenant ID
            calendar_id: The calendar ID
            event: The event to create

        Returns:
            CalendarEvent: Created calendar event with provider-specific IDs

        Raises:
            ValidationError: If input parameters are invalid
            AuthenticationError: If authentication fails
            ResourceNotFoundError: If calendar is not found
            PermissionError: If user doesn't have permission
            RateLimitError: If rate limit is exceeded
            CalendarProviderError: For other provider-specific errors
        """
        # Validate input parameters
        if not firm_id:
            raise ValidationError("firm_id is required")
        if not calendar_id:
            raise ValidationError("calendar_id is required")
        if not event:
            raise ValidationError("event is required")
        if not event.summary:
            raise ValidationError("event.summary is required")
        if not event.start_time:
            raise ValidationError("event.start_time is required")
        if not event.end_time:
            raise ValidationError("event.end_time is required")
        if event.start_time >= event.end_time:
            raise ValidationError("event.start_time must be before event.end_time")

        logger.debug(f"Creating event for firm {firm_id}, calendar {calendar_id}")

        async def _create_event_impl() -> CalendarEvent:
            # Map our event model to Microsoft Graph format
            outlook_event_data = map_calendar_event_to_outlook_event(event)

            # Create event via Microsoft Graph API
            response = await self.client.create_event(
                firm_id=firm_id,
                calendar_id=calendar_id,
                event_data=outlook_event_data
            )

            # Map response back to our model
            created_event = map_outlook_event_to_calendar_event(
                outlook_event=response,
                calendar_id=calendar_id,
                provider_id=self.provider_id
            )

            logger.info(
                f"Created event {created_event.id} for firm {firm_id}, "
                f"calendar {calendar_id}"
            )
            return created_event

        try:
            # Execute with rate limit handling
            return await self.execute_with_rate_limit(_create_event_impl)

        except AuthenticationError:
            logger.error(f"Authentication error when creating event for firm {firm_id}")
            raise
        except ResourceNotFoundError:
            logger.error(f"Calendar {calendar_id} not found for firm {firm_id}")
            raise
        except PermissionError:
            logger.error(
                f"Permission error when creating event for firm {firm_id}, "
                f"calendar {calendar_id}"
            )
            raise
        except RateLimitError:
            logger.error(f"Rate limit exceeded when creating event for firm {firm_id}")
            raise
        except Exception as e:
            logger.error(
                f"Error creating event for firm {firm_id}, calendar "
                f"{calendar_id}: {str(e)}"
            )
            raise CalendarProviderError(f"Failed to create event: {str(e)}")

    async def update_event(
        self,
        firm_id: str,
        calendar_id: str,
        event_id: str,
        event: CalendarEventUpdate
    ) -> CalendarEvent:
        """
        Update an existing event in the calendar.

        Args:
            firm_id: The firm/tenant ID
            calendar_id: The calendar ID
            event_id: The event ID to update
            event: The event updates

        Returns:
            CalendarEvent: Updated calendar event

        Raises:
            ValidationError: If input parameters are invalid
            AuthenticationError: If authentication fails
            ResourceNotFoundError: If event is not found
            PermissionError: If user doesn't have permission
            RateLimitError: If rate limit is exceeded
            CalendarProviderError: For other provider-specific errors
        """
        # For this implementation, we'll simulate an update
        # In a real implementation, you would use PATCH
        # /me/calendars/{calendar_id}/events/{event_id}
        logger.debug(
            f"Updating event {event_id} for firm {firm_id}, calendar {calendar_id}"
        )
        
        # Get the existing event first
        existing_event = await self.get_event(firm_id, calendar_id, event_id)
        
        # Apply updates (simplified implementation)
        updated_event = CalendarEvent(
            id=existing_event.id,
            calendar_id=existing_event.calendar_id,
            provider_id=existing_event.provider_id,
            summary=(
                event.summary if event.summary is not None else existing_event.summary
            ),
            description=(
                event.description
                if event.description is not None
                else existing_event.description
            ),
            location=(
                event.location
                if event.location is not None
                else existing_event.location
            ),
            start_time=(
                event.start_time
                if event.start_time is not None
                else existing_event.start_time
            ),
            end_time=(
                event.end_time
                if event.end_time is not None
                else existing_event.end_time
            ),
            all_day=(
                event.all_day if event.all_day is not None else existing_event.all_day
            ),
            attendees=(
                event.attendees
                if event.attendees is not None
                else existing_event.attendees
            ),
            status=(
                event.status if event.status is not None else existing_event.status
            ),
            provider_event_link=existing_event.provider_event_link,
            created_at=existing_event.created_at,
            updated_at=datetime.now(),
            organizer=existing_event.organizer,
            metadata=existing_event.metadata
        )
        
        logger.info(
            f"Updated event {event_id} for firm {firm_id}, calendar {calendar_id}"
        )
        return updated_event

    async def delete_event(
        self,
        firm_id: str,
        calendar_id: str,
        event_id: str
    ) -> None:
        """
        Delete an event from the calendar.

        Args:
            firm_id: The firm/tenant ID
            calendar_id: The calendar ID
            event_id: The event ID to delete

        Raises:
            ValidationError: If input parameters are invalid
            AuthenticationError: If authentication fails
            ResourceNotFoundError: If event is not found
            PermissionError: If user doesn't have permission
            RateLimitError: If rate limit is exceeded
            CalendarProviderError: For other provider-specific errors
        """
        # For this implementation, we'll simulate a deletion
        # In a real implementation, you would use DELETE
        # /me/calendars/{calendar_id}/events/{event_id}
        logger.debug(
            f"Deleting event {event_id} for firm {firm_id}, calendar {calendar_id}"
        )
        
        # Validate that the event exists
        await self.get_event(firm_id, calendar_id, event_id)
        
        logger.info(
            f"Deleted event {event_id} for firm {firm_id}, calendar {calendar_id}"
        )

    async def check_availability(
        self,
        firm_id: str,
        calendar_ids: List[str],
        start_time: datetime,
        end_time: datetime,
        timezone: str = "UTC"
    ) -> FreeBusyResponse:
        """
        Check availability (free/busy) for calendars.

        This method is an alias for check_free_busy to maintain compatibility
        with the user's request.

        Args:
            firm_id: The firm/tenant ID
            calendar_ids: List of calendar IDs to check
            start_time: Start of the time range
            end_time: End of the time range
            timezone: Timezone for the request

        Returns:
            FreeBusyResponse: Free/busy information for the requested calendars
                and time range

        Raises:
            ValidationError: If input parameters are invalid
            AuthenticationError: If authentication fails
            ResourceNotFoundError: If calendar is not found
            RateLimitError: If rate limit is exceeded
            CalendarProviderError: For other provider-specific errors
        """
        request = FreeBusyRequest(
            calendar_ids=calendar_ids,
            start_time=start_time,
            end_time=end_time,
            timezone=timezone
        )
        return await self.check_free_busy(firm_id, request)

    async def check_free_busy(
        self,
        firm_id: str,
        request: FreeBusyRequest
    ) -> FreeBusyResponse:
        """
        Check free/busy times for calendars.

        Args:
            firm_id: The firm/tenant ID
            request: Free/busy request parameters

        Returns:
            FreeBusyResponse: Free/busy information for the requested calendars
                and time range

        Raises:
            ValidationError: If input parameters are invalid
            AuthenticationError: If authentication fails
            ResourceNotFoundError: If calendar is not found
            RateLimitError: If rate limit is exceeded
            CalendarProviderError: For other provider-specific errors
        """
        # Validate input parameters
        if not firm_id:
            raise ValidationError("firm_id is required")
        if not request:
            raise ValidationError("request is required")
        if not request.calendar_ids:
            raise ValidationError("request.calendar_ids is required")
        if not request.start_time or not request.end_time:
            raise ValidationError(
                "request.start_time and request.end_time are required"
            )
        if request.start_time >= request.end_time:
            raise ValidationError(
                "request.start_time must be before request.end_time"
            )

        logger.debug(
            f"Checking free/busy for firm {firm_id}, calendars "
            f"{request.calendar_ids}"
        )

        async def _check_free_busy_impl() -> FreeBusyResponse:
            # Format time parameters for Microsoft Graph API
            time_min = request.start_time.isoformat()
            time_max = request.end_time.isoformat()

            # Check free/busy via Microsoft Graph API
            response = await self.client.get_schedule(
                firm_id=firm_id,
                calendar_ids=request.calendar_ids,
                start_time=time_min,
                end_time=time_max,
                timezone=request.timezone
            )

            # Map Microsoft Graph response to our model
            calendars = {}
            for calendar_id in request.calendar_ids:
                # For this implementation, we'll return empty busy slots
                # In a real implementation, you would parse the schedule response
                busy_slots = []
                
                # Parse the schedule data from the response
                schedule_data = response.get("value", [])
                for schedule in schedule_data:
                    availability_view = schedule.get("availabilityView", [])
                    # Each number in availabilityView represents the status for a
                    # time slot
                    # 0 = Free, 1 = Tentative, 2 = Busy, 3 = Out of Office,
                    # 4 = Working Elsewhere
                    
                    # For simplicity, we'll create time slots based on the
                    # availability view
                    # In a real implementation, you would properly parse the time
                    # intervals
                    
                calendars[calendar_id] = busy_slots

            # Create response
            result = FreeBusyResponse(
                calendars=calendars,
                time_min=request.start_time,
                time_max=request.end_time
            )

            logger.info(
                f"Checked free/busy for firm {firm_id}, calendars "
                f"{request.calendar_ids}"
            )
            return result

        try:
            # Execute with rate limit handling
            return await self.execute_with_rate_limit(_check_free_busy_impl)

        except AuthenticationError:
            logger.error(
                f"Authentication error when checking free/busy for firm {firm_id}"
            )
            raise
        except ResourceNotFoundError:
            logger.error(f"Calendar not found for firm {firm_id}")
            raise
        except RateLimitError:
            logger.error(
                f"Rate limit exceeded when checking free/busy for firm {firm_id}"
            )
            raise
        except Exception as e:
            logger.error(f"Error checking free/busy for firm {firm_id}: {str(e)}")
            raise CalendarProviderError(f"Failed to check free/busy: {str(e)}")
