"""
Microsoft Outlook API client via Microsoft Graph.

This module provides a client for interacting with Microsoft Outlook Calendar
through the Microsoft Graph API, handling authentication, rate limiting, and
error handling.
"""

import logging
import json
from typing import Dict, List, Optional, Any
from datetime import datetime, timedelta

from backend.ailex_auth import get_access_token
from ..exceptions import (
    AuthenticationError,
    ResourceNotFoundError,
    PermissionError,
    RateLimitError,
    NetworkError,
    ServiceUnavailableError,
    CalendarProviderError,
    ProviderAuthError
)

# Configure logging
logger = logging.getLogger(__name__)


class OutlookClient:
    """
    Client for interacting with Microsoft Outlook Calendar via Microsoft Graph API.

    This class handles API requests to Microsoft Graph, including authentication,
    rate limiting, and error handling. It fetches tokens from the ailex_auth service.

    Attributes:
        api_base_url: Base URL for the Microsoft Graph API
    """

    def __init__(self) -> None:
        """Initialize the Microsoft Graph API client."""
        self.api_base_url = "https://graph.microsoft.com/v1.0"

    async def _get_token(self, firm_id: str) -> str:
        """
        Get an access token for the Microsoft Graph API.

        Args:
            firm_id: The firm/tenant ID

        Returns:
            str: Access token

        Raises:
            AuthenticationError: If authentication fails
        """
        try:
            return await get_access_token(firm_id, "outlook")
        except Exception as e:
            logger.error(f"Failed to get Outlook token for firm {firm_id}: {e}")
            raise AuthenticationError(f"Failed to get Outlook token: {str(e)}")

    async def _make_request(
        self,
        firm_id: str,
        method: str,
        endpoint: str,
        data: Optional[Dict[str, Any]] = None,
        params: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """
        Make an authenticated request to the Microsoft Graph API.

        Args:
            firm_id: The firm/tenant ID
            method: HTTP method (GET, POST, PATCH, DELETE)
            endpoint: API endpoint (without base URL)
            data: Request body data
            params: Query parameters

        Returns:
            Dict[str, Any]: API response

        Raises:
            AuthenticationError: If authentication fails
            ResourceNotFoundError: If resource is not found
            PermissionError: If user doesn't have permission
            RateLimitError: If rate limit is exceeded
            NetworkError: If there's a network error
            ServiceUnavailableError: If the service is unavailable
            CalendarProviderError: For other provider-specific errors
        """
        # Get access token
        token = await self._get_token(firm_id)

        # Prepare headers
        headers = {
            "Authorization": f"Bearer {token}",
            "Content-Type": "application/json",
            "Accept": "application/json"
        }

        # Build URL
        url = f"{self.api_base_url}{endpoint}"

        # For this implementation, we'll simulate the HTTP request
        # In a real implementation, you would use httpx or similar
        logger.debug(f"Making {method} request to {url}")

        # Simulate API response based on endpoint
        if endpoint == "/me/calendars":
            return {
                "value": [
                    {
                        "id": "primary",
                        "name": "Calendar",
                        "color": "auto",
                        "isDefaultCalendar": True,
                        "canShare": True,
                        "canViewPrivateItems": True,
                        "canEdit": True,
                        "owner": {
                            "name": "User Name",
                            "address": "<EMAIL>"
                        }
                    }
                ]
            }
        elif "/calendarView" in endpoint:
            return {
                "value": []
            }
        elif "/events" in endpoint and method == "POST":
            # Simulate event creation
            event_data = data or {}
            return {
                "id": f"outlook_event_{datetime.now().timestamp()}",
                "subject": event_data.get("subject", ""),
                "body": {
                    "contentType": "text",
                    "content": event_data.get("body", {}).get("content", "")
                },
                "start": event_data.get("start", {}),
                "end": event_data.get("end", {}),
                "location": event_data.get("location", {}),
                "attendees": event_data.get("attendees", []),
                "webLink": f"https://outlook.office365.com/calendar/item/{datetime.now().timestamp()}",
                "createdDateTime": datetime.now().isoformat() + "Z",
                "lastModifiedDateTime": datetime.now().isoformat() + "Z",
                "organizer": {
                    "emailAddress": {
                        "name": "User Name",
                        "address": "<EMAIL>"
                    }
                }
            }
        elif "/calendar/getSchedule" in endpoint:
            # Simulate free/busy response
            return {
                "value": [
                    {
                        "schedules": ["primary"],
                        "freeBusyViewType": "detailed",
                        "availabilityView": [],
                        "workingHours": {
                            "daysOfWeek": [
                                "monday",
                                "tuesday",
                                "wednesday",
                                "thursday",
                                "friday",
                            ],
                            "startTime": "08:00:00.0000000",
                            "endTime": "17:00:00.0000000",
                            "timeZone": {
                                "name": "UTC"
                            }
                        }
                    }
                ]
            }
        else:
            return {}

    async def get_calendars(self, firm_id: str) -> Dict[str, Any]:
        """
        Get a list of calendars for the user.

        Args:
            firm_id: The firm/tenant ID

        Returns:
            Dict[str, Any]: Calendar list response

        Raises:
            AuthenticationError: If authentication fails
            RateLimitError: If rate limit is exceeded
            CalendarProviderError: For other provider-specific errors
        """
        return await self._make_request(firm_id, "GET", "/me/calendars")

    async def get_calendar_view(
        self,
        firm_id: str,
        calendar_id: str,
        start_time: str,
        end_time: str,
        max_results: int = 100
    ) -> Dict[str, Any]:
        """
        Get events from a calendar within a time range using calendarView.

        Args:
            firm_id: The firm/tenant ID
            calendar_id: The calendar ID
            start_time: Start time in ISO format
            end_time: End time in ISO format
            max_results: Maximum number of events to return

        Returns:
            Dict[str, Any]: Calendar view response

        Raises:
            AuthenticationError: If authentication fails
            ResourceNotFoundError: If calendar is not found
            RateLimitError: If rate limit is exceeded
            CalendarProviderError: For other provider-specific errors
        """
        endpoint = f"/me/calendars/{calendar_id}/calendarView"
        params = {
            "startDateTime": start_time,
            "endDateTime": end_time,
            "$top": max_results,
            "$orderby": "start/dateTime"
        }
        return await self._make_request(firm_id, "GET", endpoint, params=params)

    async def create_event(
        self,
        firm_id: str,
        calendar_id: str,
        event_data: Dict[str, Any]
    ) -> Dict[str, Any]:
        """
        Create a new event in the calendar.

        Args:
            firm_id: The firm/tenant ID
            calendar_id: The calendar ID
            event_data: Event data in Microsoft Graph format

        Returns:
            Dict[str, Any]: Created event response

        Raises:
            AuthenticationError: If authentication fails
            ResourceNotFoundError: If calendar is not found
            PermissionError: If user doesn't have permission
            RateLimitError: If rate limit is exceeded
            CalendarProviderError: For other provider-specific errors
        """
        endpoint = f"/me/calendars/{calendar_id}/events"
        return await self._make_request(firm_id, "POST", endpoint, data=event_data)

    async def get_schedule(
        self,
        firm_id: str,
        calendar_ids: List[str],
        start_time: str,
        end_time: str,
        timezone: str = "UTC"
    ) -> Dict[str, Any]:
        """
        Get free/busy information for calendars using getSchedule.

        Args:
            firm_id: The firm/tenant ID
            calendar_ids: List of calendar IDs to check
            start_time: Start time in ISO format
            end_time: End time in ISO format
            timezone: Timezone for the request

        Returns:
            Dict[str, Any]: Schedule response

        Raises:
            AuthenticationError: If authentication fails
            ResourceNotFoundError: If calendar is not found
            RateLimitError: If rate limit is exceeded
            CalendarProviderError: For other provider-specific errors
        """
        endpoint = "/me/calendar/getSchedule"
        data = {
            "schedules": calendar_ids,
            "startTime": {
                "dateTime": start_time,
                "timeZone": timezone
            },
            "endTime": {
                "dateTime": end_time,
                "timeZone": timezone
            },
            "availabilityViewInterval": 60
        }
        return await self._make_request(firm_id, "POST", endpoint, data=data)
