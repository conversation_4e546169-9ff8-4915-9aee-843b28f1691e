"""
Microsoft Outlook data models and mapping functions.

This module provides functions to map between Microsoft Graph API responses
and the common calendar models used by the provider interface.
"""

import logging
from datetime import datetime
from typing import Dict, Any, Optional

from ..models import CalendarEvent, CalendarEventCreate, Attendee, EventStatus

# Configure logging
logger = logging.getLogger(__name__)


def map_outlook_event_to_calendar_event(
    outlook_event: Dict[str, Any],
    calendar_id: str,
    provider_id: str
) -> CalendarEvent:
    """
    Map a Microsoft Graph event to a CalendarEvent.

    Args:
        outlook_event: Event data from Microsoft Graph API
        calendar_id: The calendar ID
        provider_id: The provider ID

    Returns:
        CalendarEvent: Mapped calendar event

    Raises:
        ValueError: If required fields are missing
    """
    try:
        # Extract basic event information
        event_id = outlook_event.get("id")
        if not event_id:
            raise ValueError("Event ID is required")

        summary = outlook_event.get("subject", "")
        description = outlook_event.get("body", {}).get("content", "")
        location = outlook_event.get("location", {}).get("displayName", "")

        # Parse start and end times
        start_data = outlook_event.get("start", {})
        end_data = outlook_event.get("end", {})
        
        start_time_str = start_data.get("dateTime")
        end_time_str = end_data.get("dateTime")
        
        if not start_time_str or not end_time_str:
            raise ValueError("Start and end times are required")

        # Parse datetime strings (Microsoft Graph format)
        start_time = datetime.fromisoformat(start_time_str.replace("Z", "+00:00"))
        end_time = datetime.fromisoformat(end_time_str.replace("Z", "+00:00"))

        # Check if it's an all-day event
        all_day = (
            start_data.get("dateTime") is None and start_data.get("date") is not None
        )

        # Map attendees
        attendees = []
        for attendee_data in outlook_event.get("attendees", []):
            email_address = attendee_data.get("emailAddress", {})
            attendee = Attendee(
                email=email_address.get("address", ""),
                name=email_address.get("name", ""),
                response_status=attendee_data.get("status", {}).get(
                    "response", "needsAction"
                ),
            )
            attendees.append(attendee)

        # Map organizer
        organizer_data = outlook_event.get("organizer", {}).get("emailAddress", {})
        organizer = Attendee(
            email=organizer_data.get("address", ""),
            name=organizer_data.get("name", ""),
            response_status="accepted"
        ) if organizer_data.get("address") else None

        # Parse timestamps
        created_at_str = outlook_event.get("createdDateTime")
        updated_at_str = outlook_event.get("lastModifiedDateTime")
        
        created_at = (
            datetime.fromisoformat(created_at_str.replace("Z", "+00:00"))
            if created_at_str
            else datetime.now()
        )
        updated_at = (
            datetime.fromisoformat(updated_at_str.replace("Z", "+00:00"))
            if updated_at_str
            else None
        )

        # Map event status
        status_map = {
            "free": EventStatus.CONFIRMED,
            "tentative": EventStatus.TENTATIVE,
            "busy": EventStatus.CONFIRMED,
            "oof": EventStatus.CONFIRMED,
            "workingElsewhere": EventStatus.CONFIRMED
        }
        status = status_map.get(
            outlook_event.get("showAs", "busy"), EventStatus.CONFIRMED
        )

        return CalendarEvent(
            id=event_id,
            calendar_id=calendar_id,
            provider_id=provider_id,
            summary=summary,
            description=description,
            location=location,
            start_time=start_time,
            end_time=end_time,
            all_day=all_day,
            attendees=attendees,
            organizer=organizer,
            status=status,
            provider_event_link=outlook_event.get("webLink"),
            created_at=created_at,
            updated_at=updated_at,
            metadata={
                "outlook_id": event_id,
                "show_as": outlook_event.get("showAs", "busy"),
                "importance": outlook_event.get("importance", "normal"),
                "sensitivity": outlook_event.get("sensitivity", "normal")
            }
        )

    except Exception as e:
        logger.error(f"Failed to map Outlook event to CalendarEvent: {str(e)}")
        raise ValueError(f"Failed to map Outlook event: {str(e)}")


def map_calendar_event_to_outlook_event(event: CalendarEventCreate) -> Dict[str, Any]:
    """
    Map a CalendarEventCreate to Microsoft Graph event format.

    Args:
        event: Calendar event to create

    Returns:
        Dict[str, Any]: Event data in Microsoft Graph format

    Raises:
        ValueError: If required fields are missing
    """
    try:
        if not event.summary:
            raise ValueError("Event summary is required")
        if not event.start_time or not event.end_time:
            raise ValueError("Start and end times are required")

        # Build the event data
        outlook_event = {
            "subject": event.summary,
            "body": {
                "contentType": "text",
                "content": event.description or ""
            },
            "start": {
                "dateTime": event.start_time.isoformat(),
                "timeZone": "UTC"
            },
            "end": {
                "dateTime": event.end_time.isoformat(),
                "timeZone": "UTC"
            }
        }

        # Add location if provided
        if event.location:
            outlook_event["location"] = {
                "displayName": event.location
            }

        # Add attendees if provided
        if event.attendees:
            outlook_event["attendees"] = []
            for attendee in event.attendees:
                outlook_attendee = {
                    "emailAddress": {
                        "address": attendee.email,
                        "name": attendee.name or attendee.email
                    },
                    "type": "required"
                }
                outlook_event["attendees"].append(outlook_attendee)

        # Map status to showAs
        status_map = {
            EventStatus.CONFIRMED: "busy",
            EventStatus.TENTATIVE: "tentative",
            EventStatus.CANCELLED: "free"
        }
        outlook_event["showAs"] = status_map.get(event.status, "busy")

        # Handle all-day events
        if event.all_day:
            # For all-day events, use date format instead of dateTime
            start_date = event.start_time.date().isoformat()
            end_date = event.end_time.date().isoformat()
            
            outlook_event["start"] = {
                "date": start_date,
                "timeZone": "UTC"
            }
            outlook_event["end"] = {
                "date": end_date,
                "timeZone": "UTC"
            }
            outlook_event["isAllDay"] = True

        return outlook_event

    except Exception as e:
        logger.error(f"Failed to map CalendarEvent to Outlook event: {str(e)}")
        raise ValueError(f"Failed to map CalendarEvent: {str(e)}")
