"""
Shared models for calendar providers.

This module defines Pydantic models for calendar events and related data
that are used across all calendar provider implementations.
"""

from datetime import datetime
from typing import Dict, List, Optional, Any
from enum import Enum
from pydantic import BaseModel, Field, field_validator

class EventStatus(str, Enum):
    """Status of a calendar event."""
    CONFIRMED = "confirmed"
    TENTATIVE = "tentative"
    CANCELLED = "cancelled"

class Attendee(BaseModel):
    """
    An attendee of a calendar event.

    Attributes:
        email: Email address of the attendee
        name: Name of the attendee
        response_status: Response status (accepted, declined, tentative, needs_action)
        is_organizer: Whether this attendee is the organizer of the event
    """
    email: str
    name: Optional[str] = None
    response_status: Optional[str] = None  # accepted, declined, tentative, needs_action
    is_organizer: bool = False

class TimeSlot(BaseModel):
    """
    A time slot for free/busy information.

    Attributes:
        start: Start time of the slot
        end: End time of the slot
        event_id: Optional ID of the event associated with this time slot
    """
    start: datetime
    end: datetime
    event_id: Optional[str] = None

    @field_validator('end')
    def end_after_start(cls, v, info):
        """Validate that end time is after start time."""
        values = info.data
        if 'start' in values and v <= values['start']:
            raise ValueError('end time must be after start time')
        return v

class CalendarEventBase(BaseModel):
    """
    Base model for calendar events.

    Attributes:
        summary: Title/summary of the event
        description: Detailed description of the event
        location: Physical location of the event
        start_time: Start time of the event
        end_time: End time of the event
        all_day: Whether this is an all-day event
        attendees: List of attendees
        recurrence: Recurrence rule (iCalendar RRULE format)
        status: Status of the event (confirmed, tentative, cancelled)
        metadata: Additional provider-specific metadata
    """
    summary: str
    description: Optional[str] = None
    location: Optional[str] = None
    start_time: datetime
    end_time: datetime
    all_day: bool = False
    attendees: List[Attendee] = Field(default_factory=list)
    recurrence: Optional[str] = None
    status: EventStatus = EventStatus.CONFIRMED
    metadata: Dict[str, Any] = Field(default_factory=dict)

    @field_validator('end_time')
    def end_after_start(cls, v, info):
        """Validate that end time is after start time."""
        values = info.data
        if 'start_time' in values and v <= values['start_time']:
            raise ValueError('end_time must be after start_time')
        return v

class CalendarEventCreate(CalendarEventBase):
    """
    Model for creating a new calendar event.

    This model extends CalendarEventBase with no additional fields,
    but is used to distinguish between creation and update operations.
    """
    pass

class CalendarEventUpdate(BaseModel):
    """
    Model for updating an existing calendar event.

    All fields are optional since only the fields that need to be
    updated are included in the update operation.

    Attributes:
        summary: Title/summary of the event
        description: Detailed description of the event
        location: Physical location of the event
        start_time: Start time of the event
        end_time: End time of the event
        all_day: Whether this is an all-day event
        attendees: List of attendees
        recurrence: Recurrence rule (iCalendar RRULE format)
        status: Status of the event (confirmed, tentative, cancelled)
        metadata: Additional provider-specific metadata
    """
    summary: Optional[str] = None
    description: Optional[str] = None
    location: Optional[str] = None
    start_time: Optional[datetime] = None
    end_time: Optional[datetime] = None
    all_day: Optional[bool] = None
    attendees: Optional[List[Attendee]] = None
    recurrence: Optional[str] = None
    status: Optional[EventStatus] = None
    metadata: Optional[Dict[str, Any]] = None

    @field_validator('end_time')
    def end_after_start(cls, v, info):
        """Validate that end time is after start time if both are provided."""
        values = info.data
        if (
            v is not None
            and "start_time" in values
            and values["start_time"] is not None
        ):
            if v <= values['start_time']:
                raise ValueError('end_time must be after start_time')
        return v

class CalendarEvent(CalendarEventBase):
    """
    Complete calendar event model with provider-specific details.

    This model extends CalendarEventBase with additional fields that
    are populated when an event is retrieved from a provider.

    Attributes:
        id: Unique identifier for the event
        calendar_id: ID of the calendar containing the event
        provider_id: ID of the provider (e.g., 'google', 'calendly')
        provider_event_link: Link to the event in the provider's UI
        created_at: When the event was created
        updated_at: When the event was last updated
        organizer: The organizer of the event
    """
    id: str
    calendar_id: str
    provider_id: str
    provider_event_link: Optional[str] = None
    created_at: datetime
    updated_at: Optional[datetime] = None
    organizer: Optional[Attendee] = None

class FreeBusyRequest(BaseModel):
    """
    Request model for free/busy checking.

    Attributes:
        calendar_ids: List of calendar IDs to check
        start_time: Start of the time range
        end_time: End of the time range
        timezone: Timezone for the time range (default: UTC)
    """
    calendar_ids: List[str]
    start_time: datetime
    end_time: datetime
    timezone: Optional[str] = "UTC"

    @field_validator('end_time')
    def end_after_start(cls, v, info):
        """Validate that end time is after start time."""
        values = info.data
        if 'start_time' in values and v <= values['start_time']:
            raise ValueError('end_time must be after start_time')
        return v

    @field_validator('calendar_ids')
    def non_empty_calendar_ids(cls, v):
        """Validate that calendar_ids is not empty."""
        if not v:
            raise ValueError('calendar_ids must not be empty')
        return v

class FreeBusyResponse(BaseModel):
    """
    Response model for free/busy checking.

    Attributes:
        calendars: Dictionary mapping calendar IDs to lists of busy time slots
        time_min: Start of the time range that was checked
        time_max: End of the time range that was checked
    """
    calendars: Dict[str, List[TimeSlot]]  # calendar_id -> list of busy time slots
    time_min: datetime
    time_max: datetime
