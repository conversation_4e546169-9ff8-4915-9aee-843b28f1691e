"""
Abstract base class for calendar providers.

This module defines the CalendarProvider interface that all calendar provider
implementations must follow, along with shared enums and types.
"""

import logging
from abc import ABC, abstractmethod
from datetime import datetime
from typing import Dict, List, Optional, Any, Union, Callable, Awaitable, TypeVar
from enum import Enum

from .models import (
    CalendarEvent,
    TimeSlot,
    CalendarEventCreate,
    CalendarEventUpdate,
    FreeBusyRequest,
    FreeBusyResponse
)
from .rate_limiter import RateLimitHandler
from ..utils.retry import async_retry, RetryConfig

# Configure logging
logger = logging.getLogger(__name__)

class ProviderCapability(str, Enum):
    """Capabilities that a calendar provider may support."""
    CREATE_EVENT = "create_event"
    READ_EVENT = "read_event"
    UPDATE_EVENT = "update_event"
    DELETE_EVENT = "delete_event"
    FREE_BUSY = "free_busy"
    LIST_CALENDARS = "list_calendars"
    WEBHOOK_SUPPORT = "webhook_support"

class CalendarProvider(ABC):
    """
    Abstract base class for calendar providers.

    This class defines the interface that all calendar provider implementations
    must follow. It provides methods for interacting with calendar services like
    Google Calendar, Microsoft Outlook, and Calendly.

    All provider implementations should check if they support a capability before
    attempting to perform an operation. The base class provides helper methods
    for capability checking.

    The class also provides rate limiting functionality through the RateLimitHandler,
    which implements retry logic with exponential backoff and circuit breaker patterns.
    """

    # Define a type variable for generic function return
    T = TypeVar('T')

    def __init__(self) -> None:
        """Initialize the calendar provider."""
        # Initialize rate limit handler
        self._rate_limit_handler = RateLimitHandler()

    @property
    def rate_limit_handler(self) -> RateLimitHandler:
        """
        Get the rate limit handler.

        Returns:
            RateLimitHandler: The rate limit handler
        """
        return self._rate_limit_handler

    async def execute_with_rate_limit(
        self,
        func: Callable[..., Awaitable[T]],
        *args: Any,
        **kwargs: Any
    ) -> T:
        """
        Execute a function with rate limit handling.

        This method wraps the function call with rate limit handling,
        implementing retry logic with exponential backoff and circuit
        breaker patterns.

        Args:
            func: The async function to execute
            *args: Arguments to pass to the function
            **kwargs: Keyword arguments to pass to the function

        Returns:
            The result of the function

        Raises:
            RateLimitError: If rate limit is hit and cannot be retried
            CalendarProviderError: For other provider-specific errors
        """
        return await self._rate_limit_handler.execute_with_rate_limit(
            self.provider_id, func, *args, **kwargs
        )

    @property
    @abstractmethod
    def provider_id(self) -> str:
        """
        Get the unique identifier for the provider.

        Returns:
            str: The provider ID
        """
        pass

    @property
    @abstractmethod
    def provider_name(self) -> str:
        """
        Get the human-readable name of the provider.

        Returns:
            str: The provider name
        """
        pass

    @property
    @abstractmethod
    def capabilities(self) -> List[ProviderCapability]:
        """
        Get the list of capabilities supported by this provider.

        Returns:
            List[ProviderCapability]: The supported capabilities
        """
        pass

    def has_capability(self, capability: Union[ProviderCapability, str]) -> bool:
        """
        Check if this provider supports a capability.

        Args:
            capability: The capability to check

        Returns:
            bool: True if the provider supports the capability, False otherwise
        """
        # Convert capability to string if it's a ProviderCapability enum
        capability_name = (
            capability.value
            if isinstance(capability, ProviderCapability)
            else capability
        )

        # Check if the capability is in the provider's capabilities
        return any(cap.value == capability_name for cap in self.capabilities)

    def require_capability(self, capability: Union[ProviderCapability, str]) -> None:
        """
        Require that this provider supports a capability.

        This method raises an UnsupportedOperationError if the provider
        does not support the specified capability.

        Args:
            capability: The capability to require

        Raises:
            UnsupportedOperationError: If the provider does not support the capability
        """
        # Import here to avoid circular imports
        from .exceptions import UnsupportedOperationError

        # Convert capability to string if it's a ProviderCapability enum
        capability_name = (
            capability.value
            if isinstance(capability, ProviderCapability)
            else capability
        )

        # Check if the capability is supported
        if not self.has_capability(capability_name):
            logger.warning(
                f"Unsupported operation: {capability_name} is not supported by "
                f"{self.provider_name}"
            )
            raise UnsupportedOperationError(
                f"The operation '{capability_name}' is not supported by the "
                f"provider '{self.provider_name}'"
            )

    @abstractmethod
    async def get_calendars(self, firm_id: str) -> List[Dict[str, Any]]:
        """
        Get a list of calendars available for the firm.

        Args:
            firm_id: The firm/tenant ID

        Returns:
            List[Dict[str, Any]]: List of calendar objects with id, name,
                description, etc.

        Raises:
            AuthenticationError: If authentication fails
            RateLimitError: If rate limit is exceeded
            CalendarProviderError: For other provider-specific errors
        """
        pass

    @abstractmethod
    async def get_events(
        self,
        firm_id: str,
        calendar_id: str,
        start_time: datetime,
        end_time: datetime,
        max_results: int = 100
    ) -> List[CalendarEvent]:
        """
        Get events from a calendar within a time range.

        Args:
            firm_id: The firm/tenant ID
            calendar_id: The calendar ID
            start_time: Start of the time range
            end_time: End of the time range
            max_results: Maximum number of events to return

        Returns:
            List[CalendarEvent]: List of calendar events

        Raises:
            AuthenticationError: If authentication fails
            ResourceNotFoundError: If calendar is not found
            RateLimitError: If rate limit is exceeded
            CalendarProviderError: For other provider-specific errors
        """
        pass

    @abstractmethod
    async def get_event(
        self,
        firm_id: str,
        calendar_id: str,
        event_id: str
    ) -> CalendarEvent:
        """
        Get a specific event by ID.

        Args:
            firm_id: The firm/tenant ID
            calendar_id: The calendar ID
            event_id: The event ID

        Returns:
            CalendarEvent: Calendar event details

        Raises:
            AuthenticationError: If authentication fails
            ResourceNotFoundError: If event is not found
            RateLimitError: If rate limit is exceeded
            CalendarProviderError: For other provider-specific errors
        """
        pass

    @abstractmethod
    @async_retry()
    async def create_event(
        self,
        firm_id: str,
        calendar_id: str,
        event: CalendarEventCreate
    ) -> CalendarEvent:
        """
        Create a new event in the calendar.

        Args:
            firm_id: The firm/tenant ID
            calendar_id: The calendar ID
            event: The event to create

        Returns:
            CalendarEvent: Created calendar event with provider-specific IDs

        Raises:
            AuthenticationError: If authentication fails
            ResourceNotFoundError: If calendar is not found
            PermissionError: If user doesn't have permission
            RateLimitError: If rate limit is exceeded
            CalendarProviderError: For other provider-specific errors
        """
        pass

    @abstractmethod
    @async_retry()
    async def update_event(
        self,
        firm_id: str,
        calendar_id: str,
        event_id: str,
        event: CalendarEventUpdate
    ) -> CalendarEvent:
        """
        Update an existing event in the calendar.

        Args:
            firm_id: The firm/tenant ID
            calendar_id: The calendar ID
            event_id: The event ID
            event: The updated event data

        Returns:
            CalendarEvent: Updated calendar event

        Raises:
            AuthenticationError: If authentication fails
            ResourceNotFoundError: If event is not found
            PermissionError: If user doesn't have permission
            RateLimitError: If rate limit is exceeded
            CalendarProviderError: For other provider-specific errors
        """
        pass

    @abstractmethod
    @async_retry()
    async def delete_event(
        self,
        firm_id: str,
        calendar_id: str,
        event_id: str
    ) -> bool:
        """
        Delete an event from the calendar.

        Args:
            firm_id: The firm/tenant ID
            calendar_id: The calendar ID
            event_id: The event ID

        Returns:
            bool: True if deletion was successful

        Raises:
            AuthenticationError: If authentication fails
            ResourceNotFoundError: If event is not found
            PermissionError: If user doesn't have permission
            RateLimitError: If rate limit is exceeded
            CalendarProviderError: For other provider-specific errors
        """
        pass

    @abstractmethod
    @async_retry()
    async def check_free_busy(
        self,
        firm_id: str,
        request: FreeBusyRequest
    ) -> FreeBusyResponse:
        """
        Check free/busy times for calendars.

        Args:
            firm_id: The firm/tenant ID
            request: Free/busy request parameters

        Returns:
            FreeBusyResponse: Free/busy information for the requested calendars
                and time range

        Raises:
            AuthenticationError: If authentication fails
            ResourceNotFoundError: If calendar is not found
            RateLimitError: If rate limit is exceeded
            CalendarProviderError: For other provider-specific errors
        """
        pass
