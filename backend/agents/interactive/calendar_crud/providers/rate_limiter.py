"""
Rate Limiting Handler for Calendar Providers

This module provides rate limiting detection and handling for calendar providers,
implementing exponential backoff, jitter, and circuit breaker patterns.
"""

import asyncio
import logging
import random
import time
from datetime import datetime, timedelta
from typing import Dict, Optional, Callable, Awaitable, TypeVar, Any, cast

from .exceptions import RateLimit<PERSON>rror, CalendarProviderError

# Configure logging
logger = logging.getLogger(__name__)

# Type variable for generic function return
T = TypeVar('T')


class RateLimitState:
    """
    State tracking for rate limits per provider.
    
    This class tracks rate limit encounters and implements circuit breaker
    functionality to prevent overwhelming providers during outages.
    
    Attributes:
        provider_id: Unique identifier for the provider
        last_rate_limit: Timestamp of the last rate limit encounter
        retry_after: Seconds to wait before retrying (from provider)
        consecutive_rate_limits: Count of consecutive rate limit errors
        circuit_open: Whether the circuit breaker is open
        circuit_open_until: When the circuit breaker will close
        request_count: Total number of requests since last reset
        error_count: Number of rate limit errors since last reset
    """
    
    def __init__(self, provider_id: str):
        """
        Initialize rate limit state for a provider.
        
        Args:
            provider_id: Unique identifier for the provider
        """
        self.provider_id = provider_id
        self.last_rate_limit: Optional[datetime] = None
        self.retry_after: Optional[int] = None
        self.consecutive_rate_limits = 0
        self.circuit_open = False
        self.circuit_open_until: Optional[datetime] = None
        self.request_count = 0
        self.error_count = 0
        self.error_threshold = 0.5  # 50% error rate triggers circuit breaker
        self.min_requests_for_circuit = 5  # Minimum requests before circuit can trip
        self.circuit_reset_time = 300  # 5 minutes
        
    def record_success(self) -> None:
        """Record a successful request."""
        self.request_count += 1
        self.consecutive_rate_limits = 0
        
    def record_rate_limit(self, retry_after: Optional[int] = None) -> None:
        """
        Record a rate limit encounter.
        
        Args:
            retry_after: Seconds to wait before retrying (from provider)
        """
        self.request_count += 1
        self.error_count += 1
        self.consecutive_rate_limits += 1
        self.last_rate_limit = datetime.now()
        self.retry_after = retry_after
        
        # Check if circuit breaker should trip
        if (self.request_count >= self.min_requests_for_circuit and 
            self.error_count / self.request_count >= self.error_threshold):
            self.trip_circuit_breaker()
            
    def trip_circuit_breaker(self) -> None:
        """Trip the circuit breaker."""
        self.circuit_open = True
        # Calculate exponential backoff based on consecutive failures
        backoff = min(
            self.circuit_reset_time * (2 ** (self.consecutive_rate_limits - 1)), 3600
        )
        self.circuit_open_until = datetime.now() + timedelta(seconds=backoff)
        logger.warning(
            f"Circuit breaker tripped for provider {self.provider_id}. "
            f"Circuit will reset after {backoff} seconds."
        )
        
    def is_circuit_open(self) -> bool:
        """
        Check if circuit breaker is open.
        
        Returns:
            bool: True if circuit is open, False otherwise
        """
        if not self.circuit_open:
            return False
            
        # Check if circuit should reset
        if self.circuit_open_until and datetime.now() > self.circuit_open_until:
            self.reset_circuit()
            return False
            
        return True
        
    def reset_circuit(self) -> None:
        """Reset the circuit breaker."""
        self.circuit_open = False
        self.circuit_open_until = None
        self.request_count = 0
        self.error_count = 0
        logger.info(f"Circuit breaker reset for provider {self.provider_id}")
        
    def get_retry_delay(self) -> float:
        """
        Calculate retry delay with exponential backoff and jitter.
        
        Returns:
            float: Delay in seconds before retrying
        """
        if self.retry_after:
            base_delay = self.retry_after
        else:
            # Default exponential backoff: 2^n seconds where n is consecutive failures
            base_delay = 2 ** self.consecutive_rate_limits
            
        # Add jitter (±25%)
        jitter = random.uniform(-0.25, 0.25) * base_delay
        delay = max(1, base_delay + jitter)
        
        return delay


class RateLimitHandler:
    """
    Handler for rate limiting across calendar providers.
    
    This class provides methods for executing functions with rate limit handling,
    implementing retry logic with exponential backoff and circuit breaker patterns.
    """
    
    def __init__(self):
        """Initialize the rate limit handler."""
        self.provider_states: Dict[str, RateLimitState] = {}
        
    def get_provider_state(self, provider_id: str) -> RateLimitState:
        """
        Get or create state for a provider.
        
        Args:
            provider_id: Unique identifier for the provider
            
        Returns:
            RateLimitState: State object for the provider
        """
        if provider_id not in self.provider_states:
            self.provider_states[provider_id] = RateLimitState(provider_id)
        return self.provider_states[provider_id]
        
    async def execute_with_rate_limit(
        self,
        provider_id: str,
        func: Callable[..., Awaitable[T]],
        *args: Any,
        **kwargs: Any
    ) -> T:
        """
        Execute a function with rate limit handling.
        
        Args:
            provider_id: The provider ID
            func: The async function to execute
            *args: Arguments to pass to the function
            **kwargs: Keyword arguments to pass to the function
            
        Returns:
            The result of the function
            
        Raises:
            RateLimitError: If rate limit is hit and cannot be retried
            CalendarProviderError: For other provider-specific errors
        """
        state = self.get_provider_state(provider_id)
        
        # Check if circuit breaker is open
        if state.is_circuit_open():
            wait_time = (state.circuit_open_until - datetime.now()).total_seconds()
            logger.warning(
                f"Circuit breaker open for provider {provider_id}. "
                f"Request blocked. Circuit will reset in {wait_time:.1f} seconds."
            )
            raise RateLimitError(
                f"Service temporarily unavailable due to rate limiting. "
                f"Please try again in {wait_time:.0f} seconds.",
                retry_after=int(wait_time)
            )
            
        # Try to execute the function
        max_retries = 3
        retries = 0
        
        while retries <= max_retries:
            try:
                result = await func(*args, **kwargs)
                state.record_success()
                return result
            except RateLimitError as e:
                retries += 1
                retry_after = getattr(e, 'retry_after', None)
                state.record_rate_limit(retry_after)
                
                if retries > max_retries:
                    logger.error(
                        f"Max retries exceeded for provider {provider_id} "
                        f"after {max_retries} attempts."
                    )
                    raise
                    
                delay = state.get_retry_delay()
                logger.warning(
                    f"Rate limit hit for provider {provider_id}. "
                    f"Retrying in {delay:.1f} seconds (attempt "
                    f"{retries}/{max_retries})."
                )
                
                # Wait before retrying
                await asyncio.sleep(delay)
            except Exception as e:
                # For non-rate-limit errors, just raise
                logger.error(
                    f"Error executing function for provider {provider_id}: {str(e)}"
                )
                raise
