"""
Google Calendar API client.

This module provides a client for interacting with the Google Calendar API,
handling authentication, rate limiting, and error handling.
"""

import os
import logging
import time
import json
from typing import Dict, List, Optional, Any, cast
from datetime import datetime, timedelta

from backend.config import settings
from shared.http import (
    SharedHTTPClient,
    HTTPNonRetryableError,
    HTTPRetryableError,
    HTTPClientError,
)
from shared.auth_client import get_access_token_with_retry
from ..exceptions import (
    AuthenticationError,
    ResourceNotFoundError,
    PermissionError,
    RateLimitError,
    NetworkError,
    ServiceUnavailableError,
    CalendarProviderError,
    ProviderAuthError
)

# Configure logging
logger = logging.getLogger(__name__)

# In-memory token cache with TTL
_token_cache: Dict[str, Dict[str, Any]] = {}

class GoogleCalendarClient:
    """
    Client for interacting with the Google Calendar API.

    This class handles API requests to Google Calendar, including authentication,
    rate limiting, and error handling. It fetches tokens from the auth-service
    and caches them in memory with a 250-second TTL.

    Attributes:
        api_base_url: Base URL for the Google Calendar API
        rate_limit_window: Time window for rate limiting (in seconds)
        rate_limit_max_requests: Maximum number of requests in the window
    """

    def __init__(self) -> None:
        """Initialize the Google Calendar API client."""
        self.api_base_url = "https://www.googleapis.com/calendar/v3"
        self.rate_limit_window = 100  # 100 seconds
        self.rate_limit_max_requests = 100  # 100 requests per 100 seconds
        self._request_timestamps: List[float] = []

    async def _get_token(self, firm_id: str) -> str:
        """
        Get an access token for the Google Calendar API with retry logic.

        Uses the shared auth client with exponential backoff retry logic
        to handle auth-service outages and rate limits gracefully.

        Args:
            firm_id: The firm/tenant ID

        Returns:
            str: Access token

        Raises:
            ProviderAuthError: If auth-service returns non-retryable errors
            AuthenticationError: If authentication fails after retries
        """
        try:
            return await get_access_token_with_retry(firm_id, "google")
        except Exception as e:
            logger.error(f"Failed to get Google Calendar token for firm {firm_id}: {e}")
            raise

    def _check_rate_limit(self) -> None:
        """
        Check if the rate limit has been exceeded.

        This method implements a sliding window rate limiter.

        Raises:
            RateLimitError: If the rate limit has been exceeded
        """
        current_time = time.time()

        # Remove timestamps older than the window
        self._request_timestamps = [
            ts for ts in self._request_timestamps
            if current_time - ts < self.rate_limit_window
        ]

        # Check if we've exceeded the rate limit
        if len(self._request_timestamps) >= self.rate_limit_max_requests:
            oldest_timestamp = min(self._request_timestamps)
            wait_time = self.rate_limit_window - (current_time - oldest_timestamp)
            raise RateLimitError(
                f"Rate limit exceeded. Try again in {wait_time:.2f} seconds."
            )

        # Add current timestamp
        self._request_timestamps.append(current_time)

    async def _make_request(
        self,
        firm_id: str,
        method: str,
        endpoint: str,
        params: Optional[Dict[str, Any]] = None,
        data: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """
        Make a request to the Google Calendar API.

        Args:
            firm_id: The firm/tenant ID
            method: HTTP method (GET, POST, PUT, PATCH, DELETE)
            endpoint: API endpoint
            params: Query parameters
            data: Request body

        Returns:
            Dict[str, Any]: Response data

        Raises:
            AuthenticationError: If authentication fails
            ResourceNotFoundError: If resource is not found
            PermissionError: If user doesn't have permission
            RateLimitError: If rate limit is exceeded
            NetworkError: If there's a network error
            ServiceUnavailableError: If the service is unavailable
            CalendarProviderError: For other provider-specific errors
        """
        # Check rate limit
        self._check_rate_limit()

        # Get access token
        token = await self._get_token(firm_id)

        # Prepare headers
        headers = {
            "Authorization": f"Bearer {token}",
            "Content-Type": "application/json",
            "Accept": "application/json"
        }

        # Prepare URL
        url = f"{self.api_base_url}{endpoint}"

        try:
            # Log request (without sensitive data)
            logger.debug(
                f"Making {method} request to {url} with params: "
                f"{json.dumps(params) if params else 'None'}"
            )

            # Make request using SharedHTTPClient
            async with SharedHTTPClient() as client:
                response = await client.request(
                    method=method,
                    url=url,
                    headers=headers,
                    params=params,
                    json=data
                )
                return response

        except HTTPNonRetryableError as e:
            # Handle 4xx errors
            if "401" in str(e):
                logger.error(f"Authentication error for Google Calendar API: {e}")
                raise AuthenticationError(f"Authentication failed: {e}")
            elif "403" in str(e):
                logger.error(f"Permission error for Google Calendar API: {e}")
                raise PermissionError(f"Permission denied: {e}")
            elif "404" in str(e):
                logger.error(f"Resource not found for Google Calendar API: {e}")
                raise ResourceNotFoundError(f"Resource not found: {e}")
            elif "429" in str(e):
                logger.error(f"Rate limit exceeded for Google Calendar API: {e}")
                raise RateLimitError(f"Rate limit exceeded: {e}")
            else:
                logger.error(f"Client error for Google Calendar API: {e}")
                raise CalendarProviderError(f"Client error: {e}")

        except (HTTPRetryableError, HTTPClientError) as e:
            logger.error(f"Network/server error when calling Google Calendar API: {e}")
            raise NetworkError(f"Error communicating with Google Calendar: {e}")

        except Exception as e:
            logger.error(f"Unexpected error when calling Google Calendar API: {e}")
            raise CalendarProviderError(f"Error calling Google Calendar API: {e}")



    async def get_calendar_list(self, firm_id: str) -> Dict[str, Any]:
        """
        Get the list of calendars for a firm.

        Args:
            firm_id: The firm/tenant ID

        Returns:
            Dict[str, Any]: Calendar list response

        Raises:
            AuthenticationError: If authentication fails
            RateLimitError: If rate limit is exceeded
            CalendarProviderError: For other provider-specific errors
        """
        return await self._make_request(firm_id, "GET", "/users/me/calendarList")

    async def get_events(
        self,
        firm_id: str,
        calendar_id: str,
        time_min: str,
        time_max: str,
        max_results: int = 100
    ) -> Dict[str, Any]:
        """
        Get events from a calendar within a time range.

        Args:
            firm_id: The firm/tenant ID
            calendar_id: The calendar ID
            time_min: Start of the time range (ISO format)
            time_max: End of the time range (ISO format)
            max_results: Maximum number of events to return

        Returns:
            Dict[str, Any]: Events response

        Raises:
            AuthenticationError: If authentication fails
            ResourceNotFoundError: If calendar is not found
            RateLimitError: If rate limit is exceeded
            CalendarProviderError: For other provider-specific errors
        """
        params = {
            "timeMin": time_min,
            "timeMax": time_max,
            "maxResults": str(max_results),
            "singleEvents": "true",
            "orderBy": "startTime"
        }

        return await self._make_request(
            firm_id, "GET", f"/calendars/{calendar_id}/events", params=params
        )

    async def get_event(
        self,
        firm_id: str,
        calendar_id: str,
        event_id: str
    ) -> Dict[str, Any]:
        """
        Get a specific event by ID.

        Args:
            firm_id: The firm/tenant ID
            calendar_id: The calendar ID
            event_id: The event ID

        Returns:
            Dict[str, Any]: Event response

        Raises:
            AuthenticationError: If authentication fails
            ResourceNotFoundError: If event is not found
            RateLimitError: If rate limit is exceeded
            CalendarProviderError: For other provider-specific errors
        """
        return await self._make_request(
            firm_id, "GET", f"/calendars/{calendar_id}/events/{event_id}"
        )

    async def create_event(
        self,
        firm_id: str,
        calendar_id: str,
        event: Dict[str, Any]
    ) -> Dict[str, Any]:
        """
        Create a new event in the calendar.

        Args:
            firm_id: The firm/tenant ID
            calendar_id: The calendar ID
            event: The event data

        Returns:
            Dict[str, Any]: Created event response

        Raises:
            AuthenticationError: If authentication fails
            ResourceNotFoundError: If calendar is not found
            PermissionError: If user doesn't have permission
            RateLimitError: If rate limit is exceeded
            CalendarProviderError: For other provider-specific errors
        """
        return await self._make_request(
            firm_id, "POST", f"/calendars/{calendar_id}/events", data=event
        )

    async def update_event(
        self,
        firm_id: str,
        calendar_id: str,
        event_id: str,
        event: Dict[str, Any]
    ) -> Dict[str, Any]:
        """
        Update an existing event in the calendar.

        Args:
            firm_id: The firm/tenant ID
            calendar_id: The calendar ID
            event_id: The event ID
            event: The updated event data

        Returns:
            Dict[str, Any]: Updated event response

        Raises:
            AuthenticationError: If authentication fails
            ResourceNotFoundError: If event is not found
            PermissionError: If user doesn't have permission
            RateLimitError: If rate limit is exceeded
            CalendarProviderError: For other provider-specific errors
        """
        return await self._make_request(
            firm_id, "PUT", f"/calendars/{calendar_id}/events/{event_id}", data=event
        )

    async def delete_event(
        self,
        firm_id: str,
        calendar_id: str,
        event_id: str
    ) -> bool:
        """
        Delete an event from the calendar.

        Args:
            firm_id: The firm/tenant ID
            calendar_id: The calendar ID
            event_id: The event ID

        Returns:
            bool: True if deletion was successful

        Raises:
            AuthenticationError: If authentication fails
            ResourceNotFoundError: If event is not found
            PermissionError: If user doesn't have permission
            RateLimitError: If rate limit is exceeded
            CalendarProviderError: For other provider-specific errors
        """
        await self._make_request(
            firm_id, "DELETE", f"/calendars/{calendar_id}/events/{event_id}"
        )
        return True

    async def check_free_busy(
        self,
        firm_id: str,
        calendar_ids: List[str],
        time_min: str,
        time_max: str,
        timezone: str = "UTC"
    ) -> Dict[str, Any]:
        """
        Check free/busy times for calendars.

        Args:
            firm_id: The firm/tenant ID
            calendar_ids: List of calendar IDs to check
            time_min: Start of the time range (ISO format)
            time_max: End of the time range (ISO format)
            timezone: Timezone for the time range

        Returns:
            Dict[str, Any]: Free/busy response

        Raises:
            AuthenticationError: If authentication fails
            ResourceNotFoundError: If calendar is not found
            RateLimitError: If rate limit is exceeded
            CalendarProviderError: For other provider-specific errors
        """
        data = {
            "timeMin": time_min,
            "timeMax": time_max,
            "timeZone": timezone,
            "items": [{"id": calendar_id} for calendar_id in calendar_ids]
        }

        return await self._make_request(
            firm_id, "POST", "/freeBusy", data=data
        )
