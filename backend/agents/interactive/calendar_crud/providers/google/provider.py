"""
Google Calendar Provider implementation.

This module provides the GoogleCalendarProvider class that implements
the CalendarProvider interface for Google Calendar.
"""

import logging
from datetime import datetime
from typing import Dict, List, Optional, Any, cast

from ..base import CalendarProvider, ProviderCapability
from ..models import (
    CalendarEvent,
    TimeSlot,
    CalendarEventCreate,
    CalendarEventUpdate,
    FreeBusyRequest,
    FreeBusyResponse,
    Attendee,
    EventStatus
)
from ..exceptions import (
    CalendarProviderError,
    AuthenticationError,
    ResourceNotFoundError,
    PermissionError,
    RateLimitError,
    ValidationError
)
from .models import (
    map_google_event_to_calendar_event,
    map_calendar_event_to_google_event,
    GoogleCalendarList,
    GoogleEvent,
    GoogleFreeBusyResponse
)
from .client import GoogleCalendarClient

# Configure logging
logger = logging.getLogger(__name__)

class GoogleCalendarProvider(CalendarProvider):
    """
    Google Calendar provider implementation.

    This class implements the CalendarProvider interface for Google Calendar,
    providing methods to interact with Google Calendar API for event management.

    Attributes:
        client: The Google Calendar API client
    """

    def __init__(self) -> None:
        """Initialize the Google Calendar provider with API client."""
        super().__init__()
        self.client = GoogleCalendarClient()

    @property
    def provider_id(self) -> str:
        """
        Get the unique identifier for the provider.

        Returns:
            str: The provider ID ('google')
        """
        return "google"

    @property
    def provider_name(self) -> str:
        """
        Get the human-readable name of the provider.

        Returns:
            str: The provider name ('Google Calendar')
        """
        return "Google Calendar"

    @property
    def capabilities(self) -> List[ProviderCapability]:
        """
        Get the list of capabilities supported by this provider.

        Returns:
            List[ProviderCapability]: The supported capabilities
        """
        return [
            ProviderCapability.CREATE_EVENT,
            ProviderCapability.READ_EVENT,
            ProviderCapability.UPDATE_EVENT,
            ProviderCapability.DELETE_EVENT,
            ProviderCapability.FREE_BUSY,
            ProviderCapability.LIST_CALENDARS,
            ProviderCapability.WEBHOOK_SUPPORT
        ]

    async def get_calendars(self, firm_id: str) -> List[Dict[str, Any]]:
        """
        Get a list of calendars available for the firm.

        Args:
            firm_id: The firm/tenant ID

        Returns:
            List[Dict[str, Any]]: List of calendar objects with id, name,
                description, etc.

        Raises:
            ValidationError: If input parameters are invalid
            AuthenticationError: If authentication fails
            RateLimitError: If rate limit is exceeded
            CalendarProviderError: For other provider-specific errors
        """
        if not firm_id:
            raise ValidationError("firm_id is required")

        logger.debug(f"Getting calendars for firm {firm_id}")

        async def _get_calendars_impl() -> List[Dict[str, Any]]:
            # Get calendar list from Google API
            response = await self.client.get_calendar_list(firm_id)

            # Map response to our model
            calendars = []
            for item in response.get("items", []):
                calendars.append({
                    "id": item.get("id"),
                    "name": item.get("summary"),
                    "description": item.get("description"),
                    "timezone": item.get("timeZone"),
                    "primary": item.get("primary", False),
                    "access_role": item.get("accessRole"),
                    "background_color": item.get("backgroundColor"),
                    "foreground_color": item.get("foregroundColor")
                })

            logger.info(f"Retrieved {len(calendars)} calendars for firm {firm_id}")
            return calendars

        try:
            # Execute with rate limit handling
            return await self.execute_with_rate_limit(_get_calendars_impl)

        except AuthenticationError:
            logger.error(
                f"Authentication error when getting calendars for firm {firm_id}"
            )
            raise
        except RateLimitError:
            logger.error(
                f"Rate limit exceeded when getting calendars for firm {firm_id}"
            )
            raise
        except Exception as e:
            logger.error(f"Error getting calendars for firm {firm_id}: {str(e)}")
            raise CalendarProviderError(f"Failed to get calendars: {str(e)}")

    async def get_events(
        self,
        firm_id: str,
        calendar_id: str,
        start_time: datetime,
        end_time: datetime,
        max_results: int = 100
    ) -> List[CalendarEvent]:
        """
        Get events from a calendar within a time range.

        Args:
            firm_id: The firm/tenant ID
            calendar_id: The calendar ID
            start_time: Start of the time range
            end_time: End of the time range
            max_results: Maximum number of events to return (default: 100)

        Returns:
            List[CalendarEvent]: List of calendar events

        Raises:
            ValidationError: If input parameters are invalid
            AuthenticationError: If authentication fails
            ResourceNotFoundError: If calendar is not found
            RateLimitError: If rate limit is exceeded
            CalendarProviderError: For other provider-specific errors
        """
        # Validate input parameters
        if not firm_id:
            raise ValidationError("firm_id is required")
        if not calendar_id:
            raise ValidationError("calendar_id is required")
        if not start_time:
            raise ValidationError("start_time is required")
        if not end_time:
            raise ValidationError("end_time is required")
        if start_time >= end_time:
            raise ValidationError("start_time must be before end_time")
        if max_results <= 0:
            raise ValidationError("max_results must be positive")

        logger.debug(
            f"Getting events for firm {firm_id}, calendar {calendar_id} "
            f"from {start_time} to {end_time}"
        )

        async def _get_events_impl() -> List[CalendarEvent]:
            # Format time parameters for Google API
            time_min = start_time.isoformat()
            time_max = end_time.isoformat()

            # Get events from Google API
            response = await self.client.get_events(
                firm_id=firm_id,
                calendar_id=calendar_id,
                time_min=time_min,
                time_max=time_max,
                max_results=max_results
            )

            # Map Google events to our model
            events = []
            for item in response.get("items", []):
                try:
                    event = map_google_event_to_calendar_event(
                        google_event=item,
                        calendar_id=calendar_id,
                        provider_id=self.provider_id
                    )
                    events.append(event)
                except Exception as e:
                    logger.warning(f"Failed to map Google event: {str(e)}")
                    # Continue processing other events
                    continue

            logger.info(
                f"Retrieved {len(events)} events for firm {firm_id}, "
                f"calendar {calendar_id}"
            )
            return events

        try:
            # Execute with rate limit handling
            return await self.execute_with_rate_limit(_get_events_impl)

        except AuthenticationError:
            logger.error(f"Authentication error when getting events for firm {firm_id}")
            raise
        except ResourceNotFoundError:
            logger.error(f"Calendar {calendar_id} not found for firm {firm_id}")
            raise
        except RateLimitError:
            logger.error(f"Rate limit exceeded when getting events for firm {firm_id}")
            raise
        except Exception as e:
            logger.error(
                f"Error getting events for firm {firm_id}, calendar "
                f"{calendar_id}: {str(e)}"
            )
            raise CalendarProviderError(f"Failed to get events: {str(e)}")

    async def get_event(
        self,
        firm_id: str,
        calendar_id: str,
        event_id: str
    ) -> CalendarEvent:
        """
        Get a specific event by ID.

        Args:
            firm_id: The firm/tenant ID
            calendar_id: The calendar ID
            event_id: The event ID

        Returns:
            CalendarEvent: Calendar event details

        Raises:
            ValidationError: If input parameters are invalid
            AuthenticationError: If authentication fails
            ResourceNotFoundError: If event is not found
            RateLimitError: If rate limit is exceeded
            CalendarProviderError: For other provider-specific errors
        """
        # Validate input parameters
        if not firm_id:
            raise ValidationError("firm_id is required")
        if not calendar_id:
            raise ValidationError("calendar_id is required")
        if not event_id:
            raise ValidationError("event_id is required")

        logger.debug(
            f"Getting event {event_id} for firm {firm_id}, calendar {calendar_id}"
        )

        async def _get_event_impl() -> CalendarEvent:
            # Get event from Google API
            google_event = await self.client.get_event(
                firm_id=firm_id,
                calendar_id=calendar_id,
                event_id=event_id
            )

            # Map Google event to our model
            event = map_google_event_to_calendar_event(
                google_event=google_event,
                calendar_id=calendar_id,
                provider_id=self.provider_id
            )

            logger.info(
                f"Retrieved event {event_id} for firm {firm_id}, "
                f"calendar {calendar_id}"
            )
            return event

        try:
            # Execute with rate limit handling
            return await self.execute_with_rate_limit(_get_event_impl)

        except AuthenticationError:
            logger.error(
                f"Authentication error when getting event {event_id} for "
                f"firm {firm_id}"
            )
            raise
        except ResourceNotFoundError:
            logger.error(
                f"Event {event_id} not found for firm {firm_id}, "
                f"calendar {calendar_id}"
            )
            raise
        except RateLimitError:
            logger.error(
                f"Rate limit exceeded when getting event {event_id} for "
                f"firm {firm_id}"
            )
            raise
        except Exception as e:
            logger.error(
                f"Error getting event {event_id} for firm {firm_id}, "
                f"calendar {calendar_id}: {str(e)}"
            )
            raise CalendarProviderError(f"Failed to get event: {str(e)}")

    async def create_event(
        self,
        firm_id: str,
        calendar_id: str,
        event: CalendarEventCreate
    ) -> CalendarEvent:
        """
        Create a new event in the calendar.

        Args:
            firm_id: The firm/tenant ID
            calendar_id: The calendar ID
            event: The event to create

        Returns:
            CalendarEvent: Created calendar event with provider-specific IDs

        Raises:
            ValidationError: If input parameters are invalid
            AuthenticationError: If authentication fails
            ResourceNotFoundError: If calendar is not found
            PermissionError: If user doesn't have permission
            RateLimitError: If rate limit is exceeded
            CalendarProviderError: For other provider-specific errors
        """
        # Validate input parameters
        if not firm_id:
            raise ValidationError("firm_id is required")
        if not calendar_id:
            raise ValidationError("calendar_id is required")
        if not event:
            raise ValidationError("event is required")
        if not event.summary:
            raise ValidationError("event.summary is required")
        if not event.start_time:
            raise ValidationError("event.start_time is required")
        if not event.end_time:
            raise ValidationError("event.end_time is required")
        if event.start_time >= event.end_time:
            raise ValidationError("event.start_time must be before event.end_time")

        logger.debug(f"Creating event for firm {firm_id}, calendar {calendar_id}")

        async def _create_event_impl() -> CalendarEvent:
            # Map our model to Google event
            google_event = map_calendar_event_to_google_event(event)

            # Create event in Google API
            created_event = await self.client.create_event(
                firm_id=firm_id,
                calendar_id=calendar_id,
                event=google_event
            )

            # Map created Google event back to our model
            result = map_google_event_to_calendar_event(
                google_event=created_event,
                calendar_id=calendar_id,
                provider_id=self.provider_id
            )

            logger.info(
                f"Created event {result.id} for firm {firm_id}, "
                f"calendar {calendar_id}"
            )
            return result

        try:
            # Execute with rate limit handling
            return await self.execute_with_rate_limit(_create_event_impl)

        except AuthenticationError:
            logger.error(f"Authentication error when creating event for firm {firm_id}")
            raise
        except ResourceNotFoundError:
            logger.error(f"Calendar {calendar_id} not found for firm {firm_id}")
            raise
        except PermissionError:
            logger.error(
                f"Permission error when creating event for firm {firm_id}, "
                f"calendar {calendar_id}"
            )
            raise
        except RateLimitError:
            logger.error(f"Rate limit exceeded when creating event for firm {firm_id}")
            raise
        except Exception as e:
            logger.error(
                f"Error creating event for firm {firm_id}, calendar "
                f"{calendar_id}: {str(e)}"
            )
            raise CalendarProviderError(f"Failed to create event: {str(e)}")

    async def update_event(
        self,
        firm_id: str,
        calendar_id: str,
        event_id: str,
        event: CalendarEventUpdate
    ) -> CalendarEvent:
        """
        Update an existing event in the calendar.

        Args:
            firm_id: The firm/tenant ID
            calendar_id: The calendar ID
            event_id: The event ID
            event: The updated event data

        Returns:
            CalendarEvent: Updated calendar event

        Raises:
            ValidationError: If input parameters are invalid
            AuthenticationError: If authentication fails
            ResourceNotFoundError: If event is not found
            PermissionError: If user doesn't have permission
            RateLimitError: If rate limit is exceeded
            CalendarProviderError: For other provider-specific errors
        """
        # Validate input parameters
        if not firm_id:
            raise ValidationError("firm_id is required")
        if not calendar_id:
            raise ValidationError("calendar_id is required")
        if not event_id:
            raise ValidationError("event_id is required")
        if not event:
            raise ValidationError("event is required")
        if event.start_time and event.end_time and event.start_time >= event.end_time:
            raise ValidationError("event.start_time must be before event.end_time")

        logger.debug(
            f"Updating event {event_id} for firm {firm_id}, calendar {calendar_id}"
        )

        async def _update_event_impl() -> CalendarEvent:
            # First get the existing event to ensure it exists and to merge updates
            existing_google_event = await self.client.get_event(
                firm_id=firm_id,
                calendar_id=calendar_id,
                event_id=event_id
            )

            # Map update to Google event format
            google_event = map_calendar_event_to_google_event(
                event, existing_event=existing_google_event
            )

            # Update event in Google API
            updated_event = await self.client.update_event(
                firm_id=firm_id,
                calendar_id=calendar_id,
                event_id=event_id,
                event=google_event
            )

            # Map updated Google event back to our model
            result = map_google_event_to_calendar_event(
                google_event=updated_event,
                calendar_id=calendar_id,
                provider_id=self.provider_id
            )

            logger.info(
                f"Updated event {event_id} for firm {firm_id}, "
                f"calendar {calendar_id}"
            )
            return result

        try:
            # Execute with rate limit handling
            return await self.execute_with_rate_limit(_update_event_impl)

        except AuthenticationError:
            logger.error(
                f"Authentication error when updating event {event_id} for "
                f"firm {firm_id}"
            )
            raise
        except ResourceNotFoundError:
            logger.error(
                f"Event {event_id} not found for firm {firm_id}, "
                f"calendar {calendar_id}"
            )
            raise
        except PermissionError:
            logger.error(
                f"Permission error when updating event {event_id} for "
                f"firm {firm_id}"
            )
            raise
        except RateLimitError:
            logger.error(
                f"Rate limit exceeded when updating event {event_id} for "
                f"firm {firm_id}"
            )
            raise
        except Exception as e:
            logger.error(
                f"Error updating event {event_id} for firm {firm_id}, "
                f"calendar {calendar_id}: {str(e)}"
            )
            raise CalendarProviderError(f"Failed to update event: {str(e)}")

    async def delete_event(
        self,
        firm_id: str,
        calendar_id: str,
        event_id: str
    ) -> bool:
        """
        Delete an event from the calendar.

        Args:
            firm_id: The firm/tenant ID
            calendar_id: The calendar ID
            event_id: The event ID

        Returns:
            bool: True if deletion was successful

        Raises:
            ValidationError: If input parameters are invalid
            AuthenticationError: If authentication fails
            ResourceNotFoundError: If event is not found
            PermissionError: If user doesn't have permission
            RateLimitError: If rate limit is exceeded
            CalendarProviderError: For other provider-specific errors
        """
        # Validate input parameters
        if not firm_id:
            raise ValidationError("firm_id is required")
        if not calendar_id:
            raise ValidationError("calendar_id is required")
        if not event_id:
            raise ValidationError("event_id is required")

        logger.debug(
            f"Deleting event {event_id} for firm {firm_id}, calendar {calendar_id}"
        )

        async def _delete_event_impl() -> bool:
            # Delete event in Google API
            success = await self.client.delete_event(
                firm_id=firm_id,
                calendar_id=calendar_id,
                event_id=event_id
            )

            logger.info(
                f"Deleted event {event_id} for firm {firm_id}, "
                f"calendar {calendar_id}"
            )
            return success

        try:
            # Execute with rate limit handling
            return await self.execute_with_rate_limit(_delete_event_impl)

        except AuthenticationError:
            logger.error(
                f"Authentication error when deleting event {event_id} for "
                f"firm {firm_id}"
            )
            raise
        except ResourceNotFoundError:
            logger.error(
                f"Event {event_id} not found for firm {firm_id}, "
                f"calendar {calendar_id}"
            )
            raise
        except PermissionError:
            logger.error(
                f"Permission error when deleting event {event_id} for "
                f"firm {firm_id}"
            )
            raise
        except RateLimitError:
            logger.error(
                f"Rate limit exceeded when deleting event {event_id} for "
                f"firm {firm_id}"
            )
            raise
        except Exception as e:
            logger.error(
                f"Error deleting event {event_id} for firm {firm_id}, "
                f"calendar {calendar_id}: {str(e)}"
            )
            raise CalendarProviderError(f"Failed to delete event: {str(e)}")

    async def check_free_busy(
        self,
        firm_id: str,
        request: FreeBusyRequest
    ) -> FreeBusyResponse:
        """
        Check free/busy times for calendars.

        Args:
            firm_id: The firm/tenant ID
            request: Free/busy request parameters

        Returns:
            FreeBusyResponse: Free/busy information for the requested calendars
                and time range

        Raises:
            ValidationError: If input parameters are invalid
            AuthenticationError: If authentication fails
            ResourceNotFoundError: If calendar is not found
            RateLimitError: If rate limit is exceeded
            CalendarProviderError: For other provider-specific errors
        """
        # Validate input parameters
        if not firm_id:
            raise ValidationError("firm_id is required")
        if not request:
            raise ValidationError("request is required")
        if not request.calendar_ids:
            raise ValidationError("request.calendar_ids must not be empty")
        if not request.start_time:
            raise ValidationError("request.start_time is required")
        if not request.end_time:
            raise ValidationError("request.end_time is required")
        if request.start_time >= request.end_time:
            raise ValidationError("request.start_time must be before request.end_time")

        logger.debug(
            f"Checking free/busy for firm {firm_id}, calendars "
            f"{request.calendar_ids}"
        )

        async def _check_free_busy_impl() -> FreeBusyResponse:
            # Format time parameters for Google API
            time_min = request.start_time.isoformat()
            time_max = request.end_time.isoformat()

            # Check free/busy in Google API
            response = await self.client.check_free_busy(
                firm_id=firm_id,
                calendar_ids=request.calendar_ids,
                time_min=time_min,
                time_max=time_max,
                timezone=request.timezone
            )

            # Map Google response to our model
            calendars = {}
            for calendar_id, calendar_data in response.get("calendars", {}).items():
                busy_slots = []
                for busy in calendar_data.get("busy", []):
                    start = datetime.fromisoformat(busy["start"].replace("Z", "+00:00"))
                    end = datetime.fromisoformat(busy["end"].replace("Z", "+00:00"))
                    busy_slots.append(TimeSlot(start=start, end=end))
                calendars[calendar_id] = busy_slots

            # Create response
            result = FreeBusyResponse(
                calendars=calendars,
                time_min=request.start_time,
                time_max=request.end_time
            )

            logger.info(
                f"Checked free/busy for firm {firm_id}, calendars "
                f"{request.calendar_ids}"
            )
            return result

        try:
            # Execute with rate limit handling
            return await self.execute_with_rate_limit(_check_free_busy_impl)

        except AuthenticationError:
            logger.error(
                f"Authentication error when checking free/busy for firm {firm_id}"
            )
            raise
        except ResourceNotFoundError:
            logger.error(f"Calendar not found for firm {firm_id}")
            raise
        except RateLimitError:
            logger.error(
                f"Rate limit exceeded when checking free/busy for firm {firm_id}"
            )
            raise
        except Exception as e:
            logger.error(f"Error checking free/busy for firm {firm_id}: {str(e)}")
            raise CalendarProviderError(f"Failed to check free/busy: {str(e)}")
