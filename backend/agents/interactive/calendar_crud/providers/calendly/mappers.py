"""
Mappers for Calendly provider.

This module provides functions for mapping between Calendly API models
and our internal models.
"""

from datetime import datetime
from typing import Dict, List, Any, Optional

from ..models import (
    CalendarEvent,
    CalendarEventCreate,
    EventStatus,
    Attendee
)

def map_calendly_event_to_calendar_event(
    calendly_event: Dict[str, Any],
    calendar_id: str,
    provider_id: str
) -> CalendarEvent:
    """
    Map a Calendly event to a CalendarEvent.
    
    Args:
        calendly_event: The Calendly event
        calendar_id: The calendar ID
        provider_id: The provider ID
        
    Returns:
        CalendarEvent: The mapped calendar event
    """
    # Extract event ID from URI
    event_uri = calendly_event.get("uri", "")
    event_id = event_uri.split("/")[-1] if event_uri else ""
    
    # Extract start and end times
    start_time = datetime.fromisoformat(
        calendly_event.get("start_time", "").replace("Z", "+00:00")
    )
    end_time = datetime.fromisoformat(
        calendly_event.get("end_time", "").replace("Z", "+00:00")
    )
    
    # Extract created and updated times
    created_at = datetime.fromisoformat(
        calendly_event.get("created_at", "").replace("Z", "+00:00")
    )
    updated_at = (
        datetime.fromisoformat(
            calendly_event.get("updated_at", "").replace("Z", "+00:00")
        )
        if calendly_event.get("updated_at")
        else None
    )
    
    # Map status
    status_map = {
        "active": EventStatus.CONFIRMED,
        "canceled": EventStatus.CANCELLED
    }
    status = status_map.get(
        calendly_event.get("status", "active"), EventStatus.CONFIRMED
    )
    
    # Extract location
    location = None
    location_data = calendly_event.get("location", {})
    if location_data:
        if location_data.get("type") == "physical":
            location = location_data.get("location")
        elif location_data.get("type") == "custom":
            location = location_data.get("location")
        elif location_data.get("type") in [
            "zoom",
            "google_meet",
            "microsoft_teams",
            "webex",
        ]:
            location = f"{location_data.get('type').replace('_', ' ').title()} Meeting"
    
    # Extract attendees
    attendees = []
    invitees_counter = calendly_event.get("invitees_counter", {})
    if invitees_counter and invitees_counter.get("total") > 0:
        # We don't have detailed invitee information in this response
        # We would need to make additional API calls to get this information
        # For now, we'll just create a placeholder attendee
        attendees.append(
            Attendee(
                email="<EMAIL>",
                name="Invitee",
                response_status="accepted",
                is_organizer=False
            )
        )
    
    # Create organizer attendee
    organizer = Attendee(
        email=calendly_event.get("organizer", {}).get("email", ""),
        name=calendly_event.get("organizer", {}).get("name", ""),
        response_status="accepted",
        is_organizer=True
    )
    
    # Create event
    return CalendarEvent(
        id=event_id,
        calendar_id=calendar_id,
        provider_id=provider_id,
        provider_event_link=calendly_event.get("scheduling_url"),
        summary=calendly_event.get("name", ""),
        description=calendly_event.get("description", ""),
        location=location,
        start_time=start_time,
        end_time=end_time,
        all_day=False,  # Calendly doesn't support all-day events
        attendees=attendees,
        status=status,
        created_at=created_at,
        updated_at=updated_at,
        organizer=organizer,
        metadata={
            "event_type": calendly_event.get("event_type"),
            "event_type_name": calendly_event.get("event_type_name"),
            "event_memberships": calendly_event.get("event_memberships"),
            "cancellation": calendly_event.get("cancellation"),
            "location": calendly_event.get("location")
        }
    )

def map_calendar_event_to_calendly_event(event: CalendarEventCreate) -> Dict[str, Any]:
    """
    Map a CalendarEventCreate to a Calendly event.
    
    Note: Calendly API does not support creating events programmatically.
    This function is a placeholder for future implementation.
    
    Args:
        event: The calendar event to create
        
    Returns:
        Dict[str, Any]: The mapped Calendly event
    """
    # This is a placeholder since Calendly API doesn't support creating events
    # programmatically
    return {
        "name": event.summary,
        "description": event.description,
        "start_time": event.start_time.isoformat(),
        "end_time": event.end_time.isoformat()
    }
