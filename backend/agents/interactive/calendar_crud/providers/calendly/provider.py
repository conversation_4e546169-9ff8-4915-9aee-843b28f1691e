"""
Calendly Provider implementation.

This module provides the CalendlyProvider class that implements
the CalendarProvider interface for Calendly.
"""

import logging
from datetime import datetime
from typing import Dict, List, Optional, Any, cast

from ..base import CalendarProvider, ProviderCapability
from ..models import (
    CalendarEvent,
    TimeSlot,
    CalendarEventCreate,
    CalendarEventUpdate,
    FreeBusyRequest,
    FreeBusyResponse,
    Attendee,
    EventStatus
)
from ..exceptions import (
    CalendarProviderError,
    AuthenticationError,
    ResourceNotFoundError,
    PermissionError,
    RateLimitError,
    ValidationError
)
from .client import CalendlyClient
from .mappers import (
    map_calendly_event_to_calendar_event,
    map_calendar_event_to_calendly_event
)

# Configure logging
logger = logging.getLogger(__name__)

class CalendlyProvider(CalendarProvider):
    """
    Calendly provider implementation.

    This class implements the CalendarProvider interface for Calendly,
    providing methods to interact with Calendly API for event management.

    Attributes:
        client: The Calendly API client
    """

    def __init__(self) -> None:
        """Initialize the Calendly provider with API client."""
        self.client = CalendlyClient()

    @property
    def provider_id(self) -> str:
        """
        Get the unique identifier for the provider.

        Returns:
            str: The provider ID ('calendly')
        """
        return "calendly"

    @property
    def provider_name(self) -> str:
        """
        Get the human-readable name of the provider.

        Returns:
            str: The provider name ('Calendly')
        """
        return "Calendly"

    @property
    def capabilities(self) -> List[ProviderCapability]:
        """
        Get the list of capabilities supported by this provider.

        Returns:
            List[ProviderCapability]: The supported capabilities
        """
        return [
            ProviderCapability.READ_EVENT,
            ProviderCapability.FREE_BUSY,
            ProviderCapability.LIST_CALENDARS,
            ProviderCapability.WEBHOOK_SUPPORT
        ]

    async def get_calendars(self, firm_id: str) -> List[Dict[str, Any]]:
        """
        Get a list of calendars available for the firm.

        For Calendly, this returns a list of event types as "calendars".

        Args:
            firm_id: The firm/tenant ID

        Returns:
            List[Dict[str, Any]]: List of calendar objects with id, name,
                description, etc.

        Raises:
            AuthenticationError: If authentication fails
            RateLimitError: If rate limit is exceeded
            CalendarProviderError: For other provider-specific errors
        """
        logger.debug(f"Getting calendars for firm {firm_id}")

        try:
            # Get user information
            user_response = await self.client.get_user(firm_id)
            user_uri = user_response.get("resource", {}).get("uri")

            if not user_uri:
                raise CalendarProviderError("Failed to get user URI")

            # Get event types
            event_types_response = await self.client.get_user_event_types(
                firm_id, user_uri
            )

            # Map response to our model
            calendars = []
            for item in event_types_response.get("collection", []):
                calendars.append({
                    "id": item.get("uri"),
                    "name": item.get("name"),
                    "description": item.get("description"),
                    "duration": item.get("duration"),
                    "kind": item.get("kind"),
                    "slug": item.get("slug"),
                    "active": item.get("active", True),
                    "scheduling_url": item.get("scheduling_url")
                })

            logger.info(f"Retrieved {len(calendars)} calendars for firm {firm_id}")
            return calendars
        except AuthenticationError:
            logger.error(
                f"Authentication error when getting calendars for firm {firm_id}"
            )
            raise
        except RateLimitError:
            logger.error(
                f"Rate limit exceeded when getting calendars for firm {firm_id}"
            )
            raise
        except Exception as e:
            logger.error(f"Error getting calendars for firm {firm_id}: {str(e)}")
            raise CalendarProviderError(f"Failed to get calendars: {str(e)}")

    async def get_events(
        self,
        firm_id: str,
        calendar_id: str,
        start_time: datetime,
        end_time: datetime,
        max_results: int = 100
    ) -> List[CalendarEvent]:
        """
        Get events from a calendar within a time range.

        Args:
            firm_id: The firm/tenant ID
            calendar_id: The calendar ID (event type URI)
            start_time: Start of the time range
            end_time: End of the time range
            max_results: Maximum number of events to return

        Returns:
            List[CalendarEvent]: List of calendar events

        Raises:
            AuthenticationError: If authentication fails
            ResourceNotFoundError: If calendar is not found
            RateLimitError: If rate limit is exceeded
            CalendarProviderError: For other provider-specific errors
        """
        logger.debug(f"Getting events for firm {firm_id}, calendar {calendar_id}")

        try:
            # Get user information
            user_response = await self.client.get_user(firm_id)
            user_uri = user_response.get("resource", {}).get("uri")

            if not user_uri:
                raise CalendarProviderError("Failed to get user URI")

            # Get scheduled events
            events_response = await self.client.get_scheduled_events(
                firm_id=firm_id,
                user_uri=user_uri,
                min_start_time=start_time,
                max_start_time=end_time,
                status="active",
                count=max_results
            )

            # Filter events by calendar_id (event type URI)
            filtered_events = []
            for item in events_response.get("collection", []):
                event_type_uri = item.get("event_type")
                if event_type_uri == calendar_id:
                    filtered_events.append(item)

            # Map Calendly events to our model
            events = []
            for item in filtered_events:
                try:
                    event = map_calendly_event_to_calendar_event(
                        calendly_event=item,
                        calendar_id=calendar_id,
                        provider_id=self.provider_id
                    )
                    events.append(event)
                except Exception as e:
                    logger.warning(f"Failed to map Calendly event: {str(e)}")
                    # Continue processing other events
                    continue

            logger.info(
                f"Retrieved {len(events)} events for firm {firm_id}, "
                f"calendar {calendar_id}"
            )
            return events
        except AuthenticationError:
            logger.error(f"Authentication error when getting events for firm {firm_id}")
            raise
        except ResourceNotFoundError:
            logger.error(f"Calendar not found for firm {firm_id}")
            raise
        except RateLimitError:
            logger.error(f"Rate limit exceeded when getting events for firm {firm_id}")
            raise
        except Exception as e:
            logger.error(f"Error getting events for firm {firm_id}: {str(e)}")
            raise CalendarProviderError(f"Failed to get events: {str(e)}")

    async def get_event(
        self,
        firm_id: str,
        calendar_id: str,
        event_id: str
    ) -> CalendarEvent:
        """
        Get a single event by ID.

        Args:
            firm_id: The firm/tenant ID
            calendar_id: The calendar ID
            event_id: The event ID

        Returns:
            CalendarEvent: The calendar event

        Raises:
            AuthenticationError: If authentication fails
            ResourceNotFoundError: If event is not found
            RateLimitError: If rate limit is exceeded
            CalendarProviderError: For other provider-specific errors
        """
        logger.debug(
            f"Getting event {event_id} for firm {firm_id}, calendar {calendar_id}"
        )

        try:
            # Make request to get the event
            event_response = await self.client._make_request(
                method="GET",
                endpoint=f"/v2/scheduled_events/{event_id}",
                firm_id=firm_id
            )

            # Map Calendly event to our model
            event = map_calendly_event_to_calendar_event(
                calendly_event=event_response.get("resource", {}),
                calendar_id=calendar_id,
                provider_id=self.provider_id
            )

            logger.info(
                f"Retrieved event {event_id} for firm {firm_id}, "
                f"calendar {calendar_id}"
            )
            return event
        except AuthenticationError:
            logger.error(
                f"Authentication error when getting event {event_id} for "
                f"firm {firm_id}"
            )
            raise
        except ResourceNotFoundError:
            logger.error(f"Event {event_id} not found for firm {firm_id}")
            raise
        except RateLimitError:
            logger.error(
                f"Rate limit exceeded when getting event {event_id} for "
                f"firm {firm_id}"
            )
            raise
        except Exception as e:
            logger.error(f"Error getting event {event_id} for firm {firm_id}: {str(e)}")
            raise CalendarProviderError(f"Failed to get event: {str(e)}")

    async def create_event(
        self,
        firm_id: str,
        calendar_id: str,
        event: CalendarEventCreate
    ) -> CalendarEvent:
        """
        Create a new event in the calendar.

        Note: Calendly API does not support creating events programmatically.
        This method raises an UnsupportedOperationError.

        Args:
            firm_id: The firm/tenant ID
            calendar_id: The calendar ID
            event: The event to create

        Raises:
            UnsupportedOperationError: Calendly API does not support creating
                events programmatically
        """
        # Check if this provider supports creating events
        self.require_capability(ProviderCapability.CREATE_EVENT)

    async def update_event(
        self,
        firm_id: str,
        calendar_id: str,
        event_id: str,
        event: CalendarEventUpdate
    ) -> CalendarEvent:
        """
        Update an existing event in the calendar.

        Note: Calendly API does not support updating events programmatically.
        This method raises an UnsupportedOperationError.

        Args:
            firm_id: The firm/tenant ID
            calendar_id: The calendar ID
            event_id: The event ID
            event: The event updates

        Raises:
            UnsupportedOperationError: Calendly API does not support updating
                events programmatically
        """
        # Check if this provider supports updating events
        self.require_capability(ProviderCapability.UPDATE_EVENT)

    async def delete_event(
        self,
        firm_id: str,
        calendar_id: str,
        event_id: str
    ) -> bool:
        """
        Delete an event from the calendar.

        Note: Calendly API does not support deleting events programmatically.
        This method raises an UnsupportedOperationError.

        Args:
            firm_id: The firm/tenant ID
            calendar_id: The calendar ID
            event_id: The event ID

        Returns:
            bool: True if deletion was successful

        Raises:
            UnsupportedOperationError: Calendly API does not support deleting
                events programmatically
        """
        # Check if this provider supports deleting events
        self.require_capability(ProviderCapability.DELETE_EVENT)

    async def check_free_busy(
        self,
        firm_id: str,
        request: FreeBusyRequest
    ) -> FreeBusyResponse:
        """
        Check free/busy times for calendars.

        Args:
            firm_id: The firm/tenant ID
            request: Free/busy request parameters

        Returns:
            FreeBusyResponse: Free/busy information for the requested calendars
                and time range

        Raises:
            AuthenticationError: If authentication fails
            ResourceNotFoundError: If calendar is not found
            RateLimitError: If rate limit is exceeded
            CalendarProviderError: For other provider-specific errors
        """
        logger.debug(
            f"Checking free/busy for firm {firm_id}, calendars "
            f"{request.calendar_ids}"
        )

        try:
            # Get user information
            user_response = await self.client.get_user(firm_id)
            user_uri = user_response.get("resource", {}).get("uri")

            if not user_uri:
                raise CalendarProviderError("Failed to get user URI")

            # Get scheduled events
            events_response = await self.client.get_scheduled_events(
                firm_id=firm_id,
                user_uri=user_uri,
                min_start_time=request.start_time,
                max_start_time=request.end_time,
                status="active"
            )

            # Map response to our model
            calendars = {}
            for calendar_id in request.calendar_ids:
                # Filter events by calendar_id (event type URI)
                busy_slots = []
                for item in events_response.get("collection", []):
                    event_type_uri = item.get("event_type")
                    if event_type_uri == calendar_id:
                        start = datetime.fromisoformat(
                            item.get("start_time").replace("Z", "+00:00")
                        )
                        end = datetime.fromisoformat(
                            item.get("end_time").replace("Z", "+00:00")
                        )
                        busy_slots.append(TimeSlot(start=start, end=end))

                calendars[calendar_id] = busy_slots

            # Create response
            result = FreeBusyResponse(
                calendars=calendars,
                time_min=request.start_time,
                time_max=request.end_time
            )

            logger.info(
                f"Checked free/busy for firm {firm_id}, calendars "
                f"{request.calendar_ids}"
            )
            return result
        except AuthenticationError:
            logger.error(
                f"Authentication error when checking free/busy for firm {firm_id}"
            )
            raise
        except ResourceNotFoundError:
            logger.error(f"Calendar not found for firm {firm_id}")
            raise
        except RateLimitError:
            logger.error(
                f"Rate limit exceeded when checking free/busy for firm {firm_id}"
            )
            raise
        except Exception as e:
            logger.error(f"Error checking free/busy for firm {firm_id}: {str(e)}")
            raise CalendarProviderError(f"Failed to check free/busy: {str(e)}")
