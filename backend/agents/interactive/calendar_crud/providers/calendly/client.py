"""
Calendly API client.

This module provides a client for interacting with the Calendly API.
"""

import logging
import json
from typing import Dict, List, Optional, Any, Union
from datetime import datetime, timedelta

from backend.config import settings
from shared.http import (
    SharedHTTPClient,
    HTTPNonRetryableError,
    HTTPRetryableError,
    HTTPClientError,
)
from shared.auth_client import get_access_token_with_retry
from ..exceptions import (
    CalendarProviderError,
    AuthenticationError,
    ResourceNotFoundError,
    PermissionError,
    RateLimitError,
    ValidationError,
    NetworkError,
    ServiceUnavailableError,
    ProviderAuthError
)

# Configure logging
logger = logging.getLogger(__name__)

# In-memory token cache with TTL
_token_cache: Dict[str, Dict[str, Any]] = {}

class CalendlyClient:
    """
    Client for interacting with the Calendly API.

    This class provides methods for making API requests to Calendly.
    It fetches tokens from the auth-service and caches them in memory with a
    250-second TTL.
    """

    BASE_URL = "https://api.calendly.com"

    async def _get_token(self, firm_id: str) -> str:
        """
        Get an access token for the Calendly API with retry logic.

        Uses the shared auth client with exponential backoff retry logic
        to handle auth-service outages and rate limits gracefully.

        Args:
            firm_id: The firm/tenant ID

        Returns:
            str: Access token

        Raises:
            ProviderAuthError: If auth-service returns non-retryable errors
            AuthenticationError: If authentication fails after retries
        """
        try:
            return await get_access_token_with_retry(firm_id, "calendly")
        except Exception as e:
            logger.error(f"Failed to get Calendly token for firm {firm_id}: {e}")
            raise

    async def _make_request(
        self,
        method: str,
        endpoint: str,
        firm_id: str,
        params: Optional[Dict[str, Any]] = None,
        data: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """
        Make an API request to Calendly.

        Args:
            method: HTTP method (GET, POST, etc.)
            endpoint: API endpoint
            firm_id: The firm/tenant ID
            params: Query parameters
            data: Request body

        Returns:
            Dict[str, Any]: API response

        Raises:
            AuthenticationError: If authentication fails
            ResourceNotFoundError: If resource is not found
            PermissionError: If user doesn't have permission
            RateLimitError: If rate limit is exceeded
            NetworkError: If network error occurs
            ServiceUnavailableError: If service is unavailable
            CalendarProviderError: For other provider-specific errors
        """
        # Get access token
        token = await self._get_token(firm_id)

        # Prepare headers
        headers = {
            "Authorization": f"Bearer {token}",
            "Content-Type": "application/json"
        }

        # Build URL
        url = f"{self.BASE_URL}{endpoint}"

        try:
            # Log request (without sensitive data)
            logger.debug(
                f"Making {method} request to {url} with params: "
                f"{json.dumps(params) if params else 'None'}"
            )

            # Make request using SharedHTTPClient
            async with SharedHTTPClient() as client:
                response = await client.request(
                    method=method,
                    url=url,
                    headers=headers,
                    params=params,
                    json=data
                )
                return response

        except HTTPNonRetryableError as e:
            # Handle 4xx errors
            if "401" in str(e):
                logger.error(f"Authentication error for Calendly API: {e}")
                raise AuthenticationError(f"Authentication failed: {e}")
            elif "403" in str(e):
                logger.error(f"Permission error for Calendly API: {e}")
                raise PermissionError(f"Permission denied: {e}")
            elif "404" in str(e):
                logger.error(f"Resource not found for Calendly API: {e}")
                raise ResourceNotFoundError(f"Resource not found: {e}")
            elif "429" in str(e):
                logger.error(f"Rate limit exceeded for Calendly API: {e}")
                raise RateLimitError(f"Rate limit exceeded: {e}")
            else:
                logger.error(f"Client error for Calendly API: {e}")
                raise CalendarProviderError(f"Client error: {e}")

        except (HTTPRetryableError, HTTPClientError) as e:
            logger.error(f"Network/server error when calling Calendly API: {e}")
            raise NetworkError(f"Error communicating with Calendly: {e}")

        except Exception as e:
            logger.error(f"Unexpected error when calling Calendly API: {e}")
            raise CalendarProviderError(f"Error calling Calendly API: {e}")

    async def get_user(self, firm_id: str) -> Dict[str, Any]:
        """
        Get the current user.

        Args:
            firm_id: The firm/tenant ID

        Returns:
            Dict[str, Any]: User information

        Raises:
            AuthenticationError: If authentication fails
            CalendarProviderError: For other provider-specific errors
        """
        return await self._make_request("GET", "/v2/users/me", firm_id)

    async def get_user_event_types(self, firm_id: str, user_uri: str) -> Dict[str, Any]:
        """
        Get event types for a user.

        Args:
            firm_id: The firm/tenant ID
            user_uri: The user URI

        Returns:
            Dict[str, Any]: Event types

        Raises:
            AuthenticationError: If authentication fails
            ResourceNotFoundError: If user is not found
            CalendarProviderError: For other provider-specific errors
        """
        params = {"user": user_uri}
        return await self._make_request(
            "GET", "/v2/event_types", firm_id, params=params
        )

    async def get_scheduled_events(
        self,
        firm_id: str,
        user_uri: str,
        min_start_time: Optional[datetime] = None,
        max_start_time: Optional[datetime] = None,
        status: Optional[str] = None,
        count: int = 100
    ) -> Dict[str, Any]:
        """
        Get scheduled events for a user.

        Args:
            firm_id: The firm/tenant ID
            user_uri: The user URI
            min_start_time: Minimum start time
            max_start_time: Maximum start time
            status: Event status (active, canceled)
            count: Maximum number of events to return

        Returns:
            Dict[str, Any]: Scheduled events

        Raises:
            AuthenticationError: If authentication fails
            ResourceNotFoundError: If user is not found
            CalendarProviderError: For other provider-specific errors
        """
        params = {
            "user": user_uri,
            "count": count
        }

        if min_start_time:
            params["min_start_time"] = min_start_time.isoformat()

        if max_start_time:
            params["max_start_time"] = max_start_time.isoformat()

        if status:
            params["status"] = status

        return await self._make_request(
            "GET", "/v2/scheduled_events", firm_id, params=params
        )

    async def get_event_invitees(self, firm_id: str, event_uri: str) -> Dict[str, Any]:
        """
        Get invitees for an event.

        Args:
            firm_id: The firm/tenant ID
            event_uri: The event URI

        Returns:
            Dict[str, Any]: Event invitees

        Raises:
            AuthenticationError: If authentication fails
            ResourceNotFoundError: If event is not found
            CalendarProviderError: For other provider-specific errors
        """
        params = {"event": event_uri}
        return await self._make_request("GET", "/v2/invitees", firm_id, params=params)
