"""
Calendar provider factory.

This module provides a factory for creating calendar provider instances
based on provider type.
"""

import os
import json
import logging
from typing import Dict, List, Type, Optional, Any

from .base import CalendarProvider
from .google import GoogleCalendarProvider
from .calendly import CalendlyProvider
from .outlook import OutlookProvider
from .exceptions import ConfigurationError

# Configure logging
logger = logging.getLogger(__name__)

# Flag for testing
_TESTING = False

# Path to the capability matrix JSON file
CAPABILITY_MATRIX_PATH = os.path.join(os.path.dirname(__file__), "capabilities.json")

def load_capability_matrix() -> Dict[str, Any]:
    """
    Load the provider capability matrix from the JSON file.

    Returns:
        Dict[str, Any]: The provider capability matrix

    Raises:
        ConfigurationError: If the capability matrix file is not found or is invalid
    """
    try:
        with open(CAPABILITY_MATRIX_PATH, "r") as f:
            return json.load(f)
    except FileNotFoundError:
        logger.error(f"Capability matrix file not found: {CAPABILITY_MATRIX_PATH}")
        raise ConfigurationError(
            f"Capability matrix file not found: {CAPABILITY_MATRIX_PATH}"
        )
    except json.JSONDecodeError as e:
        logger.error(f"Invalid capability matrix file: {str(e)}")
        raise ConfigurationError(f"Invalid capability matrix file: {str(e)}")

class CalendarProviderFactory:
    """
    Factory for creating calendar provider instances.

    This class provides methods for registering and creating calendar provider
    instances based on provider type.

    Attributes:
        _providers: Dictionary mapping provider IDs to provider classes
    """

    def __init__(self) -> None:
        """Initialize the calendar provider factory."""
        self._providers: Dict[str, Type[CalendarProvider]] = {}

        # Register built-in providers
        self.register_provider("google", GoogleCalendarProvider)
        self.register_provider("calendly", CalendlyProvider)
        self.register_provider("outlook", OutlookProvider)

    def register_provider(
        self, provider_id: str, provider_class: Type[CalendarProvider]
    ) -> None:
        """
        Register a calendar provider class.

        Args:
            provider_id: The provider ID
            provider_class: The provider class
        """
        self._providers[provider_id] = provider_class
        logger.debug(f"Registered calendar provider: {provider_id}")

    def get_provider(self, provider_id: str) -> CalendarProvider:
        """
        Get a calendar provider instance.

        Args:
            provider_id: The provider ID

        Returns:
            CalendarProvider: The calendar provider instance

        Raises:
            ConfigurationError: If the provider ID is not registered
        """
        if provider_id not in self._providers:
            raise ConfigurationError(f"Calendar provider not found: {provider_id}")

        provider_class = self._providers[provider_id]
        provider = provider_class()

        logger.debug(f"Created calendar provider instance: {provider_id}")
        return provider

    def get_available_providers(self) -> Dict[str, str]:
        """
        Get a dictionary of available providers.

        Returns:
            Dict[str, str]: Dictionary mapping provider IDs to provider names
        """
        return {
            provider_id: provider_class().provider_name
            for provider_id, provider_class in self._providers.items()
        }

    def get_provider_capabilities(self, provider_id: str) -> Dict[str, bool]:
        """
        Get the capabilities of a provider from the capability matrix.

        Args:
            provider_id: The provider ID

        Returns:
            Dict[str, bool]: Dictionary mapping capability names to boolean values

        Raises:
            ConfigurationError: If the provider is not found in the capability matrix
        """
        matrix = load_capability_matrix()

        if provider_id not in matrix:
            logger.error(f"Provider not found in capability matrix: {provider_id}")
            raise ConfigurationError(
                f"Provider not found in capability matrix: {provider_id}"
            )

        return matrix[provider_id]["capabilities"]

# Create a singleton instance
provider_factory = CalendarProviderFactory()

def get_provider(provider_id: str, firm_id: Optional[str] = None) -> CalendarProvider:
    """
    Get a calendar provider instance.

    This is a convenience function that uses the singleton factory instance.

    Args:
        provider_id: The provider ID
        firm_id: The firm/tenant ID (optional)

    Returns:
        CalendarProvider: The calendar provider instance

    Raises:
        ConfigurationError: If the provider ID is not registered
    """
    # For testing, use mock implementations
    if _TESTING:
        try:
            # Import mock implementations
            from ..tests.mocks.google_provider import MockGoogleProvider
            from ..tests.mocks.calendly_provider import MockCalendlyProvider

            if provider_id == "google":
                return MockGoogleProvider(firm_id or "test-firm-id")
            elif provider_id == "calendly":
                return MockCalendlyProvider(firm_id or "test-firm-id")
            else:
                raise ConfigurationError(f"Calendar provider not found: {provider_id}")
        except ImportError:
            # Fall back to normal implementation
            pass

    return provider_factory.get_provider(provider_id)


def get_provider_instance(
    provider_id: str, firm_id: Optional[str] = None
) -> CalendarProvider:
    """
    Get a calendar provider instance.

    This is an alias for get_provider for backward compatibility.

    Args:
        provider_id: The provider ID
        firm_id: The firm/tenant ID (optional)

    Returns:
        CalendarProvider: The calendar provider instance

    Raises:
        ConfigurationError: If the provider ID is not registered
    """
    return get_provider(provider_id, firm_id)

def get_available_providers() -> Dict[str, str]:
    """
    Get a dictionary of available providers.

    This is a convenience function that uses the singleton factory instance.

    Returns:
        Dict[str, str]: Dictionary mapping provider IDs to provider names
    """
    return provider_factory.get_available_providers()


def list_providers() -> List[str]:
    """
    Get a list of available provider IDs.

    Returns:
        List[str]: A list of provider IDs
    """
    # For testing, return a fixed list
    if _TESTING:
        return ["google", "calendly"]

    return list(provider_factory.get_available_providers().keys())

def register_provider(provider_id: str, provider_class: Type[CalendarProvider]) -> None:
    """
    Register a calendar provider class.

    This is a convenience function that uses the singleton factory instance.

    Args:
        provider_id: The provider ID
        provider_class: The provider class
    """
    provider_factory.register_provider(provider_id, provider_class)

def get_provider_capabilities(provider_id: str) -> Dict[str, bool]:
    """
    Get the capabilities of a provider from the capability matrix.

    This is a convenience function that uses the singleton factory instance.

    Args:
        provider_id: The provider ID

    Returns:
        Dict[str, bool]: Dictionary mapping capability names to boolean values

    Raises:
        ConfigurationError: If the provider is not found in the capability matrix
    """
    return provider_factory.get_provider_capabilities(provider_id)

def enable_testing_mode(enable: bool = True) -> None:
    """
    Enable or disable testing mode.

    In testing mode, the factory returns mock provider instances.

    Args:
        enable: Whether to enable testing mode
    """
    global _TESTING
    _TESTING = enable
    logger.debug(f"Testing mode {'enabled' if enable else 'disabled'}")


# Export public functions and classes
__all__ = [
    "CalendarProviderFactory",
    "get_provider",
    "get_provider_instance",
    "get_available_providers",
    "list_providers",
    "register_provider",
    "get_provider_capabilities",
    "load_capability_matrix",
    "enable_testing_mode"
]
