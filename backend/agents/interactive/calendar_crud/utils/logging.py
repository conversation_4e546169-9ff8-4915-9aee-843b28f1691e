"""
Logging utilities for Calendar CRUD Agent.

This module provides logging utilities for the Calendar CRUD Agent,
including structured logging and security event logging.
"""

import logging
import json
import os
import sys
from datetime import datetime
from typing import Any, Dict, Optional


def get_logger(name: str) -> logging.Logger:
    """
    Get a logger with the specified name.

    Args:
        name: The name of the logger

    Returns:
        logging.Logger: The configured logger
    """
    logger = logging.getLogger(name)
    
    # Set log level from environment variable or default to INFO
    log_level = os.getenv("LOG_LEVEL", "INFO").upper()
    logger.setLevel(getattr(logging, log_level))
    
    # Add console handler if not already added
    if not logger.handlers:
        handler = logging.StreamHandler(sys.stdout)
        formatter = logging.Formatter(
            "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
        )
        handler.setFormatter(formatter)
        logger.addHandler(handler)
    
    return logger


def log_security_event(
    event_type: str,
    user_id: Optional[str] = None,
    firm_id: Optional[str] = None,
    details: Optional[Dict[str, Any]] = None,
    success: bool = True,
    source: str = "calendar_crud",
) -> None:
    """
    Log a security event.

    Args:
        event_type: The type of security event (e.g., "authentication", "authorization")
        user_id: The ID of the user associated with the event
        firm_id: The ID of the firm/tenant associated with the event
        details: Additional details about the event
        success: Whether the event was successful
        source: The source of the event
    """
    logger = get_logger("calendar_crud.security")
    
    # Create event data
    event_data = {
        "timestamp": datetime.utcnow().isoformat(),
        "event_type": event_type,
        "user_id": user_id,
        "firm_id": firm_id,
        "details": details or {},
        "success": success,
        "source": source,
    }
    
    # Log the event
    if success:
        logger.info(f"Security event: {event_type}", extra={"event": event_data})
    else:
        logger.warning(
            f"Security event failure: {event_type}", extra={"event": event_data}
        )
    
    # TODO: In a production environment, security events should be stored in a database
    # This would be implemented in Phase 1.3.3 of the roadmap
