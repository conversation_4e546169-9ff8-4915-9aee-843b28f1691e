"""
Interval Tree implementation for calendar conflict detection.

This module provides an efficient data structure for detecting overlapping time
intervals,
which is used for calendar event conflict detection.
"""

from typing import Any, Dict, List, Optional, Tuple, TypeVar, Generic
from datetime import datetime

T = TypeVar('T')  # Type variable for the data associated with intervals


class IntervalNode(Generic[T]):
    """
    Node in an interval tree.

    Each node represents an interval with a start and end time, and optional
    associated data.
    The node also maintains the maximum end time in its subtree.
    """

    def __init__(self, start: datetime, end: datetime, data: Optional[T] = None):
        """
        Initialize an interval node.

        Args:
            start: Start time of the interval
            end: End time of the interval
            data: Optional data associated with the interval
        """
        self.start = start
        self.end = end
        self.data = data
        self.max_end = end
        self.left: Optional[IntervalNode[T]] = None
        self.right: Optional[IntervalNode[T]] = None


class IntervalTree(Generic[T]):
    """
    Interval Tree data structure for efficient interval overlap queries.

    This implementation uses a binary search tree augmented with the maximum
    end time in each subtree to efficiently find overlapping intervals.
    """

    def __init__(self):
        """Initialize an empty interval tree."""
        self.root: Optional[IntervalNode[T]] = None

    def insert(self, start: datetime, end: datetime, data: Optional[T] = None) -> None:
        """
        Insert an interval into the tree.

        Args:
            start: Start time of the interval
            end: End time of the interval
            data: Optional data associated with the interval
        """
        self.root = self._insert_recursive(self.root, start, end, data)

    def _insert_recursive(
        self,
        node: Optional[IntervalNode[T]],
        start: datetime,
        end: datetime,
        data: Optional[T]
    ) -> IntervalNode[T]:
        """
        Recursively insert an interval into the tree.

        Args:
            node: Current node in the recursion
            start: Start time of the interval
            end: End time of the interval
            data: Optional data associated with the interval

        Returns:
            The updated node
        """
        # Base case: empty tree or leaf node
        if node is None:
            return IntervalNode(start, end, data)

        # Update max_end if needed
        if end > node.max_end:
            node.max_end = end

        # Recursively insert into left or right subtree
        if start < node.start:
            node.left = self._insert_recursive(node.left, start, end, data)
        else:
            node.right = self._insert_recursive(node.right, start, end, data)

        return node

    def find_overlaps(
        self, start: datetime, end: datetime
    ) -> List[Tuple[datetime, datetime, Optional[T]]]:
        """
        Find all intervals that overlap with the given interval.

        Args:
            start: Start time of the query interval
            end: End time of the query interval

        Returns:
            List of tuples (start, end, data) for all overlapping intervals
        """
        result: List[Tuple[datetime, datetime, Optional[T]]] = []
        self._find_overlaps_recursive(self.root, start, end, result)
        return result

    def _find_overlaps_recursive(
        self,
        node: Optional[IntervalNode[T]],
        start: datetime,
        end: datetime,
        result: List[Tuple[datetime, datetime, Optional[T]]]
    ) -> None:
        """
        Recursively find all intervals that overlap with the given interval.

        Args:
            node: Current node in the recursion
            start: Start time of the query interval
            end: End time of the query interval
            result: List to collect the overlapping intervals
        """
        if node is None:
            return

        # No need to search this subtree if max_end is before start
        if node.max_end <= start:
            return

        # Search left subtree
        if node.left is not None:
            self._find_overlaps_recursive(node.left, start, end, result)

        # Check if current node overlaps
        if end > node.start and start < node.end:
            result.append((node.start, node.end, node.data))

        # Search right subtree
        if node.right is not None:
            self._find_overlaps_recursive(node.right, start, end, result)
