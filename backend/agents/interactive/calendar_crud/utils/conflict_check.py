"""
Conflict checking utilities for calendar events.

This module provides functions for checking if calendar events conflict with
existing events.
"""

import logging
from datetime import timed<PERSON><PERSON>
from typing import Dict, Any

from ..providers.models import FreeBusyRequest, CalendarEventCreate

# Import these at runtime to avoid circular imports
# from ..providers.factory import get_provider_instance
# from ..providers.exceptions import UnsupportedOperationError

# Configure logging
logger = logging.getLogger(__name__)


async def check_event_conflicts(
    tenant_id: str,
    provider: str,
    calendar_id: str,
    event: CalendarEventCreate,
    buffer_minutes: int = 0
) -> Dict[str, Any]:
    """
    Check if a new event conflicts with existing events.

    This function uses the free/busy functionality to check if a new event
    conflicts with existing events in the calendar.

    Args:
        tenant_id: The tenant ID
        provider: The calendar provider (e.g., "google", "calendly")
        calendar_id: The calendar ID
        event: The event to check for conflicts
        buffer_minutes: Buffer time in minutes to add before and after the event

    Returns:
        Dict with the following keys:
        - has_conflicts: Boolean indicating if conflicts were found
        - conflicts: List of conflicting time slots
        - message: Human-readable message describing the conflicts
    """
    try:
        # Import at runtime to avoid circular imports
        from ..providers.factory import get_provider_instance
        from ..providers.base import ProviderCapability

        # Get the provider instance
        provider_instance = get_provider_instance(provider, tenant_id)

        # Check if the provider supports free/busy checking
        supports_free_busy = provider_instance.has_capability(
            ProviderCapability.FREE_BUSY
        )

        if not supports_free_busy:
            # If free/busy checking is not supported, we can't check for conflicts
            logger.warning(
                f"Provider {provider} does not support free/busy checking, "
                f"skipping conflict check"
            )
            return {
                "has_conflicts": False,
                "conflicts": [],
                "message": f"Provider {provider} does not support conflict checking."
            }

        # Add buffer time to the event start and end times
        buffer = timedelta(minutes=buffer_minutes)
        check_start = event.start_time - buffer
        check_end = event.end_time + buffer

        # Check free/busy for the time range
        free_busy_result = await provider_instance.check_free_busy(
            firm_id=tenant_id,
            request=FreeBusyRequest(
                calendar_ids=[calendar_id],
                start_time=check_start,
                end_time=check_end
            )
        )

        # Check for conflicts
        conflicts = []
        try:
            calendars = free_busy_result.calendars
            for cal_id, busy_slots in calendars.items():
                for slot in busy_slots:
                    # Check if the busy slot overlaps with the event
                    if (slot.start < event.end_time and slot.end > event.start_time):
                        conflicts.append(slot)
        except Exception as e:
            logger.error(f"Error processing free/busy results: {str(e)}")
            return {
                "has_conflicts": False,
                "conflicts": [],
                "message": f"Error processing free/busy results: {str(e)}"
            }

        # Format the response
        if conflicts:
            conflict_descriptions = []
            for i, conflict in enumerate(conflicts, 1):
                start_time = conflict.start.strftime("%Y-%m-%d %H:%M")
                end_time = conflict.end.strftime("%Y-%m-%d %H:%M")
                conflict_descriptions.append(f"{i}. {start_time} - {end_time}")

            message = (
                f"Found {len(conflicts)} conflicting events:\n"
                + "\n".join(conflict_descriptions)
            )
            return {
                "has_conflicts": True,
                "conflicts": conflicts,
                "message": message
            }
        else:
            return {
                "has_conflicts": False,
                "conflicts": [],
                "message": "No conflicts found."
            }

    except Exception as e:
        # Check if it's an UnsupportedOperationError
        if (
            hasattr(e, "__class__")
            and e.__class__.__name__ == "UnsupportedOperationError"
        ):
            logger.warning(
                f"Provider {provider} does not support free/busy checking, "
                f"skipping conflict check"
            )
            return {
                "has_conflicts": False,
                "conflicts": [],
                "message": f"Provider {provider} does not support conflict checking."
            }
        # Handle other exceptions
        logger.error(f"Error checking for conflicts: {str(e)}")
        return {
            "has_conflicts": False,
            "conflicts": [],
            "message": f"Error checking for conflicts: {str(e)}"
        }
