"""
Async retry utilities for calendar operations.

This module provides utilities for retrying async operations with exponential backoff
and circuit breaker patterns to handle transient errors and rate limiting.
"""

import asyncio
import logging
import random
import time
from functools import wraps
from typing import Any, Callable, Dict, List, Optional, TypeVar, cast

from ..providers.exceptions import (
    RateLimitError,
    NetworkError,
    ServiceUnavailableError,
    CalendarProviderError
)

# Configure logging
logger = logging.getLogger(__name__)

# Define a type variable for the return type
T = TypeVar('T')


class RetryConfig:
    """Configuration for retry behavior."""
    
    def __init__(
        self,
        max_retries: int = 3,
        initial_backoff: float = 1.0,
        max_backoff: float = 60.0,
        backoff_factor: float = 2.0,
        jitter: bool = True,
        retry_on: Optional[List[type]] = None
    ):
        """
        Initialize retry configuration.
        
        Args:
            max_retries: Maximum number of retries
            initial_backoff: Initial backoff time in seconds
            max_backoff: Maximum backoff time in seconds
            backoff_factor: Factor to multiply backoff by after each retry
            jitter: Whether to add jitter to backoff times
            retry_on: List of exception types to retry on
        """
        self.max_retries = max_retries
        self.initial_backoff = initial_backoff
        self.max_backoff = max_backoff
        self.backoff_factor = backoff_factor
        self.jitter = jitter
        self.retry_on = retry_on or [
            RateLimitError,
            NetworkError,
            ServiceUnavailableError
        ]


def async_retry(config: Optional[RetryConfig] = None):
    """
    Decorator for retrying async functions with exponential backoff.
    
    Args:
        config: Retry configuration
        
    Returns:
        Decorated function
    """
    retry_config = config or RetryConfig()
    
    def decorator(func):
        @wraps(func)
        async def wrapper(*args, **kwargs):
            retries = 0
            backoff = retry_config.initial_backoff
            
            while True:
                try:
                    return await func(*args, **kwargs)
                except tuple(retry_config.retry_on) as e:
                    retries += 1
                    if retries > retry_config.max_retries:
                        logger.error(
                            f"Max retries ({retry_config.max_retries}) exceeded for "
                            f"{func.__name__}"
                        )
                        raise
                    
                    # Calculate backoff with jitter
                    if retry_config.jitter:
                        jitter = random.uniform(0.8, 1.2)
                        sleep_time = min(backoff * jitter, retry_config.max_backoff)
                    else:
                        sleep_time = min(backoff, retry_config.max_backoff)
                    
                    logger.warning(
                        f"Retry {retries}/{retry_config.max_retries} for "
                        f"{func.__name__} "
                        f"after {sleep_time:.2f}s due to {type(e).__name__}: "
                        f"{str(e)}"
                    )
                    
                    # Sleep before retry
                    await asyncio.sleep(sleep_time)
                    
                    # Increase backoff for next retry
                    backoff = min(
                        backoff * retry_config.backoff_factor,
                        retry_config.max_backoff,
                    )
                except Exception:
                    # Don't retry on other exceptions
                    raise
        
        return wrapper
    
    return decorator


class CircuitBreaker:
    """
    Circuit breaker pattern implementation for async functions.
    
    This class implements the circuit breaker pattern to prevent repeated calls
    to a failing service, allowing it time to recover.
    """
    
    def __init__(
        self,
        failure_threshold: int = 5,
        recovery_timeout: float = 60.0,
        retry_timeout: float = 30.0
    ):
        """
        Initialize the circuit breaker.
        
        Args:
            failure_threshold: Number of failures before opening the circuit
            recovery_timeout: Time in seconds to wait before trying to close the circuit
            retry_timeout: Time in seconds between retry attempts when half-open
        """
        self.failure_threshold = failure_threshold
        self.recovery_timeout = recovery_timeout
        self.retry_timeout = retry_timeout
        
        self.failure_count = 0
        self.last_failure_time = 0
        self.state = "closed"  # closed, open, half-open
    
    def is_open(self) -> bool:
        """
        Check if the circuit is open.
        
        Returns:
            True if the circuit is open, False otherwise
        """
        if self.state == "open":
            # Check if recovery timeout has elapsed
            if time.time() - self.last_failure_time > self.recovery_timeout:
                self.state = "half-open"
                logger.info("Circuit changed from open to half-open")
                return False
            return True
        return False
    
    def record_success(self) -> None:
        """Record a successful call."""
        if self.state == "half-open":
            self.state = "closed"
            self.failure_count = 0
            logger.info("Circuit changed from half-open to closed")
        elif self.state == "closed":
            self.failure_count = 0
    
    def record_failure(self) -> None:
        """Record a failed call."""
        self.failure_count += 1
        self.last_failure_time = time.time()
        
        if self.state == "closed" and self.failure_count >= self.failure_threshold:
            self.state = "open"
            logger.warning(f"Circuit opened after {self.failure_count} failures")
        elif self.state == "half-open":
            self.state = "open"
            logger.warning("Circuit reopened after failure in half-open state")
