"""
Calendar event conflict detection using interval trees.

This module provides enhanced conflict detection for calendar events using an
interval tree
data structure for efficient overlap detection. It builds on the existing
conflict_check.py
implementation but provides more robust and efficient conflict detection.
"""

import logging
from datetime import datetime, timedelta
from typing import Dict, Any, List, Optional, Tuple

from .interval_tree import IntervalTree
from ..providers.models import (
    FreeBusyRequest,
    CalendarEventCreate,
    TimeSlot
)

# Import these at runtime to avoid circular imports
# from ..providers.factory import get_provider_instance
# from ..providers.base import ProviderCapability
# from ..providers.exceptions import UnsupportedOperationError

# Configure logging
logger = logging.getLogger(__name__)


class ConflictDetector:
    """
    Calendar event conflict detector using interval trees.

    This class provides methods for detecting conflicts between calendar events
    using an efficient interval tree data structure.
    """

    def __init__(self):
        """Initialize the conflict detector."""
        self.interval_tree = IntervalTree()

    def add_event(self, start: datetime, end: datetime, event_data: Any) -> None:
        """
        Add an event to the conflict detector.

        Args:
            start: Start time of the event
            end: End time of the event
            event_data: Data associated with the event
        """
        self.interval_tree.insert(start, end, event_data)

    def check_conflict(
        self, start: datetime, end: datetime
    ) -> List[Tuple[datetime, datetime, Any]]:
        """
        Check if an event conflicts with existing events.

        Args:
            start: Start time of the event
            end: End time of the event

        Returns:
            List of conflicting events as (start, end, data) tuples
        """
        return self.interval_tree.find_overlaps(start, end)


async def detect_conflicts(
    tenant_id: str,
    provider: str,
    calendar_id: str,
    event: CalendarEventCreate,
    buffer_minutes: int = 0,
    exclude_event_id: Optional[str] = None
) -> Dict[str, Any]:
    """
    Detect conflicts for a calendar event using interval trees.

    This function checks if a new or updated event conflicts with existing events
    in the calendar. It uses the free/busy functionality to get existing events and
    then uses an interval tree for efficient conflict detection.

    Args:
        tenant_id: The tenant ID
        provider: The calendar provider (e.g., "google", "calendly")
        calendar_id: The calendar ID
        event: The event to check for conflicts
        buffer_minutes: Buffer time in minutes to add before and after the event
        exclude_event_id: Optional event ID to exclude from conflict detection
            (for updates)

    Returns:
        Dict with conflict information:
        - has_conflicts: Boolean indicating if conflicts were found
        - conflicts: List of conflicting time slots
        - message: Human-readable message about conflicts
    """
    try:
        # Import at runtime to avoid circular imports
        from ..providers.factory import get_provider_instance
        from ..providers.base import ProviderCapability

        # Get the provider instance
        provider_instance = get_provider_instance(provider, tenant_id)

        # Check if the provider supports free/busy checking
        supports_free_busy = False
        try:
            supports_free_busy = await provider_instance.supports(
                ProviderCapability.FREE_BUSY
            )
        except:
            # If it's not an async method, try the sync version
            supports_free_busy = provider_instance.has_capability(
                ProviderCapability.FREE_BUSY
            )

        if not supports_free_busy:
            # If free/busy checking is not supported, we can't check for conflicts
            logger.warning(
                f"Provider {provider} does not support free/busy checking, "
                f"skipping conflict check"
            )
            return {
                "has_conflicts": False,
                "conflicts": [],
                "message": (
                    f"Provider {provider} does not support conflict checking."
                ),
            }

        # Add buffer time to the event start and end times
        buffer = timedelta(minutes=buffer_minutes)
        check_start = event.start_time - buffer
        check_end = event.end_time + buffer

        # Check free/busy for the time range
        free_busy_result = await provider_instance.check_free_busy(
            firm_id=tenant_id,
            request=FreeBusyRequest(
                calendar_ids=[calendar_id],
                start_time=check_start,
                end_time=check_end
            )
        )

        # Create a conflict detector and add all busy slots
        detector = ConflictDetector()

        # Process the free/busy results
        try:
            calendars = free_busy_result.calendars
            for cal_id, busy_slots in calendars.items():
                for slot in busy_slots:
                    # Skip the event being updated if exclude_event_id is
                    # provided
                    if (
                        exclude_event_id
                        and hasattr(slot, "event_id")
                        and slot.event_id == exclude_event_id
                    ):
                        continue

                    # Add the busy slot to the conflict detector
                    detector.add_event(slot.start, slot.end, slot)
        except Exception as e:
            logger.error(f"Error processing free/busy results: {str(e)}")
            return {
                "has_conflicts": False,
                "conflicts": [],
                "message": f"Error processing free/busy results: {str(e)}"
            }

        # Check for conflicts with the new event
        # If buffer_minutes is provided, we need to check for conflicts with the
        # buffered time range
        event_start = event.start_time
        event_end = event.end_time

        # Apply buffer if specified
        if buffer_minutes > 0:
            buffer = timedelta(minutes=buffer_minutes)
            event_start = event_start - buffer
            event_end = event_end + buffer

        conflicts = detector.check_conflict(event_start, event_end)

        # Convert conflicts to TimeSlot objects
        conflict_slots = []
        for start, end, data in conflicts:
            if isinstance(data, TimeSlot):
                conflict_slots.append(data)
            else:
                # Create a new TimeSlot if the data is not already a TimeSlot
                conflict_slots.append(TimeSlot(start=start, end=end))

        # Format the response
        if conflict_slots:
            conflict_descriptions = []
            for i, conflict in enumerate(conflict_slots, 1):
                start_time = conflict.start.strftime("%Y-%m-%d %H:%M")
                end_time = conflict.end.strftime("%Y-%m-%d %H:%M")
                conflict_descriptions.append(f"{i}. {start_time} - {end_time}")

            message = (
                f"Found {len(conflict_slots)} conflicting events:\n"
                + "\n".join(conflict_descriptions)
            )
            return {
                "has_conflicts": True,
                "conflicts": conflict_slots,
                "message": message
            }
        else:
            return {
                "has_conflicts": False,
                "conflicts": [],
                "message": "No conflicts found."
            }

    except Exception as e:
        # Check if it's an UnsupportedOperationError
        if (
            hasattr(e, "__class__")
            and e.__class__.__name__ == "UnsupportedOperationError"
        ):
            logger.warning(
                f"Provider {provider} does not support free/busy checking, "
                f"skipping conflict check"
            )
            return {
                "has_conflicts": False,
                "conflicts": [],
                "message": f"Provider {provider} does not support conflict checking."
            }
        # Handle other exceptions
        logger.error(f"Error checking for conflicts: {str(e)}")
        return {
            "has_conflicts": False,
            "conflicts": [],
            "message": f"Error checking for conflicts: {str(e)}"
        }
