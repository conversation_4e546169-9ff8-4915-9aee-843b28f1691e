"""
Calendar CRUD Agent Configuration

This module provides configuration for the Calendar CRUD Agent,
including agent settings, node configurations, and prompt templates.
"""

from typing import Dict, Any, Optional
from pydantic import BaseModel, Field

class CalendarCrudAgentConfig(BaseModel):
    """
    Configuration for the Calendar CRUD Agent.
    
    Attributes:
        name: Agent name
        description: Agent description
        version: Agent version
        prompt_templates: Prompt templates for the agent
        node_configs: Configuration for agent nodes
    """
    
    name: str = "calendar_crud_agent"
    description: str = "Calendar CRUD Agent for managing calendar operations"
    version: str = "0.1.0"
    prompt_templates: Dict[str, str] = Field(default_factory=dict)
    node_configs: Dict[str, Dict[str, Any]] = Field(default_factory=dict)

def get_calendar_crud_agent_config() -> CalendarCrudAgentConfig:
    """
    Get the default configuration for the Calendar CRUD Agent.
    
    Returns:
        CalendarCrudAgentConfig: Default configuration
    """
    return CalendarCrudAgentConfig(
        prompt_templates={
            "router": (
                "You are a calendar management assistant. Your job is to help "
                "users manage their calendar events.\n"
                "\n"
                "Based on the user's request, determine which operation they want to "
                "perform:\n"
                "- create_event: Create a new calendar event\n"
                "- read_event: Read or list calendar events\n"
                "- update_event: Update an existing calendar event\n"
                "- delete_event: Delete a calendar event\n"
                "- check_free_busy: Check free/busy times for scheduling\n"
                "\n"
                "User request: {input}\n"
                "\n"
                "Operation:"
            ),
            "create_event": (
                "You are a calendar management assistant. Your job is to help "
                "users create new calendar events.\n"
                "\n"
                "Extract the following information from the user's request:\n"
                "- Event title/summary\n"
                "- Event description\n"
                "- Start date and time\n"
                "- End date and time\n"
                "- Location (if provided)\n"
                "- Attendees (if provided)\n"
                "- Calendar ID (if provided, otherwise use \"primary\")\n"
                "- Provider (if provided, otherwise use \"google\")\n"
                "\n"
                "User request: {input}\n"
                "\n"
                "Provide the extracted information in the following JSON format:\n"
                "```json\n"
                "{\n"
                "  \"summary\": \"Meeting with Client\",\n"
                "  \"description\": \"Discuss project requirements\",\n"
                "  \"start_time\": \"2023-06-01T09:00:00\",\n"
                "  \"end_time\": \"2023-06-01T10:00:00\",\n"
                "  \"location\": \"Office\",\n"
                "  \"attendees\": [\"<EMAIL>\"],\n"
                "  \"calendar_id\": \"primary\",\n"
                "  \"provider\": \"google\"\n"
                "}\n"
                "```\n"
                "\n"
                "JSON:"
            ),
            "read_event": (
                "You are a calendar management assistant. Your job is to help "
                "users read or list calendar events.\n"
                "\n"
                "Extract the following information from the user's request:\n"
                "- Date range (start and end dates)\n"
                "- Calendar ID (if provided, otherwise use \"primary\")\n"
                "- Provider (if provided, otherwise use \"google\")\n"
                "- Any filters or search terms\n"
                "\n"
                "User request: {input}\n"
                "\n"
                "Provide the extracted information in the following JSON format:\n"
                "```json\n"
                "{\n"
                "  \"start_time\": \"2023-06-01T00:00:00\",\n"
                "  \"end_time\": \"2023-06-07T23:59:59\",\n"
                "  \"calendar_id\": \"primary\",\n"
                "  \"provider\": \"google\",\n"
                "  \"query\": \"client meeting\"\n"
                "}\n"
                "```\n"
                "\n"
                "JSON:"
            ),
            "update_event": (
                "You are a calendar management assistant. Your job is to help "
                "users update existing calendar events.\n"
                "\n"
                "Extract the following information from the user's request:\n"
                "- Event ID\n"
                "- Calendar ID (if provided, otherwise use \"primary\")\n"
                "- Provider (if provided, otherwise use \"google\")\n"
                "- Fields to update (summary, description, start/end times, location, "
                "attendees)\n"
                "\n"
                "User request: {input}\n"
                "\n"
                "Provide the extracted information in the following JSON format:\n"
                "```json\n"
                "{\n"
                "  \"event_id\": \"abc123\",\n"
                "  \"calendar_id\": \"primary\",\n"
                "  \"provider\": \"google\",\n"
                "  \"updates\": {\n"
                "    \"summary\": \"Updated Meeting with Client\",\n"
                "    \"description\": \"Discuss revised project requirements\",\n"
                "    \"start_time\": \"2023-06-01T10:00:00\",\n"
                "    \"end_time\": \"2023-06-01T11:00:00\",\n"
                "    \"location\": \"Conference Room\",\n"
                "    \"attendees\": [\"<EMAIL>\", \"<EMAIL>\"]\n"
                "  }\n"
                "}\n"
                "```\n"
                "\n"
                "JSON:"
            ),
            "delete_event": (
                "You are a calendar management assistant. Your job is to help "
                "users delete calendar events.\n"
                "\n"
                "Extract the following information from the user's request:\n"
                "- Event ID\n"
                "- Calendar ID (if provided, otherwise use \"primary\")\n"
                "- Provider (if provided, otherwise use \"google\")\n"
                "\n"
                "User request: {input}\n"
                "\n"
                "Provide the extracted information in the following JSON format:\n"
                "```json\n"
                "{\n"
                "  \"event_id\": \"abc123\",\n"
                "  \"calendar_id\": \"primary\",\n"
                "  \"provider\": \"google\"\n"
                "}\n"
                "```\n"
                "\n"
                "JSON:"
            ),
            "check_free_busy": (
                "You are a calendar management assistant. Your job is to help "
                "users check free/busy times for scheduling.\n"
                "\n"
                "Extract the following information from the user's request:\n"
                "- Date range (start and end dates)\n"
                "- Calendar IDs (if provided, otherwise use [\"primary\"])\n"
                "- Provider (if provided, otherwise use \"google\")\n"
                "\n"
                "User request: {input}\n"
                "\n"
                "Provide the extracted information in the following JSON format:\n"
                "```json\n"
                "{\n"
                "  \"start_time\": \"2023-06-01T09:00:00\",\n"
                "  \"end_time\": \"2023-06-01T17:00:00\",\n"
                "  \"calendar_ids\": [\"primary\"],\n"
                "  \"provider\": \"google\"\n"
                "}\n"
                "```\n"
                "\n"
                "JSON:"
            )
        },
        node_configs={
            "router": {
                "llm_model": "voyage/voyage-3-large",
                "temperature": 0.0,
            },
            "create_event": {
                "llm_model": "voyage/voyage-3-large",
                "temperature": 0.0,
            },
            "read_event": {
                "llm_model": "voyage/voyage-3-large",
                "temperature": 0.0,
            },
            "update_event": {
                "llm_model": "voyage/voyage-3-large",
                "temperature": 0.0,
            },
            "delete_event": {
                "llm_model": "voyage/voyage-3-large",
                "temperature": 0.0,
            },
            "check_free_busy": {
                "llm_model": "voyage/voyage-3-large",
                "temperature": 0.0,
            }
        }
    )
