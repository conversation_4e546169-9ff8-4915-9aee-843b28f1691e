"""
Calendar CRUD Agent for AiLex.

This package provides calendar integration features for both Core AiLex and AI
Voice Receptionist products,
including OAuth integration, calendar event management, and webhook integration.
"""

from backend.agents.interactive.calendar_crud.agent import CalendarCrudAgent
from backend.agents.interactive.calendar_crud.router import calendar_crud_router
from backend.agents.interactive.calendar_crud.graph import (
    create_calendar_graph,
    get_workflow_info,
)
from backend.agents.interactive.calendar_crud.nodes import (
    create_event,
    read_event,
    update_event,
    delete_event,
    check_free_busy,
    parse_date
)

__version__ = "0.1.0"

__all__ = [
    "CalendarCrudAgent",
    "calendar_crud_router",
    "create_calendar_graph",
    "get_workflow_info",
    "create_event",
    "read_event",
    "update_event",
    "delete_event",
    "check_free_busy",
    "parse_date"
]
