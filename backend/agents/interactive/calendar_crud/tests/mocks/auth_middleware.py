"""
Mock implementation of auth middleware for testing.

This module provides mock implementations of the auth middleware functions for testing.
"""

import logging
from typing import Any, Awaitable, Callable, Dict, Optional

from fastapi import Request, Response, HTTPException, status, Depends
from pydantic import BaseModel

# Configure logging
logger = logging.getLogger(__name__)


class MockUserContext(BaseModel):
    """Mock user context for testing."""
    
    user_id: str = "test-user-id"
    email: Optional[str] = "<EMAIL>"
    role: str = "user"
    firm_id: Optional[str] = "test-firm-id"
    permissions: list[str] = []
    is_authenticated: bool = True


async def mock_get_firm_id(request: Request = None) -> str:
    """
    Mock implementation of get_firm_id.
    
    Args:
        request: The FastAPI request object (optional)
        
    Returns:
        str: The firm_id
    """
    return "test-firm-id"


async def mock_verify_tenant_access(
    request: Request = None, firm_id: str = None
) -> None:
    """
    Mock implementation of verify_tenant_access.
    
    Args:
        request: The FastAPI request object (optional)
        firm_id: The firm/tenant ID (optional)
    """
    return None


async def mock_get_current_user(request: Request = None) -> MockUserContext:
    """
    Mock implementation of get_current_user.
    
    Args:
        request: The FastAPI request object (optional)
        
    Returns:
        MockUserContext: The mock user context
    """
    return MockUserContext()


class MockAuthMiddleware:
    """Mock implementation of AuthMiddleware for testing."""
    
    async def dispatch(
        self, request: Request, call_next: Callable[[Request], Awaitable[Response]]
    ) -> Response:
        """
        Mock implementation of dispatch.
        
        Args:
            request: The incoming request
            call_next: The next middleware or route handler
            
        Returns:
            Response: The response from the next middleware or route handler
        """
        # Add mock user context to request state
        request.state.user_id = "test-user-id"
        request.state.firm_id = "test-firm-id"
        request.state.role = "user"
        request.state.email = "<EMAIL>"
        request.state.user = MockUserContext()
        
        # Proceed with the request
        return await call_next(request)
