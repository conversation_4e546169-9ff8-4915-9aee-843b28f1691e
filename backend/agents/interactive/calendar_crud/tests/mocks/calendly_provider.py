"""
Mock implementation of Calendly provider for testing.

This module provides a mock implementation of the Calendly provider for testing.
"""

import logging
from datetime import datetime, timedelta, timezone
from typing import Any, Dict, List, Optional

from ...providers.base import CalendarProvider, ProviderCapability
from ...providers.exceptions import UnsupportedOperationError
from ...providers.models import (
    Calendar,
    CalendarEvent,
    CalendarEventCreate,
    CalendarEventUpdate,
    FreeBusyResponse,
    SchedulingLink,
    TimeSlot,
)

# Configure logging
logger = logging.getLogger(__name__)


class MockCalendlyProvider(CalendarProvider):
    """Mock implementation of Calendly provider for testing."""
    
    def __init__(self, firm_id: str):
        """
        Initialize the mock provider.
        
        Args:
            firm_id: The firm/tenant ID
        """
        self.firm_id = firm_id
        self.provider_name = "Calendly"
        self.provider_id = "calendly"
        self.auth_type = "oauth2"
        self.scopes = [
            "default",
        ]
        self.rate_limits = {
            "requests_per_day": 10000,
            "requests_per_100_seconds": None,
            "requests_per_minute": 100,
        }
        self.retry_strategy = {
            "max_retries": 5,
            "initial_backoff_seconds": 2,
            "max_backoff_seconds": 120,
            "jitter_factor": 0.2,
            "circuit_breaker_threshold": 0.7,
            "circuit_breaker_reset_seconds": 600,
        }
        
    async def is_connected(self) -> bool:
        """
        Check if the provider is connected.
        
        Returns:
            bool: True if connected, False otherwise
        """
        return False
        
    def has_capability(self, capability: ProviderCapability) -> bool:
        """
        Check if the provider has a capability.
        
        Args:
            capability: The capability to check
            
        Returns:
            bool: True if the provider has the capability, False otherwise
        """
        # Calendly only supports CREATE_EVENT, READ_EVENT, and LIST_CALENDARS
        return capability in [
            ProviderCapability.CREATE_EVENT,
            ProviderCapability.READ_EVENT,
            ProviderCapability.LIST_CALENDARS,
        ]
        
    async def get_calendars(self) -> List[Calendar]:
        """
        Get the list of calendars.
        
        Returns:
            List[Calendar]: The list of calendars
        """
        return [
            Calendar(
                id="test-calendly-id-1",
                name="Test Calendly 1",
                description="Test Calendly 1 Description",
                timezone="UTC",
                primary=True,
                access_role="owner",
            ),
        ]
        
    async def get_calendar(self, calendar_id: str) -> Calendar:
        """
        Get a calendar by ID.
        
        Args:
            calendar_id: The calendar ID
            
        Returns:
            Calendar: The calendar
        """
        calendars = await self.get_calendars()
        for calendar in calendars:
            if calendar.id == calendar_id:
                return calendar
        raise ValueError(f"Calendar not found: {calendar_id}")
        
    async def get_events(
        self,
        calendar_id: str,
        start_time: datetime,
        end_time: datetime,
        max_results: int = 100,
    ) -> List[CalendarEvent]:
        """
        Get events for a calendar.
        
        Args:
            calendar_id: The calendar ID
            start_time: The start time
            end_time: The end time
            max_results: The maximum number of results
            
        Returns:
            List[CalendarEvent]: The list of events
        """
        return [
            CalendarEvent(
                id="test-calendly-event-id-1",
                calendar_id=calendar_id,
                provider_id=self.provider_id,
                summary="Test Calendly Event 1",
                description="Test Calendly Event 1 Description",
                location="Test Location 1",
                start_time=start_time + timedelta(hours=1),
                end_time=start_time + timedelta(hours=2),
                status="confirmed",
            ),
        ]
        
    async def get_event(self, calendar_id: str, event_id: str) -> CalendarEvent:
        """
        Get an event by ID.
        
        Args:
            calendar_id: The calendar ID
            event_id: The event ID
            
        Returns:
            CalendarEvent: The event
        """
        events = await self.get_events(
            calendar_id=calendar_id,
            start_time=datetime.now(timezone.utc),
            end_time=datetime.now(timezone.utc) + timedelta(days=7),
        )
        for event in events:
            if event.id == event_id:
                return event
        raise ValueError(f"Event not found: {event_id}")
        
    async def create_event(
        self, calendar_id: str, event: CalendarEventCreate
    ) -> CalendarEvent:
        """
        Create an event.
        
        Args:
            calendar_id: The calendar ID
            event: The event to create
            
        Returns:
            CalendarEvent: The created event
        """
        # Calendly doesn't support direct event creation
        raise UnsupportedOperationError(
            "Calendly provider does not support direct event creation"
        )
        
    async def create_scheduling_link(
        self, calendar_id: str, event: CalendarEventCreate
    ) -> SchedulingLink:
        """
        Create a scheduling link.
        
        Args:
            calendar_id: The calendar ID
            event: The event to create
            
        Returns:
            SchedulingLink: The scheduling link
        """
        return SchedulingLink(
            scheduling_url="https://calendly.com/test/test-event",
            expires_at=datetime.now(timezone.utc) + timedelta(days=7),
            event_type_id="test-event-type-id",
        )
        
    async def update_event(
        self, calendar_id: str, event_id: str, event: CalendarEventUpdate
    ) -> CalendarEvent:
        """
        Update an event.
        
        Args:
            calendar_id: The calendar ID
            event_id: The event ID
            event: The event updates
            
        Returns:
            CalendarEvent: The updated event
        """
        # Calendly doesn't support updating events
        raise UnsupportedOperationError(
            "Calendly provider does not support updating events"
        )
        
    async def delete_event(self, calendar_id: str, event_id: str) -> bool:
        """
        Delete an event.
        
        Args:
            calendar_id: The calendar ID
            event_id: The event ID
            
        Returns:
            bool: True if the event was deleted, False otherwise
        """
        # Calendly doesn't support deleting events
        raise UnsupportedOperationError(
            "Calendly provider does not support deleting events"
        )
        
    async def check_free_busy(
        self,
        calendar_ids: List[str],
        start_time: datetime,
        end_time: datetime,
        timezone: Optional[str] = None,
    ) -> FreeBusyResponse:
        """
        Check free/busy times for calendars.
        
        Args:
            calendar_ids: The calendar IDs
            start_time: The start time
            end_time: The end time
            timezone: The timezone
            
        Returns:
            FreeBusyResponse: The free/busy response
        """
        # Calendly doesn't support free/busy checking
        raise UnsupportedOperationError(
            "Calendly provider does not support free/busy checking"
        )
