"""
Mock implementation of Google Calendar provider for testing.

This module provides a mock implementation of the Google Calendar provider for testing.
"""

import logging
from datetime import datetime, timedelta, timezone
from typing import Any, Dict, List, Optional

from ...providers.base import CalendarProvider, ProviderCapability
from ...providers.models import (
    Calendar,
    CalendarEvent,
    CalendarEventCreate,
    CalendarEventUpdate,
    FreeBusyResponse,
    TimeSlot,
)

# Configure logging
logger = logging.getLogger(__name__)


class MockGoogleProvider(CalendarProvider):
    """Mock implementation of Google Calendar provider for testing."""
    
    def __init__(self, firm_id: str):
        """
        Initialize the mock provider.
        
        Args:
            firm_id: The firm/tenant ID
        """
        self.firm_id = firm_id
        self.provider_name = "Google Calendar"
        self.provider_id = "google"
        self.auth_type = "oauth2"
        self.scopes = [
            "https://www.googleapis.com/auth/calendar",
            "https://www.googleapis.com/auth/calendar.events",
        ]
        self.rate_limits = {
            "requests_per_day": 1000000,
            "requests_per_100_seconds": 100,
            "requests_per_minute": None,
        }
        self.retry_strategy = {
            "max_retries": 3,
            "initial_backoff_seconds": 1,
            "max_backoff_seconds": 60,
            "jitter_factor": 0.1,
            "circuit_breaker_threshold": 0.5,
            "circuit_breaker_reset_seconds": 300,
        }
        
    async def is_connected(self) -> bool:
        """
        Check if the provider is connected.
        
        Returns:
            bool: True if connected, False otherwise
        """
        return True
        
    def has_capability(self, capability: ProviderCapability) -> bool:
        """
        Check if the provider has a capability.
        
        Args:
            capability: The capability to check
            
        Returns:
            bool: True if the provider has the capability, False otherwise
        """
        # Google Calendar supports all capabilities except DELETE_EVENT for testing
        return capability != ProviderCapability.DELETE_EVENT
        
    async def get_calendars(self) -> List[Calendar]:
        """
        Get the list of calendars.
        
        Returns:
            List[Calendar]: The list of calendars
        """
        return [
            Calendar(
                id="test-calendar-id-1",
                name="Test Calendar 1",
                description="Test Calendar 1 Description",
                timezone="UTC",
                primary=True,
                access_role="owner",
                background_color="#4285F4",
                foreground_color="#FFFFFF",
            ),
            Calendar(
                id="test-calendar-id-2",
                name="Test Calendar 2",
                description="Test Calendar 2 Description",
                timezone="UTC",
                primary=False,
                access_role="reader",
                background_color="#0B8043",
                foreground_color="#FFFFFF",
            ),
        ]
        
    async def get_calendar(self, calendar_id: str) -> Calendar:
        """
        Get a calendar by ID.
        
        Args:
            calendar_id: The calendar ID
            
        Returns:
            Calendar: The calendar
        """
        calendars = await self.get_calendars()
        for calendar in calendars:
            if calendar.id == calendar_id:
                return calendar
        raise ValueError(f"Calendar not found: {calendar_id}")
        
    async def get_events(
        self,
        calendar_id: str,
        start_time: datetime,
        end_time: datetime,
        max_results: int = 100,
    ) -> List[CalendarEvent]:
        """
        Get events for a calendar.
        
        Args:
            calendar_id: The calendar ID
            start_time: The start time
            end_time: The end time
            max_results: The maximum number of results
            
        Returns:
            List[CalendarEvent]: The list of events
        """
        return [
            CalendarEvent(
                id="test-event-id-1",
                calendar_id=calendar_id,
                provider_id=self.provider_id,
                summary="Test Event 1",
                description="Test Event 1 Description",
                location="Test Location 1",
                start_time=start_time + timedelta(hours=1),
                end_time=start_time + timedelta(hours=2),
                status="confirmed",
            ),
            CalendarEvent(
                id="test-event-id-2",
                calendar_id=calendar_id,
                provider_id=self.provider_id,
                summary="Test Event 2",
                description="Test Event 2 Description",
                location="Test Location 2",
                start_time=start_time + timedelta(hours=3),
                end_time=start_time + timedelta(hours=4),
                status="confirmed",
            ),
        ]
        
    async def get_event(self, calendar_id: str, event_id: str) -> CalendarEvent:
        """
        Get an event by ID.
        
        Args:
            calendar_id: The calendar ID
            event_id: The event ID
            
        Returns:
            CalendarEvent: The event
        """
        events = await self.get_events(
            calendar_id=calendar_id,
            start_time=datetime.now(timezone.utc),
            end_time=datetime.now(timezone.utc) + timedelta(days=7),
        )
        for event in events:
            if event.id == event_id:
                return event
        raise ValueError(f"Event not found: {event_id}")
        
    async def create_event(
        self, calendar_id: str, event: CalendarEventCreate
    ) -> CalendarEvent:
        """
        Create an event.
        
        Args:
            calendar_id: The calendar ID
            event: The event to create
            
        Returns:
            CalendarEvent: The created event
        """
        return CalendarEvent(
            id="test-event-id-new",
            calendar_id=calendar_id,
            provider_id=self.provider_id,
            summary=event.summary,
            description=event.description,
            location=event.location,
            start_time=event.start_time,
            end_time=event.end_time,
            status="confirmed",
        )
        
    async def update_event(
        self, calendar_id: str, event_id: str, event: CalendarEventUpdate
    ) -> CalendarEvent:
        """
        Update an event.
        
        Args:
            calendar_id: The calendar ID
            event_id: The event ID
            event: The event updates
            
        Returns:
            CalendarEvent: The updated event
        """
        # Get the existing event
        existing_event = await self.get_event(calendar_id, event_id)
        
        # Apply updates
        if event.summary is not None:
            existing_event.summary = event.summary
        if event.description is not None:
            existing_event.description = event.description
        if event.location is not None:
            existing_event.location = event.location
        if event.start_time is not None:
            existing_event.start_time = event.start_time
        if event.end_time is not None:
            existing_event.end_time = event.end_time
        if event.status is not None:
            existing_event.status = event.status
            
        return existing_event
        
    async def delete_event(self, calendar_id: str, event_id: str) -> bool:
        """
        Delete an event.
        
        Args:
            calendar_id: The calendar ID
            event_id: The event ID
            
        Returns:
            bool: True if the event was deleted, False otherwise
        """
        # This provider doesn't support deleting events
        raise NotImplementedError(
            "Google Calendar provider does not support deleting events"
        )
        
    async def check_free_busy(
        self,
        calendar_ids: List[str],
        start_time: datetime,
        end_time: datetime,
        timezone: Optional[str] = None,
    ) -> FreeBusyResponse:
        """
        Check free/busy times for calendars.
        
        Args:
            calendar_ids: The calendar IDs
            start_time: The start time
            end_time: The end time
            timezone: The timezone
            
        Returns:
            FreeBusyResponse: The free/busy response
        """
        # Create a response with some busy times
        calendars = {}
        for calendar_id in calendar_ids:
            calendars[calendar_id] = [
                TimeSlot(
                    start=start_time + timedelta(hours=1),
                    end=start_time + timedelta(hours=2),
                ),
                TimeSlot(
                    start=start_time + timedelta(hours=4),
                    end=start_time + timedelta(hours=5),
                ),
            ]
            
        return FreeBusyResponse(
            calendars=calendars,
            time_min=start_time,
            time_max=end_time,
        )
