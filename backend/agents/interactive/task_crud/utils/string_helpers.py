"""
String Helper Functions

This module provides helper functions for string manipulation and parsing.
"""

import re
from typing import Optional


def strip_leading_to(text: str) -> str:
    """
    Strip leading 'to' from a string.
    
    Args:
        text: The text to strip
        
    Returns:
        The text with leading 'to' removed
    """
    return re.sub(r"^\s*to\s+", "", text, 1, re.I)


def extract_title(text: str) -> str:
    """
    Extract a title from text.
    
    Args:
        text: The text to extract from
        
    Returns:
        The extracted title
    """
    # Check for explicit title
    title_match = re.search(r"title\s*:\s*([^,\.]+)", text, re.I)
    if title_match:
        return title_match.group(1).strip()
    
    # Remove common prefixes
    cleaned_text = strip_leading_to(text)
    
    # Remove common task creation phrases
    cleaned_text = re.sub(
        r"^(?:create|add|make)\s+(?:a\s+)?(?:new\s+)?task(?:\s+to\s+|\s*$)",
        "",
        cleaned_text,
        1,
        re.I,
    )
    cleaned_text = re.sub(
        r"^(?:a\s+)?(?:new\s+)?task(?:\s+to\s+|\s*$)", "", cleaned_text, 1, re.I
    )
    cleaned_text = re.sub(r"^(?:called|named)\s+", "", cleaned_text, 1, re.I)

    # Stop at description, due date, assignee, or case information
    stop_patterns = [
        r"\s+with\s+description\s*:",
        r"\s+description\s*:",
        r"\s+due\s+",
        r"\s+assign(?:ed)?\s+to\s+",
        r"\s+for\s+case\s+",
        r"\s+(?:related|for)\s+case\s*:"
    ]

    for pattern in stop_patterns:
        match = re.search(pattern, cleaned_text, re.I)
        if match:
            cleaned_text = cleaned_text[:match.start()]
            break

    return cleaned_text.strip()


def extract_description(text: str) -> Optional[str]:
    """
    Extract a description from text.
    
    Args:
        text: The text to extract from
        
    Returns:
        The extracted description or None if not found
    """
    # Check for explicit description
    desc_match = re.search(r"description\s*:\s*([^,\.]+)", text, re.I)
    if desc_match:
        return desc_match.group(1).strip()
    
    # Check for with description
    desc_match = re.search(r"with\s+description\s*:?\s*([^,\.]+)", text, re.I)
    if desc_match:
        return desc_match.group(1).strip()
    
    return None


def extract_status(text: str) -> Optional[str]:
    """
    Extract a status from text.
    
    Args:
        text: The text to extract from
        
    Returns:
        The extracted status or None if not found
    """
    # Check for explicit status
    status_match = re.search(r"status\s*:\s*([^,\.]+)", text, re.I)
    if status_match:
        status = status_match.group(1).strip().lower()
        return normalize_status(status)
    
    # Check for status keywords
    if re.search(r"\b(?:mark|set)\s+(?:as\s+)?done\b", text, re.I):
        return "done"
    if re.search(r"\b(?:mark|set)\s+(?:as\s+)?complete\b", text, re.I):
        return "done"
    if re.search(r"\b(?:mark|set)\s+(?:as\s+)?in\s+progress\b", text, re.I):
        return "in_progress"
    if re.search(r"\b(?:mark|set)\s+(?:as\s+)?todo\b", text, re.I):
        return "todo"
    
    return None


def normalize_status(status: str) -> str:
    """
    Normalize a status string.
    
    Args:
        status: The status to normalize
        
    Returns:
        The normalized status
    """
    status = status.lower()
    if status in ["done", "complete", "completed", "finished"]:
        return "done"
    elif status in ["in progress", "in-progress", "inprogress", "ongoing", "started"]:
        return "in_progress"
    elif status in ["todo", "to-do", "to do", "not started", "pending"]:
        return "todo"
    else:
        return status


def extract_priority(text: str) -> Optional[str]:
    """
    Extract a priority from text.
    
    Args:
        text: The text to extract from
        
    Returns:
        The extracted priority or None if not found
    """
    # Check for explicit priority
    priority_match = re.search(r"priority\s*:\s*([^,\.]+)", text, re.I)
    if priority_match:
        priority = priority_match.group(1).strip().lower()
        return normalize_priority(priority)
    
    # Check for priority keywords
    if re.search(r"\b(?:high|highest)\s+priority\b", text, re.I):
        return "high"
    if re.search(r"\b(?:medium|normal)\s+priority\b", text, re.I):
        return "medium"
    if re.search(r"\b(?:low|lowest)\s+priority\b", text, re.I):
        return "low"
    
    return None


def normalize_priority(priority: str) -> str:
    """
    Normalize a priority string.
    
    Args:
        priority: The priority to normalize
        
    Returns:
        The normalized priority
    """
    priority = priority.lower()
    if priority in ["high", "highest", "urgent", "important"]:
        return "high"
    elif priority in ["medium", "normal", "average", "standard"]:
        return "medium"
    elif priority in ["low", "lowest", "minor"]:
        return "low"
    else:
        return priority


def has_confirmed_deletion(text: str) -> bool:
    """
    Check if the text confirms a deletion.
    
    Args:
        text: The text to check
        
    Returns:
        True if the text confirms deletion, False otherwise
    """
    # Check for negative confirmation first
    negative_terms = ["don't", "do not", "nah", "cancel", "no", "stop", "abort", "wait"]
    for term in negative_terms:
        if term.lower() in text.lower() and "delete" in text.lower():
            # Check if the negative term appears before "delete"
            neg_pos = text.lower().find(term.lower())
            del_pos = text.lower().find("delete")
            if neg_pos < del_pos:
                return False
    
    # Check for positive confirmation
    positive_patterns = [
        r"\byes\b",
        r"\bconfirm\b",
        r"\bapprove\b",
        r"\bproceed\b",
        r"\bgo\s+ahead\b",
        r"\bdelete\s+it\b",
        r"\bremove\s+it\b",
        r"\bok\b",
        r"\bsure\b"
    ]
    
    for pattern in positive_patterns:
        if re.search(pattern, text, re.I):
            return True
    
    return False
