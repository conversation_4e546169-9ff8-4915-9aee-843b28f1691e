"""
Task CRUD Agent Graph

This module defines the LangGraph workflow for the Task CRUD Agent.
It connects the various nodes to create a complete task management workflow
with intent detection and routing to appropriate operations.

The graph supports multiple task operations:
1. Create Task: Creates new tasks with title, description, due date, priority,
   and assignee
2. Read Task: Retrieves and lists tasks with filtering by status, date range,
   and assignee
3. Update Task: Updates existing task properties including status transitions
4. Delete Task: Deletes tasks with proper validation

The workflow starts with intent detection (DetectIntentNode) which routes to the
appropriate operation node based on user input analysis.
"""

import logging
from typing import Any, Dict

from langchain_core.runnables import RunnableConfig
from langgraph.graph import END, StateGraph

from backend.agents.interactive.task_crud.nodes import (
    create_task,
    delete_task,
    read_task,
    return_result,
    update_task,
)
from backend.agents.interactive.task_crud.router import task_crud_router
from backend.agents.interactive.task_crud.state import TaskLangGraphState
from shared.core.llm.voyage import VoyageClient

# Configure logger
logger = logging.getLogger(__name__)


def create_task_graph(*, voyage: VoyageClient) -> StateGraph:
    """
    Create the task CRUD workflow graph.

    This function builds a LangGraph StateGraph that implements the task CRUD workflow.
    It connects the various nodes to create a complete task management process
    with intent detection and routing to appropriate operations.

    Workflow:
    1. DetectIntentNode → sets state.intent (create|read|update|delete)
    2. Branch:
       • CreateTaskNode
       • ReadTaskNode
       • UpdateTaskNode
       • DeleteTaskNode
    3. ReturnResultNode → serialises `task` or op-result to dict

    Args:
        voyage: VoyageClient instance for LLM operations

    Returns:
        StateGraph: Compiled task CRUD workflow graph
    """
    logger.info("Creating task CRUD StateGraph workflow")
    
    # Create the graph with TaskLangGraphState as the state type
    workflow = StateGraph(TaskLangGraphState)

    # Add the DetectIntentNode (router) for intent classification
    workflow.add_node("detect_intent", task_crud_router)

    # Add operation nodes for task CRUD operations
    workflow.add_node("create_task", create_task)
    workflow.add_node("read_task", read_task)
    workflow.add_node("update_task", update_task)
    workflow.add_node("delete_task", delete_task)
    
    # Add the return result node for serializing results
    workflow.add_node("return_result", return_result)

    # Set the entry point to intent detection
    workflow.set_entry_point("detect_intent")

    # Add conditional edges from intent detection to operation nodes based on
    # detected intent
    workflow.add_conditional_edges(
        "detect_intent",
        _route_to_operation,
        {
            "create_task": "create_task",
            "read_task": "read_task", 
            "update_task": "update_task",
            "delete_task": "delete_task",
        }
    )

    # Add edges from all operation nodes to return_result
    workflow.add_edge("create_task", "return_result")
    workflow.add_edge("read_task", "return_result")
    workflow.add_edge("update_task", "return_result")
    workflow.add_edge("delete_task", "return_result")
    
    # Add edge from return_result to END
    workflow.add_edge("return_result", END)

    # Compile the workflow
    compiled_workflow = workflow.compile()
    
    logger.info("Task CRUD StateGraph workflow created and compiled successfully")
    return compiled_workflow


def _route_to_operation(state: TaskLangGraphState) -> str:
    """
    Route to the appropriate operation based on the detected intent.
    
    This function examines the state to determine which operation node
    should be executed next based on the intent detected by the router.
    
    Args:
        state: Current workflow state containing routing information
        
    Returns:
        str: Name of the next operation node to execute
    """
    # Check if the router set a 'next' field in the state
    next_operation = state.get("next")
    
    if next_operation:
        logger.info(f"Routing to operation: {next_operation}")
        return next_operation
    
    # Check for intent in state
    intent = state.get("intent")
    if intent:
        logger.info(f"Routing based on intent field: {intent}")
        # Map intent to node names
        if intent == "create":
            return "create_task"
        elif intent in ["read", "list"]:
            return "read_task"
        elif intent == "update":
            return "update_task"
        elif intent == "delete":
            return "delete_task"
    
    # Check for operation in metadata or other state fields
    operation = state.get("operation")
    if operation:
        logger.info(f"Routing based on operation field: {operation}")
        # Map operation to node names
        if operation == "create":
            return "create_task"
        elif operation in ["read", "list"]:
            return "read_task"
        elif operation == "update":
            return "update_task"
        elif operation == "delete":
            return "delete_task"
    
    # Default to create_task if no routing information is found
    logger.warning("No routing information found in state, defaulting to create_task")
    return "create_task"


async def _error_handler(
    state: TaskLangGraphState, config: RunnableConfig
) -> TaskLangGraphState:
    """
    Handle errors that occur during workflow execution.
    
    This function provides centralized error handling for the task CRUD workflow.
    It logs errors and returns an appropriate error response to the user.
    
    Args:
        state: Current workflow state
        config: Runnable configuration
        
    Returns:
        TaskLangGraphState: Updated state with error information
    """
    error_msg = state.get("error", "An unknown error occurred")
    logger.error(f"Task CRUD workflow error: {error_msg}")
    
    # Get existing messages or initialize empty list
    messages = state.get("messages", [])
    
    # Add error message to the conversation
    error_response = {
        "role": "assistant",
        "content": (
            f"I apologize, but I encountered an error while processing your "
            f"task request: {error_msg}. Please try again or contact support "
            f"if the issue persists."
        ),
        "metadata": {
            "error": True,
            "error_message": error_msg
        }
    }
    
    return {
        **state,
        "messages": messages + [error_response],
        "error": error_msg
    }


def get_workflow_info() -> Dict[str, Any]:
    """
    Get information about the task CRUD workflow.
    
    Returns:
        Dict[str, Any]: Workflow information including nodes, edges, and capabilities
    """
    return {
        "name": "task_crud_workflow",
        "description": "LangGraph workflow for task CRUD operations",
        "version": "1.0.0",
        "nodes": [
            "detect_intent",
            "create_task",
            "read_task",
            "update_task",
            "delete_task",
            "return_result"
        ],
        "entry_point": "detect_intent",
        "capabilities": [
            "Intent detection and routing",
            "Task creation with natural language processing",
            "Task reading and filtering with various criteria",
            "Task updates including status transitions",
            "Task deletion with proper validation",
            "Result serialization and formatting"
        ],
        "supported_operations": [
            "create",
            "read",
            "update", 
            "delete",
            "list"
        ]
    }


__all__ = ["create_task_graph"]
