"""
Date Tools

This module provides tools for date parsing and manipulation, including:
- parse_date: Parse a natural language date into a standardized format
"""

import logging
import re
from datetime import datetime, timedelta
from typing import Optional

import dateparser

from shared.core.tools.base import register_tool

# Set up logging
logger = logging.getLogger(__name__)


@register_tool
async def parse_date(date_string: str) -> Optional[str]:
    """
    Parse a natural language date into a standardized format.

    Args:
        date_string: The date string to parse

    Returns:
        The parsed date in ISO format, or None if parsing failed
    """
    logger.info(f"Parsing date: {date_string}")
    
    # Handle empty input
    if not date_string or not date_string.strip():
        logger.warning("Empty date string")
        return None
    
    # Clean up the date string
    date_string = date_string.strip().lower()
    
    # Handle special cases
    if date_string in ["today", "now"]:
        return datetime.now().strftime("%Y-%m-%d")
    
    if date_string == "tomorrow":
        return (datetime.now() + timedelta(days=1)).strftime("%Y-%m-%d")
    
    if date_string == "yesterday":
        return (datetime.now() - timedelta(days=1)).strftime("%Y-%m-%d")
    
    # Handle "next X" and "this X" expressions
    next_match = re.match(
        r"next\s+(monday|tuesday|wednesday|thursday|friday|saturday|sunday|week|month|year)",
        date_string,
    )
    if next_match:
        unit = next_match.group(1)
        if unit in [
            "monday",
            "tuesday",
            "wednesday",
            "thursday",
            "friday",
            "saturday",
            "sunday",
        ]:
            # Map day names to weekday numbers (0 = Monday, 6 = Sunday)
            day_map = {
                "monday": 0,
                "tuesday": 1,
                "wednesday": 2,
                "thursday": 3,
                "friday": 4,
                "saturday": 5,
                "sunday": 6
            }
            target_weekday = day_map[unit]
            
            # Get today's weekday (0 = Monday, 6 = Sunday)
            today_weekday = datetime.now().weekday()
            
            # Calculate days until next occurrence
            days_until = (target_weekday - today_weekday) % 7
            if days_until == 0:
                days_until = 7  # If today is the target day, go to next week
            
            return (datetime.now() + timedelta(days=days_until)).strftime("%Y-%m-%d")
        
        elif unit == "week":
            # Next week = 7 days from now
            return (datetime.now() + timedelta(days=7)).strftime("%Y-%m-%d")
        
        elif unit == "month":
            # Next month = same day next month
            now = datetime.now()
            month = now.month + 1
            year = now.year
            if month > 12:
                month = 1
                year += 1
            try:
                next_month = datetime(year, month, now.day)
            except ValueError:
                # Handle edge cases like Feb 30
                next_month = datetime(year, month + 1, 1) - timedelta(days=1)
            return next_month.strftime("%Y-%m-%d")
        
        elif unit == "year":
            # Next year = same day next year
            now = datetime.now()
            try:
                next_year = datetime(now.year + 1, now.month, now.day)
            except ValueError:
                # Handle Feb 29 in leap years
                next_year = datetime(now.year + 1, now.month, 28)
            return next_year.strftime("%Y-%m-%d")
    
    # Handle "in X days/weeks/months/years" expressions
    in_match = re.match(r"in\s+(\d+)\s+(day|week|month|year)s?", date_string)
    if in_match:
        count = int(in_match.group(1))
        unit = in_match.group(2)
        
        if unit == "day":
            return (datetime.now() + timedelta(days=count)).strftime("%Y-%m-%d")
        
        elif unit == "week":
            return (datetime.now() + timedelta(days=count * 7)).strftime("%Y-%m-%d")
        
        elif unit == "month":
            # Add months
            now = datetime.now()
            month = now.month + count
            year = now.year
            while month > 12:
                month -= 12
                year += 1
            try:
                future_date = datetime(year, month, now.day)
            except ValueError:
                # Handle edge cases like Feb 30
                future_date = datetime(year, month + 1, 1) - timedelta(days=1)
            return future_date.strftime("%Y-%m-%d")
        
        elif unit == "year":
            # Add years
            now = datetime.now()
            try:
                future_date = datetime(now.year + count, now.month, now.day)
            except ValueError:
                # Handle Feb 29 in leap years
                future_date = datetime(now.year + count, now.month, 28)
            return future_date.strftime("%Y-%m-%d")
    
    # Use dateparser for other cases
    try:
        parsed_date = dateparser.parse(
            date_string,
            settings={
                'PREFER_DAY_OF_MONTH': 'first',
                'PREFER_DATES_FROM': 'future',
                'RETURN_AS_TIMEZONE_AWARE': False
            }
        )
        
        if parsed_date:
            return parsed_date.strftime("%Y-%m-%d")
        else:
            logger.warning(f"Failed to parse date: {date_string}")
            return None
    except Exception as e:
        logger.error(f"Error parsing date: {str(e)}")
        return None
