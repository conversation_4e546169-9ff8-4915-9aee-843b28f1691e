"""
Task Database Tools

This module provides database tools for task operations, including:
- create_task: Create a new task
- get_task: Get a task by ID
- list_tasks: List tasks with filtering
- update_task: Update an existing task
- delete_task: Delete a task
"""

import logging
from datetime import datetime
from typing import Any, Dict, List, Optional

from shared.core.db.supabase import get_supabase_client
from shared.core.tools.base import register_tool

# Set up logging
logger = logging.getLogger(__name__)


@register_tool
async def create_task(
    title: str,
    description: Optional[str] = None,
    status: str = "todo",
    due_date: Optional[str] = None,
    assigned_to: Optional[str] = None,
    related_case: Optional[str] = None,
    tenant_id: str = None,
    created_by: str = None,
    ai_metadata: Optional[Dict[str, Any]] = None
) -> Dict[str, Any]:
    """
    Create a new task.

    Args:
        title: The task title
        description: The task description
        status: The task status (todo, in_progress, done)
        due_date: The task due date (ISO format)
        assigned_to: The user ID of the assignee
        related_case: The case ID related to the task
        tenant_id: The tenant ID
        created_by: The user ID of the creator
        ai_metadata: Additional metadata for AI processing

    Returns:
        The created task
    """
    logger.info(f"Creating task: {title}")
    
    # Get Supabase client
    supabase = get_supabase_client()
    
    # Prepare task data
    task_data = {
        "title": title,
        "description": description,
        "status": status,
        "due_date": due_date,
        "assigned_to": assigned_to,
        "related_case": related_case,
        "tenant_id": tenant_id,
        "created_by": created_by,
        "ai_metadata": ai_metadata or {}
    }
    
    try:
        # Insert the task
        result = await (
            supabase.schema("tenants").table("tasks").insert(task_data).execute()
        )
        
        if result.data and len(result.data) > 0:
            logger.info(f"Task created: {result.data[0].get('id')}")
            return result.data[0]
        else:
            logger.warning("Failed to create task")
            return None
    except Exception as e:
        logger.error(f"Error creating task: {str(e)}")
        raise


@register_tool
async def get_task(task_id: str, tenant_id: str = None) -> Optional[Dict[str, Any]]:
    """
    Get a task by ID.

    Args:
        task_id: The task ID
        tenant_id: The tenant ID

    Returns:
        The task, or None if not found
    """
    logger.info(f"Getting task: {task_id}")
    
    # Get Supabase client
    supabase = get_supabase_client()
    
    try:
        # Get the task
        result = await (
            supabase.schema("tenants")
            .table("tasks")
            .select(
                "*, assignee:assigned_to(id, email, first_name, last_name), "
                "case:related_case(id, title)"
            )
            .eq("id", task_id)
            .eq("tenant_id", tenant_id)
            .execute()
        )
        
        if result.data and len(result.data) > 0:
            task = result.data[0]
            
            # Add assignee name for convenience
            if task.get("assignee"):
                assignee = task["assignee"]
                task["assignee_name"] = (
                    f"{assignee.get('first_name', '')} "
                    f"{assignee.get('last_name', '')}".strip()
                    or assignee.get("email")
                )
            
            logger.info(f"Task found: {task.get('id')}")
            return task
        else:
            logger.warning(f"Task not found: {task_id}")
            return None
    except Exception as e:
        logger.error(f"Error getting task: {str(e)}")
        raise


@register_tool
async def list_tasks(
    tenant_id: str,
    status: Optional[str] = None,
    priority: Optional[str] = None,
    assigned_to: Optional[str] = None,
    related_case: Optional[str] = None,
    due_date: Optional[str] = None,
    due_date_start: Optional[str] = None,
    due_date_end: Optional[str] = None,
    search_term: Optional[str] = None,
    limit: int = 20
) -> List[Dict[str, Any]]:
    """
    List tasks with filtering.

    Args:
        tenant_id: The tenant ID
        status: Filter by task status
        priority: Filter by task priority
        assigned_to: Filter by assignee
        related_case: Filter by related case
        due_date: Filter by due date
        due_date_start: Filter by due date (start)
        due_date_end: Filter by due date (end)
        search_term: Search term for title and description
        limit: Maximum number of tasks to return

    Returns:
        A list of tasks
    """
    logger.info(f"Listing tasks for tenant: {tenant_id}")
    
    # Get Supabase client
    supabase = get_supabase_client()
    
    try:
        # Build the query
        query = (
            supabase.schema("tenants")
            .table("tasks")
            .select(
                "*, assignee:assigned_to(id, email, first_name, last_name), "
                "case:related_case(id, title)"
            )
            .eq("tenant_id", tenant_id)
        )
        
        # Apply filters
        if status:
            query = query.eq("status", status)
        
        if priority:
            query = query.eq("priority", priority)
        
        if assigned_to:
            query = query.eq("assigned_to", assigned_to)
        
        if related_case:
            query = query.eq("related_case", related_case)
        
        if due_date:
            query = query.eq("due_date", due_date)
        
        if due_date_start:
            query = query.gte("due_date", due_date_start)
        
        if due_date_end:
            query = query.lte("due_date", due_date_end)
        
        if search_term:
            query = query.or_(
                f"title.ilike.%{search_term}%,"
                f"description.ilike.%{search_term}%"
            )
        
        # Apply limit
        query = query.limit(limit)
        
        # Execute the query
        result = await query.execute()
        
        if result.data:
            tasks = result.data
            
            # Add assignee name for convenience
            for task in tasks:
                if task.get("assignee"):
                    assignee = task["assignee"]
                    task["assignee_name"] = (
                        f"{assignee.get('first_name', '')} "
                        f"{assignee.get('last_name', '')}".strip()
                        or assignee.get("email")
                    )
            
            logger.info(f"Found {len(tasks)} tasks")
            return tasks
        else:
            logger.info("No tasks found")
            return []
    except Exception as e:
        logger.error(f"Error listing tasks: {str(e)}")
        raise


@register_tool
async def update_task(
    task_id: str,
    update_data: Dict[str, Any],
    tenant_id: str = None
) -> Optional[Dict[str, Any]]:
    """
    Update an existing task.

    Args:
        task_id: The task ID
        update_data: The update data
        tenant_id: The tenant ID

    Returns:
        The updated task, or None if not found
    """
    logger.info(f"Updating task: {task_id}")
    
    # Get Supabase client
    supabase = get_supabase_client()
    
    try:
        # Add updated_at timestamp
        update_data["updated_at"] = datetime.now().isoformat()
        
        # Update the task
        result = await (
            supabase.schema("tenants")
            .table("tasks")
            .update(update_data)
            .eq("id", task_id)
            .eq("tenant_id", tenant_id)
            .select(
                "*, assignee:assigned_to(id, email, first_name, last_name), "
                "case:related_case(id, title)"
            )
            .execute()
        )
        
        if result.data and len(result.data) > 0:
            task = result.data[0]
            
            # Add assignee name for convenience
            if task.get("assignee"):
                assignee = task["assignee"]
                task["assignee_name"] = (
                    f"{assignee.get('first_name', '')} "
                    f"{assignee.get('last_name', '')}".strip()
                    or assignee.get("email")
                )
            
            logger.info(f"Task updated: {task.get('id')}")
            return task
        else:
            logger.warning(f"Task not found: {task_id}")
            return None
    except Exception as e:
        logger.error(f"Error updating task: {str(e)}")
        raise


@register_tool
async def delete_task(task_id: str, tenant_id: str = None) -> bool:
    """
    Delete a task.

    Args:
        task_id: The task ID
        tenant_id: The tenant ID

    Returns:
        True if the task was deleted, False otherwise
    """
    logger.info(f"Deleting task: {task_id}")
    
    # Get Supabase client
    supabase = get_supabase_client()
    
    try:
        # Delete the task
        result = await (
            supabase.schema("tenants")
            .table("tasks")
            .delete()
            .eq("id", task_id)
            .eq("tenant_id", tenant_id)
            .execute()
        )
        
        if result.data and len(result.data) > 0:
            logger.info(f"Task deleted: {task_id}")
            return True
        else:
            logger.warning(f"Task not found: {task_id}")
            return False
    except Exception as e:
        logger.error(f"Error deleting task: {str(e)}")
        raise
