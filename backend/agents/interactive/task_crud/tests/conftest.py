"""
Pytest Configuration for Task CRUD Agent Tests

This module provides fixtures and configuration for the Task CRUD Agent tests.
"""

import importlib
import os
import uuid
from datetime import datetime, timedelta
from typing import Optional
from unittest.mock import AsyncMock, MagicMock

import pytest
from sqlalchemy import event

# Verify that the shared core modules are available
try:
    importlib.import_module("shared.core.tools.executor")
    importlib.import_module("shared.core.base_agent")
    importlib.import_module("shared.core.state")
    importlib.import_module("shared.core.llm.voyage")
    importlib.import_module("shared.core.db.supabase")
except ImportError as e:
    pytest.fail(f"Required shared core module not found: {e}")

# Import the modules
# Create a test-specific Task model without schema for SQLite compatibility
from uuid import uuid4

from sqlalchemy import JSON, CheckConstraint, DateTime, String, Text
from sqlalchemy.orm import Mapped, mapped_column

from backend.agents.interactive.task_crud.agent import TaskCrudAgent

# Import SQLAlchemy models
from backend.models import Base


class TaskModel(Base):
    """Test-specific SQLAlchemy model for tasks (without schema for SQLite)."""

    __tablename__ = "tasks"
    __table_args__ = (
        CheckConstraint(
            "status IN ('todo', 'in_progress', 'done')",
            name="tasks_status_check"
        ),
        CheckConstraint(
            "priority IN ('low', 'medium', 'high', 'urgent')",
            name="tasks_priority_check"
        ),
    )

    # Primary key
    id: Mapped[str] = mapped_column(
        String(36), primary_key=True, default=lambda: str(uuid4())
    )

    # Tenant isolation
    tenant_id: Mapped[str] = mapped_column(
        String(36), nullable=False, index=True
    )

    # Basic task information
    title: Mapped[str] = mapped_column(String(255), nullable=False)
    description: Mapped[Optional[str]] = mapped_column(Text, nullable=True)
    status: Mapped[str] = mapped_column(String(50), nullable=False, default="todo")
    priority: Mapped[Optional[str]] = mapped_column(String(50), nullable=True)

    # Dates
    due_date: Mapped[Optional[datetime]] = mapped_column(DateTime, nullable=True)
    created_at: Mapped[datetime] = mapped_column(
        DateTime, nullable=False, default=datetime.utcnow
    )
    updated_at: Mapped[Optional[datetime]] = mapped_column(
        DateTime,
        nullable=True,
        default=datetime.utcnow,
        onupdate=datetime.utcnow,
    )

    # Relationships
    assigned_to: Mapped[Optional[str]] = mapped_column(
        String(36), nullable=True, index=True
    )
    related_case: Mapped[Optional[str]] = mapped_column(
        String(36), nullable=True, index=True
    )
    created_by: Mapped[str] = mapped_column(String(36), nullable=False)
    updated_by: Mapped[Optional[str]] = mapped_column(String(36), nullable=True)

    # AI metadata
    ai_metadata: Mapped[dict] = mapped_column(JSON, nullable=False, default=dict)

    def __repr__(self) -> str:
        """Return a string representation of the task."""
        return (
            f"<TaskModel(id={self.id}, title='{self.title}', "
            f"status='{self.status}')>"
        )

# Import mock database
from backend.agents.interactive.task_crud.tests.mocks.mock_db import (
    patch_task_db_tools,
    setup_mock_task_database,
)


# Set up test database URL environment variable
@pytest.fixture(autouse=True)
def setup_test_database_url():
    """Set the TEST_DATABASE_URL environment variable before tests run."""
    test_db_url = "sqlite:///:memory:"
    os.environ["TEST_DATABASE_URL"] = test_db_url
    yield test_db_url
    # Clean up
    if "TEST_DATABASE_URL" in os.environ:
        del os.environ["TEST_DATABASE_URL"]


@pytest.fixture
def db_session():
    """
    Fixture that creates all tables, yields a SQLAlchemy session, then drops tables.

    This fixture provides a clean database session for each test.
    Note: Using synchronous SQLite for simplicity in tests.
    """
    from sqlalchemy import MetaData, create_engine
    from sqlalchemy.orm import sessionmaker

    # Get the test database URL
    test_db_url = os.getenv("TEST_DATABASE_URL", "sqlite:///:memory:")

    # Create synchronous engine for testing
    engine = create_engine(
        test_db_url,
        echo=False,  # Set to True for SQL query logging during tests
    )

    # Set up SQLite foreign key constraints if using SQLite
    if test_db_url.startswith("sqlite"):
        @event.listens_for(engine, "connect")
        def set_sqlite_pragma(dbapi_connection, connection_record):
            cursor = dbapi_connection.cursor()
            cursor.execute("PRAGMA foreign_keys=ON")
            cursor.close()

    # Create test metadata and tables
    test_metadata = MetaData()
    TaskModel.__table__.to_metadata(test_metadata)
    test_metadata.create_all(engine)

    # Create session factory
    session_factory = sessionmaker(bind=engine)

    # Create and yield session
    session = session_factory()
    try:
        yield session
    finally:
        session.close()

    # Drop all tables
    test_metadata.drop_all(engine)

    # Dispose of the engine
    engine.dispose()


@pytest.fixture
def mock_task_database():
    """Fixture for mock task database."""
    db = setup_mock_task_database()
    with patch_task_db_tools(db):
        yield db


@pytest.fixture
def tenant_id():
    """Fixture for tenant ID."""
    return str(uuid.uuid4())


@pytest.fixture
def user_id():
    """Fixture for user ID."""
    return str(uuid.uuid4())


@pytest.fixture
def thread_id():
    """Fixture for thread ID."""
    return str(uuid.uuid4())


@pytest.fixture
def sample_task():
    """Fixture for a sample task."""
    return {
        "id": str(uuid.uuid4()),
        "tenant_id": str(uuid.uuid4()),
        "title": "Sample Task",
        "description": "This is a sample task for testing",
        "status": "todo",
        "priority": "medium",
        "due_date": (datetime.now() + timedelta(days=7)).strftime("%Y-%m-%d"),
        "assigned_to": str(uuid.uuid4()),
        "related_case": str(uuid.uuid4()),
        "created_by": str(uuid.uuid4()),
        "created_at": datetime.now().isoformat(),
        "updated_by": None,
        "updated_at": None,
        "ai_metadata": {}
    }


@pytest.fixture
def sample_tasks():
    """Fixture for a list of sample tasks."""
    return [
        {
            "id": str(uuid.uuid4()),
            "tenant_id": str(uuid.uuid4()),
            "title": f"Task {i}",
            "description": f"This is task {i} for testing",
            "status": ["todo", "in_progress", "done"][i % 3],
            "priority": ["low", "medium", "high", "urgent"][i % 4],
            "due_date": (datetime.now() + timedelta(days=i)).strftime("%Y-%m-%d"),
            "assigned_to": str(uuid.uuid4()),
            "related_case": str(uuid.uuid4()),
            "created_by": str(uuid.uuid4()),
            "created_at": datetime.now().isoformat(),
            "updated_by": None,
            "updated_at": None,
            "ai_metadata": {}
        }
        for i in range(10)
    ]


@pytest.fixture
def empty_state(tenant_id, user_id, thread_id):
    """Fixture for an empty state."""
    return {
        "messages": [],
        "user_context": {},
        "thread_id": thread_id,
        "tenant_id": tenant_id,
        "operation": None,
        "task_id": None,
        "task_data": {},
        "task_filter": {},
        "tasks": [],
        "current_task": None,
        "parsed_date": None,
        "error": None,
        "next": None
    }


@pytest.fixture
def config(tenant_id, user_id):
    """Fixture for runnable configuration."""
    return {
        "configurable": {
            "tenant_id": tenant_id,
            "user_id": user_id,
            "thread_id": str(uuid.uuid4())
        }
    }


@pytest.fixture
def task_crud_agent():
    """Fixture for Task CRUD Agent."""
    # Create mock dependencies
    tool_executor = AsyncMock()
    tool_executor.execute_tool.return_value = None

    db_client = MagicMock()

    llm_client = AsyncMock()
    llm_client.chat_completion.return_value = {
        "choices": [
            {
                "message": {
                    "role": "assistant",
                    "content": "I'll help you with that task."
                }
            }
        ]
    }

    # Create the agent with mock dependencies
    return TaskCrudAgent(
        tool_executor=tool_executor,
        db_client=db_client
    )


@pytest.fixture
def human_message(content: str):
    """Fixture for creating a human message."""
    return {
        "role": "user",
        "content": content,
        "metadata": {}
    }


@pytest.fixture
def state_with_human_message(empty_state, human_message):
    """Fixture for state with a human message."""
    state = empty_state.copy()
    state["messages"] = [human_message]
    return state
