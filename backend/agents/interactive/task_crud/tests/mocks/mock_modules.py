"""
Mock Modules for Testing

This module provides mock implementations of external dependencies for testing.
"""

import sys
from unittest.mock import AsyncMock, MagicMock


# Create mock classes
class MockBaseAgent:
    """Mock implementation of BaseAgent."""
    
    def __init__(self, config=None):
        self.config = config or {}
        self.name = "mock_agent"
        self.agent_type = "mock"
        self.version = "1.0.0"
        self.nodes = {
            "create_task": MagicMock(),
            "read_task": <PERSON><PERSON>ock(),
            "update_task": MagicMock(),
            "delete_task": <PERSON>Mock(),
            "parse_date": MagicMock(),
        }
    
    async def initialize(self, state, config):
        return state
    
    async def execute(self, state, config):
        return state
    
    async def cleanup(self, state, config):
        return state


class MockAiLexState:
    """Mock implementation of AiLexState."""
    
    def __init__(self):
        self.messages = []
        self.user_context = {}
        self.thread_id = "mock-thread-id"
        self.tenant_id = "mock-tenant-id"
        self.metadata = {}
        self.next = None


class MockVoyageClient:
    """Mock implementation of VoyageClient."""
    
    def __init__(self):
        pass


class MockToolExecutor:
    """Mock implementation of ToolExecutor."""
    
    def __init__(self):
        self.execute_tool = AsyncMock()
    
    async def execute_tool(self, tool_name, tool_args, tenant_id):
        return None


# Create mock modules
def setup_mock_modules():
    """Set up mock modules for testing."""
    
    # Create mock modules
    mock_modules = {
        'shared.core.base_agent': MagicMock(),
        'shared.core.state': MagicMock(),
        'shared.core.tools.executor': MagicMock(),
        'shared.core.llm.voyage': MagicMock(),
        'shared.core.db.supabase': MagicMock(),
        'shared.core.tools.base': MagicMock(),
    }
    
    # Set up BaseAgent
    mock_modules['shared.core.base_agent'].BaseAgent = MockBaseAgent
    
    # Set up AiLexState
    mock_modules['shared.core.state'].AiLexState = MockAiLexState
    
    # Set up ToolExecutor
    mock_modules['shared.core.tools.executor'].get_tool_executor = (
        lambda: MockToolExecutor()
    )
    
    # Set up VoyageClient
    mock_modules['shared.core.llm.voyage'].VoyageClient = MockVoyageClient
    
    # Set up Supabase client
    mock_modules['shared.core.db.supabase'].get_supabase_client = lambda: MagicMock()
    
    # Set up register_tool
    mock_modules['shared.core.tools.base'].register_tool = lambda func: func
    
    # Add modules to sys.modules
    for name, module in mock_modules.items():
        sys.modules[name] = module
    
    return mock_modules
