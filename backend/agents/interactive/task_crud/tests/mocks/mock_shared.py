"""
Mock Shared Core Modules

This module provides mock implementations of shared core modules for testing.
"""

from typing import Any, Dict
from unittest.mock import AsyncMock


class MockToolExecutor:
    """
    Mock implementation of the tool executor.
    
    This class provides a simple mock for the tool executor used in the Task CRUD Agent.
    """
    
    def __init__(self):
        """Initialize the mock tool executor."""
        self.execute_tool = AsyncMock()
    
    async def execute_tool(
        self, tool_name: str, tool_args: Dict[str, Any], tenant_id: str
    ) -> Any:
        """
        Execute a tool.
        
        Args:
            tool_name: The name of the tool to execute
            tool_args: The arguments for the tool
            tenant_id: The tenant ID
            
        Returns:
            The result of the tool execution
        """
        return None


def get_tool_executor() -> MockToolExecutor:
    """
    Get a mock tool executor.
    
    Returns:
        A mock tool executor
    """
    return MockToolExecutor()


class MockVoyageClient:
    """
    Mock implementation of the Voyage client.
    
    This class provides a simple mock for the Voyage client used in the Task CRUD Agent.
    """
    
    def __init__(self):
        """Initialize the mock Voyage client."""
        pass


def get_supabase_client():
    """
    Get a mock Supabase client.
    
    Returns:
        A mock Supabase client
    """
    return None
