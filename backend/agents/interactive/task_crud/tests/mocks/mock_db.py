"""
Mock Database Implementation for Task CRUD Agent Tests

This module provides a mock database implementation for the Task CRUD Agent tests.
It allows testing database operations without an actual database connection.
"""

import uuid
from contextlib import contextmanager
from datetime import datetime
from typing import Any, Dict, List, Optional
from unittest.mock import patch


class MockTaskDatabase:
    """
    Mock implementation of a task database.
    
    This class provides a simple in-memory database for testing task CRUD operations.
    """
    
    def __init__(self):
        """Initialize the mock database."""
        self.tasks = {}
    
    def add_task(self, task_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Add a task to the database.
        
        Args:
            task_data: The task data
            
        Returns:
            The created task
        """
        # Generate an ID if not provided
        if "id" not in task_data:
            task_data["id"] = str(uuid.uuid4())
        
        # Add timestamps if not provided
        if "created_at" not in task_data:
            task_data["created_at"] = datetime.now().isoformat()
        
        # Store the task
        self.tasks[task_data["id"]] = task_data
        
        return task_data
    
    def get_task(self, task_id: str, tenant_id: str = None) -> Optional[Dict[str, Any]]:
        """
        Get a task by ID.
        
        Args:
            task_id: The task ID
            tenant_id: The tenant ID
            
        Returns:
            The task, or None if not found
        """
        task = self.tasks.get(task_id)
        
        if task and (tenant_id is None or task.get("tenant_id") == tenant_id):
            return task
        
        return None
    
    def list_tasks(
        self,
        tenant_id: str,
        status: Optional[str] = None,
        priority: Optional[str] = None,
        assigned_to: Optional[str] = None,
        related_case: Optional[str] = None,
        due_date: Optional[str] = None,
        due_date_start: Optional[str] = None,
        due_date_end: Optional[str] = None,
        search_term: Optional[str] = None,
        limit: int = 20
    ) -> List[Dict[str, Any]]:
        """
        List tasks with filtering.
        
        Args:
            tenant_id: The tenant ID
            status: Filter by task status
            priority: Filter by task priority
            assigned_to: Filter by assignee
            related_case: Filter by related case
            due_date: Filter by due date
            due_date_start: Filter by due date (start)
            due_date_end: Filter by due date (end)
            search_term: Search term for title and description
            limit: Maximum number of tasks to return
            
        Returns:
            A list of tasks
        """
        # Filter tasks by tenant ID
        filtered_tasks = [
            task for task in self.tasks.values()
            if task.get("tenant_id") == tenant_id
        ]
        
        # Apply additional filters
        if status:
            filtered_tasks = [
                task for task in filtered_tasks if task.get("status") == status
            ]
        
        if priority:
            filtered_tasks = [
                task for task in filtered_tasks if task.get("priority") == priority
            ]
        
        if assigned_to:
            filtered_tasks = [
                task
                for task in filtered_tasks
                if task.get("assigned_to") == assigned_to
            ]
        
        if related_case:
            filtered_tasks = [
                task
                for task in filtered_tasks
                if task.get("related_case") == related_case
            ]
        
        if due_date:
            filtered_tasks = [
                task for task in filtered_tasks if task.get("due_date") == due_date
            ]
        
        if due_date_start:
            filtered_tasks = [
                task
                for task in filtered_tasks
                if task.get("due_date") and task.get("due_date") >= due_date_start
            ]
        
        if due_date_end:
            filtered_tasks = [
                task
                for task in filtered_tasks
                if task.get("due_date") and task.get("due_date") <= due_date_end
            ]
        
        if search_term:
            filtered_tasks = [
                task for task in filtered_tasks
                if (search_term.lower() in task.get("title", "").lower() or
                    search_term.lower() in task.get("description", "").lower())
            ]
        
        # Apply limit
        return filtered_tasks[:limit]
    
    def update_task(
        self,
        task_id: str,
        update_data: Dict[str, Any],
        tenant_id: str = None
    ) -> Optional[Dict[str, Any]]:
        """
        Update an existing task.
        
        Args:
            task_id: The task ID
            update_data: The update data
            tenant_id: The tenant ID
            
        Returns:
            The updated task, or None if not found
        """
        task = self.get_task(task_id, tenant_id)
        
        if task:
            # Update the task
            for key, value in update_data.items():
                if value is not None:
                    task[key] = value
            
            # Add updated_at timestamp
            task["updated_at"] = datetime.now().isoformat()
            
            # Store the updated task
            self.tasks[task_id] = task
            
            return task
        
        return None
    
    def delete_task(self, task_id: str, tenant_id: str = None) -> bool:
        """
        Delete a task.
        
        Args:
            task_id: The task ID
            tenant_id: The tenant ID
            
        Returns:
            True if the task was deleted, False otherwise
        """
        task = self.get_task(task_id, tenant_id)
        
        if task:
            # Delete the task
            del self.tasks[task_id]
            return True
        
        return False


def setup_mock_task_database() -> MockTaskDatabase:
    """
    Set up a mock task database.
    
    Returns:
        A mock task database
    """
    return MockTaskDatabase()


@contextmanager
def patch_task_db_tools(db: MockTaskDatabase):
    """
    Patch the task database tools to use the mock database.
    
    Args:
        db: The mock database
    """
    # Define mock implementations
    async def mock_create_task(**kwargs):
        return db.add_task(kwargs)
    
    async def mock_get_task(task_id, tenant_id=None):
        return db.get_task(task_id, tenant_id)
    
    async def mock_list_tasks(tenant_id, **kwargs):
        return db.list_tasks(tenant_id, **kwargs)
    
    async def mock_update_task(task_id, update_data, tenant_id=None):
        return db.update_task(task_id, update_data, tenant_id)
    
    async def mock_delete_task(task_id, tenant_id=None):
        return db.delete_task(task_id, tenant_id)
    
    # Apply patches
    with (
        patch(
            "backend.agents.interactive.task_crud.tools.task_db.create_task",
            mock_create_task,
        ),
        patch(
            "backend.agents.interactive.task_crud.tools.task_db.get_task", mock_get_task
        ),
        patch(
            "backend.agents.interactive.task_crud.tools.task_db.list_tasks",
            mock_list_tasks,
        ),
        patch(
            "backend.agents.interactive.task_crud.tools.task_db.update_task",
            mock_update_task,
        ),
        patch(
            "backend.agents.interactive.task_crud.tools.task_db.delete_task",
            mock_delete_task,
        ),
    ):
        yield
