"""
Return Result Node

This module provides a node for serializing task operation results to a
dictionary format.
It handles the final step of the task CRUD workflow by preparing the result
for return.

Usage:
    from backend.agents.interactive.task_crud.nodes.return_result import return_result

    # Add the node to the StateGraph
    sg.add_node("return_result", return_result)
"""

import logging
from datetime import datetime
from typing import Any, Dict

from langchain_core.runnables import RunnableConfig

# Set up logging
logger = logging.getLogger(__name__)


async def return_result(
    state: Dict[str, Any], config: RunnableConfig
) -> Dict[str, Any]:
    """
    Return result node for task CRUD operations.
    
    This node serializes the task operation result to a dictionary format
    and prepares the final response for the user.
    
    Args:
        state: Current workflow state containing operation results
        config: Runnable configuration
        
    Returns:
        Dict[str, Any]: Updated state with serialized result
    """
    logger.info("Serializing task operation result")
    
    try:
        # Get the operation type
        operation = state.get("operation") or state.get("intent")
        
        # Initialize result structure
        result = {
            "operation": operation,
            "success": True,
            "timestamp": datetime.utcnow().isoformat(),
            "data": None,
            "message": None
        }
        
        # Handle different operation types
        if operation == "create":
            current_task = state.get("current_task")
            if current_task:
                result["data"] = current_task
                result["message"] = (
                    f"Successfully created task: "
                    f"{current_task.get('title', 'Untitled')}"
                )
            else:
                result["success"] = False
                result["message"] = "Failed to create task"
                
        elif operation in ["read", "list"]:
            tasks = state.get("tasks", [])
            result["data"] = tasks
            task_count = len(tasks)
            if task_count == 0:
                result["message"] = "No tasks found matching your criteria"
            elif task_count == 1:
                result["message"] = "Found 1 task"
            else:
                result["message"] = f"Found {task_count} tasks"
                
        elif operation == "update":
            current_task = state.get("current_task")
            if current_task:
                result["data"] = current_task
                result["message"] = (
                    f"Successfully updated task: "
                    f"{current_task.get('title', 'Untitled')}"
                )
            else:
                result["success"] = False
                result["message"] = "Failed to update task"
                
        elif operation == "delete":
            task_id = state.get("task_id")
            if task_id:
                result["data"] = {"task_id": task_id}
                result["message"] = "Successfully deleted task"
            else:
                result["success"] = False
                result["message"] = "Failed to delete task"
        else:
            result["success"] = False
            result["message"] = f"Unknown operation: {operation}"
        
        # Check for errors in state
        error = state.get("error")
        if error:
            result["success"] = False
            result["message"] = error
            result["error"] = error
        
        # Update state with result
        updated_state = {
            **state,
            "result": result
        }
        
        # Add final message to conversation
        messages = state.get("messages", [])
        final_message = {
            "role": "assistant",
            "content": result["message"],
            "metadata": {
                "operation": operation,
                "success": result["success"],
                "timestamp": result["timestamp"]
            }
        }
        
        updated_state["messages"] = messages + [final_message]
        
        logger.info(f"Task operation result serialized successfully: {operation}")
        return updated_state
        
    except Exception as e:
        logger.error(f"Error serializing task operation result: {e}")
        
        # Return error result
        error_result = {
            "operation": state.get("operation", "unknown"),
            "success": False,
            "timestamp": datetime.utcnow().isoformat(),
            "data": None,
            "message": f"Error processing task operation: {str(e)}",
            "error": str(e)
        }
        
        messages = state.get("messages", [])
        error_message = {
            "role": "assistant",
            "content": error_result["message"],
            "metadata": {
                "operation": state.get("operation", "unknown"),
                "success": False,
                "error": str(e),
                "timestamp": error_result["timestamp"]
            }
        }
        
        return {
            **state,
            "result": error_result,
            "error": str(e),
            "messages": messages + [error_message]
        }


def _serialize_task(task: Dict[str, Any]) -> Dict[str, Any]:
    """
    Serialize a task object to a dictionary.
    
    Args:
        task: Task object to serialize
        
    Returns:
        Dict[str, Any]: Serialized task
    """
    if not task:
        return {}
    
    # Handle datetime serialization
    serialized = {}
    for key, value in task.items():
        if isinstance(value, datetime):
            serialized[key] = value.isoformat()
        elif hasattr(value, 'dict'):  # Pydantic model
            serialized[key] = value.dict()
        elif hasattr(value, '__dict__'):  # Regular object
            serialized[key] = value.__dict__
        else:
            serialized[key] = value
    
    return serialized


def _serialize_tasks(tasks: list) -> list:
    """
    Serialize a list of task objects.
    
    Args:
        tasks: List of task objects to serialize
        
    Returns:
        list: List of serialized tasks
    """
    return [_serialize_task(task) for task in tasks if task]
