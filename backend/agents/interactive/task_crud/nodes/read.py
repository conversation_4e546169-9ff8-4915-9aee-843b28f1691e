"""
Read Task Node

This module provides a node for reading and listing tasks with filtering
by status, date range, assignee, and other criteria.

Usage:
    from backend.agents.interactive.task_crud.nodes.read import read_task

    # Add the node to the StateGraph
    sg.add_node("read_task", read_task)
"""

import logging
import re
from datetime import datetime, timed<PERSON>ta
from typing import Any, Dict

from langchain_core.runnables import RunnableConfig

from shared.core.tools.executor import get_tool_executor

# Set up logging
logger = logging.getLogger(__name__)


async def read_task(state: Dict[str, Any], config: RunnableConfig) -> Dict[str, Any]:
    """
    Read or list tasks.

    Args:
        state: Current state
        config: Runnable configuration

    Returns:
        Updated state
    """
    logger.info("Executing read_task node")

    # Extract configurable values
    configurable = config.get("configurable", {})
    tenant_id = configurable.get("tenant_id", "unknown")
    _user_id = configurable.get("user_id", "unknown")

    # Get the last human message
    last_message = None
    for message in reversed(state.get("messages", [])):
        if message.get("role") == "user" or message.get("type") == "human":
            last_message = message
            break

    content = last_message.get("content", "") if last_message else ""

    # Extract filter criteria from the message
    filter_criteria = await _extract_filter_criteria(content, tenant_id)
    
    # If we have a parsed date in the state, use it for due date filtering
    if state.get("parsed_date"):
        filter_criteria["due_date"] = state["parsed_date"]
    
    # If we're looking for a specific task by ID
    if filter_criteria.get("task_id"):
        await _get_task_by_id(state, filter_criteria["task_id"], tenant_id)
    else:
        # Otherwise, list tasks with the filter criteria
        await _list_tasks(state, filter_criteria, tenant_id)
    
    # Set next to FINISH
    state["next"] = "FINISH"
    
    return state


async def _get_task_by_id(state: Dict[str, Any], task_id: str, tenant_id: str) -> None:
    """
    Get a task by ID.

    Args:
        state: Current state
        task_id: The task ID
        tenant_id: The tenant ID
    """
    logger.info(f"Getting task by ID: {task_id}")
    
    # Use the tool executor to get the task
    tool_executor = get_tool_executor()
    
    try:
        task = await tool_executor.execute_tool(
            tool_name="get_task",
            tool_args={"task_id": task_id},
            tenant_id=tenant_id
        )
        
        if task:
            logger.info(f"Found task: {task.get('id')}")
            state["current_task"] = task
            state["tasks"] = [task]
            
            # Format the task details
            _due_date = (
                f", due on {task.get('due_date')}" if task.get('due_date') else ""
            )
            _status = f", status: {task.get('status')}" if task.get('status') else ""
            _assignee = (
                f", assigned to {task.get('assignee_name')}"
                if task.get('assignee_name')
                else ""
            )
            
            state["messages"].append({
                "role": "assistant",
                "content": "Here's the task you requested:\n\n" +
                           f"**Title**: {task.get('title')}\n" +
                           (
                               f"**Description**: "
                               f"{task.get('description') or 'No description'}\n"
                           ) +
                           f"**Status**: {task.get('status')}\n" +
                           f"**Due Date**: {task.get('due_date') or 'No due date'}\n" +
                           (
                               f"**Assigned To**: "
                               f"{task.get('assignee_name') or 'Unassigned'}\n"
                           ),
                "metadata": {"task": task}
            })
        else:
            logger.warning(f"Task not found: {task_id}")
            state["error"] = f"Task not found: {task_id}"
            state["messages"].append({
                "role": "assistant",
                "content": (
                    f"I couldn't find a task with ID {task_id}. Please check "
                    "the ID and try again."
                ),
                "metadata": {"error": "task_not_found"}
            })
    except Exception as e:
        logger.error(f"Error getting task: {str(e)}")
        state["error"] = str(e)
        state["messages"].append({
            "role": "assistant",
            "content": (
                f"I encountered an error retrieving the task: {str(e)}. "
                "Please try again."
            ),
            "metadata": {"error": str(e)}
        })


async def _list_tasks(
    state: Dict[str,
    Any],
    filter_criteria: Dict[str,
    Any],
    tenant_id: str
) -> None:
    """
    List tasks with filter criteria.

    Args:
        state: Current state
        filter_criteria: The filter criteria
        tenant_id: The tenant ID
    """
    logger.info(f"Listing tasks with filter criteria: {filter_criteria}")
    
    # Use the tool executor to list tasks
    tool_executor = get_tool_executor()
    
    try:
        tasks = await tool_executor.execute_tool(
            tool_name="list_tasks",
            tool_args=filter_criteria,
            tenant_id=tenant_id
        )
        
        if tasks and len(tasks) > 0:
            logger.info(f"Found {len(tasks)} tasks")
            state["tasks"] = tasks
            
            # Format the task list
            task_list = "\n\n".join([
                f"**{i+1}. {task.get('title')}**\n" +
                f"   Status: {task.get('status')}\n" +
                (f"   Due: {task.get('due_date')}\n" if task.get('due_date') else "") +
                (
                    f"   Assigned to: {task.get('assignee_name')}\n"
                    if task.get('assignee_name')
                    else ""
                )
                for i, task in enumerate(tasks[:10])  # Limit to 10 tasks
            ])
            
            # Add a message with the task list
            filter_description = _format_filter_description(filter_criteria)
            
            state["messages"].append({
                "role": "assistant",
                "content": (
                    f"I found {len(tasks)} tasks{filter_description}:\n\n{task_list}"
                    + (
                        f"\n\n*Showing 10 of {len(tasks)} tasks*"
                        if len(tasks) > 10
                        else ""
                    )
                ),
                "metadata": {"tasks": tasks[:10], "total_count": len(tasks)}
            })
        else:
            logger.info("No tasks found")
            
            # Add a message indicating no tasks were found
            filter_description = _format_filter_description(filter_criteria)
            
            state["messages"].append({
                "role": "assistant",
                "content": f"I didn't find any tasks{filter_description}.",
                "metadata": {"tasks": [], "total_count": 0}
            })
    except Exception as e:
        logger.error(f"Error listing tasks: {str(e)}")
        state["error"] = str(e)
        state["messages"].append({
            "role": "assistant",
            "content": (
                f"I encountered an error listing tasks: {str(e)}. "
                "Please try again."
            ),
            "metadata": {"error": str(e)}
        })


def _format_filter_description(filter_criteria: Dict[str, Any]) -> str:
    """
    Format a description of the filter criteria.

    Args:
        filter_criteria: The filter criteria

    Returns:
        A formatted description
    """
    parts = []
    
    if filter_criteria.get("status"):
        parts.append(f"with status '{filter_criteria['status']}'")
    
    if filter_criteria.get("priority"):
        parts.append(f"with priority '{filter_criteria['priority']}'")
    
    if filter_criteria.get("assigned_to"):
        parts.append(
            f"assigned to "
            f"{filter_criteria['assigned_to_name'] or filter_criteria['assigned_to']}"
        )
    
    if filter_criteria.get("due_date"):
        parts.append(f"due on {filter_criteria['due_date']}")
    
    if filter_criteria.get("due_date_start") and filter_criteria.get("due_date_end"):
        parts.append(
            f"due between {filter_criteria['due_date_start']} and "
            f"{filter_criteria['due_date_end']}"
        )
    elif filter_criteria.get("due_date_start"):
        parts.append(f"due after {filter_criteria['due_date_start']}")
    elif filter_criteria.get("due_date_end"):
        parts.append(f"due before {filter_criteria['due_date_end']}")
    
    if filter_criteria.get("search_term"):
        parts.append(f"matching '{filter_criteria['search_term']}'")
    
    if parts:
        return " " + " and ".join(parts)
    else:
        return ""


async def _extract_filter_criteria(content: str, tenant_id: str) -> Dict[str, Any]:
    """
    Extract filter criteria from a message.

    Args:
        content: The message content
        tenant_id: The tenant ID

    Returns:
        A dictionary with filter criteria
    """
    # Initialize filter criteria with defaults
    filter_criteria = {
        "status": None,
        "priority": None,
        "assigned_to": None,
        "related_case": None,
        "due_date": None,
        "due_date_start": None,
        "due_date_end": None,
        "search_term": None,
        "task_id": None
    }
    
    # Check for task ID
    task_id_patterns = [
        r"task\s+(?:id|number)[:\s]+([a-f0-9\-]+)",
        r"id[:\s]+([a-f0-9\-]+)"
    ]
    
    for pattern in task_id_patterns:
        match = re.search(pattern, content, re.IGNORECASE)
        if match:
            filter_criteria["task_id"] = match.group(1).strip()
            return filter_criteria
    
    # Extract status
    status_patterns = [
        r"status[:\s]+(todo|in\s*progress|done)",
        r"(todo|in\s*progress|done)\s+tasks"
    ]
    
    for pattern in status_patterns:
        match = re.search(pattern, content, re.IGNORECASE)
        if match:
            _status = match.group(1).strip().lower()
            if _status == "in progress":
                _status = "in_progress"
            filter_criteria["status"] = _status
            break
    
    # Extract priority
    priority_patterns = [
        r"priority[:\s]+(low|medium|high|urgent)",
        r"(low|medium|high|urgent)\s+priority"
    ]
    
    for pattern in priority_patterns:
        match = re.search(pattern, content, re.IGNORECASE)
        if match:
            filter_criteria["priority"] = match.group(1).strip().lower()
            break
    
    # Extract assignee
    assignee_patterns = [
        r"assign(?:ed)?\s+to[:\s]+([^\n]+)",
        r"assignee[:\s]+([^\n]+)",
        r"tasks\s+(?:for|of)\s+([^\n]+)"
    ]
    
    for pattern in assignee_patterns:
        match = re.search(pattern, content, re.IGNORECASE)
        if match:
            _assignee = match.group(1).strip()
            # Store the name for display purposes
            filter_criteria["assigned_to_name"] = _assignee
            # We'll need to look up the user ID based on the name
            filter_criteria["assigned_to"] = _assignee
            break
    
    # Extract related case
    case_patterns = [
        r"(?:related|for)\s+case[:\s]+([^\n]+)",
        r"case[:\s]+([^\n]+)"
    ]
    
    for pattern in case_patterns:
        match = re.search(pattern, content, re.IGNORECASE)
        if match:
            case = match.group(1).strip()
            # We'll need to look up the case ID based on the name
            filter_criteria["related_case"] = case
            break
    
    # Extract due date range
    due_date_range_patterns = [
        r"due\s+(?:date\s+)?between\s+([^\n]+)\s+and\s+([^\n]+)",
        r"due\s+(?:date\s+)?from\s+([^\n]+)\s+to\s+([^\n]+)"
    ]
    
    for pattern in due_date_range_patterns:
        match = re.search(pattern, content, re.IGNORECASE)
        if match:
            filter_criteria["due_date_start"] = match.group(1).strip()
            filter_criteria["due_date_end"] = match.group(2).strip()
            break
    
    # Extract due date start
    due_date_start_patterns = [
        r"due\s+(?:date\s+)?after\s+([^\n]+)",
        r"due\s+(?:date\s+)?from\s+([^\n]+)"
    ]
    
    for pattern in due_date_start_patterns:
        match = re.search(pattern, content, re.IGNORECASE)
        if match and not filter_criteria["due_date_start"]:
            filter_criteria["due_date_start"] = match.group(1).strip()
            break
    
    # Extract due date end
    due_date_end_patterns = [
        r"due\s+(?:date\s+)?before\s+([^\n]+)",
        r"due\s+(?:date\s+)?by\s+([^\n]+)",
        r"due\s+(?:date\s+)?until\s+([^\n]+)"
    ]
    
    for pattern in due_date_end_patterns:
        match = re.search(pattern, content, re.IGNORECASE)
        if match and not filter_criteria["due_date_end"]:
            filter_criteria["due_date_end"] = match.group(1).strip()
            break
    
    # Extract search term
    search_patterns = [
        r"search\s+(?:for\s+)?([^\n]+)",
        r"find\s+(?:tasks\s+)?(?:with|containing)\s+([^\n]+)",
        r"tasks\s+(?:with|containing)\s+([^\n]+)"
    ]
    
    for pattern in search_patterns:
        match = re.search(pattern, content, re.IGNORECASE)
        if match:
            search_term = match.group(1).strip()
            # Remove common filter words that might be captured
            filter_words = ["status", "priority", "assigned to", "due date", "case"]
            for word in filter_words:
                if search_term.lower().startswith(word):
                    search_term = ""
                    break
            
            if search_term:
                filter_criteria["search_term"] = search_term
            break
    
    # Check for time-based filters
    if "overdue" in content.lower() or "past due" in content.lower():
        filter_criteria["due_date_end"] = datetime.now().strftime("%Y-%m-%d")
    
    if "due today" in content.lower():
        today = datetime.now().strftime("%Y-%m-%d")
        filter_criteria["due_date"] = today
    
    if "due this week" in content.lower():
        today = datetime.now()
        end_of_week = today + timedelta(days=(6 - today.weekday()))
        filter_criteria["due_date_start"] = today.strftime("%Y-%m-%d")
        filter_criteria["due_date_end"] = end_of_week.strftime("%Y-%m-%d")
    
    if "due next week" in content.lower():
        today = datetime.now()
        start_of_next_week = today + timedelta(days=(7 - today.weekday()))
        end_of_next_week = start_of_next_week + timedelta(days=6)
        filter_criteria["due_date_start"] = start_of_next_week.strftime("%Y-%m-%d")
        filter_criteria["due_date_end"] = end_of_next_week.strftime("%Y-%m-%d")
    
    # Remove None values
    return {k: v for k, v in filter_criteria.items() if v is not None}
