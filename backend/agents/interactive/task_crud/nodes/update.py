"""
Update Task Node

This module provides a node for updating existing tasks, including
title, description, due date, priority, status, and assignee.

Usage:
    from backend.agents.interactive.task_crud.nodes.update import update_task

    # Add the node to the StateGraph
    sg.add_node("update_task", update_task)
"""

import logging
import re
from datetime import datetime
from typing import Any, Dict, Optional, Tuple

from langchain_core.runnables import RunnableConfig

from shared.core.tools.executor import get_tool_executor

# Set up logging
logger = logging.getLogger(__name__)


async def update_task(state: Dict[str, Any], config: RunnableConfig) -> Dict[str, Any]:
    """
    Update an existing task.

    Args:
        state: Current state
        config: Runnable configuration

    Returns:
        Updated state
    """
    logger.info("Executing update_task node")

    # Extract configurable values
    configurable = config.get("configurable", {})
    tenant_id = configurable.get("tenant_id", "unknown")
    user_id = configurable.get("user_id", "unknown")

    # Get the last human message
    last_message = None
    for message in reversed(state.get("messages", [])):
        if message.get("role") == "user" or message.get("type") == "human":
            last_message = message
            break

    content = last_message.get("content", "") if last_message else ""

    # Extract task ID and update information from the message
    task_id, update_info = await _extract_update_info(content, tenant_id)
    
    # If we have a parsed date in the state, use it
    if state.get("parsed_date"):
        update_info["due_date"] = state["parsed_date"]
    
    # If we don't have a task ID, ask for one
    if not task_id:
        # Check if we have a current task in the state
        if state.get("current_task") and state["current_task"].get("id"):
            task_id = state["current_task"]["id"]
        else:
            # We need to find the task first
            state["messages"].append({
                "role": "assistant",
                "content": (
                    "Which task would you like to update? Please provide the "
                    "task ID or title."
                ),
                "metadata": {"awaiting": "task_id"}
            })
            state["task_data"] = update_info
            return state
    
    # Get the task to update
    tool_executor = get_tool_executor()
    
    try:
        # Get the task
        task = await tool_executor.execute_tool(
            tool_name="get_task",
            tool_args={"task_id": task_id},
            tenant_id=tenant_id
        )
        
        if not task:
            logger.warning(f"Task not found: {task_id}")
            state["error"] = f"Task not found: {task_id}"
            state["messages"].append({
                "role": "assistant",
                "content": (
                    f"I couldn't find a task with ID {task_id}. Please check "
                    "the ID and try again."
                ),
                "metadata": {"error": "task_not_found"}
            })
            state["next"] = "FINISH"
            return state
        
        # Store the current task
        state["current_task"] = task
        
        # If we don't have any update information, ask for it
        # Check only the actual update fields, not metadata
        update_fields = [
            "title",
            "description",
            "status",
            "priority",
            "due_date",
            "assigned_to",
        ]
        has_updates = any(update_info.get(field) for field in update_fields)

        if not has_updates:
            state["messages"].append({
                "role": "assistant",
                "content": (
                    f"What would you like to update about the task "
                    f"'{task.get('title')}'? You can update the title, "
                    "description, status, due date, or assignee."
                ),
                "metadata": {"awaiting": "update_info", "current_task": task}
            })
            state["next"] = "FINISH"
            return state
        
        # Add tenant_id and user_id to the update info
        update_info["tenant_id"] = tenant_id
        update_info["updated_by"] = user_id
        
        # Execute the update_task tool
        updated_task = await tool_executor.execute_tool(
            tool_name="update_task",
            tool_args={"task_id": task_id, "update_data": update_info},
            tenant_id=tenant_id
        )
        
        if updated_task:
            logger.info(f"Updated task: {updated_task.get('id')}")
            state["current_task"] = updated_task
            
            # Create a message describing the updates
            update_description = _format_update_description(update_info)
            
            state["messages"].append({
                "role": "assistant",
                "content": (
                    f"I've updated the task '{updated_task.get('title')}'. "
                    f"{update_description}"
                ),
                "metadata": {"updated_task": updated_task}
            })
            state["next"] = "FINISH"
        else:
            logger.warning("Failed to update task")
            state["error"] = "Failed to update task"
            state["messages"].append({
                "role": "assistant",
                "content": "I couldn't update the task. Please try again.",
                "metadata": {"error": "task_update_failed"}
            })
            state["next"] = "FINISH"
    except Exception as e:
        logger.error(f"Error updating task: {str(e)}")
        state["error"] = str(e)
        state["messages"].append({
            "role": "assistant",
            "content": (
                f"I encountered an error updating the task: {str(e)}. "
                "Please try again."
            ),
            "metadata": {"error": str(e)}
        })
        state["next"] = "FINISH"

    return state


def _format_update_description(update_info: Dict[str, Any]) -> str:
    """
    Format a description of the updates.

    Args:
        update_info: The update information

    Returns:
        A formatted description
    """
    parts = []
    
    if update_info.get("title"):
        parts.append(f"title to '{update_info['title']}'")
    
    if update_info.get("description"):
        parts.append(f"description to '{update_info['description']}'")
    
    if update_info.get("status"):
        parts.append(f"status to '{update_info['status']}'")
    
    if update_info.get("priority"):
        parts.append(f"priority to '{update_info['priority']}'")
    
    if update_info.get("due_date"):
        parts.append(f"due date to {update_info['due_date']}")
    
    if update_info.get("assigned_to"):
        parts.append(
            f"assignee to "
            f"{update_info.get('assigned_to_name') or update_info['assigned_to']}"
        )
    
    if parts:
        return "I updated the " + ", ".join(parts) + "."
    else:
        return "No changes were made."


async def _extract_update_info(
    content: str,
    tenant_id: str
) -> Tuple[Optional[str], Dict[str, Any]]:
    """
    Extract task ID and update information from a message.

    Args:
        content: The message content
        tenant_id: The tenant ID

    Returns:
        A tuple of (task_id, update_info)
    """
    # Initialize update info with defaults
    update_info = {
        "title": None,
        "description": None,
        "status": None,
        "priority": None,
        "due_date": None,
        "assigned_to": None,
        "ai_metadata": {}
    }
    
    # Extract task ID
    task_id = None
    task_id_patterns = [
        r"update\s+task\s+([a-f0-9\-\w]+)\s+with",  # Most specific pattern first
        r"update\s+task\s+([a-f0-9\-\w]+)",
        r"task\s+(?:id|number)[:\s]+([a-f0-9\-\w]+)",
        r"(?:^|\s)id[:\s]+([a-f0-9\-\w]+)",  # More specific to avoid matching
    ]
    
    for pattern in task_id_patterns:
        match = re.search(pattern, content, re.IGNORECASE)
        if match:
            task_id = match.group(1).strip()
            break
    
    # Extract title
    title_patterns = [
        r"title[:\s]+([^\n,]+)",
        r"change\s+title\s+to[:\s]*([^\n,]+)",
        r"update\s+title\s+to[:\s]*([^\n,]+)",
        r"rename\s+to[:\s]*([^\n,]+)",
        r"title\s+to[:\s]*([^\n,]+)"
    ]

    for pattern in title_patterns:
        match = re.search(pattern, content, re.IGNORECASE)
        if match:
            title = match.group(1).strip()
            # Remove leading "to" if present
            title = re.sub(r"^to\s+", "", title, flags=re.IGNORECASE)
            update_info["title"] = title
            break
    
    # Extract description
    description_patterns = [
        r"description[:\s]+([^\n]+)",
        r"change\s+description\s+to[:\s]*([^\n]+)",
        r"update\s+description\s+to[:\s]*([^\n]+)"
    ]

    for pattern in description_patterns:
        match = re.search(pattern, content, re.IGNORECASE)
        if match:
            description = match.group(1).strip()
            # Remove leading "to" if present
            description = re.sub(r"^to\s+", "", description, flags=re.IGNORECASE)
            update_info["description"] = description
            break
    
    # Extract status
    status_patterns = [
        r"status[:\s]+(todo|in\s*progress|done)",
        r"change\s+status\s+to[:\s]+(todo|in\s*progress|done)",
        r"update\s+status\s+to[:\s]+(todo|in\s*progress|done)",
        r"mark\s+(?:as\s+)?(todo|in\s*progress|done)"
    ]
    
    for pattern in status_patterns:
        match = re.search(pattern, content, re.IGNORECASE)
        if match:
            status = match.group(1).strip().lower()
            if status == "in progress":
                status = "in_progress"
            update_info["status"] = status
            break
    
    # Check for common status phrases
    if (
        "mark as done" in content.lower()
        or "mark as complete" in content.lower()
        or "complete the task" in content.lower()
    ):
        update_info["status"] = "done"
    
    if "mark as in progress" in content.lower() or "start the task" in content.lower():
        update_info["status"] = "in_progress"
    
    # Extract priority
    priority_patterns = [
        r"priority[:\s]+(low|medium|high|urgent)",
        r"change\s+priority\s+to[:\s]+(low|medium|high|urgent)",
        r"update\s+priority\s+to[:\s]+(low|medium|high|urgent)"
    ]
    
    for pattern in priority_patterns:
        match = re.search(pattern, content, re.IGNORECASE)
        if match:
            update_info["priority"] = match.group(1).strip().lower()
            break
    
    # Extract due date
    date_patterns = [
        r"due\s+(?:date|on)[:\s]+([^\n]+)",
        r"change\s+due\s+date\s+to[:\s]+([^\n]+)",
        r"update\s+due\s+date\s+to[:\s]+([^\n]+)",
        r"deadline[:\s]+([^\n]+)",
        r"change\s+deadline\s+to[:\s]+([^\n]+)",
        r"update\s+deadline\s+to[:\s]+([^\n]+)"
    ]
    
    for pattern in date_patterns:
        match = re.search(pattern, content, re.IGNORECASE)
        if match:
            date_string = match.group(1).strip()
            # Remove leading "to" if present
            date_string = re.sub(r"^to\s+", "", date_string, flags=re.IGNORECASE)
            # We'll parse the date in the parse_date node
            update_info["due_date"] = date_string
            break
    
    # Extract assignee
    assignee_patterns = [
        r"assign(?:ed)?\s+to[:\s]+([^\n]+)",
        r"change\s+assignee\s+to[:\s]+([^\n]+)",
        r"update\s+assignee\s+to[:\s]+([^\n]+)",
        r"assignee[:\s]+([^\n]+)"
    ]
    
    for pattern in assignee_patterns:
        match = re.search(pattern, content, re.IGNORECASE)
        if match:
            assignee = match.group(1).strip()
            # Store the name for display purposes
            update_info["assigned_to_name"] = assignee
            # We'll need to look up the user ID based on the name
            update_info["assigned_to"] = assignee
            break
    
    # Add AI metadata
    update_info["ai_metadata"] = {
        "updated_from": "task_crud_agent",
        "original_message": content,
        "update_timestamp": datetime.now().isoformat()
    }
    
    return task_id, update_info
