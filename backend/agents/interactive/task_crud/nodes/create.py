"""
Create Task Node

This module provides a node for creating new tasks with title, description,
due date, priority, and assignee.

Usage:
    from backend.agents.interactive.task_crud.nodes.create import create_task

    # Add the node to the StateGraph
    sg.add_node("create_task", create_task)
"""

import json
import logging
import os
import re
from datetime import datetime
from typing import Any, Dict

from langchain_core.runnables import RunnableConfig

from backend.agents.interactive.task_crud.utils.string_helpers import (
    extract_description,
    extract_priority,
    extract_status,
    extract_title,
)
from shared.core.llm.voyage import VoyageClient
from shared.core.tools.executor import get_tool_executor

# Set up logging
logger = logging.getLogger(__name__)

# Check if we should use LLM for extraction
USE_LLM_EXTRACTION = os.environ.get("TASK_EXTRACTOR_USE_LLM", "").lower() in (
    "true",
    "1",
    "yes",
)


async def create_task(state: Dict[str, Any], config: RunnableConfig) -> Dict[str, Any]:
    """
    Create a new task.

    Args:
        state: Current state
        config: Runnable configuration

    Returns:
        Updated state
    """
    logger.info("Executing create_task node")

    # Extract configurable values
    configurable = config.get("configurable", {})
    tenant_id = configurable.get("tenant_id", "unknown")
    user_id = configurable.get("user_id", "unknown")

    # Get the last human message
    last_message = None
    for message in reversed(state.get("messages", [])):
        if message.get("role") == "user" or message.get("type") == "human":
            last_message = message
            break

    content = last_message.get("content", "") if last_message else ""

    # Extract task information from the message
    task_info = await _extract_task_info(content, tenant_id)

    # If we have a parsed date in the state, use it
    if state.get("parsed_date"):
        task_info["due_date"] = state["parsed_date"]

    # If we don't have a meaningful title, ask for one
    title = task_info.get("title", "").strip()
    generic_titles = ["new task", "task", "a task", "the task", ""]
    if not title or title.lower() in generic_titles:
        state["messages"].append({
            "role": "assistant",
            "content": "What would you like to title this task?",
            "metadata": {"awaiting": "title"}
        })
        state["task_data"] = task_info
        return state

    # Create the task
    tool_executor = get_tool_executor()

    try:
        # Add tenant_id and user_id to the task info
        task_info["tenant_id"] = tenant_id
        task_info["created_by"] = user_id

        # Execute the create_task tool
        created_task = await tool_executor.execute_tool(
            tool_name="create_task",
            tool_args=task_info,
            tenant_id=tenant_id
        )

        if created_task:
            logger.info(f"Created task: {created_task.get('id')}")
            state["current_task"] = created_task
            state["messages"].append({
                "role": "assistant",
                "content": (
                    f"I've created a task titled '{created_task.get('title')}'"
                    + (
                        f" due on {created_task.get('due_date')}"
                        if created_task.get('due_date')
                        else ""
                    )
                    + "."
                ),
                "metadata": {"created_task": created_task}
            })
            state["next"] = "FINISH"
        else:
            logger.warning("Failed to create task")
            state["error"] = "Failed to create task"
            state["messages"].append({
                "role": "assistant",
                "content": "I couldn't create the task. Please try again.",
                "metadata": {"error": "task_creation_failed"}
            })
    except Exception as e:
        logger.error(f"Error creating task: {str(e)}")
        state["error"] = str(e)
        state["messages"].append({
            "role": "assistant",
            "content": (
                f"I encountered an error creating the task: {str(e)}. "
                "Please try again."
            ),
            "metadata": {"error": str(e)}
        })

    return state


async def _extract_task_info(content: str, tenant_id: str) -> Dict[str, Any]:
    """
    Extract task information from a message.

    Args:
        content: The message content
        tenant_id: The tenant ID

    Returns:
        A dictionary with task information
    """
    # Initialize task info with defaults
    task_info = {
        "title": None,
        "description": None,
        "status": "todo",
        "priority": None,
        "due_date": None,
        "assigned_to": None,
        "related_case": None,
        "ai_metadata": {}
    }

    # Try LLM-based extraction if enabled
    if USE_LLM_EXTRACTION:
        try:
            logger.info("Using LLM for task info extraction")
            llm_client = VoyageClient()

            # Call the LLM
            response = await llm_client.chat_completion(
                messages=[
                    {
                        "role": "system",
                        "content": (
                            "Extract task information from the user message. "
                            "Return a JSON object with these fields: title, "
                            "description, status, priority, due_date, assigned_to, "
                            "related_case."
                        )
                    },
                    {"role": "user", "content": content}
                ],
                temperature=0,
                model="gpt-4o"  # Use a capable model for accurate extraction
            )

            # Extract the content from the response
            assistant_message = response["choices"][0]["message"]["content"]

            # Parse the JSON response
            try:
                # Try to parse directly
                json_str = assistant_message.strip()
                if json_str.startswith("```json"):
                    json_str = json_str.split("```json")[1].split("```")[0].strip()
                elif json_str.startswith("```"):
                    json_str = json_str.split("```")[1].split("```")[0].strip()

                result = json.loads(json_str)

                # Update task_info with the extracted values
                for key in [
                    "title",
                    "description",
                    "status",
                    "priority",
                    "due_date",
                    "assigned_to",
                    "related_case",
                ]:
                    if key in result and result[key]:
                        task_info[key] = result[key]

                logger.info(f"LLM extracted task info: {task_info}")

                # If we got a title, we're good to go
                if task_info["title"]:
                    # Add AI metadata
                    task_info["ai_metadata"] = {
                        "created_from": "task_crud_agent_llm",
                        "original_message": content,
                        "extraction_timestamp": datetime.now().isoformat()
                    }
                    return task_info
            except (json.JSONDecodeError, KeyError) as e:
                logger.warning(f"Failed to parse LLM response: {e}")

        except Exception as e:
            logger.warning(f"LLM extraction failed: {e}")

    # Fall back to regex-based extraction
    logger.info("Using regex for task info extraction")

    # Extract title using our helper
    task_info["title"] = extract_title(content)

    # Extract description using our helper
    description = extract_description(content)
    if description:
        task_info["description"] = description

    # Extract status using our helper
    status = extract_status(content)
    if status:
        task_info["status"] = status

    # Extract priority using our helper
    priority = extract_priority(content)
    if priority:
        task_info["priority"] = priority

    # Extract due date
    date_patterns = [
        r"due\s+(?:date|on)[:\s]+([^\n]+)",
        r"deadline[:\s]+([^\n]+)",
        r"by\s+([^\n]+)",
        r"due\s+([^\n,\.]+)"
    ]

    for pattern in date_patterns:
        match = re.search(pattern, content, re.IGNORECASE)
        if match:
            date_string = match.group(1).strip()
            # We'll parse the date in the parse_date node
            task_info["due_date"] = date_string
            break

    # Extract assignee
    assignee_patterns = [
        r"assign(?:ed)?\s+to[:\s]+([^\n,\.]+)",
        r"assignee[:\s]+([^\n,\.]+)",
        r"for\s+([^\n,\.]+)\s+to\s+do"
    ]

    for pattern in assignee_patterns:
        match = re.search(pattern, content, re.IGNORECASE)
        if match:
            assignee = match.group(1).strip()
            # We'll need to look up the user ID based on the name
            task_info["assigned_to"] = assignee
            break

    # Extract related case
    case_patterns = [
        r"(?:related|for)\s+case[:\s]+([^\n,]+)",  # Allow periods in case names
        r"case[:\s]+([^\n,]+)",
        r"for\s+(?:the\s+)?case\s+([^\n,]+)"
    ]

    for pattern in case_patterns:
        match = re.search(pattern, content, re.IGNORECASE)
        if match:
            case = match.group(1).strip()
            # We'll need to look up the case ID based on the name
            task_info["related_case"] = case
            break

    # Add AI metadata
    task_info["ai_metadata"] = {
        "created_from": "task_crud_agent_regex",
        "original_message": content,
        "extraction_timestamp": datetime.now().isoformat()
    }

    return task_info
