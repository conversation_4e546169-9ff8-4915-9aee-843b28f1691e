"""
Date Parser Node

This module provides a node for parsing natural language dates into
standardized formats.
It supports various date formats, relative dates, and natural language expressions.

Usage:
    from backend.agents.interactive.task_crud.nodes.date_parser import parse_date

    # Add the node to the StateGraph
    sg.add_node("parse_date", parse_date)
"""

import logging
import re
from typing import Any, Dict, List

from dateparser.search import search_dates
from langchain_core.runnables import RunnableConfig

from shared.core.tools.executor import get_tool_executor

# Set up logging
logger = logging.getLogger(__name__)


async def parse_date(state: Dict[str, Any], config: RunnableConfig) -> Dict[str, Any]:
    """
    Parse a natural language date into a standardized format.

    Args:
        state: Current state
        config: Runnable configuration

    Returns:
        Updated state
    """
    logger.info("Executing parse_date node")

    # Extract configurable values
    configurable = config.get("configurable", {})
    tenant_id = configurable.get("tenant_id", "unknown")

    # Get the last human message
    last_message = None
    for message in reversed(state.get("messages", [])):
        if message.get("role") == "user" or message.get("type") == "human":
            last_message = message
            break

    content = last_message.get("content", "") if last_message else ""

    # Extract date expressions from the message
    date_expressions = extract_date_expressions(content)

    if not date_expressions:
        state["messages"].append({
            "role": "assistant",
            "content": (
                "I couldn't find any date expressions in your message. "
                "Please provide a date for the task."
            ),
            "metadata": {"error": "no_date_found"}
        })
        state["parsed_date"] = None
        return state

    # Parse the first date expression found
    date_string = date_expressions[0]

    # Use the tool executor to parse the date
    tool_executor = get_tool_executor()

    try:
        parsed_date = await tool_executor.execute_tool(
            tool_name="parse_date",
            tool_args={"date_string": date_string},
            tenant_id=tenant_id
        )

        if parsed_date:
            logger.info(f"Parsed date: {parsed_date}")
            state["parsed_date"] = parsed_date
            state["messages"].append({
                "role": "assistant",
                "content": f"I've set the date to {parsed_date}.",
                "metadata": {"parsed_date": parsed_date}
            })
        else:
            logger.warning(f"Failed to parse date: {date_string}")
            state["parsed_date"] = None
            state["messages"].append({
                "role": "assistant",
                "content": (
                    f"I couldn't parse the date '{date_string}'. "
                    "Please provide a date in a standard format "
                    "(e.g., 'tomorrow', 'next Friday', 'July 15')."
                ),
                "metadata": {"error": "date_parse_failed"}
            })
    except Exception as e:
        logger.error(f"Error parsing date: {str(e)}")
        state["parsed_date"] = None
        state["messages"].append({
            "role": "assistant",
            "content": (
                f"I encountered an error parsing the date: {str(e)}. "
                "Please provide a date in a standard format."
            ),
            "metadata": {"error": str(e)}
        })

    return state


def extract_date_expressions(text: str) -> List[str]:
    """
    Extract date expressions from text.

    Args:
        text: The text to extract date expressions from

    Returns:
        A list of date expressions
    """
    # Skip dateparser.search for now as it doesn't preserve full context
    # We'll use regex patterns directly to get better control over extraction

    # Fall back to regex patterns if dateparser fails
    patterns = [
        r"(today|tomorrow|yesterday|someday|sometime)",  # Include vague dates
        r"((next|this|last)\s+(monday|tuesday|wednesday|thursday|friday|saturday|sunday|week|month|year))",
        r"(in\s+\d+\s+(?:day|week|month|year)s?)",
        r"(\d{1,2}\/\d{1,2}\/\d{2,4})",  # MM/DD/YYYY or DD/MM/YYYY
        r"(\d{1,2}\-\d{1,2}\-\d{2,4})",  # MM-DD-YYYY or DD-MM-YYYY
        r"(\d{4}\-\d{1,2}\-\d{1,2})",    # YYYY-MM-DD
        r"((january|february|march|april|may|june|july|august|september|october|november|december)\s+\d{1,2}(?:st|nd|rd|th)?(?:\s*,?\s*\d{4})?)",
        r"((\d{1,2}(?:st|nd|rd|th)?\s+(?:of\s+)?(?:january|february|march|april|may|june|july|august|september|october|november|december)(?:\s*,?\s*\d{4})?))",
    ]

    # Combine patterns
    combined_pattern = "|".join(patterns)

    # Find all matches
    matches = re.findall(combined_pattern, text.lower())

    # Flatten the matches (re.findall returns a list of tuples for grouped patterns)
    date_expressions = []
    for match in matches:
        if isinstance(match, tuple):
            # Get the first non-empty string in the tuple (this will be the outer group)
            for item in match:
                if item and item.strip():
                    date_expressions.append(item.strip())
                    break
        else:
            date_expressions.append(match.strip())

    return date_expressions
