#!/usr/bin/env python3
"""
Comprehensive Python API Test Suite for Client Intake
This script provides a robust test framework for the client intake API endpoints.
"""

import os
import sys
import json
import random
import argparse
import datetime
import requests
from typing import Dict, List, Any, Optional
from dotenv import load_dotenv
import pytest
from supabase import create_client, Client

# Load environment variables
load_dotenv()

# Supabase configuration
SUPABASE_URL = os.getenv("NEXT_PUBLIC_SUPABASE_URL")
SUPABASE_KEY = os.getenv("NEXT_PUBLIC_SUPABASE_ANON_KEY")
SUPABASE_SERVICE_KEY = os.getenv("SUPABASE_SERVICE_KEY")

# API endpoint (when testing through a REST API)
API_BASE_URL = os.getenv("API_BASE_URL", "http://localhost:3000/api")

# Test configuration
TEST_EMAIL_DOMAIN = "test.pilawyer.ai"
TEST_PHONE_PREFIX = "555-"


class ClientIntakeTestSuite:
    """Test suite for client intake functionality"""

    def __init__(self, use_direct_db: bool = True):
        """Initialize the test suite

        Args:
            use_direct_db: If True, use Supabase client directly.
                If False, use REST API.
        """
        self.use_direct_db = use_direct_db
        self.test_results = []

        if use_direct_db:
            if not SUPABASE_URL or not SUPABASE_KEY:
                raise ValueError("SUPABASE_URL and SUPABASE_ANON_KEY must be set")
            self.supabase = create_client(SUPABASE_URL, SUPABASE_KEY)

            # Set schema to tenants
            if hasattr(self.supabase, "schema"):
                self.supabase.schema("tenants")
        else:
            if not API_BASE_URL:
                raise ValueError("API_BASE_URL must be set")

    def _generate_test_client(
        self, overrides: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """Generate test client data"""
        timestamp = datetime.datetime.now().timestamp()
        data = {
            "first_name": f"Test{int(timestamp)}",
            "last_name": f"Client{int(timestamp % 10000)}",
            "date_of_birth": "1980-01-01",
            "email": f"test.client.{int(timestamp)}@{TEST_EMAIL_DOMAIN}",
            "phone_primary": f"{TEST_PHONE_PREFIX}{1000 + int(timestamp % 9000)}",
            "address": {
                "street": "123 Test St",
                "city": "Test City",
                "state": "TX",
                "zip": "78701",
            },
            "occupation": "Software Tester",
            "employer": "Test Inc.",
            "work_status": "full_time",
            "status": "active",
            "client_type": "individual",  # Added required field
            "intake_date": datetime.datetime.now().strftime("%Y-%m-%d"),
            "conflict_check_status": "pending",
            "tenant_id": "f5a731c2-9c0b-4ef8-bb6d-6bb9bd380a11",  # Added for testing
        }

        if overrides:
            data.update(overrides)

        return data

    def _generate_test_case(
        self, overrides: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """Generate test case data"""
        timestamp = datetime.datetime.now().timestamp()
        data = {
            "title": f"Test Case {int(timestamp)}",
            "description": "This is a test case for automated testing",
            "practice_area": "personal_injury",
            "case_type": "auto_accident",
            "intake_priority": "medium",
            "status": "active",
            "previously_consulted": False,
            "metadata": {"internal_notes": "Test case created for automated testing"},
            "tenant_id": "f5a731c2-9c0b-4ef8-bb6d-6bb9bd380a11",  # Added for testing
        }

        if overrides:
            data.update(overrides)

        return data

    def _generate_test_party(
        self, overrides: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """Generate test other party data"""
        timestamp = datetime.datetime.now().timestamp()
        data = {
            "first_name": f"Test{int(timestamp)}",
            "last_name": f"Party{int(timestamp % 10000)}",
            "type": "other",
            "role": "defendant",
            "address": {},
            "tenant_id": "f5a731c2-9c0b-4ef8-bb6d-6bb9bd380a11",  # Added for testing
        }

        if overrides:
            data.update(overrides)

        return data

    def _create_client_intake_direct(
        self, client_data, case_data, other_parties
    ) -> Dict[str, Any]:
        """Create client intake using Supabase client directly"""
        # Update case status to a valid value from the constraint
        if "status" in case_data and case_data["status"] == "intake":
            case_data[
                "status"
            ] = "active"  # Use a value allowed by the case_status_check constraint

        # Make sure client_data has an address field
        if "address" not in client_data or not client_data["address"]:
            client_data["address"] = {
                "street": "123 Test St",
                "city": "Test City",
                "state": "TX",
                "zip": "78701",
            }

        # Create function parameters
        params = {
            "p_client_data": client_data,
            "p_case_data": case_data,
            "p_other_parties": other_parties,
            "p_test_tenant_id": "f5a731c2-9c0b-4ef8-bb6d-6bb9bd380a11",
            "p_test_user_id": "550e8400-e29b-41d4-a716-446655440000",
        }

        try:
            # Call the RPC function
            response = self.supabase.rpc("create_client_intake", params)

            # Execute the request
            result = response.execute()

            # Return the data
            if hasattr(result, "data"):
                return result.data
            else:
                return result

        except Exception as e:
            raise Exception(f"Error creating client intake: {e}")

    def _create_client_intake_api(
        self, client_data, case_data, other_parties
    ) -> Dict[str, Any]:
        """Create client intake using REST API"""
        # Update case status to a valid value from the constraint
        if "status" in case_data and case_data["status"] == "intake":
            case_data[
                "status"
            ] = "active"  # Use a value allowed by the case_status_check constraint

        headers = {"Content-Type": "application/json"}
        payload = {
            "clientData": client_data,
            "caseData": case_data,
            "otherParties": other_parties,
        }

        try:
            response = requests.post(
                f"{API_BASE_URL}/clients/intake", headers=headers, json=payload
            )

            if response.status_code != 200:
                raise Exception(
                    f"Error creating client intake via API: {response.text}"
                )

            return response.json()
        except Exception as e:
            raise Exception(f"API request failed: {str(e)}")

    def create_client_intake(
        self, client_data, case_data, other_parties
    ) -> Dict[str, Any]:
        """Create client intake using configured method"""
        try:
            if self.use_direct_db:
                return self._create_client_intake_direct(
                    client_data, case_data, other_parties
                )
            else:
                return self._create_client_intake_api(
                    client_data, case_data, other_parties
                )
        except Exception as e:
            print(f"Error in create_client_intake: {str(e)}")
            raise

    def run_test(self, test_name: str, test_fn):
        """Run a test and record result"""
        print(f"\nRunning test: {test_name}")
        start_time = datetime.datetime.now()

        try:
            result = test_fn()
            success = True
            error = None
        except Exception as e:
            success = False
            error = str(e)
            result = None

        end_time = datetime.datetime.now()
        duration = (end_time - start_time).total_seconds()

        test_result = {
            "name": test_name,
            "success": success,
            "error": error,
            "duration": duration,
            "timestamp": end_time.isoformat(),
            "result": result,
        }

        self.test_results.append(test_result)

        if success:
            print(f"✅ Test passed: {test_name} ({duration:.2f}s)")
            return result
        else:
            print(f"❌ Test failed: {test_name} ({duration:.2f}s)")
            print(f"Error: {error}")
            return None

    def test_basic_client_intake(self):
        """Test basic client intake creation"""
        client_data = self._generate_test_client()
        case_data = self._generate_test_case()
        other_parties = []

        result = self.create_client_intake(client_data, case_data, other_parties)
        print(f"Response data: {result}")

        # Check if result indicates success directly or indirectly
        if isinstance(result, dict):
            if result.get("error"):
                assert False, f"Response indicates error: {result.get('error')}"

            # For cases where result contains client_id and case_id but no explicit
            # success flag
            if "client_id" in result and "case_id" in result:
                # This is sufficient to consider it a success
                pass
            else:
                # If there's an explicit success flag, check it
                if "success" in result:
                    assert result.get("success") is True, "Response indicates failure"
                else:
                    assert False, "Response does not contain client_id and case_id"
        else:
            assert False, f"Unexpected response type: {type(result)}"

        # Always verify these fields
        assert "client_id" in result, "Client ID not returned"
        assert "case_id" in result, "Case ID not returned"

        print(f"Created client ID: {result['client_id']}")
        print(f"Created case ID: {result['case_id']}")

        return {"client_id": result["client_id"], "case_id": result["case_id"]}

    def test_client_intake_with_parties(self):
        """Test client intake creation with other parties"""
        client_data = self._generate_test_client()
        case_data = self._generate_test_case()
        other_parties = [
            self._generate_test_party({"role": "defendant"}),
            self._generate_test_party({"role": "witness"}),
        ]

        result = self.create_client_intake(client_data, case_data, other_parties)

        assert result.get("success") is True, "Response does not indicate success"
        assert "client_id" in result, "Client ID not returned"
        assert "case_id" in result, "Case ID not returned"

        return {
            "client_id": result["client_id"],
            "case_id": result["case_id"],
            "expected_parties": len(other_parties),
        }

    def test_client_intake_missing_fields(self):
        """Test client intake with missing required fields"""
        client_data = self._generate_test_client()
        case_data = self._generate_test_case()

        # Remove required fields
        del client_data["first_name"]
        del client_data["last_name"]

        try:
            result = self.create_client_intake(client_data, case_data, [])
            # This should fail, so if we get here, that's a problem
            assert (
                False
            ), "Expected error due to missing required fields but got success"
        except Exception as e:
            # We expect an exception due to missing fields
            assert (
                "error" in str(e).lower() or "missing" in str(e).lower()
            ), "Unexpected error message"
            return {"expected_error": True}

    def test_different_practice_areas(self):
        """Test client intake with different practice areas"""
        practice_areas = [
            ("personal_injury", "auto_accident"),
            ("criminal_defense", "dwi_dui"),
            ("family_law", "divorce"),
        ]

        results = []

        for practice_area, case_type in practice_areas:
            client_data = self._generate_test_client()
            case_data = self._generate_test_case(
                {"practice_area": practice_area, "case_type": case_type}
            )

            result = self.create_client_intake(client_data, case_data, [])

            assert (
                result.get("success") is True
            ), f"Response does not indicate success for {practice_area}"
            assert "client_id" in result, f"Client ID not returned for {practice_area}"
            assert "case_id" in result, f"Case ID not returned for {practice_area}"

            results.append(
                {
                    "practice_area": practice_area,
                    "case_type": case_type,
                    "client_id": result["client_id"],
                    "case_id": result["case_id"],
                }
            )

        return results

    def run_all_tests(self):
        """Run all tests in the suite"""
        tests = [
            ("Basic client intake", self.test_basic_client_intake),
            ("Client intake with other parties", self.test_client_intake_with_parties),
            (
                "Client intake with missing fields",
                self.test_client_intake_missing_fields,
            ),
            ("Different practice areas", self.test_different_practice_areas),
        ]

        for test_name, test_fn in tests:
            self.run_test(test_name, test_fn)

        self.print_results()
        return self.test_results

    def print_results(self):
        """Print test results summary"""
        total = len(self.test_results)
        passed = sum(1 for result in self.test_results if result["success"])

        print("\n=== Test Results Summary ===")
        print(f"Total tests: {total}")
        print(f"Passed: {passed}")
        print(f"Failed: {total - passed}")
        print(f"Success rate: {(passed / total) * 100:.1f}%")
        print("===========================")

        if total - passed > 0:
            print("\nFailed tests:")
            for result in self.test_results:
                if not result["success"]:
                    print(f"  - {result['name']}: {result['error']}")


def parse_arguments():
    """Parse command line arguments"""
    parser = argparse.ArgumentParser(description="Client Intake API Test Suite")
    parser.add_argument(
        "--api", action="store_true", help="Use REST API instead of direct DB access"
    )
    parser.add_argument("--test", type=str, help="Run a specific test")
    parser.add_argument("--run-all", action="store_true", help="Run all tests")
    return parser.parse_args()


if __name__ == "__main__":
    args = parse_arguments()

    try:
        test_suite = ClientIntakeTestSuite(use_direct_db=not args.api)

        if args.test:
            # Run specific test
            test_method_name = f"test_{args.test}"
            if hasattr(test_suite, test_method_name):
                test_method = getattr(test_suite, test_method_name)
                test_suite.run_test(args.test, test_method)
            else:
                print(f"Error: Test '{args.test}' not found")
                sys.exit(1)
        elif args.run_all:
            # Run all tests
            test_suite.run_all_tests()
        else:
            print("Please specify --test or --run-all")
            sys.exit(1)

    except Exception as e:
        print(f"Error: {e}")
        sys.exit(1)
