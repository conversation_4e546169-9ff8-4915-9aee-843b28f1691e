repos:
-   repo: https://github.com/pre-commit/pre-commit-hooks
    rev: v4.4.0
    hooks:
    -   id: trailing-whitespace
    -   id: end-of-file-fixer
    -   id: check-yaml
    -   id: check-added-large-files

-   repo: https://github.com/psf/black
    rev: 23.3.0
    hooks:
    -   id: black
        language_version: python3.9

-   repo: local
    hooks:
    -   id: mypy
        name: mypy
        entry: python -m mypy
        language: system
        types: [python]
        args: [
            "--show-error-codes",
            "--disallow-untyped-defs",  # Strict typing for our own code
            "--disallow-incomplete-defs",  # Ensure complete typing
            # Only selectively ignore third-party libraries without stubs
            "--no-warn-unused-ignores",
            # Specific modules to ignore import issues with
            "--ignore-missing-imports",
            # Use a more targeted approach for import-untyped warnings
            "--disable-error-code=import-untyped"
        ]
        require_serial: true
        files: ^pi_lawyer/
