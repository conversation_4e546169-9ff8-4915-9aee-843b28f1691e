
tests/test_simple.py::test_simple PASSED                                 [  2%]
tests/test_simple.py::test_math PASSED                                   [  5%]
tests/test_ai_intake.py::test_extract_client_info[0] 
-------------------------------- live log setup --------------------------------
2025-06-02 07:51:28 - httpx - DEBUG - load_ssl_context verify=True cert=None trust_env=True http2=False
2025-06-02 07:51:28 - httpx - DEBUG - load_verify_locations cafile='/mnt/persist/workspace/venv/lib/python3.10/site-packages/certifi/cacert.pem'
PASSED                                                                   [  8%]
tests/test_ai_intake.py::test_ai_form_completion PASSED                  [ 11%]
tests/test_client_intake.py::test_create_client_intake_basic[basic_client_intake] SKIPPED [ 14%]
tests/test_client_intake.py::test_create_client_intake_with_parties[client_intake_with_other_parties] SKIPPED [ 17%]
tests/test_client_intake.py::test_create_client_intake_missing_fields[client_intake_missing_required_fields] SKIPPED [ 20%]
tests/test_database.py::test_database_connection ERROR                   [ 22%]
tests/test_database.py::test_schema_existence ERROR                      [ 25%]
tests/test_database.py::test_public_tables ERROR                         [ 28%]
tests/test_database.py::test_tenant_tables ERROR                         [ 31%]
tests/test_logging.py::test_setup_logger 
-------------------------------- live log call ---------------------------------
2025-06-02 07:51:29 - test_logger - INFO - This is a test log
PASSED                                                                   [ 34%]
tests/test_metrics_simple.py::test_metrics_module_imports PASSED         [ 37%]
tests/test_metrics_simple.py::test_middleware_module_imports PASSED      [ 40%]
tests/test_metrics_simple.py::test_metrics_output_format PASSED          [ 42%]
tests/test_query_classifier.py::test_has_legal_keywords 
-------------------------------- live log call ---------------------------------
2025-06-02 07:51:29 - httpx - DEBUG - load_ssl_context verify=True cert=None trust_env=True http2=False
2025-06-02 07:51:29 - httpx - DEBUG - load_verify_locations cafile='/mnt/persist/workspace/venv/lib/python3.10/site-packages/certifi/cacert.pem'
2025-06-02 07:51:29 - pi_lawyer.utils.query_classifier - INFO - Groq client initialized
2025-06-02 07:51:29 - pi_lawyer.utils.query_classifier - INFO - Gemini client initialized
PASSED                                                                   [ 45%]
tests/test_query_classifier.py::test_classify_with_groq 
-------------------------------- live log setup --------------------------------
2025-06-02 07:51:29 - asyncio - DEBUG - Using selector: EpollSelector
2025-06-02 07:51:29 - asyncio - DEBUG - Using selector: EpollSelector
-------------------------------- live log call ---------------------------------
2025-06-02 07:51:29 - httpx - DEBUG - load_ssl_context verify=True cert=None trust_env=True http2=False
2025-06-02 07:51:29 - httpx - DEBUG - load_verify_locations cafile='/mnt/persist/workspace/venv/lib/python3.10/site-packages/certifi/cacert.pem'
2025-06-02 07:51:29 - pi_lawyer.utils.query_classifier - INFO - Groq client initialized
2025-06-02 07:51:29 - pi_lawyer.utils.query_classifier - INFO - Gemini client initialized
PASSED                                                                   [ 48%]
tests/test_rbac.py::test_user_context_retrieval ERROR                    [ 51%]
tests/test_rbac.py::test_partner_access ERROR                            [ 54%]
tests/test_rbac.py::test_attorney_access ERROR                           [ 57%]
tests/test_rbac.py::test_paralegal_access ERROR                          [ 60%]
tests/test_rbac.py::test_staff_access ERROR                              [ 62%]
tests/test_rbac.py::test_cross_tenant_isolation ERROR                    [ 65%]
tests/test_rbac.py::test_access_logging ERROR                            [ 68%]
tests/test_structured_logging.py::test_structured_logging_module_imports PASSED [ 71%]
tests/test_structured_logging.py::test_context_set_get_clear PASSED      [ 74%]
tests/test_template_utils.py::TestTemplateUtils::test_categorize_template PASSED [ 77%]
tests/test_template_utils.py::TestTemplateUtils::test_compile_template_sections PASSED [ 80%]
tests/test_template_utils.py::TestTemplateUtils::test_extract_variables PASSED [ 82%]
tests/test_template_utils.py::TestTemplateUtils::test_fetch_template_from_supabase SKIPPED [ 85%]
tests/test_template_utils.py::TestTemplateUtils::test_get_template_by_state_and_type PASSED [ 88%]
tests/test_template_utils.py::TestTemplateUtils::test_process_conditionals PASSED [ 91%]
tests/test_template_utils.py::TestTemplateUtils::test_process_loops PASSED [ 94%]
tests/test_template_utils.py::TestTemplateUtils::test_render_template PASSED [ 97%]
tests/test_template_utils.py::TestTemplateUtils::test_save_template_to_supabase SKIPPED [100%]
------------------------------ live log teardown -------------------------------
2025-06-02 07:51:29 - asyncio - DEBUG - Using selector: EpollSelector


==================================== ERRORS ====================================
__________________ ERROR at setup of test_database_connection __________________

    @pytest.fixture
    def db_connection():
        """Create database connection fixture."""
>       connection = psycopg2.connect(
            user=os.getenv("DB_USER"),
            password=os.getenv("DB_PASSWORD"),
            host=os.getenv("DB_HOST"),
            port=os.getenv("DB_PORT"),
            dbname=os.getenv("DB_NAME"),
        )

tests/test_database.py:14: 
_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ 

dsn = '', connection_factory = None, cursor_factory = None
kwargs = {'dbname': None, 'host': None, 'password': None, 'port': None, ...}
kwasync = {}

    def connect(dsn=None, connection_factory=None, cursor_factory=None, **kwargs):
        """
        Create a new database connection.
    
        The connection parameters can be specified as a string:
    
            conn = psycopg2.connect("dbname=test user=postgres password=secret")
    
        or using a set of keyword arguments:
    
            conn = psycopg2.connect(database="test", user="postgres", password="secret")
    
        Or as a mix of both. The basic connection parameters are:
    
        - *dbname*: the database name
        - *database*: the database name (only as keyword argument)
        - *user*: user name used to authenticate
        - *password*: password used to authenticate
        - *host*: database host address (defaults to UNIX socket if not provided)
        - *port*: connection port number (defaults to 5432 if not provided)
    
        Using the *connection_factory* parameter a different class or connections
        factory can be specified. It should be a callable object taking a dsn
        argument.
    
        Using the *cursor_factory* parameter, a new default cursor factory will be
        used by cursor().
    
        Using *async*=True an asynchronous connection will be created. *async_* is
        a valid alias (for Python versions where ``async`` is a keyword).
    
        Any other keyword parameter will be passed to the underlying client
        library: the list of supported parameters depends on the library version.
    
        """
        kwasync = {}
        if 'async' in kwargs:
            kwasync['async'] = kwargs.pop('async')
        if 'async_' in kwargs:
            kwasync['async_'] = kwargs.pop('async_')
    
        dsn = _ext.make_dsn(dsn, **kwargs)
>       conn = _connect(dsn, connection_factory=connection_factory, **kwasync)
E       psycopg2.OperationalError: connection to server on socket "/var/run/postgresql/.s.PGSQL.5432" failed: No such file or directory
E       	Is the server running locally and accepting connections on that socket?

venv/lib/python3.10/site-packages/psycopg2/__init__.py:122: OperationalError
___________________ ERROR at setup of test_schema_existence ____________________

    @pytest.fixture
    def db_connection():
        """Create database connection fixture."""
>       connection = psycopg2.connect(
            user=os.getenv("DB_USER"),
            password=os.getenv("DB_PASSWORD"),
            host=os.getenv("DB_HOST"),
            port=os.getenv("DB_PORT"),
            dbname=os.getenv("DB_NAME"),
        )

tests/test_database.py:14: 
_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ 

dsn = '', connection_factory = None, cursor_factory = None
kwargs = {'dbname': None, 'host': None, 'password': None, 'port': None, ...}
kwasync = {}

    def connect(dsn=None, connection_factory=None, cursor_factory=None, **kwargs):
        """
        Create a new database connection.
    
        The connection parameters can be specified as a string:
    
            conn = psycopg2.connect("dbname=test user=postgres password=secret")
    
        or using a set of keyword arguments:
    
            conn = psycopg2.connect(database="test", user="postgres", password="secret")
    
        Or as a mix of both. The basic connection parameters are:
    
        - *dbname*: the database name
        - *database*: the database name (only as keyword argument)
        - *user*: user name used to authenticate
        - *password*: password used to authenticate
        - *host*: database host address (defaults to UNIX socket if not provided)
        - *port*: connection port number (defaults to 5432 if not provided)
    
        Using the *connection_factory* parameter a different class or connections
        factory can be specified. It should be a callable object taking a dsn
        argument.
    
        Using the *cursor_factory* parameter, a new default cursor factory will be
        used by cursor().
    
        Using *async*=True an asynchronous connection will be created. *async_* is
        a valid alias (for Python versions where ``async`` is a keyword).
    
        Any other keyword parameter will be passed to the underlying client
        library: the list of supported parameters depends on the library version.
    
        """
        kwasync = {}
        if 'async' in kwargs:
            kwasync['async'] = kwargs.pop('async')
        if 'async_' in kwargs:
            kwasync['async_'] = kwargs.pop('async_')
    
        dsn = _ext.make_dsn(dsn, **kwargs)
>       conn = _connect(dsn, connection_factory=connection_factory, **kwasync)
E       psycopg2.OperationalError: connection to server on socket "/var/run/postgresql/.s.PGSQL.5432" failed: No such file or directory
E       	Is the server running locally and accepting connections on that socket?

venv/lib/python3.10/site-packages/psycopg2/__init__.py:122: OperationalError
_____________________ ERROR at setup of test_public_tables _____________________

    @pytest.fixture
    def db_connection():
        """Create database connection fixture."""
>       connection = psycopg2.connect(
            user=os.getenv("DB_USER"),
            password=os.getenv("DB_PASSWORD"),
            host=os.getenv("DB_HOST"),
            port=os.getenv("DB_PORT"),
            dbname=os.getenv("DB_NAME"),
        )

tests/test_database.py:14: 
_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ 

dsn = '', connection_factory = None, cursor_factory = None
kwargs = {'dbname': None, 'host': None, 'password': None, 'port': None, ...}
kwasync = {}

    def connect(dsn=None, connection_factory=None, cursor_factory=None, **kwargs):
        """
        Create a new database connection.
    
        The connection parameters can be specified as a string:
    
            conn = psycopg2.connect("dbname=test user=postgres password=secret")
    
        or using a set of keyword arguments:
    
            conn = psycopg2.connect(database="test", user="postgres", password="secret")
    
        Or as a mix of both. The basic connection parameters are:
    
        - *dbname*: the database name
        - *database*: the database name (only as keyword argument)
        - *user*: user name used to authenticate
        - *password*: password used to authenticate
        - *host*: database host address (defaults to UNIX socket if not provided)
        - *port*: connection port number (defaults to 5432 if not provided)
    
        Using the *connection_factory* parameter a different class or connections
        factory can be specified. It should be a callable object taking a dsn
        argument.
    
        Using the *cursor_factory* parameter, a new default cursor factory will be
        used by cursor().
    
        Using *async*=True an asynchronous connection will be created. *async_* is
        a valid alias (for Python versions where ``async`` is a keyword).
    
        Any other keyword parameter will be passed to the underlying client
        library: the list of supported parameters depends on the library version.
    
        """
        kwasync = {}
        if 'async' in kwargs:
            kwasync['async'] = kwargs.pop('async')
        if 'async_' in kwargs:
            kwasync['async_'] = kwargs.pop('async_')
    
        dsn = _ext.make_dsn(dsn, **kwargs)
>       conn = _connect(dsn, connection_factory=connection_factory, **kwasync)
E       psycopg2.OperationalError: connection to server on socket "/var/run/postgresql/.s.PGSQL.5432" failed: No such file or directory
E       	Is the server running locally and accepting connections on that socket?

venv/lib/python3.10/site-packages/psycopg2/__init__.py:122: OperationalError
_____________________ ERROR at setup of test_tenant_tables _____________________

    @pytest.fixture
    def db_connection():
        """Create database connection fixture."""
>       connection = psycopg2.connect(
            user=os.getenv("DB_USER"),
            password=os.getenv("DB_PASSWORD"),
            host=os.getenv("DB_HOST"),
            port=os.getenv("DB_PORT"),
            dbname=os.getenv("DB_NAME"),
        )

tests/test_database.py:14: 
_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ 

dsn = '', connection_factory = None, cursor_factory = None
kwargs = {'dbname': None, 'host': None, 'password': None, 'port': None, ...}
kwasync = {}

    def connect(dsn=None, connection_factory=None, cursor_factory=None, **kwargs):
        """
        Create a new database connection.
    
        The connection parameters can be specified as a string:
    
            conn = psycopg2.connect("dbname=test user=postgres password=secret")
    
        or using a set of keyword arguments:
    
            conn = psycopg2.connect(database="test", user="postgres", password="secret")
    
        Or as a mix of both. The basic connection parameters are:
    
        - *dbname*: the database name
        - *database*: the database name (only as keyword argument)
        - *user*: user name used to authenticate
        - *password*: password used to authenticate
        - *host*: database host address (defaults to UNIX socket if not provided)
        - *port*: connection port number (defaults to 5432 if not provided)
    
        Using the *connection_factory* parameter a different class or connections
        factory can be specified. It should be a callable object taking a dsn
        argument.
    
        Using the *cursor_factory* parameter, a new default cursor factory will be
        used by cursor().
    
        Using *async*=True an asynchronous connection will be created. *async_* is
        a valid alias (for Python versions where ``async`` is a keyword).
    
        Any other keyword parameter will be passed to the underlying client
        library: the list of supported parameters depends on the library version.
    
        """
        kwasync = {}
        if 'async' in kwargs:
            kwasync['async'] = kwargs.pop('async')
        if 'async_' in kwargs:
            kwasync['async_'] = kwargs.pop('async_')
    
        dsn = _ext.make_dsn(dsn, **kwargs)
>       conn = _connect(dsn, connection_factory=connection_factory, **kwasync)
E       psycopg2.OperationalError: connection to server on socket "/var/run/postgresql/.s.PGSQL.5432" failed: No such file or directory
E       	Is the server running locally and accepting connections on that socket?

venv/lib/python3.10/site-packages/psycopg2/__init__.py:122: OperationalError
________________ ERROR at setup of test_user_context_retrieval _________________

    @pytest.fixture
    def db_connection():
        """Create database connection fixture."""
>       connection = psycopg2.connect(
            user=os.getenv("DB_USER"),
            password=os.getenv("DB_PASSWORD"),
            host=os.getenv("DB_HOST"),
            port=os.getenv("DB_PORT"),
            dbname=os.getenv("DB_NAME"),
        )

tests/test_rbac.py:16: 
_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ 

dsn = '', connection_factory = None, cursor_factory = None
kwargs = {'dbname': None, 'host': None, 'password': None, 'port': None, ...}
kwasync = {}

    def connect(dsn=None, connection_factory=None, cursor_factory=None, **kwargs):
        """
        Create a new database connection.
    
        The connection parameters can be specified as a string:
    
            conn = psycopg2.connect("dbname=test user=postgres password=secret")
    
        or using a set of keyword arguments:
    
            conn = psycopg2.connect(database="test", user="postgres", password="secret")
    
        Or as a mix of both. The basic connection parameters are:
    
        - *dbname*: the database name
        - *database*: the database name (only as keyword argument)
        - *user*: user name used to authenticate
        - *password*: password used to authenticate
        - *host*: database host address (defaults to UNIX socket if not provided)
        - *port*: connection port number (defaults to 5432 if not provided)
    
        Using the *connection_factory* parameter a different class or connections
        factory can be specified. It should be a callable object taking a dsn
        argument.
    
        Using the *cursor_factory* parameter, a new default cursor factory will be
        used by cursor().
    
        Using *async*=True an asynchronous connection will be created. *async_* is
        a valid alias (for Python versions where ``async`` is a keyword).
    
        Any other keyword parameter will be passed to the underlying client
        library: the list of supported parameters depends on the library version.
    
        """
        kwasync = {}
        if 'async' in kwargs:
            kwasync['async'] = kwargs.pop('async')
        if 'async_' in kwargs:
            kwasync['async_'] = kwargs.pop('async_')
    
        dsn = _ext.make_dsn(dsn, **kwargs)
>       conn = _connect(dsn, connection_factory=connection_factory, **kwasync)
E       psycopg2.OperationalError: connection to server on socket "/var/run/postgresql/.s.PGSQL.5432" failed: No such file or directory
E       	Is the server running locally and accepting connections on that socket?

venv/lib/python3.10/site-packages/psycopg2/__init__.py:122: OperationalError
____________________ ERROR at setup of test_partner_access _____________________

    @pytest.fixture
    def db_connection():
        """Create database connection fixture."""
>       connection = psycopg2.connect(
            user=os.getenv("DB_USER"),
            password=os.getenv("DB_PASSWORD"),
            host=os.getenv("DB_HOST"),
            port=os.getenv("DB_PORT"),
            dbname=os.getenv("DB_NAME"),
        )

tests/test_rbac.py:16: 
_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ 

dsn = '', connection_factory = None, cursor_factory = None
kwargs = {'dbname': None, 'host': None, 'password': None, 'port': None, ...}
kwasync = {}

    def connect(dsn=None, connection_factory=None, cursor_factory=None, **kwargs):
        """
        Create a new database connection.
    
        The connection parameters can be specified as a string:
    
            conn = psycopg2.connect("dbname=test user=postgres password=secret")
    
        or using a set of keyword arguments:
    
            conn = psycopg2.connect(database="test", user="postgres", password="secret")
    
        Or as a mix of both. The basic connection parameters are:
    
        - *dbname*: the database name
        - *database*: the database name (only as keyword argument)
        - *user*: user name used to authenticate
        - *password*: password used to authenticate
        - *host*: database host address (defaults to UNIX socket if not provided)
        - *port*: connection port number (defaults to 5432 if not provided)
    
        Using the *connection_factory* parameter a different class or connections
        factory can be specified. It should be a callable object taking a dsn
        argument.
    
        Using the *cursor_factory* parameter, a new default cursor factory will be
        used by cursor().
    
        Using *async*=True an asynchronous connection will be created. *async_* is
        a valid alias (for Python versions where ``async`` is a keyword).
    
        Any other keyword parameter will be passed to the underlying client
        library: the list of supported parameters depends on the library version.
    
        """
        kwasync = {}
        if 'async' in kwargs:
            kwasync['async'] = kwargs.pop('async')
        if 'async_' in kwargs:
            kwasync['async_'] = kwargs.pop('async_')
    
        dsn = _ext.make_dsn(dsn, **kwargs)
>       conn = _connect(dsn, connection_factory=connection_factory, **kwasync)
E       psycopg2.OperationalError: connection to server on socket "/var/run/postgresql/.s.PGSQL.5432" failed: No such file or directory
E       	Is the server running locally and accepting connections on that socket?

venv/lib/python3.10/site-packages/psycopg2/__init__.py:122: OperationalError
____________________ ERROR at setup of test_attorney_access ____________________

    @pytest.fixture
    def db_connection():
        """Create database connection fixture."""
>       connection = psycopg2.connect(
            user=os.getenv("DB_USER"),
            password=os.getenv("DB_PASSWORD"),
            host=os.getenv("DB_HOST"),
            port=os.getenv("DB_PORT"),
            dbname=os.getenv("DB_NAME"),
        )

tests/test_rbac.py:16: 
_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ 

dsn = '', connection_factory = None, cursor_factory = None
kwargs = {'dbname': None, 'host': None, 'password': None, 'port': None, ...}
kwasync = {}

    def connect(dsn=None, connection_factory=None, cursor_factory=None, **kwargs):
        """
        Create a new database connection.
    
        The connection parameters can be specified as a string:
    
            conn = psycopg2.connect("dbname=test user=postgres password=secret")
    
        or using a set of keyword arguments:
    
            conn = psycopg2.connect(database="test", user="postgres", password="secret")
    
        Or as a mix of both. The basic connection parameters are:
    
        - *dbname*: the database name
        - *database*: the database name (only as keyword argument)
        - *user*: user name used to authenticate
        - *password*: password used to authenticate
        - *host*: database host address (defaults to UNIX socket if not provided)
        - *port*: connection port number (defaults to 5432 if not provided)
    
        Using the *connection_factory* parameter a different class or connections
        factory can be specified. It should be a callable object taking a dsn
        argument.
    
        Using the *cursor_factory* parameter, a new default cursor factory will be
        used by cursor().
    
        Using *async*=True an asynchronous connection will be created. *async_* is
        a valid alias (for Python versions where ``async`` is a keyword).
    
        Any other keyword parameter will be passed to the underlying client
        library: the list of supported parameters depends on the library version.
    
        """
        kwasync = {}
        if 'async' in kwargs:
            kwasync['async'] = kwargs.pop('async')
        if 'async_' in kwargs:
            kwasync['async_'] = kwargs.pop('async_')
    
        dsn = _ext.make_dsn(dsn, **kwargs)
>       conn = _connect(dsn, connection_factory=connection_factory, **kwasync)
E       psycopg2.OperationalError: connection to server on socket "/var/run/postgresql/.s.PGSQL.5432" failed: No such file or directory
E       	Is the server running locally and accepting connections on that socket?

venv/lib/python3.10/site-packages/psycopg2/__init__.py:122: OperationalError
___________________ ERROR at setup of test_paralegal_access ____________________

    @pytest.fixture
    def db_connection():
        """Create database connection fixture."""
>       connection = psycopg2.connect(
            user=os.getenv("DB_USER"),
            password=os.getenv("DB_PASSWORD"),
            host=os.getenv("DB_HOST"),
            port=os.getenv("DB_PORT"),
            dbname=os.getenv("DB_NAME"),
        )

tests/test_rbac.py:16: 
_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ 

dsn = '', connection_factory = None, cursor_factory = None
kwargs = {'dbname': None, 'host': None, 'password': None, 'port': None, ...}
kwasync = {}

    def connect(dsn=None, connection_factory=None, cursor_factory=None, **kwargs):
        """
        Create a new database connection.
    
        The connection parameters can be specified as a string:
    
            conn = psycopg2.connect("dbname=test user=postgres password=secret")
    
        or using a set of keyword arguments:
    
            conn = psycopg2.connect(database="test", user="postgres", password="secret")
    
        Or as a mix of both. The basic connection parameters are:
    
        - *dbname*: the database name
        - *database*: the database name (only as keyword argument)
        - *user*: user name used to authenticate
        - *password*: password used to authenticate
        - *host*: database host address (defaults to UNIX socket if not provided)
        - *port*: connection port number (defaults to 5432 if not provided)
    
        Using the *connection_factory* parameter a different class or connections
        factory can be specified. It should be a callable object taking a dsn
        argument.
    
        Using the *cursor_factory* parameter, a new default cursor factory will be
        used by cursor().
    
        Using *async*=True an asynchronous connection will be created. *async_* is
        a valid alias (for Python versions where ``async`` is a keyword).
    
        Any other keyword parameter will be passed to the underlying client
        library: the list of supported parameters depends on the library version.
    
        """
        kwasync = {}
        if 'async' in kwargs:
            kwasync['async'] = kwargs.pop('async')
        if 'async_' in kwargs:
            kwasync['async_'] = kwargs.pop('async_')
    
        dsn = _ext.make_dsn(dsn, **kwargs)
>       conn = _connect(dsn, connection_factory=connection_factory, **kwasync)
E       psycopg2.OperationalError: connection to server on socket "/var/run/postgresql/.s.PGSQL.5432" failed: No such file or directory
E       	Is the server running locally and accepting connections on that socket?

venv/lib/python3.10/site-packages/psycopg2/__init__.py:122: OperationalError
_____________________ ERROR at setup of test_staff_access ______________________

    @pytest.fixture
    def db_connection():
        """Create database connection fixture."""
>       connection = psycopg2.connect(
            user=os.getenv("DB_USER"),
            password=os.getenv("DB_PASSWORD"),
            host=os.getenv("DB_HOST"),
            port=os.getenv("DB_PORT"),
            dbname=os.getenv("DB_NAME"),
        )

tests/test_rbac.py:16: 
_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ 

dsn = '', connection_factory = None, cursor_factory = None
kwargs = {'dbname': None, 'host': None, 'password': None, 'port': None, ...}
kwasync = {}

    def connect(dsn=None, connection_factory=None, cursor_factory=None, **kwargs):
        """
        Create a new database connection.
    
        The connection parameters can be specified as a string:
    
            conn = psycopg2.connect("dbname=test user=postgres password=secret")
    
        or using a set of keyword arguments:
    
            conn = psycopg2.connect(database="test", user="postgres", password="secret")
    
        Or as a mix of both. The basic connection parameters are:
    
        - *dbname*: the database name
        - *database*: the database name (only as keyword argument)
        - *user*: user name used to authenticate
        - *password*: password used to authenticate
        - *host*: database host address (defaults to UNIX socket if not provided)
        - *port*: connection port number (defaults to 5432 if not provided)
    
        Using the *connection_factory* parameter a different class or connections
        factory can be specified. It should be a callable object taking a dsn
        argument.
    
        Using the *cursor_factory* parameter, a new default cursor factory will be
        used by cursor().
    
        Using *async*=True an asynchronous connection will be created. *async_* is
        a valid alias (for Python versions where ``async`` is a keyword).
    
        Any other keyword parameter will be passed to the underlying client
        library: the list of supported parameters depends on the library version.
    
        """
        kwasync = {}
        if 'async' in kwargs:
            kwasync['async'] = kwargs.pop('async')
        if 'async_' in kwargs:
            kwasync['async_'] = kwargs.pop('async_')
    
        dsn = _ext.make_dsn(dsn, **kwargs)
>       conn = _connect(dsn, connection_factory=connection_factory, **kwasync)
E       psycopg2.OperationalError: connection to server on socket "/var/run/postgresql/.s.PGSQL.5432" failed: No such file or directory
E       	Is the server running locally and accepting connections on that socket?

venv/lib/python3.10/site-packages/psycopg2/__init__.py:122: OperationalError
________________ ERROR at setup of test_cross_tenant_isolation _________________

    @pytest.fixture
    def db_connection():
        """Create database connection fixture."""
>       connection = psycopg2.connect(
            user=os.getenv("DB_USER"),
            password=os.getenv("DB_PASSWORD"),
            host=os.getenv("DB_HOST"),
            port=os.getenv("DB_PORT"),
            dbname=os.getenv("DB_NAME"),
        )

tests/test_rbac.py:16: 
_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ 

dsn = '', connection_factory = None, cursor_factory = None
kwargs = {'dbname': None, 'host': None, 'password': None, 'port': None, ...}
kwasync = {}

    def connect(dsn=None, connection_factory=None, cursor_factory=None, **kwargs):
        """
        Create a new database connection.
    
        The connection parameters can be specified as a string:
    
            conn = psycopg2.connect("dbname=test user=postgres password=secret")
    
        or using a set of keyword arguments:
    
            conn = psycopg2.connect(database="test", user="postgres", password="secret")
    
        Or as a mix of both. The basic connection parameters are:
    
        - *dbname*: the database name
        - *database*: the database name (only as keyword argument)
        - *user*: user name used to authenticate
        - *password*: password used to authenticate
        - *host*: database host address (defaults to UNIX socket if not provided)
        - *port*: connection port number (defaults to 5432 if not provided)
    
        Using the *connection_factory* parameter a different class or connections
        factory can be specified. It should be a callable object taking a dsn
        argument.
    
        Using the *cursor_factory* parameter, a new default cursor factory will be
        used by cursor().
    
        Using *async*=True an asynchronous connection will be created. *async_* is
        a valid alias (for Python versions where ``async`` is a keyword).
    
        Any other keyword parameter will be passed to the underlying client
        library: the list of supported parameters depends on the library version.
    
        """
        kwasync = {}
        if 'async' in kwargs:
            kwasync['async'] = kwargs.pop('async')
        if 'async_' in kwargs:
            kwasync['async_'] = kwargs.pop('async_')
    
        dsn = _ext.make_dsn(dsn, **kwargs)
>       conn = _connect(dsn, connection_factory=connection_factory, **kwasync)
E       psycopg2.OperationalError: connection to server on socket "/var/run/postgresql/.s.PGSQL.5432" failed: No such file or directory
E       	Is the server running locally and accepting connections on that socket?

venv/lib/python3.10/site-packages/psycopg2/__init__.py:122: OperationalError
____________________ ERROR at setup of test_access_logging _____________________

    @pytest.fixture
    def db_connection():
        """Create database connection fixture."""
>       connection = psycopg2.connect(
            user=os.getenv("DB_USER"),
            password=os.getenv("DB_PASSWORD"),
            host=os.getenv("DB_HOST"),
            port=os.getenv("DB_PORT"),
            dbname=os.getenv("DB_NAME"),
        )

tests/test_rbac.py:16: 
_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ 

dsn = '', connection_factory = None, cursor_factory = None
kwargs = {'dbname': None, 'host': None, 'password': None, 'port': None, ...}
kwasync = {}

    def connect(dsn=None, connection_factory=None, cursor_factory=None, **kwargs):
        """
        Create a new database connection.
    
        The connection parameters can be specified as a string:
    
            conn = psycopg2.connect("dbname=test user=postgres password=secret")
    
        or using a set of keyword arguments:
    
            conn = psycopg2.connect(database="test", user="postgres", password="secret")
    
        Or as a mix of both. The basic connection parameters are:
    
        - *dbname*: the database name
        - *database*: the database name (only as keyword argument)
        - *user*: user name used to authenticate
        - *password*: password used to authenticate
        - *host*: database host address (defaults to UNIX socket if not provided)
        - *port*: connection port number (defaults to 5432 if not provided)
    
        Using the *connection_factory* parameter a different class or connections
        factory can be specified. It should be a callable object taking a dsn
        argument.
    
        Using the *cursor_factory* parameter, a new default cursor factory will be
        used by cursor().
    
        Using *async*=True an asynchronous connection will be created. *async_* is
        a valid alias (for Python versions where ``async`` is a keyword).
    
        Any other keyword parameter will be passed to the underlying client
        library: the list of supported parameters depends on the library version.
    
        """
        kwasync = {}
        if 'async' in kwargs:
            kwasync['async'] = kwargs.pop('async')
        if 'async_' in kwargs:
            kwasync['async_'] = kwargs.pop('async_')
    
        dsn = _ext.make_dsn(dsn, **kwargs)
>       conn = _connect(dsn, connection_factory=connection_factory, **kwasync)
E       psycopg2.OperationalError: connection to server on socket "/var/run/postgresql/.s.PGSQL.5432" failed: No such file or directory
E       	Is the server running locally and accepting connections on that socket?

venv/lib/python3.10/site-packages/psycopg2/__init__.py:122: OperationalError
=============================== warnings summary ===============================
src/pi_lawyer/db/supabase_client_stub.py:18
  /mnt/persist/workspace/src/pi_lawyer/db/supabase_client_stub.py:18: RuntimeWarning: Using stub implementation of SupabaseClient - NOT SUITABLE FOR PRODUCTION
    warnings.warn(

src/pi_lawyer/db/pinecone_client_stub.py:19
  /mnt/persist/workspace/src/pi_lawyer/db/pinecone_client_stub.py:19: RuntimeWarning: Using stub implementation of Pinecone client - NOT SUITABLE FOR PRODUCTION
    warnings.warn(

venv/lib/python3.10/site-packages/pydantic/_internal/_config.py:323: 12 warnings
  /mnt/persist/workspace/venv/lib/python3.10/site-packages/pydantic/_internal/_config.py:323: PydanticDeprecatedSince20: Support for class-based `config` is deprecated, use ConfigDict instead. Deprecated in Pydantic V2.0 to be removed in V3.0. See Pydantic V2 Migration Guide at https://errors.pydantic.dev/2.11/migration/
    warnings.warn(DEPRECATION_MESSAGE, DeprecationWarning)

src/pi_lawyer/services/__init__.py:30
  /mnt/persist/workspace/src/pi_lawyer/services/__init__.py:30: UserWarning: Error importing service module: cannot import name 'DocumentChunk' from 'pi_lawyer.models.document' (/mnt/persist/workspace/pi_lawyer/models/document.py)
    warnings.warn(f"Error importing service module: {e}")

src/pi_lawyer/__init__.py:18
  /mnt/persist/workspace/src/pi_lawyer/__init__.py:18: UserWarning: Error importing submodule: No module named 'pi_lawyer.db.supabase_client'
    warnings.warn(f"Error importing submodule: {e}")

venv/lib/python3.10/site-packages/langchain/llms/__init__.py:549
venv/lib/python3.10/site-packages/langchain/llms/__init__.py:549
  /mnt/persist/workspace/venv/lib/python3.10/site-packages/langchain/llms/__init__.py:549: LangChainDeprecationWarning: Importing LLMs from langchain is deprecated. Importing from langchain will no longer be supported as of langchain==0.2.0. Please import from langchain-community instead:
  
  `from langchain_community.llms import OpenAI`.
  
  To install langchain-community run `pip install -U langchain-community`.
    warnings.warn(

tests/test_metrics_simple.py::test_metrics_module_imports
  /mnt/persist/workspace/venv/lib/python3.10/site-packages/_pytest/python.py:198: PytestReturnNotNoneWarning: Expected None, but tests/test_metrics_simple.py::test_metrics_module_imports returned True, which will be an error in a future version of pytest.  Did you mean to use `assert` instead of `return`?
    warnings.warn(

tests/test_metrics_simple.py::test_middleware_module_imports
  /mnt/persist/workspace/venv/lib/python3.10/site-packages/_pytest/python.py:198: PytestReturnNotNoneWarning: Expected None, but tests/test_metrics_simple.py::test_middleware_module_imports returned True, which will be an error in a future version of pytest.  Did you mean to use `assert` instead of `return`?
    warnings.warn(

tests/test_metrics_simple.py::test_metrics_output_format
  /mnt/persist/workspace/venv/lib/python3.10/site-packages/_pytest/python.py:198: PytestReturnNotNoneWarning: Expected None, but tests/test_metrics_simple.py::test_metrics_output_format returned True, which will be an error in a future version of pytest.  Did you mean to use `assert` instead of `return`?
    warnings.warn(

-- Docs: https://docs.pytest.org/en/stable/how-to/capture-warnings.html

---------- coverage: platform linux, python 3.10.12-final-0 ----------
Name                                                                     Stmts   Miss  Cover   Missing
------------------------------------------------------------------------------------------------------
src/pi_lawyer/__init__.py                                                   13      4    69%   12-15
src/pi_lawyer/agents/__init__.py                                             7      7     0%   8-25
src/pi_lawyer/agents/_stubs.py                                              36     36     0%   8-150
src/pi_lawyer/agents/base_agent.py                                         134    134     0%   42-518
src/pi_lawyer/agents/config.py                                             124    124     0%   37-434
src/pi_lawyer/agents/copilotkit_runtime.py                                  14     14     0%   1-36
src/pi_lawyer/agents/deadline_agent.py                                      20     20     0%   2-70
src/pi_lawyer/agents/document_agent.py                                      18     18     0%   2-61
src/pi_lawyer/agents/echo_agent.py                                         128    128     0%   22-413
src/pi_lawyer/agents/graph/__init__.py                                       3      3     0%   20-23
src/pi_lawyer/agents/graph/builder.py                                       68     68     0%   8-246
src/pi_lawyer/agents/graph/builder_update.py                                53     53     0%   8-199
src/pi_lawyer/agents/graph/finish.py                                        31     31     0%   8-124
src/pi_lawyer/agents/insights/__init__.py                                    0      0   100%
src/pi_lawyer/agents/insights/daily.py                                      13     13     0%   11-81
src/pi_lawyer/agents/insights/document.py                                   13     13     0%   11-81
src/pi_lawyer/agents/insights/supervisor/__init__.py                         4      4     0%   43-47
src/pi_lawyer/agents/insights/supervisor/agent.py                          168    168     0%   26-451
src/pi_lawyer/agents/insights/supervisor/nodes.py                           34     34     0%   7-101
src/pi_lawyer/agents/insights/supervisor/router.py                          22     22     0%   20-76
src/pi_lawyer/agents/insights/supervisor/schema.py                          18     18     0%   7-38
src/pi_lawyer/agents/insights/swarm.py                                       9      9     0%   10-56
src/pi_lawyer/agents/insights/test_insight_agents.py                        26     26     0%   7-68
src/pi_lawyer/agents/insights/verify_agents.py                              33     33     0%   7-86
src/pi_lawyer/agents/intake_agent.py                                        22     22     0%   2-73
src/pi_lawyer/agents/interactive/__init__.py                                 3      3     0%   16-25
src/pi_lawyer/agents/interactive/intake/__init__.py                          4      4     0%   24-35
src/pi_lawyer/agents/interactive/intake/agent.py                           138    138     0%   26-345
src/pi_lawyer/agents/interactive/intake/routers.py                          21     21     0%   15-78
src/pi_lawyer/agents/interactive/intake/tasks.py                           151    151     0%   23-436
src/pi_lawyer/agents/router.py                                              49     49     0%   8-143
src/pi_lawyer/api/__init__.py                                                7      4    43%   14-20
src/pi_lawyer/api/authored_document_embedding.py                            99     92     7%   16-284
src/pi_lawyer/api/copilotkit_route.py                                       44     44     0%   7-132
src/pi_lawyer/api/document_route.py                                        656    656     0%   10-1658
src/pi_lawyer/api/document_route_variables_update.py                        37     37     0%   7-118
src/pi_lawyer/api/health_api.py                                             48     32    33%   35-57, 71-82, 95-106, 124-146
src/pi_lawyer/api/intake_route.py                                           23     23     0%   1-57
src/pi_lawyer/api/main.py                                                   44     44     0%   8-94
src/pi_lawyer/api/research_route.py                                         26     26     0%   1-87
src/pi_lawyer/api/runtime.py                                                93     93     0%   9-264
src/pi_lawyer/api/runtime_old.py                                             0      0   100%
src/pi_lawyer/api/task_route.py                                            101    101     0%   2-264
src/pi_lawyer/auth/__init__.py                                               4      0   100%
src/pi_lawyer/auth/auth_helpers.py                                          11      1    91%   18
src/pi_lawyer/auth/jwt_auth.py                                              42     16    62%   50-68, 72-80, 88-89
src/pi_lawyer/auth/middleware.py                                            57     53     7%   6-111
src/pi_lawyer/auth/rbac.py                                                  68     43    37%   48, 54-76, 82-101, 113-158, 169
src/pi_lawyer/auth/tenant.py                                                29     21    28%   14-52, 57, 62
src/pi_lawyer/config.py                                                     23     23     0%   2-72
src/pi_lawyer/config/__init__.py                                           124    124     0%   9-320
src/pi_lawyer/config/cli.py                                                 56     56     0%   8-171
src/pi_lawyer/config/settings.py                                           125    125     0%   9-374
src/pi_lawyer/config/utils.py                                               53     53     0%   8-171
src/pi_lawyer/db/__init__.py                                                10      3    70%   16-18
src/pi_lawyer/db/client.py                                                  28     28     0%   8-68
src/pi_lawyer/db/database.py                                                28     19    32%   17-23, 38-40, 56-73, 85-87, 99-101
src/pi_lawyer/db/mock_auth.py                                               18     11    39%   10, 15-16, 21-37
src/pi_lawyer/db/pinecone_client.py                                         25     15    40%   25-27, 38-43, 55, 70, 88-95, 110-117, 126
src/pi_lawyer/db/pinecone_client_stub.py                                    22      8    64%   30-31, 35, 44-45, 49, 54, 67
src/pi_lawyer/db/supabase_client.py                                         69     50    28%   20-22, 30, 41-90, 103-110, 125-132, 143-150, 166-182, 196-212, 226-233, 247-265, 279-285, 299-317, 332-338
src/pi_lawyer/db/supabase_client_stub.py                                    18      8    56%   31-34, 38, 42, 46, 50
src/pi_lawyer/document_worker.py                                           163    163     0%   10-436
src/pi_lawyer/documents/__init__.py                                          5      5     0%   3-8
src/pi_lawyer/documents/embeddings.py                                       79     79     0%   2-210
src/pi_lawyer/documents/manager.py                                          72     72     0%   2-382
src/pi_lawyer/documents/processor.py                                        53     53     0%   2-142
src/pi_lawyer/documents/storage.py                                          30     30     0%   2-75
src/pi_lawyer/services/__init__.py                                          14      5    64%   16-24
src/pi_lawyer/services/authored_document_embedding_service.py              121    121     0%   9-509
src/pi_lawyer/services/authored_document_worker.py                          85     85     0%   8-243
src/pi_lawyer/services/circuit_breaker.py                                  143    103    28%   93-97, 101-117, 129-166, 175-199, 209-221, 238-249, 266-277, 298-326
src/pi_lawyer/services/circuit_breaker_stub.py                              35     35     0%   11-75
src/pi_lawyer/services/deadline_extraction_service.py                      267    267     0%   2-730
src/pi_lawyer/services/document_analysis_service.py                        152    131    14%   18, 22-36, 40-41, 45-63, 67-105, 114-128, 134-212, 216-237, 246-274, 278-310, 314-344
src/pi_lawyer/services/document_classifier_service.py                       92     75    18%   51-86, 112-296
src/pi_lawyer/services/document_embedding_utils.py                          49     49     0%   8-178
src/pi_lawyer/services/document_parser_service.py                           80     80     0%   9-493
src/pi_lawyer/services/document_processing_queue.py                        310    310     0%   5-1152
src/pi_lawyer/services/document_processing_transaction.py                  100    100     0%   8-318
src/pi_lawyer/services/document_summarization.py                            15     15     0%   1-48
src/pi_lawyer/services/document_worker.py                                  174    161     7%   23-624
src/pi_lawyer/services/error_classification.py                              75     53    29%   42-93, 106-107, 125-139, 152-169
src/pi_lawyer/services/error_classification_stub.py                         39     39     0%   11-99
src/pi_lawyer/services/health_service.py                                   112     88    21%   37-52, 61-106, 119-129, 138-166, 175-199, 211-218, 230-272, 284-290, 309-312
src/pi_lawyer/services/metrics_collector.py                                150    129    14%   42-50, 56, 60-62, 75-102, 122-147, 172-232, 249-303, 307-332, 351-354
src/pi_lawyer/services/redis_lock_service.py                                80     80     0%   9-266
src/pi_lawyer/services/redis_queue_service.py                              127    106    17%   25-37, 50-65, 75-102, 112-135, 146-191, 201-213, 225-228, 241-250, 262-283, 292-297, 318-320
src/pi_lawyer/services/services/__init__.py                                  2      2     0%   4-6
src/pi_lawyer/services/services/authored_document_embedding_service.py     121    121     0%   9-496
src/pi_lawyer/services/services/authored_document_worker.py                 85     85     0%   8-243
src/pi_lawyer/services/services/circuit_breaker.py                         131    131     0%   23-334
src/pi_lawyer/services/services/deadline_extraction_service.py             267    267     0%   2-729
src/pi_lawyer/services/services/document_analysis_service.py               154    154     0%   1-348
src/pi_lawyer/services/services/document_classifier_service.py              92     92     0%   10-296
src/pi_lawyer/services/services/document_embedding_utils.py                 49     49     0%   8-178
src/pi_lawyer/services/services/document_parser_service.py                  76     76     0%   9-484
src/pi_lawyer/services/services/document_processing_queue.py               312    312     0%   5-1161
src/pi_lawyer/services/services/document_processing_transaction.py         100    100     0%   8-318
src/pi_lawyer/services/services/document_summarization.py                   15     15     0%   1-48
src/pi_lawyer/services/services/document_worker.py                         174    174     0%   9-627
src/pi_lawyer/services/services/error_classification.py                     75     75     0%   8-169
src/pi_lawyer/services/services/health_service.py                          112    112     0%   8-312
src/pi_lawyer/services/services/metrics_collector.py                       150    150     0%   9-354
src/pi_lawyer/services/services/redis_lock_service.py                       80     80     0%   9-266
src/pi_lawyer/services/services/redis_queue_service.py                     127    127     0%   9-320
src/pi_lawyer/services/services/task_embedding_service.py                   69     69     0%   2-243
src/pi_lawyer/services/services/tenant_document_embedding_service.py       141    141     0%   10-503
src/pi_lawyer/services/task_embedding_service.py                            81     57    30%   39-52, 57-58, 72-100, 112-137, 152-173, 187-198, 220-254
src/pi_lawyer/services/tenant_document_embedding_service.py                167    155     7%   26-551
src/pi_lawyer/shared/__init__.py                                             1      1     0%   8
src/pi_lawyer/shared/core/__init__.py                                        2      2     0%   8-16
src/pi_lawyer/shared/core/llm/__init__.py                                   16     16     0%   9-118
src/pi_lawyer/shared/core/llm/admin/__init__.py                              5      5     0%   8-13
src/pi_lawyer/shared/core/llm/admin/keys.py                                100    100     0%   7-347
src/pi_lawyer/shared/core/llm/admin/models.py                               31     31     0%   7-307
src/pi_lawyer/shared/core/llm/admin/prompts.py                              83     83     0%   7-269
src/pi_lawyer/shared/core/llm/admin/registry.py                             71     71     0%   8-174
src/pi_lawyer/shared/core/llm/api.py                                        49     49     0%   8-206
src/pi_lawyer/shared/core/llm/base.py                                       57     57     0%   8-235
src/pi_lawyer/shared/core/llm/config.py                                     76     76     0%   8-182
src/pi_lawyer/shared/core/llm/embeddings/__init__.py                         3      3     0%   7-10
src/pi_lawyer/shared/core/llm/embeddings/api.py                             49     49     0%   7-153
src/pi_lawyer/shared/core/llm/embeddings/base.py                            53     53     0%   8-185
src/pi_lawyer/shared/core/llm/factory.py                                    56     56     0%   7-183
src/pi_lawyer/shared/core/llm/openai.py                                     43     43     0%   8-201
src/pi_lawyer/shared/core/llm/providers/__init__.py                          6      6     0%   7-13
src/pi_lawyer/shared/core/llm/providers/anthropic.py                        59     59     0%   8-260
src/pi_lawyer/shared/core/llm/providers/gemini.py                           75     75     0%   8-279
src/pi_lawyer/shared/core/llm/providers/groq.py                             44     44     0%   8-218
src/pi_lawyer/shared/core/llm/providers/mock.py                             34     34     0%   8-208
src/pi_lawyer/shared/core/llm/providers/openai.py                           44     44     0%   8-218
src/pi_lawyer/shared/core/llm/voyage.py                                    101    101     0%   8-314
src/pi_lawyer/shared/core/state.py                                          79     79     0%   16-239
src/pi_lawyer/shared/core/tools/__init__.py                                  5      5     0%   8-26
src/pi_lawyer/shared/core/tools/async_job.py                                33     33     0%   27-144
src/pi_lawyer/shared/core/tools/base.py                                     23     23     0%   7-66
src/pi_lawyer/shared/core/tools/enqueue_async_job.py                        27     27     0%   7-74
src/pi_lawyer/shared/core/tools/executor.py                                 97     97     0%   44-318
src/pi_lawyer/shared/core/tools/schema.py                                   29     29     0%   7-413
src/pi_lawyer/utils/__init__.py                                              0      0   100%
src/pi_lawyer/utils/check_gcs.py                                            45     45     0%   5-79
src/pi_lawyer/utils/demonstrate_paths.py                                    47     47     0%   5-120
src/pi_lawyer/utils/gemini_client.py                                        52     52     0%   7-165
src/pi_lawyer/utils/logging.py                                              14     14     0%   2-38
src/pi_lawyer/utils/query_classifier.py                                     76     76     0%   7-264
src/pi_lawyer/utils/storage_utils.py                                       170    170     0%   5-450
src/pi_lawyer/utils/structured_logging.py                                   95     68    28%   21, 31-33, 38, 43, 48, 53, 58, 63-68, 75-76, 81-100, 114-123, 130-146, 150-166, 177-203, 229-240
src/pi_lawyer/utils/telemetry.py                                            64     64     0%   10-218
src/pi_lawyer/utils/template_utils.py                                      193    193     0%   3-477
src/pi_lawyer/utils/test_gcs_structure.py                                   40     40     0%   5-87
src/pi_lawyer/utils/voyage_embeddings.py                                    47     27    43%   45-56, 67-80, 92-95, 106-114, 126-129
src/pi_lawyer/utils/voyage_reranker.py                                      42     42     0%   4-123
------------------------------------------------------------------------------------------------------
TOTAL                                                                    10803  10344     4%

=========================== short test summary info ============================
ERROR tests/test_database.py::test_database_connection - psycopg2.Operational...
ERROR tests/test_database.py::test_schema_existence - psycopg2.OperationalErr...
ERROR tests/test_database.py::test_public_tables - psycopg2.OperationalError:...
ERROR tests/test_database.py::test_tenant_tables - psycopg2.OperationalError:...
ERROR tests/test_rbac.py::test_user_context_retrieval - psycopg2.OperationalE...
ERROR tests/test_rbac.py::test_partner_access - psycopg2.OperationalError: co...
ERROR tests/test_rbac.py::test_attorney_access - psycopg2.OperationalError: c...
ERROR tests/test_rbac.py::test_paralegal_access - psycopg2.OperationalError: ...
ERROR tests/test_rbac.py::test_staff_access - psycopg2.OperationalError: conn...
ERROR tests/test_rbac.py::test_cross_tenant_isolation - psycopg2.OperationalE...
ERROR tests/test_rbac.py::test_access_logging - psycopg2.OperationalError: co...
============ 19 passed, 5 skipped, 21 warnings, 11 errors in 2.64s =============
