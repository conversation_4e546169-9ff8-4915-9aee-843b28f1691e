name: OWASP ZAP Security Scan

on:
  schedule:
    # Run nightly at 2 AM UTC
    - cron: '0 2 * * *'
  pull_request:
    branches: [ main, final-auth-checks ]
  workflow_dispatch:
    # Allow manual triggering

jobs:
  zap_scan:
    name: OWASP ZAP Full Scan
    runs-on: ubuntu-latest
    timeout-minutes: 60


    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Check target URL accessibility
        run: |
          TARGET_URL="${{ secrets.ZAP_TARGET_URL || 'https://btwaueeckvylrlrnbvgt.supabase.co' }}"
          echo "Testing connectivity to: $TARGET_URL"
          if curl -f -s --max-time 30 "$TARGET_URL" > /dev/null; then
            echo "✅ Target URL is accessible"
          else
            echo "❌ Target URL is not accessible, skipping ZAP scan"
            exit 0
          fi

      - name: ZAP Full Scan
        uses: zaproxy/action-full-scan@v0.9.0
        with:
          target: ${{ secrets.ZAP_TARGET_URL || 'https://btwaueeckvylrlrnbvgt.supabase.co' }}
          rules_file_name: '.github/zap-rules.tsv'
          cmd_options: '-a -j -T 60'
          fail_action: true
          allow_issue_writing: false
          issue_title: 'ZAP Full Scan Report'
          token: ${{ secrets.GITHUB_TOKEN }}
          artifact_name: 'zap-scan-report'
          fail_on_medium: true
          fail_on_high: true
          fail_on_critical: true
