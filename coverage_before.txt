
----------------------------- live log collection ------------------------------
2025-06-02 07:50:50 - pi_lawyer.db.pinecone_client - INFO - Using mock Pinecone client for testing
2025-06-02 07:50:52 - google.auth._default - DEBUG - Checking /dev/null for explicit credentials as part of auth process...
2025-06-02 07:50:52 - pi_lawyer.utils.storage_utils - ERROR - Failed to initialize GCS client: ('File /dev/null is not a valid json file.', <PERSON><PERSON><PERSON><PERSON>odeError('Expecting value: line 1 column 1 (char 0)'))
2025-06-02 07:50:52 - google.auth._default - DEBUG - Checking /dev/null for explicit credentials as part of auth process...
2025-06-02 07:50:52 - pi_lawyer.utils.storage_utils - ERROR - Failed to initialize GCS client: ('File /dev/null is not a valid json file.', J<PERSON><PERSON>ecode<PERSON><PERSON><PERSON>('Expecting value: line 1 column 1 (char 0)'))
2025-06-02 07:50:55 - backend.services.calendly - WARNING - CALENDLY_PAT not set. Calendly API calls will fail.
2025-06-02 07:50:55 - backend.services.calendly - WARNING - CALENDLY_ORG_URI not set. Calendly API calls will fail.

==================================== ERRORS ====================================
________________ ERROR collecting tests/test_circuit_breaker.py ________________
ImportError while importing test module '/mnt/persist/workspace/tests/test_circuit_breaker.py'.
Hint: make sure your test modules/packages have valid Python names.
Traceback:
/usr/lib/python3.10/importlib/__init__.py:126: in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
tests/test_circuit_breaker.py:4: in <module>
    from pi_lawyer.services.circuit_breaker import (
E   ModuleNotFoundError: No module named 'pi_lawyer.services.circuit_breaker'
________________ ERROR collecting tests/test_document_worker.py ________________
ImportError while importing test module '/mnt/persist/workspace/tests/test_document_worker.py'.
Hint: make sure your test modules/packages have valid Python names.
Traceback:
/usr/lib/python3.10/importlib/__init__.py:126: in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
tests/test_document_worker.py:11: in <module>
    import pi_lawyer.services.circuit_breaker as _cb
E   ModuleNotFoundError: No module named 'pi_lawyer.services.circuit_breaker'
------------------------------- Captured stderr --------------------------------
INFO:pi_lawyer.db.pinecone_client:Using mock Pinecone client for testing
_____________ ERROR collecting tests/test_error_classification.py ______________
ImportError while importing test module '/mnt/persist/workspace/tests/test_error_classification.py'.
Hint: make sure your test modules/packages have valid Python names.
Traceback:
/usr/lib/python3.10/importlib/__init__.py:126: in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
tests/test_error_classification.py:11: in <module>
    from pi_lawyer.services.error_classification import ErrorClassifier, ErrorCategory
E   ModuleNotFoundError: No module named 'pi_lawyer.services.error_classification'
____________________ ERROR collecting tests/test_metrics.py ____________________
venv/lib/python3.10/site-packages/_pytest/runner.py:341: in from_call
    result: Optional[TResult] = func()
venv/lib/python3.10/site-packages/_pytest/runner.py:372: in <lambda>
    call = CallInfo.from_call(lambda: list(collector.collect()), "collect")
venv/lib/python3.10/site-packages/_pytest/python.py:531: in collect
    self._inject_setup_module_fixture()
venv/lib/python3.10/site-packages/_pytest/python.py:545: in _inject_setup_module_fixture
    self.obj, ("setUpModule", "setup_module")
venv/lib/python3.10/site-packages/_pytest/python.py:310: in obj
    self._obj = obj = self._getobj()
venv/lib/python3.10/site-packages/_pytest/python.py:528: in _getobj
    return self._importtestmodule()
venv/lib/python3.10/site-packages/_pytest/python.py:617: in _importtestmodule
    mod = import_path(self.path, mode=importmode, root=self.config.rootpath)
venv/lib/python3.10/site-packages/_pytest/pathlib.py:567: in import_path
    importlib.import_module(module_name)
/usr/lib/python3.10/importlib/__init__.py:126: in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
<frozen importlib._bootstrap>:1050: in _gcd_import
    ???
<frozen importlib._bootstrap>:1027: in _find_and_load
    ???
<frozen importlib._bootstrap>:1006: in _find_and_load_unlocked
    ???
<frozen importlib._bootstrap>:688: in _load_unlocked
    ???
venv/lib/python3.10/site-packages/_pytest/assertion/rewrite.py:186: in exec_module
    exec(co, module.__dict__)
tests/test_metrics.py:13: in <module>
    from backend.api.main import create_app
backend/api/main.py:20: in <module>
    from .copilotkit_route import router as copilotkit_router
backend/api/copilotkit_route.py:12: in <module>
    from backend.agents.interactive.research.copilot_handler import ResearchCopilotHandler
backend/agents/interactive/research/__init__.py:22: in <module>
    from backend.agents.interactive.research.agent import ResearchAgent
backend/agents/interactive/research/agent.py:38: in <module>
    from backend.agents.interactive.research.graph import create_research_graph
backend/agents/interactive/research/graph.py:43: in <module>
    from backend.agents.interactive.research.node_classes import (
backend/agents/interactive/research/node_classes.py:13: in <module>
    from pi_lawyer.agents.base_agent import BaseAgent
src/pi_lawyer/agents/__init__.py:9: in <module>
    from pi_lawyer.agents.interactive import *
E   AttributeError: module 'pi_lawyer.agents.interactive' has no attribute 'IntakeAgent'
________________ ERROR collecting tests/test_research_access.py ________________
venv/lib/python3.10/site-packages/_pytest/runner.py:341: in from_call
    result: Optional[TResult] = func()
venv/lib/python3.10/site-packages/_pytest/runner.py:372: in <lambda>
    call = CallInfo.from_call(lambda: list(collector.collect()), "collect")
venv/lib/python3.10/site-packages/_pytest/python.py:531: in collect
    self._inject_setup_module_fixture()
venv/lib/python3.10/site-packages/_pytest/python.py:545: in _inject_setup_module_fixture
    self.obj, ("setUpModule", "setup_module")
venv/lib/python3.10/site-packages/_pytest/python.py:310: in obj
    self._obj = obj = self._getobj()
venv/lib/python3.10/site-packages/_pytest/python.py:528: in _getobj
    return self._importtestmodule()
venv/lib/python3.10/site-packages/_pytest/python.py:617: in _importtestmodule
    mod = import_path(self.path, mode=importmode, root=self.config.rootpath)
venv/lib/python3.10/site-packages/_pytest/pathlib.py:567: in import_path
    importlib.import_module(module_name)
/usr/lib/python3.10/importlib/__init__.py:126: in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
<frozen importlib._bootstrap>:1050: in _gcd_import
    ???
<frozen importlib._bootstrap>:1027: in _find_and_load
    ???
<frozen importlib._bootstrap>:1006: in _find_and_load_unlocked
    ???
<frozen importlib._bootstrap>:688: in _load_unlocked
    ???
venv/lib/python3.10/site-packages/_pytest/assertion/rewrite.py:186: in exec_module
    exec(co, module.__dict__)
tests/test_research_access.py:6: in <module>
    from pi_lawyer.agents.research_agent.state import (
src/pi_lawyer/agents/__init__.py:9: in <module>
    from pi_lawyer.agents.interactive import *
E   AttributeError: module 'pi_lawyer.agents.interactive' has no attribute 'IntakeAgent'
________________ ERROR collecting tests/test_research_agent.py _________________
venv/lib/python3.10/site-packages/_pytest/runner.py:341: in from_call
    result: Optional[TResult] = func()
venv/lib/python3.10/site-packages/_pytest/runner.py:372: in <lambda>
    call = CallInfo.from_call(lambda: list(collector.collect()), "collect")
venv/lib/python3.10/site-packages/_pytest/python.py:531: in collect
    self._inject_setup_module_fixture()
venv/lib/python3.10/site-packages/_pytest/python.py:545: in _inject_setup_module_fixture
    self.obj, ("setUpModule", "setup_module")
venv/lib/python3.10/site-packages/_pytest/python.py:310: in obj
    self._obj = obj = self._getobj()
venv/lib/python3.10/site-packages/_pytest/python.py:528: in _getobj
    return self._importtestmodule()
venv/lib/python3.10/site-packages/_pytest/python.py:617: in _importtestmodule
    mod = import_path(self.path, mode=importmode, root=self.config.rootpath)
venv/lib/python3.10/site-packages/_pytest/pathlib.py:567: in import_path
    importlib.import_module(module_name)
/usr/lib/python3.10/importlib/__init__.py:126: in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
<frozen importlib._bootstrap>:1050: in _gcd_import
    ???
<frozen importlib._bootstrap>:1027: in _find_and_load
    ???
<frozen importlib._bootstrap>:1006: in _find_and_load_unlocked
    ???
<frozen importlib._bootstrap>:688: in _load_unlocked
    ???
venv/lib/python3.10/site-packages/_pytest/assertion/rewrite.py:186: in exec_module
    exec(co, module.__dict__)
tests/test_research_agent.py:32: in <module>
    from backend.agents.interactive.research.graph import create_research_graph
backend/agents/interactive/research/__init__.py:22: in <module>
    from backend.agents.interactive.research.agent import ResearchAgent
backend/agents/interactive/research/agent.py:38: in <module>
    from backend.agents.interactive.research.graph import create_research_graph
backend/agents/interactive/research/graph.py:43: in <module>
    from backend.agents.interactive.research.node_classes import (
backend/agents/interactive/research/node_classes.py:13: in <module>
    from pi_lawyer.agents.base_agent import BaseAgent
src/pi_lawyer/agents/__init__.py:9: in <module>
    from pi_lawyer.agents.interactive import *
E   AttributeError: module 'pi_lawyer.agents.interactive' has no attribute 'IntakeAgent'
_________________ ERROR collecting tests/test_storage_utils.py _________________
venv/lib/python3.10/site-packages/google/auth/_default.py:131: in load_credentials_from_file
    info = json.load(file_obj)
/usr/lib/python3.10/json/__init__.py:293: in load
    return loads(fp.read(),
/usr/lib/python3.10/json/__init__.py:346: in loads
    return _default_decoder.decode(s)
/usr/lib/python3.10/json/decoder.py:337: in decode
    obj, end = self.raw_decode(s, idx=_w(s, 0).end())
/usr/lib/python3.10/json/decoder.py:355: in raw_decode
    raise JSONDecodeError("Expecting value", s, err.value) from None
E   json.decoder.JSONDecodeError: Expecting value: line 1 column 1 (char 0)

The above exception was the direct cause of the following exception:
venv/lib/python3.10/site-packages/_pytest/runner.py:341: in from_call
    result: Optional[TResult] = func()
venv/lib/python3.10/site-packages/_pytest/runner.py:372: in <lambda>
    call = CallInfo.from_call(lambda: list(collector.collect()), "collect")
venv/lib/python3.10/site-packages/_pytest/python.py:531: in collect
    self._inject_setup_module_fixture()
venv/lib/python3.10/site-packages/_pytest/python.py:545: in _inject_setup_module_fixture
    self.obj, ("setUpModule", "setup_module")
venv/lib/python3.10/site-packages/_pytest/python.py:310: in obj
    self._obj = obj = self._getobj()
venv/lib/python3.10/site-packages/_pytest/python.py:528: in _getobj
    return self._importtestmodule()
venv/lib/python3.10/site-packages/_pytest/python.py:617: in _importtestmodule
    mod = import_path(self.path, mode=importmode, root=self.config.rootpath)
venv/lib/python3.10/site-packages/_pytest/pathlib.py:567: in import_path
    importlib.import_module(module_name)
/usr/lib/python3.10/importlib/__init__.py:126: in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
<frozen importlib._bootstrap>:1050: in _gcd_import
    ???
<frozen importlib._bootstrap>:1027: in _find_and_load
    ???
<frozen importlib._bootstrap>:1006: in _find_and_load_unlocked
    ???
<frozen importlib._bootstrap>:688: in _load_unlocked
    ???
venv/lib/python3.10/site-packages/_pytest/assertion/rewrite.py:186: in exec_module
    exec(co, module.__dict__)
tests/test_storage_utils.py:2: in <module>
    from pi_lawyer.utils.storage_utils import StorageClient
pi_lawyer/utils/storage_utils.py:346: in <module>
    storage_client = StorageClient()
pi_lawyer/utils/storage_utils.py:41: in __init__
    self.client = storage.Client()
venv/lib/python3.10/site-packages/google/cloud/storage/client.py:226: in __init__
    super(Client, self).__init__(
venv/lib/python3.10/site-packages/google/cloud/client/__init__.py:338: in __init__
    _ClientProjectMixin.__init__(self, project=project, credentials=credentials)
venv/lib/python3.10/site-packages/google/cloud/client/__init__.py:286: in __init__
    project = self._determine_default(project)
venv/lib/python3.10/site-packages/google/cloud/client/__init__.py:305: in _determine_default
    return _determine_default_project(project)
venv/lib/python3.10/site-packages/google/cloud/_helpers/__init__.py:152: in _determine_default_project
    _, project = google.auth.default()
venv/lib/python3.10/site-packages/google/auth/_default.py:651: in default
    credentials, project_id = checker()
venv/lib/python3.10/site-packages/google/auth/_default.py:644: in <lambda>
    lambda: _get_explicit_environ_credentials(quota_project_id=quota_project_id),
venv/lib/python3.10/site-packages/google/auth/_default.py:293: in _get_explicit_environ_credentials
    credentials, project_id = load_credentials_from_file(
venv/lib/python3.10/site-packages/google/auth/_default.py:136: in load_credentials_from_file
    raise new_exc from caught_exc
E   google.auth.exceptions.DefaultCredentialsError: ('File /dev/null is not a valid json file.', JSONDecodeError('Expecting value: line 1 column 1 (char 0)'))
------------------------------- Captured stderr --------------------------------
DEBUG:google.auth._default:Checking /dev/null for explicit credentials as part of auth process...
ERROR:pi_lawyer.utils.storage_utils:Failed to initialize GCS client: ('File /dev/null is not a valid json file.', JSONDecodeError('Expecting value: line 1 column 1 (char 0)'))
______________ ERROR collecting tests/test_test_gcs_structure.py _______________
venv/lib/python3.10/site-packages/google/auth/_default.py:131: in load_credentials_from_file
    info = json.load(file_obj)
/usr/lib/python3.10/json/__init__.py:293: in load
    return loads(fp.read(),
/usr/lib/python3.10/json/__init__.py:346: in loads
    return _default_decoder.decode(s)
/usr/lib/python3.10/json/decoder.py:337: in decode
    obj, end = self.raw_decode(s, idx=_w(s, 0).end())
/usr/lib/python3.10/json/decoder.py:355: in raw_decode
    raise JSONDecodeError("Expecting value", s, err.value) from None
E   json.decoder.JSONDecodeError: Expecting value: line 1 column 1 (char 0)

The above exception was the direct cause of the following exception:
venv/lib/python3.10/site-packages/_pytest/runner.py:341: in from_call
    result: Optional[TResult] = func()
venv/lib/python3.10/site-packages/_pytest/runner.py:372: in <lambda>
    call = CallInfo.from_call(lambda: list(collector.collect()), "collect")
venv/lib/python3.10/site-packages/_pytest/python.py:531: in collect
    self._inject_setup_module_fixture()
venv/lib/python3.10/site-packages/_pytest/python.py:545: in _inject_setup_module_fixture
    self.obj, ("setUpModule", "setup_module")
venv/lib/python3.10/site-packages/_pytest/python.py:310: in obj
    self._obj = obj = self._getobj()
venv/lib/python3.10/site-packages/_pytest/python.py:528: in _getobj
    return self._importtestmodule()
venv/lib/python3.10/site-packages/_pytest/python.py:617: in _importtestmodule
    mod = import_path(self.path, mode=importmode, root=self.config.rootpath)
venv/lib/python3.10/site-packages/_pytest/pathlib.py:567: in import_path
    importlib.import_module(module_name)
/usr/lib/python3.10/importlib/__init__.py:126: in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
<frozen importlib._bootstrap>:1050: in _gcd_import
    ???
<frozen importlib._bootstrap>:1027: in _find_and_load
    ???
<frozen importlib._bootstrap>:1006: in _find_and_load_unlocked
    ???
<frozen importlib._bootstrap>:688: in _load_unlocked
    ???
venv/lib/python3.10/site-packages/_pytest/assertion/rewrite.py:186: in exec_module
    exec(co, module.__dict__)
tests/test_test_gcs_structure.py:2: in <module>
    from pi_lawyer.utils import test_gcs_structure
<frozen importlib._bootstrap>:1027: in _find_and_load
    ???
<frozen importlib._bootstrap>:1006: in _find_and_load_unlocked
    ???
<frozen importlib._bootstrap>:688: in _load_unlocked
    ???
venv/lib/python3.10/site-packages/_pytest/assertion/rewrite.py:186: in exec_module
    exec(co, module.__dict__)
pi_lawyer/utils/test_gcs_structure.py:10: in <module>
    from pi_lawyer.utils.storage_utils import test_gcs_structure
pi_lawyer/utils/storage_utils.py:346: in <module>
    storage_client = StorageClient()
pi_lawyer/utils/storage_utils.py:41: in __init__
    self.client = storage.Client()
venv/lib/python3.10/site-packages/google/cloud/storage/client.py:226: in __init__
    super(Client, self).__init__(
venv/lib/python3.10/site-packages/google/cloud/client/__init__.py:338: in __init__
    _ClientProjectMixin.__init__(self, project=project, credentials=credentials)
venv/lib/python3.10/site-packages/google/cloud/client/__init__.py:286: in __init__
    project = self._determine_default(project)
venv/lib/python3.10/site-packages/google/cloud/client/__init__.py:305: in _determine_default
    return _determine_default_project(project)
venv/lib/python3.10/site-packages/google/cloud/_helpers/__init__.py:152: in _determine_default_project
    _, project = google.auth.default()
venv/lib/python3.10/site-packages/google/auth/_default.py:651: in default
    credentials, project_id = checker()
venv/lib/python3.10/site-packages/google/auth/_default.py:644: in <lambda>
    lambda: _get_explicit_environ_credentials(quota_project_id=quota_project_id),
venv/lib/python3.10/site-packages/google/auth/_default.py:293: in _get_explicit_environ_credentials
    credentials, project_id = load_credentials_from_file(
venv/lib/python3.10/site-packages/google/auth/_default.py:136: in load_credentials_from_file
    raise new_exc from caught_exc
E   google.auth.exceptions.DefaultCredentialsError: ('File /dev/null is not a valid json file.', JSONDecodeError('Expecting value: line 1 column 1 (char 0)'))
------------------------------- Captured stderr --------------------------------
DEBUG:google.auth._default:Checking /dev/null for explicit credentials as part of auth process...
ERROR:pi_lawyer.utils.storage_utils:Failed to initialize GCS client: ('File /dev/null is not a valid json file.', JSONDecodeError('Expecting value: line 1 column 1 (char 0)'))
______________ ERROR collecting tests/api/test_jwt_integration.py ______________
ImportError while importing test module '/mnt/persist/workspace/tests/api/test_jwt_integration.py'.
Hint: make sure your test modules/packages have valid Python names.
Traceback:
/usr/lib/python3.10/importlib/__init__.py:126: in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
tests/api/test_jwt_integration.py:8: in <module>
    from src.pi_lawyer.api.runtime import app
src/pi_lawyer/api/__init__.py:11: in <module>
    from .authored_document_embedding import router as authored_document_embedding_router
src/pi_lawyer/api/authored_document_embedding.py:15: in <module>
    from pi_lawyer.auth.middleware import withAuth
src/pi_lawyer/auth/middleware.py:5: in <module>
    from pi_lawyer.db.supabase_client import SupabaseClient
E   ModuleNotFoundError: No module named 'pi_lawyer.db.supabase_client'
_______________ ERROR collecting tests/contract/test_calendar.py _______________
venv/lib/python3.10/site-packages/_pytest/runner.py:341: in from_call
    result: Optional[TResult] = func()
venv/lib/python3.10/site-packages/_pytest/runner.py:372: in <lambda>
    call = CallInfo.from_call(lambda: list(collector.collect()), "collect")
venv/lib/python3.10/site-packages/_pytest/python.py:531: in collect
    self._inject_setup_module_fixture()
venv/lib/python3.10/site-packages/_pytest/python.py:545: in _inject_setup_module_fixture
    self.obj, ("setUpModule", "setup_module")
venv/lib/python3.10/site-packages/_pytest/python.py:310: in obj
    self._obj = obj = self._getobj()
venv/lib/python3.10/site-packages/_pytest/python.py:528: in _getobj
    return self._importtestmodule()
venv/lib/python3.10/site-packages/_pytest/python.py:617: in _importtestmodule
    mod = import_path(self.path, mode=importmode, root=self.config.rootpath)
venv/lib/python3.10/site-packages/_pytest/pathlib.py:567: in import_path
    importlib.import_module(module_name)
/usr/lib/python3.10/importlib/__init__.py:126: in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
<frozen importlib._bootstrap>:1050: in _gcd_import
    ???
<frozen importlib._bootstrap>:1027: in _find_and_load
    ???
<frozen importlib._bootstrap>:1006: in _find_and_load_unlocked
    ???
<frozen importlib._bootstrap>:688: in _load_unlocked
    ???
venv/lib/python3.10/site-packages/_pytest/assertion/rewrite.py:186: in exec_module
    exec(co, module.__dict__)
tests/contract/test_calendar.py:14: in <module>
    from backend.agents.interactive.calendar_crud.agent import CalendarCrudAgent
backend/agents/interactive/calendar_crud/__init__.py:9: in <module>
    from backend.agents.interactive.calendar_crud.agent import CalendarCrudAgent
backend/agents/interactive/calendar_crud/agent.py:27: in <module>
    from backend.agents.interactive.calendar_crud.nodes import (
backend/agents/interactive/calendar_crud/nodes/__init__.py:13: in <module>
    from backend.agents.interactive.calendar_crud.nodes.create import create_event
backend/agents/interactive/calendar_crud/nodes/create.py:15: in <module>
    from pi_lawyer.agents.base_agent import BaseAgent
src/pi_lawyer/agents/__init__.py:9: in <module>
    from pi_lawyer.agents.interactive import *
E   AttributeError: module 'pi_lawyer.agents.interactive' has no attribute 'IntakeAgent'
_________ ERROR collecting tests/integration/test_auth_service_flow.py _________
ImportError while importing test module '/mnt/persist/workspace/tests/integration/test_auth_service_flow.py'.
Hint: make sure your test modules/packages have valid Python names.
Traceback:
/usr/lib/python3.10/importlib/__init__.py:126: in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
tests/integration/test_auth_service_flow.py:22: in <module>
    import respx
E   ModuleNotFoundError: No module named 'respx'
________ ERROR collecting tests/integration/api/test_jwt_integration.py ________
ImportError while importing test module '/mnt/persist/workspace/tests/integration/api/test_jwt_integration.py'.
Hint: make sure your test modules/packages have valid Python names.
Traceback:
/usr/lib/python3.10/importlib/__init__.py:126: in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
tests/integration/api/test_jwt_integration.py:8: in <module>
    from src.pi_lawyer.api.runtime import app
src/pi_lawyer/api/__init__.py:11: in <module>
    from .authored_document_embedding import router as authored_document_embedding_router
src/pi_lawyer/api/authored_document_embedding.py:15: in <module>
    from pi_lawyer.auth.middleware import withAuth
src/pi_lawyer/auth/middleware.py:5: in <module>
    from pi_lawyer.db.supabase_client import SupabaseClient
E   ModuleNotFoundError: No module named 'pi_lawyer.db.supabase_client'
_____________ ERROR collecting tests/providers/test_auth_retry.py ______________
venv/lib/python3.10/site-packages/_pytest/runner.py:341: in from_call
    result: Optional[TResult] = func()
venv/lib/python3.10/site-packages/_pytest/runner.py:372: in <lambda>
    call = CallInfo.from_call(lambda: list(collector.collect()), "collect")
venv/lib/python3.10/site-packages/_pytest/python.py:531: in collect
    self._inject_setup_module_fixture()
venv/lib/python3.10/site-packages/_pytest/python.py:545: in _inject_setup_module_fixture
    self.obj, ("setUpModule", "setup_module")
venv/lib/python3.10/site-packages/_pytest/python.py:310: in obj
    self._obj = obj = self._getobj()
venv/lib/python3.10/site-packages/_pytest/python.py:528: in _getobj
    return self._importtestmodule()
venv/lib/python3.10/site-packages/_pytest/python.py:617: in _importtestmodule
    mod = import_path(self.path, mode=importmode, root=self.config.rootpath)
venv/lib/python3.10/site-packages/_pytest/pathlib.py:567: in import_path
    importlib.import_module(module_name)
/usr/lib/python3.10/importlib/__init__.py:126: in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
<frozen importlib._bootstrap>:1050: in _gcd_import
    ???
<frozen importlib._bootstrap>:1027: in _find_and_load
    ???
<frozen importlib._bootstrap>:1006: in _find_and_load_unlocked
    ???
<frozen importlib._bootstrap>:688: in _load_unlocked
    ???
venv/lib/python3.10/site-packages/_pytest/assertion/rewrite.py:186: in exec_module
    exec(co, module.__dict__)
tests/providers/test_auth_retry.py:22: in <module>
    from backend.agents.interactive.calendar_crud.providers.exceptions import (
backend/agents/interactive/calendar_crud/__init__.py:9: in <module>
    from backend.agents.interactive.calendar_crud.agent import CalendarCrudAgent
backend/agents/interactive/calendar_crud/agent.py:27: in <module>
    from backend.agents.interactive.calendar_crud.nodes import (
backend/agents/interactive/calendar_crud/nodes/__init__.py:13: in <module>
    from backend.agents.interactive.calendar_crud.nodes.create import create_event
backend/agents/interactive/calendar_crud/nodes/create.py:15: in <module>
    from pi_lawyer.agents.base_agent import BaseAgent
src/pi_lawyer/agents/__init__.py:9: in <module>
    from pi_lawyer.agents.interactive import *
E   AttributeError: module 'pi_lawyer.agents.interactive' has no attribute 'IntakeAgent'
_________ ERROR collecting tests/providers/test_auth_retry_failure.py __________
venv/lib/python3.10/site-packages/_pytest/runner.py:341: in from_call
    result: Optional[TResult] = func()
venv/lib/python3.10/site-packages/_pytest/runner.py:372: in <lambda>
    call = CallInfo.from_call(lambda: list(collector.collect()), "collect")
venv/lib/python3.10/site-packages/_pytest/python.py:531: in collect
    self._inject_setup_module_fixture()
venv/lib/python3.10/site-packages/_pytest/python.py:545: in _inject_setup_module_fixture
    self.obj, ("setUpModule", "setup_module")
venv/lib/python3.10/site-packages/_pytest/python.py:310: in obj
    self._obj = obj = self._getobj()
venv/lib/python3.10/site-packages/_pytest/python.py:528: in _getobj
    return self._importtestmodule()
venv/lib/python3.10/site-packages/_pytest/python.py:617: in _importtestmodule
    mod = import_path(self.path, mode=importmode, root=self.config.rootpath)
venv/lib/python3.10/site-packages/_pytest/pathlib.py:567: in import_path
    importlib.import_module(module_name)
/usr/lib/python3.10/importlib/__init__.py:126: in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
<frozen importlib._bootstrap>:1050: in _gcd_import
    ???
<frozen importlib._bootstrap>:1027: in _find_and_load
    ???
<frozen importlib._bootstrap>:1006: in _find_and_load_unlocked
    ???
<frozen importlib._bootstrap>:688: in _load_unlocked
    ???
venv/lib/python3.10/site-packages/_pytest/assertion/rewrite.py:186: in exec_module
    exec(co, module.__dict__)
tests/providers/test_auth_retry_failure.py:22: in <module>
    from backend.agents.interactive.calendar_crud.providers.exceptions import (
backend/agents/interactive/calendar_crud/__init__.py:9: in <module>
    from backend.agents.interactive.calendar_crud.agent import CalendarCrudAgent
backend/agents/interactive/calendar_crud/agent.py:27: in <module>
    from backend.agents.interactive.calendar_crud.nodes import (
backend/agents/interactive/calendar_crud/nodes/__init__.py:13: in <module>
    from backend.agents.interactive.calendar_crud.nodes.create import create_event
backend/agents/interactive/calendar_crud/nodes/create.py:15: in <module>
    from pi_lawyer.agents.base_agent import BaseAgent
src/pi_lawyer/agents/__init__.py:9: in <module>
    from pi_lawyer.agents.interactive import *
E   AttributeError: module 'pi_lawyer.agents.interactive' has no attribute 'IntakeAgent'
________ ERROR collecting tests/providers/test_calendly_token_fetch.py _________
venv/lib/python3.10/site-packages/_pytest/runner.py:341: in from_call
    result: Optional[TResult] = func()
venv/lib/python3.10/site-packages/_pytest/runner.py:372: in <lambda>
    call = CallInfo.from_call(lambda: list(collector.collect()), "collect")
venv/lib/python3.10/site-packages/_pytest/python.py:531: in collect
    self._inject_setup_module_fixture()
venv/lib/python3.10/site-packages/_pytest/python.py:545: in _inject_setup_module_fixture
    self.obj, ("setUpModule", "setup_module")
venv/lib/python3.10/site-packages/_pytest/python.py:310: in obj
    self._obj = obj = self._getobj()
venv/lib/python3.10/site-packages/_pytest/python.py:528: in _getobj
    return self._importtestmodule()
venv/lib/python3.10/site-packages/_pytest/python.py:617: in _importtestmodule
    mod = import_path(self.path, mode=importmode, root=self.config.rootpath)
venv/lib/python3.10/site-packages/_pytest/pathlib.py:567: in import_path
    importlib.import_module(module_name)
/usr/lib/python3.10/importlib/__init__.py:126: in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
<frozen importlib._bootstrap>:1050: in _gcd_import
    ???
<frozen importlib._bootstrap>:1027: in _find_and_load
    ???
<frozen importlib._bootstrap>:1006: in _find_and_load_unlocked
    ???
<frozen importlib._bootstrap>:688: in _load_unlocked
    ???
venv/lib/python3.10/site-packages/_pytest/assertion/rewrite.py:186: in exec_module
    exec(co, module.__dict__)
tests/providers/test_calendly_token_fetch.py:13: in <module>
    from backend.agents.interactive.calendar_crud.providers.calendly.client import CalendlyClient
backend/agents/interactive/calendar_crud/__init__.py:9: in <module>
    from backend.agents.interactive.calendar_crud.agent import CalendarCrudAgent
backend/agents/interactive/calendar_crud/agent.py:27: in <module>
    from backend.agents.interactive.calendar_crud.nodes import (
backend/agents/interactive/calendar_crud/nodes/__init__.py:13: in <module>
    from backend.agents.interactive.calendar_crud.nodes.create import create_event
backend/agents/interactive/calendar_crud/nodes/create.py:15: in <module>
    from pi_lawyer.agents.base_agent import BaseAgent
src/pi_lawyer/agents/__init__.py:9: in <module>
    from pi_lawyer.agents.interactive import *
E   AttributeError: module 'pi_lawyer.agents.interactive' has no attribute 'IntakeAgent'
_________ ERROR collecting tests/providers/test_google_token_fetch.py __________
venv/lib/python3.10/site-packages/_pytest/runner.py:341: in from_call
    result: Optional[TResult] = func()
venv/lib/python3.10/site-packages/_pytest/runner.py:372: in <lambda>
    call = CallInfo.from_call(lambda: list(collector.collect()), "collect")
venv/lib/python3.10/site-packages/_pytest/python.py:531: in collect
    self._inject_setup_module_fixture()
venv/lib/python3.10/site-packages/_pytest/python.py:545: in _inject_setup_module_fixture
    self.obj, ("setUpModule", "setup_module")
venv/lib/python3.10/site-packages/_pytest/python.py:310: in obj
    self._obj = obj = self._getobj()
venv/lib/python3.10/site-packages/_pytest/python.py:528: in _getobj
    return self._importtestmodule()
venv/lib/python3.10/site-packages/_pytest/python.py:617: in _importtestmodule
    mod = import_path(self.path, mode=importmode, root=self.config.rootpath)
venv/lib/python3.10/site-packages/_pytest/pathlib.py:567: in import_path
    importlib.import_module(module_name)
/usr/lib/python3.10/importlib/__init__.py:126: in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
<frozen importlib._bootstrap>:1050: in _gcd_import
    ???
<frozen importlib._bootstrap>:1027: in _find_and_load
    ???
<frozen importlib._bootstrap>:1006: in _find_and_load_unlocked
    ???
<frozen importlib._bootstrap>:688: in _load_unlocked
    ???
venv/lib/python3.10/site-packages/_pytest/assertion/rewrite.py:186: in exec_module
    exec(co, module.__dict__)
tests/providers/test_google_token_fetch.py:13: in <module>
    from backend.agents.interactive.calendar_crud.providers.google.client import GoogleCalendarClient
backend/agents/interactive/calendar_crud/__init__.py:9: in <module>
    from backend.agents.interactive.calendar_crud.agent import CalendarCrudAgent
backend/agents/interactive/calendar_crud/agent.py:27: in <module>
    from backend.agents.interactive.calendar_crud.nodes import (
backend/agents/interactive/calendar_crud/nodes/__init__.py:13: in <module>
    from backend.agents.interactive.calendar_crud.nodes.create import create_event
backend/agents/interactive/calendar_crud/nodes/create.py:15: in <module>
    from pi_lawyer.agents.base_agent import BaseAgent
src/pi_lawyer/agents/__init__.py:9: in <module>
    from pi_lawyer.agents.interactive import *
E   AttributeError: module 'pi_lawyer.agents.interactive' has no attribute 'IntakeAgent'
_ ERROR collecting tests/unit/agents/insights/supervisor/test_model_select_node.py _
venv/lib/python3.10/site-packages/_pytest/runner.py:341: in from_call
    result: Optional[TResult] = func()
venv/lib/python3.10/site-packages/_pytest/runner.py:372: in <lambda>
    call = CallInfo.from_call(lambda: list(collector.collect()), "collect")
venv/lib/python3.10/site-packages/_pytest/python.py:531: in collect
    self._inject_setup_module_fixture()
venv/lib/python3.10/site-packages/_pytest/python.py:545: in _inject_setup_module_fixture
    self.obj, ("setUpModule", "setup_module")
venv/lib/python3.10/site-packages/_pytest/python.py:310: in obj
    self._obj = obj = self._getobj()
venv/lib/python3.10/site-packages/_pytest/python.py:528: in _getobj
    return self._importtestmodule()
venv/lib/python3.10/site-packages/_pytest/python.py:617: in _importtestmodule
    mod = import_path(self.path, mode=importmode, root=self.config.rootpath)
venv/lib/python3.10/site-packages/_pytest/pathlib.py:567: in import_path
    importlib.import_module(module_name)
/usr/lib/python3.10/importlib/__init__.py:126: in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
<frozen importlib._bootstrap>:1050: in _gcd_import
    ???
<frozen importlib._bootstrap>:1027: in _find_and_load
    ???
<frozen importlib._bootstrap>:1006: in _find_and_load_unlocked
    ???
<frozen importlib._bootstrap>:688: in _load_unlocked
    ???
venv/lib/python3.10/site-packages/_pytest/assertion/rewrite.py:186: in exec_module
    exec(co, module.__dict__)
tests/unit/agents/insights/supervisor/test_model_select_node.py:9: in <module>
    from src.pi_lawyer.agents.insights.supervisor.nodes import model_select_node
src/pi_lawyer/agents/__init__.py:9: in <module>
    from pi_lawyer.agents.interactive import *
E   AttributeError: module 'pi_lawyer.agents.interactive' has no attribute 'IntakeAgent'
________ ERROR collecting tests/unit/middleware/test_jwt_middleware.py _________
import file mismatch:
imported module 'test_jwt_middleware' has this __file__ attribute:
  /mnt/persist/workspace/tests/middleware/test_jwt_middleware.py
which is not the same as the test file we want to collect:
  /mnt/persist/workspace/tests/unit/middleware/test_jwt_middleware.py
HINT: remove __pycache__ / .pyc files and/or use a unique basename for your test file modules
=============================== warnings summary ===============================
src/pi_lawyer/db/supabase_client_stub.py:18
  /mnt/persist/workspace/src/pi_lawyer/db/supabase_client_stub.py:18: RuntimeWarning: Using stub implementation of SupabaseClient - NOT SUITABLE FOR PRODUCTION
    warnings.warn(

src/pi_lawyer/db/pinecone_client_stub.py:19
  /mnt/persist/workspace/src/pi_lawyer/db/pinecone_client_stub.py:19: RuntimeWarning: Using stub implementation of Pinecone client - NOT SUITABLE FOR PRODUCTION
    warnings.warn(

venv/lib/python3.10/site-packages/pydantic/_internal/_config.py:323: 19 warnings
  /mnt/persist/workspace/venv/lib/python3.10/site-packages/pydantic/_internal/_config.py:323: PydanticDeprecatedSince20: Support for class-based `config` is deprecated, use ConfigDict instead. Deprecated in Pydantic V2.0 to be removed in V3.0. See Pydantic V2 Migration Guide at https://errors.pydantic.dev/2.11/migration/
    warnings.warn(DEPRECATION_MESSAGE, DeprecationWarning)

src/pi_lawyer/services/__init__.py:30
  /mnt/persist/workspace/src/pi_lawyer/services/__init__.py:30: UserWarning: Error importing service module: cannot import name 'DocumentChunk' from 'pi_lawyer.models.document' (/mnt/persist/workspace/pi_lawyer/models/document.py)
    warnings.warn(f"Error importing service module: {e}")

src/pi_lawyer/__init__.py:18
  /mnt/persist/workspace/src/pi_lawyer/__init__.py:18: UserWarning: Error importing submodule: No module named 'pi_lawyer.db.supabase_client'
    warnings.warn(f"Error importing submodule: {e}")

backend/agents/interactive/task_crud/graph.py:33
  /mnt/persist/workspace/backend/agents/interactive/task_crud/graph.py:33: LangChainDeprecationWarning: As of langchain-core 0.3.0, LangChain uses pydantic v2 internally. The langchain_core.pydantic_v1 module was a compatibility shim for pydantic v1, and should no longer be used. Please update the code to import from Pydantic directly.
  
  For example, replace imports like: `from langchain_core.pydantic_v1 import BaseModel`
  with: `from pydantic import BaseModel`
  or the v1 compatibility namespace if you are working in a code base that has not been fully upgraded to pydantic 2 yet. 	from pydantic.v1 import BaseModel
  
    from backend.agents.interactive.task_crud.router import task_crud_router

venv/lib/python3.10/site-packages/langchain/llms/__init__.py:549
venv/lib/python3.10/site-packages/langchain/llms/__init__.py:549
  /mnt/persist/workspace/venv/lib/python3.10/site-packages/langchain/llms/__init__.py:549: LangChainDeprecationWarning: Importing LLMs from langchain is deprecated. Importing from langchain will no longer be supported as of langchain==0.2.0. Please import from langchain-community instead:
  
  `from langchain_community.llms import OpenAI`.
  
  To install langchain-community run `pip install -U langchain-community`.
    warnings.warn(

venv/lib/python3.10/site-packages/pydantic/_internal/_config.py:373
  /mnt/persist/workspace/venv/lib/python3.10/site-packages/pydantic/_internal/_config.py:373: UserWarning: Valid config keys have changed in V2:
  * 'schema_extra' has been renamed to 'json_schema_extra'
    warnings.warn(message, UserWarning)

venv/lib/python3.10/site-packages/pydantic/_internal/_generate_schema.py:293: 10 warnings
  /mnt/persist/workspace/venv/lib/python3.10/site-packages/pydantic/_internal/_generate_schema.py:293: PydanticDeprecatedSince20: `json_encoders` is deprecated. See https://docs.pydantic.dev/2.11/concepts/serialization/#custom-serializers for alternatives. Deprecated in Pydantic V2.0 to be removed in V3.0. See Pydantic V2 Migration Guide at https://errors.pydantic.dev/2.11/migration/
    warnings.warn(

tests/llm_selector/test_selector.py:163
  /mnt/persist/workspace/tests/llm_selector/test_selector.py:163: PytestUnknownMarkWarning: Unknown pytest.mark.llm - is this a typo?  You can register custom marks to avoid this warning - for details, see https://docs.pytest.org/en/stable/how-to/mark.html
    @pytest.mark.llm

-- Docs: https://docs.pytest.org/en/stable/how-to/capture-warnings.html

---------- coverage: platform linux, python 3.10.12-final-0 ----------
Name                                                                     Stmts   Miss  Cover   Missing
------------------------------------------------------------------------------------------------------
src/pi_lawyer/__init__.py                                                   13      4    69%   12-15
src/pi_lawyer/agents/__init__.py                                             7      5    29%   14-25
src/pi_lawyer/agents/_stubs.py                                              36     36     0%   8-150
src/pi_lawyer/agents/base_agent.py                                         134    126     6%   64-518
src/pi_lawyer/agents/config.py                                             124    124     0%   37-434
src/pi_lawyer/agents/copilotkit_runtime.py                                  14     14     0%   1-36
src/pi_lawyer/agents/deadline_agent.py                                      20     20     0%   2-70
src/pi_lawyer/agents/document_agent.py                                      18     18     0%   2-61
src/pi_lawyer/agents/echo_agent.py                                         128    128     0%   22-413
src/pi_lawyer/agents/graph/__init__.py                                       3      3     0%   20-23
src/pi_lawyer/agents/graph/builder.py                                       68     68     0%   8-246
src/pi_lawyer/agents/graph/builder_update.py                                53     53     0%   8-199
src/pi_lawyer/agents/graph/finish.py                                        31     31     0%   8-124
src/pi_lawyer/agents/insights/__init__.py                                    0      0   100%
src/pi_lawyer/agents/insights/daily.py                                      13     13     0%   11-81
src/pi_lawyer/agents/insights/document.py                                   13     13     0%   11-81
src/pi_lawyer/agents/insights/supervisor/__init__.py                         4      4     0%   43-47
src/pi_lawyer/agents/insights/supervisor/agent.py                          168    168     0%   26-451
src/pi_lawyer/agents/insights/supervisor/nodes.py                           34     34     0%   7-101
src/pi_lawyer/agents/insights/supervisor/router.py                          22     22     0%   20-76
src/pi_lawyer/agents/insights/supervisor/schema.py                          18     18     0%   7-38
src/pi_lawyer/agents/insights/swarm.py                                       9      9     0%   10-56
src/pi_lawyer/agents/insights/test_insight_agents.py                        26     26     0%   7-68
src/pi_lawyer/agents/insights/verify_agents.py                              33     33     0%   7-86
src/pi_lawyer/agents/intake_agent.py                                        22     22     0%   2-73
src/pi_lawyer/agents/interactive/__init__.py                                 3      0   100%
src/pi_lawyer/agents/interactive/intake/__init__.py                          4      3    25%   25-35
src/pi_lawyer/agents/interactive/intake/agent.py                           138    127     8%   39-345
src/pi_lawyer/agents/interactive/intake/routers.py                          21     21     0%   15-78
src/pi_lawyer/agents/interactive/intake/tasks.py                           151    151     0%   23-436
src/pi_lawyer/agents/router.py                                              49     49     0%   8-143
src/pi_lawyer/api/__init__.py                                                7      4    43%   14-20
src/pi_lawyer/api/authored_document_embedding.py                            99     92     7%   16-284
src/pi_lawyer/api/copilotkit_route.py                                       44     44     0%   7-132
src/pi_lawyer/api/document_route.py                                        656    656     0%   10-1658
src/pi_lawyer/api/document_route_variables_update.py                        37     37     0%   7-118
src/pi_lawyer/api/health_api.py                                             48     32    33%   35-57, 71-82, 95-106, 124-146
src/pi_lawyer/api/intake_route.py                                           23     23     0%   1-57
src/pi_lawyer/api/main.py                                                   44     44     0%   8-94
src/pi_lawyer/api/research_route.py                                         26     26     0%   1-87
src/pi_lawyer/api/runtime.py                                                93     93     0%   9-264
src/pi_lawyer/api/runtime_old.py                                             0      0   100%
src/pi_lawyer/api/task_route.py                                            101    101     0%   2-264
src/pi_lawyer/auth/__init__.py                                               4      0   100%
src/pi_lawyer/auth/auth_helpers.py                                          11      1    91%   18
src/pi_lawyer/auth/jwt_auth.py                                              42     16    62%   50-68, 72-80, 88-89
src/pi_lawyer/auth/middleware.py                                            57     53     7%   6-111
src/pi_lawyer/auth/rbac.py                                                  68     43    37%   48, 54-76, 82-101, 113-158, 169
src/pi_lawyer/auth/tenant.py                                                29     21    28%   14-52, 57, 62
src/pi_lawyer/config.py                                                     23     23     0%   2-72
src/pi_lawyer/config/__init__.py                                           124    124     0%   9-320
src/pi_lawyer/config/cli.py                                                 56     56     0%   8-171
src/pi_lawyer/config/settings.py                                           125    125     0%   9-374
src/pi_lawyer/config/utils.py                                               53     53     0%   8-171
src/pi_lawyer/db/__init__.py                                                10      3    70%   16-18
src/pi_lawyer/db/client.py                                                  28     28     0%   8-68
src/pi_lawyer/db/database.py                                                28     19    32%   17-23, 38-40, 56-73, 85-87, 99-101
src/pi_lawyer/db/mock_auth.py                                               18     11    39%   10, 15-16, 21-37
src/pi_lawyer/db/pinecone_client.py                                         25     15    40%   25-27, 38-43, 55, 70, 88-95, 110-117, 126
src/pi_lawyer/db/pinecone_client_stub.py                                    22      8    64%   30-31, 35, 44-45, 49, 54, 67
src/pi_lawyer/db/supabase_client.py                                         69     50    28%   20-22, 30, 41-90, 103-110, 125-132, 143-150, 166-182, 196-212, 226-233, 247-265, 279-285, 299-317, 332-338
src/pi_lawyer/db/supabase_client_stub.py                                    18      8    56%   31-34, 38, 42, 46, 50
src/pi_lawyer/document_worker.py                                           163    163     0%   10-436
src/pi_lawyer/documents/__init__.py                                          5      5     0%   3-8
src/pi_lawyer/documents/embeddings.py                                       79     79     0%   2-210
src/pi_lawyer/documents/manager.py                                          72     72     0%   2-382
src/pi_lawyer/documents/processor.py                                        53     53     0%   2-142
src/pi_lawyer/documents/storage.py                                          30     30     0%   2-75
src/pi_lawyer/middleware/jwt_middleware.py                                  90     75    17%   30-100, 116-125, 138-146, 162-180, 201-269
src/pi_lawyer/middleware/rate_limit_middleware.py                           55     40    27%   32-56, 69-77, 91-111, 126-148, 156-166
src/pi_lawyer/middleware/security_headers_middleware.py                     25     15    40%   50-64, 74-92
src/pi_lawyer/models/auth.py                                                37     14    62%   52-73
src/pi_lawyer/services/__init__.py                                          14      5    64%   16-24
src/pi_lawyer/services/authored_document_embedding_service.py              121    121     0%   9-509
src/pi_lawyer/services/authored_document_worker.py                          85     85     0%   8-243
src/pi_lawyer/services/circuit_breaker.py                                  143    103    28%   93-97, 101-117, 129-166, 175-199, 209-221, 238-249, 266-277, 298-326
src/pi_lawyer/services/circuit_breaker_stub.py                              35     35     0%   11-75
src/pi_lawyer/services/deadline_extraction_service.py                      267    267     0%   2-730
src/pi_lawyer/services/document_analysis_service.py                        152    131    14%   18, 22-36, 40-41, 45-63, 67-105, 114-128, 134-212, 216-237, 246-274, 278-310, 314-344
src/pi_lawyer/services/document_classifier_service.py                       92     75    18%   51-86, 112-296
src/pi_lawyer/services/document_embedding_utils.py                          49     49     0%   8-178
src/pi_lawyer/services/document_parser_service.py                           80     80     0%   9-493
src/pi_lawyer/services/document_processing_queue.py                        310    310     0%   5-1152
src/pi_lawyer/services/document_processing_transaction.py                  100    100     0%   8-318
src/pi_lawyer/services/document_summarization.py                            15     15     0%   1-48
src/pi_lawyer/services/document_worker.py                                  174    161     7%   23-624
src/pi_lawyer/services/error_classification.py                              75     53    29%   42-93, 106-107, 125-139, 152-169
src/pi_lawyer/services/error_classification_stub.py                         39     39     0%   11-99
src/pi_lawyer/services/health_service.py                                   112     88    21%   37-52, 61-106, 119-129, 138-166, 175-199, 211-218, 230-272, 284-290, 309-312
src/pi_lawyer/services/metrics_collector.py                                150    129    14%   42-50, 56, 60-62, 75-102, 122-147, 172-232, 249-303, 307-332, 351-354
src/pi_lawyer/services/redis_lock_service.py                                80     80     0%   9-266
src/pi_lawyer/services/redis_queue_service.py                              127    106    17%   25-37, 50-65, 75-102, 112-135, 146-191, 201-213, 225-228, 241-250, 262-283, 292-297, 318-320
src/pi_lawyer/services/services/__init__.py                                  2      2     0%   4-6
src/pi_lawyer/services/services/authored_document_embedding_service.py     121    121     0%   9-496
src/pi_lawyer/services/services/authored_document_worker.py                 85     85     0%   8-243
src/pi_lawyer/services/services/circuit_breaker.py                         131    131     0%   23-334
src/pi_lawyer/services/services/deadline_extraction_service.py             267    267     0%   2-729
src/pi_lawyer/services/services/document_analysis_service.py               154    154     0%   1-348
src/pi_lawyer/services/services/document_classifier_service.py              92     92     0%   10-296
src/pi_lawyer/services/services/document_embedding_utils.py                 49     49     0%   8-178
src/pi_lawyer/services/services/document_parser_service.py                  76     76     0%   9-484
src/pi_lawyer/services/services/document_processing_queue.py               312    312     0%   5-1161
src/pi_lawyer/services/services/document_processing_transaction.py         100    100     0%   8-318
src/pi_lawyer/services/services/document_summarization.py                   15     15     0%   1-48
src/pi_lawyer/services/services/document_worker.py                         174    174     0%   9-627
src/pi_lawyer/services/services/error_classification.py                     75     75     0%   8-169
src/pi_lawyer/services/services/health_service.py                          112    112     0%   8-312
src/pi_lawyer/services/services/metrics_collector.py                       150    150     0%   9-354
src/pi_lawyer/services/services/redis_lock_service.py                       80     80     0%   9-266
src/pi_lawyer/services/services/redis_queue_service.py                     127    127     0%   9-320
src/pi_lawyer/services/services/task_embedding_service.py                   69     69     0%   2-243
src/pi_lawyer/services/services/tenant_document_embedding_service.py       141    141     0%   10-503
src/pi_lawyer/services/task_embedding_service.py                            81     57    30%   39-52, 57-58, 72-100, 112-137, 152-173, 187-198, 220-254
src/pi_lawyer/services/tenant_document_embedding_service.py                167    155     7%   26-551
src/pi_lawyer/shared/__init__.py                                             1      0   100%
src/pi_lawyer/shared/core/__init__.py                                        2      0   100%
src/pi_lawyer/shared/core/llm/__init__.py                                   16      0   100%
src/pi_lawyer/shared/core/llm/admin/__init__.py                              5      0   100%
src/pi_lawyer/shared/core/llm/admin/keys.py                                100     66    34%   68-72, 102-137, 152-166, 181-184, 198-209, 238-256, 276-292, 304-309, 329-347
src/pi_lawyer/shared/core/llm/admin/models.py                               31      8    74%   295-307
src/pi_lawyer/shared/core/llm/admin/prompts.py                              83     51    39%   52-57, 72-83, 99-102, 117-131, 159-183, 203-217, 229-236, 251-254, 266-269
src/pi_lawyer/shared/core/llm/admin/registry.py                             71     52    27%   36-40, 45-60, 68-89, 93-106, 119-122, 136-154, 163, 174
src/pi_lawyer/shared/core/llm/api.py                                        49     41    16%   40-106, 130-206
src/pi_lawyer/shared/core/llm/base.py                                       57     39    32%   70-75, 149-222, 234-235
src/pi_lawyer/shared/core/llm/config.py                                     76     22    71%   65-87, 160-182
src/pi_lawyer/shared/core/llm/embeddings/__init__.py                         3      0   100%
src/pi_lawyer/shared/core/llm/embeddings/api.py                             49     40    18%   39-81, 105-153
src/pi_lawyer/shared/core/llm/embeddings/base.py                            53     40    25%   56-62, 99-172, 184-185
src/pi_lawyer/shared/core/llm/factory.py                                    56     39    30%   31-34, 55-91, 104-108, 124-155, 182-183
src/pi_lawyer/shared/core/llm/openai.py                                     43     43     0%   8-201
src/pi_lawyer/shared/core/llm/providers/__init__.py                          6      0   100%
src/pi_lawyer/shared/core/llm/providers/anthropic.py                        59     47    20%   60-76, 109-112, 148-229, 254-260
src/pi_lawyer/shared/core/llm/providers/gemini.py                           75     61    19%   60-76, 105-152, 181-251, 274-279
src/pi_lawyer/shared/core/llm/providers/groq.py                             44     32    27%   52-68, 101-104, 140-187, 212-218
src/pi_lawyer/shared/core/llm/providers/mock.py                             34     22    35%   49-59, 85-101, 136-168, 203-208
src/pi_lawyer/shared/core/llm/providers/openai.py                           44     32    27%   52-68, 101-104, 140-187, 212-218
src/pi_lawyer/shared/core/llm/voyage.py                                    101     84    17%   59-67, 90-173, 196-281, 290-291, 307-314
src/pi_lawyer/shared/core/state.py                                          79     29    63%   84-85, 89, 93, 98-99, 104, 114-119, 128-136, 146-147, 160, 193-205, 221-222, 236-239
src/pi_lawyer/shared/core/tools/__init__.py                                  5      5     0%   8-26
src/pi_lawyer/shared/core/tools/async_job.py                                33     33     0%   27-144
src/pi_lawyer/shared/core/tools/base.py                                     23     23     0%   7-66
src/pi_lawyer/shared/core/tools/enqueue_async_job.py                        27     27     0%   7-74
src/pi_lawyer/shared/core/tools/executor.py                                 97     97     0%   44-318
src/pi_lawyer/shared/core/tools/schema.py                                   29     29     0%   7-413
src/pi_lawyer/state/langgraph_state.py                                     381    367     4%   91-1051
src/pi_lawyer/utils/__init__.py                                              0      0   100%
src/pi_lawyer/utils/check_gcs.py                                            45     45     0%   5-79
src/pi_lawyer/utils/demonstrate_paths.py                                    47     47     0%   5-120
src/pi_lawyer/utils/gemini_client.py                                        52     52     0%   7-165
src/pi_lawyer/utils/logging.py                                              14     14     0%   2-38
src/pi_lawyer/utils/query_classifier.py                                     76     76     0%   7-264
src/pi_lawyer/utils/storage_utils.py                                       170    170     0%   5-450
src/pi_lawyer/utils/structured_logging.py                                   95     68    28%   21, 31-33, 38, 43, 48, 53, 58, 63-68, 75-76, 81-100, 114-123, 130-146, 150-166, 177-203, 229-240
src/pi_lawyer/utils/telemetry.py                                            64     64     0%   10-218
src/pi_lawyer/utils/template_utils.py                                      193    193     0%   3-477
src/pi_lawyer/utils/test_gcs_structure.py                                   40     40     0%   5-87
src/pi_lawyer/utils/voyage_embeddings.py                                    47     27    43%   45-56, 67-80, 92-95, 106-114, 126-129
src/pi_lawyer/utils/voyage_reranker.py                                      42     42     0%   4-123
------------------------------------------------------------------------------------------------------
TOTAL                                                                    11391  10441     8%

=========================== short test summary info ============================
ERROR tests/test_circuit_breaker.py
ERROR tests/test_document_worker.py
ERROR tests/test_error_classification.py
ERROR tests/test_metrics.py - AttributeError: module 'pi_lawyer.agents.intera...
ERROR tests/test_research_access.py - AttributeError: module 'pi_lawyer.agent...
ERROR tests/test_research_agent.py - AttributeError: module 'pi_lawyer.agents...
ERROR tests/test_storage_utils.py - google.auth.exceptions.DefaultCredentials...
ERROR tests/test_test_gcs_structure.py - google.auth.exceptions.DefaultCreden...
ERROR tests/api/test_jwt_integration.py
ERROR tests/contract/test_calendar.py - AttributeError: module 'pi_lawyer.age...
ERROR tests/integration/test_auth_service_flow.py
ERROR tests/integration/api/test_jwt_integration.py
ERROR tests/providers/test_auth_retry.py - AttributeError: module 'pi_lawyer....
ERROR tests/providers/test_auth_retry_failure.py - AttributeError: module 'pi...
ERROR tests/providers/test_calendly_token_fetch.py - AttributeError: module '...
ERROR tests/providers/test_google_token_fetch.py - AttributeError: module 'pi...
ERROR tests/unit/agents/insights/supervisor/test_model_select_node.py - Attri...
ERROR tests/unit/middleware/test_jwt_middleware.py
!!!!!!!!!!!!!!!!!!! Interrupted: 18 errors during collection !!!!!!!!!!!!!!!!!!!
======================= 38 warnings, 18 errors in 8.23s ========================
