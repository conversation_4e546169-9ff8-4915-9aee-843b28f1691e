"""
Standalone test for AG-UI protocol models.

This script tests the AG-UI protocol models directly without importing from the
existing codebase.
"""

import unittest
import json
from typing import Dict, Any, List, Optional, Union
from pydantic import BaseModel, Field

# Define the AG-UI protocol models directly in this file
class AGUIToolCallFunction(BaseModel):
    """AG-UI Tool Call Function format."""
    name: str
    arguments: str

class AGUIToolCall(BaseModel):
    """AG-UI Tool Call format."""
    id: str
    type: str = "function"
    function: AGUIToolCallFunction

class AGUIMessage(BaseModel):
    """AG-UI Message format."""
    role: str
    content: Union[str, List[str], None]
    name: Optional[str] = None
    tool_calls: Optional[List[AGUIToolCall]] = None
    tool_call_id: Optional[str] = None

    class Config:
        """Pydantic configuration."""
        extra = "allow"  # Allow extra fields for forward compatibility

class AGUIRequest(BaseModel):
    """AG-UI Request format."""
    messages: List[AGUIMessage]
    agent: Optional[str] = None
    threadId: Optional[str] = None
    stream: bool = True
    model: Optional[str] = None
    temperature: Optional[float] = None
    max_tokens: Optional[int] = None
    tools: Optional[List[Dict[str, Any]]] = None
    tool_choice: Optional[Union[str, Dict[str, Any]]] = None
    user: Optional[str] = None
    shared_state: Optional[Dict[str, Any]] = None
    metadata: Optional[Dict[str, Any]] = None

    class Config:
        """Pydantic configuration."""
        extra = "allow"  # Allow extra fields for forward compatibility

class AGUIResponse(BaseModel):
    """AG-UI Response format."""
    messages: List[AGUIMessage]
    done: bool = True
    threadId: str
    customData: Optional[Dict[str, Any]] = None

    class Config:
        """Pydantic configuration."""
        extra = "allow"  # Allow extra fields for forward compatibility

class AGUIStreamEvent(BaseModel):
    """AG-UI Stream Event format."""
    type: str
    content: Optional[str] = None
    threadId: Optional[str] = None
    toolCalls: Optional[List[AGUIToolCall]] = None
    toolResults: Optional[Dict[str, Any]] = None
    error: Optional[str] = None
    
    class Config:
        """Pydantic configuration."""
        extra = "allow"  # Allow extra fields for forward compatibility

class TestAGUIModels(unittest.TestCase):
    """Test the AG-UI protocol models."""

    def test_agui_message_model(self):
        """Test the AGUIMessage model."""
        # Test valid message
        message = AGUIMessage(role="user", content="Test message")
        self.assertEqual(message.role, "user")
        self.assertEqual(message.content, "Test message")
        
        # Test message with tool calls
        tool_call = AGUIToolCall(
            id="tool-1",
            type="function",
            function=AGUIToolCallFunction(name="test_function", arguments="{}")
        )
        message = AGUIMessage(
            role="assistant",
            content="",
            tool_calls=[tool_call]
        )
        self.assertEqual(message.role, "assistant")
        self.assertEqual(message.tool_calls[0].id, "tool-1")
        
        # Test message with tool call ID
        message = AGUIMessage(
            role="tool",
            content="Tool result",
            tool_call_id="tool-1"
        )
        self.assertEqual(message.role, "tool")
        self.assertEqual(message.tool_call_id, "tool-1")

    def test_agui_request_model(self):
        """Test the AGUIRequest model."""
        # Test minimal request
        request = AGUIRequest(
            messages=[AGUIMessage(role="user", content="Test message")]
        )
        self.assertEqual(len(request.messages), 1)
        self.assertEqual(request.messages[0].role, "user")
        self.assertTrue(request.stream)  # Default value
        
        # Test full request
        request = AGUIRequest(
            messages=[AGUIMessage(role="user", content="Test message")],
            agent="test-agent",
            threadId="test-thread",
            stream=False,
            model="gpt-4",
            temperature=0.7,
            max_tokens=100,
            tools=[{
                "type": "function",
                "function": {"name": "test_function", "parameters": {}}
            }],
            tool_choice="auto",
            user="test-user",
            shared_state={"key": "value"},
            metadata={"session_id": "123"}
        )
        self.assertEqual(request.agent, "test-agent")
        self.assertEqual(request.threadId, "test-thread")
        self.assertFalse(request.stream)
        self.assertEqual(request.model, "gpt-4")
        self.assertEqual(request.temperature, 0.7)
        self.assertEqual(request.max_tokens, 100)
        self.assertEqual(len(request.tools), 1)
        self.assertEqual(request.tool_choice, "auto")
        self.assertEqual(request.user, "test-user")
        self.assertEqual(request.shared_state, {"key": "value"})
        self.assertEqual(request.metadata, {"session_id": "123"})

    def test_agui_response_model(self):
        """Test the AGUIResponse model."""
        # Test minimal response
        response = AGUIResponse(
            messages=[AGUIMessage(role="assistant", content="Test response")],
            threadId="test-thread"
        )
        self.assertEqual(len(response.messages), 1)
        self.assertEqual(response.messages[0].role, "assistant")
        self.assertTrue(response.done)  # Default value
        
        # Test full response
        response = AGUIResponse(
            messages=[AGUIMessage(role="assistant", content="Test response")],
            threadId="test-thread",
            done=False,
            customData={"key": "value"}
        )
        self.assertFalse(response.done)
        self.assertEqual(response.customData, {"key": "value"})

    def test_agui_stream_event_model(self):
        """Test the AGUIStreamEvent model."""
        # Test start event
        event = AGUIStreamEvent(
            type="start",
            threadId="test-thread"
        )
        self.assertEqual(event.type, "start")
        self.assertEqual(event.threadId, "test-thread")
        
        # Test content event
        event = AGUIStreamEvent(
            type="content",
            content="Test content"
        )
        self.assertEqual(event.type, "content")
        self.assertEqual(event.content, "Test content")
        
        # Test tool calls event
        tool_call = AGUIToolCall(
            id="tool-1",
            type="function",
            function=AGUIToolCallFunction(name="test_function", arguments="{}")
        )
        event = AGUIStreamEvent(
            type="tool_calls",
            toolCalls=[tool_call]
        )
        self.assertEqual(event.type, "tool_calls")
        self.assertEqual(event.toolCalls[0].id, "tool-1")
        
        # Test error event
        event = AGUIStreamEvent(
            type="error",
            error="Test error"
        )
        self.assertEqual(event.type, "error")
        self.assertEqual(event.error, "Test error")
        
        # Test done event
        event = AGUIStreamEvent(
            type="done",
            threadId="test-thread"
        )
        self.assertEqual(event.type, "done")
        self.assertEqual(event.threadId, "test-thread")

    def test_json_serialization(self):
        """Test JSON serialization of AG-UI models."""
        # Create a complex message with tool calls
        tool_call = AGUIToolCall(
            id="tool-1",
            type="function",
            function=AGUIToolCallFunction(
                name="test_function", arguments='{"arg1": "value1"}'
            )
        )
        message = AGUIMessage(
            role="assistant",
            content="",
            tool_calls=[tool_call]
        )
        
        # Serialize to JSON
        json_str = message.json()
        
        # Deserialize from JSON
        data = json.loads(json_str)
        
        # Verify the data
        self.assertEqual(data["role"], "assistant")
        self.assertEqual(data["content"], "")
        self.assertEqual(len(data["tool_calls"]), 1)
        self.assertEqual(data["tool_calls"][0]["id"], "tool-1")
        self.assertEqual(data["tool_calls"][0]["type"], "function")
        self.assertEqual(data["tool_calls"][0]["function"]["name"], "test_function")
        self.assertEqual(
            data["tool_calls"][0]["function"]["arguments"], '{"arg1": "value1"}'
        )

if __name__ == "__main__":
    unittest.main()
