"""
Script to list and debug Pinecone indexes.
"""
import os
import sys
from dotenv import load_dotenv
import pinecone
import pkg_resources

# Load environment variables
load_dotenv()

# Get Pinecone client version
pc_version = pkg_resources.get_distribution("pinecone-client").version
print(f"Pinecone client version: {pc_version}")

# Get credentials from environment
api_key = os.getenv("PINECONE_API_KEY")
environment = os.getenv("PINECONE_ENVIRONMENT")  # Should be us-east-1-aws

print(f"API Key: {api_key[:5]}...{api_key[-5:]}")
print(f"Environment: {environment}")

# List of possible environment formats
env_formats = [
    environment,
    "us-east-1",
    "us-east1-gcp",
    "gcp-starter"
]

for env in env_formats:
    print(f"\n---\nTrying environment: {env}")
    try:
        # Initialize Pinecone
        pinecone.init(api_key=api_key, environment=env)
        
        # List indexes
        print("Listing indexes...")
        indexes = pinecone.list_indexes()
        print(f"Found indexes: {indexes}")
        
        # If indexing using newer API style
        # This is Pinecone client v2
        try:
            print("\nTrying newer Pinecone client style...")
            pc = pinecone.Pinecone(api_key=api_key)
            indexes_v2 = pc.list_indexes()
            print(f"Found indexes (v2): {indexes_v2}")
        except Exception as e:
            print(f"Error with new style API: {str(e)}")
            
        if "texas-laws-voyage3large" in indexes:
            print("\n✅ Found texas-laws-voyage3large index!")
            try:
                # Try to describe the index
                print("Getting index description...")
                index = pinecone.Index("texas-laws-voyage3large")
                stats = index.describe_index_stats()
                print(f"Index stats: {stats}")
                
                # Success! Print configuration that works
                print(f"\n✅ SUCCESS with environment: {env}")
                print("Update your .env with this environment!")
                sys.exit(0)
            except Exception as e:
                print(f"Error accessing index: {str(e)}")
    except Exception as e:
        print(f"Error with environment {env}: {str(e)}")

print(
    "\n❌ Failed to find or access texas-laws-voyage3large index with any environment."
)
print(
    "Please check your Pinecone dashboard for the correct index name and API key "
    "permissions."
)
