"""
Simple Pinecone connection test with multiple environment formats.
"""
import os
from dotenv import load_dotenv
import pinecone
import sys

# Load environment variables
load_dotenv()

def test_pinecone():
    api_key = os.getenv("PINECONE_API_KEY")
    index_name = os.getenv("PINECONE_INDEX_NAME")
    
    if not api_key:
        print("❌ Missing PINECONE_API_KEY in .env file")
        sys.exit(1)
    
    # List of environment formats to try
    env_formats = [
        os.getenv("PINECONE_ENVIRONMENT", "us-east-1"),  # Current value
        "us-east-1-aws",                                # Common AWS format
        "gcp-starter",                                  # GCP starter format
        "us-west1-gcp",                                 # Other region formats
        "us-west4-gcp",
        "northamerica-northeast1-gcp",
        "asia-southeast1-gcp",
        "eu-west4-gcp"
    ]
    
    for env in env_formats:
        try:
            print(f"\n🔄 Trying environment: {env}")
            # Initialize Pinecone with the current environment format
            pinecone.init(api_key=api_key, environment=env)
            
            # Try to list indexes
            indexes = pinecone.list_indexes()
            print(f"✅ Success with environment: {env}")
            index_list = ', '.join(indexes) if indexes else 'No indexes found'
            print(f"📋 Available indexes: {index_list}")
            
            # If index_name is provided, try to connect to it
            if index_name and index_name in indexes:
                print(f"🔍 Testing connection to index: {index_name}")
                index = pinecone.Index(index_name)
                stats = index.describe_index_stats()
                print(f"📊 Index stats: {stats}")
                
                # List namespaces if any
                if 'namespaces' in stats and stats['namespaces']:
                    print(f"🗂️ Namespaces: {', '.join(stats['namespaces'].keys())}")
                else:
                    print("⚠️ No namespaces found in the index")
            
            # If we reach here without exceptions, we found a working environment
            print("\n✅ Success! Use this environment setting in your .env file:")
            print(f"PINECONE_ENVIRONMENT={env}")
            return True
            
        except Exception as e:
            print(f"❌ Failed with environment {env}: {str(e)}")
    
    # If we tried all environments and none worked
    print("\n❌ Failed to connect to Pinecone with any environment format.")
    print("Please check your API key and network connectivity.")
    return False

if __name__ == "__main__":
    print("🔍 Testing Pinecone connection with multiple environment formats...")
    test_pinecone()
