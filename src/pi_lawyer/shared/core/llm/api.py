"""
Unified LLM API

This module provides a unified API for interacting with LLMs, with support
for multiple providers, fallback mechanisms, and caching.
"""

import logging
from typing import Any, Dict, List, Optional, Union

from .base import LLMError
from .config import LLMConfig, LLMMessage, LLMResponse
from .factory import get_llm_client

# Set up logging
logger = logging.getLogger(__name__)


async def generate_text(
    prompt: str,
    config: Optional[LLMConfig] = None,
    fallback_configs: Optional[List[LLMConfig]] = None,
    **kwargs
) -> LLMResponse:
    """
    Generate text from an LLM with fallback support.
    
    Args:
        prompt: The prompt to send to the LLM
        config: Configuration for the primary LLM
        fallback_configs: Configurations for fallback LLMs
        **kwargs: Additional parameters for the LLM
        
    Returns:
        The LLM response
        
    Raises:
        LLMError: If all LLM requests fail
    """
    if config is None:
        config = LLMConfig()
    
    # Try the primary LLM
    try:
        client = get_llm_client(config)
        result = await client.generate(
            prompt=prompt,
            model=config.model,
            temperature=config.temperature,
            max_tokens=config.max_tokens,
            stop_sequences=config.stop_sequences,
            **kwargs
        )
        
        return LLMResponse(
            content=result.get("text", ""),
            model=result.get("model", config.model),
            usage=result.get("usage", {}),
            finish_reason=result.get("finish_reason")
        )
    except LLMError as e:
        logger.warning(
            f"Primary LLM request failed: {str(e)}",
            extra={"error": str(e), "provider": config.provider, "model": config.model}
        )
        
        # Try fallback LLMs if available
        if fallback_configs:
            for fallback_config in fallback_configs:
                try:
                    client = get_llm_client(fallback_config)
                    result = await client.generate(
                        prompt=prompt,
                        model=fallback_config.model,
                        temperature=fallback_config.temperature,
                        max_tokens=fallback_config.max_tokens,
                        stop_sequences=fallback_config.stop_sequences,
                        **kwargs
                    )
                    
                    logger.info(
                        f"Fallback LLM request succeeded",
                        extra={
                            "provider": fallback_config.provider,
                            "model": fallback_config.model,
                        },
                    )
                    
                    return LLMResponse(
                        content=result.get("text", ""),
                        model=result.get("model", fallback_config.model),
                        usage=result.get("usage", {}),
                        finish_reason=result.get("finish_reason")
                    )
                except LLMError as e:
                    logger.warning(
                        f"Fallback LLM request failed: {str(e)}",
                        extra={
                            "error": str(e),
                            "provider": fallback_config.provider,
                            "model": fallback_config.model,
                        },
                    )
        
        # If all LLMs fail, raise the original error
        raise


async def generate_chat(
    messages: List[Union[LLMMessage, Dict[str, str]]],
    config: Optional[LLMConfig] = None,
    fallback_configs: Optional[List[LLMConfig]] = None,
    **kwargs
) -> LLMResponse:
    """
    Generate a chat response from an LLM with fallback support.
    
    Args:
        messages: The messages to send to the LLM
        config: Configuration for the primary LLM
        fallback_configs: Configurations for fallback LLMs
        **kwargs: Additional parameters for the LLM
        
    Returns:
        The LLM response
        
    Raises:
        LLMError: If all LLM requests fail
    """
    if config is None:
        config = LLMConfig()
    
    # Convert messages to the expected format
    formatted_messages = []
    for message in messages:
        if isinstance(message, LLMMessage):
            formatted_messages.append(
                {"role": message.role, "content": message.content}
            )
        else:
            formatted_messages.append(message)
    
    # Try the primary LLM
    try:
        client = get_llm_client(config)
        result = await client.chat(
            messages=formatted_messages,
            model=config.model,
            temperature=config.temperature,
            max_tokens=config.max_tokens,
            stop_sequences=config.stop_sequences,
            **kwargs
        )
        
        return LLMResponse(
            content=result.get("text", ""),
            model=result.get("model", config.model),
            usage=result.get("usage", {}),
            finish_reason=result.get("finish_reason")
        )
    except LLMError as e:
        logger.warning(
            f"Primary LLM request failed: {str(e)}",
            extra={"error": str(e), "provider": config.provider, "model": config.model}
        )
        
        # Try fallback LLMs if available
        if fallback_configs:
            for fallback_config in fallback_configs:
                try:
                    client = get_llm_client(fallback_config)
                    result = await client.chat(
                        messages=formatted_messages,
                        model=fallback_config.model,
                        temperature=fallback_config.temperature,
                        max_tokens=fallback_config.max_tokens,
                        stop_sequences=fallback_config.stop_sequences,
                        **kwargs
                    )
                    
                    logger.info(
                        f"Fallback LLM request succeeded",
                        extra={
                            "provider": fallback_config.provider,
                            "model": fallback_config.model,
                        },
                    )
                    
                    return LLMResponse(
                        content=result.get("text", ""),
                        model=result.get("model", fallback_config.model),
                        usage=result.get("usage", {}),
                        finish_reason=result.get("finish_reason")
                    )
                except LLMError as e:
                    logger.warning(
                        f"Fallback LLM request failed: {str(e)}",
                        extra={
                            "error": str(e),
                            "provider": fallback_config.provider,
                            "model": fallback_config.model,
                        },
                    )
        
        # If all LLMs fail, raise the original error
        raise
