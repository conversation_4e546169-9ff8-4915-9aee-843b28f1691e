"""
LLM Factory

This module provides a factory for creating LLM clients based on configuration.
"""

import logging
from typing import Dict, Optional, Type, Union

from .base import Base<PERSON><PERSON>lient
from .config import LLMConfig, LLMProvider
from .admin.registry import get_llm_registry

# Set up logging
logger = logging.getLogger(__name__)


class LLMFactory:
    """
    Factory for creating LLM clients.

    This class provides methods for creating LLM clients based on configuration,
    with support for caching and fallback mechanisms.
    """

    _instance = None
    _clients: Dict[str, BaseLLMClient] = {}

    def __new__(cls):
        """Create a singleton instance of the factory."""
        if cls._instance is None:
            cls._instance = super(LLMFactory, cls).__new__(cls)
            cls._instance._clients = {}
        return cls._instance

    @classmethod
    def get_client(
        cls,
        config: Optional[LLMConfig] = None,
        force_new: bool = False
    ) -> BaseLLMClient:
        """
        Get an LLM client for the specified configuration.

        Args:
            config: Configuration for the LLM client
            force_new: Whether to force creation of a new client

        Returns:
            An LLM client

        Raises:
            ValueError: If the provider is not supported
        """
        if config is None:
            config = LLMConfig()

        # Check if we should use the registry for default model
        if not config.model or config.model == "default":
            # Get the registry
            registry = get_llm_registry()

            # Get the provider as string
            provider_str = config.provider
            if isinstance(provider_str, LLMProvider):
                provider_str = provider_str.value

            # Get the default model for the provider
            default_model = registry.get_default_model(provider_str)

            # Update the config with the default model if found
            if default_model:
                logger.debug(
                    f"Using default model {default_model} for provider {provider_str}"
                )
                config.model = default_model

        # Create a cache key from the configuration
        cache_key = cls._get_cache_key(config)

        # Return cached client if available and not forcing new
        if not force_new and cache_key in cls._instance._clients:
            return cls._instance._clients[cache_key]

        # Create a new client
        client = cls._create_client(config)

        # Cache the client
        cls._instance._clients[cache_key] = client

        return client

    @classmethod
    def _get_cache_key(cls, config: LLMConfig) -> str:
        """
        Get a cache key for the configuration.

        Args:
            config: Configuration for the LLM client

        Returns:
            A cache key
        """
        provider = config.provider
        if isinstance(provider, LLMProvider):
            provider = provider.value

        return f"{provider}:{config.model}:{config.api_base_url or 'default'}"

    @classmethod
    def _create_client(cls, config: LLMConfig) -> BaseLLMClient:
        """
        Create an LLM client for the specified configuration.

        Args:
            config: Configuration for the LLM client

        Returns:
            An LLM client

        Raises:
            ValueError: If the provider is not supported
        """
        provider = config.provider
        if isinstance(provider, str):
            try:
                provider = LLMProvider(provider)
            except ValueError:
                raise ValueError(f"Unsupported provider: {provider}")

        # Import provider implementations
        from .providers import (
            OpenAIClient,
            AnthropicClient,
            GeminiClient,
            GroqClient,
            MockClient
        )

        # Map providers to client classes
        provider_map: Dict[LLMProvider, Type[BaseLLMClient]] = {
            LLMProvider.OPENAI: OpenAIClient,
            LLMProvider.ANTHROPIC: AnthropicClient,
            LLMProvider.GEMINI: GeminiClient,
            LLMProvider.GROQ: GroqClient,
            LLMProvider.MOCK: MockClient
        }

        # Get the client class for the provider
        client_class = provider_map.get(provider)
        if client_class is None:
            raise ValueError(f"Unsupported provider: {provider}")

        # Create and return the client
        return client_class(
            api_key=config.api_key,
            max_retries=config.max_retries,
            retry_delay=config.retry_delay,
            retry_backoff=config.retry_backoff,
            timeout=config.timeout
        )


# Singleton getter
def get_llm_client(
    config: Optional[LLMConfig] = None,
    force_new: bool = False
) -> BaseLLMClient:
    """
    Get an LLM client for the specified configuration.

    Args:
        config: Configuration for the LLM client
        force_new: Whether to force creation of a new client

    Returns:
        An LLM client

    Raises:
        ValueError: If the provider is not supported
    """
    factory = LLMFactory()
    return factory.get_client(config, force_new)
