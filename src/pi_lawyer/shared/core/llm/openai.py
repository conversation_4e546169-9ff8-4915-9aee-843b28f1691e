"""
OpenAI LLM Client

This module provides a client for the OpenAI API with retry logic, logging,
and error handling.
"""

import logging
import os
from typing import Any, Dict, List, Optional, Union

from openai import AsyncOpenAI, APIError, RateLimitError as OpenAIRateLimitError
from openai.types.chat import ChatCompletion

from .base import (
    BaseLLMClient,
    RateLimitError,
    AuthenticationError,
    ServerError,
    ClientError,
)

# Set up logging
logger = logging.getLogger(__name__)


class OpenAIClient(BaseLLMClient):
    """
    Client for the OpenAI API.
    
    This class provides methods for generating text and chat completions
    using the OpenAI API.
    """
    
    def __init__(
        self,
        api_key: Optional[str] = None,
        organization: Optional[str] = None,
        max_retries: int = 3,
        retry_delay: float = 1.0,
        retry_backoff: float = 2.0,
        timeout: float = 60.0,
        logger: Optional[logging.Logger] = None
    ):
        """
        Initialize the OpenAI client.
        
        Args:
            api_key: The OpenAI API key
            organization: The OpenAI organization ID
            max_retries: Maximum number of retries for failed requests
            retry_delay: Initial delay between retries in seconds
            retry_backoff: Backoff factor for retry delay
            timeout: Request timeout in seconds
            logger: Logger instance
        """
        super().__init__(
            api_key=api_key,
            max_retries=max_retries,
            retry_delay=retry_delay,
            retry_backoff=retry_backoff,
            timeout=timeout,
            logger=logger
        )
        
        self.api_key = api_key or os.environ.get("OPENAI_API_KEY")
        if not self.api_key:
            raise ValueError("OpenAI API key is required")
        
        self.organization = organization or os.environ.get("OPENAI_ORGANIZATION")
        
        self.client = AsyncOpenAI(
            api_key=self.api_key,
            organization=self.organization,
            timeout=timeout
        )
    
    async def generate(
        self,
        prompt: str,
        model: str = "gpt-4-turbo",
        temperature: float = 0.7,
        max_tokens: Optional[int] = None,
        stop_sequences: Optional[List[str]] = None,
        **kwargs
    ) -> Dict[str, Any]:
        """
        Generate text from the OpenAI API.
        
        Args:
            prompt: The prompt to send to the API
            model: The model to use
            temperature: The temperature to use for generation
            max_tokens: The maximum number of tokens to generate
            stop_sequences: Sequences that stop generation
            **kwargs: Additional OpenAI-specific parameters
            
        Returns:
            A dictionary containing the generated text and metadata
        """
        messages = [{"role": "user", "content": prompt}]
        return await self.chat(
            messages=messages,
            model=model,
            temperature=temperature,
            max_tokens=max_tokens,
            stop_sequences=stop_sequences,
            **kwargs
        )
    
    async def chat(
        self,
        messages: List[Dict[str, str]],
        model: str = "gpt-4-turbo",
        temperature: float = 0.7,
        max_tokens: Optional[int] = None,
        stop_sequences: Optional[List[str]] = None,
        stream: bool = False,
        **kwargs
    ) -> Dict[str, Any]:
        """
        Generate a chat response from the OpenAI API.
        
        Args:
            messages: The messages to send to the API
            model: The model to use
            temperature: The temperature to use for generation
            max_tokens: The maximum number of tokens to generate
            stop_sequences: Sequences that stop generation
            stream: Whether to stream the response
            **kwargs: Additional OpenAI-specific parameters
            
        Returns:
            A dictionary containing the generated response and metadata
        """
        async def _chat_request():
            try:
                response = await self.client.chat.completions.create(
                    model=model,
                    messages=messages,
                    temperature=temperature,
                    max_tokens=max_tokens,
                    stop=stop_sequences,
                    stream=stream,
                    **kwargs
                )
                
                if stream:
                    return {"response": response, "stream": True}
                
                return {
                    "response": response,
                    "content": response.choices[0].message.content,
                    "model": model,
                    "usage": {
                        "prompt_tokens": response.usage.prompt_tokens,
                        "completion_tokens": response.usage.completion_tokens,
                        "total_tokens": response.usage.total_tokens
                    }
                }
            
            except OpenAIRateLimitError as e:
                raise RateLimitError(f"OpenAI rate limit exceeded: {str(e)}")
            
            except APIError as e:
                if e.status_code == 401:
                    raise AuthenticationError(f"OpenAI authentication error: {str(e)}")
                elif e.status_code >= 500:
                    raise ServerError(f"OpenAI server error: {str(e)}")
                else:
                    raise ClientError(f"OpenAI client error: {str(e)}")
            
            except Exception as e:
                raise ClientError(f"Unexpected error: {str(e)}")
        
        return await self.with_retries(_chat_request)


# Singleton instance for the OpenAI client
_openai_client: Optional[OpenAIClient] = None


def get_openai_client() -> OpenAIClient:
    """
    Get the singleton instance of the OpenAI client.
    
    Returns:
        The OpenAI client instance
    """
    global _openai_client
    
    if _openai_client is None:
        api_key = os.environ.get("OPENAI_API_KEY")
        organization = os.environ.get("OPENAI_ORGANIZATION")
        
        _openai_client = OpenAIClient(
            api_key=api_key,
            organization=organization
        )
    
    return _openai_client
