"""
Anthropic LLM Client

This module provides a client for the Anthropic API with retry logic, logging,
and error handling.
"""

import logging
import os
from typing import Any, Dict, List, Optional, Union

from anthropic import (
    AsyncAnthropic,
    APIError,
    RateLimitError as AnthropicRateLimitError,
)

from ..base import (
    BaseLLMClient,
    RateLimitError,
    AuthenticationError,
    ServerError,
    ClientError,
)

# Set up logging
logger = logging.getLogger(__name__)


class AnthropicClient(BaseLLMClient):
    """
    Client for the Anthropic API.
    
    This class provides methods for generating text and chat completions
    using the Anthropic API.
    """
    
    def __init__(
        self,
        api_key: Optional[str] = None,
        api_base: Optional[str] = None,
        max_retries: int = 3,
        retry_delay: float = 1.0,
        retry_backoff: float = 2.0,
        timeout: float = 60.0,
        logger: Optional[logging.Logger] = None
    ):
        """
        Initialize the Anthropic client.
        
        Args:
            api_key: The API key for the Anthropic API
            api_base: The base URL for the Anthropic API
            max_retries: Maximum number of retries for failed requests
            retry_delay: Initial delay between retries in seconds
            retry_backoff: Backoff factor for retry delay
            timeout: Request timeout in seconds
            logger: Logger instance
        """
        super().__init__(
            api_key=api_key,
            max_retries=max_retries,
            retry_delay=retry_delay,
            retry_backoff=retry_backoff,
            timeout=timeout,
            logger=logger
        )
        
        # Use environment variable if API key is not provided
        if self.api_key is None:
            self.api_key = os.environ.get("ANTHROPIC_API_KEY")
            if self.api_key is None:
                raise ValueError("Anthropic API key not found")
        
        # Initialize the Anthropic client
        self.client = AsyncAnthropic(
            api_key=self.api_key,
            base_url=api_base,
            timeout=self.timeout
        )
    
    async def generate(
        self,
        prompt: str,
        model: str,
        temperature: float = 0.7,
        max_tokens: Optional[int] = None,
        stop_sequences: Optional[List[str]] = None,
        **kwargs
    ) -> Dict[str, Any]:
        """
        Generate text from the Anthropic API.
        
        Args:
            prompt: The prompt to send to the API
            model: The model to use
            temperature: The temperature to use for generation
            max_tokens: The maximum number of tokens to generate
            stop_sequences: Sequences that stop generation
            **kwargs: Additional parameters for the API
            
        Returns:
            A dictionary containing the generated text and metadata
            
        Raises:
            LLMError: If the API request fails
        """
        # Convert the prompt to a message
        messages = [{"role": "user", "content": prompt}]
        
        # Generate a chat completion
        return await self.chat(
            messages=messages,
            model=model,
            temperature=temperature,
            max_tokens=max_tokens,
            stop_sequences=stop_sequences,
            **kwargs
        )
    
    async def chat(
        self,
        messages: List[Dict[str, str]],
        model: str,
        temperature: float = 0.7,
        max_tokens: Optional[int] = None,
        stop_sequences: Optional[List[str]] = None,
        **kwargs
    ) -> Dict[str, Any]:
        """
        Generate a chat response from the Anthropic API.
        
        Args:
            messages: The messages to send to the API
            model: The model to use
            temperature: The temperature to use for generation
            max_tokens: The maximum number of tokens to generate
            stop_sequences: Sequences that stop generation
            **kwargs: Additional parameters for the API
            
        Returns:
            A dictionary containing the generated response and metadata
            
        Raises:
            LLMError: If the API request fails
        """
        # Convert messages to Anthropic format
        anthropic_messages = []
        
        for message in messages:
            role = message["role"]
            content = message["content"]
            
            # Map roles to Anthropic roles
            if role == "system":
                # System messages are handled separately in Anthropic
                continue
            elif role == "user":
                anthropic_role = "user"
            elif role == "assistant":
                anthropic_role = "assistant"
            else:
                # Skip unknown roles
                continue
            
            anthropic_messages.append({
                "role": anthropic_role,
                "content": content
            })
        
        # Extract system message if present
        system_message = next(
            (message["content"] for message in messages if message["role"] == "system"),
            None
        )
        
        # Prepare the request parameters
        params = {
            "model": model,
            "messages": anthropic_messages,
            "temperature": temperature,
            **kwargs
        }
        
        # Add optional parameters
        if system_message is not None:
            params["system"] = system_message
        
        if max_tokens is not None:
            params["max_tokens"] = max_tokens
        
        if stop_sequences is not None:
            params["stop_sequences"] = stop_sequences
        
        # Make the API request with retries
        async def _make_request():
            try:
                response = await self.client.messages.create(**params)
                
                # Extract the response text and metadata
                result = {
                    "text": response.content[0].text,
                    "model": response.model,
                    "usage": {
                        "input_tokens": response.usage.input_tokens,
                        "output_tokens": response.usage.output_tokens,
                        "total_tokens": response.usage.input_tokens
                        + response.usage.output_tokens,
                    },
                    "finish_reason": response.stop_reason
                }
                
                return result
            
            except AnthropicRateLimitError as e:
                raise RateLimitError(str(e)) from e
            
            except APIError as e:
                if e.status_code == 401:
                    raise AuthenticationError(str(e)) from e
                elif e.status_code >= 500:
                    raise ServerError(str(e)) from e
                else:
                    raise ClientError(str(e)) from e
            
            except Exception as e:
                raise ClientError(str(e)) from e
        
        return await self.with_retries(_make_request)


# Singleton instance
_anthropic_client = None


def get_anthropic_client(
    api_key: Optional[str] = None,
    api_base: Optional[str] = None,
    force_new: bool = False
) -> AnthropicClient:
    """
    Get a singleton instance of the Anthropic client.
    
    Args:
        api_key: The API key for the Anthropic API
        api_base: The base URL for the Anthropic API
        force_new: Whether to force creation of a new client
        
    Returns:
        An Anthropic client
    """
    global _anthropic_client
    
    if _anthropic_client is None or force_new:
        _anthropic_client = AnthropicClient(
            api_key=api_key,
            api_base=api_base
        )
    
    return _anthropic_client
