"""
LangGraph State Persistence

This module provides utilities for persisting LangGraph state to a database.
It includes functions for serializing and deserializing state objects,
as well as loading and saving state from/to a database.

Key Features:
- State serialization and deserialization
- Database integration for state persistence
- Tenant isolation for multi-tenant security
- Error handling and logging
- Support for different agent types

Usage:
    from pi_lawyer.state.persistence import (
        save_state,
        load_state,
        delete_state,
        list_states
    )

    # Save a state to the database
    await save_state(state)

    # Load a state from the database
    state = await load_state(thread_id="thread-123", tenant_id="tenant-456")

    # Delete a state from the database
    await delete_state(thread_id="thread-123", tenant_id="tenant-456")

    # List all states for a tenant
    states = await list_states(tenant_id="tenant-456")
"""

import json
import logging
import uuid
import time
import asyncio
from typing import Dict, List, Optional, Any, Union, Type, cast, Tuple
from datetime import datetime, timezone

from pydantic import BaseModel, Field, ValidationError
from langchain_core.messages import (
    BaseMessage,
    HumanMessage,
    AIMessage,
    SystemMessage,
    ToolMessage,
    FunctionMessage,
)
from langgraph.graph import StateGraph
from langgraph.checkpoint.base import BaseCheckpointSaver

from pi_lawyer.state.langgraph_state import (
    BaseAgentState,
    IntakeAgentState,
    ResearchAgentState,
    DocumentAgentState,
    DeadlineAgentState,
    EventAgentState,
    UserContext,
    AgentType,
    create_state,
    STATE_VERSION,
    BaseLangGraphState
)
from pi_lawyer.db.client import get_db_client

# Define the table name and schema for state storage
STATE_TABLE = "agent_states"
STATE_SCHEMA = "security"  # Using security schema for better protection

# Set up logging
logger = logging.getLogger(__name__)


class StateRecord(BaseModel):
    """Database record for a state object."""

    id: str = Field(default_factory=lambda: str(uuid.uuid4()))
    thread_id: str
    tenant_id: str
    user_id: str
    agent_type: AgentType
    state_data: Dict[str, Any]
    created_at: str = Field(
        default_factory=lambda: datetime.now(timezone.utc).isoformat()
    )
    updated_at: str = Field(
        default_factory=lambda: datetime.now(timezone.utc).isoformat()
    )


# Message serialization/deserialization functions
def serialize_message(message: BaseMessage) -> Dict[str, Any]:
    """Serialize a message to a dictionary."""
    result = {
        "type": message.__class__.__name__,
        "content": message.content,
        "additional_kwargs": message.additional_kwargs,
    }

    # Add message-specific fields
    if hasattr(message, "name") and message.name is not None:
        result["name"] = message.name

    if hasattr(message, "tool_call_id") and message.tool_call_id is not None:
        result["tool_call_id"] = message.tool_call_id

    return result


def deserialize_message(data: Dict[str, Any]) -> BaseMessage:
    """Deserialize a message from a dictionary."""
    message_type = data.pop("type")

    # Create the appropriate message type
    if message_type == "HumanMessage":
        return HumanMessage(**data)
    elif message_type == "AIMessage":
        return AIMessage(**data)
    elif message_type == "SystemMessage":
        return SystemMessage(**data)
    elif message_type == "ToolMessage":
        return ToolMessage(**data)
    elif message_type == "FunctionMessage":
        return FunctionMessage(**data)
    else:
        raise ValueError(f"Unknown message type: {message_type}")


# State serialization/deserialization functions
def serialize_state(state: BaseAgentState) -> Dict[str, Any]:
    """
    Serialize a state object to a dictionary for database storage.

    Args:
        state: The state object to serialize

    Returns:
        A dictionary representation of the state suitable for database storage

    Raises:
        ValueError: If the state cannot be serialized
    """
    try:
        # Convert the state to a dictionary
        state_dict = state.to_dict()

        # Serialize messages
        if "messages" in state_dict:
            state_dict["messages"] = [serialize_message(msg) for msg in state.messages]

        # Handle special types in memory
        if "memory" in state_dict:
            # This is a simplified approach - complex objects in memory might need
            # custom serialization
            state_dict["memory"] = json.loads(
                json.dumps(state_dict["memory"], default=str)
            )

        # Serialize user context
        if "user_context" in state_dict:
            state_dict["user_context"] = state.user_context.model_dump()

        # Handle agent-specific fields
        if isinstance(state, ResearchAgentState):
            # Handle Document objects in legal_documents and case_documents
            if hasattr(state, "legal_documents") and state.legal_documents:
                from langchain_core.documents import Document
                state_dict["legal_documents"] = [
                    {"page_content": doc.page_content, "metadata": doc.metadata}
                    for doc in state.legal_documents
                ]

            if hasattr(state, "case_documents") and state.case_documents:
                from langchain_core.documents import Document
                state_dict["case_documents"] = [
                    {"page_content": doc.page_content, "metadata": doc.metadata}
                    for doc in state.case_documents
                ]

            # Handle practice_areas set
            if hasattr(state, "practice_areas") and state.practice_areas:
                state_dict["practice_areas"] = list(state.practice_areas)

        # Add metadata for state versioning and migration
        state_dict["_metadata"] = {
            "version": state.version,
            "serialized_at": datetime.now(timezone.utc).isoformat(),
            "agent_type": state.agent_type
        }

        return state_dict

    except Exception as e:
        logger.error(f"Error serializing state: {e}")
        raise ValueError(f"Failed to serialize state: {str(e)}")

def serialize_typed_state(state: Dict[str, Any]) -> Dict[str, Any]:
    """
    Serialize a LangGraph typed state dictionary for database storage.

    Args:
        state: The LangGraph typed state dictionary to serialize

    Returns:
        A serialized dictionary suitable for database storage

    Raises:
        ValueError: If the state cannot be serialized
    """
    try:
        # Create a deep copy to avoid modifying the original
        serialized = json.loads(json.dumps(state, default=str))

        # Serialize messages if present
        if "messages" in serialized and isinstance(serialized["messages"], list):
            serialized["messages"] = [
                serialize_message(msg) if isinstance(msg, BaseMessage) else msg
                for msg in serialized["messages"]
            ]

        # Add metadata
        serialized["_metadata"] = {
            "version": serialized.get("version", STATE_VERSION),
            "serialized_at": datetime.now(timezone.utc).isoformat(),
            "is_typed_state": True
        }

        return serialized

    except Exception as e:
        logger.error(f"Error serializing typed state: {e}")
        raise ValueError(f"Failed to serialize typed state: {str(e)}")


def deserialize_state(
    agent_type: AgentType, state_data: Dict[str, Any]
) -> BaseAgentState:
    """
    Deserialize a state object from a dictionary.

    Args:
        agent_type: The type of agent state to create
        state_data: The serialized state data

    Returns:
        A deserialized state object

    Raises:
        ValueError: If the state cannot be deserialized
    """
    try:
        # Create a copy of the data to avoid modifying the original
        data = state_data.copy()

        # Remove metadata if present
        metadata = data.pop("_metadata", {})

        # Check version compatibility
        stored_version = metadata.get("version", "0.0.0")
        if stored_version != STATE_VERSION:
            logger.warning(
                f"State version mismatch: stored={stored_version}, "
                f"current={STATE_VERSION}"
            )
            # In a production system, you would implement version migration here

        # Deserialize messages
        if "messages" in data:
            data["messages"] = [deserialize_message(msg) for msg in data["messages"]]

        # Deserialize user context
        if "user_context" in data:
            user_context_data = data["user_context"]
            data["user_context"] = UserContext(**user_context_data)

        # Handle agent-specific fields
        if agent_type == "research":
            # Convert practice_areas back to a set
            if "practice_areas" in data and isinstance(data["practice_areas"], list):
                data["practice_areas"] = set(data["practice_areas"])

            # Handle Document objects
            from langchain_core.documents import Document
            if "legal_documents" in data:
                data["legal_documents"] = [
                    Document(page_content=doc.get("page_content", ""),
                             metadata=doc.get("metadata", {}))
                    for doc in data["legal_documents"]
                ]

            if "case_documents" in data:
                data["case_documents"] = [
                    Document(page_content=doc.get("page_content", ""),
                             metadata=doc.get("metadata", {}))
                    for doc in data["case_documents"]
                ]

        # Create the appropriate state object
        if agent_type == "intake":
            return IntakeAgentState(**data)
        elif agent_type == "research":
            return ResearchAgentState(**data)
        elif agent_type == "document":
            return DocumentAgentState(**data)
        elif agent_type == "deadline":
            return DeadlineAgentState(**data)
        elif agent_type == "event":
            return EventAgentState(**data)
        else:
            return BaseAgentState(**data)

    except Exception as e:
        logger.error(f"Error deserializing state: {e}")
        raise ValueError(f"Failed to deserialize state: {str(e)}")

def deserialize_typed_state(state_data: Dict[str, Any]) -> Dict[str, Any]:
    """
    Deserialize a typed state dictionary from database storage.

    Args:
        state_data: The serialized state data

    Returns:
        A deserialized typed state dictionary suitable for LangGraph

    Raises:
        ValueError: If the state cannot be deserialized
    """
    try:
        # Create a copy of the data to avoid modifying the original
        data = state_data.copy()

        # Remove metadata if present
        metadata = data.pop("_metadata", {})

        # Check if this is a typed state
        is_typed_state = metadata.get("is_typed_state", False)
        if not is_typed_state:
            logger.warning("Deserializing a state that was not marked as a typed state")

        # Deserialize messages if present
        if "messages" in data and isinstance(data["messages"], list):
            data["messages"] = [
                (
                    deserialize_message(msg)
                    if isinstance(msg, dict) and "type" in msg
                    else msg
                )
                for msg in data["messages"]
            ]

        return data

    except Exception as e:
        logger.error(f"Error deserializing typed state: {e}")
        raise ValueError(f"Failed to deserialize typed state: {str(e)}")


# LangGraph checkpoint saver implementation
class DatabaseCheckpointSaver(BaseCheckpointSaver):
    """
    LangGraph checkpoint saver that persists state to a database.

    This class implements the BaseCheckpointSaver interface from LangGraph,
    allowing LangGraph to persist state to our database.
    """

    def __init__(self, tenant_id: str):
        """
        Initialize the checkpoint saver.

        Args:
            tenant_id: The tenant ID for tenant isolation
        """
        self.tenant_id = tenant_id

    async def get(self, key: str) -> Optional[Dict[str, Any]]:
        """
        Get a state by key.

        Args:
            key: The state key (thread ID)

        Returns:
            The state dictionary, or None if not found
        """
        try:
            # Get the database client
            db = await get_db_client()

            # Query the database
            result = (
                await db.schema(STATE_SCHEMA)
                .table(STATE_TABLE)
                .select("*")
                .eq("thread_id", key)
                .eq("tenant_id", self.tenant_id)
                .single()
            )

            if not result:
                return None

            # Deserialize the state
            record = StateRecord(**result)
            return deserialize_typed_state(record.state_data)

        except Exception as e:
            logger.error(f"Error getting state from checkpoint: {e}")
            return None

    async def put(self, key: str, state: Dict[str, Any]) -> None:
        """
        Save a state by key.

        Args:
            key: The state key (thread ID)
            state: The state dictionary
        """
        try:
            # Get the database client
            db = await get_db_client()

            # Serialize the state
            state_data = serialize_typed_state(state)

            # Extract agent type from state or metadata
            agent_type = state.get("agent_type", "supervisor")
            if "_metadata" in state_data and "agent_type" in state_data["_metadata"]:
                agent_type = state_data["_metadata"]["agent_type"]

            # Extract user ID from state
            user_id = state.get("user_id", "unknown")

            # Create a state record
            record = StateRecord(
                thread_id=key,
                tenant_id=self.tenant_id,
                user_id=user_id,
                agent_type=agent_type,
                state_data=state_data,
                updated_at=datetime.now(timezone.utc).isoformat()
            )

            # Check if the state already exists
            existing_record = (
                await db.schema(STATE_SCHEMA)
                .table(STATE_TABLE)
                .select("id")
                .eq("thread_id", key)
                .eq("tenant_id", self.tenant_id)
                .single()
            )

            if existing_record:
                # Update the existing record
                record_id = existing_record["id"]
                await db.schema(STATE_SCHEMA).table(STATE_TABLE).update({
                    "state_data": state_data,
                    "agent_type": agent_type,
                    "updated_at": record.updated_at
                }).eq("id", record_id).execute()
            else:
                # Insert a new record
                await (
                    db.schema(STATE_SCHEMA)
                    .table(STATE_TABLE)
                    .insert(record.model_dump())
                    .execute()
                )

        except Exception as e:
            logger.error(f"Error saving state to checkpoint: {e}")
            raise

    async def delete(self, key: str) -> None:
        """
        Delete a state by key.

        Args:
            key: The state key (thread ID)
        """
        try:
            # Get the database client
            db = await get_db_client()

            # Delete the record
            await (
                db.schema(STATE_SCHEMA)
                .table(STATE_TABLE)
                .delete()
                .eq("thread_id", key)
                .eq("tenant_id", self.tenant_id)
                .execute()
            )

        except Exception as e:
            logger.error(f"Error deleting state from checkpoint: {e}")
            raise

# Database operations
async def save_state(state: BaseAgentState) -> str:
    """
    Save a state object to the database.

    Args:
        state: The state object to save

    Returns:
        The ID of the saved state record

    Raises:
        ValueError: If the state cannot be saved
    """
    start_time = time.time()
    try:
        # Get the database client
        db = await get_db_client()

        # Serialize the state
        state_data = serialize_state(state)

        # Create a state record
        record = StateRecord(
            thread_id=state.thread_id,
            tenant_id=state.tenant_id,
            user_id=state.user_id,
            agent_type=state.agent_type,
            state_data=state_data,
            updated_at=datetime.now(timezone.utc).isoformat()
        )

        # Check if the state already exists
        existing_record = (
            await db.schema(STATE_SCHEMA)
            .table(STATE_TABLE)
            .select("id")
            .eq("thread_id", state.thread_id)
            .eq("tenant_id", state.tenant_id)
            .single()
        )

        if existing_record:
            # Update the existing record
            record_id = existing_record["id"]
            await db.schema(STATE_SCHEMA).table(STATE_TABLE).update({
                "state_data": state_data,
                "agent_type": state.agent_type,
                "updated_at": record.updated_at
            }).eq("id", record_id).execute()
        else:
            # Insert a new record
            result = (
                await db.schema(STATE_SCHEMA)
                .table(STATE_TABLE)
                .insert(record.model_dump())
                .execute()
            )
            record_id = result.data[0]["id"]

        # Log performance metrics
        elapsed_time = time.time() - start_time
        logger.debug(
            f"State saved in {elapsed_time:.3f}s "
            f"(thread_id={state.thread_id}, tenant_id={state.tenant_id})"
        )

        return record_id

    except Exception as e:
        logger.error(f"Error saving state: {e}")
        raise ValueError(f"Failed to save state: {str(e)}")


async def load_state(thread_id: str, tenant_id: str) -> Optional[BaseAgentState]:
    """
    Load a state object from the database.

    Args:
        thread_id: The thread ID
        tenant_id: The tenant ID

    Returns:
        The state object, or None if not found

    Raises:
        ValueError: If the state cannot be loaded
    """
    start_time = time.time()
    try:
        # Get the database client
        db = await get_db_client()

        # Query the database
        result = (
            await db.schema(STATE_SCHEMA)
            .table(STATE_TABLE)
            .select("*")
            .eq("thread_id", thread_id)
            .eq("tenant_id", tenant_id)
            .single()
        )

        if not result:
            logger.debug(
                f"No state found for thread_id={thread_id}, tenant_id={tenant_id}"
            )
            return None

        # Deserialize the state
        record = StateRecord(**result)
        state = deserialize_state(record.agent_type, record.state_data)

        # Log performance metrics
        elapsed_time = time.time() - start_time
        logger.debug(
            f"State loaded in {elapsed_time:.3f}s "
            f"(thread_id={thread_id}, tenant_id={tenant_id})"
        )

        return state

    except Exception as e:
        logger.error(f"Error loading state: {e}")
        raise ValueError(f"Failed to load state: {str(e)}")


async def delete_state(thread_id: str, tenant_id: str) -> bool:
    """
    Delete a state object from the database.

    Args:
        thread_id: The thread ID
        tenant_id: The tenant ID

    Returns:
        True if the state was deleted, False otherwise

    Raises:
        ValueError: If the state cannot be deleted
    """
    try:
        # Get the database client
        db = await get_db_client()

        # Delete the record
        result = (
            await db.schema(STATE_SCHEMA)
            .table(STATE_TABLE)
            .delete()
            .eq("thread_id", thread_id)
            .eq("tenant_id", tenant_id)
            .execute()
        )

        deleted = len(result.data) > 0
        if deleted:
            logger.debug(f"State deleted: thread_id={thread_id}, tenant_id={tenant_id}")
        else:
            logger.debug(
                f"No state found to delete: thread_id={thread_id}, "
                f"tenant_id={tenant_id}"
            )

        return deleted

    except Exception as e:
        logger.error(f"Error deleting state: {e}")
        raise ValueError(f"Failed to delete state: {str(e)}")


async def list_states(
    tenant_id: str,
    agent_type: Optional[AgentType] = None,
    limit: int = 100,
    offset: int = 0,
) -> List[Dict[str, Any]]:
    """
    List all state records for a tenant.

    Args:
        tenant_id: The tenant ID
        agent_type: Optional agent type to filter by
        limit: Maximum number of records to return
        offset: Number of records to skip

    Returns:
        A list of state records

    Raises:
        ValueError: If the states cannot be listed
    """
    try:
        # Get the database client
        db = await get_db_client()

        # Build the query
        query = db.schema(STATE_SCHEMA).table(STATE_TABLE).select(
            "thread_id, agent_type, user_id, created_at, updated_at"
        ).eq("tenant_id", tenant_id)

        if agent_type:
            query = query.eq("agent_type", agent_type)

        # Add pagination
        query = query.order("updated_at", desc=True).limit(limit).offset(offset)

        # Execute the query
        result = await query.execute()

        logger.debug(f"Listed {len(result.data)} states for tenant_id={tenant_id}")
        return result.data

    except Exception as e:
        logger.error(f"Error listing states: {e}")
        raise ValueError(f"Failed to list states: {str(e)}")


async def get_state_count(
    tenant_id: str, agent_type: Optional[AgentType] = None
) -> int:
    """
    Get the count of state records for a tenant.

    Args:
        tenant_id: The tenant ID
        agent_type: Optional agent type to filter by

    Returns:
        The number of state records

    Raises:
        ValueError: If the count cannot be retrieved
    """
    try:
        # Get the database client
        db = await get_db_client()

        # Build the query
        query = db.schema(STATE_SCHEMA).table(STATE_TABLE).select(
            "count", count="exact"
        ).eq("tenant_id", tenant_id)

        if agent_type:
            query = query.eq("agent_type", agent_type)

        # Execute the query
        result = await query.execute()

        return result.count if hasattr(result, "count") else 0

    except Exception as e:
        logger.error(f"Error getting state count: {e}")
        raise ValueError(f"Failed to get state count: {str(e)}")


async def verify_state_table_access() -> bool:
    """
    Verify that the application has access to the state table.

    This function checks if the state table exists and is accessible.
    It should be called during application startup to verify database connectivity.

    Returns:
        bool: True if the table is accessible, False otherwise
    """
    try:
        # Get the database client
        db = await get_db_client()

        # Check if the table exists and is accessible
        result = await (
            db.schema(STATE_SCHEMA)
            .table(STATE_TABLE)
            .select("count", count="exact")
            .limit(1)
            .execute()
        )

        # Log the result
        logger.info(
            f"State table verification: {STATE_SCHEMA}.{STATE_TABLE} is accessible"
        )
        return True

    except Exception as e:
        logger.error(f"Error accessing state table: {e}")
        return False
