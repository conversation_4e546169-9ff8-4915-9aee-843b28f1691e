"""
Utility functions for LangGraph state persistence.

This module provides utility functions for managing LangGraph state persistence.
"""

import logging
import asyncio
from typing import Dict, Any, Optional, List, Tuple
from datetime import datetime, timezone, timedelta

from pi_lawyer.state.persistence import (
    verify_state_table_access,
    list_states,
    delete_state,
    get_state_count,
    STATE_SCHEMA,
    STATE_TABLE
)
from pi_lawyer.db.client import get_db_client

# Set up logging
logger = logging.getLogger(__name__)


async def verify_state_persistence() -> bool:
    """
    Verify that the state persistence layer is accessible.

    This function should be called when the application starts to verify
    that the database tables are accessible.

    Returns:
        bool: True if the state persistence layer is accessible, False otherwise
    """
    try:
        # Verify that the state table is accessible
        is_accessible = await verify_state_table_access()
        if is_accessible:
            logger.info("State persistence layer verification successful")
            return True
        else:
            logger.error(
                "State persistence layer verification failed: table not accessible"
            )
            return False
    except Exception as e:
        logger.error(f"Failed to verify state persistence: {e}")
        return False


async def cleanup_expired_states(
    tenant_id: str, max_age_days: int = 30, dry_run: bool = False
) -> int:
    """
    Clean up expired states for a tenant.

    Args:
        tenant_id: The tenant ID
        max_age_days: Maximum age of states in days
        dry_run: If True, only count the states that would be deleted without
            actually deleting them

    Returns:
        The number of states deleted or that would be deleted in dry run mode

    Raises:
        ValueError: If cleanup fails
    """
    try:
        # Get the database client
        db = await get_db_client()

        # Calculate the cutoff date
        cutoff_date = (
            datetime.now(timezone.utc) - timedelta(days=max_age_days)
        ).isoformat()

        if dry_run:
            # Count the states that would be deleted
            result = await db.schema(STATE_SCHEMA).table(STATE_TABLE).select(
                "count", count="exact"
            ).eq("tenant_id", tenant_id).lt("updated_at", cutoff_date).execute()

            deleted_count = result.count if hasattr(result, "count") else 0
            logger.info(
                f"Dry run: Would delete {deleted_count} expired states for "
                f"tenant_id={tenant_id}"
            )
        else:
            # Delete expired states
            result = await db.schema(STATE_SCHEMA).table(STATE_TABLE).delete().eq(
                "tenant_id", tenant_id
            ).lt("updated_at", cutoff_date).execute()

            deleted_count = len(result.data) if hasattr(result, "data") else 0
            logger.info(
                f"Deleted {deleted_count} expired states for tenant_id={tenant_id}"
            )

        return deleted_count

    except Exception as e:
        logger.error(f"Failed to clean up expired states: {e}")
        raise ValueError(f"Failed to clean up expired states: {str(e)}")


async def get_state_statistics(tenant_id: str) -> Dict[str, Any]:
    """
    Get statistics about states for a tenant.

    Args:
        tenant_id: The tenant ID

    Returns:
        A dictionary with statistics

    Raises:
        ValueError: If statistics cannot be retrieved
    """
    try:
        # Get the database client
        db = await get_db_client()

        # Get total count
        total_count = await get_state_count(tenant_id)

        # Get counts by agent type
        agent_types = [
            "intake",
            "research",
            "document",
            "deadline",
            "event",
            "supervisor",
        ]
        agent_counts = {}

        for agent_type in agent_types:
            count = await get_state_count(tenant_id, agent_type)
            agent_counts[agent_type] = count

        # Get the most recent states
        recent_states = await list_states(tenant_id, limit=5)

        # Get the oldest states
        oldest_query = db.schema(STATE_SCHEMA).table(STATE_TABLE).select(
            "thread_id, agent_type, user_id, created_at, updated_at"
        ).eq("tenant_id", tenant_id).order("updated_at", desc=False).limit(5)

        oldest_result = await oldest_query.execute()
        oldest_states = oldest_result.data if hasattr(oldest_result, "data") else []

        # Calculate average state age
        age_query = db.schema(STATE_SCHEMA).table(STATE_TABLE).select(
            "updated_at"
        ).eq("tenant_id", tenant_id).execute()

        age_result = await age_query

        now = datetime.now(timezone.utc)
        total_age = 0
        count = 0

        for state in age_result.data if hasattr(age_result, "data") else []:
            updated_at = datetime.fromisoformat(state["updated_at"])
            age = (now - updated_at).total_seconds()
            total_age += age
            count += 1

        avg_age = total_age / count if count > 0 else 0

        # Return statistics
        return {
            "total_count": total_count,
            "agent_counts": agent_counts,
            "recent_states": recent_states,
            "oldest_states": oldest_states,
            "average_age_seconds": avg_age,
            "average_age_days": avg_age / (24 * 60 * 60) if avg_age > 0 else 0,
        }

    except Exception as e:
        logger.error(f"Failed to get state statistics: {e}")
        raise ValueError(f"Failed to get state statistics: {str(e)}")


async def export_states(
    tenant_id: str, agent_type: Optional[str] = None
) -> List[Dict[str, Any]]:
    """
    Export states for a tenant.

    Args:
        tenant_id: The tenant ID
        agent_type: Optional agent type to filter by

    Returns:
        A list of state records

    Raises:
        ValueError: If export fails
    """
    try:
        # Get the database client
        db = await get_db_client()

        # Build the query
        query = (
            db.schema(STATE_SCHEMA)
            .table(STATE_TABLE)
            .select("*")
            .eq("tenant_id", tenant_id)
        )

        if agent_type:
            query = query.eq("agent_type", agent_type)

        # Execute the query
        result = await query.execute()

        return result.data if hasattr(result, "data") else []

    except Exception as e:
        logger.error(f"Failed to export states: {e}")
        raise ValueError(f"Failed to export states: {str(e)}")


async def import_states(tenant_id: str, states: List[Dict[str, Any]]) -> int:
    """
    Import states for a tenant.

    Args:
        tenant_id: The tenant ID
        states: A list of state records

    Returns:
        The number of states imported

    Raises:
        ValueError: If import fails
    """
    try:
        # Get the database client
        db = await get_db_client()

        # Import each state
        imported_count = 0

        for state in states:
            # Ensure the tenant ID is correct
            state["tenant_id"] = tenant_id

            # Insert the state
            await db.schema(STATE_SCHEMA).table(STATE_TABLE).insert(state).execute()
            imported_count += 1

        logger.info(f"Imported {imported_count} states for tenant_id={tenant_id}")
        return imported_count

    except Exception as e:
        logger.error(f"Failed to import states: {e}")
        raise ValueError(f"Failed to import states: {str(e)}")
