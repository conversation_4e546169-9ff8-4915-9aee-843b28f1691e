"""
LangGraph State Schema

This module defines the state schema for LangGraph agents in the PI Lawyer AI system.
It provides a comprehensive set of Pydantic models for agent state management,
including serialization, deserialization, and validation.

Key Features:
- Base state model that all agent states inherit from
- Support for LangGraph's state management system
- Tenant isolation for multi-tenant security
- User context for authorization and personalization
- Support for different agent types (intake, research, document, deadline, event)
- Serialization/deserialization for state persistence
- Comprehensive validation and error handling
- State versioning and migration support
- State transition utilities
- Thread management and isolation

Usage:
    from pi_lawyer.state.langgraph_state import (
        BaseLangGraphState,
        IntakeAgentState,
        ResearchAgentState,
        DocumentAgentState,
        DeadlineAgentState,
        EventAgentState,
        create_state,
        create_typed_state
    )

    # Create a new state for a research agent
    state = create_state(
        agent_type="research",
        tenant_id="tenant-123",
        user_id="user-456",
        thread_id="thread-789",
        question="What is the statute of limitations for personal injury in Texas?"
    )

    # Create a typed state for use with LangGraph
    typed_state = create_typed_state(state)

    # Use the state with LangGraph
    from langgraph.graph import StateGraph
    workflow = StateGraph(BaseLangGraphState)
    workflow.add_node("research", research_node)
    workflow.set_entry_point("research")
"""

import json
import uuid
import logging
import copy
from typing import (
    Dict,
    List,
    Optional,
    Any,
    Union,
    Literal,
    Set,
    TypedDict,
    Type,
    cast,
    Callable,
    Generic,
    TypeVar,
)
from datetime import datetime, timezone
from enum import Enum
from pydantic import BaseModel, Field, field_validator, model_validator, ConfigDict

from typing_extensions import Annotated, get_type_hints, get_origin, get_args
from langchain_core.messages import (
    BaseMessage,
    HumanMessage,
    AIMessage,
    SystemMessage,
    ToolMessage,
    FunctionMessage,
)
from langchain_core.documents import Document
from langgraph.graph import StateGraph
from langgraph.checkpoint.base import BaseCheckpointSaver

# Import calendar-related models
from backend.agents.interactive.calendar_crud.providers.models import CalendarEvent

# Import task-related models
from backend.agents.interactive.task_crud.models import Task as TaskModel

# Set up logging
logger = logging.getLogger(__name__)

# Define common types
AgentType = Literal[
    "intake", "research", "document", "deadline", "event", "supervisor", "echo", "test"
]
UserRole = Literal[
    "partner", "attorney", "paralegal", "staff", "client", "admin"
]
QueryType = Literal["public", "private", "hybrid"]
PracticeArea = Literal[
    "personal_injury", "criminal_defense", "family_law", "estate_planning"
]


class CalendarIntentEnum(str, Enum):
    """Enum representing calendar operation intents."""

    CREATE_EVENT = "create_event"
    READ_EVENT = "read_event"
    UPDATE_EVENT = "update_event"
    DELETE_EVENT = "delete_event"
    LIST_EVENTS = "list_events"
    CHECK_AVAILABILITY = "check_availability"
    SCHEDULE_MEETING = "schedule_meeting"
    CANCEL_MEETING = "cancel_meeting"
    RESCHEDULE_MEETING = "reschedule_meeting"
    GET_FREE_BUSY = "get_free_busy"


class TaskIntentEnum(str, Enum):
    """Enum representing task operation intents."""

    CREATE_TASK = "create_task"
    READ_TASK = "read_task"
    UPDATE_TASK = "update_task"
    DELETE_TASK = "delete_task"
    LIST_TASKS = "list_tasks"

# Define state version for migration support
STATE_VERSION = "1.0.0"

# Type variable for generic state operations
T = TypeVar('T', bound='BaseAgentState')


class UserContext(BaseModel):
    """User context from authenticated session."""

    user_id: str = Field(..., description="The user ID")
    tenant_id: str = Field(..., description="The tenant ID")
    role: UserRole = Field(..., description="The user's role")
    assigned_case_ids: List[str] = Field(
        default_factory=list, description="Cases this user can access"
    )
    settings: Dict[str, Any] = Field(default_factory=dict, description="User settings")

    def can_access_case(self, case_id: str) -> bool:
        """Check if user can access a specific case."""
        if self.role in ["partner", "admin"]:
            return True
        return case_id in self.assigned_case_ids

    def can_access_sensitive_data(self) -> bool:
        """Check if user can access sensitive case details."""
        return self.role in ["partner", "attorney", "admin"]


class ClientInfo(BaseModel):
    """Client information model."""

    name: str = Field(..., description="Client name")
    contact: str = Field(..., description="Contact information")
    injury_description: str = Field(..., description="Description of the injury")
    incident_date: str = Field(..., description="Date of the incident")
    opposing_parties: List[str] = Field(
        default_factory=list, description="Opposing parties"
    )
    medical_providers: List[str] = Field(
        default_factory=list, description="Medical providers"
    )


class CaseInfo(BaseModel):
    """Case information model."""

    case_id: str = Field(..., description="Case ID")
    client_id: str = Field(..., description="Client ID")
    case_type: str = Field(..., description="Type of case")
    status: str = Field(..., description="Case status")
    deadlines: List[Dict[str, Any]] = Field(
        default_factory=list, description="Case deadlines"
    )
    documents: List[Dict[str, Any]] = Field(
        default_factory=list, description="Case documents"
    )


# Define reducer functions for LangGraph
def add_messages(
    existing: List[BaseMessage], new: Union[BaseMessage, List[BaseMessage]]
) -> List[BaseMessage]:
    """
    Reducer function for messages in LangGraph state.

    Args:
        existing: Existing messages
        new: New message(s) to add

    Returns:
        Updated list of messages
    """
    if isinstance(new, list):
        return existing + new
    return existing + [new]


def merge_dict(existing: Dict[str, Any], new: Dict[str, Any]) -> Dict[str, Any]:
    """
    Reducer function for dictionary fields in LangGraph state.

    Args:
        existing: Existing dictionary
        new: New dictionary to merge

    Returns:
        Updated dictionary
    """
    result = existing.copy()
    result.update(new)
    return result


def append_list(existing: List[Any], new: Union[Any, List[Any]]) -> List[Any]:
    """
    Reducer function for list fields in LangGraph state.

    Args:
        existing: Existing list
        new: New item(s) to append

    Returns:
        Updated list
    """
    if isinstance(new, list):
        return existing + new
    return existing + [new]


# Define the base state class for LangGraph
class BaseLangGraphState(TypedDict):
    """
    Base state for LangGraph agents.

    This is the foundation for all agent states in the system.
    It includes common fields like messages, user context, and metadata.

    This class is used as the state schema for LangGraph's StateGraph.
    It defines the structure of the state and the reducer functions for each field.
    """

    # Core fields required by LangGraph
    messages: Annotated[List[BaseMessage], add_messages]

    # Tenant and user information
    tenant_id: str
    user_id: str
    user_context: Dict[str, Any]

    # Thread and conversation information
    thread_id: str
    agent_type: AgentType

    # Memory and context
    memory: Annotated[Dict[str, Any], merge_dict]

    # Timestamps
    created_at: str
    updated_at: str

    # Version information for state migration
    version: str


# Define specialized state classes for each agent type
class IntakeLangGraphState(BaseLangGraphState):
    """State schema for the intake agent."""
    client: Dict[str, Any]
    case: Dict[str, Any]
    current_task: str
    task_status: str


class ResearchLangGraphState(BaseLangGraphState):
    """State schema for the research agent."""
    question: str
    jurisdiction: str
    practice_areas: List[str]
    queries: Annotated[List[str], append_list]
    query_type: str
    inferred_mode: str
    query_id: str
    case_id: str
    legal_documents: Annotated[List[Dict[str, Any]], append_list]
    case_documents: Annotated[List[Dict[str, Any]], append_list]


class DocumentLangGraphState(BaseLangGraphState):
    """State schema for the document agent."""
    document_id: str
    document_type: str
    document_content: str
    case_id: str
    template_id: str
    variables: Annotated[Dict[str, Any], merge_dict]


class DeadlineLangGraphState(BaseLangGraphState):
    """State schema for the deadline agent."""
    document_text: str
    document_metadata: Annotated[Dict[str, Any], merge_dict]
    extracted_deadlines: Annotated[List[Dict[str, Any]], append_list]
    validation_errors: Annotated[List[str], append_list]
    case_id: str
    jurisdiction: str


class EventLangGraphState(BaseLangGraphState):
    """State schema for the event agent."""
    event_id: str
    event_type: str
    event_details: Annotated[Dict[str, Any], merge_dict]
    case_id: str
    calendar_entries: Annotated[List[Dict[str, Any]], append_list]
    reminders: Annotated[List[Dict[str, Any]], append_list]


class CalendarLangGraphState(BaseLangGraphState):
    """State schema for the calendar agent."""
    user_id: str
    tenant_id: str
    calendar_event: Optional[CalendarEvent]
    intent: CalendarIntentEnum
    error: Optional[str]


class TaskLangGraphState(BaseLangGraphState):
    """State schema for the task agent."""
    user_id: str
    tenant_id: str
    task: Optional[TaskModel]
    intent: TaskIntentEnum
    error: Optional[str]


class BaseAgentState(BaseModel):
    """
    Base model for agent state with common functionality.

    This model provides common methods for state manipulation,
    serialization, and deserialization.
    """

    # Model configuration
    model_config = ConfigDict(arbitrary_types_allowed=True)

    # Core fields
    messages: List[BaseMessage] = Field(
        default_factory=list, description="Message history"
    )

    # Tenant and user information
    tenant_id: str = Field(..., description="The tenant ID")
    user_id: str = Field(..., description="The user ID")
    user_context: UserContext = Field(..., description="User context")

    # Thread and conversation information
    thread_id: str = Field(..., description="The thread ID for the conversation")
    agent_type: AgentType = Field(..., description="Type of agent")

    # Memory and context
    memory: Dict[str, Any] = Field(
        default_factory=dict, description="Memory for agent state"
    )

    # Timestamps
    created_at: str = Field(
        default_factory=lambda: datetime.now(timezone.utc).isoformat(),
        description="When the state was created",
    )
    updated_at: str = Field(
        default_factory=lambda: datetime.now(timezone.utc).isoformat(),
        description="When the state was last updated",
    )

    # Version information for state migration
    version: str = Field(default=STATE_VERSION, description="State schema version")

    @field_validator("updated_at", mode="before")
    @classmethod
    def update_timestamp(cls, _):
        """Update the timestamp whenever the state is modified."""
        return datetime.now(timezone.utc).isoformat()

    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for LangGraph state."""
        return self.model_dump()

    def to_json(self) -> str:
        """Convert to JSON string."""
        return json.dumps(self.model_dump(), default=str)

    @classmethod
    def from_json(cls, json_str: str) -> "BaseAgentState":
        """Create from JSON string."""
        data = json.loads(json_str)
        return cls(**data)

    def add_message(self, message: BaseMessage) -> None:
        """Add a message to the history."""
        self.messages.append(message)
        self.updated_at = datetime.now(timezone.utc).isoformat()

    def add_messages(self, messages: List[BaseMessage]) -> None:
        """Add multiple messages to the history."""
        self.messages.extend(messages)
        self.updated_at = datetime.now(timezone.utc).isoformat()

    def get_last_message(self) -> Optional[BaseMessage]:
        """Get the last message in the history."""
        if not self.messages:
            return None
        return self.messages[-1]

    def get_messages_by_type(
        self, message_type: Type[BaseMessage]
    ) -> List[BaseMessage]:
        """Get messages of a specific type."""
        return [msg for msg in self.messages if isinstance(msg, message_type)]

    def get_human_messages(self) -> List[HumanMessage]:
        """Get all human messages."""
        return self.get_messages_by_type(HumanMessage)

    def get_ai_messages(self) -> List[AIMessage]:
        """Get all AI messages."""
        return self.get_messages_by_type(AIMessage)

    def get_system_messages(self) -> List[SystemMessage]:
        """Get all system messages."""
        return self.get_messages_by_type(SystemMessage)

    def get_tool_messages(self) -> List[ToolMessage]:
        """Get all tool messages."""
        return self.get_messages_by_type(ToolMessage)

    def set_memory(self, key: str, value: Any) -> None:
        """Set a value in memory."""
        self.memory[key] = value
        self.updated_at = datetime.now(timezone.utc).isoformat()

    def get_memory(self, key: str, default: Any = None) -> Any:
        """Get a value from memory."""
        return self.memory.get(key, default)

    def update_memory(self, updates: Dict[str, Any]) -> None:
        """Update multiple memory values at once."""
        self.memory.update(updates)
        self.updated_at = datetime.now(timezone.utc).isoformat()

    def clear_memory(self) -> None:
        """Clear all memory values."""
        self.memory.clear()
        self.updated_at = datetime.now(timezone.utc).isoformat()

    def get_active_agent(self) -> Optional[str]:
        """Get the active agent from memory."""
        return self.get_memory("active_agent")

    def set_active_agent(self, agent_type: AgentType) -> None:
        """Set the active agent in memory."""
        self.set_memory("active_agent", agent_type)

    def to_typed_dict(self) -> Dict[str, Any]:
        """Convert to a dictionary compatible with LangGraph TypedDict state."""
        state_dict = self.to_dict()

        # Convert user_context to dict if it's a Pydantic model
        if isinstance(state_dict.get("user_context"), dict) and hasattr(
            self.user_context, "model_dump"
        ):
            state_dict["user_context"] = self.user_context.model_dump()

        return state_dict

    def clone(self: T) -> T:
        """Create a deep copy of the state."""
        return copy.deepcopy(self)


class IntakeAgentState(BaseAgentState):
    """State for the intake agent."""

    client: Optional[ClientInfo] = Field(None, description="Client information")
    case: Optional[CaseInfo] = Field(None, description="Case information")
    current_task: Optional[str] = Field(None, description="Current intake task")
    task_status: Optional[str] = Field(None, description="Status of the current task")

    def set_client_info(self, client_info: ClientInfo) -> None:
        """Set client information."""
        self.client = client_info
        self.updated_at = datetime.now(timezone.utc).isoformat()

    def set_case_info(self, case_info: CaseInfo) -> None:
        """Set case information."""
        self.case = case_info
        self.updated_at = datetime.now(timezone.utc).isoformat()


class ResearchAgentState(BaseAgentState):
    """State for the research agent."""

    question: Optional[str] = Field(None, description="Research question")
    jurisdiction: str = Field("texas", description="Legal jurisdiction")
    practice_areas: Set[PracticeArea] = Field(
        default_factory=set, description="Practice areas"
    )
    queries: List[str] = Field(
        default_factory=list, description="Generated search queries"
    )
    query_type: Optional[QueryType] = Field(None, description="Type of query")
    inferred_mode: Optional[QueryType] = Field(
        None, description="Inferred query mode"
    )
    query_id: str = Field(
        default_factory=lambda: str(uuid.uuid4()), description="Unique query ID"
    )
    case_id: Optional[str] = Field(None, description="Associated case ID")
    legal_documents: List[Document] = Field(
        default_factory=list, description="Retrieved legal documents"
    )
    case_documents: List[Document] = Field(
        default_factory=list, description="Retrieved case documents"
    )

    def can_view_results(self) -> bool:
        """Check if user can view search results."""
        if not self.query_type:
            return True
        if self.query_type == "public":
            return True
        if self.case_id and not self.user_context.can_access_case(self.case_id):
            return False
        if (
            self.query_type == "private"
            and not self.user_context.can_access_sensitive_data()
        ):
            return False
        return True

    def add_query(self, query: str) -> None:
        """Add a search query."""
        self.queries.append(query)
        self.updated_at = datetime.now(timezone.utc).isoformat()

    def add_legal_document(self, document: Document) -> None:
        """Add a legal document to the results."""
        self.legal_documents.append(document)
        self.updated_at = datetime.now(timezone.utc).isoformat()

    def add_case_document(self, document: Document) -> None:
        """Add a case document to the results."""
        self.case_documents.append(document)
        self.updated_at = datetime.now(timezone.utc).isoformat()


class DocumentAgentState(BaseAgentState):
    """State for the document agent."""

    document_id: Optional[str] = Field(None, description="Active document ID")
    document_type: Optional[str] = Field(None, description="Type of document")
    document_content: Optional[str] = Field(None, description="Document content")
    case_id: Optional[str] = Field(None, description="Associated case ID")
    template_id: Optional[str] = Field(None, description="Template ID")
    variables: Dict[str, Any] = Field(
        default_factory=dict, description="Template variables"
    )

    def set_document_content(self, content: str) -> None:
        """Set document content."""
        self.document_content = content
        self.updated_at = datetime.now(timezone.utc).isoformat()

    def set_variable(self, key: str, value: Any) -> None:
        """Set a template variable."""
        self.variables[key] = value
        self.updated_at = datetime.now(timezone.utc).isoformat()


class DeadlineAgentState(BaseAgentState):
    """State for the deadline agent."""

    document_text: Optional[str] = Field(
        None, description="Document text for extraction"
    )
    document_metadata: Dict[str, Any] = Field(
        default_factory=dict, description="Document metadata"
    )
    extracted_deadlines: List[Dict[str, Any]] = Field(
        default_factory=list, description="Extracted deadlines"
    )
    validation_errors: List[str] = Field(
        default_factory=list, description="Validation errors"
    )
    case_id: Optional[str] = Field(None, description="Associated case ID")
    jurisdiction: str = Field("texas", description="Legal jurisdiction")

    def add_deadline(self, deadline: Dict[str, Any]) -> None:
        """Add an extracted deadline."""
        self.extracted_deadlines.append(deadline)
        self.updated_at = datetime.now(timezone.utc).isoformat()

    def add_validation_error(self, error: str) -> None:
        """Add a validation error."""
        self.validation_errors.append(error)
        self.updated_at = datetime.now(timezone.utc).isoformat()

    def get_deadlines_by_type(self, deadline_type: str) -> List[Dict[str, Any]]:
        """Get deadlines of a specific type."""
        return [d for d in self.extracted_deadlines if d.get("type") == deadline_type]

    def get_deadlines_by_date_range(
        self, start_date: str, end_date: str
    ) -> List[Dict[str, Any]]:
        """Get deadlines within a date range."""
        return [
            d for d in self.extracted_deadlines
            if d.get("date") and start_date <= d.get("date") <= end_date
        ]


class EventAgentState(BaseAgentState):
    """State for the event agent."""

    event_id: Optional[str] = Field(None, description="Event ID")
    event_type: Optional[str] = Field(None, description="Type of event")
    event_details: Dict[str, Any] = Field(
        default_factory=dict, description="Event details"
    )
    case_id: Optional[str] = Field(None, description="Associated case ID")
    calendar_entries: List[Dict[str, Any]] = Field(
        default_factory=list, description="Calendar entries"
    )
    reminders: List[Dict[str, Any]] = Field(
        default_factory=list, description="Reminders"
    )

    def add_calendar_entry(self, entry: Dict[str, Any]) -> None:
        """Add a calendar entry."""
        self.calendar_entries.append(entry)
        self.updated_at = datetime.now(timezone.utc).isoformat()

    def add_reminder(self, reminder: Dict[str, Any]) -> None:
        """Add a reminder."""
        self.reminders.append(reminder)
        self.updated_at = datetime.now(timezone.utc).isoformat()

    def get_entries_by_type(self, entry_type: str) -> List[Dict[str, Any]]:
        """Get calendar entries of a specific type."""
        return [e for e in self.calendar_entries if e.get("type") == entry_type]

    def get_entries_by_date_range(
        self, start_date: str, end_date: str
    ) -> List[Dict[str, Any]]:
        """Get calendar entries within a date range."""
        return [
            e for e in self.calendar_entries
            if e.get("date") and start_date <= e.get("date") <= end_date
        ]


# State management utilities

class StateManager:
    """
    Utility class for managing LangGraph state.

    This class provides methods for creating, loading, saving, and manipulating state.
    It also includes utilities for state versioning and migration.
    """

    @staticmethod
    def create_state(
        agent_type: AgentType,
        tenant_id: str,
        user_id: str,
        thread_id: str,
        user_context: Optional[UserContext] = None,
        **kwargs
    ) -> BaseAgentState:
        """
        Create a new state object based on agent type.

        Args:
            agent_type: Type of agent
            tenant_id: Tenant ID
            user_id: User ID
            thread_id: Thread ID
            user_context: User context (optional)
            **kwargs: Additional agent-specific parameters

        Returns:
            A new agent state object
        """
        return create_state(
            agent_type=agent_type,
            tenant_id=tenant_id,
            user_id=user_id,
            thread_id=thread_id,
            user_context=user_context,
            **kwargs
        )

    @staticmethod
    def to_typed_dict(state: BaseAgentState) -> Dict[str, Any]:
        """
        Convert a state object to a dictionary compatible with LangGraph
        TypedDict state.

        Args:
            state: The state object to convert

        Returns:
            A dictionary compatible with LangGraph TypedDict state
        """
        return state.to_typed_dict()

    @staticmethod
    def from_typed_dict(
        state_dict: Dict[str, Any],
        agent_type: Optional[AgentType] = None
    ) -> BaseAgentState:
        """
        Create a state object from a LangGraph TypedDict state dictionary.

        Args:
            state_dict: The state dictionary
            agent_type: The agent type (optional, will be extracted from
                state_dict if not provided)

        Returns:
            A state object
        """
        # Extract agent_type from state_dict if not provided
        if agent_type is None:
            agent_type = state_dict.get("agent_type")
            if agent_type is None:
                raise ValueError(
                    "agent_type must be provided or present in state_dict"
                )

        # Create user context if present
        user_context = None
        if "user_context" in state_dict:
            user_context_data = state_dict["user_context"]
            if isinstance(user_context_data, dict):
                user_context = UserContext(**user_context_data)

        # Create state object
        return create_state(
            agent_type=agent_type,
            tenant_id=state_dict.get("tenant_id", ""),
            user_id=state_dict.get("user_id", ""),
            thread_id=state_dict.get("thread_id", ""),
            user_context=user_context,
            **{
                k: v
                for k, v in state_dict.items()
                if k not in [
                    "agent_type",
                    "tenant_id",
                    "user_id",
                    "thread_id",
                    "user_context",
                ]
            }
        )

    @staticmethod
    def migrate_state(
        state: BaseAgentState, target_version: str = STATE_VERSION
    ) -> BaseAgentState:
        """
        Migrate a state object to a new version.

        Args:
            state: The state object to migrate
            target_version: The target version to migrate to (defaults to current
                STATE_VERSION)

        Returns:
            The migrated state object
        """
        # If already at target version, return as is
        if state.version == target_version:
            return state

        # Clone the state to avoid modifying the original
        migrated_state = state.clone()

        # Apply migrations based on version
        current_version = state.version

        # Example migration from version 0.9.0 to 1.0.0
        if current_version == "0.9.0" and target_version >= "1.0.0":
            # Apply migration logic here
            # For example, adding new fields or transforming existing ones
            if not hasattr(migrated_state, "version"):
                migrated_state.version = "1.0.0"

        # Set the target version
        migrated_state.version = target_version

        return migrated_state

    @staticmethod
    async def save_state(state: BaseAgentState) -> str:
        """
        Save a state object to the database.

        Args:
            state: The state object to save

        Returns:
            The ID of the saved state record
        """
        from pi_lawyer.state.persistence import save_state
        return await save_state(state)

    @staticmethod
    async def load_state(thread_id: str, tenant_id: str) -> Optional[BaseAgentState]:
        """
        Load a state object from the database.

        Args:
            thread_id: The thread ID
            tenant_id: The tenant ID

        Returns:
            The state object, or None if not found
        """
        from pi_lawyer.state.persistence import load_state
        return await load_state(thread_id, tenant_id)

    @staticmethod
    async def delete_state(thread_id: str, tenant_id: str) -> bool:
        """
        Delete a state object from the database.

        Args:
            thread_id: The thread ID
            tenant_id: The tenant ID

        Returns:
            True if the state was deleted, False otherwise
        """
        from pi_lawyer.state.persistence import delete_state
        return await delete_state(thread_id, tenant_id)

    @staticmethod
    async def list_states(tenant_id: str) -> List[Dict[str, Any]]:
        """
        List all state records for a tenant.

        Args:
            tenant_id: The tenant ID

        Returns:
            A list of state records
        """
        from pi_lawyer.state.persistence import list_states
        return await list_states(tenant_id)


class LangGraphCheckpointSaver:
    """
    Custom checkpoint saver for LangGraph that uses our state persistence system.

    This class implements a simplified interface for testing purposes.
    In a real implementation, it would implement the BaseCheckpointSaver interface.
    """

    def __init__(self, tenant_id: str):
        """
        Initialize the checkpoint saver.

        Args:
            tenant_id: The tenant ID for isolation
        """
        self.tenant_id = tenant_id

    async def get(self, key: str) -> Optional[Dict[str, Any]]:
        """
        Get a state by key.

        Args:
            key: The state key (thread ID)

        Returns:
            The state dictionary, or None if not found
        """
        state = await StateManager.load_state(key, self.tenant_id)
        if state is None:
            return None
        return state.to_typed_dict()

    async def put(self, key: str, state: Dict[str, Any]) -> None:
        """
        Save a state by key.

        Args:
            key: The state key (thread ID)
            state: The state dictionary
        """
        state_obj = StateManager.from_typed_dict(state)
        await StateManager.save_state(state_obj)

    async def delete(self, key: str) -> None:
        """
        Delete a state by key.

        Args:
            key: The state key (thread ID)
        """
        # For testing purposes, we'll just log the deletion
        # In a real implementation, this would call StateManager.delete_state
        logger.info(f"Deleting state for key {key} in tenant {self.tenant_id}")
        # Uncomment in actual implementation:
        # await StateManager.delete_state(key, self.tenant_id)


# Utility functions for creating and converting state objects

def create_typed_state(state: BaseAgentState) -> Dict[str, Any]:
    """
    Create a typed state dictionary from a state object for use with LangGraph.

    Args:
        state: The state object to convert

    Returns:
        A dictionary compatible with LangGraph TypedDict state
    """
    return state.to_typed_dict()


def get_state_class_for_agent_type(agent_type: AgentType) -> Type[BaseLangGraphState]:
    """
    Get the appropriate LangGraph state class for an agent type.

    Args:
        agent_type: The agent type

    Returns:
        The LangGraph state class
    """
    if agent_type == "intake":
        return IntakeLangGraphState
    elif agent_type == "research":
        return ResearchLangGraphState
    elif agent_type == "document":
        return DocumentLangGraphState
    elif agent_type == "deadline":
        return DeadlineLangGraphState
    elif agent_type == "event":
        return EventLangGraphState
    else:
        return BaseLangGraphState


def create_graph_for_agent(agent_type: AgentType, tenant_id: str) -> StateGraph:
    """
    Create a LangGraph StateGraph for an agent type.

    Args:
        agent_type: The agent type
        tenant_id: The tenant ID for state persistence

    Returns:
        A LangGraph StateGraph
    """
    state_class = get_state_class_for_agent_type(agent_type)

    # Note: In the actual implementation, we would use the checkpoint_saver
    # but for testing purposes, we'll create a simple StateGraph without it
    # since the version of LangGraph in the test environment might not support it
    return StateGraph(state_class)


# Factory function to create appropriate state based on agent type
def create_state(
    agent_type: AgentType,
    tenant_id: str,
    user_id: str,
    thread_id: str,
    user_context: Optional[UserContext] = None,
    **kwargs
) -> BaseAgentState:
    """
    Create a new state object based on agent type.

    Args:
        agent_type: Type of agent
        tenant_id: Tenant ID
        user_id: User ID
        thread_id: Thread ID
        user_context: User context (optional)
        **kwargs: Additional agent-specific parameters

    Returns:
        A new agent state object
    """
    # Create default user context if not provided
    if user_context is None:
        user_context = UserContext(
            user_id=user_id,
            tenant_id=tenant_id,
            role="staff",  # Default role
            assigned_case_ids=[],
            settings={}
        )

    # Common parameters for all agent types
    common_params = {
        "tenant_id": tenant_id,
        "user_id": user_id,
        "thread_id": thread_id,
        "agent_type": agent_type,
        "user_context": user_context,
        **kwargs
    }

    # Create appropriate state based on agent type
    if agent_type == "intake":
        return IntakeAgentState(**common_params)
    elif agent_type == "research":
        return ResearchAgentState(**common_params)
    elif agent_type == "document":
        return DocumentAgentState(**common_params)
    elif agent_type == "deadline":
        return DeadlineAgentState(**common_params)
    else:
        # Default to base agent state for other types
        return BaseAgentState(**common_params)


# Export all public classes and functions
__all__ = [
    # Enums
    "CalendarIntentEnum",
    "TaskIntentEnum",

    # TypedDict state classes
    "BaseLangGraphState",
    "IntakeLangGraphState",
    "ResearchLangGraphState",
    "DocumentLangGraphState",
    "DeadlineLangGraphState",
    "EventLangGraphState",
    "CalendarLangGraphState",
    "TaskLangGraphState",

    # Pydantic model classes
    "BaseAgentState",
    "IntakeAgentState",
    "ResearchAgentState",
    "DocumentAgentState",
    "DeadlineAgentState",
    "EventAgentState",
    "UserContext",
    "ClientInfo",
    "CaseInfo",

    # Utility functions
    "create_state",
    "create_typed_state",
    "get_state_class_for_agent_type",
    "create_graph_for_agent",

    # State management
    "StateManager",
    "LangGraphCheckpointSaver",

    # Reducer functions
    "add_messages",
    "merge_dict",
    "append_list",

    # Constants
    "STATE_VERSION",
]
