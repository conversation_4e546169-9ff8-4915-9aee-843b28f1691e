"""
Voyage AI Embeddings implementation for LangChain compatibility.
"""
import os
from typing import List, Dict, Any, Optional
import logging
import asyncio
from langchain_core.embeddings import Embeddings
import voyageai

logger = logging.getLogger(__name__)


class VoyageAIEmbeddings(Embeddings):
    """Voyage AI embeddings implementation for LangChain compatibility.

    This class provides a LangChain-compatible interface for Voyage AI embeddings,
    specifically the voyage-3-large model.
    """

    def __init__(
        self, api_key: Optional[str] = None, model: str = "voyage-3-large", **kwargs
    ):
        """Initialize the Voyage AI embeddings.

        Args:
            api_key: Voyage AI API key (defaults to VOYAGE_API_KEY env var)
            model: Voyage AI model to use (defaults to voyage-3-large)
            **kwargs: Additional parameters to pass to the Voyage AI client
        """
        self.api_key = api_key
        self.model = model
        self.client = None
        self.kwargs = kwargs
        self._initialized = False
        logger.info(
            f"Created VoyageAIEmbeddings instance with model {self.model} "
            f"(lazy initialization)"
        )

    def _ensure_initialized(self):
        """Ensure the client is initialized before use.

        This lazy initialization approach allows the class to be instantiated
        without immediately requiring the API key, which is useful during application
        startup when environment variables might not be fully loaded yet.
        """
        if not self._initialized:
            # Get API key from instance or environment
            self.api_key = self.api_key or os.getenv("VOYAGE_API_KEY")
            if not self.api_key:
                raise ValueError(
                    "Voyage AI API key is required. Set VOYAGE_API_KEY environment "
                    "variable or pass api_key."
                )

            # Initialize the client
            self.client = voyageai.Client(api_key=self.api_key)
            self._initialized = True
            logger.info(
                f"Initialized VoyageAIEmbeddings client with model {self.model}"
            )

    def embed_documents(self, texts: List[str]) -> List[List[float]]:
        """Embed a list of documents using Voyage AI.

        Args:
            texts: List of text strings to embed

        Returns:
            List of embedding vectors
        """
        if not texts:
            return []

        # Ensure client is initialized
        self._ensure_initialized()

        try:
            response = self.client.embed(texts=texts, model=self.model, **self.kwargs)
            return response.embeddings
        except Exception as e:
            logger.error(f"Error generating embeddings with Voyage AI: {e}")
            # Return zero vectors as fallback
            dimension = 1024  # voyage-3-large dimension
            return [[0.0] * dimension for _ in texts]

    def embed_query(self, text: str) -> List[float]:
        """Embed a single query text using Voyage AI.

        Args:
            text: Text string to embed

        Returns:
            Embedding vector
        """
        # Ensure client is initialized
        self._ensure_initialized()

        result = self.embed_documents([text])
        return result[0] if result else [0.0] * 1024

    async def aembed_documents(self, texts: List[str]) -> List[List[float]]:
        """Asynchronously embed a list of documents using Voyage AI.

        Args:
            texts: List of text strings to embed

        Returns:
            List of embedding vectors
        """
        if not texts:
            return []

        # Ensure client is initialized (do this here to catch errors early)
        self._ensure_initialized()

        # Use asyncio to run the synchronous method in a thread pool
        loop = asyncio.get_event_loop()
        return await loop.run_in_executor(None, self.embed_documents, texts)

    async def aembed_query(self, text: str) -> List[float]:
        """Asynchronously embed a single query text using Voyage AI.

        Args:
            text: Text string to embed

        Returns:
            Embedding vector
        """
        # Ensure client is initialized (do this here to catch errors early)
        self._ensure_initialized()

        result = await self.aembed_documents([text])
        return result[0] if result else [0.0] * 1024
