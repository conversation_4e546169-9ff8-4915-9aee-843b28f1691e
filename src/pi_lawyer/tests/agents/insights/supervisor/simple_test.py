#!/usr/bin/env python
"""
Simple Test for Supervisor Agent

This script tests the Supervisor Agent with mocks.
"""

import sys
import os
import asyncio
from unittest.mock import AsyncMock, MagicMock, patch

# Add the src directory to the Python path
sys.path.insert(
    0, os.path.abspath(os.path.join(os.path.dirname(__file__), "../../../../../"))
)

# Create mocks
sys.modules['pi_lawyer.agents.base_agent'] = MagicMock()
sys.modules['pi_lawyer.agents.config'] = MagicMock()
sys.modules['pi_lawyer.shared.core.tools.executor'] = MagicMock()
sys.modules['pi_lawyer.shared.core.llm.voyage'] = MagicMock()
sys.modules['pi_lawyer.agents.insights.supervisor.schema'] = MagicMock()
sys.modules['pi_lawyer.shared.core.tools.enqueue_async_job'] = MagicMock()
sys.modules['langgraph.types'] = MagicMock()

# Create a mock Command class
class Command:
    def __init__(self, goto):
        self.goto = goto

# Create a mock Classification class
class Classification:
    def __init__(self, agent, args, confidence):
        self.agent = agent
        self.args = args
        self.confidence = confidence

# Create a mock BaseAgent class
class BaseAgent:
    def __init__(self, config=None):
        self.config = config or {}

    async def initialize(self, state, config):
        return state

    async def execute(self, state, config):
        return state

    async def cleanup(self, state, config):
        return state

# Create a mock AgentConfig class
class AgentConfig:
    def __init__(self, name, agent_type, description, version):
        self.name = name
        self.agent_type = agent_type
        self.description = description
        self.version = version

# Create a mock VoyageClient class
class VoyageClient:
    def __init__(self):
        pass

    async def chat_completion(
        self,
        messages,
        model,
        temperature,
        max_tokens,
        functions=None,
        function_call=None
    ):
        return {
            "choices": [
                {
                    "message": {
                        "function_call": {
                            "name": "classify_intent",
                            "arguments": (
                                '{"agent": "researchAgent", '
                                '"args": {"query": "test query"}, "confidence": 0.95}'
                            )
                        }
                    }
                }
            ]
        }

# Update the mocks
sys.modules['pi_lawyer.agents.base_agent'].BaseAgent = BaseAgent
sys.modules['pi_lawyer.agents.config'].AgentConfig = AgentConfig
sys.modules['pi_lawyer.agents.config'].get_agent_config = MagicMock(
    return_value=None
)
sys.modules['pi_lawyer.shared.core.llm.voyage'].VoyageClient = VoyageClient
sys.modules[
    'pi_lawyer.agents.insights.supervisor.schema'
].Classification = Classification
sys.modules['pi_lawyer.agents.insights.supervisor.schema'].CLASSIFY_FN_SCHEMA = {}
sys.modules['langgraph.types'].Command = Command

# Create a mock enqueue_async_job_tool
class EnqueueAsyncJobTool:
    async def execute(self, **kwargs):
        return MagicMock(job_id="job-123")

# Update the mocks
sys.modules[
    'pi_lawyer.shared.core.tools.enqueue_async_job'
].enqueue_async_job_tool = EnqueueAsyncJobTool()

# Import the SupervisorAgent
from pi_lawyer.agents.insights.supervisor.agent import SupervisorAgent

# Test the SupervisorAgent
async def test_supervisor_agent():
    # Create a SupervisorAgent
    agent = SupervisorAgent()

    # Create a state
    state = {
        "messages": [
            {
                "type": "human",
                "content": (
                    "Research the statute of limitations for personal injury in Texas"
                )
            }
        ],
        "tenant_id": "test-tenant",
        "memory": {}
    }

    # Execute the agent
    result = await agent.execute(state, {})

    # Print the result
    print(f"Result: {result}")

    # Return success
    return True

# Run the test
if __name__ == "__main__":
    result = asyncio.run(test_supervisor_agent())
    print(f"Test {'passed' if result else 'failed'}")
