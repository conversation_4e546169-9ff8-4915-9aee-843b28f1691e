import os
import google.generativeai as genai
from pdf2image import convert_from_path
from PIL import Image
import tempfile
import json
import logging

# Configure the Gemini API
genai.configure(api_key=os.getenv("GOOGLE_API_KEY"))

logger = logging.getLogger(__name__)


class DocumentAnalysisService:
    def __init__(self):
        # Initialize Gemini 2.0 Flash Thinking model
        self.model = genai.GenerativeModel("gemini-2.0-flash-thinking-exp")

    def convert_pdf_to_images(self, pdf_path, dpi=300):
        """Convert PDF pages to high-resolution images"""
        try:
            with tempfile.TemporaryDirectory() as temp_dir:
                images = convert_from_path(pdf_path, dpi=dpi)
                image_paths = []

                for i, image in enumerate(images):
                    image_path = os.path.join(temp_dir, f"page_{i+1}.jpg")
                    image.save(image_path, "JPEG")
                    image_paths.append(image_path)

                # Return list of PIL Image objects
                return [Image.open(path) for path in image_paths]
        except Exception as e:
            logger.error(f"Error converting PDF to images: {str(e)}")
            raise

    def batch_images(self, images, batch_size=10):
        """Group images into batches for processing large documents"""
        for i in range(0, len(images), batch_size):
            yield images[i : i + batch_size]

    def analyze_medical_form(self, pdf_path):
        """Specialized analysis for medical forms"""
        try:
            images = self.convert_pdf_to_images(
                pdf_path, dpi=400
            )  # Higher DPI for forms

            # For large documents, process in batches
            if len(images) > 10:
                all_results = []
                for batch in self.batch_images(images):
                    batch_result = self._process_medical_form_batch(batch)
                    all_results.append(batch_result)

                # Combine results from all batches
                return self._combine_medical_form_results(all_results)
            else:
                return self._process_medical_form_batch(images)
        except Exception as e:
            logger.error(f"Error analyzing medical form: {str(e)}")
            return {"error": str(e)}

    def _process_medical_form_batch(self, images):
        """Process a batch of medical form images"""
        prompt = """
        You are a legal assistant specializing in personal injury cases.

        Extract ALL information from this medical form with perfect accuracy.
        Pay special attention to:
        1. Patient demographics (name, DOB, gender, contact info)
        2. Insurance information (provider, policy number, group number)
        3. Medical history and current medications
        4. Vital signs and measurements
        5. Chief complaint and symptoms
        6. Diagnosis codes (ICD-10) and descriptions
        7. Treatment plan and recommendations
        8. Provider information and signatures
        9. Dates of service and follow-up appointments
        10. Any checkboxes or selections made on the form

        For each field:
        - Provide the exact text as written
        - Note if any field is illegible or unclear
        - For checkboxes, indicate if they are checked or unchecked

        Format your response as a structured JSON object with appropriate fields.
        Include a confidence score (0-1) for each extracted field.
        """

        try:
            response = self.model.generate_content([prompt, *images])

            # Parse the response to extract structured data
            json_str = response.text
            if "```json" in json_str:
                json_str = json_str.split("```json")[1].split("```")[0].strip()
            elif "```" in json_str:
                json_str = json_str.split("```")[1].split("```")[0].strip()

            return json.loads(json_str)
        except Exception as e:
            logger.error(f"Error processing medical form batch: {str(e)}")
            return {
                "error": str(e),
                "raw_text": response.text
                if "response" in locals()
                else "No response generated",
            }

    def _combine_medical_form_results(self, results):
        """Combine results from multiple batches of a medical form"""
        if not results:
            return {}

        combined = {}
        for result in results:
            if isinstance(result, dict) and "error" not in result:
                for key, value in result.items():
                    if key not in combined:
                        combined[key] = value
                    elif isinstance(value, dict) and isinstance(combined[key], dict):
                        combined[key].update(value)
                    elif isinstance(value, list) and isinstance(combined[key], list):
                        combined[key].extend(value)

        return combined

    def analyze_legal_document(
        self, pdf_path, document_type="general", case_context=None
    ):
        """Analyze legal documents with specialized prompts based on type"""
        try:
            images = self.convert_pdf_to_images(pdf_path)

            # Different prompts for different document types
            prompts = {
                "general": f"""
                    Extract ALL text content from this legal document.
                    Maintain document structure, formatting, and hierarchy.
                    Pay special attention to section headings, numbered lists, and any
                    defined terms.
                    Format tables using markdown table syntax.
                    Extract any dates, deadlines, or time-sensitive information.
                    Identify any parties mentioned and their roles.
                    {f"Consider this case context: {case_context}"
                     if case_context else ""}
                """,
                "contract": f"""
                    Extract ALL text content from this contract.
                    Identify and extract:
                    1. Parties involved and their details
                    2. Effective date and term
                    3. Key obligations of each party
                    4. Payment terms and amounts
                    5. Termination conditions
                    6. Any deadlines or important dates
                    7. Governing law
                    Format your response as a structured JSON object.
                    {f"Consider this case context: {case_context}"
                     if case_context else ""}
                """,
                "court_filing": f"""
                    Extract ALL text content from this court filing.
                    Identify and extract:
                    1. Court name and jurisdiction
                    2. Case number and filing date
                    3. Parties involved (plaintiff/defendant)
                    4. Type of filing
                    5. Key arguments or motions
                    6. Relief sought
                    7. Any deadlines or hearing dates
                    Format your response as a structured JSON object.
                    {f"Consider this case context: {case_context}"
                     if case_context else ""}
                """,
                "medical_record": f"""
                    Extract ALL text content from this medical record.
                    Identify and extract:
                    1. Patient information
                    2. Provider information
                    3. Date of service
                    4. Chief complaint
                    5. Diagnosis and treatment
                    6. Medications prescribed
                    7. Follow-up instructions
                    8. Any medical terminology and explain it in layman's terms
                    Format your response as a structured JSON object.
                    {f"Consider this case context: {case_context}"
                     if case_context else ""}
                """,
            }

            prompt = prompts.get(document_type, prompts["general"])

            # For large documents, process in batches
            if len(images) > 10:
                all_results = []
                for batch in self.batch_images(images):
                    batch_result = self._process_document_batch(
                        batch, prompt, document_type
                    )
                    all_results.append(batch_result)

                # Combine results from all batches
                return self._combine_document_results(all_results, document_type)
            else:
                return self._process_document_batch(images, prompt, document_type)
        except Exception as e:
            logger.error(f"Error analyzing legal document: {str(e)}")
            return {"error": str(e)}

    def _process_document_batch(self, images, prompt, document_type):
        """Process a batch of document images"""
        try:
            response = self.model.generate_content([prompt, *images])

            # For structured document types, try to parse as JSON
            if document_type in ["contract", "court_filing", "medical_record"]:
                try:
                    json_str = response.text
                    if "```json" in json_str:
                        json_str = json_str.split("```json")[1].split("```")[0].strip()
                    elif "```" in json_str:
                        json_str = json_str.split("```")[1].split("```")[0].strip()

                    return json.loads(json_str)
                except:
                    # If JSON parsing fails, return the raw text
                    return {"raw_text": response.text}

            # Return text for general document types
            return {"raw_text": response.text}
        except Exception as e:
            logger.error(f"Error processing document batch: {str(e)}")
            return {
                "error": str(e),
                "raw_text": response.text
                if "response" in locals()
                else "No response generated",
            }

    def _combine_document_results(self, results, document_type):
        """Combine results from multiple batches of a document"""
        if not results:
            return {}

        if document_type in ["contract", "court_filing", "medical_record"]:
            # For structured documents, combine JSON results
            combined = {}
            for result in results:
                if isinstance(result, dict) and "error" not in result:
                    for key, value in result.items():
                        if key not in combined:
                            combined[key] = value
                        elif isinstance(value, dict) and isinstance(
                            combined[key], dict
                        ):
                            combined[key].update(value)
                        elif isinstance(value, list) and isinstance(
                            combined[key], list
                        ):
                            combined[key].extend(value)

            return combined
        else:
            # For general documents, concatenate text
            combined_text = ""
            for result in results:
                if isinstance(result, dict) and "raw_text" in result:
                    combined_text += result["raw_text"] + "\n\n"

            return {"raw_text": combined_text.strip()}

    def extract_tasks_from_document(self, pdf_path, case_context=None):
        """Extract potential tasks and deadlines from a document"""
        try:
            images = self.convert_pdf_to_images(pdf_path)

            prompt = f"""
            You are a legal assistant analyzing a document to identify tasks and
            deadlines.

            Extract ALL potential tasks, action items, and deadlines from this document.
            For each task/deadline, provide:
            1. A clear title
            2. A detailed description
            3. The deadline date (if mentioned)
            4. Priority level (high, medium, low) based on urgency
            5. The responsible party (if mentioned)

            Format your response as a JSON array of task objects.
            {f"Consider this case context: {case_context}" if case_context else ""}
            """

            # For large documents, process in batches
            if len(images) > 10:
                all_tasks = []
                for batch in self.batch_images(images):
                    batch_tasks = self._process_tasks_batch(batch, prompt)
                    if isinstance(batch_tasks, list):
                        all_tasks.extend(batch_tasks)

                return all_tasks
            else:
                return self._process_tasks_batch(images, prompt)
        except Exception as e:
            logger.error(f"Error extracting tasks from document: {str(e)}")
            return {"error": str(e)}

    def _process_tasks_batch(self, images, prompt):
        """Process a batch of images to extract tasks"""
        try:
            response = self.model.generate_content([prompt, *images])

            # Parse the response to extract tasks
            json_str = response.text
            if "```json" in json_str:
                json_str = json_str.split("```json")[1].split("```")[0].strip()
            elif "```" in json_str:
                json_str = json_str.split("```")[1].split("```")[0].strip()

            tasks = json.loads(json_str)

            # Ensure tasks have consistent structure
            standardized_tasks = []
            for task in tasks:
                standardized_task = {
                    "title": task.get("title", "Untitled Task"),
                    "description": task.get("description", ""),
                    "deadline": task.get("deadline", task.get("deadline_date", None)),
                    "priority": task.get("priority", "medium"),
                    "responsible_party": task.get(
                        "responsible_party", task.get("assignee", None)
                    ),
                    "confidence_score": task.get("confidence_score", 0.8),
                }
                standardized_tasks.append(standardized_task)

            return standardized_tasks
        except Exception as e:
            logger.error(f"Error processing tasks batch: {str(e)}")
            return {
                "error": str(e),
                "raw_text": response.text
                if "response" in locals()
                else "No response generated",
            }
