"""
Tenant Document Embedding Service

This service handles document chunking, embedding generation, and vector storage
for the PI Lawyer AI platform. It implements a tenant-isolated approach with
pooled namespaces to ensure scalability across thousands of tenants while
maintaining strong security boundaries.
"""

import os
import hashlib
import logging
import asyncio
from typing import List, Dict, Any, Optional, Tuple
from datetime import datetime
import json

import voyage<PERSON>
import pinecone
from langchain.text_splitter import RecursiveCharacterTextSplitter

from pi_lawyer.utils.voyage_embeddings import VoyageAIEmbeddings

from pi_lawyer.models.document import DocumentChunk

logger = logging.getLogger(__name__)


class TenantDocumentEmbeddingService:
    """
    Service for processing document text into chunks, generating embeddings,
    and storing them in Pinecone with proper tenant isolation.
    """

    def __init__(self):
        """Initialize the embedding service with configuration from environment."""
        # Voyage AI Configuration
        self.voyage_api_key = os.getenv("VOYAGE_API_KEY")
        self.embedding_model = os.getenv("EMBEDDING_MODEL", "voyage-3-large")
        self.embedding_batch_size = int(
            os.getenv("EMBEDDING_BATCH_SIZE", "100")
        )  # Max number of chunks to embed at once

        # Pinecone Configuration
        self.pinecone_api_key = os.getenv("PINECONE_API_KEY")
        self.pinecone_environment = os.getenv("PINECONE_ENVIRONMENT", "us-east-1")
        self.pinecone_index_name = os.getenv("PINECONE_INDEX_NAME", "new-texas-laws")
        self.pool_count = int(
            os.getenv("EMBEDDING_POOL_COUNT", "10")
        )  # Number of namespace pools

        # Initialize clients
        self._init_voyage()
        self._init_pinecone()

        # Initialize text splitter for chunking
        self.text_splitter = RecursiveCharacterTextSplitter(
            chunk_size=int(os.getenv("DOCUMENT_CHUNK_SIZE", "1000")),
            chunk_overlap=int(os.getenv("DOCUMENT_CHUNK_OVERLAP", "100")),
            length_function=len,
            separators=["\n\n", "\n", ".", "!", "?", ",", " ", ""],
        )

        logger.info("TenantDocumentEmbeddingService initialized successfully")

    def _init_voyage(self):
        """Initialize the Voyage AI client."""
        if not self.voyage_api_key:
            raise ValueError("VOYAGE_API_KEY environment variable is required")

        self.voyage_client = voyageai.Client(api_key=self.voyage_api_key)
        # Also create a LangChain compatible embeddings instance
        self.embeddings = VoyageAIEmbeddings(
            api_key=self.voyage_api_key, model=self.embedding_model
        )
        logger.info("Voyage AI client initialized")

    def _init_pinecone(self):
        """Initialize the Pinecone client and ensure index exists if possible.
        Will gracefully handle missing API keys or account limitations.
        """
        # Set default index to None (will skip vector operations if not initialized)
        self.index = None

        # Skip if API key is missing
        if not self.pinecone_api_key:
            logger.warning(
                "PINECONE_API_KEY environment variable not set. Vector search will "
                "be disabled."
            )
            return

        try:
            # Try to initialize but catch any exceptions
            pinecone.init(
                api_key=self.pinecone_api_key, environment=self.pinecone_environment
            )

            # Check if index exists, create if not
            available_indexes = pinecone.list_indexes()
            if self.pinecone_index_name not in available_indexes:
                if len(available_indexes) == 0:
                    logger.warning(
                        "No Pinecone indexes available and cannot create new ones "
                        "(account limitation). Vector search will be disabled."
                    )
                    return
                try:
                    logger.info(f"Creating Pinecone index: {self.pinecone_index_name}")
                    pinecone.create_index(
                        self.pinecone_index_name, dimension=1024, metric="cosine"
                    )
                except Exception as e:
                    logger.warning(
                        f"Unable to create Pinecone index: {str(e)}. Vector search "
                        f"will be disabled."
                    )
                    return

            # Connect to the index
            self.index = pinecone.Index(self.pinecone_index_name)
            logger.info(
                f"Pinecone client initialized with index: {self.pinecone_index_name}"
            )

        except Exception as e:
            logger.warning(
                f"Error initializing Pinecone: {str(e)}. Vector search will be "
                f"disabled."
            )

    def get_pool_namespace(self, tenant_id: str) -> str:
        """
        Determine which pool/namespace to use for a tenant.
        Uses a deterministic hash to ensure consistent placement.

        Args:
            tenant_id: The unique identifier for the tenant

        Returns:
            String identifying the pool namespace
        """
        # Use MD5 for deterministic distribution (not for cryptographic purposes)
        hash_val = int(hashlib.md5(tenant_id.encode()).hexdigest(), 16)
        pool_id = hash_val % self.pool_count
        return f"pool{pool_id}"

    async def chunk_document(
        self, text: str, document_id: str, document_metadata: Dict[str, Any]
    ) -> List[DocumentChunk]:
        """
        Split a document into semantically meaningful chunks.

        Args:
            text: Full text content of the document
            document_id: Unique identifier for the document
            document_metadata: Additional metadata about the document

        Returns:
            List of DocumentChunk objects
        """
        # Split the text into chunks
        text_chunks = self.text_splitter.split_text(text)

        # Create DocumentChunk objects with metadata
        document_chunks = []
        for i, chunk_text in enumerate(text_chunks):
            chunk = DocumentChunk(
                content=chunk_text,
                document_id=document_id,
                chunk_num=i + 1,
                chunk_count=len(text_chunks),
                metadata={
                    **document_metadata,
                    "document_id": document_id,
                    "chunk_num": i + 1,
                    "chunk_count": len(text_chunks),
                    "created_at": datetime.utcnow().isoformat(),
                },
            )
            document_chunks.append(chunk)

        logger.info(f"Document {document_id} split into {len(document_chunks)} chunks")
        return document_chunks

    async def _generate_embedding(self, text: str) -> List[float]:
        """
        Generate a single embedding using Voyage AI's API.

        Args:
            text: Text to embed

        Returns:
            List of floats representing the embedding vector
        """
        try:
            # Use the LangChain compatible embeddings class for consistency
            return await self.embeddings.aembed_query(text)
        except Exception as e:
            logger.error(f"Error generating embedding: {e}")
            raise

    async def _generate_batch_embeddings(self, texts: List[str]) -> List[List[float]]:
        """
        Generate embeddings for a batch of texts.

        Args:
            texts: List of text strings to embed

        Returns:
            List of embedding vectors
        """
        if not texts:
            return []

        try:
            # Use the LangChain compatible embeddings class for consistency
            return await self.embeddings.aembed_documents(texts)
        except Exception as e:
            logger.error(f"Error generating batch embeddings: {e}")
            # If batch fails, try one at a time
            embeddings = []
            for text in texts:
                try:
                    embedding = await self._generate_embedding(text)
                    embeddings.append(embedding)
                except Exception as inner_e:
                    logger.error(f"Error generating individual embedding: {inner_e}")
                    # Add a placeholder to maintain alignment
                    embeddings.append([0] * 1024)  # voyage-3-large dimension
            return embeddings

    async def process_batches(
        self, chunks: List[DocumentChunk], tenant_id: str
    ) -> List[str]:
        """
        Process chunks in batches to avoid API limits.

        Args:
            chunks: List of document chunks to embed
            tenant_id: Tenant identifier for isolation

        Returns:
            List of vector IDs created in Pinecone
        """
        all_vector_ids = []

        # Process in batches of embedding_batch_size
        for i in range(0, len(chunks), self.embedding_batch_size):
            batch = chunks[i : i + self.embedding_batch_size]
            batch_vector_ids = await self.generate_embeddings(batch, tenant_id)
            all_vector_ids.extend(batch_vector_ids)

            # Avoid rate limits
            if i + self.embedding_batch_size < len(chunks):
                await asyncio.sleep(0.5)

        return all_vector_ids

    async def generate_embeddings(
        self, chunks: List[DocumentChunk], tenant_id: str
    ) -> List[str]:
        """
        Generate embeddings for document chunks with tenant isolation.

        Args:
            chunks: List of document chunks to embed
            tenant_id: Tenant identifier for isolation

        Returns:
            List of vector IDs created in Pinecone
        """
        if not chunks:
            return []

        # Determine which pool/namespace to use
        namespace = self.get_pool_namespace(tenant_id)

        # Generate embeddings in batches
        texts = [chunk.content for chunk in chunks]
        embeddings = await self._generate_batch_embeddings(texts)

        # Prepare vectors for Pinecone with tenant ID in metadata
        vectors = []
        vector_ids = []

        for i, (chunk, embedding) in enumerate(zip(chunks, embeddings)):
            # Create a structured vector ID
            vector_id = (
                f"{tenant_id}_{chunk.metadata.get('document_id')}_{chunk.chunk_num}"
            )
            vector_ids.append(vector_id)

            # Always include tenant_id in metadata for filtering
            metadata = {
                "tenant_id": tenant_id,
                "document_id": chunk.metadata.get("document_id"),
                "case_id": chunk.metadata.get("case_id"),
                "content_preview": chunk.content[:1000]
                if len(chunk.content) > 1000
                else chunk.content,
                "chunk_num": chunk.chunk_num,
                "chunk_count": chunk.metadata.get("chunk_count"),
                "document_type": chunk.metadata.get("document_type"),
                "created_at": chunk.metadata.get("created_at"),
                # Add additional metadata but ensure it's serializable
                **{
                    k: v
                    for k, v in chunk.metadata.items()
                    if k
                    not in [
                        "document_id",
                        "case_id",
                        "chunk_num",
                        "chunk_count",
                        "document_type",
                        "created_at",
                    ]
                    and isinstance(v, (str, int, float, bool, list, dict))
                },
            }

            vectors.append({"id": vector_id, "values": embedding, "metadata": metadata})

        # Upsert to Pinecone if available
        if self.index is None:
            logger.warning(
                f"Skipping vector upsert as Pinecone is not initialized. "
                f"{len(vectors)} vectors would have been stored."
            )
            return vector_ids

        try:
            self.index.upsert(vectors=vectors, namespace=namespace)
            logger.info(f"Upserted {len(vectors)} vectors to namespace {namespace}")
        except Exception as e:
            logger.warning(f"Error upserting vectors to Pinecone: {str(e)}")

        return vector_ids

    async def search_similar(
        self,
        query: str,
        tenant_id: str,
        top_k: int = 5,
        filter_params: Dict[str, Any] = None,
        user_context: Dict[str, Any] = None,
    ) -> List[Dict[str, Any]]:
        """
        Search for similar chunks with tenant isolation and access control.

        Args:
            query: Search query text
            tenant_id: Tenant identifier for isolation
            top_k: Number of results to return
            filter_params: Additional filtering criteria
            user_context: User information for access control

        Returns:
            List of matching results with scores and metadata
        """
        # Same pool determination as in generate_embeddings
        namespace = self.get_pool_namespace(tenant_id)

        # Generate query embedding
        query_embedding = await self._generate_embedding(query)

        # Always filter by tenant_id for security
        tenant_filter = {"tenant_id": tenant_id}

        # Add case-level access control if not a partner
        if user_context and user_context.get("role") != "partner":
            accessible_case_ids = user_context.get("accessible_case_ids", [])
            if accessible_case_ids:
                case_filter = {"case_id": {"$in": accessible_case_ids}}
                tenant_filter = {"$and": [tenant_filter, case_filter]}

        # Combine with any custom filters
        final_filter = tenant_filter
        if filter_params:
            final_filter = {"$and": [tenant_filter, filter_params]}

        # Search in Pinecone with strict tenant filtering
        if self.index is None:
            logger.warning("Skipping vector search as Pinecone is not initialized.")
            return []

        try:
            results = self.index.query(
                namespace=namespace,
                top_k=top_k,
                include_metadata=True,
                vector=query_embedding,
                filter=final_filter,
            )
        except Exception as e:
            logger.warning(f"Error searching vectors in Pinecone: {str(e)}")
            return []

        return [
            {"score": match["score"], "metadata": match["metadata"], "id": match["id"]}
            for match in results["matches"]
        ]

    async def delete_document_embeddings(self, document_id: str, tenant_id: str) -> int:
        """
        Delete all embeddings for a specific document.

        Args:
            document_id: Document identifier
            tenant_id: Tenant identifier

        Returns:
            Number of embeddings deleted
        """
        namespace = self.get_pool_namespace(tenant_id)

        # Construct filter to find all chunks from this document
        filter_params = {"tenant_id": tenant_id, "document_id": document_id}

        # Get vector IDs matching this document
        results = self.index.query(
            namespace=namespace,
            vector=[0] * 3072,  # Dummy vector, we just need the IDs
            filter=filter_params,
            top_k=10000,  # Get as many as possible
            include_values=False,
            include_metadata=False,
        )

        vector_ids = [match["id"] for match in results["matches"]]

        if vector_ids:
            # Delete the vectors
            self.index.delete(ids=vector_ids, namespace=namespace)
            logger.info(
                f"Deleted {len(vector_ids)} embeddings for document {document_id}"
            )
            return len(vector_ids)

        return 0

    async def process_document(
        self,
        document_id: str,
        text_content: str,
        tenant_id: str,
        document_metadata: Dict[str, Any],
    ) -> Dict[str, Any]:
        """
        Process a full document: chunk, embed, and store in vector database.

        Args:
            document_id: Unique identifier for the document
            text_content: Full text of the document
            tenant_id: Tenant identifier for isolation
            document_metadata: Additional metadata about the document

        Returns:
            Processing summary with vector IDs and counts
        """
        # Chunk the document
        chunks = await self.chunk_document(text_content, document_id, document_metadata)

        # Delete any existing embeddings for this document
        await self.delete_document_embeddings(document_id, tenant_id)

        # Generate and store embeddings
        vector_ids = await self.process_batches(chunks, tenant_id)

        return {
            "document_id": document_id,
            "tenant_id": tenant_id,
            "chunk_count": len(chunks),
            "vector_count": len(vector_ids),
            "pool_namespace": self.get_pool_namespace(tenant_id),
            "completed_at": datetime.utcnow().isoformat(),
        }

    async def get_case_accessible_users(
        self, case_id: str, tenant_id: str, db_pool
    ) -> List[str]:
        """
        Get the list of users who have access to a specific case.

        Args:
            case_id: Case identifier
            tenant_id: Tenant identifier
            db_pool: Database connection pool

        Returns:
            List of user IDs with access to the case
        """
        async with db_pool.acquire() as conn:
            # Get all users with access to this case
            users = await conn.fetch(
                """
                SELECT user_id FROM tenants.assignments
                WHERE tenant_id = $1 AND case_id = $2
                """,
                tenant_id,
                case_id,
            )

            # Also get partner users who have access to all cases
            partners = await conn.fetch(
                """
                SELECT id FROM tenants.users
                WHERE tenant_id = $1 AND role = 'partner'
                """,
                tenant_id,
            )

            # Combine both lists
            return [str(user["user_id"]) for user in users] + [
                str(partner["id"]) for partner in partners
            ]

    async def get_user_accessible_cases(
        self, user_id: str, tenant_id: str, role: str, db_pool
    ) -> List[str]:
        """
        Get list of case IDs the user has access to.

        Args:
            user_id: User identifier
            tenant_id: Tenant identifier
            role: User role
            db_pool: Database connection pool

        Returns:
            List of case IDs the user can access
        """
        async with db_pool.acquire() as conn:
            if role == "partner":
                # Partners can access all cases in their tenant
                cases = await conn.fetch(
                    "SELECT id FROM tenants.cases WHERE tenant_id = $1", tenant_id
                )
                return [str(case["id"]) for case in cases]
            else:
                # Other roles can only access assigned cases
                cases = await conn.fetch(
                    """
                    SELECT case_id FROM tenants.assignments
                    WHERE tenant_id = $1 AND user_id = $2
                    """,
                    tenant_id,
                    user_id,
                )
                return [str(case["case_id"]) for case in cases]
