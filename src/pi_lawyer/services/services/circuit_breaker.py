"""
Circuit breaker implementation for API service protection.

This module implements the circuit breaker pattern to prevent cascading failures
when external services experience issues. It provides automatic service monitoring
and graceful degradation during outages.

The circuit breaker has three states:
- CLOSED: Normal operations, all requests pass through
- OPEN: Service is failing, requests are immediately rejected
- HALF_OPEN: Testing if service has recovered, limited requests allowed

State transitions occur based on failure counts and timeouts:
- CLOSED → OPEN: When failure_threshold is reached
- OPEN → HALF_OPEN: After recovery_timeout seconds
- HALF_OPEN → CLOSED: After successful requests
- HALF_OPEN → OPEN: After failed requests

This implementation uses Redis for distributed state management, ensuring
all application instances share the same circuit state.
"""

import os
import time
import redis
import logging
from enum import Enum
from typing import Dict, Any, Optional, Callable, TypeVar, Generic
from datetime import datetime, timedelta
from functools import wraps

T = TypeVar("T")
logger = logging.getLogger(__name__)


class CircuitState(Enum):
    """Possible states of a circuit breaker."""

    CLOSED = "closed"  # Normal operations, requests pass through
    OPEN = "open"  # Circuit is open, requests immediately fail
    HALF_OPEN = "half_open"  # Testing if service has recovered


class CircuitBreaker:
    """
    Circuit breaker pattern implementation using Redis for distributed state.
    Prevents cascading failures by failing fast when a service is not responding.
    """

    def __init__(self, service_name: str):
        """
        Initialize circuit breaker for a specific service.

        Args:
            service_name: Identifier for the service (e.g., 'openai', 'pinecone')
        """
        self.service_name = service_name
        self.redis_url = os.getenv("REDIS_URL", "redis://localhost:6379")
        self.redis_client = redis.from_url(
            self.redis_url,
            password=os.getenv("REDIS_PASSWORD", ""),
            decode_responses=True,
        )

        # Circuit breaker configuration (load from env or use defaults)
        self.failure_threshold = int(
            os.getenv(f"CB_{service_name.upper()}_THRESHOLD", "5")
        )
        self.recovery_timeout = int(
            os.getenv(f"CB_{service_name.upper()}_TIMEOUT", "60")
        )  # seconds
        self.half_open_max_calls = int(
            os.getenv(f"CB_{service_name.upper()}_HALF_OPEN_CALLS", "2")
        )

        # Redis keys
        self.key_prefix = f"circuit_breaker:{service_name}"
        self.state_key = f"{self.key_prefix}:state"
        self.failure_count_key = f"{self.key_prefix}:failures"
        self.last_failure_key = f"{self.key_prefix}:last_failure"
        self.half_open_calls_key = f"{self.key_prefix}:half_open_calls"

        logger.info(f"Circuit breaker initialized for service: {service_name}")

    def get_state(self) -> CircuitState:
        """
        Get the current state of the circuit breaker.

        Returns:
            Current circuit state
        """
        state = self.redis_client.get(self.state_key)
        if not state:
            # Default to closed if not set
            return CircuitState.CLOSED
        return CircuitState(state)

    def record_success(self) -> None:
        """Record a successful call, potentially closing the circuit."""
        current_state = self.get_state()

        if current_state == CircuitState.HALF_OPEN:
            # Successful call in half-open state, close the circuit
            pipeline = self.redis_client.pipeline()
            pipeline.set(self.state_key, CircuitState.CLOSED.value)
            pipeline.delete(self.failure_count_key)
            pipeline.delete(self.last_failure_key)
            pipeline.delete(self.half_open_calls_key)
            pipeline.execute()
            logger.info(
                f"Circuit for {self.service_name} is now CLOSED after successful "
                f"recovery"
            )
        elif current_state == CircuitState.CLOSED:
            # Reset failure count after success
            self.redis_client.delete(self.failure_count_key)

    def record_failure(self) -> None:
        """Record a failure, potentially opening the circuit.

        This method tracks service failures and transitions the circuit state as needed:
        - In CLOSED state: Increments failure count, opens circuit if threshold reached
        - In HALF_OPEN state: Returns to OPEN state on any failure
        - In OPEN state: Updates last failure timestamp

        All Redis operations use pipelines for atomicity and performance.
        """
        current_state = self.get_state()
        now = datetime.now().timestamp()

        if current_state == CircuitState.CLOSED:
            # Increment failure count and track timestamp
            pipeline = self.redis_client.pipeline()
            pipeline.incr(self.failure_count_key)
            pipeline.set(self.last_failure_key, now)
            failure_count = pipeline.execute()[0]

            # Check if threshold reached
            if failure_count >= self.failure_threshold:
                # Open the circuit using pipeline for consistency
                pipeline = self.redis_client.pipeline()
                pipeline.set(self.state_key, CircuitState.OPEN.value)
                pipeline.execute()
                logger.warning(
                    f"Circuit for {self.service_name} is now OPEN after "
                    f"{failure_count} failures"
                )

        elif current_state == CircuitState.HALF_OPEN:
            # Failed in half-open state, open the circuit again
            pipeline = self.redis_client.pipeline()
            pipeline.set(self.state_key, CircuitState.OPEN.value)
            pipeline.set(self.last_failure_key, now)
            pipeline.delete(self.half_open_calls_key)
            pipeline.execute()
            logger.warning(
                f"Circuit for {self.service_name} is now OPEN again after failure "
                f"in half-open state"
            )

        elif current_state == CircuitState.OPEN:
            # Already open, just update the last failure timestamp
            pipeline = self.redis_client.pipeline()
            pipeline.set(self.last_failure_key, now)
            pipeline.execute()

    def allow_request(self) -> bool:
        """
        Check if a request should be allowed through the circuit breaker.

        Returns:
            True if request should be allowed, False if it should be rejected
        """
        current_state = self.get_state()

        if current_state == CircuitState.CLOSED:
            # Normal operation
            return True

        elif current_state == CircuitState.OPEN:
            # Check if recovery timeout has elapsed
            last_failure = self.redis_client.get(self.last_failure_key)
            if last_failure:
                elapsed = datetime.now().timestamp() - float(last_failure)
                if elapsed > self.recovery_timeout:
                    # Transition to half-open state
                    self.redis_client.set(self.state_key, CircuitState.HALF_OPEN.value)
                    logger.info(
                        f"Circuit for {self.service_name} is now HALF-OPEN for testing"
                    )
                    return self._allow_half_open_request()
            return False

        elif current_state == CircuitState.HALF_OPEN:
            # Allow limited requests in half-open state
            return self._allow_half_open_request()

        return True

    def _allow_half_open_request(self) -> bool:
        """
        Control request flow in half-open state.

        Returns:
            True if request should be allowed in half-open state
        """
        # Use Lua script for atomic increment and check
        script = """
        local current = redis.call('incr', KEYS[1])
        local max = tonumber(ARGV[1])
        if current <= max then
            return 1
        else
            return 0
        end
        """
        result = self.redis_client.eval(
            script, 1, self.half_open_calls_key, self.half_open_max_calls
        )
        return result == 1

    def execute(self, func: Callable[..., T], *args, **kwargs) -> T:
        """
        Execute a function with circuit breaker protection.

        Args:
            func: The function to execute
            *args, **kwargs: Arguments to pass to the function

        Returns:
            The result of the function execution

        Raises:
            CircuitBreakerError: If the circuit is open
            Original exception: If the function fails
        """
        if not self.allow_request():
            error_msg = f"Circuit for {self.service_name} is OPEN, request rejected"
            logger.warning(error_msg)
            raise CircuitBreakerError(error_msg)

        try:
            result = func(*args, **kwargs)
            self.record_success()
            return result
        except Exception as e:
            self.record_failure()
            raise e

    async def execute_async(self, func, *args, **kwargs):
        """
        Execute an async function with circuit breaker protection.

        Args:
            func: The async function to execute
            *args, **kwargs: Arguments to pass to the function

        Returns:
            The result of the async function execution

        Raises:
            CircuitBreakerError: If the circuit is open
            Original exception: If the function fails
        """
        if not self.allow_request():
            error_msg = f"Circuit for {self.service_name} is OPEN, request rejected"
            logger.warning(error_msg)
            raise CircuitBreakerError(error_msg)

        try:
            result = await func(*args, **kwargs)
            self.record_success()
            return result
        except Exception as e:
            self.record_failure()
            raise e


class CircuitBreakerError(Exception):
    """Exception raised when a request is rejected by the circuit breaker."""

    pass


def with_circuit_breaker(service_name: str):
    """
    Decorator to wrap a function with circuit breaker protection.

    Args:
        service_name: The name of the service to protect

    Returns:
        Decorated function
    """

    def decorator(func):
        cb = CircuitBreaker(service_name)

        @wraps(func)
        def wrapper(*args, **kwargs):
            return cb.execute(func, *args, **kwargs)

        return wrapper

    return decorator


def with_async_circuit_breaker(service_name: str):
    """
    Decorator to wrap an async function with circuit breaker protection.

    Args:
        service_name: The name of the service to protect

    Returns:
        Decorated async function
    """

    def decorator(func):
        cb = CircuitBreaker(service_name)

        @wraps(func)
        async def wrapper(*args, **kwargs):
            return await cb.execute_async(func, *args, **kwargs)

        return wrapper

    return decorator


# Initialize circuit breakers for services
openai_cb = CircuitBreaker("openai")
pinecone_cb = CircuitBreaker("pinecone")
supabase_cb = CircuitBreaker("supabase")
