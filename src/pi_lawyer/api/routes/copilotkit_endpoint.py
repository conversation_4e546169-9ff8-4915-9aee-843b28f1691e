"""
CopilotKit Endpoint for FastAPI with AG-UI Protocol Support.

This module implements a FastAPI router for the CopilotKit endpoint that supports
the AG-UI protocol framework. It provides a unified interface for LangGraph agents
to interact with the frontend through CopilotKit.

Key features:
- AG-UI protocol compatibility with comprehensive event types
- JWT authentication with tenant isolation
- Thread management with deterministic thread IDs
- Comprehensive error handling with specific error types
- Support for streaming responses with token-by-token delivery
- Performance monitoring and telemetry collection
- Tool execution with standardized formats
"""

import os
import json
import logging
import time
import uuid
import asyncio
import traceback
from typing import Dict, Any, List, Optional, Union, Callable, Awaitable, Literal
from datetime import datetime
from enum import Enum

from fastapi import (
    APIRouter, Request, HTTPException, Depends, Response, BackgroundTasks, status
)
from fastapi.responses import JSONResponse, StreamingResponse
from pydantic import BaseModel, Field, validator, root_validator

from ...utils.structured_logging import setup_logging
from ...middleware.jwt_middleware import verify_jwt
from ...config.settings import settings
from ...agents.echo_agent import echo_agent
from ...models.auth import UserContext
from ...utils.telemetry import TelemetryManager

# Set up logging
logger = logging.getLogger(__name__)
setup_logging()

# Initialize telemetry manager
telemetry_manager = TelemetryManager(
    service_name="copilotkit-endpoint",
    enable_logging=True,
    enable_metrics=settings.telemetry.enabled,
)

# Create router
router = APIRouter(
    prefix="/copilotkit",
    tags=["copilotkit"],
    responses={
        404: {"description": "Not found"},
        403: {"description": "Forbidden"},
        401: {"description": "Unauthorized"},
        400: {"description": "Bad request"},
        500: {"description": "Internal server error"},
    },
)

# AG-UI Protocol Error Types
class AGUIErrorType(str, Enum):
    """AG-UI Error Types."""
    AUTHENTICATION_ERROR = "authentication_error"
    AUTHORIZATION_ERROR = "authorization_error"
    INVALID_REQUEST = "invalid_request"
    RATE_LIMIT_EXCEEDED = "rate_limit_exceeded"
    INTERNAL_SERVER_ERROR = "internal_server_error"
    AGENT_ERROR = "agent_error"
    TOOL_EXECUTION_ERROR = "tool_execution_error"
    TIMEOUT_ERROR = "timeout_error"
    VALIDATION_ERROR = "validation_error"
    UNKNOWN_ERROR = "unknown_error"

# AG-UI Protocol Event Types
class AGUIEventType(str, Enum):
    """AG-UI Event Types."""
    START = "start"
    CONTENT = "content"
    TOOL_CALLS = "tool_calls"
    TOOL_RESULTS = "tool_results"
    ERROR = "error"
    DONE = "done"
    STATE_SNAPSHOT = "state_snapshot"
    STATE_DELTA = "state_delta"
    METADATA = "metadata"
    PING = "ping"

# Type definitions for AG-UI protocol
class AGUIToolCallFunction(BaseModel):
    """AG-UI Tool Call Function format."""
    name: str
    arguments: str

    @validator("arguments")
    def validate_arguments_json(cls, v):
        """Validate that arguments is valid JSON."""
        try:
            if v:
                json.loads(v)
            return v
        except json.JSONDecodeError:
            raise ValueError("Tool call arguments must be valid JSON")

class AGUIToolCall(BaseModel):
    """AG-UI Tool Call format."""
    id: str
    type: str = "function"
    function: AGUIToolCallFunction

class AGUIMessage(BaseModel):
    """AG-UI Message format."""
    role: str
    content: Union[str, List[str], None]
    name: Optional[str] = None
    tool_calls: Optional[List[AGUIToolCall]] = None
    tool_call_id: Optional[str] = None

    @validator("role")
    def validate_role(cls, v):
        """Validate that role is one of the allowed values."""
        allowed_roles = ["user", "assistant", "system", "tool", "function"]
        if v not in allowed_roles:
            raise ValueError(f"Role must be one of {allowed_roles}")
        return v

    @root_validator
    def validate_tool_call_id(cls, values):
        """Validate that tool_call_id is present if role is 'tool'."""
        role = values.get("role")
        tool_call_id = values.get("tool_call_id")
        if role == "tool" and not tool_call_id:
            raise ValueError("tool_call_id is required when role is 'tool'")
        return values

    class Config:
        """Pydantic configuration."""
        extra = "allow"  # Allow extra fields for forward compatibility

class AGUIRequest(BaseModel):
    """AG-UI Request format."""
    messages: List[AGUIMessage]
    agent: Optional[str] = None
    threadId: Optional[str] = None
    stream: bool = True
    model: Optional[str] = None
    temperature: Optional[float] = None
    max_tokens: Optional[int] = None
    tools: Optional[List[Dict[str, Any]]] = None
    tool_choice: Optional[Union[str, Dict[str, Any]]] = None
    user: Optional[str] = None
    shared_state: Optional[Dict[str, Any]] = None
    metadata: Optional[Dict[str, Any]] = None

    @validator("temperature")
    def validate_temperature(cls, v):
        """Validate that temperature is between 0 and 2."""
        if v is not None and (v < 0 or v > 2):
            raise ValueError("Temperature must be between 0 and 2")
        return v

    @validator("max_tokens")
    def validate_max_tokens(cls, v):
        """Validate that max_tokens is positive."""
        if v is not None and v <= 0:
            raise ValueError("max_tokens must be positive")
        return v

    class Config:
        """Pydantic configuration."""
        extra = "allow"  # Allow extra fields for forward compatibility

class AGUIResponse(BaseModel):
    """AG-UI Response format."""
    messages: List[AGUIMessage]
    done: bool = True
    threadId: str
    customData: Optional[Dict[str, Any]] = None
    usage: Optional[Dict[str, int]] = None
    model: Optional[str] = None

    class Config:
        """Pydantic configuration."""
        extra = "allow"  # Allow extra fields for forward compatibility

class AGUIErrorResponse(BaseModel):
    """AG-UI Error Response format."""
    error: str
    type: AGUIErrorType = AGUIErrorType.UNKNOWN_ERROR
    status: int = 500
    requestId: str
    threadId: Optional[str] = None
    timestamp: str = Field(
        default_factory=lambda: datetime.now(datetime.timezone.utc).isoformat()
    )
    details: Optional[Dict[str, Any]] = None

    class Config:
        """Pydantic configuration."""
        extra = "allow"  # Allow extra fields for forward compatibility

class AGUIStreamEvent(BaseModel):
    """AG-UI Stream Event format."""
    type: AGUIEventType
    content: Optional[str] = None
    threadId: Optional[str] = None
    toolCalls: Optional[List[AGUIToolCall]] = None
    toolResults: Optional[Dict[str, Any]] = None
    error: Optional[str] = None
    errorType: Optional[AGUIErrorType] = None
    metadata: Optional[Dict[str, Any]] = None
    stateSnapshot: Optional[Dict[str, Any]] = None
    stateDelta: Optional[Dict[str, Any]] = None

    @root_validator
    def validate_event_fields(cls, values):
        """Validate that the event has the correct fields for its type."""
        event_type = values.get("type")
        if event_type == AGUIEventType.CONTENT and values.get("content") is None:
            raise ValueError("content is required for content events")
        if (event_type == AGUIEventType.TOOL_CALLS and
            values.get("toolCalls") is None):
            raise ValueError("toolCalls is required for tool_calls events")
        if (event_type == AGUIEventType.TOOL_RESULTS and
            values.get("toolResults") is None):
            raise ValueError("toolResults is required for tool_results events")
        if event_type == AGUIEventType.ERROR and values.get("error") is None:
            raise ValueError("error is required for error events")
        if (event_type == AGUIEventType.STATE_SNAPSHOT and
            values.get("stateSnapshot") is None):
            raise ValueError("stateSnapshot is required for state_snapshot events")
        if event_type == AGUIEventType.STATE_DELTA and values.get("stateDelta") is None:
            raise ValueError("stateDelta is required for state_delta events")
        if event_type == AGUIEventType.METADATA and values.get("metadata") is None:
            raise ValueError("metadata is required for metadata events")
        return values

    class Config:
        """Pydantic configuration."""
        extra = "allow"  # Allow extra fields for forward compatibility


# Verify endpoint key middleware
async def verify_endpoint_key(request: Request) -> None:
    """
    Verify the CopilotKit endpoint key for secure access.

    This function is used as a dependency for the CopilotKit endpoint.
    It checks if the request has a valid endpoint key in the X-CPK-Endpoint-Key header.

    Args:
        request: The FastAPI request object

    Raises:
        HTTPException: If the endpoint key is invalid or missing

    Returns:
        None: If the endpoint key is valid or authentication is disabled
    """
    # Skip verification for health check and OPTIONS requests
    if request.url.path.endswith("/health") or request.method == "OPTIONS":
        return

    # Check if authentication was already verified by the middleware
    if hasattr(request.state, "cpk_authenticated") and request.state.cpk_authenticated:
        logger.debug("CopilotKit endpoint key already verified by middleware")
        return

    # Check if authentication was disabled by the middleware
    if hasattr(request.state, "cpk_auth_disabled") and request.state.cpk_auth_disabled:
        logger.debug("CopilotKit endpoint authentication disabled by middleware")
        return

    # Get the endpoint secret from settings
    from ...config.settings import settings
    endpoint_secret = settings.copilotkit.endpoint_secret

    # If no secret is set or in development mode with default secret, disable auth
    if (not endpoint_secret or
        (endpoint_secret == "test-endpoint-secret-123456789" and settings.debug)):
        logger.warning(
            "CopilotKit endpoint authentication disabled! "
            "This is insecure and should only be used in development."
        )
        return

    # Check the header for the endpoint key
    auth_header = request.headers.get("X-CPK-Endpoint-Key")

    # Log attempt with masked key for security
    if not auth_header:
        masked_header = "None"
    elif len(auth_header) > 8:
        masked_header = f"{auth_header[:4]}...{auth_header[-4:]}"
    else:
        masked_header = "********"
    logger.debug(
        f"CopilotKit endpoint key verification attempt with key: {masked_header}"
    )

    if not auth_header:
        logger.warning("No CopilotKit endpoint key provided")
        raise HTTPException(
            status_code=403,
            detail="CopilotKit endpoint key required"
        )

    # Use constant-time comparison to prevent timing attacks
    import secrets
    if not secrets.compare_digest(auth_header, endpoint_secret):
        logger.warning("Invalid CopilotKit endpoint key provided")
        raise HTTPException(
            status_code=403,
            detail="Invalid CopilotKit endpoint key"
        )

    logger.debug("CopilotKit endpoint key verification successful")


@router.post("")
async def handle_copilotkit_request(
    request: Request,
    background_tasks: BackgroundTasks,
    _: None = Depends(verify_endpoint_key),
    user_context: Optional[UserContext] = Depends(verify_jwt),
) -> Union[JSONResponse, StreamingResponse]:
    """
    Main endpoint for CopilotKit requests with AG-UI protocol support.

    This endpoint handles both legacy GraphQL format requests and AG-UI format requests.
    It provides comprehensive error handling, telemetry collection, and support for
    streaming responses.

    Args:
        request: The FastAPI request object
        background_tasks: FastAPI background tasks
        _: Dependency for endpoint key verification
        user_context: User context from JWT authentication

    Returns:
        Either a JSONResponse for non-streaming requests or a StreamingResponse
        for streaming
    """
    # Generate a unique request ID
    request_id = str(uuid.uuid4())

    # Start tracking the request with telemetry
    telemetry_manager.start_request(request_id, {
        "client_ip": request.client.host if request.client else "unknown",
        "user_agent": request.headers.get("User-Agent", "unknown"),
        "endpoint": "/copilotkit",
        "method": request.method,
        "x_request_id": request.headers.get("X-Request-ID"),
        "x_ag_ui_version": request.headers.get("X-AG-UI-Version"),
        "x_ag_ui_client": request.headers.get("X-AG-UI-Client"),
    })

    # Add user context to telemetry if available
    if user_context:
        telemetry_manager.update_request(request_id, {
            "user_id": user_context.user_id,
            "tenant_id": user_context.tenant_id,
            "role": user_context.role,
            "authenticated": user_context.is_authenticated,
        })

    try:
        # Parse request body
        try:
            body = await request.json()
        except json.JSONDecodeError:
            logger.warning(f"Invalid JSON in request [{request_id}]")
            telemetry_manager.complete_request(request_id, {
                "status": "error",
                "error_type": AGUIErrorType.INVALID_REQUEST,
                "error": "invalid_json",
                "status_code": 400,
            })

            # Return a properly formatted AG-UI error response
            error_response = AGUIErrorResponse(
                error="Invalid JSON in request body",
                type=AGUIErrorType.INVALID_REQUEST,
                status=400,
                requestId=request_id,
            )

            return JSONResponse(
                status_code=400,
                content=error_response.dict(),
                headers={"X-Request-ID": request_id}
            )

        # Update telemetry with request info
        agent_name = body.get(
            "agent",
            body.get("variables", {}).get("data", {}).get("agent", "unknown")
        )
        thread_id = body.get(
            "threadId",
            body.get("variables", {}).get("data", {}).get(
                "threadId", f"thread-{request_id}"
            )
        )
        is_streaming = body.get("stream", True)

        telemetry_manager.update_request(request_id, {
            "agent": agent_name,
            "thread_id": thread_id,
            "streaming": is_streaming,
            "model": body.get("model"),
            "temperature": body.get("temperature"),
            "max_tokens": body.get("max_tokens"),
            "has_tools": bool(body.get("tools")),
        })

        # Log the request (truncated for security)
        logger.info(
            f"Received CopilotKit request [{request_id}] for agent: {agent_name}, "
            f"thread: {thread_id}"
        )

        # Extract GraphQL operation if present (legacy format)
        operation_name = body.get("operationName")

        # Handle different request structures
        if operation_name == "generateCopilotResponse":
            # Legacy GraphQL format
            response = await handle_legacy_request(body, request_id)

            # Complete telemetry
            telemetry_manager.complete_request(request_id, {
                "status": "success",
                "format": "legacy",
                "status_code": 200,
            })

            # Add request ID header to response
            response.headers["X-Request-ID"] = request_id
            return response
        else:
            # Validate AG-UI request format
            try:
                # Parse the request as an AGUIRequest
                agui_request = AGUIRequest(**body)

                # Update telemetry with validated request info
                telemetry_manager.update_request(request_id, {
                    "format": "ag-ui",
                    "message_count": len(agui_request.messages),
                    "validated": True,
                })

                # AG-UI format
                response = await handle_agui_request(
                    body, request_id, background_tasks, request
                )

                # Complete telemetry for non-streaming responses only
                # (streaming responses handle their own telemetry)
                if not is_streaming:
                    telemetry_manager.complete_request(request_id, {
                        "status": "success",
                        "format": "ag-ui",
                        "status_code": 200,
                    })

                # Add request ID header to response if it's a JSONResponse
                if isinstance(response, JSONResponse):
                    response.headers["X-Request-ID"] = request_id

                return response

            except ValueError as validation_error:
                # Handle validation errors
                logger.warning(
                    f"Invalid AG-UI request format [{request_id}]: "
                    f"{str(validation_error)}"
                )
                telemetry_manager.complete_request(request_id, {
                    "status": "error",
                    "error_type": AGUIErrorType.VALIDATION_ERROR,
                    "error": str(validation_error),
                    "status_code": 400,
                })

                # Return a properly formatted AG-UI error response
                error_response = AGUIErrorResponse(
                    error=f"Invalid request format: {str(validation_error)}",
                    type=AGUIErrorType.VALIDATION_ERROR,
                    status=400,
                    requestId=request_id,
                    threadId=thread_id,
                )

                return JSONResponse(
                    status_code=400,
                    content=error_response.dict(),
                    headers={"X-Request-ID": request_id}
                )

    except HTTPException as http_error:
        # Handle HTTP exceptions (like authentication errors)
        logger.warning(f"HTTP exception in request [{request_id}]: {str(http_error)}")

        # Map HTTP status codes to AG-UI error types
        error_type = AGUIErrorType.UNKNOWN_ERROR
        if http_error.status_code == 401:
            error_type = AGUIErrorType.AUTHENTICATION_ERROR
        elif http_error.status_code == 403:
            error_type = AGUIErrorType.AUTHORIZATION_ERROR
        elif http_error.status_code == 429:
            error_type = AGUIErrorType.RATE_LIMIT_EXCEEDED

        telemetry_manager.complete_request(request_id, {
            "status": "error",
            "error_type": error_type,
            "error": str(http_error.detail),
            "status_code": http_error.status_code,
        })

        # Return a properly formatted AG-UI error response
        error_response = AGUIErrorResponse(
            error=str(http_error.detail),
            type=error_type,
            status=http_error.status_code,
            requestId=request_id,
            threadId=body.get("threadId") if 'body' in locals() else None,
        )

        return JSONResponse(
            status_code=http_error.status_code,
            content=error_response.dict(),
            headers={"X-Request-ID": request_id}
        )

    except Exception as e:
        # Handle unexpected exceptions
        logger.error(f"Error handling CopilotKit request [{request_id}]: {str(e)}")
        logger.exception("Exception details:")

        telemetry_manager.complete_request(request_id, {
            "status": "error",
            "error_type": AGUIErrorType.INTERNAL_SERVER_ERROR,
            "error": str(e),
            "status_code": 500,
            "stack_trace": traceback.format_exc(),
        })

        # Return a properly formatted AG-UI error response
        error_response = AGUIErrorResponse(
            error="An internal server error occurred",
            type=AGUIErrorType.INTERNAL_SERVER_ERROR,
            status=500,
            requestId=request_id,
            threadId=body.get("threadId") if 'body' in locals() else None,
            details={
                "error": str(e),
                "timestamp": datetime.now(datetime.timezone.utc).isoformat()
            } if settings.debug else None
        )

        return JSONResponse(
            status_code=500,
            content=error_response.dict(),
            headers={"X-Request-ID": request_id}
        )


async def handle_legacy_request(body: Dict[str, Any], request_id: str) -> JSONResponse:
    """
    Handle legacy GraphQL format requests.

    Args:
        body: The request body
        request_id: A unique identifier for the request

    Returns:
        JSONResponse with the response
    """
    # Extract GraphQL variables
    variables = body.get("variables", {})
    data = variables.get("data", {})

    # Get agent name from variables or fall back to default
    agent_name = data.get("agent", "supervisor_agent")
    thread_id = data.get("threadId", f"thread-{request_id}")

    # For now, return a simple response
    # This will be expanded in the future to properly handle legacy requests
    logger.info(f"Legacy request [{request_id}] for agent: {agent_name}")

    return JSONResponse(
        content={
            "data": {
                "generateCopilotResponse": {
                    "messages": [
                        {
                            "content": [
                                "This endpoint now supports the AG-UI protocol. Please "
                                "update your client."
                            ],
                            "role": "assistant",
                        }
                    ],
                    "done": True,
                    "threadId": thread_id,
                }
            }
        }
    )


async def handle_agui_request(
    body: Dict[str, Any],
    request_id: str,
    _background_tasks: BackgroundTasks,  # Unused but kept for API compatibility
    request: Request = None
) -> Union[JSONResponse, StreamingResponse]:
    """
    Handle AG-UI format requests.

    Args:
        body: The request body
        request_id: A unique identifier for the request
        background_tasks: FastAPI background tasks
        request: The FastAPI request object

    Returns:
        Either a JSONResponse for non-streaming requests or a StreamingResponse
        for streaming
    """
    # Extract request parameters
    agent_name = body.get("agent", "supervisor_agent")
    thread_id = body.get("threadId", f"thread-{request_id}")
    stream = body.get("stream", True)
    messages = body.get("messages", [])
    shared_state = body.get("shared_state", {})

    logger.info(
        f"AG-UI request [{request_id}] for agent: {agent_name}, stream: {stream}"
    )

    # For streaming responses
    if stream:
        return StreamingResponse(
            generate_streaming_response(
                agent_name, messages, thread_id, shared_state, request_id, request
            ),
            media_type="text/event-stream",
        )

    # For non-streaming responses
    response = await generate_response(
        agent_name, messages, thread_id, shared_state, request_id, request
    )
    return JSONResponse(content=response)


async def generate_response(
    agent_name: str,
    messages: List[Dict[str, Any]],
    thread_id: str,
    shared_state: Dict[str, Any],
    request_id: str,
    request: Optional[Request] = None
) -> Dict[str, Any]:
    """
    Generate a non-streaming response according to AG-UI protocol.

    This function:
    1. Processes the request with the appropriate agent
    2. Formats the response according to the AG-UI protocol
    3. Handles errors with appropriate error responses
    4. Collects telemetry for performance monitoring

    Args:
        agent_name: The name of the agent to use
        messages: The messages from the client
        thread_id: The thread ID
        shared_state: Shared state from the client
        request_id: A unique identifier for the request
        request: The FastAPI request object

    Returns:
        A dictionary with the response following AG-UI protocol
    """
    # Mark the start of processing in telemetry
    telemetry_manager.update_request(request_id, {
        "processing_started": True,
        "processing_start_time": time.time(),
        "streaming": False,
    })

    # Track token count for telemetry
    token_count = 0

    try:
        # Get authentication information from request state if available
        auth_info = None
        if request and hasattr(request, "state") and hasattr(request.state, "user"):
            auth_info = {
                "user_id": getattr(request.state.user, "id", "unknown"),
                "tenant_id": getattr(request.state.user, "tenant_id", "unknown"),
                "role": getattr(request.state.user, "role", "unknown"),
            }

            # Update telemetry with auth info
            telemetry_manager.update_request(request_id, {
                "user_id": auth_info["user_id"],
                "tenant_id": auth_info["tenant_id"],
                "role": auth_info["role"],
            })

        # Mark first token in telemetry (for consistency with streaming)
        telemetry_manager.mark_first_token(request_id)

        # Use the echo agent for testing
        if agent_name == "echo_agent":
            request_data = {
                "messages": messages,
                "threadId": thread_id,
                "shared_state": shared_state,
            }

            # Process the request with the echo agent
            response = await echo_agent.handle_request(
                request_data, request_id, auth_info
            )

            # Estimate token count (rough estimate)
            if response and "messages" in response:
                for message in response["messages"]:
                    if message.get("content"):
                        content = message["content"]
                        if isinstance(content, str):
                            # Rough estimate: 1 token per 4 characters
                            token_count += len(content) // 4 + 1
                        elif isinstance(content, list):
                            # For content arrays, sum up the lengths
                            token_count += sum(
                                len(c) // 4 + 1 for c in content if isinstance(c, str)
                            )

            # Add usage information if not present
            if "usage" not in response:
                prompt_tokens = len(str(messages)) // 4  # Rough estimate
                response["usage"] = {
                    "prompt_tokens": prompt_tokens,
                    "completion_tokens": token_count,
                    "total_tokens": prompt_tokens + token_count,
                }

            # Add model information if not present
            if "model" not in response:
                response["model"] = "echo-model"

            # Complete telemetry
            telemetry_manager.complete_request(request_id, {
                "status": "success",
                "agent": "echo_agent",
                "token_count": token_count,
                "processing_completed": True,
            })

            return response

        # For other agents, return a simple response for now
        # This will be replaced with actual agent invocation in future tasks

        # Create a response message
        response_text = (
            f"This is a non-streaming response from {agent_name}. "
            f"Request ID: {request_id}"
        )

        # Estimate token count (rough estimate: 1 token per 4 characters)
        token_count = len(response_text) // 4 + 1

        # Create the response
        response = {
            "messages": [
                {
                    "role": "assistant",
                    "content": response_text
                }
            ],
            "done": True,
            "threadId": thread_id,
            "usage": {
                "prompt_tokens": len(str(messages)) // 4,  # Rough estimate
                "completion_tokens": token_count,
                "total_tokens": len(str(messages)) // 4 + token_count,
            },
            "model": "default-model",
            "customData": {
                "requestId": request_id,
                "timestamp": datetime.now(datetime.timezone.utc).isoformat()
            }
        }

        # Complete telemetry
        telemetry_manager.complete_request(request_id, {
            "status": "success",
            "token_count": token_count,
            "processing_completed": True,
        })

        return response

    except Exception as e:
        # Log the error
        logger.error(f"Error in non-streaming response: {str(e)}")
        logger.exception("Exception details:")

        # Determine error type
        error_type = AGUIErrorType.UNKNOWN_ERROR
        if isinstance(e, ValueError):
            error_type = AGUIErrorType.VALIDATION_ERROR
        elif isinstance(e, TimeoutError):
            error_type = AGUIErrorType.TIMEOUT_ERROR
        elif "tool" in str(e).lower():
            error_type = AGUIErrorType.TOOL_EXECUTION_ERROR
        elif "agent" in str(e).lower():
            error_type = AGUIErrorType.AGENT_ERROR
        else:
            error_type = AGUIErrorType.INTERNAL_SERVER_ERROR

        # Update telemetry
        telemetry_manager.complete_request(request_id, {
            "status": "error",
            "error_type": error_type,
            "error": str(e),
            "token_count": token_count,
            "stack_trace": traceback.format_exc(),
            "processing_completed": False,
        })

        # Create an error message
        error_message = f"An error occurred: {str(e)}"

        # Return an error response
        return {
            "messages": [
                {
                    "role": "assistant",
                    "content": error_message
                }
            ],
            "done": True,
            "threadId": thread_id,
            "error": {
                "message": str(e),
                "type": error_type,
                "timestamp": datetime.now(datetime.timezone.utc).isoformat(),
            },
            "customData": {
                "requestId": request_id,
                "timestamp": datetime.now(datetime.timezone.utc).isoformat(),
                "error": str(e),
                "errorType": error_type,
            }
        }


async def generate_streaming_response(
    agent_name: str,
    messages: List[Dict[str, Any]],
    thread_id: str,
    shared_state: Dict[str, Any],
    request_id: str,
    request: Optional[Request] = None
):
    """
    Generate a streaming response according to AG-UI protocol.

    This function:
    1. Sends a start event to initialize the stream
    2. Processes the request with the appropriate agent
    3. Streams the response as AG-UI events
    4. Handles errors with appropriate error events
    5. Collects telemetry for performance monitoring

    Args:
        agent_name: The name of the agent to use
        messages: The messages from the client
        thread_id: The thread ID
        shared_state: Shared state from the client
        request_id: A unique identifier for the request
        request: The FastAPI request object

    Yields:
        SSE events with the response following AG-UI protocol
    """
    # Mark the start of streaming in telemetry
    telemetry_manager.update_request(request_id, {
        "streaming_started": True,
        "streaming_start_time": time.time(),
    })

    # Track token count for telemetry
    token_count = 0
    chunk_count = 0

    try:
        # Get authentication information from request state if available
        auth_info = None
        if request and hasattr(request, "state") and hasattr(request.state, "user"):
            auth_info = {
                "user_id": getattr(request.state.user, "id", "unknown"),
                "tenant_id": getattr(request.state.user, "tenant_id", "unknown"),
                "role": getattr(request.state.user, "role", "unknown"),
            }

            # Update telemetry with auth info
            telemetry_manager.update_request(request_id, {
                "user_id": auth_info["user_id"],
                "tenant_id": auth_info["tenant_id"],
                "role": auth_info["role"],
            })

        # Create a start event
        start_event = AGUIStreamEvent(
            type=AGUIEventType.START,
            threadId=thread_id,
            metadata={
                "requestId": request_id,
                "agent": agent_name,
                "timestamp": datetime.now(datetime.timezone.utc).isoformat(),
            }
        )

        # Send the start event
        start_event_json = start_event.json()
        yield f"data: {start_event_json}\n\n"

        # Mark first token in telemetry
        telemetry_manager.mark_first_token(request_id)

        # Use the echo agent for testing
        if agent_name == "echo_agent":
            request_data = {
                "messages": messages,
                "threadId": thread_id,
                "shared_state": shared_state,
            }

            # Stream events from the echo agent
            async for event in echo_agent.handle_streaming_request(
                request_data, request_id, auth_info
            ):
                yield event
                chunk_count += 1

                # Estimate token count (rough estimate)
                if "data: " in event:
                    event_data = event.replace("data: ", "").strip()
                    try:
                        event_obj = json.loads(event_data)
                        if (event_obj.get("type") == "content" and
                            event_obj.get("content")):
                            # Rough estimate: 1 token per 4 characters
                            token_count += len(event_obj["content"]) // 4 + 1
                    except json.JSONDecodeError:
                        pass

            # Complete telemetry
            telemetry_manager.complete_request(request_id, {
                "status": "success",
                "agent": "echo_agent",
                "token_count": token_count,
                "chunk_count": chunk_count,
                "streaming_completed": True,
            })

            return

        # For other agents, implement proper agent routing here
        # This will be expanded in future tasks

        # Send content events
        chunks = [
            "Hello! I'm the ",
            f"{agent_name} ",
            "assistant. ",
            "How can I help you today?"
        ]

        for chunk in chunks:
            # Simulate some processing time
            await asyncio.sleep(0.1)

            # Create a content event
            content_event = AGUIStreamEvent(
                type=AGUIEventType.CONTENT,
                content=chunk
            )

            # Send the content event
            content_event_json = content_event.json()
            yield f"data: {content_event_json}\n\n"

            # Update token count (rough estimate: 1 token per 4 characters)
            token_count += len(chunk) // 4 + 1
            chunk_count += 1

            # Update telemetry periodically
            if chunk_count % 10 == 0:
                telemetry_manager.update_request(request_id, {
                    "token_count": token_count,
                    "chunk_count": chunk_count,
                })

        # Add metadata event with usage information
        metadata_event = AGUIStreamEvent(
            type=AGUIEventType.METADATA,
            metadata={
                "usage": {
                    "prompt_tokens": len(str(messages)) // 4,  # Rough estimate
                    "completion_tokens": token_count,
                    "total_tokens": len(str(messages)) // 4 + token_count,
                },
                "model": "echo-model",
                "timestamp": datetime.now(datetime.timezone.utc).isoformat(),
            }
        )

        # Send the metadata event
        metadata_event_json = metadata_event.json()
        yield f"data: {metadata_event_json}\n\n"

        # Create a done event
        done_event = AGUIStreamEvent(
            type=AGUIEventType.DONE,
            threadId=thread_id
        )

        # Send the done event
        done_event_json = done_event.json()
        yield f"data: {done_event_json}\n\n"

        # Complete telemetry
        telemetry_manager.complete_request(request_id, {
            "status": "success",
            "token_count": token_count,
            "chunk_count": chunk_count,
            "streaming_completed": True,
        })

    except Exception as e:
        # Log the error
        logger.error(f"Error in streaming response: {str(e)}")
        logger.exception("Exception details:")

        # Determine error type
        error_type = AGUIErrorType.UNKNOWN_ERROR
        if isinstance(e, ValueError):
            error_type = AGUIErrorType.VALIDATION_ERROR
        elif isinstance(e, TimeoutError):
            error_type = AGUIErrorType.TIMEOUT_ERROR
        elif "tool" in str(e).lower():
            error_type = AGUIErrorType.TOOL_EXECUTION_ERROR
        elif "agent" in str(e).lower():
            error_type = AGUIErrorType.AGENT_ERROR
        else:
            error_type = AGUIErrorType.INTERNAL_SERVER_ERROR

        # Update telemetry
        telemetry_manager.complete_request(request_id, {
            "status": "error",
            "error_type": error_type,
            "error": str(e),
            "token_count": token_count,
            "chunk_count": chunk_count,
            "stack_trace": traceback.format_exc(),
            "streaming_completed": False,
        })

        # Create an error event
        error_event = AGUIStreamEvent(
            type=AGUIEventType.ERROR,
            error=f"An error occurred: {str(e)}",
            errorType=error_type,
            threadId=thread_id
        )

        # Send the error event
        error_event_json = error_event.json()
        yield f"data: {error_event_json}\n\n"

        # Create a done event to close the stream
        done_event = AGUIStreamEvent(
            type=AGUIEventType.DONE,
            threadId=thread_id
        )

        # Send the done event
        done_event_json = done_event.json()
        yield f"data: {done_event_json}\n\n"


# Add CORS preflight handler
@router.options("")
async def options_handler():
    """Handle CORS preflight requests."""
    return {}


# Add health check endpoint
@router.get("/health")
async def health_check():
    """Health check endpoint."""
    return {"status": "healthy", "version": "1.0.0"}
