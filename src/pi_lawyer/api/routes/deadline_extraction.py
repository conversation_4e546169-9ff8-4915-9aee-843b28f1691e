"""API route for the deadline extraction service."""
from typing import Dict, Any, List, Optional
from fastapi import APIRouter, HTTPException, Depends, Body, Query, Path
import logging
from datetime import datetime, timedelta
import uuid

from pi_lawyer.services.deadline_extraction_service import deadline_extraction_service
from pi_lawyer.models.deadline import LegalDeadline
from pi_lawyer.db.supabase_client import SupabaseClient
from pi_lawyer.middleware.auth import withAuth
from pydantic import BaseModel, Field

# Configure logging
logger = logging.getLogger(__name__)


# Create validation models
class ValidationAction(BaseModel):
    """Model for validating or rejecting a deadline."""

    action: str = Field(..., description="Action to take: 'validate' or 'reject'")
    note: Optional[str] = Field(
        None, description="Optional note about the validation decision"
    )

    class Config:
        schema_extra = {
            "example": {"action": "validate", "note": "Confirmed with court calendar"}
        }


# Create router
router = APIRouter(
    prefix="/deadlines",
    tags=["deadlines"],
    responses={404: {"description": "Not found"}},
)


@router.post("/extract")
async def extract_deadlines(
    document_data: Dict[str, Any] = Body(...),
    supabase: SupabaseClient = Depends(lambda: SupabaseClient()),
):
    """Extract deadlines from a document.

    Args:
        document_data: Dictionary containing document text and metadata.
            Required fields:
            - text: Document text
            - metadata: Document metadata including jurisdiction, document_type, etc.

    Returns:
        Dictionary containing extracted deadlines and processing metadata.
    """
    # Validate input
    if "text" not in document_data:
        raise HTTPException(status_code=400, detail="Document text is required")
    if "metadata" not in document_data:
        raise HTTPException(status_code=400, detail="Document metadata is required")

    # Extract required metadata
    metadata = document_data["metadata"]
    required_fields = ["jurisdiction", "document_type"]

    for field in required_fields:
        if field not in metadata:
            raise HTTPException(
                status_code=400, detail=f"Metadata field '{field}' is required"
            )

    try:
        # Process document
        result = await deadline_extraction_service.process_document(
            document_text=document_data["text"], metadata=metadata
        )

        return result

    except Exception as e:
        logger.error(f"Error extracting deadlines: {str(e)}")
        raise HTTPException(
            status_code=500, detail=f"Deadline extraction failed: {str(e)}"
        )


@router.get("/document/{document_id}")
async def get_document_deadlines(
    document_id: str = Path(..., description="Document ID"),
    user_claims: Dict = Depends(withAuth),
    supabase: SupabaseClient = Depends(lambda: SupabaseClient()),
):
    """Get all deadlines for a document.

    Args:
        document_id: Document ID.
        user_claims: User JWT claims with tenant_id and role.

    Returns:
        List of deadlines for the document with document metadata.
    """
    tenant_id = user_claims.get("tenant_id")
    if not tenant_id:
        raise HTTPException(status_code=401, detail="Unauthorized: Missing tenant ID")

    try:
        # Query deadlines for document - use schema for tenant data
        deadline_response = (
            await supabase.client.schema("tenants")
            .table("deadlines")
            .select("*")
            .eq("document_id", document_id)
            .eq("tenant_id", tenant_id)
            .execute()
        )

        # Get document details
        document_response = (
            await supabase.client.schema("tenants")
            .table("case_documents")
            .select("title, description, document_type, case_id, metadata")
            .eq("id", document_id)
            .eq("tenant_id", tenant_id)
            .single()
            .execute()
        )

        return {
            "document": document_response.data if document_response.data else None,
            "deadlines": deadline_response.data,
        }

    except Exception as e:
        logger.error(f"Error retrieving document deadlines: {str(e)}")
        raise HTTPException(
            status_code=500, detail=f"Failed to retrieve deadlines: {str(e)}"
        )


@router.get("/tenant/{tenant_id}")
async def get_tenant_deadlines(
    tenant_id: str = Path(..., description="Tenant ID"),
    priority: str = Query(None, description="Filter by priority level"),
    after_date: str = Query(
        None, description="Filter for deadlines after this date (ISO format)"
    ),
    before_date: str = Query(
        None, description="Filter for deadlines before this date (ISO format)"
    ),
    status: str = Query(
        None, description="Filter by status (pending, completed, etc.)"
    ),
    supabase: SupabaseClient = Depends(lambda: SupabaseClient()),
):
    """Get all deadlines for a tenant with optional filtering.

    Args:
        tenant_id: Tenant ID.
        priority: Optional priority filter (critical, high, medium, low).
        after_date: Optional date filter for deadlines after this date.
        before_date: Optional date filter for deadlines before this date.
        status: Optional status filter (pending, completed, etc.).

    Returns:
        List of filtered deadlines.
    """
    try:
        # Start query - use schema for tenant data
        query = (
            supabase.client.schema("tenants")
            .table("deadlines")
            .select("*")
            .eq("tenant_id", tenant_id)
        )

        # Apply filters
        if priority:
            query = query.eq("priority", priority)

        if after_date:
            query = query.gte("due_date", after_date)

        if before_date:
            query = query.lte("due_date", before_date)

        if status:
            query = query.eq("status", status)

        # Execute query
        response = await query.execute()

        return {"deadlines": response.data}

    except Exception as e:
        logger.error(f"Error retrieving tenant deadlines: {str(e)}")
        raise HTTPException(
            status_code=500, detail=f"Failed to retrieve deadlines: {str(e)}"
        )


@router.get("/case/{case_id}")
async def get_case_deadlines(
    case_id: str = Path(..., description="Case ID"),
    user_claims: Dict = Depends(withAuth),
    priority: str = Query(None, description="Filter by priority level"),
    upcoming_days: int = Query(
        None, description="Only show deadlines in the next X days"
    ),
    status: str = Query(
        None, description="Filter by status (pending, completed, etc.)"
    ),
    supabase: SupabaseClient = Depends(lambda: SupabaseClient()),
):
    """Get all deadlines for a specific case with optional filtering.

    Args:
        case_id: Case ID.
        user_claims: User JWT claims with tenant_id and role.
        priority: Optional priority filter (critical, high, medium, low).
        upcoming_days: Only show deadlines in the next X days.
        status: Optional status filter (pending, completed, etc.).

    Returns:
        List of filtered deadlines for the case.
    """
    tenant_id = user_claims.get("tenant_id")
    if not tenant_id:
        raise HTTPException(status_code=401, detail="Unauthorized: Missing tenant ID")

    try:
        # Start query
        query = (
            supabase.client.schema("tenants")
            .table("deadlines")
            .select("*")
            .eq("tenant_id", tenant_id)
            .eq("case_id", case_id)
        )

        # Apply filters
        if priority:
            query = query.eq("priority", priority)

        if upcoming_days:
            # Calculate date X days from now
            future_date = (datetime.now() + timedelta(days=upcoming_days)).isoformat()
            query = query.lte("due_date", future_date)

        if status:
            query = query.eq("status", status)

        # Order by due date ascending (soonest first)
        query = query.order("due_date", ascending=True)

        # Execute query
        response = await query.execute()

        # Get case details
        case_response = (
            await supabase.client.schema("tenants")
            .table("cases")
            .select("title, case_number, case_type")
            .eq("id", case_id)
            .eq("tenant_id", tenant_id)
            .single()
            .execute()
        )

        return {
            "case": case_response.data if case_response.data else None,
            "deadlines": response.data,
        }

    except Exception as e:
        logger.error(f"Error retrieving case deadlines: {str(e)}")
        raise HTTPException(
            status_code=500, detail=f"Failed to retrieve deadlines: {str(e)}"
        )


@router.get("/client/{client_id}")
async def get_client_deadlines(
    client_id: str = Path(..., description="Client ID"),
    user_claims: Dict = Depends(withAuth),
    priority: str = Query(None, description="Filter by priority level"),
    upcoming_days: int = Query(
        None, description="Only show deadlines in the next X days"
    ),
    status: str = Query(
        None, description="Filter by status (pending, completed, etc.)"
    ),
    supabase: SupabaseClient = Depends(lambda: SupabaseClient()),
):
    """Get all deadlines for a specific client across all their cases.

    Args:
        client_id: Client ID.
        user_claims: User JWT claims with tenant_id and role.
        priority: Optional priority filter (critical, high, medium, low).
        upcoming_days: Only show deadlines in the next X days.
        status: Optional status filter (pending, completed, etc.).

    Returns:
        List of filtered deadlines for the client across all cases.
    """
    tenant_id = user_claims.get("tenant_id")
    role = user_claims.get("role")

    if not tenant_id:
        raise HTTPException(status_code=401, detail="Unauthorized: Missing tenant ID")

    # Only staff or the client themselves can view client deadlines
    if role == "client" and user_claims.get("sub") != client_id:
        raise HTTPException(
            status_code=403, detail="Forbidden: Cannot access other client's deadlines"
        )

    try:
        # First get all cases for this client
        cases_response = (
            await supabase.client.schema("tenants")
            .table("cases")
            .select("id")
            .eq("client_id", client_id)
            .eq("tenant_id", tenant_id)
            .execute()
        )

        if not cases_response.data:
            return {"deadlines": []}

        # Extract case IDs
        case_ids = [case["id"] for case in cases_response.data]

        # Start query
        query = (
            supabase.client.schema("tenants")
            .table("deadlines")
            .select("*, cases(title, case_number, case_type)")
            .eq("tenant_id", tenant_id)
            .in_("case_id", case_ids)
        )

        # Apply filters
        if priority:
            query = query.eq("priority", priority)

        if upcoming_days:
            # Calculate date X days from now
            future_date = (datetime.now() + timedelta(days=upcoming_days)).isoformat()
            query = query.lte("due_date", future_date)

        if status:
            query = query.eq("status", status)

        # Order by due date ascending (soonest first)
        query = query.order("due_date", ascending=True)

        # Execute query
        response = await query.execute()

        # Get client details
        client_response = (
            await supabase.client.schema("tenants")
            .table("clients")
            .select("first_name, last_name, email")
            .eq("id", client_id)
            .eq("tenant_id", tenant_id)
            .single()
            .execute()
        )

        return {
            "client": client_response.data if client_response.data else None,
            "deadlines": response.data,
        }

    except Exception as e:
        logger.error(f"Error retrieving client deadlines: {str(e)}")
        raise HTTPException(
            status_code=500, detail=f"Failed to retrieve deadlines: {str(e)}"
        )


@router.post("/jurisdiction-rules")
async def create_jurisdiction_rule(
    rule: Dict[str, Any] = Body(...),
    user_claims: Dict = Depends(withAuth),
    supabase: SupabaseClient = Depends(lambda: SupabaseClient()),
):
    """Create a new jurisdiction rule.

    Args:
        rule: Jurisdiction rule data.
        user_claims: User JWT claims with role (must be partner).

    Returns:
        Created rule.
    """
    # Only partners can manage jurisdiction rules
    if user_claims.get("role") != "partner":
        raise HTTPException(
            status_code=403,
            detail="Forbidden: Only partners can manage jurisdiction rules",
        )

    try:
        # Create rule - jurisdiction rules are in public schema
        response = (
            await supabase.client.table("jurisdiction_rules").insert(rule).execute()
        )

        return {"rule": response.data[0]}

    except Exception as e:
        logger.error(f"Error creating jurisdiction rule: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to create rule: {str(e)}")


@router.delete("/deadlines/{deadline_id}")
async def delete_deadline(
    deadline_id: str = Path(..., description="Deadline ID"),
    user_claims: Dict = Depends(withAuth),
    supabase: SupabaseClient = Depends(lambda: SupabaseClient()),
):
    """Delete a deadline.

    Args:
        deadline_id: Deadline ID.
        user_claims: User JWT claims with tenant_id and role.

    Returns:
        Success message.
    """
    tenant_id = user_claims.get("tenant_id")
    role = user_claims.get("role")

    if not tenant_id:
        raise HTTPException(status_code=401, detail="Unauthorized: Missing tenant ID")

    # Clients cannot delete deadlines
    if role == "client":
        raise HTTPException(
            status_code=403, detail="Forbidden: Clients cannot delete deadlines"
        )

    try:
        # Delete deadline - use schema for tenant data
        await supabase.client.schema("tenants").table("deadlines").delete().eq(
            "id", deadline_id
        ).eq("tenant_id", tenant_id).execute()

        return {"message": "Deadline deleted successfully"}

    except Exception as e:
        logger.error(f"Error deleting deadline: {str(e)}")
        raise HTTPException(
            status_code=500, detail=f"Failed to delete deadline: {str(e)}"
        )


@router.post("/extract-from-document/{document_id}")
async def extract_deadlines_from_existing_document(
    document_id: str = Path(..., description="Document ID"),
    user_claims: Dict = Depends(withAuth),
    supabase: SupabaseClient = Depends(lambda: SupabaseClient()),
):
    """Extract deadlines from an existing document that has already been uploaded.

    Args:
        document_id: Document ID to extract deadlines from.
        user_claims: User JWT claims with tenant_id and role.

    Returns:
        Dictionary containing extracted deadlines and processing metadata.
    """
    tenant_id = user_claims.get("tenant_id")

    if not tenant_id:
        raise HTTPException(status_code=401, detail="Unauthorized: Missing tenant ID")

    try:
        # Get document content and metadata
        document_response = (
            await supabase.client.schema("tenants")
            .table("case_documents")
            .select(
                "id, title, content, document_type, case_id, metadata, jurisdiction"
            )
            .eq("id", document_id)
            .eq("tenant_id", tenant_id)
            .single()
            .execute()
        )

        if not document_response.data:
            raise HTTPException(status_code=404, detail="Document not found")

        document = document_response.data
        document_text = document.get("content", "")

        if not document_text:
            raise HTTPException(
                status_code=400, detail="Document has no content to extract from"
            )

        # Build metadata for extraction
        metadata = {
            "document_id": document["id"],
            "document_type": document.get("document_type", "unknown"),
            "case_id": document.get("case_id"),
            "tenant_id": tenant_id,
        }

        # Add jurisdiction if available
        if "jurisdiction" in document and document["jurisdiction"]:
            metadata["jurisdiction"] = document["jurisdiction"]
        elif (
            "metadata" in document
            and document["metadata"]
            and "jurisdiction" in document["metadata"]
        ):
            metadata["jurisdiction"] = document["metadata"]["jurisdiction"]
        else:
            # Get case jurisdiction if available
            if document.get("case_id"):
                case_response = (
                    await supabase.client.schema("tenants")
                    .table("cases")
                    .select("jurisdiction")
                    .eq("id", document["case_id"])
                    .eq("tenant_id", tenant_id)
                    .single()
                    .execute()
                )

                if case_response.data and case_response.data.get("jurisdiction"):
                    metadata["jurisdiction"] = case_response.data["jurisdiction"]

        # Process document with deadline extraction service
        result = await deadline_extraction_service.process_document(
            document_text=document_text, metadata=metadata
        )

        # Prepare deadlines for storage
        extracted_deadlines = result.get("deadlines", [])
        deadlines_to_insert = []

        for deadline in extracted_deadlines:
            # Convert to database format
            deadline_record = {
                "tenant_id": tenant_id,
                "case_id": document.get("case_id"),
                "document_id": document["id"],
                "title": deadline.get("description", "")[
                    :255
                ],  # Truncate to fit varchar
                "description": deadline.get("description", ""),
                "due_date": deadline.get("date"),
                "priority": deadline.get("priority", "medium"),
                "status": "pending",
                "created_by": user_claims.get("sub"),
                "calculation_notes": deadline.get("legal_basis", ""),
                "metadata": {
                    "consequences": deadline.get("consequences", []),
                    "jurisdiction": deadline.get("jurisdiction", ""),
                    "source": deadline.get("source", ""),
                },
            }
            deadlines_to_insert.append(deadline_record)

        # Store extracted deadlines
        if deadlines_to_insert:
            insert_response = (
                await supabase.client.schema("tenants")
                .table("deadlines")
                .insert(deadlines_to_insert)
                .execute()
            )

            return {
                "message": (
                    f"Successfully extracted {len(deadlines_to_insert)} deadlines"
                ),
                "deadlines": insert_response.data,
                "warnings": result.get("warnings", []),
            }
        else:
            return {
                "message": "No deadlines found in the document",
                "deadlines": [],
                "warnings": result.get("warnings", []),
            }

    except Exception as e:
        logger.error(f"Error extracting deadlines from document: {str(e)}")
        raise HTTPException(
            status_code=500, detail=f"Failed to extract deadlines: {str(e)}"
        )


@router.get("/validation/pending", summary="Get pending deadlines awaiting validation")
async def get_pending_deadlines(
    user_claims: Dict = Depends(withAuth),
    case_id: Optional[str] = Query(
        None, description="Optional case ID to filter deadlines"
    ),
    client_id: Optional[str] = Query(
        None, description="Optional client ID to filter deadlines"
    ),
    limit: int = Query(50, description="Maximum number of deadlines to return"),
    supabase: SupabaseClient = Depends(lambda: SupabaseClient()),
) -> Dict[str, Any]:
    """Get deadlines that need validation.

    Args:
        user_claims: User JWT claims with tenant_id and role.
        case_id: Optional case ID to filter deadlines.
        client_id: Optional client ID to filter deadlines.
        limit: Maximum number of deadlines to return.

    Returns:
        List of deadlines awaiting validation.
    """
    try:
        tenant_id = user_claims.get("tenant_id")
        if not tenant_id:
            raise HTTPException(status_code=400, detail="Missing tenant_id claim")

        # Prepare the query
        query = (
            supabase.client.schema("tenants")
            .from_("deadlines")
            .select("*, documents!inner(*)")
            .eq("tenant_id", tenant_id)
            .eq("validation_status", "pending")
            .eq("auto_extracted", True)
            .limit(limit)
        )

        # Add optional filters
        if case_id:
            query = query.eq("case_id", case_id)

        # For client filtering, we need to join with cases
        if client_id:
            query = query.select("*, documents!inner(*), cases!inner(*)").eq(
                "cases.client_id", client_id
            )

        # Execute the query
        response = await query.execute()

        if response.error:
            logger.error(f"Error fetching pending deadlines: {response.error.message}")
            raise HTTPException(
                status_code=500, detail="Error fetching pending deadlines"
            )

        return {
            "deadlines": response.data,
            "count": len(response.data),
            "metadata": {
                "tenant_id": tenant_id,
                "case_id": case_id,
                "client_id": client_id,
                "limit": limit,
            },
        }

    except Exception as e:
        logger.error(f"Error in get_pending_deadlines: {str(e)}")
        raise HTTPException(status_code=500, detail="Error fetching pending deadlines")


@router.post("/validation/{deadline_id}", summary="Validate or reject a deadline")
async def validate_deadline(
    deadline_id: str = Path(..., description="Deadline ID to validate or reject"),
    validation_action: ValidationAction = Body(...),
    user_claims: Dict = Depends(withAuth),
    supabase: SupabaseClient = Depends(lambda: SupabaseClient()),
) -> Dict[str, Any]:
    """Validate or reject an extracted deadline.

    Args:
        deadline_id: ID of the deadline to validate or reject.
        validation_action: Action to take (validate or reject) with optional note.
        user_claims: User JWT claims with tenant_id and role.

    Returns:
        Updated deadline information.
    """
    try:
        tenant_id = user_claims.get("tenant_id")
        user_id = user_claims.get("sub")
        role = user_claims.get("role")

        if not tenant_id or not user_id:
            raise HTTPException(
                status_code=400, detail="Missing tenant_id or user_id claim"
            )

        # Check if user has permission to validate deadlines (attorneys and partners)
        allowed_roles = ["attorney", "partner", "admin"]
        if role not in allowed_roles:
            raise HTTPException(
                status_code=403,
                detail=(
                    f"Insufficient permissions. Only {', '.join(allowed_roles)} can "
                    f"validate deadlines."
                ),
            )

        # Verify deadline exists and belongs to the tenant
        deadline_response = (
            await supabase.client.schema("tenants")
            .from_("deadlines")
            .select("*")
            .eq("id", deadline_id)
            .eq("tenant_id", tenant_id)
            .execute()
        )

        if deadline_response.error:
            logger.error(f"Error fetching deadline: {deadline_response.error.message}")
            raise HTTPException(
                status_code=500, detail="Error fetching deadline details"
            )

        if not deadline_response.data or len(deadline_response.data) == 0:
            raise HTTPException(
                status_code=404, detail="Deadline not found or access denied"
            )

        # Get the deadline
        deadline = deadline_response.data[0]

        # If deadline is already validated/rejected, return an error
        if deadline["validation_status"] != "pending":
            raise HTTPException(
                status_code=400,
                detail=(
                    f"Deadline already {deadline['validation_status']}. Cannot modify "
                    f"its status."
                ),
            )

        # Apply the validation action
        action = validation_action.action.lower()
        if action not in ["validate", "reject"]:
            raise HTTPException(
                status_code=400, detail="Invalid action. Must be 'validate' or 'reject'"
            )

        # Update deadline status
        validation_status = "validated" if action == "validate" else "rejected"
        current_time = datetime.utcnow().isoformat()

        update_data = {
            "validation_status": validation_status,
            "validated_by": user_id,
            "validated_at": current_time,
        }

        # Add note if provided
        if validation_action.note:
            update_data["validation_note"] = validation_action.note

        # Update in database
        update_response = (
            await supabase.client.schema("tenants")
            .from_("deadlines")
            .update(update_data)
            .eq("id", deadline_id)
            .eq("tenant_id", tenant_id)
            .execute()
        )

        if update_response.error:
            logger.error(f"Error updating deadline: {update_response.error.message}")
            raise HTTPException(
                status_code=500, detail="Error updating deadline status"
            )

        if not update_response.data or len(update_response.data) == 0:
            raise HTTPException(
                status_code=500, detail="Failed to update deadline status"
            )

        return {
            "deadline": update_response.data[0],
            "message": f"Deadline successfully {validation_status}",
            "metadata": {
                "action": action,
                "validated_by": user_id,
                "validated_at": current_time,
            },
        }

    except HTTPException:
        # Re-raise HTTP exceptions
        raise

    except Exception as e:
        logger.error(f"Error in validate_deadline: {str(e)}")
        raise HTTPException(status_code=500, detail="Error validating deadline")


@router.get("/validation/stats", summary="Get validation statistics")
async def get_validation_stats(
    user_claims: Dict = Depends(withAuth),
    case_id: Optional[str] = Query(
        None, description="Optional case ID to filter statistics"
    ),
    supabase: SupabaseClient = Depends(lambda: SupabaseClient()),
) -> Dict[str, Any]:
    """Get statistics about deadline validation status.

    Args:
        user_claims: User JWT claims with tenant_id and role.
        case_id: Optional case ID to filter statistics.

    Returns:
        Dictionary with validation statistics.
    """
    try:
        tenant_id = user_claims.get("tenant_id")
        if not tenant_id:
            raise HTTPException(status_code=400, detail="Missing tenant_id claim")

        # Build basic query components
        base_select = "validation_status, count(*)"
        base_query = (
            supabase.client.schema("tenants")
            .from_("deadlines")
            .select(base_select, count="exact")
        )
        filters = [("tenant_id", tenant_id)]

        # Add case filter if provided
        if case_id:
            filters.append(("case_id", case_id))

        # Apply all filters
        for column, value in filters:
            base_query = base_query.eq(column, value)

        # Group by validation status
        query = base_query.group_by("validation_status")

        # Execute query
        response = await query.execute()

        if response.error:
            logger.error(f"Error fetching validation stats: {response.error.message}")
            raise HTTPException(
                status_code=500, detail="Error fetching validation statistics"
            )

        # Format results as a dictionary
        status_counts = {"pending": 0, "validated": 0, "rejected": 0}
        for item in response.data:
            status_counts[item["validation_status"]] = item["count"]

        # Get total deadlines
        total_query = (
            supabase.client.schema("tenants")
            .from_("deadlines")
            .select("*", count="exact")
        )

        # Apply the same filters
        for column, value in filters:
            total_query = total_query.eq(column, value)

        # Execute the total count query
        total_response = await total_query.execute()
        total_count = total_response.count or sum(status_counts.values())

        # Calculate percentages
        percentages = {}
        for status, count in status_counts.items():
            percentages[status] = round(
                (count / total_count * 100) if total_count > 0 else 0, 2
            )

        return {
            "counts": status_counts,
            "percentages": percentages,
            "total": total_count,
            "metadata": {
                "tenant_id": tenant_id,
                "case_id": case_id,
                "timestamp": datetime.utcnow().isoformat(),
            },
        }

    except Exception as e:
        logger.error(f"Error in get_validation_stats: {str(e)}")
        raise HTTPException(
            status_code=500, detail="Error fetching validation statistics"
        )
