"""
Document Upload and Processing API

This module provides API endpoints for uploading documents, classifying them,
storing them in Google Cloud Storage, and generating vector embeddings for search.

The API supports two processing paths:
1. Simple Path: Direct chunking and embedding for plain text documents.
2. Complex Path: AI-powered parsing for forms, tables, and structured documents.
"""

import os
import json
import tempfile
import asyncio
from typing import Dict, Any, List, Optional
from datetime import datetime
import logging

from fastapi import APIRouter, UploadFile, File, Form, HTTPException, Request, status
from pydantic import UUID4

from ...utils.storage_utils import StorageClient
from ...services.document_classifier_service import DocumentClassifierService
from ...services.document_processing_queue import get_document_queue
from ...services.document_embedding_utils import queue_authored_document_for_embedding
from ...models.auth import get_auth_data
from ...services.tenant_document_embedding_service import TenantDocumentEmbeddingService
from ...services.document_parser_service import get_document_parser

# Configure logging
logging.basicConfig(
    level=logging.INFO, format="%(asctime)s - %(levelname)s - %(name)s - %(message)s"
)
logger = logging.getLogger(__name__)

# Create router for document upload routes
router = APIRouter(prefix="/api/documents", tags=["document-upload"])


@router.post("/upload", response_model=Dict[str, Any])
async def upload_document(
    request: Request,
    file: UploadFile = File(...),
    case_id: Optional[str] = Form(None),
    client_id: Optional[str] = Form(None),
    document_type: Optional[str] = Form(None),
    title: Optional[str] = Form(None),
    description: Optional[str] = Form(None),
    priority: int = Form(1),
    tags: Optional[str] = Form(None),
    metadata: Optional[str] = Form(None),
    processing_path: Optional[str] = Form("auto"),  # "auto", "simple", or "complex"
):
    """
    Upload a document for processing, classification, and embedding

    This endpoint handles the complete document processing pipeline:
    1. Uploads the file to Google Cloud Storage
    2. Classifies the document using Gemini 2.0 Flash model
    3. Determines the optimal processing path (simple or complex)
    4. Stores document metadata in Supabase
    5. Queues the document for processing
    6. Generates vector embeddings for search in Pinecone

    Processing paths:
    - auto: Automatically determine the best path based on document type
    - simple: Fast processing without AI parsing (best for plain text)
    - complex: AI-powered parsing for forms, tables, structured data (slower but
      more accurate)
    """
    auth_data = get_auth_data(request)
    tenant_id = auth_data["tenant_id"]
    user_id = auth_data["user_id"]

    try:
        # Parse optional JSON fields
        parsed_metadata = json.loads(metadata) if metadata else {}
        parsed_tags = json.loads(tags) if tags else []

        # Read file content
        file_content = await file.read()
        file_size = len(file_content)
        file_name = file.filename
        file_type = file.content_type or "application/octet-stream"

        # Create temp file for processing
        with tempfile.NamedTemporaryFile(delete=False) as temp_file:
            temp_file.write(file_content)
            temp_file_path = temp_file.name

        # Set file metadata
        file_metadata = {
            "uploaded_by": user_id,
            "tenant_id": tenant_id,
            "original_name": file_name,
            "content_type": file_type,
            "size": file_size,
            "description": description,
            "upload_date": datetime.now().isoformat(),
            "processing_status": "pending",
        }

        if case_id:
            file_metadata["case_id"] = case_id

        if client_id:
            file_metadata["client_id"] = client_id

        if parsed_tags:
            file_metadata["tags"] = parsed_tags

        # Merge any additional metadata
        file_metadata.update(parsed_metadata)

        # Log the upload
        logger.info(
            f"Processing document upload: {file_name} ({file_size} bytes) for "
            f"tenant {tenant_id}"
        )

        # 1. Upload to GCS first to secure the file
        try:
            storage_client = StorageClient()
            gcs_info = storage_client.upload_file(
                file_content=file_content,
                file_name=file_name,
                tenant_id=tenant_id,
                case_id=case_id,
                client_id=client_id,
                metadata=file_metadata,
            )

            # Add GCS path to metadata
            file_metadata["gcs_path"] = gcs_info["gcs_path"]
            logger.info(f"Document uploaded to GCS: {gcs_info['gcs_path']}")

            # Track the start of processing time for metrics
            file_metadata["processing_started_at"] = datetime.now().isoformat()
        except Exception as gcs_error:
            logger.error(f"GCS upload failed: {str(gcs_error)}")
            # Clean up temp file
            try:
                os.unlink(temp_file_path)
            except:
                pass
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"Failed to upload document to storage: {str(gcs_error)}",
            )

        # 2. Classify document using Gemini model to determine processing approach
        try:
            classifier = DocumentClassifierService()
            classification = await classifier.classify_document(
                file_path=temp_file_path,
                file_name=file_name,
                file_type=file_type,
                document_metadata=file_metadata,
            )

            # Add classification to metadata
            file_metadata["document_type"] = classification["document_type"]
            file_metadata["analysis_type"] = classification["analysis_type"]
            file_metadata["classification_confidence"] = classification["confidence"]
            file_metadata["classification_reasoning"] = classification["reasoning"]
            file_metadata["recommended_path"] = classification["processing_path"]

            # Determine processing path based on user preference or recommendation
            if processing_path == "auto":
                file_metadata["processing_path"] = classification["processing_path"]
                logger.info(
                    f"Using recommended processing path: "
                    f"{file_metadata['processing_path']}"
                )
            else:
                # User has explicitly chosen a path
                file_metadata["processing_path"] = processing_path
                logger.info(f"Using user-selected processing path: {processing_path}")

            logger.info(
                f"Document classified as {classification['document_type']} with "
                f"analysis type {classification['analysis_type']}"
            )
        except Exception as classify_error:
            logger.warning(
                f"Document classification failed, using defaults: {str(classify_error)}"
            )
            # Use default classification but don't fail the process
            file_metadata["document_type"] = "general"
            file_metadata["analysis_type"] = "text"
            file_metadata["classification_confidence"] = 0.5
            file_metadata["classification_error"] = str(classify_error)
            file_metadata["processing_path"] = (
                processing_path if processing_path != "auto" else "simple"
            )

        # 3. Save document metadata to database
        document_data = {
            "tenant_id": tenant_id,
            "title": title or file_name,
            "description": description,
            "upload_path": gcs_info["gcs_path"],
            "file_type": file_type,
            "file_name": file_name,
            "file_size": file_size,
            "uploaded_by": user_id,
            "tags": parsed_tags,
            "metadata": file_metadata,
            "processing_status": "processing",
            "document_type": file_metadata["document_type"],
            "analysis_type": file_metadata["analysis_type"],
        }

        if case_id:
            document_data["case_id"] = case_id

        if client_id:
            document_data["client_id"] = client_id

        try:
            # Insert into database
            from supabase import create_client, Client

            supabase: Client = create_client(
                os.getenv("NEXT_PUBLIC_SUPABASE_URL", ""),
                os.getenv(
                    "SUPABASE_SERVICE_KEY",
                    os.getenv("NEXT_PUBLIC_SUPABASE_ANON_KEY", ""),
                ),
            )

            response = (
                supabase.table("tenants.documents").insert(document_data).execute()
            )
            document_record = response.data[0]
            document_id = document_record["id"]
            logger.info(f"Document metadata saved to database with ID: {document_id}")
        except Exception as db_error:
            logger.error(f"Database insertion failed: {str(db_error)}")
            # Document is still in GCS, but we couldn't save metadata
            # Clean up temp file
            try:
                os.unlink(temp_file_path)
            except:
                pass
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"Failed to save document metadata: {str(db_error)}",
            )

        # 4. Queue for processing and embedding
        try:
            queue = get_document_queue()
            job_id = queue.enqueue_document(
                document_id=document_id,
                file_path=temp_file_path,
                file_name=file_name,
                file_type=file_type,
                tenant_id=tenant_id,
                user_id=user_id,
                priority=priority,
                metadata=file_metadata,
            )

            # Update document with job information
            supabase.table("tenants.documents").update(
                {
                    "processing_job_id": job_id,
                    "processing_status": "queued",
                    "embedding_status": "pending",
                }
            ).eq("id", document_id).execute()

            logger.info(f"Document queued for processing with job ID: {job_id}")
        except Exception as queue_error:
            logger.error(f"Error queueing document for processing: {str(queue_error)}")
            # Update document to show processing error
            try:
                supabase.table("tenants.documents").update(
                    {"processing_status": "error", "processing_error": str(queue_error)}
                ).eq("id", document_id).execute()
            except:
                pass

            # Don't fail the request since document is uploaded and saved
            # We'll just return with a warning
            return {
                "status": "partial_success",
                "message": "Document uploaded but processing queue failed",
                "document_id": document_id,
                "gcs_info": gcs_info,
                "warning": f"Processing queue error: {str(queue_error)}",
            }

        # 5. Also queue for vector embedding using TenantDocumentEmbeddingService
        try:
            embedding_result = queue_authored_document_for_embedding(
                authored_document_id=document_id,
                tenant_id=tenant_id,
                user_id=user_id,
                document_data=document_data,
                trigger_reason="Document upload",
                source="api",
                priority=priority,
            )

            logger.info(f"Document queued for embedding: {embedding_result['job_id']}")
        except Exception as embed_error:
            logger.error(
                f"Error queueing document for embedding: {str(embed_error)}"
            )
            # Non-critical failure - document is still uploaded and queued for
            # processing

        # 6. Clean up temporary file if everything succeeded
        try:
            os.unlink(temp_file_path)
            logger.debug(f"Temporary file deleted: {temp_file_path}")
        except Exception as cleanup_error:
            logger.warning(f"Failed to clean up temporary file: {str(cleanup_error)}")

        # If complex path, also queue for AI parsing
        if file_metadata.get("processing_path") == "complex":
            try:
                # Update status to show we're starting AI parsing
                supabase.table("tenants.documents").update(
                    {"ai_parsing_status": "pending"}
                ).eq("id", document_id).execute()

                logger.info(
                    f"Document {document_id} will use complex processing path with "
                    f"AI parsing"
                )
            except Exception as parsing_setup_error:
                logger.warning(
                    f"Error setting up AI parsing: {str(parsing_setup_error)}"
                )
                # Non-critical error, continue with processing

        # Estimate processing time based on path
        estimated_time = "30-60 seconds"
        if file_metadata.get("processing_path") == "complex":
            estimated_time = "1-3 minutes"

        return {
            "status": "success",
            "message": "Document uploaded and queued for processing",
            "document_id": document_id,
            "job_id": job_id,
            "gcs_info": gcs_info,
            "document_type": file_metadata["document_type"],
            "analysis_type": file_metadata["analysis_type"],
            "processing_path": file_metadata.get("processing_path", "simple"),
            "estimated_processing_time": estimated_time,
        }

    except HTTPException:
        # Re-raise HTTP exceptions as-is
        raise
    except Exception as e:
        # Catch any unexpected errors
        logger.error(f"Unexpected error uploading document: {str(e)}", exc_info=True)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to upload document: {str(e)}",
        )


@router.get("/status/{job_id}", response_model=Dict[str, Any])
async def get_processing_status(request: Request, job_id: str):
    """Get the status of a document processing job"""
    auth_data = get_auth_data(request)
    tenant_id = auth_data["tenant_id"]

    try:
        # Get queue service
        queue = get_document_queue()

        # Get job status
        status_info = queue.get_job_status(job_id)

        if not status_info:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND, detail="Job not found"
            )

        # Check tenant isolation - only return info for jobs in the tenant
        if status_info.get("tenant_id") != tenant_id:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="You don't have permission to view this job",
            )

        # Add parsing status information for complex path
        parsing_status = None
        parsing_result = None

        if status_info.get("metadata", {}).get("processing_path") == "complex":
            # Get the document to check parsing status
            try:
                doc_response = (
                    supabase.table("tenants.documents")
                    .select("*")
                    .eq("id", status_info.get("document_id"))
                    .execute()
                )
                if doc_response.data:
                    document = doc_response.data[0]
                    parsing_status = document.get("ai_parsing_status")
                    parsing_result = document.get("ai_parsing_result")
            except Exception as e:
                logger.warning(f"Error fetching parsing status: {str(e)}")

        return {
            "status": status_info.get("status", "unknown"),
            "progress": status_info.get("progress", 0),
            "message": status_info.get("message", ""),
            "started_at": status_info.get("processing_started"),
            "completed_at": status_info.get("completed_at"),
            "document_id": status_info.get("document_id"),
            "analysis_type": status_info.get("analysis_type"),
            "processing_path": status_info.get("metadata", {}).get(
                "processing_path", "simple"
            ),
            "parsing_status": parsing_status,
            "parsing_result_available": parsing_result is not None,
            "error": status_info.get("error"),
        }
    except HTTPException:
        # Re-raise HTTP exceptions
        raise
    except Exception as e:
        logger.error(f"Error retrieving job status: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to retrieve job status: {str(e)}",
        )


@router.post("/reprocess/{document_id}", response_model=Dict[str, Any])
async def reprocess_document(
    request: Request, document_id: str, priority: int = Form(1)
):
    """Reprocess a document that was already uploaded"""
    auth_data = get_auth_data(request)
    tenant_id = auth_data["tenant_id"]
    user_id = auth_data["user_id"]

    try:
        # Get Supabase client
        from supabase import create_client, Client

        supabase: Client = create_client(
            os.getenv("NEXT_PUBLIC_SUPABASE_URL", ""),
            os.getenv(
                "SUPABASE_SERVICE_KEY", os.getenv("NEXT_PUBLIC_SUPABASE_ANON_KEY", "")
            ),
        )

        # Check if document exists and belongs to tenant
        response = (
            supabase.table("tenants.documents")
            .select("*")
            .eq("id", document_id)
            .eq("tenant_id", tenant_id)
            .execute()
        )

        if not response.data:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND, detail="Document not found"
            )

        document = response.data[0]

        # Get document from GCS
        storage_client = StorageClient()
        file_content = storage_client.download_file(document["upload_path"])

        if not file_content:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Document file not found in storage",
            )

        # Save to temporary file
        with tempfile.NamedTemporaryFile(delete=False) as temp_file:
            temp_file.write(file_content)
            temp_file_path = temp_file.name

        # Get processing path from request or use existing one
        processing_path = request.query_params.get("processing_path", None)

        # If processing path is not specified, use the existing one from document
        # metadata
        if not processing_path:
            processing_path = document.get("metadata", {}).get(
                "processing_path", "simple"
            )

        # Update metadata with processing path
        metadata = document.get("metadata", {})
        metadata["processing_path"] = processing_path

        # Queue for reprocessing
        queue = get_document_queue()
        job_id = queue.enqueue_document(
            document_id=document_id,
            file_path=temp_file_path,
            file_name=document["file_name"],
            file_type=document["file_type"],
            tenant_id=tenant_id,
            user_id=user_id,
            priority=priority,
            metadata=metadata,
        )

        # Update document status
        supabase.table("tenants.documents").update(
            {
                "processing_job_id": job_id,
                "processing_status": "reprocessing",
                "last_reprocessed_at": datetime.now().isoformat(),
            }
        ).eq("id", document_id).execute()

        # Queue for vector embedding again
        try:
            embedding_result = queue_authored_document_for_embedding(
                authored_document_id=document_id,
                tenant_id=tenant_id,
                user_id=user_id,
                document_data=document,
                trigger_reason="Manual reprocessing",
                source="api",
                priority=priority,
            )

            logger.info(f"Document queued for embedding: {embedding_result['job_id']}")
        except Exception as embed_error:
            logger.warning(f"Error queueing document for embedding: {str(embed_error)}")

        # If complex path, reset AI parsing status
        if processing_path == "complex":
            try:
                # Update status to show we're starting AI parsing
                supabase.table("tenants.documents").update(
                    {"ai_parsing_status": "pending", "ai_parsing_result": None}
                ).eq("id", document_id).execute()

                logger.info(f"Document {document_id} will be reparsed with AI")
            except Exception as parsing_setup_error:
                logger.warning(
                    f"Error resetting AI parsing status: {str(parsing_setup_error)}"
                )

        # Estimate processing time based on path
        estimated_time = "30-60 seconds"
        if processing_path == "complex":
            estimated_time = "1-3 minutes"

        return {
            "status": "success",
            "message": "Document queued for reprocessing",
            "document_id": document_id,
            "job_id": job_id,
            "processing_path": processing_path,
            "estimated_processing_time": estimated_time,
        }

    except HTTPException:
        # Re-raise HTTP exceptions
        raise
    except Exception as e:
        logger.error(f"Error reprocessing document: {str(e)}", exc_info=True)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to reprocess document: {str(e)}",
        )


def register_document_upload_routes(app):
    """Register the document upload routes with the main app"""
    app.include_router(router)
