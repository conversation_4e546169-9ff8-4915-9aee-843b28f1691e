"""
Document Management API Routes

This module provides API endpoints for managing document templates, drafted documents,
document approvals, comments, and version history.

**Note:** All endpoints in this module are specifically for managing attorney-authored
documents.
"""

from fastapi import APIRouter, Depends, HTTPException, Request, status
from pydantic import BaseModel, Field, UUID4
from typing import List, Optional, Dict, Any
import uuid
from datetime import datetime
import logging
import json
from supabase import create_client, Client
import os
from dotenv import load_dotenv
from pi_lawyer.services.document_processing_queue import get_authored_document_queue
from pi_lawyer.services.document_embedding_utils import (
    queue_authored_document_for_embedding,
    should_embed_document,
)

# Configure logging
logging.basicConfig(
    level=logging.INFO, format="%(asctime)s - %(levelname)s - %(message)s"
)
logger = logging.getLogger(__name__)

# Load environment variables
load_dotenv()

# Initialize Supabase client
supabase: Client = create_client(
    os.getenv("NEXT_PUBLIC_SUPABASE_URL", ""),
    os.getenv("SUPABASE_SERVICE_KEY", os.getenv("NEXT_PUBLIC_SUPABASE_ANON_KEY", "")),
)

# Create router for document routes
router = APIRouter(prefix="/api/documents", tags=["documents"])


# Define Pydantic models for request/response validation
class TemplateBase(BaseModel):
    title: str
    content: str
    variables: Optional[Dict[str, Any]] = Field(default_factory=dict)
    metadata: Optional[Dict[str, Any]] = Field(default_factory=dict)


class TemplateCreate(TemplateBase):
    pass


class TemplateResponse(TemplateBase):
    id: UUID4
    tenant_id: UUID4
    version: int
    created_at: datetime
    updated_at: Optional[datetime] = None

    class Config:
        orm_mode = True


class DocumentBase(BaseModel):
    title: str
    content: str
    template_id: Optional[UUID4] = None
    case_id: Optional[UUID4] = None
    client_id: Optional[UUID4] = None
    variables_used: Optional[Dict[str, Any]] = Field(default_factory=dict)
    metadata: Optional[Dict[str, Any]] = Field(default_factory=dict)


class DocumentCreate(DocumentBase):
    pass


class DocumentUpdate(BaseModel):
    title: Optional[str] = None
    content: Optional[str] = None
    status: Optional[str] = None
    variables_used: Optional[Dict[str, Any]] = None
    metadata: Optional[Dict[str, Any]] = None


class DocumentVariableUpdate(BaseModel):
    """Model for updating document variables"""

    variables: Dict[str, Any] = Field(
        ..., description="Dictionary of variable names and values"
    )


class DocumentResponse(DocumentBase):
    id: UUID4
    tenant_id: UUID4
    status: str
    version: int
    created_at: datetime
    updated_at: Optional[datetime] = None

    class Config:
        orm_mode = True


class CommentBase(BaseModel):
    document_id: UUID4
    content: str
    position: Optional[Dict[str, Any]] = None
    parent_id: Optional[UUID4] = None
    metadata: Optional[Dict[str, Any]] = Field(default_factory=dict)


class CommentCreate(CommentBase):
    pass


class CommentUpdate(BaseModel):
    content: Optional[str] = None
    resolved: Optional[bool] = None
    metadata: Optional[Dict[str, Any]] = None


class CommentResponse(CommentBase):
    id: UUID4
    tenant_id: UUID4
    author_id: UUID4
    created_at: datetime
    updated_at: Optional[datetime] = None
    resolved: bool
    resolved_by: Optional[UUID4] = None
    resolved_at: Optional[datetime] = None

    class Config:
        orm_mode = True


class ApprovalBase(BaseModel):
    document_id: UUID4
    approver_id: UUID4
    comments: Optional[str] = None
    metadata: Optional[Dict[str, Any]] = Field(default_factory=dict)


class ApprovalCreate(ApprovalBase):
    pass


class ApprovalUpdate(BaseModel):
    status: str
    comments: Optional[str] = None
    metadata: Optional[Dict[str, Any]] = None


class ApprovalResponse(ApprovalBase):
    id: UUID4
    tenant_id: UUID4
    requester_id: UUID4
    status: str
    created_at: datetime
    updated_at: Optional[datetime] = None

    class Config:
        orm_mode = True


class VersionHistoryResponse(BaseModel):
    id: UUID4
    document_id: UUID4
    version_number: int
    changed_by: UUID4
    changed_at: datetime
    change_type: str
    has_diff: bool
    parent_version_id: Optional[UUID4] = None

    class Config:
        orm_mode = True


# Helper function to get tenant_id and user_id from JWT
def get_auth_data(request: Request):
    auth_header = request.headers.get("Authorization")
    if not auth_header or not auth_header.startswith("Bearer "):
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Invalid authentication credentials",
        )

    token = auth_header.replace("Bearer ", "")

    # You can use Supabase to verify the token and extract claims
    try:
        user = supabase.auth.get_user(token)
        jwt = user.user.app_metadata
        tenant_id = jwt.get("tenant_id")
        user_id = user.user.id
        role = jwt.get("role")

        if not tenant_id:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="User not associated with a tenant",
            )

        return {"tenant_id": tenant_id, "user_id": user_id, "role": role}
    except Exception as e:
        logger.error(f"Error verifying token: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Invalid token",
        )


# Document Template Endpoints
@router.get("/templates", response_model=List[TemplateResponse])
async def list_templates(
    request: Request, category: Optional[str] = None, skip: int = 0, limit: int = 100
):
    """List document templates with optional category filter"""
    auth_data = get_auth_data(request)
    tenant_id = auth_data["tenant_id"]

    try:
        query = (
            supabase.table("tenants.authored_document_templates")
            .select("*")
            .eq("tenant_id", tenant_id)
        )

        if category:
            query = query.contains("metadata", {"category": category})

        query = query.range(skip, skip + limit - 1)
        response = query.execute()

        return response.data
    except Exception as e:
        logger.error(f"Error listing templates: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to list templates: {str(e)}",
        )


@router.get("/templates/{template_id}", response_model=TemplateResponse)
async def get_template(request: Request, template_id: UUID4):
    """Get a document template by ID"""
    auth_data = get_auth_data(request)
    tenant_id = auth_data["tenant_id"]

    try:
        response = (
            supabase.table("tenants.authored_document_templates")
            .select("*")
            .eq("id", str(template_id))
            .eq("tenant_id", tenant_id)
            .execute()
        )

        if not response.data:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Template not found",
            )

        return response.data[0]
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error retrieving template: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to retrieve template: {str(e)}",
        )


@router.post(
    "/templates", response_model=TemplateResponse, status_code=status.HTTP_201_CREATED
)
async def create_template(request: Request, template: TemplateCreate):
    """Create a new document template"""
    auth_data = get_auth_data(request)
    tenant_id = auth_data["tenant_id"]
    user_id = auth_data["user_id"]
    role = auth_data["role"]

    # Check if the user has permission to create templates
    if role not in ["partner", "attorney"]:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Only partners and attorneys can create templates",
        )

    try:
        template_data = {
            "tenant_id": tenant_id,
            "title": template.title,
            "content": template.content,
            "variables": template.variables,
            "metadata": template.metadata,
            "created_by": user_id,
        }

        response = (
            supabase.table("tenants.authored_document_templates")
            .insert(template_data)
            .execute()
        )

        return response.data[0]
    except Exception as e:
        logger.error(f"Error creating template: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to create template: {str(e)}",
        )


@router.put("/templates/{template_id}", response_model=TemplateResponse)
async def update_template(
    request: Request, template_id: UUID4, template: TemplateCreate
):
    """Update a document template"""
    auth_data = get_auth_data(request)
    tenant_id = auth_data["tenant_id"]
    user_id = auth_data["user_id"]
    role = auth_data["role"]

    # Check if the user has permission to update templates
    if role not in ["partner", "attorney"]:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Only partners and attorneys can update templates",
        )

    try:
        # First, check if the template exists and belongs to the tenant
        check_response = (
            supabase.table("tenants.authored_document_templates")
            .select("*")
            .eq("id", str(template_id))
            .eq("tenant_id", tenant_id)
            .execute()
        )

        if not check_response.data:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Template not found",
            )

        existing_template = check_response.data[0]

        # Increment the version number
        new_version = existing_template["version"] + 1

        template_data = {
            "title": template.title,
            "content": template.content,
            "variables": template.variables,
            "metadata": template.metadata,
            "updated_by": user_id,
            "updated_at": "now()",
            "version": new_version,
        }

        response = (
            supabase.table("tenants.authored_document_templates")
            .update(template_data)
            .eq("id", str(template_id))
            .execute()
        )

        return response.data[0]
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error updating template: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to update template: {str(e)}",
        )


@router.delete("/templates/{template_id}", status_code=status.HTTP_204_NO_CONTENT)
async def delete_template(request: Request, template_id: UUID4):
    """Delete a document template"""
    auth_data = get_auth_data(request)
    tenant_id = auth_data["tenant_id"]
    role = auth_data["role"]

    # Check if the user has permission to delete templates
    if role != "partner":
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Only partners can delete templates",
        )

    try:
        # First, check if the template exists and belongs to the tenant
        check_response = (
            supabase.table("tenants.authored_document_templates")
            .select("*")
            .eq("id", str(template_id))
            .eq("tenant_id", tenant_id)
            .execute()
        )

        if not check_response.data:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Template not found",
            )

        # Delete the template
        supabase.table("tenants.authored_document_templates").delete().eq(
            "id", str(template_id)
        ).execute()

        return None
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error deleting template: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to delete template: {str(e)}",
        )


# Authored Documents Endpoints
@router.get("/", response_model=List[DocumentResponse])
async def list_documents(
    request: Request,
    case_id: Optional[UUID4] = None,
    client_id: Optional[UUID4] = None,
    status: Optional[str] = None,
    skip: int = 0,
    limit: int = 100,
):
    """List drafted documents with optional filters"""
    auth_data = get_auth_data(request)
    tenant_id = auth_data["tenant_id"]
    user_id = auth_data["user_id"]
    role = auth_data["role"]

    try:
        query = (
            supabase.table("tenants.authored_documents")
            .select("*")
            .eq("tenant_id", tenant_id)
        )

        # Apply filters
        if case_id:
            query = query.eq("case_id", str(case_id))
        if client_id:
            query = query.eq("client_id", str(client_id))
        if status:
            query = query.eq("status", status)

        # If user is a client, only show documents for that client
        if role == "client":
            query = query.eq("client_id", user_id)

        query = query.range(skip, skip + limit - 1)
        response = query.execute()

        return response.data
    except Exception as e:
        logger.error(f"Error listing documents: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to list documents: {str(e)}",
        )


@router.get("/{document_id}", response_model=DocumentResponse)
async def get_document(request: Request, document_id: UUID4):
    """Get a document by ID"""
    auth_data = get_auth_data(request)
    tenant_id = auth_data["tenant_id"]
    user_id = auth_data["user_id"]
    role = auth_data["role"]

    try:
        query = (
            supabase.table("tenants.authored_documents")
            .select("*")
            .eq("id", str(document_id))
            .eq("tenant_id", tenant_id)
        )

        # If user is a client, they can only view their own documents
        if role == "client":
            query = query.eq("client_id", user_id)

        response = query.execute()

        if not response.data:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Document not found",
            )

        return response.data[0]
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error retrieving document: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to retrieve document: {str(e)}",
        )


@router.post("/", response_model=DocumentResponse, status_code=status.HTTP_201_CREATED)
async def create_document(request: Request, document: DocumentCreate):
    """Create a new document, optionally from a template"""
    auth_data = get_auth_data(request)
    tenant_id = auth_data["tenant_id"]
    user_id = auth_data["user_id"]
    role = auth_data["role"]

    # Check if the user has permission to create documents
    if role not in ["partner", "attorney", "paralegal", "staff"]:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Only staff members can create documents",
        )

    try:
        document_data = {
            "tenant_id": tenant_id,
            "title": document.title,
            "content": document.content,
            "template_id": str(document.template_id) if document.template_id else None,
            "case_id": str(document.case_id) if document.case_id else None,
            "client_id": str(document.client_id) if document.client_id else None,
            "variables_used": document.variables_used,
            "metadata": document.metadata,
            "created_by": user_id,
            "status": "draft",
        }

        # Create the document
        doc_response = (
            supabase.table("tenants.authored_documents").insert(document_data).execute()
        )
        new_document = doc_response.data[0]

        # Add the creating user as an editor
        editor_data = {
            "tenant_id": tenant_id,
            "document_id": new_document["id"],
            "editor_id": user_id,
            "can_edit": True,
            "can_comment": True,
            "can_approve": role in ["partner", "attorney"],
        }

        supabase.table("tenants.authored_document_editors").insert(
            editor_data
        ).execute()

        # Check if the document should be embedded
        should_embed, embed_reason = should_embed_document(
            new_document, document.metadata
        )

        # Queue the document for embedding if needed
        if should_embed:
            embedding_result = queue_authored_document_for_embedding(
                authored_document_id=str(new_document["id"]),
                tenant_id=tenant_id,
                user_id=user_id,
                document_data={
                    "title": document.title,
                    "case_id": str(document.case_id) if document.case_id else None,
                    "client_id": str(document.client_id)
                    if document.client_id
                    else None,
                    "status": new_document.get("status", "draft"),
                    "metadata": document.metadata,
                },
                trigger_reason=embed_reason,
                source="api_create",
                supabase_client=supabase,
            )

            if embedding_result.get("status") == "error":
                logger.error(
                    f"Error queueing document for embedding: "
                    f"{embedding_result.get('error')}"
                )

        return new_document
    except Exception as e:
        logger.error(f"Error creating document: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to create document: {str(e)}",
        )


@router.put("/{document_id}", response_model=DocumentResponse)
async def update_document(
    request: Request, document_id: UUID4, document: DocumentUpdate
):
    """Update a document"""
    auth_data = get_auth_data(request)
    tenant_id = auth_data["tenant_id"]
    user_id = auth_data["user_id"]

    try:
        # First, check if the document exists and belongs to the tenant
        doc_response = (
            supabase.table("tenants.authored_documents")
            .select("*")
            .eq("id", str(document_id))
            .eq("tenant_id", tenant_id)
            .execute()
        )

        if not doc_response.data:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Document not found",
            )

        # Check if the user has edit permission
        editor_response = (
            supabase.table("tenants.authored_document_editors")
            .select("*")
            .eq("document_id", str(document_id))
            .eq("editor_id", user_id)
            .eq("can_edit", True)
            .execute()
        )

        if not editor_response.data:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="You don't have permission to edit this document",
            )

        # Prepare update data
        update_data = {}
        if document.title is not None:
            update_data["title"] = document.title
        if document.content is not None:
            update_data["content"] = document.content
        if document.status is not None:
            update_data["status"] = document.status
        if document.variables_used is not None:
            update_data["variables_used"] = document.variables_used
        if document.metadata is not None:
            update_data["metadata"] = document.metadata

        update_data["updated_by"] = user_id
        update_data["updated_at"] = "now()"

        # Update the document
        response = (
            supabase.table("tenants.authored_documents")
            .update(update_data)
            .eq("id", str(document_id))
            .execute()
        )
        updated_document = response.data[0]

        # Check if the document should be embedded based on updates
        should_embed, embed_reason = should_embed_document(
            updated_document, document.metadata
        )

        # Queue for embedding if needed
        if should_embed:
            embedding_result = queue_authored_document_for_embedding(
                authored_document_id=str(document_id),
                tenant_id=tenant_id,
                user_id=user_id,
                document_data={
                    "title": updated_document.get("title"),
                    "case_id": updated_document.get("case_id"),
                    "client_id": updated_document.get("client_id"),
                    "status": document.status
                    if document.status
                    else updated_document.get("status", "draft"),
                    "metadata": updated_document.get("metadata"),
                },
                trigger_reason=embed_reason,
                source="api_update",
                supabase_client=supabase,
            )

            if embedding_result.get("status") == "error":
                # Log error but don't fail the request
                logger.error(
                    f"Error queueing document for embedding: "
                    f"{embedding_result.get('error')}"
                )

        return updated_document
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error updating document: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to update document: {str(e)}",
        )


@router.patch("/{document_id}/variables", response_model=DocumentResponse)
async def update_document_variables(
    request: Request, document_id: UUID4, variable_update: DocumentVariableUpdate
):
    """
    Update just the variables for a document.
    This is a more efficient endpoint when only variables need to be changed.
    """
    auth_data = get_auth_data(request)
    tenant_id = auth_data["tenant_id"]
    user_id = auth_data["user_id"]

    try:
        # First, check if the document exists and belongs to the tenant
        doc_response = (
            supabase.table("tenants.authored_documents")
            .select("*")
            .eq("id", str(document_id))
            .eq("tenant_id", tenant_id)
            .execute()
        )

        if not doc_response.data:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Document not found",
            )

        existing_document = doc_response.data[0]

        # Check if the user has edit permission
        editor_response = (
            supabase.table("tenants.authored_document_editors")
            .select("*")
            .eq("document_id", str(document_id))
            .eq("editor_id", user_id)
            .eq("can_edit", True)
            .execute()
        )

        if not editor_response.data:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="You don't have permission to edit this document",
            )

        # Merge existing variables with new ones
        current_variables = existing_document.get("variables_used") or {}
        updated_variables = {**current_variables, **variable_update.variables}

        # Update only the variables and timestamp
        update_data = {
            "variables_used": updated_variables,
            "updated_by": user_id,
            "updated_at": "now()",
        }

        # Update the document
        response = (
            supabase.table("tenants.authored_documents")
            .update(update_data)
            .eq("id", str(document_id))
            .execute()
        )
        updated_document = response.data[0]

        # Check if the document should be embedded based on variable updates
        should_embed = False
        embed_reason = None

        # Check if the document is in a final state that should trigger embedding
        # when variables change
        doc_status = existing_document.get("status")
        if doc_status in ["final", "approved", "published"]:
            should_embed = True
            embed_reason = f"Variables updated in '{doc_status}' document"

        # Check if embedding was explicitly requested in variables
        if variable_update.variables.get("should_embed", False):
            should_embed = True
            embed_reason = "Explicitly requested in variables update"

        # Queue for embedding if needed
        if should_embed:
            embedding_result = queue_authored_document_for_embedding(
                authored_document_id=str(document_id),
                tenant_id=tenant_id,
                user_id=user_id,
                document_data={
                    "title": updated_document.get("title"),
                    "case_id": updated_document.get("case_id"),
                    "client_id": updated_document.get("client_id"),
                    "status": updated_document.get("status", "draft"),
                    "metadata": updated_document.get("metadata"),
                    "variables_used": updated_variables,
                },
                trigger_reason=embed_reason,
                source="api_variable_update",
                supabase_client=supabase,
            )

            if embedding_result.get("status") == "error":
                # Log error but don't fail the request
                logger.error(
                    f"Error queueing document for embedding: "
                    f"{embedding_result.get('error')}"
                )

        return updated_document
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error updating document variables: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to update document variables: {str(e)}",
        )


@router.delete("/{document_id}", status_code=status.HTTP_204_NO_CONTENT)
async def delete_document(request: Request, document_id: UUID4):
    """Delete a document"""
    auth_data = get_auth_data(request)
    tenant_id = auth_data["tenant_id"]
    role = auth_data["role"]

    # Check if the user has permission to delete documents
    if role not in ["partner", "attorney"]:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Only partners and attorneys can delete documents",
        )

    try:
        # First, check if the document exists and belongs to the tenant
        check_response = (
            supabase.table("tenants.authored_documents")
            .select("*")
            .eq("id", str(document_id))
            .eq("tenant_id", tenant_id)
            .execute()
        )

        if not check_response.data:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Document not found",
            )

        # Delete associated records first (comments, approvals, editors, history)
        supabase.table("tenants.authored_document_comments").delete().eq(
            "document_id", str(document_id)
        ).execute()
        supabase.table("tenants.authored_document_approvals").delete().eq(
            "document_id", str(document_id)
        ).execute()
        supabase.table("tenants.authored_document_editors").delete().eq(
            "document_id", str(document_id)
        ).execute()
        supabase.table("tenants.authored_document_history").delete().eq(
            "document_id", str(document_id)
        ).execute()

        # Delete the document
        supabase.table("tenants.authored_documents").delete().eq(
            "id", str(document_id)
        ).execute()

        return None
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error deleting document: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to delete document: {str(e)}",
        )


# Document Comments Endpoints
@router.get("/{document_id}/comments", response_model=List[CommentResponse])
async def list_comments(request: Request, document_id: UUID4):
    """List comments for a document"""
    auth_data = get_auth_data(request)
    tenant_id = auth_data["tenant_id"]

    try:
        response = (
            supabase.table("tenants.authored_document_comments")
            .select("*")
            .eq("document_id", str(document_id))
            .execute()
        )

        return response.data
    except Exception as e:
        logger.error(f"Error listing comments: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to list comments: {str(e)}",
        )


@router.post(
    "/{document_id}/comments",
    response_model=CommentResponse,
    status_code=status.HTTP_201_CREATED,
)
async def create_comment(request: Request, document_id: UUID4, comment: CommentCreate):
    """Add a comment to a document"""
    auth_data = get_auth_data(request)
    tenant_id = auth_data["tenant_id"]
    user_id = auth_data["user_id"]

    try:
        # First, check if the document exists and belongs to the tenant
        doc_response = (
            supabase.table("tenants.authored_documents")
            .select("*")
            .eq("id", str(document_id))
            .eq("tenant_id", tenant_id)
            .execute()
        )

        if not doc_response.data:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Document not found",
            )

        # Check if the user has comment permission
        editor_response = (
            supabase.table("tenants.authored_document_editors")
            .select("*")
            .eq("document_id", str(document_id))
            .eq("editor_id", user_id)
            .eq("can_comment", True)
            .execute()
        )

        if not editor_response.data:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="You don't have permission to comment on this document",
            )

        comment_data = {
            "tenant_id": tenant_id,
            "document_id": str(document_id),
            "content": comment.content,
            "position": comment.position,
            "parent_id": str(comment.parent_id) if comment.parent_id else None,
            "author_id": user_id,
            "resolved": False,
            "metadata": comment.metadata,
        }

        response = (
            supabase.table("tenants.authored_document_comments")
            .insert(comment_data)
            .execute()
        )

        return response.data[0]
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error creating comment: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to create comment: {str(e)}",
        )


@router.put("/{document_id}/comments/{comment_id}", response_model=CommentResponse)
async def update_comment(
    request: Request, document_id: UUID4, comment_id: UUID4, comment: CommentUpdate
):
    """Update a comment on a document"""
    auth_data = get_auth_data(request)
    tenant_id = auth_data["tenant_id"]
    user_id = auth_data["user_id"]
    role = auth_data["role"]

    try:
        # First, check if the comment exists and belongs to the tenant
        comment_response = (
            supabase.table("tenants.authored_document_comments")
            .select("*")
            .eq("id", str(comment_id))
            .eq("document_id", str(document_id))
            .eq("tenant_id", tenant_id)
            .execute()
        )

        if not comment_response.data:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Comment not found",
            )

        existing_comment = comment_response.data[0]

        # Check permissions - authors can edit their own comments
        is_author = existing_comment["author_id"] == user_id
        can_resolve = role in [
            "partner",
            "attorney",
        ]  # Only partners and attorneys can resolve comments

        if not is_author and not can_resolve:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="You don't have permission to update this comment",
            )

        # Prepare update data
        update_data = {}
        if comment.content is not None and is_author:  # Only author can change content
            update_data["content"] = comment.content
        if (
            comment.resolved is not None and can_resolve
        ):  # Only authorized roles can resolve
            update_data["resolved"] = comment.resolved
            if comment.resolved:
                update_data["resolved_by"] = user_id
                update_data["resolved_at"] = "now()"
        if (
            comment.metadata is not None and is_author
        ):  # Only author can change metadata
            update_data["metadata"] = comment.metadata

        update_data["updated_at"] = "now()"

        response = (
            supabase.table("tenants.authored_document_comments")
            .update(update_data)
            .eq("id", str(comment_id))
            .execute()
        )

        return response.data[0]
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error updating comment: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to update comment: {str(e)}",
        )


@router.delete(
    "/{document_id}/comments/{comment_id}", status_code=status.HTTP_204_NO_CONTENT
)
async def delete_comment(request: Request, document_id: UUID4, comment_id: UUID4):
    """Delete a comment from a document"""
    auth_data = get_auth_data(request)
    tenant_id = auth_data["tenant_id"]
    user_id = auth_data["user_id"]
    role = auth_data["role"]

    try:
        # First, check if the comment exists and belongs to the tenant
        comment_response = (
            supabase.table("tenants.authored_document_comments")
            .select("*")
            .eq("id", str(comment_id))
            .eq("document_id", str(document_id))
            .eq("tenant_id", tenant_id)
            .execute()
        )

        if not comment_response.data:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Comment not found",
            )

        existing_comment = comment_response.data[0]

        # Check permissions - only the author or a partner/attorney can delete
        is_author = existing_comment["author_id"] == user_id
        can_delete = role in ["partner", "attorney"]

        if not is_author and not can_delete:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="You don't have permission to delete this comment",
            )

        # Delete the comment
        supabase.table("tenants.authored_document_comments").delete().eq(
            "id", str(comment_id)
        ).execute()

        return None
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error deleting comment: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to delete comment: {str(e)}",
        )


# Document Approval Endpoints
@router.get("/{document_id}/approvals", response_model=List[ApprovalResponse])
async def list_approvals(request: Request, document_id: UUID4):
    """List approval requests for a document"""
    auth_data = get_auth_data(request)
    tenant_id = auth_data["tenant_id"]

    try:
        response = (
            supabase.table("tenants.authored_document_approvals")
            .select("*")
            .eq("document_id", str(document_id))
            .eq("tenant_id", tenant_id)
            .execute()
        )

        return response.data
    except Exception as e:
        logger.error(f"Error listing approvals: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to list approvals: {str(e)}",
        )


@router.post(
    "/{document_id}/approvals",
    response_model=ApprovalResponse,
    status_code=status.HTTP_201_CREATED,
)
async def request_approval(
    request: Request, document_id: UUID4, approval: ApprovalCreate
):
    """Request approval for a document"""
    auth_data = get_auth_data(request)
    tenant_id = auth_data["tenant_id"]
    user_id = auth_data["user_id"]
    role = auth_data["role"]

    # Check if the user has permission to request approvals
    if role not in ["partner", "attorney", "paralegal", "staff"]:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Only staff members can request approvals",
        )

    try:
        # First, check if the document exists and belongs to the tenant
        doc_response = (
            supabase.table("tenants.authored_documents")
            .select("*")
            .eq("id", str(document_id))
            .eq("tenant_id", tenant_id)
            .execute()
        )

        if not doc_response.data:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Document not found",
            )

        # Create the approval request
        approval_data = {
            "tenant_id": tenant_id,
            "document_id": str(document_id),
            "approver_id": str(approval.approver_id),
            "requester_id": user_id,
            "status": "pending",
            "comments": approval.comments,
            "metadata": approval.metadata,
        }

        response = (
            supabase.table("tenants.authored_document_approvals")
            .insert(approval_data)
            .execute()
        )

        return response.data[0]
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error requesting approval: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to request approval: {str(e)}",
        )


@router.put("/{document_id}/approvals/{approval_id}", response_model=ApprovalResponse)
async def update_approval(
    request: Request, document_id: UUID4, approval_id: UUID4, approval: ApprovalUpdate
):
    """Update an approval request (approve or reject)"""
    auth_data = get_auth_data(request)
    tenant_id = auth_data["tenant_id"]
    user_id = auth_data["user_id"]

    try:
        # First, check if the approval exists and belongs to the tenant
        approval_response = (
            supabase.table("tenants.authored_document_approvals")
            .select("*")
            .eq("id", str(approval_id))
            .eq("document_id", str(document_id))
            .eq("tenant_id", tenant_id)
            .execute()
        )

        if not approval_response.data:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Approval request not found",
            )

        existing_approval = approval_response.data[0]

        # Check if the current user is the approver
        if existing_approval["approver_id"] != user_id:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Only the assigned approver can update this approval",
            )

        # Validate status
        if approval.status not in ["approved", "rejected"]:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Status must be 'approved' or 'rejected'",
            )

        # Prepare update data
        update_data = {"status": approval.status, "updated_at": "now()"}

        if approval.comments is not None:
            update_data["comments"] = approval.comments
        if approval.metadata is not None:
            update_data["metadata"] = approval.metadata

        response = (
            supabase.table("tenants.authored_document_approvals")
            .update(update_data)
            .eq("id", str(approval_id))
            .execute()
        )

        # If approved, update the document status if all approvals are complete
        if approval.status == "approved":
            # Check if all required approvals are approved
            all_approvals = (
                supabase.table("tenants.authored_document_approvals")
                .select("*")
                .eq("document_id", str(document_id))
                .execute()
                .data
            )
            all_approved = all(a["status"] == "approved" for a in all_approvals)

            if all_approved:
                # Update document status to "approved"
                supabase.table("tenants.authored_documents").update(
                    {"status": "approved", "updated_at": "now()", "updated_by": user_id}
                ).eq("id", str(document_id)).execute()

        return response.data[0]
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error updating approval: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to update approval: {str(e)}",
        )


@router.delete(
    "/{document_id}/approvals/{approval_id}", status_code=status.HTTP_204_NO_CONTENT
)
async def delete_approval(request: Request, document_id: UUID4, approval_id: UUID4):
    """Delete an approval request"""
    auth_data = get_auth_data(request)
    tenant_id = auth_data["tenant_id"]
    user_id = auth_data["user_id"]
    role = auth_data["role"]

    # Only partners and attorneys can delete approval requests
    if role not in ["partner", "attorney"]:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Only partners and attorneys can delete approval requests",
        )

    try:
        # First, check if the approval exists and belongs to the tenant
        approval_response = (
            supabase.table("tenants.authored_document_approvals")
            .select("*")
            .eq("id", str(approval_id))
            .eq("document_id", str(document_id))
            .eq("tenant_id", tenant_id)
            .execute()
        )

        if not approval_response.data:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Approval request not found",
            )

        # Delete the approval
        supabase.table("tenants.authored_document_approvals").delete().eq(
            "id", str(approval_id)
        ).execute()

        return None
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error deleting approval: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to delete approval: {str(e)}",
        )


# Document Version History Endpoints
@router.get("/{document_id}/history", response_model=List[VersionHistoryResponse])
async def list_version_history(request: Request, document_id: UUID4):
    """List version history for a document"""
    auth_data = get_auth_data(request)
    tenant_id = auth_data["tenant_id"]

    try:
        # Call the function to get document version history
        query = f"""
        SELECT * FROM tenants.get_document_version_history('{document_id}')
        """
        response = supabase.rpc(
            "get_document_version_history", {"p_document_id": str(document_id)}
        ).execute()

        return response.data
    except Exception as e:
        logger.error(f"Error listing version history: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to list version history: {str(e)}",
        )


@router.get("/{document_id}/history/{version_id}", response_model=dict)
async def get_version_content(request: Request, document_id: UUID4, version_id: UUID4):
    """Get a specific version of a document"""
    auth_data = get_auth_data(request)
    tenant_id = auth_data["tenant_id"]

    try:
        # Call the function to get document version
        response = supabase.rpc(
            "get_document_version", {"p_version_id": str(version_id)}
        ).execute()

        if not response.data:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Version not found",
            )

        return response.data[0]
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error retrieving version: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to retrieve version: {str(e)}",
        )


@router.get("/{document_id}/history/version/{version_number}", response_model=str)
async def reconstruct_version(
    request: Request, document_id: UUID4, version_number: int
):
    """Reconstruct a document at a specific version"""
    auth_data = get_auth_data(request)
    tenant_id = auth_data["tenant_id"]

    try:
        # Call the function to reconstruct document version
        response = supabase.rpc(
            "reconstruct_document_version",
            {"p_document_id": str(document_id), "p_version_number": version_number},
        ).execute()

        if not response.data:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"Version {version_number} not found for document {document_id}",
            )

        return response.data
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error reconstructing version: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to reconstruct version: {str(e)}",
        )


# Document Editors Endpoints
@router.get("/{document_id}/editors", response_model=List[dict])
async def list_editors(request: Request, document_id: UUID4):
    """List editors for a document"""
    auth_data = get_auth_data(request)
    tenant_id = auth_data["tenant_id"]

    try:
        response = (
            supabase.table("tenants.authored_document_editors")
            .select("*")
            .eq("document_id", str(document_id))
            .eq("tenant_id", tenant_id)
            .execute()
        )

        return response.data
    except Exception as e:
        logger.error(f"Error listing editors: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to list editors: {str(e)}",
        )


@router.post(
    "/{document_id}/editors", response_model=dict, status_code=status.HTTP_201_CREATED
)
async def add_editor(request: Request, document_id: UUID4, editor_data: dict):
    """Add an editor to a document"""
    auth_data = get_auth_data(request)
    tenant_id = auth_data["tenant_id"]
    user_id = auth_data["user_id"]
    role = auth_data["role"]

    # Check if the user has permission to add editors
    if role not in ["partner", "attorney"]:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Only partners and attorneys can add editors",
        )

    try:
        # First, check if the document exists and belongs to the tenant
        doc_response = (
            supabase.table("tenants.authored_documents")
            .select("*")
            .eq("id", str(document_id))
            .eq("tenant_id", tenant_id)
            .execute()
        )

        if not doc_response.data:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Document not found",
            )

        # Check if the editor is already added
        editor_id = editor_data.get("editor_id")
        if not editor_id:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="editor_id is required",
            )

        existing_editor = (
            supabase.table("tenants.authored_document_editors")
            .select("*")
            .eq("document_id", str(document_id))
            .eq("editor_id", editor_id)
            .execute()
            .data
        )

        if existing_editor:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"User {editor_id} is already an editor for this document",
            )

        # Add the editor
        new_editor_data = {
            "tenant_id": tenant_id,
            "document_id": str(document_id),
            "editor_id": editor_id,
            "can_edit": editor_data.get("can_edit", True),
            "can_comment": editor_data.get("can_comment", True),
            "can_approve": editor_data.get(
                "can_approve", role in ["partner", "attorney"]
            ),
        }

        response = (
            supabase.table("tenants.authored_document_editors")
            .insert(new_editor_data)
            .execute()
        )

        return response.data[0]
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error adding editor: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to add editor: {str(e)}",
        )


@router.put("/{document_id}/editors/{editor_id}", response_model=dict)
async def update_editor_permissions(
    request: Request, document_id: UUID4, editor_id: UUID4, permissions: dict
):
    """Update editor permissions"""
    auth_data = get_auth_data(request)
    tenant_id = auth_data["tenant_id"]
    user_id = auth_data["user_id"]
    role = auth_data["role"]

    # Check if the user has permission to update editor permissions
    if role not in ["partner", "attorney"]:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Only partners and attorneys can update editor permissions",
        )

    try:
        # First, check if the editor exists for this document
        editor_response = (
            supabase.table("tenants.authored_document_editors")
            .select("*")
            .eq("document_id", str(document_id))
            .eq("editor_id", str(editor_id))
            .eq("tenant_id", tenant_id)
            .execute()
        )

        if not editor_response.data:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Editor not found for this document",
            )

        # Update permissions
        update_data = {}
        if "can_edit" in permissions:
            update_data["can_edit"] = permissions["can_edit"]
        if "can_comment" in permissions:
            update_data["can_comment"] = permissions["can_comment"]
        if "can_approve" in permissions:
            update_data["can_approve"] = permissions["can_approve"]

        response = (
            supabase.table("tenants.authored_document_editors")
            .update(update_data)
            .eq("document_id", str(document_id))
            .eq("editor_id", str(editor_id))
            .execute()
        )

        return response.data[0]
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error updating editor permissions: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to update editor permissions: {str(e)}",
        )


@router.delete(
    "/{document_id}/editors/{editor_id}", status_code=status.HTTP_204_NO_CONTENT
)
async def remove_editor(request: Request, document_id: UUID4, editor_id: UUID4):
    """Remove an editor from a document"""
    auth_data = get_auth_data(request)
    tenant_id = auth_data["tenant_id"]
    role = auth_data["role"]

    # Check if the user has permission to remove editors
    if role not in ["partner", "attorney"]:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Only partners and attorneys can remove editors",
        )

    try:
        # First, check if the editor exists for this document
        editor_response = (
            supabase.table("tenants.authored_document_editors")
            .select("*")
            .eq("document_id", str(document_id))
            .eq("editor_id", str(editor_id))
            .eq("tenant_id", tenant_id)
            .execute()
        )

        if not editor_response.data:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Editor not found for this document",
            )

        # Remove the editor
        supabase.table("tenants.authored_document_editors").delete().eq(
            "document_id", str(document_id)
        ).eq("editor_id", str(editor_id)).execute()

        return None
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error removing editor: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to remove editor: {str(e)}",
        )


# Register the router in the main app
def register_document_routes(app):
    app.include_router(router)
