import { createClient } from '@supabase/supabase-js';
import { Database } from '../src/lib/supabase/database.types';
import * as dotenv from 'dotenv';

// Load environment variables from .env.local
dotenv.config({ path: '.env.local' });

// Create a Supabase client with service role key
const supabase = createClient<Database>(
  process.env.NEXT_PUBLIC_SUPABASE_URL || '',
  process.env.SUPABASE_SERVICE_ROLE_KEY || ''
);

/**
 * Main function to set up test users and subscriptions
 */
async function setupTestUsers() {
  try {
    console.log('Setting up test users and subscriptions...');

    // 1. Set up subscription plans if they don't exist
    await setupSubscriptionPlans();

    // 2. Set up subscription add-ons if they don't exist
    await setupSubscriptionAddons();

    // 3. Set up main test user (<EMAIL>)
    await setupMainUser();

    // 4. Set up additional test users with different subscription states
    await setupAdditionalUsers();

    console.log('Test users and subscriptions set up successfully!');
  } catch (error) {
    console.error('Error setting up test users and subscriptions:', error);
  }
}

/**
 * Set up subscription plans
 */
async function setupSubscriptionPlans() {
  console.log('Setting up subscription plans...');

  const plans = [
    {
      name: 'Basic',
      code: 'basic',
      description: 'Basic plan for small firms',
      is_active: true,
      is_public: true,
      base_price_monthly: 49.99,
      base_price_yearly: 499.99,
      features: {
        maxUsers: 5,
        maxCases: 100,
        maxDocuments: 1000,
        hasIntakeAgent: false,
        hasAdvancedAnalytics: false,
        hasPrioritySupport: false,
        basic_access: true,
      },
    },
    {
      name: 'Pro',
      code: 'pro',
      description: 'Professional plan for medium-sized firms',
      is_active: true,
      is_public: true,
      base_price_monthly: 99.99,
      base_price_yearly: 999.99,
      features: {
        maxUsers: 15,
        maxCases: 500,
        maxDocuments: 5000,
        hasIntakeAgent: true,
        hasAdvancedAnalytics: false,
        hasPrioritySupport: false,
        basic_access: true,
      },
    },
    {
      name: 'Enterprise',
      code: 'enterprise',
      description: 'Enterprise plan for large firms',
      is_active: true,
      is_public: true,
      base_price_monthly: 199.99,
      base_price_yearly: 1999.99,
      features: {
        maxUsers: 50,
        maxCases: 2000,
        maxDocuments: 20000,
        hasIntakeAgent: true,
        hasAdvancedAnalytics: true,
        hasPrioritySupport: true,
        basic_access: true,
        enterprise_access: true,
      },
    },
  ];

  // Check if plans already exist
  const { data: existingPlans, error: plansError } = await supabase
    .from('subscription_plans' as any)
    .select('code');

  if (plansError) {
    throw plansError;
  }

const existingPlanCodes = existingPlans ? existingPlans.map((plan: any) => plan.code) : [];

  // Insert plans that don't already exist
  for (const plan of plans) {
    if (!existingPlanCodes.includes(plan.code)) {
      const { error } = await supabase
        .from('subscription_plans' as any)
        .insert(plan);

      if (error) {
        console.error(`Error creating plan ${plan.code}:`, error);
      } else {
        console.log(`Created plan: ${plan.name}`);
      }
    } else {
      console.log(`Plan ${plan.code} already exists, skipping...`);
    }
  }
}

/**
 * Set up subscription add-ons
 */
async function setupSubscriptionAddons() {
  console.log('Setting up subscription add-ons...');

  const addons = [
    {
      name: 'Additional User Seat',
      code: 'extra_user',
      description: 'Allows one additional user account.',
      is_active: true,
      is_public: true,
      price_monthly: 9.99,
      price_yearly: 99.99,
      type: 'per_unit',
    },
    {
      name: 'Advanced Analytics Module',
      code: 'analytics_module',
      description: 'Unlocks advanced reporting and analytics features.',
      is_active: true,
      is_public: true,
      price_monthly: 29.99,
      price_yearly: 299.99,
      type: 'boolean',
    },
    {
      name: 'Priority Support',
      code: 'priority_support',
      description: 'Access to priority support channels.',
      is_active: true,
      is_public: true,
      price_monthly: 19.99,
      price_yearly: 199.99,
      type: 'boolean',
    },
  ];

  // Check if add-ons already exist
  const { data: existingAddons, error: addonsError } = await supabase
    .from('subscription_addons' as any)
    .select('code');

  if (addonsError) {
    throw addonsError;
  }

const existingAddonCodes = existingAddons ? existingAddons.map((addon: any) => addon.code) : [];

  // Insert add-ons that don't already exist
  for (const addon of addons) {
    if (!existingAddonCodes.includes(addon.code)) {
      const { error } = await supabase
        .from('subscription_addons' as any)
        .insert(addon);

      if (error) {
        console.error(`Error creating add-on ${addon.code}:`, error);
      } else {
        console.log(`Created add-on: ${addon.name}`);
      }
    } else {
      console.log(`Add-on ${addon.code} already exists, skipping...`);
    }
  }
}

/**
 * Set up main test user (<EMAIL>)
 */
async function setupMainUser() {
  console.log('Setting up main test user (<EMAIL>)...');

  // Check if the user exists
  const { data: users, error: userError } = await supabase
    .from('users' as any)
    .select('id, email')
    .eq('email', '<EMAIL>')
    .limit(1);

  if (userError) {
    console.error('Error checking if main user exists:', userError);
    return;
  }

  if (!users || users.length === 0) {
    console.error('Main user (<EMAIL>) not found. Please create this user manually.');
    return;
  }

  const user = users[0];
  // @ts-expect-error - Supabase query result type
console.log('Main user found:', user.id);

  // Get the tenant ID from the user's metadata
  const tenantId = (user as any).tenant_id;

  if (!tenantId) {
    console.error('Main user does not have a tenant ID. Please set up the tenant ID manually.');
    return;
  }

  console.log('Main user tenant ID:', tenantId);

  // Check if the tenant exists
  const { data: tenant, error: tenantError } = await supabase
    .from('firms' as any)
    .select('*')
    .eq('id', tenantId)
    .single();

  if (tenantError) {
    console.error('Error checking if tenant exists:', tenantError);
    return;
  }

  if (!tenant) {
    console.error('Tenant not found for main user. Please set up the tenant manually.');
    return;
  }

  // @ts-expect-error - Supabase query result type
console.log('Main user tenant found:', tenant.name);

  // Set up subscription for the main user's tenant
  await setupSubscription(tenantId, {
    planCode: 'pro',
    status: 'active',
    billingCycle: 'monthly',
  });

  // Generate usage data for the main user's tenant
  await generateUsageData(tenantId);
}

/**
 * Set up additional test users with different subscription states
 */
async function setupAdditionalUsers() {
  console.log('Setting up additional test users...');

  const testUsers = [
    {
      email: '<EMAIL>',
      password: 'password123',
      fullName: 'Trial User',
      tenantName: 'Trial Firm',
      tenantSlug: 'trial-firm',
      role: 'admin',
      subscription: {
        planCode: 'basic',
        status: 'trialing',
        billingCycle: 'monthly',
        trialEnd: new Date(Date.now() + (14 * 24 * 60 * 60 * 1000)), // 14 days from now
      },
    },
    {
      email: '<EMAIL>',
      password: 'password123',
      fullName: 'Canceled User',
      tenantName: 'Canceled Firm',
      tenantSlug: 'canceled-firm',
      role: 'admin',
      subscription: {
        planCode: 'basic',
        status: 'canceled',
        billingCycle: 'monthly',
        canceledAt: new Date(),
      },
    },
    {
      email: '<EMAIL>',
      password: 'password123',
      fullName: 'No Subscription User',
      tenantName: 'No Subscription Firm',
      tenantSlug: 'no-subscription-firm',
      role: 'admin',
      subscription: null,
    },
    {
      email: '<EMAIL>',
      password: 'password123',
      fullName: 'Approaching Quota User',
      tenantName: 'Approaching Quota Firm',
      tenantSlug: 'approaching-quota-firm',
      role: 'admin',
      subscription: {
        planCode: 'basic',
        status: 'active',
        billingCycle: 'monthly',
      },
      usagePercentage: 80,
    },
    {
      email: '<EMAIL>',
      password: 'password123',
      fullName: 'Exceeded Quota User',
      tenantName: 'Exceeded Quota Firm',
      tenantSlug: 'exceeded-quota-firm',
      role: 'admin',
      subscription: {
        planCode: 'basic',
        status: 'active',
        billingCycle: 'monthly',
      },
      usagePercentage: 110,
    },
  ];

  for (const testUser of testUsers) {
    await createTestUser(testUser);
  }
}

/**
 * Create a test user with tenant and subscription
 */
async function createTestUser(userData: {
  email: string;
  password: string;
  fullName: string;
  tenantName: string;
  tenantSlug: string;
  role: string;
  subscription: {
    planCode: string;
    status: string;
    billingCycle: string;
    trialEnd?: Date;
    canceledAt?: Date;
  } | null;
  usagePercentage?: number;
}) {
  console.log(`Setting up test user: ${userData.email}...`);

  // Check if the user already exists
  const { data: users, error: userError } = await supabase
    .from('users' as any)
    .select('id, email')
    .eq('email', userData.email)
    .limit(1);

  if (userError) {
    console.error(`Error checking if user ${userData.email} exists:`, userError);
    return;
  }

  let userId: string;
  let tenantId: string;

  if (users && users.length > 0) {
    console.log(`User ${userData.email} already exists, using existing user.`);
    // @ts-expect-error - Supabase query result type
userId = users[0].id;
    tenantId = (users[0] as any).tenant_id;

    if (!tenantId) {
      console.error(`User ${userData.email} does not have a tenant ID. Please set up the tenant ID manually.`);
      return;
    }
  } else {
    // Create tenant
    const { data: tenant, error: tenantError } = await supabase
      .from('firms' as any)
      .insert({
        name: userData.tenantName,
        slug: userData.tenantSlug,
        country: 'US',
        state: 'TX',
        payment_provider_customer_id: `cus_${userData.tenantSlug}`,
      })
      .select()
      .single();

    if (tenantError) {
      console.error(`Error creating tenant for user ${userData.email}:`, tenantError);
      return;
    }

    // @ts-expect-error - Supabase query result type
tenantId = tenant.id;
    console.log(`Created tenant: ${userData.tenantName} (${tenantId})`);

    // Create user
    const { data: newUser, error: createError } = await supabase.auth.admin.createUser({
      email: userData.email,
      password: userData.password,
      email_confirm: true,
      app_metadata: {
        tenant_id: tenantId,
        role: userData.role,
      },
    });

    if (createError) {
      console.error(`Error creating user ${userData.email}:`, createError);
      return;
    }

    userId = newUser.user.id;
    console.log(`Created user: ${userData.email} (${userId})`);

    // Create profile
    const { error: profileError } = await supabase
      .from('profiles' as any)
      .insert({
        id: userId,
        email: userData.email,
        tenant_id: tenantId,
        role: userData.role,
        full_name: userData.fullName,
        notification_preferences: {
          emailEnabled: true,
          smsEnabled: true,
          inAppEnabled: true,
        },
      });

    if (profileError) {
      console.error(`Error creating profile for user ${userData.email}:`, profileError);
    }
  }

  // Set up subscription if provided
  if (userData.subscription) {
    await setupSubscription(tenantId, userData.subscription);
  }

  // Generate usage data if percentage is provided
  if (userData.usagePercentage) {
    await generateUsageData(tenantId, userData.usagePercentage);
  }
}

/**
 * Set up a subscription for a tenant
 */
async function setupSubscription(tenantId: string, subscriptionData: {
  planCode: string;
  status: string;
  billingCycle: string;
  trialEnd?: Date;
  canceledAt?: Date;
}) {
  console.log(`Setting up subscription for tenant ${tenantId}...`);

  // Get the plan ID
  const { data: plan, error: planError } = await supabase
    .from('subscription_plans' as any)
    .select('id')
    .eq('code', subscriptionData.planCode)
    .single();

  if (planError) {
    console.error(`Error getting plan ${subscriptionData.planCode}:`, planError);
    return;
  }

  // @ts-expect-error - Supabase query result type
const planId = plan.id;

  // Check if subscription already exists
  const { data: existingSubscription, error: subError } = await supabase
    .from('tenants.tenant_subscriptions' as any)
    .select('id')
    .eq('tenant_id', tenantId)
    .maybeSingle();

  if (subError) {
    console.error(`Error checking if subscription exists for tenant ${tenantId}:`, subError);
    return;
  }

  const now = new Date();
  const currentPeriodStart = new Date(now.getFullYear(), now.getMonth(), 1);
  const currentPeriodEnd = new Date(now.getFullYear(), now.getMonth() + 1, 0, 23, 59, 59, 999);

  if (existingSubscription) {
    // Update existing subscription
    const { error: updateError } = await supabase
      .from('tenants.tenant_subscriptions' as any)
      .update({
        plan_id: planId,
        status: subscriptionData.status,
        billing_cycle: subscriptionData.billingCycle,
        trial_end: subscriptionData.trialEnd,
        current_period_start: currentPeriodStart.toISOString(),
        current_period_end: currentPeriodEnd.toISOString(),
        canceled_at: subscriptionData.canceledAt,
        payment_provider_subscription_id: `sub_${tenantId}`,
        updated_at: now.toISOString(),
      })
      // @ts-expect-error - Supabase query result type
      .eq('id', existingSubscription.id);

    if (updateError) {
      console.error(`Error updating subscription for tenant ${tenantId}:`, updateError);
    } else {
      console.log(`Updated subscription for tenant: ${tenantId}`);
    }
  } else {
    // Create new subscription
    const { error: createError } = await supabase
      .from('tenants.tenant_subscriptions' as any)
      .insert({
        tenant_id: tenantId,
        plan_id: planId,
        status: subscriptionData.status,
        billing_cycle: subscriptionData.billingCycle,
        trial_end: subscriptionData.trialEnd,
        current_period_start: currentPeriodStart.toISOString(),
        current_period_end: currentPeriodEnd.toISOString(),
        canceled_at: subscriptionData.canceledAt,
        payment_provider: 'stripe',
        payment_provider_subscription_id: `sub_${tenantId}`,
        created_at: now.toISOString(),
        updated_at: now.toISOString(),
      });

    if (createError) {
      console.error(`Error creating subscription for tenant ${tenantId}:`, createError);
    } else {
      console.log(`Created subscription for tenant: ${tenantId}`);
    }
  }

  // Set up tenant quotas
  await setupTenantQuota(tenantId, subscriptionData.planCode);
}

/**
 * Set up tenant quota
 */
async function setupTenantQuota(tenantId: string, planCode: string) {
  // Define quota limits based on plan
  let quotaLimits = {
    max_daily_uploads: 10,
    max_monthly_uploads: 100,
    max_document_size_mb: 10,
    max_concurrent_processing: 5,
    plan_tier: 'basic',
  };

  if (planCode === 'pro') {
    quotaLimits = {
      max_daily_uploads: 50,
      max_monthly_uploads: 500,
      max_document_size_mb: 50,
      max_concurrent_processing: 10,
      plan_tier: 'pro',
    };
  } else if (planCode === 'enterprise') {
    quotaLimits = {
      max_daily_uploads: 100,
      max_monthly_uploads: 1000,
      max_document_size_mb: 100,
      max_concurrent_processing: 20,
      plan_tier: 'enterprise',
    };
  }

  // Check if quota already exists
  const { data: existingQuota, error: quotaError } = await supabase
    .from('tenant_quotas' as any)
    .select('tenant_id')
    .eq('tenant_id', tenantId)
    .maybeSingle();

  if (quotaError) {
    console.error(`Error checking if quota exists for tenant ${tenantId}:`, quotaError);
    return;
  }

  if (existingQuota) {
    // Update existing quota
    const { error: updateError } = await supabase
      .from('tenant_quotas' as any)
      .update({
        ...quotaLimits,
        updated_at: new Date().toISOString(),
      })
      .eq('tenant_id', tenantId);

    if (updateError) {
      console.error(`Error updating quota for tenant ${tenantId}:`, updateError);
    } else {
      console.log(`Updated quota for tenant: ${tenantId}`);
    }
  } else {
    // Create new quota
    const { error: createError } = await supabase
      .from('tenant_quotas' as any)
      .insert({
        tenant_id: tenantId,
        ...quotaLimits,
        created_at: new Date().toISOString(),
      });

    if (createError) {
      console.error(`Error creating quota for tenant ${tenantId}:`, createError);
    } else {
      console.log(`Created quota for tenant: ${tenantId}`);
    }
  }
}

/**
 * Generate usage data for a tenant
 */
async function generateUsageData(tenantId: string, usagePercentage: number = 50) {
  console.log(`Generating usage data for tenant ${tenantId} (${usagePercentage}%)...`);

  // Get the tenant's quota
  const { data: quota, error: quotaError } = await supabase
    .from('tenant_quotas' as any)
    .select('*')
    .eq('tenant_id', tenantId)
    .single();

  if (quotaError) {
    console.error(`Error getting quota for tenant ${tenantId}:`, quotaError);
    return;
  }

  if (!quota) {
    console.error(`No quota found for tenant ${tenantId}. Please set up the quota first.`);
    return;
  }

  // Calculate usage based on percentage
const documentUploadUsage = Math.floor(((quota as any).max_monthly_uploads * usagePercentage) / 100);
  const documentSizeBytes = documentUploadUsage * 1024 * 1024; // 1 MB per upload

  const now = new Date();
  const periodStart = new Date(now.getFullYear(), now.getMonth(), 1);
  const periodEnd = new Date(now.getFullYear(), now.getMonth() + 1, 0, 23, 59, 59, 999);

  // Set up usage data for different resource types
  const usageData = [
    {
      usage_type: 'document_upload',
      usage_count: documentUploadUsage,
      resource_size_bytes: documentSizeBytes,
    },
    {
      usage_type: 'document_processing',
      usage_count: Math.floor(documentUploadUsage * 0.8), // 80% of uploads are processed
      resource_size_bytes: null,
    },
    {
      usage_type: 'api_calls',
      usage_count: documentUploadUsage * 5, // 5 API calls per upload
      resource_size_bytes: null,
    },
    {
      usage_type: 'storage_usage',
      usage_count: 1,
      resource_size_bytes: documentSizeBytes,
    },
  ];

  // Create or update usage data
  for (const usage of usageData) {
    // Check if usage data already exists
    const { data: existingUsage, error: usageError } = await supabase
      .from('resource_usage' as any)
      .select('id')
      .eq('tenant_id', tenantId)
      .eq('usage_type', usage.usage_type)
      .gte('period_start', periodStart.toISOString())
      .lte('period_end', periodEnd.toISOString())
      .maybeSingle();

    if (usageError) {
      console.error(`Error checking if usage data exists for tenant ${tenantId}:`, usageError);
      continue;
    }

    if (existingUsage) {
      // Update existing usage data
      const { error: updateError } = await supabase
        .from('resource_usage' as any)
        .update({
          usage_count: usage.usage_count,
          resource_size_bytes: usage.resource_size_bytes,
        })
        // @ts-expect-error - Supabase query result type
        .eq('id', existingUsage.id);

      if (updateError) {
        console.error(`Error updating usage data for tenant ${tenantId}:`, updateError);
      } else {
        console.log(`Updated usage data for tenant: ${tenantId}, type: ${usage.usage_type}`);
      }
    } else {
      // Create new usage data
      const { error: createError } = await supabase
        .from('resource_usage' as any)
        .insert({
          tenant_id: tenantId,
          usage_type: usage.usage_type,
          usage_count: usage.usage_count,
          resource_size_bytes: usage.resource_size_bytes,
          period_start: periodStart.toISOString(),
          period_end: periodEnd.toISOString(),
          created_at: now.toISOString(),
        });

      if (createError) {
        console.error(`Error creating usage data for tenant ${tenantId}:`, createError);
      } else {
        console.log(`Created usage data for tenant: ${tenantId}, type: ${usage.usage_type}`);
      }
    }
  }
}

// Run the setup
setupTestUsers().catch(console.error);
