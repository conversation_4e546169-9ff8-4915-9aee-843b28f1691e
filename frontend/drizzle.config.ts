import { defineConfig } from 'drizzle-kit';
import 'dotenv/config';
import type { Config } from 'drizzle-kit';

// This configuration is used for drizzle-kit commands
export default defineConfig({
  schema: './src/lib/drizzle/schema/**/*.ts', // Directory where schema files are located
  out: './drizzle', // Output directory for migrations
  dialect: 'postgresql',
  dbCredentials: {
    host: process.env.DB_HOST || 'localhost',
    port: Number(process.env.DB_PORT) || 5432,
    user: process.env.DB_USER || 'postgres',
    password: process.env.DB_PASSWORD || '',
    database: process.env.DB_NAME || 'postgres',
  },
  // Optional: customize settings
  verbose: true,
  strict: true,
}) as Config;
