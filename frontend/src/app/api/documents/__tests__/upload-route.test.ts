import { NextRequest, NextResponse } from 'next/server';
import { AuthUser } from '@/lib/auth-helpers';
import { UserRole } from '@/lib/types/auth';
import { SupabaseClient } from '@supabase/supabase-js';
import { Database } from '@/lib/supabase/database.types'; // Import Database type

// Helper to create a basic mock JWT (unsigned)
const createMockJwt = (payload: object): string => {
  const header = Buffer.from(JSON.stringify({ alg: 'none', typ: 'JWT' })).toString('base64url');
  const body = Buffer.from(JSON.stringify(payload)).toString('base64url');
  return `${header}.${body}.`;
};

// --- Global Mocks/Spies ---
const jsonSpy = jest.spyOn(NextResponse, 'json').mockImplementation((body: any, init?: ResponseInit) => {
  return {
    status: init?.status || 200,
    json: jest.fn().mockResolvedValue(body),
    headers: new Headers(init?.headers),
  } as any;
});

// --- Define Mock User (used in mockSupabase) ---
const mockUser: AuthUser = {
  id: 'user-456',
  tenantId: 'tenant-xyz',
  role: UserRole.Partner,
  email: '<EMAIL>',
};

// --- Define Mock Supabase Client Structure (used by mock factory) ---
let mockSupabase: any = {
  auth: {
    getUser: jest.fn().mockResolvedValue({ data: { user: mockUser }, error: null }),
    getSession: jest.fn().mockResolvedValue({
      data: { session: { user: mockUser } },
      error: null
    }),
    signOut: jest.fn().mockResolvedValue({ error: null }),
  },
  from: jest.fn(() => ({
    select: jest.fn().mockReturnThis(),
    insert: jest.fn().mockResolvedValue({ data: [{ id: 'doc-1' }], error: null }),
  })),
};

// --- Mock @supabase/ssr ---
jest.mock('@supabase/ssr', () => ({
  createServerClient: jest.fn(() => ({
    auth: {
      getUser: jest.fn().mockResolvedValue({ data: { user: null }, error: null }),
      getSession: jest.fn().mockResolvedValue({ data: { session: null }, error: null }),
    },
    storage: {
      from: jest.fn().mockReturnThis(),
      upload: jest.fn(),
      download: jest.fn(),
    },
  })),
  createBrowserClient: jest.fn(() => ({
    auth: {
      signInWithPassword: jest.fn(),
      signOut: jest.fn(),
    },
  })),
  parseCookieHeader: jest.fn(),
  serializeCookieHeader: jest.fn(),
}));

// --- Mock Services ---
jest.mock('../../../../lib/services/rate-limit-service', () => ({
  RateLimitService: jest.fn(() => ({
    canUploadDocument: jest.fn().mockResolvedValue({ allowed: true }),
  })),
}));

describe('/api/documents/upload Route', () => {
  let mockReq: NextRequest;
  let routeModule: any;
  let mockJwt: string;
  let uploadSpy: jest.SpyInstance;

  beforeEach(async () => {
    jest.clearAllMocks();
    jest.resetModules();

    mockSupabase = {
      auth: {
        getUser: jest.fn().mockResolvedValue({ data: { user: mockUser }, error: null }),
        getSession: jest.fn().mockResolvedValue({
          data: { session: { user: mockUser, access_token: 'initial-placeholder' } },
          error: null
        }),
        signOut: jest.fn().mockResolvedValue({ error: null }),
      },
      from: jest.fn(() => ({
        select: jest.fn().mockReturnThis(),
        insert: jest.fn().mockResolvedValue({ data: [{ id: 'doc-1' }], error: null }),
      })),
    };
    jest.mock('@supabase/ssr', () => ({
      createServerClient: jest.fn(() => mockSupabase),
      createBrowserClient: jest.fn(),
    }));

    mockJwt = createMockJwt({
      sub: mockUser.id,
      role: mockUser.role,
      tenant_id: mockUser.tenantId,
      email: mockUser.email,
      iat: Math.floor(Date.now() / 1000),
      exp: Math.floor(Date.now() / 1000) + 3600,
    });
    mockSupabase.auth.getSession.mockResolvedValue({
      data: { session: { user: mockUser, access_token: mockJwt } },
      error: null
    });

    const { DocumentService } = await import('../../../../lib/services/document-service');
    uploadSpy = jest.spyOn(DocumentService.prototype, 'upload')
                    .mockResolvedValue({
        id: 'doc-123',
        tenant_id: mockUser.tenantId!,
        title: 'test.pdf',
        gcs_path: `${mockUser.tenantId}/test.pdf`,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
        case_id: null,
        client_id: null,
        version: 1,
        status: 'uploaded', // Change back to a potential enum string value
        created_by: null,
        embedding_status: null,
        indexed_at: null,
        indexed_by: null,
        content: "", // Change from null to empty string
        metadata: null,
        summary: null,
        updated_by: null,
        uploaded_by: mockUser.id,
        document_type: null,
        last_embedded_at: null,
        sent_at: null,
        signed_at: null,
        template_id: null,
        variables_used: null,
    } as Database['tenants']['Tables']['authored_documents']['Row']); // Explicit Cast

    const formData = new FormData();
    formData.append('file', new File(['content'], 'test.pdf', { type: 'application/pdf' }));
    mockReq = {
      headers: new Headers({ 'content-type': 'multipart/form-data' }),
      method: 'POST',
      url: 'http://localhost/api/documents/upload',
      json: jest.fn(),
      formData: jest.fn().mockResolvedValue(formData),
      nextUrl: new URL('http://localhost/api/documents/upload'),
      cookies: {
        get: jest.fn((name: string) => name.startsWith('sb-') ? { name, value: mockJwt } : undefined),
        set: jest.fn(),
        delete: jest.fn(),
        has: jest.fn((name: string) => name.startsWith('sb-')),
        getAll: jest.fn(() => [{name: 'sb-mock-token', value: mockJwt}]),
      },
      clone: jest.fn().mockReturnThis(),
    } as unknown as NextRequest;

    routeModule = await import('../upload/route');

  });

  it('should return 200 on successful upload', async () => {
     // Call the actual exported POST handler
    const response = await routeModule.POST(mockReq, {}); // Pass empty context

    // Check Auth via Supabase mock
    expect(mockSupabase.auth.getSession).toHaveBeenCalled();
    expect(mockSupabase.auth.getUser).toHaveBeenCalled();

    // Check DocumentService call
    expect(uploadSpy).toHaveBeenCalledTimes(1);
    expect(uploadSpy).toHaveBeenCalledWith(
        expect.any(File), // The file object
        expect.objectContaining({ title: 'test.pdf' }), // Basic metadata from file name/request
        mockUser.id // User ID from authenticated user
    );

    // Check success response (using spy) - Assuming the route returns the document object
    expect(jsonSpy).toHaveBeenCalledWith(expect.objectContaining({
      id: 'doc-123',
      gcs_path: `${mockUser.tenantId}/test.pdf`
    }), { status: 200 });
  });

  it('should return 400 if file is missing', async () => {
     const formData = new FormData(); // Empty FormData
    const reqWithoutFile = {
      ...mockReq,
      formData: jest.fn().mockResolvedValue(formData),
      cookies: {...mockReq.cookies}, // Ensure cookies are carried over
      clone: jest.fn().mockReturnThis()
    } as unknown as NextRequest;

    // Call the actual handler
    const response = await routeModule.POST(reqWithoutFile, {});

    // Auth might still be checked by HOCs before handler logic
    expect(mockSupabase.auth.getSession).toHaveBeenCalled();
    expect(mockSupabase.auth.getUser).toHaveBeenCalled();

    // Check 400 response
    expect(jsonSpy).toHaveBeenCalledWith({ error: 'File is required' }, { status: 400 });
    // DocumentService upload should not have been called
    expect(uploadSpy).not.toHaveBeenCalled();
  });

   it('should return 401 if authentication fails', async () => {
     // Simulate auth failure in mockSupabase
    mockSupabase.auth.getUser.mockResolvedValue({ data: { user: null }, error: { message: 'Unauthorized', status: 401 } });
    mockSupabase.auth.getSession.mockResolvedValue({ data: { session: null }, error: { message: 'Unauthorized', status: 401 } });
    // Re-apply mock for @supabase/ssr AFTER modifying mockSupabase
    jest.mock('@supabase/ssr', () => ({
       createServerClient: jest.fn(() => mockSupabase),
       createBrowserClient: jest.fn(),
    }));

    // Re-import module AFTER changing mock behavior
    const routeModuleFailedAuth = await import('../upload/route');
    // Call the actual handler
    const response = await routeModuleFailedAuth.POST(mockReq, { params: {} });

    // Check auth attempt
    expect(mockSupabase.auth.getSession).toHaveBeenCalledTimes(1);
    expect(mockSupabase.auth.getUser).toHaveBeenCalledTimes(1);

    // Check 401 response
    expect(jsonSpy).toHaveBeenCalledWith({ error: 'Unauthorized' }, { status: 401 });
    // DocumentService upload should not have been called
    expect(uploadSpy).not.toHaveBeenCalled();
   });

   it('should return 500 if DocumentService upload fails', async () => {
    // Setup DocumentService mock to reject
    const uploadError = new Error('Supabase storage error');
    uploadSpy.mockRejectedValue(uploadError);

    // Call the actual handler
    const response = await routeModule.POST(mockReq, {});

     // Auth should pass
    expect(mockSupabase.auth.getSession).toHaveBeenCalled();
    expect(mockSupabase.auth.getUser).toHaveBeenCalled();

    // DocumentService was called
    expect(uploadSpy).toHaveBeenCalledTimes(1);

    // Check 500 response
    // Adjust error message based on actual route's error handling
    expect(jsonSpy).toHaveBeenCalledWith({ error: 'Failed to upload document.' }, { status: 500 });
  });

  // Add test for rate limiting if applicable (assuming HOC is applied)
  it('should return 429 if rate limited', async () => {
      // Mock RateLimitService to deny
      const { RateLimitService: MockRLServiceCtor } = await import('../../../../lib/services/rate-limit-service'); // Use relative path
      const mockRLServiceInstance = new (MockRLServiceCtor as jest.Mock)();
      mockRLServiceInstance.canUploadDocument.mockResolvedValue({ allowed: false, reason: 'Test limit' });
       (MockRLServiceCtor as jest.Mock).mockImplementation(() => mockRLServiceInstance);

      // Re-import route module to pick up the new RateLimitService mock behavior IF rate limit is applied via HOC *inside* the route file
      const routeModuleRateLimited = await import('../upload/route');
      const response = await routeModuleRateLimited.POST(mockReq, { params: {} });

      // Auth check should pass
      expect(mockSupabase.auth.getSession).toHaveBeenCalled();
      expect(mockSupabase.auth.getUser).toHaveBeenCalled();

      // Check rate limit service call
      expect(mockRLServiceInstance.canUploadDocument).toHaveBeenCalled();

       // Check 429 response (adjust error message if needed)
      expect(jsonSpy).toHaveBeenCalledWith(expect.objectContaining({ error: expect.stringContaining('Rate limit exceeded') }), { status: 429 });
      // DocumentService upload should not have been called
      expect(uploadSpy).not.toHaveBeenCalled();
  });

});
