import { NextRequest, NextResponse } from 'next/server';
import { Database } from '@/lib/supabase/database.types';
import { withAuth, AuthUser } from '@/lib/auth-helpers';
import { UserRole } from '@/lib/types/auth';
import { SupabaseClient } from '@supabase/supabase-js';
import { Json } from '@/lib/supabase/database.types';

// Define interfaces for document data
interface AuthoredDocument {
  id: string;
  variables_used?: Json | null;
  user_id: string;
  updated_at?: string;
}

interface UpdateVariablesRequest {
  document_id: string;
  variables: Record<string, Json | undefined>; // Expect an object map
}

// Export POST handler with withAuth wrapper to respect RLS since these are tenant-specific documents
export const POST = withAuth(
  async (
    req: NextRequest,
    user: AuthUser,
    supabase: SupabaseClient<Database>,
    context: Record<string, unknown>
  ): Promise<Response> => {
    const { searchParams } = new URL(req.url);
    const action = searchParams.get('action');

    try {
      const body = await req.json() as UpdateVariablesRequest;

      // Handle different actions
      switch (action) {
        case 'update_variables':
          return await updateDocumentVariables(user.id, body, supabase);
        default:
          return NextResponse.json({ error: 'Invalid action' }, { status: 400 });
      }
    } catch (error: unknown) {
      const message = error instanceof Error ? error.message : 'Unknown API error occurred';
      console.error('API error:', message, error); // Log original error too
      return NextResponse.json({ error: message }, { status: 500 });
    }
  }
);

/**
 * Update variables for a specific document
 */
async function updateDocumentVariables(
  userId: string,
  body: UpdateVariablesRequest,
  supabase: SupabaseClient<Database>
): Promise<Response> {
  const { document_id, variables } = body;

  if (!document_id) {
    return NextResponse.json({ error: 'Document ID is required' }, { status: 400 });
  }

  // Validate document belongs to user
  const { data: document, error: docError } = await supabase
    .schema('tenants') // Explicitly use tenants schema
    .from('authored_documents')
    .select('id, variables_used')
    .eq('id', document_id)
    .eq('user_id', userId)
    .single();

  if (docError || !document) {
    return NextResponse.json({ error: 'Document not found or access denied' }, { status: 404 });
  }

  // Update document variables by merging existing with new
  // Ensure types are object-like for spreading
  const updatedVariables: Record<string, Json | undefined> = { // Use Record type
    // Explicitly handle null/undefined and assert object structure for existing variables
    ...((document.variables_used as Record<string, Json | undefined> | null) || {}),
    // `variables` is now Record type from request body
    ...variables
  };

  const { error: updateError } = await supabase
    .schema('tenants') // Explicitly use tenants schema
    .from('authored_documents')
    .update({
      variables_used: updatedVariables,
      updated_at: new Date().toISOString()
    })
    .eq('id', document_id);

  if (updateError) {
    return NextResponse.json({ error: updateError.message }, { status: 500 });
  }

  return NextResponse.json({
    success: true,
    data: { id: document_id, variables_used: updatedVariables }
  });
}
