import { NextRequest, NextResponse } from 'next/server';
import { SupabaseClient } from '@supabase/supabase-js';
import { withAuth, AuthUser, RouteHandlerWithAuth } from '@/lib/auth-helpers';
import { UserRole } from '@/lib/types/auth';
import { createServiceClient } from '@/lib/supabase/server';
import { Database } from '@/lib/supabase/database.types';
import { logSecurityEvent } from '@/lib/security/forensics';
import { z } from 'zod';

const getQuotaSchema = z.object({
  tenant_id: z.string().uuid().optional(),
});

const updateQuotaSchema = z.object({
  tenant_id: z.string().uuid(),
  new_quota: z.number().int().positive(),
});

type AdminRouteHandler = (req: NextRequest, user: AuthUser, supabase: SupabaseClient, context: { params?: any }) => Promise<NextResponse>;

// Type definition for the inner handler function passed to with<PERSON>uth
// Renamed for clarity, but functionally the same as RouteHandlerWithAuth if imported
type QuotaRouteHandlerLogic = (
  req: NextRequest,
  user: AuthUser,
  supabase: SupabaseClient<Database>,
  context: { params?: any }
) => Promise<NextResponse | Response>;

export const GET = withAuth(async (req, user, supabase, context) => {
  logSecurityEvent(supabase, 'admin.quota.fetch_attempt', {
    userId: user.id,
    resource: 'tenant_quotas',
  });

  if (user.role !== UserRole.Superadmin) {
    if (!user.tenantId) {
      logSecurityEvent(supabase, 'admin.quota.fetch_authorization_failure', {
        userId: user.id,
        resource: 'tenant_quotas',
        reason: 'Tenant ID missing.',
        status: 403,
      });
      return NextResponse.json({ error: 'Forbidden: Tenant ID missing.' }, { status: 403 });
    }
    logSecurityEvent(supabase, 'admin.quota.fetch_single_attempt', {
      userId: user.id,
      resource: 'tenant_quotas',
      tenantId: user.tenantId,
    });
    const { data, error } = await supabase
      .schema('tenants')
      .from('tenant_quotas')
      .select('*')
      .eq('tenant_id', user.tenantId)
      .single();

    if (error) {
      logSecurityEvent(supabase, 'admin.quota.fetch_single_failure', {
        userId: user.id,
        resource: 'tenant_quotas',
        tenantId: user.tenantId,
        error: error.message,
        status: 500,
      });
      return NextResponse.json({ error: 'Failed to fetch quota' }, { status: 500 });
    }
    if (!data) {
      logSecurityEvent(supabase, 'admin.quota.fetch_single_not_found', {
        userId: user.id,
        resource: 'tenant_quotas',
        tenantId: user.tenantId,
        reason: 'Quota not found for tenant',
        status: 404,
      });
      return NextResponse.json({ error: 'Quota not found for tenant' }, { status: 404 });
    }
    logSecurityEvent(supabase, 'admin.quota.fetch_single_success', {
      userId: user.id,
      resource: 'tenant_quotas',
      tenantId: user.tenantId,
      status: 200,
      count: 1,
    });
    return NextResponse.json(data);
  }

  const url = new URL(req.url);
  const tenantIdQuery = url.searchParams.get('tenant_id');

  if (tenantIdQuery) {
    const validation = z.string().uuid().safeParse(tenantIdQuery);
    if (!validation.success) {
      logSecurityEvent(supabase, 'admin.quota.fetch_single_validation_error', {
        userId: user.id,
        resource: 'tenant_quotas',
        action: 'fetch',
        error: 'Invalid tenant_id format',
        details: { tenantIdQuery },
        status: 400,
      });
      return NextResponse.json({ error: 'Invalid tenant_id format' }, { status: 400 });
    }

    logSecurityEvent(supabase, 'admin.quota.fetch_single_attempt', {
      userId: user.id,
      resource: 'tenant_quotas',
      tenantId: tenantIdQuery,
    });
    const { data, error } = await supabase
      .schema('tenants')
      .from('tenant_quotas')
      .select('*')
      .eq('tenant_id', tenantIdQuery)
      .single();

    if (error) {
      logSecurityEvent(supabase, 'admin.quota.fetch_single_failure', {
        userId: user.id,
        resource: 'tenant_quotas',
        tenantId: tenantIdQuery,
        error: error.message,
        status: 500,
      });
      return NextResponse.json({ error: 'Failed to fetch quota' }, { status: 500 });
    }
    if (!data) {
      logSecurityEvent(supabase, 'admin.quota.fetch_single_not_found', {
        userId: user.id,
        resource: 'tenant_quotas',
        tenantId: tenantIdQuery,
        reason: 'Quota not found for tenant',
        status: 404,
      });
      return NextResponse.json({ error: 'Quota not found for tenant' }, { status: 404 });
    }
    logSecurityEvent(supabase, 'admin.quota.fetch_single_success', {
      userId: user.id,
      resource: 'tenant_quotas',
      tenantId: tenantIdQuery,
      status: 200,
      count: 1,
    });
    return NextResponse.json(data);

  } else {
    logSecurityEvent(supabase, 'admin.quota.fetch_all_attempt', {
      userId: user.id,
      resource: 'tenant_quotas',
    });
    const { data, error } = await supabase
      .schema('tenants')
      .from('tenant_quotas')
      .select('*');

    if (error) {
      logSecurityEvent(supabase, 'admin.quota.fetch_all_failure', {
        userId: user.id,
        resource: 'tenant_quotas',
        error: error.message,
        status: 500,
      });
      return NextResponse.json({ error: 'Failed to fetch quotas' }, { status: 500 });
    }
    logSecurityEvent(supabase, 'admin.quota.fetch_all_success', {
      userId: user.id,
      resource: 'tenant_quotas',
      status: 200,
      count: data?.length ?? 0,
    });
    return NextResponse.json(data);
  }
});

export const POST = withAuth(async (req, user, supabase, context) => {
  logSecurityEvent(supabase, 'admin.quota.update_attempt', {
    userId: user.id,
    resource: 'tenant_quotas',
  });

  if (user.role !== UserRole.Superadmin) {
    logSecurityEvent(supabase, 'admin.quota.update_authorization_failure', {
      userId: user.id,
      resource: 'tenant_quotas',
      action: 'update',
      error: 'Insufficient privileges',
      status: 403,
    });
    return NextResponse.json({ error: 'Forbidden: Insufficient privileges.' }, { status: 403 });
  }

  let payload;
  try {
    const body = await req.json();
    payload = updateQuotaSchema.parse(body);
    logSecurityEvent(supabase, 'admin.quota.update_validation_success', {
      userId: user.id,
      resource: 'tenant_quotas',
      action: 'update',
    });
  } catch (error) {
    logSecurityEvent(supabase, 'admin.quota.update_validation_error', {
      userId: user.id,
      resource: 'tenant_quotas',
      action: 'update',
      error: 'Invalid request body',
      details: error,
      status: 400,
    });
    if (error instanceof z.ZodError) {
      return NextResponse.json({ error: 'Invalid request body', details: error.errors }, { status: 400 });
    }
    return NextResponse.json({ error: 'Failed to parse request body' }, { status: 400 });
  }

  const { tenant_id, new_quota } = payload;

  const supabaseAdmin = createServiceClient();
  logSecurityEvent(supabaseAdmin, 'admin.quota.update_db_attempt', {
    userId: user.id,
    resource: 'tenant_quotas',
    action: 'update',
    tenantId: tenant_id,
  });

  const { data, error } = await supabaseAdmin
    .schema('tenants')
    .from('tenant_quotas')
    .update({ quota_limit: new_quota, updated_at: new Date().toISOString() })
    .eq('tenant_id', tenant_id)
    .select()
    .single();

  if (error) {
    logSecurityEvent(supabaseAdmin, 'admin.quota.update_db_failure', {
      userId: user.id,
      resource: 'tenant_quotas',
      action: 'update',
      tenantId: tenant_id,
      error: error.message,
      status: 500,
    });
    if (error.code === 'PGRST116') {
        return NextResponse.json({ error: `Tenant with ID ${tenant_id} not found.` }, { status: 404 });
    }
    return NextResponse.json({ error: 'Failed to update quota' }, { status: 500 });
  }

  if (!data) {
      logSecurityEvent(supabaseAdmin, 'admin.quota.update_db_not_found', {
        userId: user.id,
        resource: 'tenant_quotas',
        action: 'update',
        tenantId: tenant_id,
        error: 'Quota record not found after update attempt',
        status: 404,
      });
      return NextResponse.json({ error: `Quota record for tenant ${tenant_id} not found after update attempt.` }, { status: 404 });
  }

  logSecurityEvent(supabaseAdmin, 'admin.quota.update_db_success', {
    userId: user.id,
    resource: 'tenant_quotas',
    action: 'update',
    tenantId: tenant_id,
    status: 200,
  });
  return NextResponse.json(data);

});
