// @ts-nocheck - API route type issues
import { NextRequest, NextResponse } from 'next/server';
import { Database } from '@/lib/supabase/database.types';
import { createClient, isAuthenticated, hasRole } from '@/lib/auth/auth-helper';
import { enhanceClientWithSchemas } from '@/lib/supabase/schema-client';

/**
 * GET /api/admin/subscription
 * Get all subscription plans and addons
 */
export async function GET(request: NextRequest) {
  try {
    // Create a Supabase client
    const supabase = createClient();
    // Use the enhanced client with schema support
    const enhancedClient = enhanceClientWithSchemas(supabase);

    // Get the user from the session
    const { data: { session }, error: sessionError } = await supabase.auth.getSession();

    if (sessionError || !session) {
      console.error('Session error:', sessionError);
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const user = session.user;

    // Check if user is authenticated
    if (!isAuthenticated(user)) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Check if user is a superadmin
    if (!hasRole(user, ['superadmin'])) {
      return NextResponse.json({ error: 'Forbidden' }, { status: 403 });
    }

    // Get query parameters
    const searchParams = request.nextUrl.searchParams;
    const tenantId = searchParams.get('tenantId');
    const includeInactive = searchParams.get('includeInactive') === 'true';

    // Get subscription plans
    const { data: plans, error: plansError } = await enhancedClient.tenants
      .from('subscription_plans')
      .select('*')
      .order('base_price_monthly', { ascending: true });

    if (plansError) {
      console.error('Error fetching subscription plans:', plansError);
      return NextResponse.json({ error: 'Failed to fetch subscription plans' }, { status: 500 });
    }

    // Get subscription addons
    const { data: addons, error: addonsError } = await enhancedClient.tenants
      .from('subscription_addons')
      .select('*')
      .order('price_monthly', { ascending: true });

    if (addonsError) {
      console.error('Error fetching subscription addons:', addonsError);
      return NextResponse.json({ error: 'Failed to fetch subscription addons' }, { status: 500 });
    }

    // If tenantId is provided, get tenant subscriptions
    let tenantSubscriptions = null;
    if (tenantId) {
      const { data: subscriptions, error: subscriptionsError } = await enhancedClient.tenants
        .from('tenant_subscriptions')
        .select(`
          *,
          subscription_plans (
            name, code, base_price_monthly, base_price_yearly, features
          )
        `)
        .eq('tenant_id', tenantId)
        .order('created_at', { ascending: false });

      if (subscriptionsError) {
        console.error('Error fetching tenant subscriptions:', subscriptionsError);
        return NextResponse.json({ error: 'Failed to fetch tenant subscriptions' }, { status: 500 });
      }

      // Get tenant addons
      const { data: tenantAddons, error: tenantAddonsError } = await enhancedClient.tenants
        .from('tenant_addons')
        .select(`
          *,
          subscription_addons (
            name, code, category, price_monthly, price_yearly, features
          )
        `)
        .eq('tenant_id', tenantId);

      if (tenantAddonsError) {
        console.error('Error fetching tenant addons:', tenantAddonsError);
        return NextResponse.json({ error: 'Failed to fetch tenant addons' }, { status: 500 });
      }

      tenantSubscriptions = {
        subscriptions,
        addons: tenantAddons
      };
    }

    return NextResponse.json({
      plans,
      addons,
      tenantSubscriptions
    });
  } catch (error) {
    console.error('Error in subscription API:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}

/**
 * POST /api/admin/subscription
 * Create or update subscription plans and addons
 */
export async function POST(request: NextRequest) {
  try {
    // Create a Supabase client
    const supabase = createClient();
    // Use the enhanced client with schema support
    const enhancedClient = enhanceClientWithSchemas(supabase);

    // Get the user from the session
    const { data: { session }, error: sessionError } = await supabase.auth.getSession();

    if (sessionError || !session) {
      console.error('Session error:', sessionError);
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const user = session.user;

    // Check if user is authenticated
    if (!isAuthenticated(user)) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Check if user is a superadmin
    if (!hasRole(user, ['superadmin'])) {
      return NextResponse.json({ error: 'Forbidden' }, { status: 403 });
    }
    const body = await request.json();

    // Determine the action to take
    const { action, data } = body;

    if (action === 'createPlan') {
      // Create a new subscription plan
      const { data: plan, error } = await enhancedClient.tenants
        .from('subscription_plans')
        .insert({
          name: data.name,
          code: data.code,
          description: data.description,
          is_active: data.isActive,
          is_public: data.isPublic,
          base_price_monthly: data.basePriceMonthly,
          base_price_yearly: data.basePriceYearly,
          features: data.features
        })
        .select()
        .single();

      if (error) {
        console.error('Error creating subscription plan:', error);
        return NextResponse.json({ error: 'Failed to create subscription plan' }, { status: 500 });
      }

      return NextResponse.json({ plan });
    }
    else if (action === 'updatePlan') {
      // Update an existing subscription plan
      const { data: plan, error } = await enhancedClient.tenants
        .from('subscription_plans')
        .update({
          name: data.name,
          description: data.description,
          is_active: data.isActive,
          is_public: data.isPublic,
          base_price_monthly: data.basePriceMonthly,
          base_price_yearly: data.basePriceYearly,
          features: data.features,
          updated_at: new Date().toISOString()
        })
        .eq('id', data.id)
        .select()
        .single();

      if (error) {
        console.error('Error updating subscription plan:', error);
        return NextResponse.json({ error: 'Failed to update subscription plan' }, { status: 500 });
      }

      return NextResponse.json({ plan });
    }
    else if (action === 'createAddon') {
      // Create a new subscription addon
      const { data: addon, error } = await enhancedClient.tenants
        .from('subscription_addons')
        .insert({
          name: data.name,
          code: data.code,
          description: data.description,
          category: data.category,
          is_active: data.isActive,
          price_monthly: data.priceMonthly,
          price_yearly: data.priceYearly,
          features: data.features
        })
        .select()
        .single();

      if (error) {
        console.error('Error creating subscription addon:', error);
        return NextResponse.json({ error: 'Failed to create subscription addon' }, { status: 500 });
      }

      return NextResponse.json({ addon });
    }
    else if (action === 'updateAddon') {
      // Update an existing subscription addon
      const { data: addon, error } = await enhancedClient.tenants
        .from('subscription_addons')
        .update({
          name: data.name,
          description: data.description,
          category: data.category,
          is_active: data.isActive,
          price_monthly: data.priceMonthly,
          price_yearly: data.priceYearly,
          features: data.features,
          updated_at: new Date().toISOString()
        })
        .eq('id', data.id)
        .select()
        .single();

      if (error) {
        console.error('Error updating subscription addon:', error);
        return NextResponse.json({ error: 'Failed to update subscription addon' }, { status: 500 });
      }

      return NextResponse.json({ addon });
    }

    return NextResponse.json({ error: 'Invalid action' }, { status: 400 });
  } catch (error) {
    console.error('Error in subscription API:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}
