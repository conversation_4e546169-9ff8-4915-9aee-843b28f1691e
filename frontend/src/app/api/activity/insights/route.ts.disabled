// @ts-nocheck - API route type issues
import { NextResponse } from 'next/server';
import { type NextRequest } from 'next/server';
import { AuthUser } from '@/lib/types';
import { withAuth } from '@/lib/auth-helpers';
import { getRecentUserActivities, logUserFeedback } from '@/lib/neo4j/client';
import { Activity, UserPreferences } from '@/lib/types';
import { analyzeActivities, getLLMStats, clearLLMCache } from '@/lib/openai/client';
import { LLMProvider } from '@/lib/llm/service';

/**
 * Represents a processed, actionable insight derived from user activities.
 */
interface Insight {
  id: string; // Use activityId or generate a new one
  message: string;
  suggestions: string[];
  priority: number; // Higher number = higher priority (e.g., 10 for high, 5 for medium)
  relatedEntity?: {
    type: string; // e.g., 'case', 'document'
    id?: string;
    name?: string | null;
  };
  timestamp: string; // Original activity timestamp
  groupKey?: string; // For grouping related activities (e.g., case name, deadline date)
  relatedActivities?: string[]; // IDs of related activities
  feedbackId?: string; // ID for tracking user feedback
  aiGenerated?: boolean; // Flag to indicate if this was generated by AI
}

/**
 * Groups activities by case, document, or deadline for better insight generation
 */
function groupActivitiesByContext(activities: Activity[]) {
  const groups: Record<string, Activity[]> = {};

  // Group by case
  activities.forEach(activity => {
    if (activity.caseTitle) {
      const key = `case:${activity.caseTitle}`;
      groups[key] = groups[key] || [];
      groups[key].push(activity);
    } else if (activity.documentName) {
      const key = `document:${activity.documentName}`;
      groups[key] = groups[key] || [];
      groups[key].push(activity);
    } else {
      // Group by date (for activities without case/document)
      const date = new Date(activity.time).toISOString().split('T')[0];
      const key = `date:${date}`;
      groups[key] = groups[key] || [];
      groups[key].push(activity);
    }
  });

  return groups;
}

/**
 * Generates a unique feedback ID for tracking user responses to insights
 */
function generateFeedbackId(): string {
  return `fb_${Date.now()}_${Math.random().toString(36).substring(2, 9)}`;
}

/**
 * Processes raw activity data to generate actionable insights.
 * Uses rule-based approach as fallback when AI analysis is unavailable.
 */
async function generateInsightsFromActivities(activities: Activity[], user: AuthUser, options: { useAi: boolean, provider?: LLMProvider } = { useAi: true }): Promise<Insight[]> {
  if (!activities || activities.length === 0) {
    return [];
  }

  const { useAi, provider } = options;
  let insights: Insight[] = [];

  // Try AI-based analysis if enabled
  if (useAi) {
    try {
      // Determine which LLM provider to use
      const preferredProvider = provider || (process.env.PREFERRED_LLM_PROVIDER as LLMProvider) || 'openai';
      console.log(`[API Insights] Using ${preferredProvider} to analyze ${activities.length} activities for user ${user.id}`);

      // Use caching by default for performance
      const aiInsights = await analyzeActivities(activities, {
        useCache: true,
        forceProvider: preferredProvider
      });

      if (aiInsights && aiInsights.length > 0) {
        // Transform AI insights to match our interface
        insights = aiInsights.map((insight: any) => ({
          ...insight,
          feedbackId: generateFeedbackId(),
          aiGenerated: true
        }));

        // Log cache stats for monitoring
        const cacheStats = getLLMStats();
        console.log(`[API Insights] Generated ${insights.length} AI-powered insights. Cache stats:`, cacheStats);

        return insights.slice(0, 3); // Return top 3 AI insights
      }
    } catch (error) {
      console.error('[API Insights] Error using AI for insight generation:', error);
      // Fall back to rule-based approach
    }
  }

  console.log('[API Insights] Using rule-based insight generation');

  // --- Rule-Based Insight Generation (Fallback) ---

  // Group activities for better context
  const activityGroups = groupActivitiesByContext(activities);

  // Process each group to generate insights
  Object.entries(activityGroups).forEach(([groupKey, groupActivities]) => {
    // Skip groups with no activities
    if (groupActivities.length === 0) return;

    // Extract group type and name
    const [groupType, groupName] = groupKey.split(':');

    // Find high importance activities in this group
    const highImportance = groupActivities.filter(a => a.importance === 'high');

    if (highImportance.length > 0) {
      // Create insight for high importance activities
      const activity = highImportance[0]; // Use the first high importance activity
      insights.push({
        id: activity.activityId,
        message: groupType === 'case'
          ? `High Priority: Case "${groupName}" needs attention`
          : `High Priority: ${activity.summary}`,
        suggestions: [
          'View Details',
          groupType === 'case' ? `Go to Case: ${groupName}` : 'Check Related Items'
        ].filter(s => !!s),
        priority: 10,
        relatedEntity: groupType === 'case'
          ? { type: 'case', name: groupName }
          : groupType === 'document'
          ? { type: 'document', name: groupName }
          : undefined,
        timestamp: activity.time,
        groupKey,
        relatedActivities: highImportance.map(a => a.activityId),
        feedbackId: generateFeedbackId()
      });
    } else {
      // If no high importance, check for medium importance
      const mediumImportance = groupActivities.filter(a => a.importance === 'medium');

      if (mediumImportance.length > 0) {
        const activity = mediumImportance[0];
        insights.push({
          id: activity.activityId,
          message: groupType === 'case'
            ? `Recent Activity: Updates on case "${groupName}"`
            : `Recent Activity: ${activity.summary}`,
          suggestions: [
            'View Details',
            groupType === 'case' ? `Go to Case: ${groupName}` : 'Check Related Items'
          ].filter(s => !!s),
          priority: 5,
          relatedEntity: groupType === 'case'
            ? { type: 'case', name: groupName }
            : groupType === 'document'
            ? { type: 'document', name: groupName }
            : undefined,
          timestamp: activity.time,
          groupKey,
          relatedActivities: mediumImportance.map(a => a.activityId),
          feedbackId: generateFeedbackId()
        });
      }
    }
  });

  // If no insights generated from groups, create a generic one from the most recent activity
  if (insights.length === 0 && activities.length > 0) {
    const activity = activities[0]; // Most recent overall
    insights.push({
      id: activity.activityId,
      message: `Latest: ${activity.summary}`,
      suggestions: ['View Recent Activity'],
      priority: 1,
      relatedEntity: activity.caseTitle
        ? { type: 'case', name: activity.caseTitle }
        : activity.documentName
        ? { type: 'document', name: activity.documentName }
        : undefined,
      timestamp: activity.time,
      feedbackId: generateFeedbackId()
    });
  }

  // Sort insights by priority (descending) then timestamp (descending)
  insights.sort((a, b) => b.priority - a.priority || new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime());

  return insights.slice(0, 3); // Return top 3 insights
}

/**
 * API route handler for fetching recent activities and potentially generating insights.
 * Retrieves enriched activity data from Neo4j for the authenticated user.
 * Protected by authentication.
 */
export const GET = withAuth(async (request: NextRequest, user: AuthUser, supabase: any, context: any) => {
  // 'user' object is available and validated by withAuth
  try {
    const { searchParams } = new URL(request.url);
    const daysBackParam = searchParams.get('daysBack');
    const limitParam = searchParams.get('limit');
    const useAiParam = searchParams.get('useAi');
    const providerParam = searchParams.get('provider') as LLMProvider | null;
    const clearCacheParam = searchParams.get('clearCache');

    const daysBack = daysBackParam ? parseInt(daysBackParam, 10) : 7; // Default 7 days
    const limit = limitParam ? parseInt(limitParam, 10) : 15; // Default 15 activities
    const useAi = useAiParam !== 'false'; // Default to true unless explicitly set to false

    // Clear cache if requested (admin functionality)
    if (clearCacheParam === 'true' && user.role === 'admin') {
      clearLLMCache();
      console.log('[API Insights] Cache cleared by admin user');
    }

    if (isNaN(daysBack) || isNaN(limit) || daysBack <= 0 || limit <= 0) {
        return NextResponse.json({ message: 'Invalid daysBack or limit parameter' }, { status: 400 });
    }

    console.log(`[API Insights] User ${user.id} requesting insights (daysBack: ${daysBack}, limit: ${limit}, useAi: ${useAi}, provider: ${providerParam || 'default'}).`);

    // Fetch recent activities including importance and tags
    const recentActivities = await getRecentUserActivities({
      userId: user.id,
      daysBack,
      limit,
    });

    // Process activities to generate insights with the specified options
    const insights = await generateInsightsFromActivities(recentActivities, user, {
      useAi,
      provider: providerParam || undefined
    });

    console.log(`[API Insights] Generated ${insights.length} insights for user ${user.id}.`);

    // Get cache stats for metadata
    const cacheStats = getLLMStats();

    // Return the fetched (and potentially processed) activities/insights
    return NextResponse.json({
      insights,
      meta: {
        total: insights.length,
        aiGenerated: insights.some(i => i.aiGenerated === true),
        generatedAt: new Date().toISOString(),
        cacheStats: useAi ? cacheStats : null,
        provider: useAi ? (providerParam || process.env.PREFERRED_LLM_PROVIDER || 'openai') : 'rule-based'
      }
    }, { status: 200 });

  } catch (error) {
    console.error('[API Insights] Error fetching or processing activities:', error);
    if (error instanceof Response) {
      return error;
    }
    return NextResponse.json({ message: 'Internal Server Error fetching insights' }, { status: 500 });
  }
});

/**
 * API route handler for recording user feedback on insights.
 * This helps improve future insight generation.
 */
export const POST = withAuth(async (request: NextRequest, user: AuthUser, supabase: any, context: any) => {
  try {
    const body = await request.json();
    const { feedbackId, insightId, action, rating, comment } = body;

    if (!feedbackId || !insightId || !action) {
      return NextResponse.json({ message: 'Missing required fields' }, { status: 400 });
    }

    // Log the feedback to Neo4j
    await logUserFeedback({
      userId: user.id,
      feedbackId,
      insightId,
      action, // e.g., 'clicked', 'dismissed', 'rated'
      rating, // optional 1-5 rating
      comment, // optional user comment
      timestamp: new Date().toISOString()
    });

    // Track feedback for model improvement
    // If rating is low, we can use this data to improve the model
    if (action === 'rated' && rating !== undefined) {
      console.log(`[API Insights] User ${user.id} rated insight ${insightId} with ${rating}/5`);

      // For very negative ratings, we might want to log more details for analysis
      if (rating <= 2) {
        console.warn(`[API Insights] Low rating (${rating}/5) for insight ${insightId}. Comment: ${comment || 'No comment provided'}`);
      }
    }

    return NextResponse.json({
      success: true,
      timestamp: new Date().toISOString()
    }, { status: 200 });
  } catch (error) {
    console.error('[API Insights] Error recording feedback:', error);
    if (error instanceof Response) {
      return error;
    }
    return NextResponse.json({ message: 'Internal Server Error recording feedback' }, { status: 500 });
  }
});
