/**
 * Data Access Test API
 *
 * This API tests the data access layer by querying different schemas.
 * It provides comprehensive testing of all repositories and schema access methods.
 */
import { NextRequest, NextResponse } from 'next/server';
import { withAuth, AuthUser } from '@/lib/auth-helpers';
import { createDataAccess } from '@/lib/data-access';
import { Database } from '@/lib/supabase/database.types';
import type { SupabaseClient } from '@supabase/supabase-js';
import { UserRole } from '@/lib/types/auth'; // Import UserRole enum

/**
 * Interface for test results
 */
interface TestResult {
  success: boolean;
  count: number;
  data?: any[];
  error: string | null;
  timing?: number;
}

/**
 * GET handler that tests the data access layer
 */
export const GET = withAuth(async (
  req: NextRequest,
  user: AuthUser,
  supabase: SupabaseClient<Database>,
  context: Record<string, unknown>
): Promise<Response> => {
  try {
    // Extract query parameters
    const { searchParams } = new URL(req.url);
    const testType = searchParams.get('test') || 'all';
    const limit = parseInt(searchParams.get('limit') || '5', 10);

    // Create a data access layer
    const dataAccess = createDataAccess(supabase, user.tenantId ?? undefined);

    // Initialize results object
    const results: Record<string, TestResult> = {};

    // Helper function to run a test and measure timing
    const runTest = async <T>(
      name: string,
      testFn: () => Promise<T>,
      getCount: (result: T) => number = () => 0
    ): Promise<void> => {
      console.log(`Testing ${name}...`);
      const startTime = performance.now();

      try {
        const result = await testFn();
        const endTime = performance.now();

        results[name] = {
          success: true,
          count: getCount(result),
          data: Array.isArray(result) ? result.slice(0, 3) : undefined, // Include sample data
          error: null,
          timing: Math.round(endTime - startTime)
        };
      } catch (error: any) {
        console.error(`Error in ${name} test:`, error);
        results[name] = {
          success: false,
          count: 0,
          error: error.message || String(error),
          timing: 0
        };
      }
    };

    // Run tests based on the test type
    if (testType === 'all' || testType === 'clients') {
      await runTest(
        'clients',
        () => dataAccess.clients.getClients({ limit }),
        (result) => result.count
      );
    }

    if (testType === 'all' || testType === 'cases') {
      await runTest(
        'cases',
        () => dataAccess.cases.getCases({ limit }),
        (result) => result.count
      );
    }

    if (testType === 'all' || testType === 'tasks') {
      await runTest(
        'tasks',
        () => dataAccess.tasks.getTasks({ limit }),
        (result) => result.count
      );
    }

    if (testType === 'all' || testType === 'security') {
      await runTest(
        'securityEvents',
        () => dataAccess.securityEvents.getEvents({ limit }),
        (result) => result.count
      );
    }

    if (testType === 'all' || testType === 'audit') {
      await runTest(
        'authAudit',
        () => dataAccess.authAudit.getAuditLogs({ limit }),
        (result) => result.count
      );
    }

    if (testType === 'all' || testType === 'schema') {
      await runTest(
        'directSchema',
        async () => {
          const { data, error } = await dataAccess.schema.tenants('clients')
            .select('*')
            .eq('tenant_id', user.tenantId)
            .limit(limit);

          if (error) throw error;
          return data || [];
        },
        (result) => result.length
      );
    }

    // Return the results of all tests
    return NextResponse.json({
      success: Object.values(results).every(r => r.success),
      message: 'Data access layer tests completed',
      testType,
      timestamp: new Date().toISOString(),
      user: {
        id: user.id,
        tenantId: user.tenantId,
        role: user.role
      },
      results
    });
  } catch (error: any) {
    console.error('Error in data-access-test route:', error);
    return NextResponse.json({
      success: false,
      message: 'Error in data-access-test route',
      error: error.message,
      stack: error.stack
    }, { status: 500 });
  }
}); // Use enum members
