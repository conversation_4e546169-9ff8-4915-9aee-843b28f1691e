/**
 * Clients API
 *
 * This API provides endpoints for managing clients.
 */
import { NextRequest, NextResponse } from 'next/server';
import { with<PERSON><PERSON>, <PERSON>th<PERSON><PERSON>, RouteHandlerWithAuth } from '@/lib/auth-helpers';
import { UserRole } from '@/lib/types/auth';
import { SupabaseClient } from '@supabase/supabase-js';
import { Database } from '@/lib/supabase/database.types';
import { z } from 'zod';
import { logSecurityEvent, SecurityEventDetails } from '@/lib/security/forensics';

import { createDataAccess } from '@/lib/data-access';
import { createServices } from '@/lib/services';

// Define interfaces for client data
interface Client {
  id: string;
  first_name: string;
  last_name: string;
  email?: string | null;
  phone_primary?: string | null;
  client_type: 'individual' | 'business';
  intake_date?: string;
  status: 'active' | 'inactive' | 'pending';
  business_name?: string;
  business_type?: string;
  tax_id?: string;
  date_of_birth?: string;
  address?: {
    street?: string;
    city?: string;
    state?: string;
    zip?: string;
  };
  assigned_attorney_id?: string;
  created_at?: string;
  updated_at?: string;
  created_by?: string;
  tenant_id: string;
}

interface ClientQueryParams {
  page: number;
  limit: number;
  status?: string;
  client_type?: string;
  searchTerm?: string;
}

interface ClientQueryResult {
  clients: Client[];
  total: number;
  page: number;
  limit: number;
  totalPages: number;
}

// Re-using the Zod schema from the client service
/*const ClientSchema = z.object({
  first_name: z.string().min(1, 'First name is required'),
  last_name: z.string().min(1, 'Last name is required'),
  email: z.string().email('Invalid email address').optional(),
  phone_primary: z.string().optional(),
  client_type: z.enum(['individual', 'business']),
  intake_date: z.string().optional().default(() => new Date().toISOString().split('T')[0]),
  status: z.enum(['active', 'inactive', 'pending']).default('active'),
  business_name: z.string().optional(),
  business_type: z.string().optional(),
  tax_id: z.string().optional(),
  date_of_birth: z.string().optional(),
  address: z.object({
    street: z.string().optional(),
    city: z.string().optional(),
    state: z.string().optional(),
    zip: z.string().optional(),
  }),
  assigned_attorney_id: z.string().uuid().optional(),
});*/

/**
 * GET: Fetch clients for the current tenant
 *
 * This endpoint uses the new data access layer to fetch clients.
 */
export const GET = withAuth(async (
  req: NextRequest,
  user: AuthUser,
  supabase: SupabaseClient<Database>,
  context: Record<string, unknown>
): Promise<Response> => {
  try {
    // Extract query params
    const { searchParams } = new URL(req.url);

    // Parse pagination and filters
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '20');
    const status = searchParams.get('status') || undefined;
    const client_type = searchParams.get('client_type') || undefined;
    const searchTerm = searchParams.get('search') || undefined;

    // Make sure page and limit are valid
    const validPage = Math.max(1, page);
    const validLimit = Math.min(100, Math.max(1, limit));

    // Log the user tenant ID to verify it's correct
    console.log('Fetching clients for tenant ID:', user.tenantId);

    if (!user.tenantId) {
      console.error('User does not have a tenantId');
      return NextResponse.json({ error: 'User tenant association missing.' }, { status: 400 });
    }

    // Create a data access layer
    const dataAccess = createDataAccess(supabase, user.tenantId);

    // Use the clients repository to fetch clients
    const result = await dataAccess.clients.getClients({
      page: validPage,
      limit: validLimit,
      status,
      client_type,
      searchTerm
    });

    // Create the response
    const response: ClientQueryResult = {
      clients: result.data as Client[], // Assert type here
      total: result.count,
      page: result.page,
      limit: result.limit,
      totalPages: result.totalPages
    };

    return NextResponse.json(response);
  } catch (err) {
    console.error('Error in clients route:', err instanceof Error ? err.message : String(err));
    console.error('Error stack:', err instanceof Error ? err.stack : 'No stack trace available');
    return NextResponse.json({
      error: 'Failed to fetch clients',
      details: err instanceof Error ? err.message : String(err)
    }, { status: 500 });
  }
});

/**
 * POST: Create a new client
 *
 * This endpoint uses the new data access layer to create a client.
 */
export const POST = withAuth(async (
  req: NextRequest,
  user: AuthUser,
  supabase: SupabaseClient<Database>,
  context: Record<string, unknown>
): Promise<Response> => {
  try {
    console.log('POST /api/clients user:', JSON.stringify(user, null, 2));

    if (!user.tenantId) {
      console.error('User does not have a tenantId');
      return NextResponse.json({ error: 'User tenant association missing.' }, { status: 400 });
    }

    const dataAccess = createDataAccess(supabase, user.tenantId);
    const requestBody = await req.json();
    console.log('POST /api/clients requestBody:', JSON.stringify(requestBody, null, 2));

    // Create a data access layer
    // const dataAccess = createDataAccess(supabase, user.tenantId);

    // Use the clients repository to create a client
    const client = await dataAccess.clients.create({
      ...requestBody,
      created_by: user.id
    });

    return NextResponse.json({ message: 'Client created successfully', client }, { status: 201 });
  } catch (err) {
    if (err instanceof z.ZodError) {
      return NextResponse.json({ error: 'Validation failed', details: err.errors }, { status: 400 });
    }
    console.error('Internal error in client creation:', err);
    return NextResponse.json({ error: 'Internal server error', details: String(err) }, { status: 500 });
  }
});

/**
 * PUT: Update an existing client
 *
 * This endpoint uses the new data access layer to update a client.
 */
export const PUT = withAuth(async (
  req: NextRequest,
  user: AuthUser,
  supabase: SupabaseClient<Database>,
  context: Record<string, unknown>
): Promise<Response> => {
  try {
    const body = await req.json();
    const { id, ...updateData } = body;

    if (!id || !z.string().uuid().safeParse(id).success) {
      return NextResponse.json({ error: 'Valid Client ID (UUID) is required' }, { status: 400 });
    }

    if (!user.tenantId) {
      console.error('User does not have a tenantId');
      return NextResponse.json({ error: 'User tenant association missing.' }, { status: 400 });
    }

    // Create a data access layer
    const dataAccess = createDataAccess(supabase, user.tenantId);

    try {
      // Check if the client exists
      const existingClient = await dataAccess.clients.getById(id);

      if (!existingClient) {
        return NextResponse.json({ error: 'Client not found' }, { status: 404 });
      }

      // Update the client
      const client = await dataAccess.clients.update(id, {
        ...updateData,
        updated_at: new Date().toISOString()
      });

      return NextResponse.json({ message: 'Client updated successfully', client });
    } catch (error: any) {
      if (error.message === 'Client not found') {
        return NextResponse.json({ error: 'Client not found' }, { status: 404 });
      }
      throw error;
    }
  } catch (err) {
    if (err instanceof z.ZodError) {
      return NextResponse.json({ error: 'Validation failed', details: err.errors }, { status: 400 });
    }
    console.error('Internal error in client update:', err);
    return NextResponse.json({ error: 'Internal server error', details: String(err) }, { status: 500 });
  }
});

/**
 * DELETE: Soft delete a client (change status to inactive)
 *
 * This endpoint uses the new data access layer to soft delete a client.
 */
export const DELETE = withAuth(async (
  req: NextRequest,
  user: AuthUser,
  supabase: SupabaseClient<Database>,
  context: Record<string, unknown>
): Promise<Response> => {
  try {
    const body = await req.json();
    const { id } = body;

    if (!id || !z.string().uuid().safeParse(id).success) {
      return NextResponse.json({ error: 'Valid Client ID (UUID) is required' }, { status: 400 });
    }

    if (!user.tenantId) {
      console.error('User does not have a tenantId');
      return NextResponse.json({ error: 'User tenant association missing.' }, { status: 400 });
    }

    // Create a data access layer
    const dataAccess = createDataAccess(supabase, user.tenantId);

    try {
      // Check if the client exists
      const existingClient = await dataAccess.clients.getById(id);

      if (!existingClient) {
        return NextResponse.json({ error: 'Client not found' }, { status: 404 });
      }

      // Soft delete the client by updating its status
      await dataAccess.clients.update(id, {
        status: 'inactive',
        updated_at: new Date().toISOString()
      });

      return NextResponse.json({ message: 'Client marked as inactive' });
    } catch (error: any) {
      if (error.message === 'Client not found') {
        return NextResponse.json({ error: 'Client not found' }, { status: 404 });
      }
      throw error;
    }
  } catch (err) {
    console.error('Internal error in client deletion:', err);
    return NextResponse.json({ error: 'Internal server error', details: String(err) }, { status: 500 });
  }
});
