/**
 * AG-UI Authentication Live Test
 * 
 * This is a special test file designed to be run manually when needed
 * to verify that our JWT authentication is working properly with the 
 * CopilotKit Cloud API. It makes real API calls with generated tokens.
 * 
 * To run this test:
 * 1. Make sure your .env file has all required variables set
 * 2. Run: npx ts-node src/app/api/tests/auth-live-test.ts
 */

import dotenv from 'dotenv';
import { SignJWT } from 'jose';
import fetch from 'node-fetch';

// Load environment variables from .env file
dotenv.config({ path: '.env.local' });
dotenv.config(); // Fallback to .env

// Constants for testing
const COPILOTKIT_API_URL = 'https://api.copilotkit.ai/v1';
const TEST_USER_ID = 'test-user-123';
const TEST_ORG_ID = 'test-org-456';

/**
 * Generate a test JWT token
 */
async function generateTestJWT() {
  // Ensure we have the JWT secret
  const jwtSecret = process.env.SUPABASE_JWT_SECRET;
  if (!jwtSecret) {
    throw new Error('JWT secret not configured. Set SUPABASE_JWT_SECRET in .env');
  }

  // Create a JWT token with test user and organization
  const token = await new SignJWT({
    // Standard claims
    sub: TEST_USER_ID,
    // Custom claims for tenant isolation
    organization_id: TEST_ORG_ID,
    tenant_id: TEST_ORG_ID,
    role: 'user',
    authenticated: true,
  })
    .setProtectedHeader({ alg: 'HS256' })
    .setIssuedAt()
    .setExpirationTime('15m')
    .sign(new TextEncoder().encode(jwtSecret));

  return token;
}

/**
 * Test API call with JWT authentication
 */
async function testAPIWithAuth() {
  try {
    // Generate JWT token
    const token = await generateTestJWT();
    console.log('Generated JWT token:', token);
    
    // Ensure we have the API key
    const apiKey = process.env.COPILOTKIT_API_KEY;
    if (!apiKey) {
      throw new Error('CopilotKit API key not configured. Set COPILOTKIT_API_KEY in .env');
    }

    // Create authorization header with JWT context
    const authHeader = `Bearer ${JSON.stringify({
      user_id: TEST_USER_ID,
      organization_id: TEST_ORG_ID,
      role: 'user',
      authenticated: true
    })}`;

    // Make a simple API call to verify authentication
    // This is just a test completion call to see if auth works
    const response = await fetch(`${COPILOTKIT_API_URL}/chat/completions`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': authHeader,
        'X-API-KEY': apiKey
      },
      body: JSON.stringify({
        messages: [
          { role: 'system', content: 'You are a helpful assistant.' },
          { role: 'user', content: 'Hello, can you verify authentication is working?' }
        ],
        model: 'gpt-3.5-turbo',
        stream: false
      })
    });

    // Check response
    if (response.ok) {
      const result = await response.json();
      console.log('✅ Authentication successful!');
      console.log('AI Response:', result.choices[0]?.message?.content || 'No content returned');
      return true;
    } else {
      const error = await response.text();
      console.error('❌ Authentication failed:', response.status, error);
      return false;
    }
  } catch (error) {
    console.error('❌ Error during API test:', error);
    return false;
  }
}

/**
 * Test tenant isolation by making API calls with different tenant contexts
 */
async function testTenantIsolation() {
  try {
    console.log('\n--- Testing Tenant Isolation ---');
    // Generate JWT token (already has TEST_ORG_ID)
    const token1 = await generateTestJWT();
    
    // Create authorization header for first tenant
    const authHeader1 = `Bearer ${JSON.stringify({
      user_id: TEST_USER_ID,
      organization_id: TEST_ORG_ID,
      role: 'user',
      authenticated: true
    })}`;
    
    // Create authorization header for second tenant
    const authHeader2 = `Bearer ${JSON.stringify({
      user_id: TEST_USER_ID,
      organization_id: 'different-org',
      role: 'user', 
      authenticated: true
    })}`;

    // API key
    const apiKey = process.env.COPILOTKIT_API_KEY;
    
    // First request - set a context for tenant A
    console.log('Setting context for Tenant A...');
    const response1 = await fetch(`${COPILOTKIT_API_URL}/chat/completions`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': authHeader1,
        'X-API-KEY': apiKey || ''
      },
      body: JSON.stringify({
        messages: [
          { role: 'system', content: 'You are a helpful assistant.' },
          { role: 'user', content: "Remember that the secret code is: TENANT_A_SECRET_123" }
        ],
        model: 'gpt-3.5-turbo',
        stream: false,
        thread_id: `org-${TEST_ORG_ID}-thread`
      })
    });
    
    await response1.json();
    console.log('Context set for Tenant A');
    
    // Second request - set a context for tenant B
    console.log('Setting context for Tenant B...');
    const response2 = await fetch(`${COPILOTKIT_API_URL}/chat/completions`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': authHeader2,
        'X-API-KEY': apiKey || ''
      },
      body: JSON.stringify({
        messages: [
          { role: 'system', content: 'You are a helpful assistant.' },
          { role: 'user', content: "Remember that the secret code is: TENANT_B_SECRET_456" }
        ],
        model: 'gpt-3.5-turbo',
        stream: false,
        thread_id: 'org-different-org-thread'
      })
    });
    
    await response2.json();
    console.log('Context set for Tenant B');
    
    // Now test if tenant A can access tenant A's data only
    console.log('\nTesting if Tenant A can access its own context...');
    const responseA = await fetch(`${COPILOTKIT_API_URL}/chat/completions`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': authHeader1,
        'X-API-KEY': apiKey || ''
      },
      body: JSON.stringify({
        messages: [
          { role: 'system', content: 'You are a helpful assistant.' },
          { role: 'user', content: "What was the secret code I told you earlier?" }
        ],
        model: 'gpt-3.5-turbo',
        stream: false,
        thread_id: `org-${TEST_ORG_ID}-thread`
      })
    });
    
    const resultA = await responseA.json();
    console.log('Tenant A response:', resultA.choices[0]?.message?.content);
    
    // Test if tenant B has its own isolated data
    console.log('\nTesting if Tenant B can access its own context...');
    const responseB = await fetch(`${COPILOTKIT_API_URL}/chat/completions`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': authHeader2,
        'X-API-KEY': apiKey || ''
      },
      body: JSON.stringify({
        messages: [
          { role: 'system', content: 'You are a helpful assistant.' },
          { role: 'user', content: "What was the secret code I told you earlier?" }
        ],
        model: 'gpt-3.5-turbo',
        stream: false,
        thread_id: 'org-different-org-thread'
      })
    });
    
    const resultB = await responseB.json();
    console.log('Tenant B response:', resultB.choices[0]?.message?.content);
    
    return true;
  } catch (error) {
    console.error('❌ Error during tenant isolation test:', error);
    return false;
  }
}

// Main function
async function runTests() {
  console.log('🧪 Running AG-UI Authentication Live Tests');
  console.log('---------------------------------------');
  
  const authSuccess = await testAPIWithAuth();
  
  if (authSuccess) {
    // Only test tenant isolation if authentication works
    await testTenantIsolation();
  }
  
  console.log('\n---------------------------------------');
  console.log('🧪 Tests completed');
}

// Run the tests
runTests().catch(console.error);
