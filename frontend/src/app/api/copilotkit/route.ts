/**
 * CopilotKit API route - AG-UI compatible
 * 
 * This file implements the AG-UI HTTP/SSE JSON event stream protocol for CopilotKit.
 * Includes JWT authentication, organization/user context, error handling, and graceful fallbacks.
 */
import { NextRequest, NextResponse } from 'next/server';
import { isAGUIEnabled } from '@/lib/features/ag-ui';
import { performanceMonitor } from '@/lib/performance/metrics';
import { responseCache } from '@/lib/performance/cache';
import { getStreamingConfig, StreamingPreset, applyStreamingConfig } from '@/lib/performance/streaming';
import { 
  extractJwtFromRequest, 
  validateJwt, 
  generateCopilotAuthHeader, 
  JWTValidationError 
} from '@/lib/auth/jwt-utils';
// Error handling and fallback imports
import { errorHandler } from '@/lib/error-handling/error-handler';
import { ErrorType, ErrorSeverity } from '@/lib/error-handling/error-types';
import { withFallbacks, ApiFallbackOptions } from '@/lib/error-handling/api-fallback-middleware';
import ConnectionMonitor from '@/lib/error-handling/offline-detection';
import { FallbackCache } from '@/lib/error-handling/local-cache-fallback';

/**
 * Type definitions for CopilotKit v2
 */

// Define the agent configuration for each agent type
type AgentConfig = {
  cloudAgentId: string | undefined;
};

/**
 * Configure the available agents in the application
 */
const agents: Record<string, AgentConfig> = {
  'supervisor_agent': {
    cloudAgentId: process.env.CLOUD_AGENT_ID_SUPERVISOR
  },
  'intake_agent': {
    cloudAgentId: process.env.CLOUD_AGENT_ID_INTAKE
  },
  'document_agent': {
    cloudAgentId: process.env.CLOUD_AGENT_ID_DOCUMENT
  },
  'research_agent': {
    cloudAgentId: process.env.CLOUD_AGENT_ID_RESEARCH
  },
  'event_agent': {
    cloudAgentId: process.env.CLOUD_AGENT_ID_EVENT
  }
};

/**
 * API reference configuration for CopilotKit v2
 */
const config = {
  // API key for accessing the CopilotKit cloud services
  apiKey: process.env.COPILOTKIT_API_KEY,
  // Agent configuration
  agents: agents,
  // Guardrails configuration to block unsafe content
  guardrails: {
    enabled: true,
    // Define blocked content categories
    blockedCategories: [
      'illegal',
      'sexual',
      'harmful', 
      'harassment'
    ],
    // Configure policy violation handling
    policyViolations: {
      // Report violations for monitoring but still allow response
      enableReporting: true,
      // Block responses that violate policy
      blockCompletions: true,
      // Return a specific status code for violations
      responseStatusCode: 403
    },
    // Custom message to return for policy violations
    safetyMessage: 'This request has been blocked due to our content policy. Please rephrase your request.'
  }
};

/**
 * Authentication handler for JWT validation and context extraction
 */
async function handleAuth(req: NextRequest): Promise<string | undefined> {
  try {
    // Extract JWT token from request
    const token = extractJwtFromRequest(req);
    
    // If no token is present, return undefined (anonymous access if allowed)
    if (!token) {
      console.log('No JWT token found in request');
      return undefined;
    }
    
    // Validate the JWT token
    const payload = await validateJwt(token);
    
    // Generate authorization header with context for CopilotKit
    // This passes organization and user information to maintain tenant isolation
    return generateCopilotAuthHeader(payload);
  } catch (error) {
    // Log authentication errors but don't expose details in response
    if (error instanceof JWTValidationError) {
      console.error(`JWT validation error: ${error.message}`);
    } else {
      console.error('Authentication error:', error);
    }
    
    // Return undefined for auth failures, the handler will return 401 Unauthorized
    return undefined;
  }
}

/**
 * POST handler for AG-UI chat API
 */
export const POST = withFallbacks(
  async (req: NextRequest): Promise<NextResponse> => {
  // Generate request ID for performance tracking
  const requestId = `req_${Date.now()}_${Math.random().toString(36).substring(2, 9)}`;
  
  // Start tracking performance
  performanceMonitor.startRequest(requestId);
    try {
      // Generate request ID for performance tracking
      const requestId = `req_${Date.now()}_${Math.random().toString(36).substring(2, 9)}`;
      
      // Start tracking performance
      performanceMonitor.startRequest(requestId);
      
      // Check connection status
      const connectionMonitor = ConnectionMonitor.getInstance();
      if (!connectionMonitor.isOnline()) {
        throw errorHandler.handleError(new Error('Network offline'), {
          type: ErrorType.NETWORK_OFFLINE,
          status: 503
        });
      }
      
      // Apply authentication and get context headers
      const authHeader = await handleAuth(req);
      
      // If auth fails, throw a proper auth error for better handling
      if (!authHeader) {
        throw errorHandler.handleError(new Error('Authentication failed'), {
          type: ErrorType.AUTH_INVALID,
          status: 401
        });
      }

      // Parse the request body and extract organization ID
      const body = await req.json();
      let organizationId = 'default';
      
      // Try to extract organization ID from auth header
      try {
        const authData = JSON.parse(authHeader.replace('Bearer ', ''));
        organizationId = authData.organization_id || organizationId;
      } catch (e) {
        // Use default if parsing fails
      }
      
      // Apply guardrails - check for blocked content
      const potentialViolation = checkForContentViolations(body);
      if (potentialViolation) {
        performanceMonitor.completeRequest(requestId, {
          cacheHit: false,
          tokenCount: 0
        });
        
        return new NextResponse(JSON.stringify({
          error: 'Content policy violation',
          type: potentialViolation.type,
          message: potentialViolation.message,
        }), {
          status: 403,
          headers: { 'Content-Type': 'application/json' }
        });
      }
      
      // Apply streaming configuration based on request parameters
      const streamPreset = body.stream === false ? StreamingPreset.LOW_BANDWIDTH : StreamingPreset.BALANCED;
      const optimizedBody = applyStreamingConfig(body, getStreamingConfig(streamPreset));
      
      // Check cache for this request
      const cacheResult = await responseCache.get(optimizedBody, organizationId);
      if (cacheResult.hit) {
        // If we have a cache hit, return it immediately
        performanceMonitor.completeRequest(requestId, {
          cacheHit: true,
          cacheTier: (cacheResult.source as 'memory' | 'local' | 'remote' | null),
          tokenCount: cacheResult.data.usage?.total_tokens || 0
        });
        
        console.log(`Cache hit (${cacheResult.source}) for organization ${organizationId}`);
        
        return new NextResponse(JSON.stringify(cacheResult.data), {
          headers: { 'Content-Type': 'application/json' }
        });
      }
      
      // Clone the request with our authentication header
      const modifiedReq = new Request('https://api.copilotkit.ai/v1/chat/completions', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': authHeader,
          'X-API-KEY': config.apiKey || ''
        },
        body: JSON.stringify(optimizedBody)
      });
      
      // Forward to CopilotKit Cloud API
      const response = await fetch(modifiedReq);
      
      // Check for errors
      if (!response.ok) {
        const errorText = await response.text();
        
        // Complete performance tracking
        performanceMonitor.completeRequest(requestId, {
          cacheHit: false,
          tokenCount: 0
        });
        
        return new NextResponse(errorText, {
          status: response.status,
          headers: { 'Content-Type': 'application/json' }
        });
      }
      
      // Parse the API response
      const data = await response.json();
      
      // Store in cache if not streaming
      if (!optimizedBody.stream) {
        responseCache.set(optimizedBody, data, organizationId);
      }
      
      // Complete performance tracking
      performanceMonitor.completeRequest(requestId, {
        cacheHit: false,
        tokenCount: data.usage?.total_tokens || 0
      });
      
      // Return the API response
      return new NextResponse(JSON.stringify(data), {
        headers: { 'Content-Type': 'application/json' }
      });
    } catch (error) {
      // This shouldn't be reached due to withFallbacks, but as a safeguard
      console.error('Unhandled error in POST handler:', error);
      
      // Complete performance tracking
      performanceMonitor.completeRequest(requestId, {
        cacheHit: false,
        tokenCount: 0
      });
      
      // Let the error handling middleware handle it
      throw error;
    }
  },
  {
    cacheFallback: true,
    retry: true,
    transformErrors: true
  }
);

/**
 * GET handler for AG-UI server-sent events (SSE)
 */
export const GET = withFallbacks(
  async (req: NextRequest): Promise<NextResponse> => {
  // Generate request ID for performance tracking
  const requestId = `stream_${Date.now()}_${Math.random().toString(36).substring(2, 9)}`;
  
  try {
    // Start tracking performance
    performanceMonitor.startRequest(requestId);
    
    // Apply authentication and get context headers
    const authHeader = await handleAuth(req);
    
    // If auth fails, return 401 Unauthorized
    if (!authHeader) {
      return new NextResponse(JSON.stringify({ error: 'Unauthorized' }), {
        status: 401,
        headers: { 'Content-Type': 'application/json' }
      });
    }
    
    // Parse the URL search params for streaming configuration
    const { searchParams } = new URL(req.url);
    const bodyStr = searchParams.get('body') || '{}';
    const body = JSON.parse(bodyStr);
    
    // Extract organization ID for tenant isolation
    let organizationId = 'default';
    try {
      const authData = JSON.parse(authHeader.replace('Bearer ', ''));
      organizationId = authData.organization_id || organizationId;
    } catch (e) {
      // Use default if parsing fails
    }
    
    // Apply guardrails - check for blocked content
    const potentialViolation = checkForContentViolations(body);
    if (potentialViolation) {
      performanceMonitor.completeRequest(requestId, {
        cacheHit: false,
        tokenCount: 0
      });
      
      return new NextResponse(JSON.stringify({
        error: 'Content policy violation',
        type: potentialViolation.type,
        message: potentialViolation.message,
      }), {
        status: 403,
        headers: { 'Content-Type': 'application/json' }
      });
    }
    
    // Apply streaming configuration optimized for fast first token latency
    const optimizedBody = applyStreamingConfig(body, getStreamingConfig(StreamingPreset.FAST_RESPONSE));
    
    // Set stream parameter explicitly (redundant safety check)
    optimizedBody.stream = true;
    
    // No caching for streaming responses - but we could check for cached partial responses
    // in a more advanced implementation
    
    // Construct streaming URL
    const streamingUrl = new URL('https://api.copilotkit.ai/v1/chat/completions');
    
    // Create a request with the authentication headers and optimized body
    const modifiedReq = new Request(streamingUrl.toString(), {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': authHeader,
        'X-API-KEY': config.apiKey || ''
      },
      body: JSON.stringify(optimizedBody)
    });
    
    // Forward to CopilotKit Cloud API for streaming
    const response = await fetch(modifiedReq);
    
    // Check for errors in the response
    if (!response.ok) {
      const errorText = await response.text();
      
      // Complete performance tracking
      performanceMonitor.completeRequest(requestId, {
        cacheHit: false,
        tokenCount: 0
      });
      
      return new NextResponse(errorText, {
        status: response.status,
        headers: { 'Content-Type': 'application/json' }
      });
    }
    
    // Mark the first token arrival (for response stream, we assume first token arrives now)
    performanceMonitor.markFirstToken(requestId);
    
    // We don't complete the performance tracking here because the stream will continue
    // The client-side will need to measure the complete streaming duration
    
    // Return the streaming response
    return new NextResponse(response.body, {
      status: 200,
      headers: {
        'Content-Type': 'text/event-stream',
        'Cache-Control': 'no-cache',
        'Connection': 'keep-alive' 
      }
    });
  } catch (error) {
    // This shouldn't be reached due to withFallbacks, but as a safeguard
    console.error('Unhandled error in GET handler:', error);
    
    // Complete performance tracking
    performanceMonitor.completeRequest(requestId, {
      cacheHit: false,
      tokenCount: 0
    });
    
    // Let the error handling middleware handle it
    throw error;
  }
  },
  {
  // Fallback options for GET (streaming) requests
  cacheFallback: false, // Can't cache streaming responses easily
  retry: true,
  transformErrors: true
});

/**
 * OPTIONS handler for CORS support
 */
export const OPTIONS = withFallbacks(
  async (req: NextRequest): Promise<NextResponse> => {
  return new NextResponse(null, {
    status: 200,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type, Authorization',
      'Access-Control-Max-Age': '86400', // 24 hours
    },
  });
  },
  {
  // Simple OPTIONS requests don't need retries or heavy fallbacks
  cacheFallback: false,
  retry: false,
  transformErrors: false
});

/**
 * Check for content policy violations
 * 
 * This function checks if the user's input potentially violates content policies
 * by looking for problematic keywords and patterns.
 * 
 * @param body The request body to check
 * @returns A violation object if found, or null if no violations detected
 */
function checkForContentViolations(body: any): { type: string; message: string } | null {
  try {
    // Get user messages from the request body
    const messages = body.messages || [];
    
    // Find the last user message if it exists
    const userMessages = messages.filter((msg: any) => msg.role === 'user');
    if (userMessages.length === 0) return null;
    
    const lastUserMessage = userMessages[userMessages.length - 1].content || '';
    if (!lastUserMessage) return null;
    
    // Convert to lowercase for case-insensitive matching
    const lowerMessage = lastUserMessage.toLowerCase();
    
    // Check for illegal content keywords
    const illegalKeywords = ['hack', 'steal', 'illegal', 'breach', 'criminal', 'fraud'];
    if (illegalKeywords.some(word => lowerMessage.includes(word))) {
      return {
        type: 'illegal',
        message: 'Your request contains content that may violate legal regulations. Please rephrase your request.'
      };
    }
    
    // Check for sexual content keywords
    const sexualKeywords = ['porn', 'explicit', 'sexual', 'nude', 'xxx'];
    if (sexualKeywords.some(word => lowerMessage.includes(word))) {
      return {
        type: 'sexual',
        message: 'Your request contains inappropriate content. Please rephrase your request to be more professional.'
      };
    }
    
    // Check for harmful content keywords
    const harmfulKeywords = ['suicide', 'harm', 'kill', 'hurt', 'injure', 'weapon', 'bomb'];
    if (harmfulKeywords.some(word => lowerMessage.includes(word))) {
      return {
        type: 'harmful',
        message: 'Your request contains potentially harmful content. For everyone\'s safety, please rephrase your request.'
      };
    }
    
    // Check for harassment content keywords
    const harassmentKeywords = ['insult', 'bully', 'hate', 'stupid', 'idiot', 'moron'];
    if (harassmentKeywords.some(word => lowerMessage.includes(word))) {
      return {
        type: 'harassment',
        message: 'Your request contains content that could be perceived as harassment. Please rephrase your request.'
      };
    }
    
    // No violations found
    return null;
  } catch (error) {
    console.error('Error checking for content violations:', error);
    return null; // Continue if there's an error in the check
  }
}
