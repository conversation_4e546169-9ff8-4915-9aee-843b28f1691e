/**
 * CopilotKit AG-UI Authentication Integration Tests
 * 
 * These tests verify the integration between our JWT authentication system and 
 * the CopilotKit API endpoint, including tenant isolation and token validation.
 */

import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest';
import { NextRequest, NextResponse } from 'next/server';

// Mock JWT utilities
vi.mock('@/lib/auth/jwt-utils', () => ({
  extractJwtFromRequest: vi.fn(),
  validateJwt: vi.fn(),
  generateCopilotAuthHeader: vi.fn(),
  JWTValidationError: class JWTValidationError extends Error {
    constructor(message: string, public status = 401) {
      super(message);
      this.name = 'JWTValidationError';
    }
  }
}));

// Mock API reference
vi.mock('@copilotkit/runtime', () => ({
  apiReference: {
    chat: vi.fn(),
    streamingChat: vi.fn()
  }
}));

describe('CopilotKit API Authentication Integration', () => {
  let mockRequest: NextRequest;
  let apiReference: any;

  beforeEach(() => {
    // Reset all mocks before each test
    vi.resetAllMocks();

    // Get the mocked apiReference
    apiReference = require('@copilotkit/runtime').apiReference;

    // Create a mock request
    mockRequest = new NextRequest('https://example.com/api/copilotkit', {
      method: 'POST'
    });

    // Setup basic successful auth flow as default
    const jwtUtils = require('@/lib/auth/jwt-utils');
    jwtUtils.extractJwtFromRequest.mockReturnValue('mock-token');
    jwtUtils.validateJwt.mockResolvedValue({
      sub: 'user-123',
      organization_id: 'org-456',
      tenant_id: 'org-456',
      role: 'user'
    });
    jwtUtils.generateCopilotAuthHeader.mockReturnValue('Bearer mock-auth-header');

    // Mock apiReference.chat to return a basic response
    apiReference.chat.mockResolvedValue(
      new NextResponse(JSON.stringify({ ok: true }), {
        status: 200,
        headers: { 'Content-Type': 'application/json' }
      })
    );
  });

  afterEach(() => {
    vi.restoreAllMocks();
  });

  describe('Authentication flow', () => {
    it('should extract and validate JWT tokens correctly', async () => {
      // Import the handlers directly from the route file
      const { POST } = require('../route');
      
      // Execute the handler
      await POST(mockRequest);
      
      // Verify JWT extraction and validation
      const jwtUtils = require('@/lib/auth/jwt-utils');
      expect(jwtUtils.extractJwtFromRequest).toHaveBeenCalledWith(mockRequest);
      expect(jwtUtils.validateJwt).toHaveBeenCalledWith('mock-token');
    });
    
    it('should forward authenticated requests to CopilotKit with auth headers', async () => {
      // Import the handlers directly from the route file
      const { POST } = require('../route');
      
      // Execute the handler
      await POST(mockRequest);
      
      // Verify API reference was called with auth headers
      expect(apiReference.chat).toHaveBeenCalledWith(
        mockRequest, 
        expect.anything(), // config object
        { Authorization: 'Bearer mock-auth-header' }
      );
    });
    
    it('should handle missing tokens gracefully', async () => {
      // Setup extraction to return no token
      const jwtUtils = require('@/lib/auth/jwt-utils');
      jwtUtils.extractJwtFromRequest.mockReturnValue(undefined);
      
      // Import the handlers directly from the route file
      const { POST } = require('../route');
      
      // Execute the handler
      await POST(mockRequest);
      
      // Verify API reference was called without auth headers (undefined)
      expect(apiReference.chat).toHaveBeenCalledWith(
        mockRequest, 
        expect.anything(), // config object
        { Authorization: undefined }
      );
    });
  });
  
  describe('Tenant isolation', () => {
    it('should pass different authorization context for different tenants', async () => {
      const jwtUtils = require('@/lib/auth/jwt-utils');
      
      // First tenant
      jwtUtils.validateJwt.mockResolvedValueOnce({
        sub: 'user-123',
        organization_id: 'tenant-A',
        tenant_id: 'tenant-A'
      });
      jwtUtils.generateCopilotAuthHeader.mockReturnValueOnce('Bearer tenant-A-context');
      
      // Import the handlers
      const { POST } = require('../route');
      
      // Execute for first tenant
      await POST(mockRequest);
      
      // Verify first tenant auth headers
      expect(apiReference.chat).toHaveBeenLastCalledWith(
        mockRequest, 
        expect.anything(),
        { Authorization: 'Bearer tenant-A-context' }
      );
      
      // Second tenant
      jwtUtils.validateJwt.mockResolvedValueOnce({
        sub: 'user-123', // Same user
        organization_id: 'tenant-B',
        tenant_id: 'tenant-B'
      });
      jwtUtils.generateCopilotAuthHeader.mockReturnValueOnce('Bearer tenant-B-context');
      
      // Execute for second tenant
      await POST(mockRequest);
      
      // Verify second tenant auth headers
      expect(apiReference.chat).toHaveBeenLastCalledWith(
        mockRequest, 
        expect.anything(),
        { Authorization: 'Bearer tenant-B-context' }
      );
    });
  });
  
  describe('Error handling', () => {
    it('should handle token validation errors', async () => {
      // Setup validation to throw an error
      const jwtUtils = require('@/lib/auth/jwt-utils');
      jwtUtils.validateJwt.mockRejectedValueOnce(
        new jwtUtils.JWTValidationError('Invalid token')
      );
      
      // Import the handlers
      const { POST } = require('../route');
      
      // Execute the handler
      await POST(mockRequest);
      
      // Verify API reference was called without auth headers
      expect(apiReference.chat).toHaveBeenCalledWith(
        mockRequest, 
        expect.anything(),
        { Authorization: undefined }
      );
    });
    
    it('should handle unexpected errors during auth', async () => {
      // Setup extraction to throw an unexpected error
      const jwtUtils = require('@/lib/auth/jwt-utils');
      jwtUtils.extractJwtFromRequest.mockImplementationOnce(() => {
        throw new Error('Unexpected error');
      });
      
      // Import the handlers
      const { POST } = require('../route');
      
      // Execute the handler - it should not crash
      await POST(mockRequest);
      
      // Verify API reference was called without auth headers
      expect(apiReference.chat).toHaveBeenCalledWith(
        mockRequest, 
        expect.anything(),
        { Authorization: undefined }
      );
    });
  });
  
  describe('Streaming handlers', () => {
    it('should apply the same authentication to streaming requests', async () => {
      // Import the handlers directly from the route file
      const { GET } = require('../route');
      
      // Execute the handler
      await GET(mockRequest);
      
      // Verify JWT extraction and validation
      const jwtUtils = require('@/lib/auth/jwt-utils');
      expect(jwtUtils.extractJwtFromRequest).toHaveBeenCalledWith(mockRequest);
      expect(jwtUtils.validateJwt).toHaveBeenCalledWith('mock-token');
      
      // Verify streaming API was called with auth headers
      expect(apiReference.streamingChat).toHaveBeenCalledWith(
        mockRequest, 
        expect.anything(),
        { Authorization: 'Bearer mock-auth-header' }
      );
    });
  });
});
