/**
 * Tests for the CopilotKit LangGraph API Route
 * 
 * This file contains tests for the LangGraph API route implementation.
 */
import { NextRequest } from 'next/server';
import { POST, GET } from './route';
import { jest } from '@jest/globals';

// Mock the performance monitor
jest.mock('@/lib/performance/metrics', () => ({
  performanceMonitor: {
    startRequest: jest.fn(),
    completeRequest: jest.fn(),
    markFirstToken: jest.fn(),
  },
}));

// Mock the connection monitor
jest.mock('@/lib/error-handling/offline-detection', () => ({
  __esModule: true,
  default: {
    getInstance: jest.fn(() => ({
      isOnline: jest.fn(() => true),
    })),
  },
}));

// Mock the JWT utils
jest.mock('@/lib/auth/jwt-utils', () => ({
  extractJwtFromRequest: jest.fn(),
  validateJwt: jest.fn(),
  generateCopilotAuthHeader: jest.fn(),
  JWTValidationError: class JWTValidationError extends Error {},
}));

// Mock the error handler
jest.mock('@/lib/error-handling/error-handler', () => ({
  errorHandler: {
    handleError: jest.fn((error: any) => error),
  },
}));

// Mock the AG-UI feature flag
jest.mock('@/lib/features/ag-ui', () => ({
  isAGUIEnabled: jest.fn(() => true),
}));

// Mock the withFallbacks middleware
jest.mock('@/lib/error-handling/api-fallback-middleware', () => ({
  withFallbacks: (fn: any) => fn,
}));

// Mock the global fetch function
global.fetch = jest.fn();

describe('CopilotKit LangGraph API Route', () => {
  beforeEach(() => {
    // Reset all mocks
    jest.clearAllMocks();
  });

  describe('POST handler', () => {
    it('should forward requests to the FastAPI backend', async () => {
      // Mock the request
      const req = new NextRequest('http://localhost:3000/api/copilotkit/langgraph', {
        method: 'POST',
        body: JSON.stringify({
          messages: [{ role: 'user', content: 'Hello' }],
          threadId: 'test-thread',
        }),
      });

      // Mock the fetch response
      (global.fetch as jest.Mock).mockResolvedValueOnce({
        ok: true,
        json: async () => ({
          messages: [{ role: 'assistant', content: 'Echo: Hello' }],
          done: true,
          threadId: 'test-thread',
        }),
      });

      // Call the handler
      const response = await POST(req);
      
      // Verify the response
      expect(response.status).toBe(200);
      
      // Verify that fetch was called with the correct arguments
      expect(global.fetch).toHaveBeenCalledTimes(1);
      const fetchCall = (global.fetch as jest.Mock).mock.calls[0][0];
      expect(fetchCall.url).toContain('/copilotkit');
      
      // Verify the request body
      const requestBody = JSON.parse(await fetchCall.json());
      expect(requestBody.agent).toBe('echo_agent');
      expect(requestBody.stream).toBe(false);
      expect(requestBody.messages).toEqual([{ role: 'user', content: 'Hello' }]);
    });

    it('should handle errors from the FastAPI backend', async () => {
      // Mock the request
      const req = new NextRequest('http://localhost:3000/api/copilotkit/langgraph', {
        method: 'POST',
        body: JSON.stringify({
          messages: [{ role: 'user', content: 'Hello' }],
          threadId: 'test-thread',
        }),
      });

      // Mock the fetch response with an error
      (global.fetch as jest.Mock).mockResolvedValueOnce({
        ok: false,
        status: 500,
        text: async () => JSON.stringify({ error: 'Internal server error' }),
      });

      // Call the handler
      const response = await POST(req);
      
      // Verify the response
      expect(response.status).toBe(500);
    });
  });

  describe('GET handler', () => {
    it('should forward streaming requests to the FastAPI backend', async () => {
      // Mock the request
      const req = new NextRequest('http://localhost:3000/api/copilotkit/langgraph?body=' + 
        encodeURIComponent(JSON.stringify({
          messages: [{ role: 'user', content: 'Hello' }],
          threadId: 'test-thread',
        })), {
        method: 'GET',
      });

      // Create a mock ReadableStream for the response body
      const mockStream = new ReadableStream({
        start(controller) {
          controller.enqueue('data: {"type":"start","threadId":"test-thread"}\n\n');
          controller.enqueue('data: {"type":"content","content":"Hello"}\n\n');
          controller.enqueue('data: {"type":"done","threadId":"test-thread"}\n\n');
          controller.close();
        }
      });

      // Mock the fetch response
      (global.fetch as jest.Mock).mockResolvedValueOnce({
        ok: true,
        body: mockStream,
      });

      // Call the handler
      const response = await GET(req);
      
      // Verify the response
      expect(response.status).toBe(200);
      expect(response.headers.get('Content-Type')).toBe('text/event-stream');
      
      // Verify that fetch was called with the correct arguments
      expect(global.fetch).toHaveBeenCalledTimes(1);
      const fetchCall = (global.fetch as jest.Mock).mock.calls[0][0];
      expect(fetchCall.url).toContain('/copilotkit');
      
      // Verify the request body
      const requestBody = JSON.parse(await fetchCall.json());
      expect(requestBody.agent).toBe('echo_agent');
      expect(requestBody.stream).toBe(true);
      expect(requestBody.messages).toEqual([{ role: 'user', content: 'Hello' }]);
    });
  });
});
