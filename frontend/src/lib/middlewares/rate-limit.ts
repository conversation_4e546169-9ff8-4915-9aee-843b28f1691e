/**
 * Rate limit utilities for API routes
 * Provides a direct rate limiting function that can be called within route handlers
 */

import Redis from 'ioredis';
import { isProduction } from '@/lib/utils';

// Initialize Redis client
const redisUrl = process.env.REDIS_URL || 'redis://localhost:6379';
let redis: Redis | null = null;

// Only initialize Redis in production or if explicitly enabled for development
const useRedis = isProduction() || process.env.ENABLE_REDIS_DEV === 'true';

if (useRedis) {
  try {
    redis = new Redis(redisUrl);
    console.log('Redis client initialized for rate limiting');

    // Handle Redis connection errors
    redis.on('error', (err: Error) => {
      console.error('Redis connection error:', err);
    });
  } catch (error) {
    console.error('Failed to initialize Redis client:', error);
    redis = null;
  }
}

interface RateLimitResult {
  success: boolean;
  current: number;
  limit: number;
  retryAfter: number; // seconds until reset
}

/**
 * Check and apply rate limiting for a specific action
 *
 * @param action The action being rate limited (e.g., 'api_call', 'document_upload')
 * @param limit Maximum number of requests allowed in the window
 * @param windowSeconds Time window in seconds
 * @param identifier Unique identifier (e.g., user ID, IP address)
 * @returns Result object with rate limiting information
 */
export async function rateLimit(
  action: string,
  limit: number,
  windowSeconds: number,
  identifier: string
): Promise<RateLimitResult> {
  // If Redis is not available, allow all requests
  if (!redis) {
    return {
      success: true,
      current: 1,
      limit,
      retryAfter: 0
    };
  }

  const key = `ratelimit:${action}:${identifier}`;

  try {
    // Get current count and increment
    const currentCount = await redis.incr(key);

    // Set expiry if this is the first request in the window
    if (currentCount === 1) {
      await redis.expire(key, windowSeconds);
    }

    // Get TTL (time to live) for the key
    const ttl = await redis.ttl(key);

    // Check if the limit has been exceeded
    if (currentCount > limit) {
      return {
        success: false,
        current: currentCount,
        limit,
        retryAfter: ttl > 0 ? ttl : windowSeconds
      };
    }

    // Request is allowed
    return {
      success: true,
      current: currentCount,
      limit,
      retryAfter: 0
    };
  } catch (error) {
    console.error('Rate limit error:', error);

    // On error, allow the request but log the issue
    return {
      success: true,
      current: 0,
      limit,
      retryAfter: 0
    };
  }
}
