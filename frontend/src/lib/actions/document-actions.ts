/**
 * Document-related actions for CopilotKit
 * Implements AG-UI protocol format exclusively for v2.x
 */
import { useCopilotAction } from "@copilotkit/react-core";
import { validateActionInput, createActionSuccess, ensureRequiredFields } from '../utils/ag-actions';
import { z } from "zod";

// Schema for document data
export const DocumentSchema = z.object({
  id: z.string().optional(),
  title: z.string(),
  content: z.string(),
  variables: z.record(z.string(), z.string().optional()).optional(),
  templateId: z.string().optional(),
});

export type DocumentData = z.infer<typeof DocumentSchema>;

// Parameter interfaces for document actions
interface SaveDocumentParams {
  document: DocumentData;
}

/**
 * Server action to save a document
 * This is the actual implementation that gets called by both legacy and AG-UI formats
 */
export async function saveDocumentData(documentData: DocumentData): Promise<{
  success: boolean;
  document_id?: string;
  error?: string;
  message?: string;
}> {
  console.log('Server Action: saveDocumentData received:', documentData);

  try {
    // Validate the document data
    const validationResult = DocumentSchema.safeParse(documentData);
    if (!validationResult.success) {
      console.error('Server Action Validation Error:', validationResult.error);
      return {
        success: false,
        error: `Invalid document data: ${validationResult.error.errors.map(e => e.message).join(', ')}`
      };
    }

    // In a real implementation, this would save to the database
    // For now, we'll just simulate a successful save
    const newId = documentData.id || `doc_${Date.now()}`;

    // In a production app, this would call a database API or server action
    // const response = await fetch('/api/documents', {
    //   method: 'POST',
    //   headers: { 'Content-Type': 'application/json' },
    //   body: JSON.stringify(validationResult.data)
    // });

    // Return success response
    return {
      success: true,
      document_id: newId,
      message: `Document "${documentData.title}" saved successfully!`
    };
  } catch (error) {
    console.error('Error saving document:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'An unknown error occurred'
    };
  }
}

/**
 * Hook to use the save document action with CopilotKit
 * Implements AG-UI format for v2.x
 */
export function useSaveDocumentAction() {
  useCopilotAction({
    name: 'saveDocument',
    description: 'Save the current document draft',
    parameters: [
      {
        name: 'document',
        type: 'object',
        description: 'The document data to save',
        required: true
      }
    ],
    handler: async (args: any) => {
      try {
        // Extract document data from args
        const documentData = args.document as Record<string, any>;
        
        // Ensure required fields are present
        ensureRequiredFields(documentData, ['title', 'content']);
        
        // Validate document data against schema
        const validatedData = validateActionInput(documentData, DocumentSchema) as DocumentData;
        
        // Call server action with validated data
        const result = await saveDocumentData(validatedData);

        if (result.success) {
          return createActionSuccess(
            result.message || `Document saved! ID: ${result.document_id}`,
            { documentId: result.document_id }
          );
        } else {
          throw new Error(result.error || 'Failed to save document.');
        }
      } catch (error) {
        // Handle any errors in the validation or document saving process
        console.error('Error in saveDocument action:', error);
        throw new Error(error instanceof Error ? error.message : 'Unknown error in document submission');
      }
    }
  });
}
