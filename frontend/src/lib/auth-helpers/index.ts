// Barrel file for auth-helpers
// Minimal exports to fix TypeScript errors

// Primary auth function - this is what most files need
export { withAuth, withServiceRole, createServiceClient } from '../supabase/api-helpers';
export type { AuthRouteHandler } from '../supabase/api-helpers';

// Auth types from types/auth
export { UserRole } from '../types/auth';
export type { AuthUser } from '../types/auth';

// Additional auth functions that some files need
export { createServerClientForUser } from '../auth-helpers';
export type { RouteHandlerWithAuth } from '../auth-helpers';

// MFA and WebAuthn functions
export * from './mfa';
export * from './webauthn';

// Special auth callback function
// Note: withAuthCallback was removed as it was a duplicate implementation
