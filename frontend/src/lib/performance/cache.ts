/**
 * Caching System for AG-UI Integration
 * 
 * This module provides a multi-level caching system for CopilotKit responses:
 * - In-memory cache for fastest access (cleared on page reload)
 * - Local storage cache for persistence across page loads
 * - Organization-specific caching to maintain tenant isolation
 */

import { v5 as uuidv5 } from 'uuid';
import { createClient } from '@supabase/supabase-js';

// Define cache entry structure
export interface CacheEntry {
  response: any;
  timestamp: number;
  expiry: number;
  orgId: string;
  metadata?: Record<string, any>;
}

// Cache configuration
export interface CacheConfig {
  useMemoryCache: boolean;
  useLocalStorageCache: boolean;
  useSupabaseCache: boolean;
  defaultTTL: number; // Time-to-live in milliseconds
  namespaceUUID: string; // UUID namespace for consistent hashing
}

// Default configuration
const DEFAULT_CONFIG: CacheConfig = {
  useMemoryCache: true,
  useLocalStorageCache: true,
  useSupabaseCache: false, // Remote caching disabled by default
  defaultTTL: 1000 * 60 * 60, // 1 hour
  namespaceUUID: '6ba7b810-9dad-11d1-80b4-00c04fd430c8' // Default UUID namespace
};

/**
 * Generate a cache key for a request
 */
function generateCacheKey(request: any, organizationId: string): string {
  try {
    // Extract messages for normalized cache key
    let messages = [];
    if (request.messages && Array.isArray(request.messages)) {
      // Deep copy and normalize messages
      messages = request.messages.map((msg: any) => ({
        role: msg.role,
        content: msg.content,
        // Extract only relevant fields, removing timestamps and IDs
      }));
    }
    
    // Create a deterministic representation for cache key
    const keyInput = JSON.stringify({
      messages,
      model: request.model,
      temperature: request.temperature,
      max_tokens: request.max_tokens,
      organization: organizationId, // Include org ID for tenant isolation
    });
    
    // Generate UUID v5 based on input and namespace
    return uuidv5(keyInput, DEFAULT_CONFIG.namespaceUUID);
  } catch (error) {
    console.error("Error generating cache key:", error);
    // Fallback to a timestamp-based key
    return `fallback-${Date.now()}-${Math.random().toString(36).substring(2, 9)}`;
  }
}

/**
 * AG-UI Response Cache
 */
export class ResponseCache {
  private static instance: ResponseCache;
  private memoryCache: Map<string, CacheEntry> = new Map();
  private config: CacheConfig;
  private supabaseClient: any | null = null;
  
  private constructor(config: Partial<CacheConfig> = {}) {
    this.config = { ...DEFAULT_CONFIG, ...config };
    
    // Initialize Supabase client if remote caching is enabled
    if (this.config.useSupabaseCache && 
        typeof process !== 'undefined' && 
        process.env.NEXT_PUBLIC_SUPABASE_URL &&
        process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY) {
      this.supabaseClient = createClient(
        process.env.NEXT_PUBLIC_SUPABASE_URL,
        process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY
      );
    }
    
    // Clean up expired memory cache entries periodically
    if (typeof window !== 'undefined') {
      setInterval(() => this.cleanupExpiredEntries(), 5 * 60 * 1000); // Every 5 minutes
    }
  }
  
  public static getInstance(config?: Partial<CacheConfig>): ResponseCache {
    if (!ResponseCache.instance) {
      ResponseCache.instance = new ResponseCache(config);
    }
    return ResponseCache.instance;
  }
  
  /**
   * Get a cached response
   */
  public async get(
    request: any, 
    organizationId: string
  ): Promise<{ hit: boolean; source: string | null; data: any }> {
    const key = generateCacheKey(request, organizationId);
    const now = Date.now();
    
    // Try memory cache first (fastest)
    if (this.config.useMemoryCache) {
      const memoryEntry = this.memoryCache.get(key);
      if (memoryEntry && memoryEntry.expiry > now && memoryEntry.orgId === organizationId) {
        return { 
          hit: true, 
          source: 'memory',
          data: memoryEntry.response
        };
      }
    }
    
    // Try local storage cache
    if (this.config.useLocalStorageCache && typeof window !== 'undefined') {
      try {
        const storageKey = `agui-cache-${key}`;
        const storedValue = localStorage.getItem(storageKey);
        
        if (storedValue) {
          const entry = JSON.parse(storedValue) as CacheEntry;
          
          // Check if the entry is valid and belongs to the correct organization
          if (entry.expiry > now && entry.orgId === organizationId) {
            // Refresh in memory cache too
            if (this.config.useMemoryCache) {
              this.memoryCache.set(key, entry);
            }
            
            return {
              hit: true,
              source: 'local',
              data: entry.response
            };
          }
          
          // Clean up expired item
          localStorage.removeItem(storageKey);
        }
      } catch (error) {
        console.error('Error accessing local storage cache:', error);
      }
    }
    
    // Try Supabase cache if enabled
    if (this.config.useSupabaseCache && this.supabaseClient) {
      try {
        const { data, error } = await this.supabaseClient
          .from('response_cache')
          .select('*')
          .eq('cache_key', key)
          .eq('organization_id', organizationId)
          .gt('expiry', new Date().toISOString())
          .single();
        
        if (data && !error) {
          const entry: CacheEntry = {
            response: JSON.parse(data.response),
            timestamp: new Date(data.created_at).getTime(),
            expiry: new Date(data.expiry).getTime(),
            orgId: data.organization_id
          };
          
          // Update memory cache too
          if (this.config.useMemoryCache) {
            this.memoryCache.set(key, entry);
          }
          
          return {
            hit: true,
            source: 'remote',
            data: entry.response
          };
        }
      } catch (error) {
        console.error('Error accessing remote cache:', error);
      }
    }
    
    // No cache hit
    return { hit: false, source: null, data: null };
  }
  
  /**
   * Store a response in the cache
   */
  public async set(
    request: any,
    response: any,
    organizationId: string,
    ttl: number = this.config.defaultTTL
  ): Promise<void> {
    const key = generateCacheKey(request, organizationId);
    const now = Date.now();
    
    const entry: CacheEntry = {
      response,
      timestamp: now,
      expiry: now + ttl,
      orgId: organizationId
    };
    
    // Store in memory cache
    if (this.config.useMemoryCache) {
      this.memoryCache.set(key, entry);
    }
    
    // Store in local storage
    if (this.config.useLocalStorageCache && typeof window !== 'undefined') {
      try {
        const storageKey = `agui-cache-${key}`;
        localStorage.setItem(storageKey, JSON.stringify(entry));
      } catch (error) {
        console.error('Error saving to local storage cache:', error);
      }
    }
    
    // Store in Supabase if enabled
    if (this.config.useSupabaseCache && this.supabaseClient) {
      try {
        await this.supabaseClient
          .from('response_cache')
          .upsert({
            cache_key: key,
            request: JSON.stringify(request),
            response: JSON.stringify(response),
            organization_id: organizationId,
            created_at: new Date().toISOString(),
            expiry: new Date(now + ttl).toISOString()
          }, { onConflict: 'cache_key' });
      } catch (error) {
        console.error('Error saving to remote cache:', error);
      }
    }
  }
  
  /**
   * Remove expired entries from memory cache
   */
  private cleanupExpiredEntries(): void {
    const now = Date.now();
    
    // Clean memory cache
    for (const [key, entry] of this.memoryCache.entries()) {
      if (entry.expiry <= now) {
        this.memoryCache.delete(key);
      }
    }
    
    // Clean localStorage cache (only check a few random keys to avoid performance issues)
    if (this.config.useLocalStorageCache && typeof window !== 'undefined') {
      try {
        const maxKeysToCheck = 10;
        const keysToCheck = [];
        
        // Find cache keys in local storage
        for (let i = 0; i < localStorage.length && keysToCheck.length < maxKeysToCheck; i++) {
          const key = localStorage.key(i);
          if (key?.startsWith('agui-cache-')) {
            keysToCheck.push(key);
          }
        }
        
        // Check each key and remove if expired
        keysToCheck.forEach(key => {
          try {
            const entry = JSON.parse(localStorage.getItem(key) || '{}') as CacheEntry;
            if (entry.expiry <= now) {
              localStorage.removeItem(key);
            }
          } catch (e) {
            // If JSON is invalid, remove the entry
            localStorage.removeItem(key);
          }
        });
      } catch (error) {
        // Ignore localStorage errors
      }
    }
  }
  
  /**
   * Clear all cached entries for an organization
   */
  public clearOrganizationCache(organizationId: string): void {
    // Clear memory cache
    for (const [key, entry] of this.memoryCache.entries()) {
      if (entry.orgId === organizationId) {
        this.memoryCache.delete(key);
      }
    }
    
    // Clear localStorage cache
    if (this.config.useLocalStorageCache && typeof window !== 'undefined') {
      try {
        for (let i = 0; i < localStorage.length; i++) {
          const key = localStorage.key(i);
          if (key?.startsWith('agui-cache-')) {
            try {
              const entry = JSON.parse(localStorage.getItem(key) || '{}') as CacheEntry;
              if (entry.orgId === organizationId) {
                localStorage.removeItem(key);
              }
            } catch (e) {
              // If JSON is invalid, skip
            }
          }
        }
      } catch (error) {
        // Ignore localStorage errors
      }
    }
    
    // Clear Supabase cache
    if (this.config.useSupabaseCache && this.supabaseClient) {
      this.supabaseClient
        .from('response_cache')
        .delete()
        .eq('organization_id', organizationId)
        .then(() => {
          console.log(`Cleared remote cache for organization ${organizationId}`);
        })
        .catch((error: any) => {
          console.error('Error clearing remote cache:', error);
        });
    }
  }
  
  /**
   * Clear all cached entries
   */
  public clearAllCache(): void {
    // Clear memory cache
    this.memoryCache.clear();
    
    // Clear localStorage cache
    if (this.config.useLocalStorageCache && typeof window !== 'undefined') {
      try {
        for (let i = localStorage.length - 1; i >= 0; i--) {
          const key = localStorage.key(i);
          if (key?.startsWith('agui-cache-')) {
            localStorage.removeItem(key);
          }
        }
      } catch (error) {
        // Ignore localStorage errors
      }
    }
    
    // Clear Supabase cache
    if (this.config.useSupabaseCache && this.supabaseClient) {
      this.supabaseClient
        .from('response_cache')
        .delete()
        .then(() => {
          console.log('Cleared all remote cache entries');
        })
        .catch((error: any) => {
          console.error('Error clearing remote cache:', error);
        });
    }
  }
}

// Export singleton instance
export const responseCache = ResponseCache.getInstance();

/**
 * Hook for using the cache in React components
 */
export function useResponseCache() {
  return {
    getFromCache: async (request: any, organizationId: string) => {
      return responseCache.get(request, organizationId);
    },
    
    saveToCache: async (request: any, response: any, organizationId: string, ttl?: number) => {
      return responseCache.set(request, response, organizationId, ttl);
    },
    
    clearOrganizationCache: (organizationId: string) => {
      responseCache.clearOrganizationCache(organizationId);
    },
    
    clearAllCache: () => {
      responseCache.clearAllCache();
    }
  };
}
