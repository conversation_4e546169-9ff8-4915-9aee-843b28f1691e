/**
 * Monitoring System Initialization
 * 
 * This file exports functions to initialize and configure the monitoring system
 * for the AG-UI integration, including error reporting and performance tracking.
 */

import { Error<PERSON><PERSON>ort<PERSON>, ErrorReporterConfig } from './error-reporter';
import { errorHandler } from '@/lib/error-handling/error-handler';
import { AGError } from '@/lib/error-handling/error-classes';

// Initialize the monitoring system
export function initializeMonitoring(config?: {
  errorReporting?: ErrorReporterConfig
}): void {
  // Initialize error reporter
  const reporter = ErrorReporter.getInstance(config?.errorReporting);
  
  // Connect error handler to error reporter
  if (config?.errorReporting?.enabled) {
    // Subscribe to error handler events to automatically report errors
    errorHandler.subscribe((error: AGError) => {
      reporter.reportError(error);
    });
    
    console.log('Error reporting initialized and connected to error handler');
  }
}

// Export error reporter
export { 
  ErrorReporter, 
  useErrorReporter 
} from './error-reporter';

// Default export for easy importing
export default {
  initialize: initializeMonitoring,
  ErrorReporter
};
