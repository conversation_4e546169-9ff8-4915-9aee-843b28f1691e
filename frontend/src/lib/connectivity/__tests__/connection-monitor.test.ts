import ConnectionMonitor from '../../error-handling/offline-detection';

describe('ConnectionMonitor', () => {
  let connectionMonitor: ConnectionMonitor;
  let originalAddEventListener: any;
  let originalRemoveEventListener: any;
  let callbacks: {[key: string]: EventListenerOrEventListenerObject} = {};
  
  beforeEach(() => {
    // Mock window event listeners
    originalAddEventListener = window.addEventListener;
    originalRemoveEventListener = window.removeEventListener;
    
    callbacks = {};
    
    window.addEventListener = jest.fn((event, callback) => {
      callbacks[event] = callback;
    });
    
    window.removeEventListener = jest.fn((event) => {
      delete callbacks[event];
    });
    
    // Create a fresh instance for each test
    // @ts-ignore - accessing private property for testing
    ConnectionMonitor.instance = undefined;
    connectionMonitor = ConnectionMonitor.getInstance();
  });
  
  afterEach(() => {
    // Restore original window methods
    window.addEventListener = originalAddEventListener;
    window.removeEventListener = originalRemoveEventListener;
    
    // Cleanup any registered callbacks
    if (connectionMonitor) {
      // ConnectionMonitor doesn't have a cleanup method in the current implementation
      // The cleanup is handled automatically
    }
  });
  
  describe('getInstance', () => {
    it('should return the same instance when called multiple times', () => {
      const instance1 = ConnectionMonitor.getInstance();
      const instance2 = ConnectionMonitor.getInstance();
      
      expect(instance1).toBe(instance2);
    });
  });
  
  describe('initialization', () => {
    it('should register online and offline event listeners', () => {
      expect(window.addEventListener).toHaveBeenCalledWith('online', expect.any(Function));
      expect(window.addEventListener).toHaveBeenCalledWith('offline', expect.any(Function));
    });
    
    it('should initialize with ONLINE state when navigator is online', () => {
      // Mock navigator.onLine
      Object.defineProperty(navigator, 'onLine', {
        configurable: true,
        value: true
      });
      
      // Reset to get a fresh instance with the mocked navigator
      // @ts-ignore - accessing private property for testing
      ConnectionMonitor.instance = undefined;
      connectionMonitor = ConnectionMonitor.getInstance();
      
      expect(connectionMonitor.getStatus()).toBe('online');
    });
    
    it('should initialize with OFFLINE state when navigator is offline', () => {
      // Mock navigator.onLine
      Object.defineProperty(navigator, 'onLine', {
        configurable: true,
        value: false
      });
      
      // Reset to get a fresh instance with the mocked navigator
      // @ts-ignore - accessing private property for testing
      ConnectionMonitor.instance = undefined;
      connectionMonitor = ConnectionMonitor.getInstance();
      
      expect(connectionMonitor.getStatus()).toBe('offline');
    });
  });
  
  describe('event handling', () => {
    it('should update state to ONLINE when online event is triggered', () => {
      // Start in OFFLINE state
      // @ts-ignore - accessing private property for testing
      connectionMonitor.status = 'offline';

      // Trigger online event
      (callbacks['online'] as any)();

      expect(connectionMonitor.getStatus()).toBe('online');
    });

    it('should update state to OFFLINE when offline event is triggered', () => {
      // Start in ONLINE state
      // @ts-ignore - accessing private property for testing
      connectionMonitor.status = 'online';

      // Trigger offline event
      (callbacks['offline'] as any)();

      expect(connectionMonitor.getStatus()).toBe('offline');
    });
    
    it('should call registered listeners when state changes', () => {
      const mockListener = jest.fn();

      connectionMonitor.subscribe(mockListener);

      // Trigger state change
      (callbacks['offline'] as any)();

      expect(mockListener).toHaveBeenCalledWith('offline');
    });

    it('should allow removing state change listeners', () => {
      const mockListener = jest.fn();

      const unsubscribe = connectionMonitor.subscribe(mockListener);
      unsubscribe();

      // Trigger state change
      (callbacks['offline'] as any)();

      expect(mockListener).toHaveBeenCalledTimes(1); // Only the initial call
    });
  });
  
  describe('performHealthCheck', () => {
    let originalFetch: any;
    
    beforeEach(() => {
      originalFetch = global.fetch;
    });
    
    afterEach(() => {
      global.fetch = originalFetch;
    });
    
    it('should update state to ONLINE if health check passes', async () => {
      // Mock successful fetch
      global.fetch = jest.fn().mockResolvedValue({
        ok: true,
        status: 200
      });

      // Start in OFFLINE state
      // @ts-ignore - accessing private property for testing
      connectionMonitor.status = 'offline';

      await (connectionMonitor as any).performHealthCheck();

      expect(connectionMonitor.getStatus()).toBe('online');
    });

    it('should update state to OFFLINE if health check fails', async () => {
      // Mock failed fetch
      global.fetch = jest.fn().mockRejectedValue(new Error('Network error'));

      // Start in ONLINE state
      // @ts-ignore - accessing private property for testing
      connectionMonitor.status = 'online';

      await (connectionMonitor as any).performHealthCheck();

      expect(connectionMonitor.getStatus()).toBe('offline');
    });

    it('should update state to OFFLINE if health check returns non-200 status', async () => {
      // Mock unsuccessful fetch
      global.fetch = jest.fn().mockResolvedValue({
        ok: false,
        status: 500
      });

      // Start in ONLINE state
      // @ts-ignore - accessing private property for testing
      connectionMonitor.status = 'online';

      await (connectionMonitor as any).performHealthCheck();

      expect(connectionMonitor.getStatus()).toBe('offline');
    });
  });
  
  describe('cleanup', () => {
    it('should remove all event listeners', () => {
      (connectionMonitor as any).cleanup();

      expect(window.removeEventListener).toHaveBeenCalledWith('online', expect.any(Function));
      expect(window.removeEventListener).toHaveBeenCalledWith('offline', expect.any(Function));
    });
  });
});
