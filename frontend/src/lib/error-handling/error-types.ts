/**
 * AG-UI Error Types and Classification
 * Provides standardized error types and handling for AG-UI integration
 */

// Standardized error types for AG-UI integration
export enum ErrorType {
  // Network related errors
  NETWORK_OFFLINE = 'network_offline',        // Browser is offline
  NETWORK_TIMEOUT = 'network_timeout',        // Request timed out
  NETWORK_DNS = 'network_dns',                // DNS resolution failed
  
  // Authentication related errors
  AUTH_EXPIRED = 'auth_expired',              // Token expired
  AUTH_INVALID = 'auth_invalid',              // Invalid credentials
  AUTH_MISSING = 'auth_missing',              // No authentication provided
  
  // Authorization related errors
  ACCESS_DENIED = 'access_denied',            // Permission denied
  TENANT_MISMATCH = 'tenant_mismatch',        // Cross-tenant access attempt
  QUOTA_EXCEEDED = 'quota_exceeded',          // User/tenant quota exceeded
  
  // Server related errors
  SERVER_ERROR = 'server_error',              // 5xx errors
  SERVER_OVERLOAD = 'server_overload',        // Server overloaded
  SERVICE_UNAVAILABLE = 'service_unavailable', // Service temporarily down
  
  // LLM related errors
  LLM_CONTEXT_LIMIT = 'llm_context_limit',    // Context window exceeded
  LLM_CONTENT_FILTER = 'llm_content_filter',  // Content filtered by LLM
  LLM_GUARDRAIL = 'llm_guardrail',            // Guardrail triggered
  
  // Unknown/misc errors
  UNKNOWN = 'unknown',                        // Unclassified error
  DATA_VALIDATION = 'data_validation',        // Invalid data format
  INVALID_REQUEST = 'invalid_request',        // Invalid request format/content
  TOOL_EXECUTION = 'tool_execution'           // Tool execution failed
}

// Error severity levels
export enum ErrorSeverity {
  FATAL = 'fatal',       // Requires page reload/restart
  ERROR = 'error',       // Blocks current operation
  WARNING = 'warning',   // Operation can continue but with limitations
  INFO = 'info'          // Informational only
}

// Retry policy for different error types
export interface RetryPolicy {
  maxAttempts: number;   // Maximum number of retry attempts
  baseDelay: number;     // Base delay in ms between attempts
  maxDelay: number;      // Maximum delay in ms between attempts
  backoffFactor: number; // Exponential backoff factor
}

// Standardized error interface
export interface AGError {
  type: ErrorType;
  message: string;
  severity: ErrorSeverity;
  retryable: boolean;
  originalError?: Error | unknown;
  statusCode?: number;
  context?: Record<string, any>;
  timestamp: number;
}

// Default retry policies by error type
export const DEFAULT_RETRY_POLICIES: Record<ErrorType, RetryPolicy | null> = {
  // Network errors - generally retryable
  [ErrorType.NETWORK_OFFLINE]: {
    maxAttempts: 5,
    baseDelay: 1000,
    maxDelay: 30000,
    backoffFactor: 1.5
  },
  [ErrorType.NETWORK_TIMEOUT]: {
    maxAttempts: 3,
    baseDelay: 2000,
    maxDelay: 10000,
    backoffFactor: 2
  },
  [ErrorType.NETWORK_DNS]: {
    maxAttempts: 2,
    baseDelay: 3000,
    maxDelay: 10000,
    backoffFactor: 2
  },
  
  // Auth errors - generally not retryable automatically
  [ErrorType.AUTH_EXPIRED]: null,  // Needs user intervention
  [ErrorType.AUTH_INVALID]: null,  // Needs user intervention
  [ErrorType.AUTH_MISSING]: null,  // Needs user intervention
  
  // Authorization errors - not retryable
  [ErrorType.ACCESS_DENIED]: null,
  [ErrorType.TENANT_MISMATCH]: null,
  [ErrorType.QUOTA_EXCEEDED]: null,
  
  // Server errors - retryable with caution
  [ErrorType.SERVER_ERROR]: {
    maxAttempts: 3,
    baseDelay: 2000,
    maxDelay: 10000,
    backoffFactor: 1.5
  },
  [ErrorType.SERVER_OVERLOAD]: {
    maxAttempts: 2,
    baseDelay: 5000,
    maxDelay: 15000,
    backoffFactor: 2
  },
  [ErrorType.SERVICE_UNAVAILABLE]: {
    maxAttempts: 3,
    baseDelay: 3000,
    maxDelay: 15000,
    backoffFactor: 2
  },
  
  // LLM errors - generally not retryable
  [ErrorType.LLM_CONTEXT_LIMIT]: null,
  [ErrorType.LLM_CONTENT_FILTER]: null,
  [ErrorType.LLM_GUARDRAIL]: null,
  
  // Misc errors
  [ErrorType.UNKNOWN]: {
    maxAttempts: 1,
    baseDelay: 1000,
    maxDelay: 2000,
    backoffFactor: 1
  },
  [ErrorType.DATA_VALIDATION]: null,
  [ErrorType.INVALID_REQUEST]: null,  // Invalid requests shouldn't be retried
  [ErrorType.TOOL_EXECUTION]: {
    maxAttempts: 2,
    baseDelay: 1000,
    maxDelay: 5000,
    backoffFactor: 1.5
  }
};

// Map HTTP status code to error type
export function mapStatusCodeToErrorType(status: number): ErrorType {
  if (status >= 500) return ErrorType.SERVER_ERROR;
  if (status === 429) return ErrorType.QUOTA_EXCEEDED;
  if (status === 408) return ErrorType.NETWORK_TIMEOUT;
  if (status === 404) return ErrorType.SERVER_ERROR;
  if (status === 403) return ErrorType.ACCESS_DENIED;
  if (status === 401) return ErrorType.AUTH_EXPIRED;
  if (status === 400) return ErrorType.DATA_VALIDATION;
  
  return ErrorType.UNKNOWN;
}

// Determine if error is related to network connectivity
export function isNetworkError(error: Error | unknown): boolean {
  if (!error) return false;
  
  // Check for fetch/network error types
  if (error instanceof TypeError && error.message.includes('fetch')) return true;
  if (error instanceof DOMException && error.name === 'AbortError') return true;
  
  // Check error messages for common network issues
  const errorStr = String(error).toLowerCase();
  return errorStr.includes('network') || 
         errorStr.includes('offline') || 
         errorStr.includes('timeout') ||
         errorStr.includes('connection');
}

// Check if error is authentication related
export function isAuthError(error: Error | unknown): boolean {
  if (!error) return false;
  
  const errorStr = String(error).toLowerCase();
  return errorStr.includes('auth') || 
         errorStr.includes('token') ||
         errorStr.includes('unauthenticated') ||
         errorStr.includes('expired');
}

// Get appropriate retry policy for an error
export function getRetryPolicy(errorType: ErrorType): RetryPolicy | null {
  return DEFAULT_RETRY_POLICIES[errorType];
}
