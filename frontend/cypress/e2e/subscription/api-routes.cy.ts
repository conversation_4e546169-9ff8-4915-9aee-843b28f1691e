/// <reference types="cypress" />
/// <reference path="../../support/global.d.ts" />

describe('API Routes', () => {
  beforeEach(() => {
    // Login as an admin user
    cy.login('<EMAIL>', 'password');
  });

  describe('Subscription API', () => {
    it('TC-8.1.1: Should create subscription via API', () => {
      // Get the auth token
      cy.window().then((win: any) => {
        const token = win.localStorage.getItem('supabase.auth.token');

        // Make API request to create subscription
        cy.request({
          method: 'POST',
          url: '/api/subscription',
          headers: {
            Authorization: `Bearer ${token}`,
          },
          body: {
            planId: 'plan_basic',
            billingCycle: 'monthly',
          },
        }).then((response: any) => {
          // Verify response
          expect(response.status).to.eq(200);
          expect(response.body.success).to.eq(true);
          expect(response.body.subscription).to.exist;
          expect(response.body.subscription.planId).to.eq('plan_basic');
          expect(response.body.subscription.billingCycle).to.eq('monthly');
          expect(response.body.subscription.status).to.eq('active');
        });
      });
    });

    it('TC-8.1.2: Should update subscription via API', () => {
      // Get the auth token
      cy.window().then((win: any) => {
        const token = win.localStorage.getItem('supabase.auth.token');

        // Make API request to update subscription
        cy.request({
          method: 'PATCH' as any,
          url: '/api/subscription',
          headers: {
            Authorization: `Bearer ${token}`,
          },
          body: {
            planId: 'plan_pro',
          },
        }).then((response: any) => {
          // Verify response
          expect(response.status).to.eq(200);
          expect(response.body.success).to.eq(true);
          expect(response.body.subscription).to.exist;
          expect(response.body.subscription.planId).to.eq('plan_pro');
          expect(response.body.subscription.status).to.eq('active');
        });
      });
    });

    it('TC-8.1.3: Should cancel subscription via API', () => {
      // Get the auth token
      cy.window().then((win: any) => {
        const token = win.localStorage.getItem('supabase.auth.token');

        // Make API request to cancel subscription
        cy.request({
          method: 'DELETE',
          url: '/api/subscription',
          headers: {
            Authorization: `Bearer ${token}`,
          },
        }).then((response: any) => {
          // Verify response
          expect(response.status).to.eq(200);
          expect(response.body.success).to.eq(true);
          expect(response.body.subscription).to.exist;
          expect(response.body.subscription.status).to.eq('canceled');
          expect(response.body.subscription.canceledAt).to.exist;
        });
      });
    });

    it('TC-8.1.4: Should get subscription details via API', () => {
      // Get the auth token
      cy.window().then((win: any) => {
        const token = win.localStorage.getItem('supabase.auth.token');

        // Make API request to get subscription details
        cy.request({
          method: 'GET',
          url: '/api/subscription',
          headers: {
            Authorization: `Bearer ${token}`,
          },
        }).then((response: any) => {
          // Verify response
          expect(response.status).to.eq(200);
          expect(response.body.subscription).to.exist;
          expect(response.body.subscription.tenantId).to.exist;
          expect(response.body.subscription.planId).to.exist;
          expect(response.body.subscription.status).to.exist;
          expect(response.body.subscription.billingCycle).to.exist;
        });
      });
    });
  });

  describe('Usage API', () => {
    it('TC-8.2.1: Should track resource usage via API', () => {
      // Get the auth token
      cy.window().then((win: any) => {
        const token = win.localStorage.getItem('supabase.auth.token');

        // Make API request to track resource usage
        cy.request({
          method: 'POST',
          url: '/api/subscription/usage',
          headers: {
            Authorization: `Bearer ${token}`,
          },
          body: {
            usageType: 'api_calls',
            incrementBy: 1,
          },
        }).then((response: any) => {
          // Verify response
          expect(response.status).to.eq(200);
          expect(response.body.success).to.eq(true);
          expect(response.body.usage).to.exist;
          expect(response.body.usage.usageType).to.eq('api_calls');
          expect(response.body.usage.usageCount).to.be.at.least(1);
        });
      });
    });

    it('TC-8.2.2: Should get resource usage via API', () => {
      // Get the auth token
      cy.window().then((win: any) => {
        const token = win.localStorage.getItem('supabase.auth.token');

        // Make API request to get resource usage
        cy.request({
          method: 'GET',
          url: '/api/subscription/usage?type=api_calls',
          headers: {
            Authorization: `Bearer ${token}`,
          },
        }).then((response: any) => {
          // Verify response
          expect(response.status).to.eq(200);
          expect(response.body.usage).to.exist;
          expect(response.body.usage).to.be.an('array');

          if (response.body.usage.length > 0) {
            expect(response.body.usage[0].usageType).to.eq('api_calls');
            expect(response.body.usage[0].usageCount).to.be.at.least(0);
          }
        });
      });
    });

    it('TC-8.2.3: Should check quota limit via API', () => {
      // Get the auth token
      cy.window().then((win: any) => {
        const token = win.localStorage.getItem('supabase.auth.token');

        // Make API request to check quota limit
        cy.request({
          method: 'POST',
          url: '/api/subscription/usage/check',
          headers: {
            Authorization: `Bearer ${token}`,
          },
          body: {
            usageType: 'api_calls',
            incrementBy: 1,
          },
        }).then((response: any) => {
          // Verify response
          expect(response.status).to.eq(200);
          expect(response.body.quota).to.exist;
          expect(response.body.quota.withinQuota).to.be.a('boolean');
          expect(response.body.quota.currentUsage).to.be.a('number');
          expect(response.body.quota.quotaLimit).to.be.a('number');
          expect(response.body.quota.percentUsed).to.be.a('number');
        });
      });
    });
  });

  describe('Webhook Handling', () => {
    // Note: These tests require admin access to simulate webhook events
    // For testing purposes, we'll use a special admin user

    beforeEach(() => {
      // Logout
      cy.logout();

      // Login as a super admin user
      cy.login('<EMAIL>', 'admin-password');
    });

    it('TC-8.3.1: Should handle subscription created webhook', () => {
      // Get the auth token
      cy.window().then((win: any) => {
        const token = win.localStorage.getItem('supabase.auth.token');

        // Make API request to simulate webhook
        cy.request({
          method: 'POST',
          url: '/api/webhooks/subscription/test',
          headers: {
            Authorization: `Bearer ${token}`,
          },
          body: {
            type: 'subscription.created',
            data: {
              id: 'sub_test123',
              customer: 'cus_test123',
              status: 'active',
            },
          },
        }).then((response: any) => {
          // Verify response
          expect(response.status).to.eq(200);
          expect(response.body.success).to.eq(true);
          expect(response.body.event).to.eq('subscription.created');
          expect(response.body.processed).to.eq(true);
        });
      });
    });

    it('TC-8.3.2: Should handle subscription updated webhook', () => {
      // Get the auth token
      cy.window().then((win: any) => {
        const token = win.localStorage.getItem('supabase.auth.token');

        // Make API request to simulate webhook
        cy.request({
          method: 'POST',
          url: '/api/webhooks/subscription/test',
          headers: {
            Authorization: `Bearer ${token}`,
          },
          body: {
            type: 'subscription.updated',
            data: {
              id: 'sub_test123',
              customer: 'cus_test123',
              status: 'active',
            },
          },
        }).then((response: any) => {
          // Verify response
          expect(response.status).to.eq(200);
          expect(response.body.success).to.eq(true);
          expect(response.body.event).to.eq('subscription.updated');
          expect(response.body.processed).to.eq(true);
        });
      });
    });

    it('TC-8.3.3: Should handle subscription deleted webhook', () => {
      // Get the auth token
      cy.window().then((win: any) => {
        const token = win.localStorage.getItem('supabase.auth.token');

        // Make API request to simulate webhook
        cy.request({
          method: 'POST',
          url: '/api/webhooks/subscription/test',
          headers: {
            Authorization: `Bearer ${token}`,
          },
          body: {
            type: 'subscription.deleted',
            data: {
              id: 'sub_test123',
              customer: 'cus_test123',
            },
          },
        }).then((response: any) => {
          // Verify response
          expect(response.status).to.eq(200);
          expect(response.body.success).to.eq(true);
          expect(response.body.event).to.eq('subscription.deleted');
          expect(response.body.processed).to.eq(true);
        });
      });
    });

    it('TC-8.3.4: Should handle payment succeeded webhook', () => {
      // Get the auth token
      cy.window().then((win: any) => {
        const token = win.localStorage.getItem('supabase.auth.token');

        // Make API request to simulate webhook
        cy.request({
          method: 'POST',
          url: '/api/webhooks/subscription/test',
          headers: {
            Authorization: `Bearer ${token}`,
          },
          body: {
            type: 'invoice.payment_succeeded',
            data: {
              subscription: 'sub_test123',
              customer: 'cus_test123',
              amount_paid: 1999,
            },
          },
        }).then((response: any) => {
          // Verify response
          expect(response.status).to.eq(200);
          expect(response.body.success).to.eq(true);
          expect(response.body.event).to.eq('invoice.payment_succeeded');
          expect(response.body.processed).to.eq(true);
        });
      });
    });

    it('TC-8.3.5: Should handle payment failed webhook', () => {
      // Get the auth token
      cy.window().then((win: any) => {
        const token = win.localStorage.getItem('supabase.auth.token');

        // Make API request to simulate webhook
        cy.request({
          method: 'POST',
          url: '/api/webhooks/subscription/test',
          headers: {
            Authorization: `Bearer ${token}`,
          },
          body: {
            type: 'invoice.payment_failed',
            data: {
              subscription: 'sub_test123',
              customer: 'cus_test123',
            },
          },
        }).then((response: any) => {
          // Verify response
          expect(response.status).to.eq(200);
          expect(response.body.success).to.eq(true);
          expect(response.body.event).to.eq('invoice.payment_failed');
          expect(response.body.processed).to.eq(true);
        });
      });
    });

    it('TC-8.3.6: Should handle trial will end webhook', () => {
      // Get the auth token
      cy.window().then((win: any) => {
        const token = win.localStorage.getItem('supabase.auth.token');

        // Make API request to simulate webhook
        cy.request({
          method: 'POST',
          url: '/api/webhooks/subscription/test',
          headers: {
            Authorization: `Bearer ${token}`,
          },
          body: {
            type: 'customer.subscription.trial_will_end',
            data: {
              id: 'sub_test123',
              customer: 'cus_test123',
              trial_end: Math.floor(Date.now() / 1000) + (3 * 24 * 60 * 60), // 3 days from now
            },
          },
        }).then((response: any) => {
          // Verify response
          expect(response.status).to.eq(200);
          expect(response.body.success).to.eq(true);
          expect(response.body.event).to.eq('customer.subscription.trial_will_end');
          expect(response.body.processed).to.eq(true);
        });
      });
    });
  });
});
