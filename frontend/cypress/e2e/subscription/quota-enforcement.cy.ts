/// <reference types="cypress" />
/// <reference path="../../support/global.d.ts" />

describe('Quota Enforcement', () => {
  beforeEach(() => {
    // Login as an admin user
    cy.login('<EMAIL>', 'password');
  });

  it('TC-5.1.1: Should check quota limit for a resource type', () => {
    // Visit the usage dashboard
    cy.visit('/settings/usage');

    // Select document upload resource type
    cy.get('[data-testid="resource-type-select"]').click();
    cy.contains('Document Uploads').click();

    // Verify that quota limit is displayed
    cy.get('[data-testid="quota-limit"]').should('be.visible');
    cy.get('[data-testid="quota-usage-percentage"]').should('be.visible');
  });

  it('TC-5.2.1: Should allow using a resource within quota limits', () => {
    // Visit the usage dashboard to check current usage
    cy.visit('/settings/usage');

    // Select document upload resource type
    cy.get('[data-testid="resource-type-select"]').click();
    cy.contains('Document Uploads').click();

    // Get current usage and quota limit
    cy.get('[data-testid="current-usage"]').invoke('text').then((currentUsage: any) => {
      cy.get('[data-testid="quota-limit"]').invoke('text').then((quotaLimit: any) => {
        // Only proceed if we're within quota limits
        if (parseInt(currentUsage) < parseInt(quotaLimit)) {
          // Visit the document upload page
          cy.visit('/documents/upload');

          // Upload a document
          cy.get('input[type="file"]').attachFile('test-document.pdf');
          cy.contains('button', 'Upload').click();

          // Verify upload success
          cy.contains('Document uploaded successfully').should('be.visible');
        } else {
          cy.log('Skipping test: Already at quota limit');
        }
      });
    });
  });

  it('TC-5.2.2: Should deny using a resource exceeding quota limits', () => {
    // This test requires a tenant with a low quota limit that can be easily exceeded
    // For testing purposes, we'll use a special test tenant with a quota limit of 1

    // Logout
    cy.logout();

    // Login as a user with a low quota limit
    cy.login('<EMAIL>', 'password');

    // Visit the usage dashboard to check current usage
    cy.visit('/settings/usage');

    // Select document upload resource type
    cy.get('[data-testid="resource-type-select"]').click();
    cy.contains('Document Uploads').click();

    // Get current usage and quota limit
    cy.get('[data-testid="current-usage"]').invoke('text').then((currentUsage: any) => {
      cy.get('[data-testid="quota-limit"]').invoke('text').then((quotaLimit: any) => {
        // Only proceed if we're at or near quota limits
        if (parseInt(currentUsage) >= parseInt(quotaLimit) - 1) {
          // Visit the document upload page
          cy.visit('/documents/upload');

          // Try to upload a document
          cy.get('input[type="file"]').attachFile('test-document.pdf');
          cy.contains('button', 'Upload').click();

          // Verify quota exceeded error
          cy.contains('Quota Exceeded').should('be.visible');
          cy.contains('You have reached your document upload quota').should('be.visible');
          cy.contains('button', 'Upgrade Plan').should('be.visible');
        } else {
          // If we're not near quota limits, we need to upload documents until we reach the limit
          cy.visit('/documents/upload');

          // Calculate how many uploads we need to reach the limit
          const uploadsNeeded = parseInt(quotaLimit as string) - parseInt(currentUsage as string);

          // Upload documents until we reach the limit
          for (let i = 0; i < uploadsNeeded; i++) {
            cy.get('input[type="file"]').attachFile('test-document.pdf');
            cy.contains('button', 'Upload').click();
            cy.contains('Document uploaded successfully').should('be.visible');
            cy.reload();
          }

          // Try one more upload to exceed the limit
          cy.get('input[type="file"]').attachFile('test-document.pdf');
          cy.contains('button', 'Upload').click();

          // Verify quota exceeded error
          cy.contains('Quota Exceeded').should('be.visible');
          cy.contains('You have reached your document upload quota').should('be.visible');
          cy.contains('button', 'Upgrade Plan').should('be.visible');
        }
      });
    });
  });

  it('TC-5.2.3: Should warn when approaching quota limits', () => {
    // This test requires a tenant with usage approaching quota limits
    // For testing purposes, we'll use a special test tenant with usage at 80% of quota

    // Logout
    cy.logout();

    // Login as a user with usage approaching quota limits
    cy.login('<EMAIL>', 'password');

    // Visit the usage dashboard
    cy.visit('/settings/usage');

    // Select document upload resource type
    cy.get('[data-testid="resource-type-select"]').click();
    cy.contains('Document Uploads').click();

    // Verify that quota warning is displayed
    cy.get('[data-testid="quota-warning"]').should('be.visible');
    cy.contains('You are approaching your quota limit').should('be.visible');

    // Visit the document upload page
    cy.visit('/documents/upload');

    // Verify that quota warning is displayed on the upload page
    cy.get('[data-testid="quota-warning-banner"]').should('be.visible');
    cy.contains('You are approaching your document upload quota').should('be.visible');
  });
});
