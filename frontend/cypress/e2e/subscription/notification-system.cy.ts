/// <reference types="cypress" />
/// <reference path="../../support/global.d.ts" />

describe('Notification System', () => {
  beforeEach(() => {
    // Login as an admin user
    cy.login('<EMAIL>', 'password');
  });

  it('TC-6.1.1: Should receive notification for subscription creation', () => {
    // Create a new subscription
    cy.visit('/settings/subscription');
    cy.contains('button', 'Change Plan').click();
    cy.get('[data-testid="plan-basic"]').click();
    cy.get('[data-testid="billing-cycle-monthly"]').click();
    cy.contains('button', 'Subscribe').click();

    // Verify subscription creation notification
    cy.get('[data-testid="notification-bell"]').click();
    cy.contains('Your subscription has been created successfully').should('be.visible');
  });

  it('TC-6.1.2: Should receive notification for subscription update', () => {
    // Update subscription
    cy.visit('/settings/subscription');
    cy.contains('button', 'Change Plan').click();
    cy.get('[data-testid="plan-pro"]').click();
    cy.contains('button', 'Update Subscription').click();

    // Verify subscription update notification
    cy.get('[data-testid="notification-bell"]').click();
    cy.contains('Your subscription has been updated').should('be.visible');
  });

  it('TC-6.1.3: Should receive notification for subscription cancellation', () => {
    // Cancel subscription
    cy.visit('/settings/subscription');
    cy.contains('button', 'Manage Subscription').click();
    cy.contains('button', 'Cancel Subscription').click();
    cy.contains('button', 'Confirm').click();

    // Verify subscription cancellation notification
    cy.get('[data-testid="notification-bell"]').click();
    cy.contains('Your subscription has been canceled').should('be.visible');
  });

  it('TC-6.2.1: Should receive notification for trial ending soon', () => {
    // This test requires a tenant with a trial that's ending soon
    // For testing purposes, we'll use a special test tenant with a trial ending in 3 days

    // Logout
    cy.logout();

    // Login as a user with a trial ending soon
    cy.login('<EMAIL>', 'password');

    // Visit the dashboard to trigger notifications
    cy.visit('/dashboard');

    // Verify trial ending notification
    cy.get('[data-testid="notification-bell"]').click();
    cy.contains('Your trial will end in 3 days').should('be.visible');
  });

  it('TC-6.3.1: Should receive notification for quota approaching limit', () => {
    // This test requires a tenant with usage approaching quota limits
    // For testing purposes, we'll use a special test tenant with usage at 80% of quota

    // Logout
    cy.logout();

    // Login as a user with usage approaching quota limits
    cy.login('<EMAIL>', 'password');

    // Visit the dashboard to trigger notifications
    cy.visit('/dashboard');

    // Verify quota approaching notification
    cy.get('[data-testid="notification-bell"]').click();
    cy.contains('You have used 80% of your document upload quota').should('be.visible');
  });

  it('TC-6.3.2: Should receive notification for quota exceeded', () => {
    // This test requires a tenant that has exceeded quota limits
    // For testing purposes, we'll use a special test tenant that has exceeded quota

    // Logout
    cy.logout();

    // Login as a user who has exceeded quota
    cy.login('<EMAIL>', 'password');

    // Visit the dashboard to trigger notifications
    cy.visit('/dashboard');

    // Verify quota exceeded notification
    cy.get('[data-testid="notification-bell"]').click();
    cy.contains('You have reached your document upload quota').should('be.visible');
  });

  it('TC-6.4.1: Should receive notification for payment success', () => {
    // This test requires a tenant with a recent successful payment
    // For testing purposes, we'll use a special test tenant with a recent payment

    // Logout
    cy.logout();

    // Login as a user with a recent payment
    cy.login('<EMAIL>', 'password');

    // Visit the dashboard to trigger notifications
    cy.visit('/dashboard');

    // Verify payment success notification
    cy.get('[data-testid="notification-bell"]').click();
    cy.contains('Your subscription payment was successful').should('be.visible');
  });

  it('TC-6.4.2: Should receive notification for payment failure', () => {
    // This test requires a tenant with a recent failed payment
    // For testing purposes, we'll use a special test tenant with a failed payment

    // Logout
    cy.logout();

    // Login as a user with a failed payment
    cy.login('<EMAIL>', 'password');

    // Visit the dashboard to trigger notifications
    cy.visit('/dashboard');

    // Verify payment failure notification
    cy.get('[data-testid="notification-bell"]').click();
    cy.contains('Your subscription payment has failed').should('be.visible');
  });
});
