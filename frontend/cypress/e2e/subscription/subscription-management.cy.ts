/// <reference types="cypress" />
/// <reference path="../../support/global.d.ts" />
/**
 * E2E tests for subscription management flows
 *
 * These tests verify the critical user flows related to subscription management:
 * - Viewing subscription details
 * - Changing subscription plans
 * - Managing add-ons
 */
describe('Subscription Management Flow', () => {
  beforeEach(() => {
    // Login before each test
    cy.login(Cypress.env('TEST_ACTIVE_USER_EMAIL'), "password");
  });

  it('displays the current subscription details correctly', () => {
    // Visit the subscription settings page
    cy.visit('/settings/subscription');

    // Check that the subscription status component is displayed
    cy.get('[data-testid="subscription-status"]').should('exist');

    // Check for subscription details
    cy.contains(/subscription status/i).should('exist');
    cy.contains(/current plan/i).should('exist');

    // Check for billing information
    cy.contains(/billing cycle/i).should('exist');
    cy.contains(/next billing date/i).should('exist');
  });

  it('displays usage metrics correctly', () => {
    // Visit the subscription settings page
    cy.visit('/settings/subscription');

    // Check that the usage dashboard component is displayed
    cy.get('[data-testid="usage-dashboard"]').should('exist');

    // Check for usage metrics
    cy.contains(/usage dashboard/i).should('exist');
    cy.contains(/document upload/i).should('exist');

    // Check for quota information
    cy.contains(/quota limit/i).should('exist');
    cy.contains(/current usage/i).should('exist');
  });

  it('allows navigation to subscription plans', () => {
    // Visit the subscription settings page
    cy.visit('/settings/subscription');

    // Click on the "View Plans" button
    cy.contains(/view plans/i).click();

    // Check that we're on the plans page
    cy.url().should('include', '/settings/subscription/plans');

    // Check for plan options
    cy.contains(/available plans/i).should('exist');
    cy.get('[data-testid="subscription-plan-card"]').should('have.length.at.least', 1);
  });

  it('allows navigation to billing information', () => {
    // Visit the subscription settings page
    cy.visit('/settings/subscription');

    // Click on the "Manage Billing" button
    cy.contains(/manage billing/i).click();

    // Check that we're on the billing page
    cy.url().should('include', '/settings/subscription/billing');

    // Check for billing information
    cy.contains(/billing information/i).should('exist');
    cy.contains(/payment method/i).should('exist');
    cy.contains(/billing history/i).should('exist');
  });
});

/**
 * E2E tests for usage monitoring flows
 *
 * These tests verify the critical user flows related to usage monitoring:
 * - Viewing usage metrics
 * - Receiving quota warnings
 * - Handling quota exceeded scenarios
 */
describe('Usage Monitoring Flow', () => {
  beforeEach(() => {
    // Login before each test
    cy.login(Cypress.env('TEST_ACTIVE_USER_EMAIL'), "password");
  });

  it('displays usage metrics for different resource types', () => {
    // Visit the subscription settings page
    cy.visit('/settings/subscription');

    // Check that the usage dashboard component is displayed
    cy.get('[data-testid="usage-dashboard"]').should('exist');

    // Check for document upload usage
    cy.contains(/document upload/i).click();
    cy.contains(/document upload usage/i).should('exist');

    // Check for API calls usage
    cy.contains(/api calls/i).click();
    cy.contains(/api calls usage/i).should('exist');
  });

  it('displays usage history correctly', () => {
    // Visit the subscription settings page
    cy.visit('/settings/subscription/usage');

    // Check that the usage history component is displayed
    cy.get('[data-testid="usage-history"]').should('exist');

    // Check for usage history table
    cy.contains(/usage history/i).should('exist');
    cy.contains(/date/i).should('exist');
    cy.contains(/resource type/i).should('exist');
    cy.contains(/amount/i).should('exist');
  });

  it('allows filtering usage by resource type', () => {
    // Visit the subscription usage page
    cy.visit('/settings/subscription/usage');

    // Check that the filter component is displayed
    cy.get('[data-testid="usage-filter"]').should('exist');

    // Select document upload filter
    cy.get('[data-testid="usage-filter"]').click();
    cy.contains(/document upload/i).click();

    // Check that the filtered results are displayed
    cy.get('[data-testid="usage-history-table"]').should('contain', 'Document Upload');
  });
});

/**
 * E2E tests for quota enforcement flows
 *
 * These tests verify the critical user flows related to quota enforcement:
 * - Receiving warnings when approaching quota limits
 * - Handling quota exceeded scenarios
 * - Upgrading plans to increase quota
 */
describe('Quota Enforcement Flow', () => {
  beforeEach(() => {
    // Login before each test
    cy.login(Cypress.env('TEST_ACTIVE_USER_EMAIL'), "password");

    // Mock the quota data to simulate different scenarios
    cy.intercept('GET', '/api/subscription/quota*', (req) => {
      // Get the query parameter for the test scenario
      const scenario = req.query.scenario || 'normal';

      let responseBody = {
        withinQuota: true,
        currentUsage: 50,
        quotaLimit: 100,
        percentUsed: 50
      };

      if (scenario === 'approaching') {
        responseBody = {
          withinQuota: true,
          currentUsage: 80,
          quotaLimit: 100,
          percentUsed: 80
        };
      } else if (scenario === 'exceeded') {
        responseBody = {
          withinQuota: false,
          currentUsage: 110,
          quotaLimit: 100,
          percentUsed: 110
        };
      }

      req.reply({
        statusCode: 200,
        body: responseBody
      });
    });
  });

  it('displays normal usage indicators when within quota', () => {
    // Visit the subscription settings page with normal usage scenario
    cy.visit('/settings/subscription?scenario=normal');

    // Check that the usage dashboard shows normal indicators
    cy.get('[data-testid="usage-dashboard"]').should('exist');
    cy.contains(/50% of quota used/i).should('exist');

    // Check that there are no warning indicators
    cy.contains(/approaching quota limit/i).should('not.exist');
    cy.contains(/quota exceeded/i).should('not.exist');
  });

  it('displays warning indicators when approaching quota limit', () => {
    // Visit the subscription settings page with approaching quota scenario
    cy.visit('/settings/subscription?scenario=approaching');

    // Check that the usage dashboard shows warning indicators
    cy.get('[data-testid="usage-dashboard"]').should('exist');
    cy.contains(/approaching quota limit/i).should('exist');
    cy.contains(/80% used/i).should('exist');

    // Check for the upgrade suggestion
    cy.contains(/consider upgrading/i).should('exist');
  });

  it('displays error indicators when quota is exceeded', () => {
    // Visit the subscription settings page with exceeded quota scenario
    cy.visit('/settings/subscription?scenario=exceeded');

    // Check that the usage dashboard shows error indicators
    cy.get('[data-testid="usage-dashboard"]').should('exist');
    cy.contains(/quota exceeded/i).should('exist');
    cy.contains(/110% used/i).should('exist');

    // Check for the upgrade button
    cy.contains(/upgrade now/i).should('exist');
  });

  it('allows upgrading plan when quota is exceeded', () => {
    // Visit the subscription settings page with exceeded quota scenario
    cy.visit('/settings/subscription?scenario=exceeded');

    // Click on the upgrade button
    cy.contains(/upgrade now/i).click();

    // Check that we're on the plans page
    cy.url().should('include', '/settings/subscription/plans');

    // Check for plan options
    cy.contains(/available plans/i).should('exist');
    cy.get('[data-testid="subscription-plan-card"]').should('have.length.at.least', 1);
  });
});
