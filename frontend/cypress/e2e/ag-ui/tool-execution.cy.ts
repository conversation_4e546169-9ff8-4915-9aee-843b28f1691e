/**
 * AG-UI Tool Execution Tests
 * 
 * Tests the tool execution functionality in AG-UI protocol.
 * Verifies proper tool calling, execution, and response handling.
 */

/// <reference types="cypress" />

/// <reference path="../../support/global.d.ts" />

describe('AG-UI Tool Execution', () => {
  beforeEach(() => {
    // Login before each test
    cy.login();
    
    // Enable AG-UI for testing
    cy.window().then((win: any) => {
      win.localStorage.setItem('NEXT_PUBLIC_AGUI_ENABLED', 'true');
    });

    // Intercept tool call API request
    cy.intercept('POST', '/api/copilotkit', (req: any) => {
      // Check if the request contains a tool call
      const reqBody = req.body;
      if (reqBody && reqBody.messages && reqBody.messages.some((m: any) => m.content.includes('search case'))) {
        // Return a tool call response
        req.reply({
          id: 'mock-tool-call-id',
          object: 'chat.completion',
          created: Date.now(),
          model: 'gpt-4',
          choices: [
            {
              index: 0,
              message: {
                role: 'assistant',
                content: null,
                tool_calls: [
                  {
                    id: 'tool_call_1',
                    type: 'function',
                    function: {
                      name: 'searchCases',
                      arguments: JSON.stringify({
                        query: 'car accident',
                        limit: 5
                      })
                    }
                  }
                ]
              },
              finish_reason: 'tool_calls',
            },
          ],
          usage: {
            prompt_tokens: 50,
            completion_tokens: 30,
            total_tokens: 80,
          },
        });
      } else if (reqBody && reqBody.messages && reqBody.messages.some((m: any) => m.role === 'tool' && m.tool_call_id === 'tool_call_1')) {
        // Return a response to the tool call result
        req.reply({
          id: 'mock-tool-response-id',
          object: 'chat.completion',
          created: Date.now(),
          model: 'gpt-4',
          choices: [
            {
              index: 0,
              message: {
                role: 'assistant',
                content: 'I found 5 relevant cases related to car accidents:\n\n1. Smith v. Johnson (2022) - Rear-end collision case\n2. Garcia v. City Transit (2021) - Bus accident settlement\n3. Wong v. Insurance Co. (2020) - Disputed liability claim\n4. Parker v. Transport Inc. (2022) - Commercial vehicle accident\n5. Lee v. Rideshare (2021) - Rideshare accident case',
              },
              finish_reason: 'stop',
            },
          ],
          usage: {
            prompt_tokens: 150,
            completion_tokens: 120,
            total_tokens: 270,
          },
        });
      } else {
        // Normal response for other messages
        req.reply({
          id: 'mock-chat-id',
          object: 'chat.completion',
          created: Date.now(),
          model: 'gpt-4',
          choices: [
            {
              index: 0,
              message: {
                role: 'assistant',
                content: 'I can help you with that. What specific information are you looking for?',
              },
              finish_reason: 'stop',
            },
          ],
          usage: {
            prompt_tokens: 50,
            completion_tokens: 30,
            total_tokens: 80,
          },
        });
      }
    }).as('chatAPI');
    
    // Mock the search cases API endpoint
    cy.intercept('GET', '/api/cases/search*', {
      statusCode: 200,
      body: {
        results: [
          { id: 'case001', title: 'Smith v. Johnson', date: '2022-05-15', type: 'Personal Injury' },
          { id: 'case002', title: 'Garcia v. City Transit', date: '2021-09-22', type: 'Personal Injury' },
          { id: 'case003', title: 'Wong v. Insurance Co.', date: '2020-11-30', type: 'Insurance Dispute' },
          { id: 'case004', title: 'Parker v. Transport Inc.', date: '2022-03-17', type: 'Commercial' },
          { id: 'case005', title: 'Lee v. Rideshare', date: '2021-07-08', type: 'Rideshare Accident' }
        ],
        total: 5
      }
    }).as('searchCasesAPI');
  });

  it('should execute a tool call and display results', () => {
    // Visit the page with a research chat component
    cy.visit('/research');
    
    // Wait for the chat component to load
    cy.get('[data-testid="chat-container"]').should('be.visible');
    
    // Type a message that will trigger a tool call
    cy.get('[data-testid="chat-input"]').type('Can you search case law for car accident precedents?{enter}');
    
    // Wait for the API call to happen
    cy.wait('@chatAPI');
    
    // Verify tool execution indicator appears
    cy.get('[data-testid="tool-execution-indicator"]', { timeout: 10000 })
      .should('be.visible')
      .should('contain', 'Searching cases');
    
    // Verify tool result appears
    cy.get('[data-testid="assistant-message"]', { timeout: 10000 })
      .last()
      .should('contain', 'I found 5 relevant cases');
    
    // Verify the cases are displayed correctly
    cy.get('[data-testid="assistant-message"]')
      .last()
      .should('contain', 'Smith v. Johnson')
      .should('contain', 'Garcia v. City Transit')
      .should('contain', 'Wong v. Insurance Co.')
      .should('contain', 'Parker v. Transport Inc.')
      .should('contain', 'Lee v. Rideshare');
  });

  it('should handle failed tool execution gracefully', () => {
    // Override the search cases API to return an error
    cy.intercept('GET', '/api/cases/search*', {
      statusCode: 500,
      body: { 
        error: 'Internal Server Error',
        message: 'Failed to search cases'
      }
    }).as('searchCasesErrorAPI');
    
    // Visit the page with a research chat component
    cy.visit('/research');
    
    // Wait for the chat component to load
    cy.get('[data-testid="chat-container"]').should('be.visible');
    
    // Type a message that will trigger a tool call
    cy.get('[data-testid="chat-input"]').type('Can you search case law for car accident precedents?{enter}');
    
    // Wait for the API call to happen
    cy.wait('@chatAPI');
    
    // Verify error handling indicator appears
    cy.get('[data-testid="tool-error-indicator"]', { timeout: 10000 })
      .should('be.visible')
      .should('contain', 'Error');
    
    // Check for a retry button if implemented
    cy.get('[data-testid="retry-tool-button"]')
      .should('be.visible');
  });

  it('should chain multiple tool calls correctly', () => {
    // Set up a more complex intercept for multiple tool calls
    cy.intercept('POST', '/api/copilotkit', (req: any) => {
      const reqBody = req.body;
      
      // First tool call - search cases
      if (reqBody && reqBody.messages && reqBody.messages.some((m: any) => m.content && m.content.includes('analyze accident case'))) {
        req.reply({
          id: 'tool-call-1',
          choices: [{
            message: {
              role: 'assistant',
              content: null,
              tool_calls: [{
                id: 'tool_call_search',
                type: 'function',
                function: {
                  name: 'searchCases',
                  arguments: JSON.stringify({ query: 'car accident', limit: 1 })
                }
              }]
            },
            finish_reason: 'tool_calls'
          }]
        });
      } 
      // Response to first tool call result
      else if (reqBody && reqBody.messages && reqBody.messages.some((m: any) => m.role === 'tool' && m.tool_call_id === 'tool_call_search')) {
        req.reply({
          id: 'tool-call-2',
          choices: [{
            message: {
              role: 'assistant',
              content: null,
              tool_calls: [{
                id: 'tool_call_details',
                type: 'function',
                function: {
                  name: 'getCaseDetails',
                  arguments: JSON.stringify({ caseId: 'case001' })
                }
              }]
            },
            finish_reason: 'tool_calls'
          }]
        });
      }
      // Response to second tool call result
      else if (reqBody && reqBody.messages && reqBody.messages.some((m: any) => m.role === 'tool' && m.tool_call_id === 'tool_call_details')) {
        req.reply({
          id: 'final-response',
          choices: [{
            message: {
              role: 'assistant',
              content: 'Based on my analysis of Smith v. Johnson (2022), this case established that in rear-end collisions, the following driver is presumed to be at fault unless they can prove otherwise. The settlement amount was $85,000 for medical expenses and pain and suffering.',
            },
            finish_reason: 'stop'
          }]
        });
      }
    }).as('chainedToolCallsAPI');
    
    // Mock the case details API
    cy.intercept('GET', '/api/cases/details*', {
      statusCode: 200,
      body: {
        id: 'case001',
        title: 'Smith v. Johnson',
        date: '2022-05-15',
        type: 'Personal Injury',
        description: 'Rear-end collision resulting in whiplash and back injuries',
        settlement: '$85,000',
        precedentValue: 'High',
        notes: 'Established fault presumption for following driver in rear-end collisions'
      }
    }).as('caseDetailsAPI');
    
    // Visit the page with a research chat component
    cy.visit('/research');
    
    // Wait for the chat component to load
    cy.get('[data-testid="chat-container"]').should('be.visible');
    
    // Type a message that will trigger multiple tool calls
    cy.get('[data-testid="chat-input"]').type('Can you analyze accident case precedents and tell me about settlement amounts?{enter}');
    
    // Wait for all API calls to complete
    cy.wait('@chainedToolCallsAPI');
    
    // Verify the first tool execution indicator
    cy.get('[data-testid="tool-execution-indicator"]')
      .should('be.visible')
      .should('contain', 'Searching');
    
    // Verify the second tool execution indicator
    cy.get('[data-testid="tool-execution-indicator"]')
      .should('be.visible')
      .should('contain', 'Getting case details');
    
    // Verify the final response that combines results from both tools
    cy.get('[data-testid="assistant-message"]')
      .last()
      .should('contain', 'Smith v. Johnson')
      .should('contain', '$85,000')
      .should('contain', 'rear-end collisions');
  });

  it('should authenticate and authorize tool execution', () => {
    // Set up an intercept that checks for proper auth headers
    cy.intercept('POST', '/api/copilotkit', (req: any) => {
      // Check for auth headers
      if (!req.headers.authorization) {
        req.reply({
          statusCode: 401,
          body: {
            error: 'Unauthorized',
            message: 'Authentication required'
          }
        });
      } else {
        // Return a successful tool call for authenticated requests
        req.reply({
          id: 'auth-tool-call',
          choices: [{
            message: {
              role: 'assistant',
              content: null,
              tool_calls: [{
                id: 'tool_call_protected',
                type: 'function',
                function: {
                  name: 'getSensitiveCaseInfo',
                  arguments: JSON.stringify({ caseId: 'case001' })
                }
              }]
            },
            finish_reason: 'tool_calls'
          }]
        });
      }
    }).as('authToolCallAPI');
    
    // Mock the sensitive case info API with auth check
    cy.intercept('GET', '/api/cases/sensitive*', (req: any) => {
      if (!req.headers.authorization) {
        req.reply({
          statusCode: 403,
          body: {
            error: 'Forbidden',
            message: 'Not authorized to access sensitive case information'
          }
        });
      } else {
        req.reply({
          statusCode: 200,
          body: {
            id: 'case001',
            clientInfo: 'Jane Smith, 35',
            medicalRecords: 'Confidential medical information',
            financialDetails: 'Settlement details and financial records'
          }
        });
      }
    }).as('sensitiveCaseAPI');
    
    // Test unauthorized access
    cy.visit('/research');
    cy.window().then((win: any) => {
      // Clear any auth tokens
      win.localStorage.removeItem('auth_token');
    });
    
    // Type a message that would trigger a sensitive tool call
    cy.get('[data-testid="chat-input"]').type('Show me the confidential details of the Smith case{enter}');
    
    // Verify the auth error is handled properly
    cy.get('[data-testid="auth-error-message"]')
      .should('be.visible')
      .should('contain', 'Authentication required');
    
    // Now test with proper auth
    cy.login();
    cy.get('[data-testid="chat-input"]').type('Show me the confidential details of the Smith case{enter}');
    
    // Wait for the tool call to complete successfully
    cy.wait('@authToolCallAPI');
    
    // Verify success message
    cy.get('[data-testid="assistant-message"]')
      .last()
      .should('contain', 'confidential');
  });
});
