/// <reference types="cypress" />
/// <reference path="../support/global.d.ts" />
describe('Authentication Check', () => {
  it('Should navigate to the login page', () => {
    // Visit the login page
    cy.visit('/login');

    // Check if the page loads without errors
    cy.get('body').should('exist');

    // Verify we're on the login page
    cy.url().should('include', '/login');

    // Check for login form elements
    cy.get('input[type="email"]').should('exist');
    cy.get('input[type="password"]').should('exist');
    cy.get('button[type="submit"]').should('exist');
  });
});
