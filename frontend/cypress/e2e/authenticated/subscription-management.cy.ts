/// <reference types="cypress" />
/// <reference path="../../support/global.d.ts" />

describe('Authenticated Subscription Management', () => {
  beforeEach(() => {
    // Set up Cypress to handle redirects and wait for authentication
    cy.intercept('GET', '/settings/subscription').as('subscriptionPage');
    cy.intercept('GET', '/settings/usage').as('usagePage');

    // Login with active subscription user
    cy.login(Cypress.env('TEST_ACTIVE_USER_EMAIL'), "password");

    // Wait for authentication to complete
    cy.wait(1000); // Give time for auth to process
  });

  it('Should login and navigate to the subscription page', () => {
    // Visit the subscription management page directly
    cy.visit('/settings/subscription', {
      failOnStatusCode: false,
      timeout: 10000
    });

    // Force the test to wait for the page to load
    cy.wait(2000);

    // Check if the page loads without errors
    cy.get('body', { timeout: 10000 }).should('exist');

    // Look for subscription content
    cy.get('body').contains(/subscription|plan|billing/i, { timeout: 10000 }).should('exist');

    // Check for subscription information elements
    cy.get('h1, h2, h3', { timeout: 10000 }).should('exist');

    // Look for common subscription elements
    cy.get('body').then(($body: any) => {
      const bodyText = $body.text().toLowerCase();
      const hasSubscriptionText =
        bodyText.includes('subscription') ||
        bodyText.includes('plan') ||
        bodyText.includes('billing') ||
        bodyText.includes('payment');

      expect(hasSubscriptionText).to.be.true;
    });
  });

  it('Should login and navigate to the usage dashboard', () => {
    // Visit the usage dashboard page directly
    cy.visit('/settings/usage', {
      failOnStatusCode: false,
      timeout: 10000
    });

    // Force the test to wait for the page to load
    cy.wait(2000);

    // Check if the page loads without errors
    cy.get('body', { timeout: 10000 }).should('exist');

    // Look for usage dashboard content
    cy.get('body').contains(/usage|resource|quota|dashboard/i, { timeout: 10000 }).should('exist');

    // Check for usage dashboard elements
    cy.get('h1, h2, h3', { timeout: 10000 }).should('exist');

    // Look for common usage dashboard elements
    cy.get('body').then(($body: any) => {
      const bodyText = $body.text().toLowerCase();
      const hasUsageText =
        bodyText.includes('usage') ||
        bodyText.includes('resource') ||
        bodyText.includes('quota') ||
        bodyText.includes('dashboard');

      expect(hasUsageText).to.be.true;
    });
  });
});
