/// <reference types="cypress" />
/// <reference path="../../support/global.d.ts" />

describe('Subscription States', () => {
  context('Active Subscription', () => {
    beforeEach(() => {
      // Login as the main test user with an active subscription
      cy.login(Cypress.env('TEST_ACTIVE_USER_EMAIL'), "password");

      // Wait for authentication to complete
      cy.wait(1000);
    });

    it('Should display active subscription details', () => {
      // Visit the subscription management page
      cy.visit('/settings/subscription', {
        failOnStatusCode: false,
        timeout: 10000
      });

      // Force the test to wait for the page to load
      cy.wait(2000);

      // Look for active subscription content
      cy.get('body', { timeout: 10000 }).contains(/active|current|pro|subscription/i).should('exist');

      // Check for active subscription indicators
      cy.get('body').then(($body: any) => {
        const bodyText = $body.text().toLowerCase();
        const hasActiveIndicators =
          bodyText.includes('active') ||
          bodyText.includes('current') ||
          bodyText.includes('pro') ||
          bodyText.includes('subscription');

        expect(hasActiveIndicators).to.be.true;
      });
    });

    it('Should allow access to subscription features', () => {
      // Visit a feature that requires an active subscription
      cy.visit('/settings/usage', {
        failOnStatusCode: false,
        timeout: 10000
      });

      // Force the test to wait for the page to load
      cy.wait(2000);

      // Verify we can access the feature
      cy.url().should('include', '/settings/usage');
      cy.get('body', { timeout: 10000 }).should('exist');

      // Look for usage dashboard content
      cy.get('body').contains(/usage|quota|dashboard/i, { timeout: 10000 }).should('exist');
    });
  });

  context('Trial Subscription', () => {
    beforeEach(() => {
      // Login as a user with a trial subscription
      cy.login(Cypress.env('TEST_TRIAL_USER_EMAIL'), "password");

      // Wait for authentication to complete
      cy.wait(1000);
    });

    it('Should display trial subscription details', () => {
      // Visit the subscription management page
      cy.visit('/settings/subscription', {
        failOnStatusCode: false,
        timeout: 10000
      });

      // Force the test to wait for the page to load
      cy.wait(2000);

      // Look for trial subscription content
      cy.get('body', { timeout: 10000 }).contains(/trial|days|remaining|subscription/i).should('exist');

      // Check for trial subscription indicators
      cy.get('body').then(($body: any) => {
        const bodyText = $body.text().toLowerCase();
        const hasTrialIndicators =
          bodyText.includes('trial') ||
          bodyText.includes('days') ||
          bodyText.includes('remaining') ||
          bodyText.includes('subscription');

        expect(hasTrialIndicators).to.be.true;
      });
    });
  });

  context('Canceled Subscription', () => {
    beforeEach(() => {
      // Login as a user with a canceled subscription
      cy.login(Cypress.env('TEST_CANCELED_USER_EMAIL'), "password");

      // Wait for authentication to complete
      cy.wait(1000);
    });

    it('Should display canceled subscription details', () => {
      // Visit the subscription management page
      cy.visit('/settings/subscription', {
        failOnStatusCode: false,
        timeout: 10000
      });

      // Force the test to wait for the page to load
      cy.wait(2000);

      // Look for canceled subscription content
      cy.get('body', { timeout: 10000 }).contains(/canceled|cancelled|ended|subscription/i).should('exist');

      // Check for canceled subscription indicators
      cy.get('body').then(($body: any) => {
        const bodyText = $body.text().toLowerCase();
        const hasCanceledIndicators =
          bodyText.includes('canceled') ||
          bodyText.includes('cancelled') ||
          bodyText.includes('expired') ||
          bodyText.includes('ended') ||
          bodyText.includes('subscription');

        expect(hasCanceledIndicators).to.be.true;
      });

      // Check for the renew subscription button
      cy.contains(/renew subscription/i, { timeout: 10000 }).should('exist');
    });
  });

  context('No Subscription', () => {
    beforeEach(() => {
      // Login as a user with no subscription
      cy.login(Cypress.env('TEST_NONE_USER_EMAIL'), "password");

      // Wait for authentication to complete
      cy.wait(1000);
    });

    it('Should display no subscription state', () => {
      // Visit the subscription management page
      cy.visit('/settings/subscription', {
        failOnStatusCode: false,
        timeout: 10000
      });

      // Force the test to wait for the page to load
      cy.wait(2000);

      // Look for no subscription content
      cy.get('body', { timeout: 10000 }).contains(/no subscription|subscribe|start|plan/i).should('exist');

      // Check for no subscription indicators
      cy.get('body').then(($body: any) => {
        const bodyText = $body.text().toLowerCase();
        const hasNoSubscriptionIndicators =
          bodyText.includes('no subscription') ||
          bodyText.includes('subscribe') ||
          bodyText.includes('start') ||
          bodyText.includes('plan');

        expect(hasNoSubscriptionIndicators).to.be.true;
      });
    });
  });
});
