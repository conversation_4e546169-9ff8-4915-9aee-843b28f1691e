/// <reference types="cypress" />

// Import Cypress types to ensure they're available
import 'cypress';

// Extend the existing Cypress namespace in global scope for proper merging
declare global {
  namespace Cypress {
    interface VisitOptions {
      failOnStatusCode?: boolean;
      [key: string]: any;
    }

    interface Chainable<Subject = any> {
      // Custom commands
      login(email?: string, password?: string): Chainable<void>;
      logout(): Chainable<void>;
      attachFile(filePath: string): Chainable<void>;
      navigateAuthenticated(url: string): Chainable<Element>;
      checkAuthentication(): Chainable<Element>;
      shadcnSelect(selector: string, optionText: string): Chainable<Element>;
      getByTestId(id: string): Chainable<Element>;
      getBySel(selector: string): Chainable<Element>;
      getBySelLike(selector: string): Chainable<Element>;

      // Built-in Cypress commands that might be missing
      session(id: string, setup: () => void, options?: any): Chainable<void>;
      intercept(method: string, url: string, response?: any): Chainable<any>;
      intercept(url: string, response?: any): Chainable<any>;
      intercept(options: any): Chainable<any>;
      intercept(url: string, handler: (req: any) => void): Chainable<any>;

      // Window command with proper typing
      window(): Chainable<Window & typeof globalThis>;
    }

    interface Commands {
      add(name: string, fn: (...args: any[]) => any, options?: any): void;
    }
  }

  // Declare the global Cypress object
  const Cypress: {
    Commands: {
      add(name: string, fn: (...args: any[]) => any, options?: any): void;
    };
    env(key?: string): any;
    Blob: any;
  };

  // Extend Window interface for localStorage typing
  interface Window {
    localStorage: Storage;
  }

  // Add jQuery types for Cypress
  interface JQuery {
    text(): string;
  }
}

export {};
