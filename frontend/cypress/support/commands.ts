// ***********************************************
// This example commands.ts shows you how to
// create various custom commands and overwrite
// existing commands.
//
// For more comprehensive examples of custom
// commands please read more here:
// https://on.cypress.io/custom-commands
// ***********************************************

// Import Cypress types
/// <reference types="cypress" />
/// <reference path="./global.d.ts" />

// Extend Cypress namespace with custom commands
declare global {
  namespace Cypress {
    interface Chainable {
      login(email?: string, password?: string): Chainable<void>;
      logout(): Chainable<void>;
      attachFile(filePath: string): Chainable<void>;
    }
  }
}

// Login command using mock authentication
Cypress.Commands.add('login', (email?: string, password?: string) => {
  // Use environment variables if email and password are not provided
  const userEmail = email || Cypress.env('TEST_USER_EMAIL');

  // Log the credentials being used
  cy.log(`Mock login as ${userEmail}`);

  // Determine subscription status based on email
  let subscriptionStatus = 'active';
  let quotaPercentUsed = 80;

  if (userEmail === Cypress.env('TEST_TRIAL_USER_EMAIL')) {
    subscriptionStatus = 'trialing';
  } else if (userEmail === Cypress.env('TEST_CANCELED_USER_EMAIL')) {
    subscriptionStatus = 'canceled';
  } else if (userEmail === Cypress.env('TEST_NONE_USER_EMAIL')) {
    subscriptionStatus = 'inactive';
  }

  // For quota testing
  if (userEmail === Cypress.env('TEST_USER_EMAIL')) {
    // Main test user has approaching quota
    quotaPercentUsed = 80;
  } else if (subscriptionStatus === 'active') {
    // Active subscription user has exceeded quota
    quotaPercentUsed = 110;
  }

  // Create a mock session
  cy.session(`${userEmail}-${subscriptionStatus}`, () => {
    // Create a mock user and session
    const mockUser = {
      id: 'test-user-id',
      email: userEmail,
      app_metadata: {
        tenant_id: 'f5a731c2-9c0b-4ef8-bb6d-6bb9bd380a11',
        role: 'partner'
      },
      user_metadata: {
        first_name: 'Test',
        last_name: 'User'
      },
      aud: 'authenticated',
      created_at: new Date().toISOString()
    };

    const mockSession = {
      access_token: 'mock-access-token',
      refresh_token: 'mock-refresh-token',
      expires_at: Math.floor(Date.now() / 1000) + 3600,
      user: mockUser
    };

    // Set the mock session in localStorage
    cy.window().then((win: any) => {
      // Store the auth data in localStorage
      win.localStorage.setItem('supabase.auth.token', JSON.stringify({
        access_token: mockSession.access_token,
        refresh_token: mockSession.refresh_token,
        expires_at: mockSession.expires_at
      }));

      // Also store the user data
      win.localStorage.setItem('supabase.auth.data', JSON.stringify({
        user: mockUser,
        session: {
          access_token: mockSession.access_token,
          refresh_token: mockSession.refresh_token,
          expires_at: mockSession.expires_at
        }
      }));

      // Add a flag to indicate this is a test session
      win.localStorage.setItem('cypress-test-auth', 'true');
      win.localStorage.setItem('cypress-test-subscription-status', subscriptionStatus);
    });

    // Add Cypress test header to all requests
    cy.intercept('/api/**', (req: any) => {
      req.headers['x-cypress-test'] = 'true';
    });

    // Visit the home page
    cy.visit('/');

    // Intercept API calls that would normally require authentication
    cy.intercept('GET', '/api/subscription/usage*', (req: any) => {
      req.headers['x-cypress-test'] = 'true';
      req.continue((res: any) => {
        // If the API doesn't return proper data, provide mock data
        if (!res.body?.usage) {
          res.send({
            statusCode: 200,
            body: {
              usage: [
                {
                  id: 'mock-usage-1',
                  tenantId: 'f5a731c2-9c0b-4ef8-bb6d-6bb9bd380a11',
                  usageType: 'document_upload',
                  usageCount: Math.floor(quotaPercentUsed),
                  resourceSizeBytes: 83886080,
                  periodStart: new Date(new Date().getFullYear(), new Date().getMonth(), 1).toISOString(),
                  periodEnd: new Date(new Date().getFullYear(), new Date().getMonth() + 1, 0).toISOString(),
                  createdAt: new Date().toISOString()
                }
              ],
              quota: {
                quotaLimit: 100,
                currentUsage: Math.floor(quotaPercentUsed),
                percentUsed: quotaPercentUsed,
                remainingQuota: 100 - Math.floor(quotaPercentUsed)
              }
            }
          });
        }
      });
    }).as('getUsage');

    cy.intercept('POST', '/api/subscription/usage', (req: any) => {
      req.headers['x-cypress-test'] = 'true';
      req.continue((res: any) => {
        // If the API doesn't return proper data, provide mock data
        if (!res.body?.quota) {
          res.send({
            statusCode: 200,
            body: {
              quota: {
                quotaLimit: 100,
                currentUsage: Math.floor(quotaPercentUsed),
                percentUsed: quotaPercentUsed,
                remainingQuota: 100 - Math.floor(quotaPercentUsed)
              }
            }
          });
        }
      });
    }).as('checkQuota');

    // Prepare dates for subscription
    const now = new Date();
    const currentPeriodStart = new Date(now.getFullYear(), now.getMonth(), 1).toISOString();
    const currentPeriodEnd = new Date(now.getFullYear(), now.getMonth() + 1, 0, 23, 59, 59).toISOString();

    // Trial end date (14 days from now)
    const trialEnd = new Date(now.getTime() + 14 * 24 * 60 * 60 * 1000).toISOString();

    // Canceled date (3 days ago)
    const canceledAt = new Date(now.getTime() - 3 * 24 * 60 * 60 * 1000).toISOString();

    // Intercept subscription data
    cy.intercept('GET', '/api/subscription/tenant*', {
      statusCode: 200,
      body: {
        id: 'mock-subscription-id',
        tenantId: 'f5a731c2-9c0b-4ef8-bb6d-6bb9bd380a11',
        planId: 'mock-plan-id',
        status: subscriptionStatus,
        billingCycle: 'monthly',
        currentPeriodStart: currentPeriodStart,
        currentPeriodEnd: currentPeriodEnd,
        trialEnd: subscriptionStatus === 'trialing' ? trialEnd : null,
        canceledAt: subscriptionStatus === 'canceled' ? canceledAt : null,
        plan: {
          id: 'mock-plan-id',
          name: 'Professional Plan',
          code: 'pro',
          description: 'Professional plan for testing',
          features: {
            maxUsers: 15,
            maxCases: 500,
            maxDocuments: 5000,
            hasIntakeAgent: true,
            hasAdvancedAnalytics: false,
            hasPrioritySupport: false,
            basic_access: true
          }
        }
      }
    }).as('getSubscription');
  }, {
    validate: () => {
      // Check if we're still logged in
      cy.window().then((win: any) => {
        const authData = win.localStorage.getItem('supabase.auth.data');
        return authData !== null;
      });
    },
    cacheAcrossSpecs: true
  });
});

// Logout command
Cypress.Commands.add('logout', () => {
  cy.visit('/dashboard');
  cy.get('[data-testid="user-menu"]').click();
  cy.contains('Sign Out').click();
  cy.url().should('include', '/login');
});

// Attach file command (for file uploads)
Cypress.Commands.add('attachFile', (filePath: string) => {
  cy.get('input[type="file"]').then((input) => {
    cy.fixture(filePath, 'base64').then((content: string) => {
      const blob = Cypress.Blob.base64StringToBlob(content);
      const testFile = new File([blob], filePath.split('/').pop() || 'file');
      const dataTransfer = new DataTransfer();

      dataTransfer.items.add(testFile);
      const inputElement = (input as any)[0] as HTMLInputElement;
      inputElement.files = dataTransfer.files;
      return cy.wrap(input).trigger('change', { force: true });
    });
  });
});

export {};
