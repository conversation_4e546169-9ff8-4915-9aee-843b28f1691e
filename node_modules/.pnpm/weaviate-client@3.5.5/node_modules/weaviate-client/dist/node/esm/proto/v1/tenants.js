// Code generated by protoc-gen-ts_proto. DO NOT EDIT.
// versions:
//   protoc-gen-ts_proto  v1.176.0
//   protoc               v3.19.1
// source: v1/tenants.proto
/* eslint-disable */
import _m0 from 'protobufjs/minimal.js';
export const protobufPackage = 'weaviate.v1';
export var TenantActivityStatus;
(function (TenantActivityStatus) {
  TenantActivityStatus[(TenantActivityStatus['TENANT_ACTIVITY_STATUS_UNSPECIFIED'] = 0)] =
    'TENANT_ACTIVITY_STATUS_UNSPECIFIED';
  TenantActivityStatus[(TenantActivityStatus['TENANT_ACTIVITY_STATUS_HOT'] = 1)] =
    'TENANT_ACTIVITY_STATUS_HOT';
  TenantActivityStatus[(TenantActivityStatus['TENANT_ACTIVITY_STATUS_COLD'] = 2)] =
    'TENANT_ACTIVITY_STATUS_COLD';
  TenantActivityStatus[(TenantActivityStatus['TENANT_ACTIVITY_STATUS_FROZEN'] = 4)] =
    'TENANT_ACTIVITY_STATUS_FROZEN';
  TenantActivityStatus[(TenantActivityStatus['TENANT_ACTIVITY_STATUS_UNFREEZING'] = 5)] =
    'TENANT_ACTIVITY_STATUS_UNFREEZING';
  TenantActivityStatus[(TenantActivityStatus['TENANT_ACTIVITY_STATUS_FREEZING'] = 6)] =
    'TENANT_ACTIVITY_STATUS_FREEZING';
  /** TENANT_ACTIVITY_STATUS_ACTIVE - not used yet - added to let the clients already add code to handle this in the future */
  TenantActivityStatus[(TenantActivityStatus['TENANT_ACTIVITY_STATUS_ACTIVE'] = 7)] =
    'TENANT_ACTIVITY_STATUS_ACTIVE';
  TenantActivityStatus[(TenantActivityStatus['TENANT_ACTIVITY_STATUS_INACTIVE'] = 8)] =
    'TENANT_ACTIVITY_STATUS_INACTIVE';
  TenantActivityStatus[(TenantActivityStatus['TENANT_ACTIVITY_STATUS_OFFLOADED'] = 9)] =
    'TENANT_ACTIVITY_STATUS_OFFLOADED';
  TenantActivityStatus[(TenantActivityStatus['TENANT_ACTIVITY_STATUS_OFFLOADING'] = 10)] =
    'TENANT_ACTIVITY_STATUS_OFFLOADING';
  TenantActivityStatus[(TenantActivityStatus['TENANT_ACTIVITY_STATUS_ONLOADING'] = 11)] =
    'TENANT_ACTIVITY_STATUS_ONLOADING';
  TenantActivityStatus[(TenantActivityStatus['UNRECOGNIZED'] = -1)] = 'UNRECOGNIZED';
})(TenantActivityStatus || (TenantActivityStatus = {}));
export function tenantActivityStatusFromJSON(object) {
  switch (object) {
    case 0:
    case 'TENANT_ACTIVITY_STATUS_UNSPECIFIED':
      return TenantActivityStatus.TENANT_ACTIVITY_STATUS_UNSPECIFIED;
    case 1:
    case 'TENANT_ACTIVITY_STATUS_HOT':
      return TenantActivityStatus.TENANT_ACTIVITY_STATUS_HOT;
    case 2:
    case 'TENANT_ACTIVITY_STATUS_COLD':
      return TenantActivityStatus.TENANT_ACTIVITY_STATUS_COLD;
    case 4:
    case 'TENANT_ACTIVITY_STATUS_FROZEN':
      return TenantActivityStatus.TENANT_ACTIVITY_STATUS_FROZEN;
    case 5:
    case 'TENANT_ACTIVITY_STATUS_UNFREEZING':
      return TenantActivityStatus.TENANT_ACTIVITY_STATUS_UNFREEZING;
    case 6:
    case 'TENANT_ACTIVITY_STATUS_FREEZING':
      return TenantActivityStatus.TENANT_ACTIVITY_STATUS_FREEZING;
    case 7:
    case 'TENANT_ACTIVITY_STATUS_ACTIVE':
      return TenantActivityStatus.TENANT_ACTIVITY_STATUS_ACTIVE;
    case 8:
    case 'TENANT_ACTIVITY_STATUS_INACTIVE':
      return TenantActivityStatus.TENANT_ACTIVITY_STATUS_INACTIVE;
    case 9:
    case 'TENANT_ACTIVITY_STATUS_OFFLOADED':
      return TenantActivityStatus.TENANT_ACTIVITY_STATUS_OFFLOADED;
    case 10:
    case 'TENANT_ACTIVITY_STATUS_OFFLOADING':
      return TenantActivityStatus.TENANT_ACTIVITY_STATUS_OFFLOADING;
    case 11:
    case 'TENANT_ACTIVITY_STATUS_ONLOADING':
      return TenantActivityStatus.TENANT_ACTIVITY_STATUS_ONLOADING;
    case -1:
    case 'UNRECOGNIZED':
    default:
      return TenantActivityStatus.UNRECOGNIZED;
  }
}
export function tenantActivityStatusToJSON(object) {
  switch (object) {
    case TenantActivityStatus.TENANT_ACTIVITY_STATUS_UNSPECIFIED:
      return 'TENANT_ACTIVITY_STATUS_UNSPECIFIED';
    case TenantActivityStatus.TENANT_ACTIVITY_STATUS_HOT:
      return 'TENANT_ACTIVITY_STATUS_HOT';
    case TenantActivityStatus.TENANT_ACTIVITY_STATUS_COLD:
      return 'TENANT_ACTIVITY_STATUS_COLD';
    case TenantActivityStatus.TENANT_ACTIVITY_STATUS_FROZEN:
      return 'TENANT_ACTIVITY_STATUS_FROZEN';
    case TenantActivityStatus.TENANT_ACTIVITY_STATUS_UNFREEZING:
      return 'TENANT_ACTIVITY_STATUS_UNFREEZING';
    case TenantActivityStatus.TENANT_ACTIVITY_STATUS_FREEZING:
      return 'TENANT_ACTIVITY_STATUS_FREEZING';
    case TenantActivityStatus.TENANT_ACTIVITY_STATUS_ACTIVE:
      return 'TENANT_ACTIVITY_STATUS_ACTIVE';
    case TenantActivityStatus.TENANT_ACTIVITY_STATUS_INACTIVE:
      return 'TENANT_ACTIVITY_STATUS_INACTIVE';
    case TenantActivityStatus.TENANT_ACTIVITY_STATUS_OFFLOADED:
      return 'TENANT_ACTIVITY_STATUS_OFFLOADED';
    case TenantActivityStatus.TENANT_ACTIVITY_STATUS_OFFLOADING:
      return 'TENANT_ACTIVITY_STATUS_OFFLOADING';
    case TenantActivityStatus.TENANT_ACTIVITY_STATUS_ONLOADING:
      return 'TENANT_ACTIVITY_STATUS_ONLOADING';
    case TenantActivityStatus.UNRECOGNIZED:
    default:
      return 'UNRECOGNIZED';
  }
}
function createBaseTenantsGetRequest() {
  return { collection: '', names: undefined };
}
export const TenantsGetRequest = {
  encode(message, writer = _m0.Writer.create()) {
    if (message.collection !== '') {
      writer.uint32(10).string(message.collection);
    }
    if (message.names !== undefined) {
      TenantNames.encode(message.names, writer.uint32(18).fork()).ldelim();
    }
    return writer;
  },
  decode(input, length) {
    const reader = input instanceof _m0.Reader ? input : _m0.Reader.create(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseTenantsGetRequest();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1:
          if (tag !== 10) {
            break;
          }
          message.collection = reader.string();
          continue;
        case 2:
          if (tag !== 18) {
            break;
          }
          message.names = TenantNames.decode(reader, reader.uint32());
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skipType(tag & 7);
    }
    return message;
  },
  fromJSON(object) {
    return {
      collection: isSet(object.collection) ? globalThis.String(object.collection) : '',
      names: isSet(object.names) ? TenantNames.fromJSON(object.names) : undefined,
    };
  },
  toJSON(message) {
    const obj = {};
    if (message.collection !== '') {
      obj.collection = message.collection;
    }
    if (message.names !== undefined) {
      obj.names = TenantNames.toJSON(message.names);
    }
    return obj;
  },
  create(base) {
    return TenantsGetRequest.fromPartial(base !== null && base !== void 0 ? base : {});
  },
  fromPartial(object) {
    var _a;
    const message = createBaseTenantsGetRequest();
    message.collection = (_a = object.collection) !== null && _a !== void 0 ? _a : '';
    message.names =
      object.names !== undefined && object.names !== null ? TenantNames.fromPartial(object.names) : undefined;
    return message;
  },
};
function createBaseTenantNames() {
  return { values: [] };
}
export const TenantNames = {
  encode(message, writer = _m0.Writer.create()) {
    for (const v of message.values) {
      writer.uint32(10).string(v);
    }
    return writer;
  },
  decode(input, length) {
    const reader = input instanceof _m0.Reader ? input : _m0.Reader.create(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseTenantNames();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1:
          if (tag !== 10) {
            break;
          }
          message.values.push(reader.string());
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skipType(tag & 7);
    }
    return message;
  },
  fromJSON(object) {
    return {
      values: globalThis.Array.isArray(object === null || object === void 0 ? void 0 : object.values)
        ? object.values.map((e) => globalThis.String(e))
        : [],
    };
  },
  toJSON(message) {
    var _a;
    const obj = {};
    if ((_a = message.values) === null || _a === void 0 ? void 0 : _a.length) {
      obj.values = message.values;
    }
    return obj;
  },
  create(base) {
    return TenantNames.fromPartial(base !== null && base !== void 0 ? base : {});
  },
  fromPartial(object) {
    var _a;
    const message = createBaseTenantNames();
    message.values = ((_a = object.values) === null || _a === void 0 ? void 0 : _a.map((e) => e)) || [];
    return message;
  },
};
function createBaseTenantsGetReply() {
  return { took: 0, tenants: [] };
}
export const TenantsGetReply = {
  encode(message, writer = _m0.Writer.create()) {
    if (message.took !== 0) {
      writer.uint32(13).float(message.took);
    }
    for (const v of message.tenants) {
      Tenant.encode(v, writer.uint32(18).fork()).ldelim();
    }
    return writer;
  },
  decode(input, length) {
    const reader = input instanceof _m0.Reader ? input : _m0.Reader.create(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseTenantsGetReply();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1:
          if (tag !== 13) {
            break;
          }
          message.took = reader.float();
          continue;
        case 2:
          if (tag !== 18) {
            break;
          }
          message.tenants.push(Tenant.decode(reader, reader.uint32()));
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skipType(tag & 7);
    }
    return message;
  },
  fromJSON(object) {
    return {
      took: isSet(object.took) ? globalThis.Number(object.took) : 0,
      tenants: globalThis.Array.isArray(object === null || object === void 0 ? void 0 : object.tenants)
        ? object.tenants.map((e) => Tenant.fromJSON(e))
        : [],
    };
  },
  toJSON(message) {
    var _a;
    const obj = {};
    if (message.took !== 0) {
      obj.took = message.took;
    }
    if ((_a = message.tenants) === null || _a === void 0 ? void 0 : _a.length) {
      obj.tenants = message.tenants.map((e) => Tenant.toJSON(e));
    }
    return obj;
  },
  create(base) {
    return TenantsGetReply.fromPartial(base !== null && base !== void 0 ? base : {});
  },
  fromPartial(object) {
    var _a, _b;
    const message = createBaseTenantsGetReply();
    message.took = (_a = object.took) !== null && _a !== void 0 ? _a : 0;
    message.tenants =
      ((_b = object.tenants) === null || _b === void 0 ? void 0 : _b.map((e) => Tenant.fromPartial(e))) || [];
    return message;
  },
};
function createBaseTenant() {
  return { name: '', activityStatus: 0 };
}
export const Tenant = {
  encode(message, writer = _m0.Writer.create()) {
    if (message.name !== '') {
      writer.uint32(10).string(message.name);
    }
    if (message.activityStatus !== 0) {
      writer.uint32(16).int32(message.activityStatus);
    }
    return writer;
  },
  decode(input, length) {
    const reader = input instanceof _m0.Reader ? input : _m0.Reader.create(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseTenant();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1:
          if (tag !== 10) {
            break;
          }
          message.name = reader.string();
          continue;
        case 2:
          if (tag !== 16) {
            break;
          }
          message.activityStatus = reader.int32();
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skipType(tag & 7);
    }
    return message;
  },
  fromJSON(object) {
    return {
      name: isSet(object.name) ? globalThis.String(object.name) : '',
      activityStatus: isSet(object.activityStatus) ? tenantActivityStatusFromJSON(object.activityStatus) : 0,
    };
  },
  toJSON(message) {
    const obj = {};
    if (message.name !== '') {
      obj.name = message.name;
    }
    if (message.activityStatus !== 0) {
      obj.activityStatus = tenantActivityStatusToJSON(message.activityStatus);
    }
    return obj;
  },
  create(base) {
    return Tenant.fromPartial(base !== null && base !== void 0 ? base : {});
  },
  fromPartial(object) {
    var _a, _b;
    const message = createBaseTenant();
    message.name = (_a = object.name) !== null && _a !== void 0 ? _a : '';
    message.activityStatus = (_b = object.activityStatus) !== null && _b !== void 0 ? _b : 0;
    return message;
  },
};
function isSet(value) {
  return value !== null && value !== undefined;
}
