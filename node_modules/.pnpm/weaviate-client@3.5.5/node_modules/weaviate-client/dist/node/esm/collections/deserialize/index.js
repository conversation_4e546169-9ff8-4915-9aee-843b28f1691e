var __awaiter =
  (this && this.__awaiter) ||
  function (thisArg, _arguments, P, generator) {
    function adopt(value) {
      return value instanceof P
        ? value
        : new P(function (resolve) {
            resolve(value);
          });
    }
    return new (P || (P = Promise))(function (resolve, reject) {
      function fulfilled(value) {
        try {
          step(generator.next(value));
        } catch (e) {
          reject(e);
        }
      }
      function rejected(value) {
        try {
          step(generator['throw'](value));
        } catch (e) {
          reject(e);
        }
      }
      function step(result) {
        result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected);
      }
      step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
  };
import { WeaviateDeserializationError } from '../../errors.js';
import { TenantActivityStatus } from '../../proto/v1/tenants.js';
import { referenceFromObjects } from '../references/utils.js';
export class Deserialize {
  constructor(supports125ListValue) {
    this.supports125ListValue = supports125ListValue;
  }
  static use(support) {
    return __awaiter(this, void 0, void 0, function* () {
      const supports125ListValue = yield support.supports125ListValue().then((res) => res.supports);
      return new Deserialize(supports125ListValue);
    });
  }
  static aggregateBoolean(aggregation) {
    return {
      count: aggregation.count,
      percentageFalse: aggregation.percentageFalse,
      percentageTrue: aggregation.percentageTrue,
      totalFalse: aggregation.totalFalse,
      totalTrue: aggregation.totalTrue,
    };
  }
  static aggregateDate(aggregation) {
    const parse = (date) => (date !== undefined ? date : undefined);
    return {
      count: aggregation.count,
      maximum: parse(aggregation.maximum),
      median: parse(aggregation.median),
      minimum: parse(aggregation.minimum),
      mode: parse(aggregation.mode),
    };
  }
  static aggregateInt(aggregation) {
    return {
      count: aggregation.count,
      maximum: aggregation.maximum,
      mean: aggregation.mean,
      median: aggregation.median,
      minimum: aggregation.minimum,
      mode: aggregation.mode,
      sum: aggregation.sum,
    };
  }
  static aggregateNumber(aggregation) {
    return {
      count: aggregation.count,
      maximum: aggregation.maximum,
      mean: aggregation.mean,
      median: aggregation.median,
      minimum: aggregation.minimum,
      mode: aggregation.mode,
      sum: aggregation.sum,
    };
  }
  static aggregateText(aggregation) {
    var _a;
    return {
      count: aggregation.count,
      topOccurrences:
        (_a = aggregation.topOccurences) === null || _a === void 0
          ? void 0
          : _a.items.map((occurrence) => {
              return {
                occurs: occurrence.occurs,
                value: occurrence.value,
              };
            }),
    };
  }
  static mapAggregate(aggregation) {
    if (aggregation.boolean !== undefined) return Deserialize.aggregateBoolean(aggregation.boolean);
    if (aggregation.date !== undefined) return Deserialize.aggregateDate(aggregation.date);
    if (aggregation.int !== undefined) return Deserialize.aggregateInt(aggregation.int);
    if (aggregation.number !== undefined) return Deserialize.aggregateNumber(aggregation.number);
    // if (aggregation.reference !== undefined) return aggregation.reference;
    if (aggregation.text !== undefined) return Deserialize.aggregateText(aggregation.text);
    throw new WeaviateDeserializationError(`Unknown aggregation type: ${aggregation}`);
  }
  static aggregations(aggregations) {
    return aggregations
      ? Object.fromEntries(
          aggregations.aggregations.map((aggregation) => [
            aggregation.property,
            Deserialize.mapAggregate(aggregation),
          ])
        )
      : {};
  }
  static aggregate(reply) {
    if (reply.singleResult === undefined) {
      throw new WeaviateDeserializationError('No single result in aggregate response');
    }
    return {
      totalCount: reply.singleResult.objectsCount,
      properties: Deserialize.aggregations(reply.singleResult.aggregations),
    };
  }
  static aggregateGroupBy(reply) {
    if (reply.groupedResults === undefined)
      throw new WeaviateDeserializationError('No grouped results in aggregate response');
    const parse = (groupedBy) => {
      if (groupedBy === undefined)
        throw new WeaviateDeserializationError('No groupedBy in aggregate response');
      let value;
      if (groupedBy.boolean !== undefined) value = groupedBy.boolean;
      else if (groupedBy.booleans !== undefined) value = groupedBy.booleans.values;
      else if (groupedBy.geo !== undefined) value = groupedBy.geo;
      else if (groupedBy.int !== undefined) value = groupedBy.int;
      else if (groupedBy.ints !== undefined) value = groupedBy.ints.values;
      else if (groupedBy.number !== undefined) value = groupedBy.number;
      else if (groupedBy.numbers !== undefined) value = groupedBy.numbers.values;
      else if (groupedBy.text !== undefined) value = groupedBy.text;
      else if (groupedBy.texts !== undefined) value = groupedBy.texts.values;
      else {
        console.warn(`Unknown groupBy type: ${JSON.stringify(groupedBy, null, 2)}`);
        value = '';
      }
      return {
        prop: groupedBy.path[0],
        value,
      };
    };
    return reply.groupedResults.groups.map((group) => {
      return {
        totalCount: group.objectsCount,
        groupedBy: parse(group.groupedBy),
        properties: Deserialize.aggregations(group.aggregations),
      };
    });
  }
  query(reply) {
    return {
      objects: reply.results.map((result) => {
        return {
          metadata: Deserialize.metadata(result.metadata),
          properties: this.properties(result.properties),
          references: this.references(result.properties),
          uuid: Deserialize.uuid(result.metadata),
          vectors: Deserialize.vectors(result.metadata),
        };
      }),
    };
  }
  generate(reply) {
    var _a, _b;
    return {
      objects: reply.results.map((result) => {
        var _a, _b, _c, _d;
        return {
          generated: ((_a = result.metadata) === null || _a === void 0 ? void 0 : _a.generativePresent)
            ? (_b = result.metadata) === null || _b === void 0
              ? void 0
              : _b.generative
            : result.generative
            ? result.generative.values[0].result
            : undefined,
          generative: result.generative
            ? {
                text: result.generative.values[0].result,
                debug: result.generative.values[0].debug,
                metadata: result.generative.values[0].metadata,
              }
            : ((_c = result.metadata) === null || _c === void 0 ? void 0 : _c.generativePresent)
            ? {
                text: (_d = result.metadata) === null || _d === void 0 ? void 0 : _d.generative,
              }
            : undefined,
          metadata: Deserialize.metadata(result.metadata),
          properties: this.properties(result.properties),
          references: this.references(result.properties),
          uuid: Deserialize.uuid(result.metadata),
          vectors: Deserialize.vectors(result.metadata),
        };
      }),
      generated:
        reply.generativeGroupedResult !== ''
          ? reply.generativeGroupedResult
          : reply.generativeGroupedResults
          ? reply.generativeGroupedResults.values[0].result
          : undefined,
      generative: reply.generativeGroupedResults
        ? {
            text:
              (_a = reply.generativeGroupedResults) === null || _a === void 0 ? void 0 : _a.values[0].result,
            metadata:
              (_b = reply.generativeGroupedResults) === null || _b === void 0
                ? void 0
                : _b.values[0].metadata,
          }
        : reply.generativeGroupedResult !== ''
        ? {
            text: reply.generativeGroupedResult,
          }
        : undefined,
    };
  }
  queryGroupBy(reply) {
    const objects = [];
    const groups = {};
    reply.groupByResults.forEach((result) => {
      const objs = result.objects.map((object) => {
        return {
          belongsToGroup: result.name,
          metadata: Deserialize.metadata(object.metadata),
          properties: this.properties(object.properties),
          references: this.references(object.properties),
          uuid: Deserialize.uuid(object.metadata),
          vectors: Deserialize.vectors(object.metadata),
        };
      });
      groups[result.name] = {
        maxDistance: result.maxDistance,
        minDistance: result.minDistance,
        name: result.name,
        numberOfObjects: result.numberOfObjects,
        objects: objs,
      };
      objects.push(...objs);
    });
    return {
      objects: objects,
      groups: groups,
    };
  }
  generateGroupBy(reply) {
    const objects = [];
    const groups = {};
    reply.groupByResults.forEach((result) => {
      var _a;
      const objs = result.objects.map((object) => {
        return {
          belongsToGroup: result.name,
          metadata: Deserialize.metadata(object.metadata),
          properties: this.properties(object.properties),
          references: this.references(object.properties),
          uuid: Deserialize.uuid(object.metadata),
          vectors: Deserialize.vectors(object.metadata),
        };
      });
      groups[result.name] = {
        maxDistance: result.maxDistance,
        minDistance: result.minDistance,
        name: result.name,
        numberOfObjects: result.numberOfObjects,
        objects: objs,
        generated: (_a = result.generative) === null || _a === void 0 ? void 0 : _a.result,
      };
      objects.push(...objs);
    });
    return {
      objects: objects,
      groups: groups,
      generated: reply.generativeGroupedResult,
    };
  }
  properties(properties) {
    if (!properties) return {};
    return this.objectProperties(properties.nonRefProps);
  }
  references(properties) {
    if (!properties) return undefined;
    if (properties.refProps.length === 0) return properties.refPropsRequested ? {} : undefined;
    const out = {};
    properties.refProps.forEach((property) => {
      const uuids = [];
      out[property.propName] = referenceFromObjects(
        property.properties.map((property) => {
          const uuid = Deserialize.uuid(property.metadata);
          uuids.push(uuid);
          return {
            metadata: Deserialize.metadata(property.metadata),
            properties: this.properties(property),
            references: this.references(property),
            uuid: uuid,
            vectors: Deserialize.vectors(property.metadata),
          };
        }),
        property.properties.length > 0 ? property.properties[0].targetCollection : '',
        uuids
      );
    });
    return out;
  }
  parsePropertyValue(value) {
    if (value.boolValue !== undefined) return value.boolValue;
    if (value.dateValue !== undefined) return new Date(value.dateValue);
    if (value.intValue !== undefined) return value.intValue;
    if (value.listValue !== undefined)
      return this.supports125ListValue
        ? this.parseListValue(value.listValue)
        : value.listValue.values.map((v) => this.parsePropertyValue(v));
    if (value.numberValue !== undefined) return value.numberValue;
    if (value.objectValue !== undefined) return this.objectProperties(value.objectValue);
    if (value.stringValue !== undefined) return value.stringValue;
    if (value.textValue !== undefined) return value.textValue;
    if (value.uuidValue !== undefined) return value.uuidValue;
    if (value.blobValue !== undefined) return value.blobValue;
    if (value.geoValue !== undefined) return value.geoValue;
    if (value.phoneValue !== undefined) return value.phoneValue;
    if (value.nullValue !== undefined) return undefined;
    throw new WeaviateDeserializationError(`Unknown value type: ${JSON.stringify(value, null, 2)}`);
  }
  parseListValue(value) {
    if (value.boolValues !== undefined) return value.boolValues.values;
    if (value.dateValues !== undefined) return value.dateValues.values.map((date) => new Date(date));
    if (value.intValues !== undefined) return Deserialize.intsFromBytes(value.intValues.values);
    if (value.numberValues !== undefined) return Deserialize.numbersFromBytes(value.numberValues.values);
    if (value.objectValues !== undefined)
      return value.objectValues.values.map((v) => this.objectProperties(v));
    if (value.textValues !== undefined) return value.textValues.values;
    if (value.uuidValues !== undefined) return value.uuidValues.values;
    throw new Error(`Unknown list value type: ${JSON.stringify(value, null, 2)}`);
  }
  objectProperties(properties) {
    const out = {};
    if (properties) {
      Object.entries(properties.fields).forEach(([key, value]) => {
        out[key] = this.parsePropertyValue(value);
      });
    }
    return out;
  }
  static metadata(metadata) {
    const out = {};
    if (!metadata) return undefined;
    if (metadata.creationTimeUnixPresent) out.creationTime = new Date(metadata.creationTimeUnix);
    if (metadata.lastUpdateTimeUnixPresent) out.updateTime = new Date(metadata.lastUpdateTimeUnix);
    if (metadata.distancePresent) out.distance = metadata.distance;
    if (metadata.certaintyPresent) out.certainty = metadata.certainty;
    if (metadata.scorePresent) out.score = metadata.score;
    if (metadata.explainScorePresent) out.explainScore = metadata.explainScore;
    if (metadata.rerankScorePresent) out.rerankScore = metadata.rerankScore;
    if (metadata.isConsistent) out.isConsistent = metadata.isConsistent;
    return out;
  }
  static uuid(metadata) {
    if (!metadata || !(metadata.id.length > 0))
      throw new WeaviateDeserializationError('No uuid returned from server');
    return metadata.id;
  }
  static vectorFromBytes(bytes) {
    const buffer = Buffer.from(bytes);
    const view = new Float32Array(buffer.buffer, buffer.byteOffset, buffer.byteLength / 4); // vector is float32 in weaviate
    return Array.from(view);
  }
  static intsFromBytes(bytes) {
    const buffer = Buffer.from(bytes);
    const view = new BigInt64Array(buffer.buffer, buffer.byteOffset, buffer.byteLength / 8); // ints are float64 in weaviate
    return Array.from(view).map(Number);
  }
  static numbersFromBytes(bytes) {
    const buffer = Buffer.from(bytes);
    const view = new Float64Array(buffer.buffer, buffer.byteOffset, buffer.byteLength / 8); // numbers are float64 in weaviate
    return Array.from(view);
  }
  static vectors(metadata) {
    if (!metadata) return {};
    if (metadata.vectorBytes.length === 0 && metadata.vector.length === 0 && metadata.vectors.length === 0)
      return {};
    if (metadata.vectorBytes.length > 0)
      return { default: Deserialize.vectorFromBytes(metadata.vectorBytes) };
    return Object.fromEntries(
      metadata.vectors.map((vector) => [vector.name, Deserialize.vectorFromBytes(vector.vectorBytes)])
    );
  }
  static batchObjects(reply, originalObjs, mappedObjs, elapsed) {
    const allResponses = [];
    const errors = {};
    const successes = {};
    const batchErrors = {};
    reply.errors.forEach((error) => {
      batchErrors[error.index] = error.error;
    });
    for (const [index, object] of originalObjs.entries()) {
      if (index in batchErrors) {
        const error = {
          message: batchErrors[index],
          object: object,
          originalUuid: object.id,
        };
        errors[index] = error;
        allResponses[index] = error;
      } else {
        const mappedObj = mappedObjs[index];
        successes[index] = mappedObj.uuid;
        allResponses[index] = mappedObj.uuid;
      }
    }
    return {
      uuids: successes,
      errors: errors,
      hasErrors: reply.errors.length > 0,
      allResponses: allResponses,
      elapsedSeconds: elapsed,
    };
  }
  static deleteMany(reply, verbose) {
    return Object.assign(Object.assign({}, reply), {
      objects: verbose
        ? reply.objects.map((obj) => {
            return {
              id: obj.uuid.toString(),
              successful: obj.successful,
              error: obj.error,
            };
          })
        : undefined,
    });
  }
  static activityStatusGRPC(status) {
    switch (status) {
      case TenantActivityStatus.TENANT_ACTIVITY_STATUS_COLD:
      case TenantActivityStatus.TENANT_ACTIVITY_STATUS_INACTIVE:
        return 'INACTIVE';
      case TenantActivityStatus.TENANT_ACTIVITY_STATUS_HOT:
      case TenantActivityStatus.TENANT_ACTIVITY_STATUS_ACTIVE:
        return 'ACTIVE';
      case TenantActivityStatus.TENANT_ACTIVITY_STATUS_FROZEN:
      case TenantActivityStatus.TENANT_ACTIVITY_STATUS_OFFLOADED:
        return 'OFFLOADED';
      case TenantActivityStatus.TENANT_ACTIVITY_STATUS_FREEZING:
      case TenantActivityStatus.TENANT_ACTIVITY_STATUS_OFFLOADING:
        return 'OFFLOADING';
      case TenantActivityStatus.TENANT_ACTIVITY_STATUS_UNFREEZING:
      case TenantActivityStatus.TENANT_ACTIVITY_STATUS_ONLOADING:
        return 'ONLOADING';
      default:
        throw new Error(`Unsupported tenant activity status: ${status}`);
    }
  }
  static activityStatusREST(status) {
    switch (status) {
      case 'COLD':
        return 'INACTIVE';
      case 'HOT':
        return 'ACTIVE';
      case 'FROZEN':
        return 'OFFLOADED';
      case 'FREEZING':
        return 'OFFLOADING';
      case 'UNFREEZING':
        return 'ONLOADING';
      case undefined:
        return 'ACTIVE';
      default:
        return status;
    }
  }
  static tenantsGet(reply) {
    const tenants = {};
    reply.tenants.forEach((t) => {
      tenants[t.name] = {
        name: t.name,
        activityStatus: Deserialize.activityStatusGRPC(t.activityStatus),
      };
    });
    return tenants;
  }
}
