/// <reference types="node" resolution-mode="require"/>
import Connection from '../../connection/grpc.js';
import { ConsistencyLevel } from '../../data/index.js';
import { DbVersionSupport } from '../../utils/dbVersion.js';
import {
  BaseBm25Options,
  BaseHybridOptions,
  BaseNearOptions,
  BaseNearTextOptions,
  FetchObjectsOptions,
  GroupByBm25Options,
  GroupByHybridOptions,
  GroupByNearOptions,
  GroupByNearTextOptions,
  NearMediaType,
} from '../query/types.js';
import {
  GenerateOptions,
  GenerativeConfigRuntime,
  GenerativeGroupByReturn,
  GenerativeReturn,
} from '../types/index.js';
import { Generate } from './types.js';
declare class GenerateManager<T> implements Generate<T> {
  private check;
  private constructor();
  static use<T>(
    connection: Connection,
    name: string,
    dbVersionSupport: DbVersionSupport,
    consistencyLevel?: ConsistencyLevel,
    tenant?: string
  ): GenerateManager<T>;
  private parseReply;
  private parseGroupByReply;
  fetchObjects<C extends GenerativeConfigRuntime | undefined = undefined>(
    generate: GenerateOptions<T, C>,
    opts?: FetchObjectsOptions<T>
  ): Promise<GenerativeReturn<T, C>>;
  bm25<C extends GenerativeConfigRuntime | undefined = undefined>(
    query: string,
    generate: GenerateOptions<T, C>,
    opts?: BaseBm25Options<T>
  ): Promise<GenerativeReturn<T, C>>;
  bm25<C extends GenerativeConfigRuntime | undefined = undefined>(
    query: string,
    generate: GenerateOptions<T, C>,
    opts: GroupByBm25Options<T>
  ): Promise<GenerativeGroupByReturn<T, C>>;
  hybrid<C extends GenerativeConfigRuntime | undefined = undefined>(
    query: string,
    generate: GenerateOptions<T, C>,
    opts?: BaseHybridOptions<T>
  ): Promise<GenerativeReturn<T, C>>;
  hybrid<C extends GenerativeConfigRuntime | undefined = undefined>(
    query: string,
    generate: GenerateOptions<T, C>,
    opts: GroupByHybridOptions<T>
  ): Promise<GenerativeGroupByReturn<T, C>>;
  nearImage<C extends GenerativeConfigRuntime | undefined = undefined>(
    image: string | Buffer,
    generate: GenerateOptions<T, C>,
    opts?: BaseNearOptions<T>
  ): Promise<GenerativeReturn<T, C>>;
  nearImage<C extends GenerativeConfigRuntime | undefined = undefined>(
    image: string | Buffer,
    generate: GenerateOptions<T, C>,
    opts: GroupByNearOptions<T>
  ): Promise<GenerativeGroupByReturn<T, C>>;
  nearObject<C extends GenerativeConfigRuntime | undefined = undefined>(
    id: string,
    generate: GenerateOptions<T, C>,
    opts?: BaseNearOptions<T>
  ): Promise<GenerativeReturn<T, C>>;
  nearObject<C extends GenerativeConfigRuntime | undefined = undefined>(
    id: string,
    generate: GenerateOptions<T, C>,
    opts: GroupByNearOptions<T>
  ): Promise<GenerativeGroupByReturn<T, C>>;
  nearText<C extends GenerativeConfigRuntime | undefined = undefined>(
    query: string | string[],
    generate: GenerateOptions<T, C>,
    opts?: BaseNearTextOptions<T>
  ): Promise<GenerativeReturn<T, C>>;
  nearText<C extends GenerativeConfigRuntime | undefined = undefined>(
    query: string | string[],
    generate: GenerateOptions<T, C>,
    opts: GroupByNearTextOptions<T>
  ): Promise<GenerativeGroupByReturn<T, C>>;
  nearVector<C extends GenerativeConfigRuntime | undefined = undefined>(
    vector: number[],
    generate: GenerateOptions<T, C>,
    opts?: BaseNearOptions<T>
  ): Promise<GenerativeReturn<T, C>>;
  nearVector<C extends GenerativeConfigRuntime | undefined = undefined>(
    vector: number[],
    generate: GenerateOptions<T, C>,
    opts: GroupByNearOptions<T>
  ): Promise<GenerativeGroupByReturn<T, C>>;
  nearMedia<C extends GenerativeConfigRuntime | undefined = undefined>(
    media: string | Buffer,
    type: NearMediaType,
    generate: GenerateOptions<T, C>,
    opts?: BaseNearOptions<T>
  ): Promise<GenerativeReturn<T, C>>;
  nearMedia<C extends GenerativeConfigRuntime | undefined = undefined>(
    media: string | Buffer,
    type: NearMediaType,
    generate: GenerateOptions<T, C>,
    opts: GroupByNearOptions<T>
  ): Promise<GenerativeGroupByReturn<T, C>>;
}
declare const _default: typeof GenerateManager.use;
export default _default;
export { generativeParameters } from './config.js';
export { Generate } from './types.js';
