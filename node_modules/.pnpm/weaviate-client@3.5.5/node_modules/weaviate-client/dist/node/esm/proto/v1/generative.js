// Code generated by protoc-gen-ts_proto. DO NOT EDIT.
// versions:
//   protoc-gen-ts_proto  v1.176.0
//   protoc               v3.19.1
// source: v1/generative.proto
/* eslint-disable */
import Long from 'long';
import _m0 from 'protobufjs/minimal.js';
import { TextArray } from './base.js';
export const protobufPackage = 'weaviate.v1';
function createBaseGenerativeSearch() {
  return {
    singleResponsePrompt: '',
    groupedResponseTask: '',
    groupedProperties: [],
    single: undefined,
    grouped: undefined,
  };
}
export const GenerativeSearch = {
  encode(message, writer = _m0.Writer.create()) {
    if (message.singleResponsePrompt !== '') {
      writer.uint32(10).string(message.singleResponsePrompt);
    }
    if (message.groupedResponseTask !== '') {
      writer.uint32(18).string(message.groupedResponseTask);
    }
    for (const v of message.groupedProperties) {
      writer.uint32(26).string(v);
    }
    if (message.single !== undefined) {
      GenerativeSearch_Single.encode(message.single, writer.uint32(34).fork()).ldelim();
    }
    if (message.grouped !== undefined) {
      GenerativeSearch_Grouped.encode(message.grouped, writer.uint32(42).fork()).ldelim();
    }
    return writer;
  },
  decode(input, length) {
    const reader = input instanceof _m0.Reader ? input : _m0.Reader.create(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseGenerativeSearch();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1:
          if (tag !== 10) {
            break;
          }
          message.singleResponsePrompt = reader.string();
          continue;
        case 2:
          if (tag !== 18) {
            break;
          }
          message.groupedResponseTask = reader.string();
          continue;
        case 3:
          if (tag !== 26) {
            break;
          }
          message.groupedProperties.push(reader.string());
          continue;
        case 4:
          if (tag !== 34) {
            break;
          }
          message.single = GenerativeSearch_Single.decode(reader, reader.uint32());
          continue;
        case 5:
          if (tag !== 42) {
            break;
          }
          message.grouped = GenerativeSearch_Grouped.decode(reader, reader.uint32());
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skipType(tag & 7);
    }
    return message;
  },
  fromJSON(object) {
    return {
      singleResponsePrompt: isSet(object.singleResponsePrompt)
        ? globalThis.String(object.singleResponsePrompt)
        : '',
      groupedResponseTask: isSet(object.groupedResponseTask)
        ? globalThis.String(object.groupedResponseTask)
        : '',
      groupedProperties: globalThis.Array.isArray(
        object === null || object === void 0 ? void 0 : object.groupedProperties
      )
        ? object.groupedProperties.map((e) => globalThis.String(e))
        : [],
      single: isSet(object.single) ? GenerativeSearch_Single.fromJSON(object.single) : undefined,
      grouped: isSet(object.grouped) ? GenerativeSearch_Grouped.fromJSON(object.grouped) : undefined,
    };
  },
  toJSON(message) {
    var _a;
    const obj = {};
    if (message.singleResponsePrompt !== '') {
      obj.singleResponsePrompt = message.singleResponsePrompt;
    }
    if (message.groupedResponseTask !== '') {
      obj.groupedResponseTask = message.groupedResponseTask;
    }
    if ((_a = message.groupedProperties) === null || _a === void 0 ? void 0 : _a.length) {
      obj.groupedProperties = message.groupedProperties;
    }
    if (message.single !== undefined) {
      obj.single = GenerativeSearch_Single.toJSON(message.single);
    }
    if (message.grouped !== undefined) {
      obj.grouped = GenerativeSearch_Grouped.toJSON(message.grouped);
    }
    return obj;
  },
  create(base) {
    return GenerativeSearch.fromPartial(base !== null && base !== void 0 ? base : {});
  },
  fromPartial(object) {
    var _a, _b, _c;
    const message = createBaseGenerativeSearch();
    message.singleResponsePrompt = (_a = object.singleResponsePrompt) !== null && _a !== void 0 ? _a : '';
    message.groupedResponseTask = (_b = object.groupedResponseTask) !== null && _b !== void 0 ? _b : '';
    message.groupedProperties =
      ((_c = object.groupedProperties) === null || _c === void 0 ? void 0 : _c.map((e) => e)) || [];
    message.single =
      object.single !== undefined && object.single !== null
        ? GenerativeSearch_Single.fromPartial(object.single)
        : undefined;
    message.grouped =
      object.grouped !== undefined && object.grouped !== null
        ? GenerativeSearch_Grouped.fromPartial(object.grouped)
        : undefined;
    return message;
  },
};
function createBaseGenerativeSearch_Single() {
  return { prompt: '', debug: false, queries: [] };
}
export const GenerativeSearch_Single = {
  encode(message, writer = _m0.Writer.create()) {
    if (message.prompt !== '') {
      writer.uint32(10).string(message.prompt);
    }
    if (message.debug !== false) {
      writer.uint32(16).bool(message.debug);
    }
    for (const v of message.queries) {
      GenerativeProvider.encode(v, writer.uint32(26).fork()).ldelim();
    }
    return writer;
  },
  decode(input, length) {
    const reader = input instanceof _m0.Reader ? input : _m0.Reader.create(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseGenerativeSearch_Single();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1:
          if (tag !== 10) {
            break;
          }
          message.prompt = reader.string();
          continue;
        case 2:
          if (tag !== 16) {
            break;
          }
          message.debug = reader.bool();
          continue;
        case 3:
          if (tag !== 26) {
            break;
          }
          message.queries.push(GenerativeProvider.decode(reader, reader.uint32()));
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skipType(tag & 7);
    }
    return message;
  },
  fromJSON(object) {
    return {
      prompt: isSet(object.prompt) ? globalThis.String(object.prompt) : '',
      debug: isSet(object.debug) ? globalThis.Boolean(object.debug) : false,
      queries: globalThis.Array.isArray(object === null || object === void 0 ? void 0 : object.queries)
        ? object.queries.map((e) => GenerativeProvider.fromJSON(e))
        : [],
    };
  },
  toJSON(message) {
    var _a;
    const obj = {};
    if (message.prompt !== '') {
      obj.prompt = message.prompt;
    }
    if (message.debug !== false) {
      obj.debug = message.debug;
    }
    if ((_a = message.queries) === null || _a === void 0 ? void 0 : _a.length) {
      obj.queries = message.queries.map((e) => GenerativeProvider.toJSON(e));
    }
    return obj;
  },
  create(base) {
    return GenerativeSearch_Single.fromPartial(base !== null && base !== void 0 ? base : {});
  },
  fromPartial(object) {
    var _a, _b, _c;
    const message = createBaseGenerativeSearch_Single();
    message.prompt = (_a = object.prompt) !== null && _a !== void 0 ? _a : '';
    message.debug = (_b = object.debug) !== null && _b !== void 0 ? _b : false;
    message.queries =
      ((_c = object.queries) === null || _c === void 0
        ? void 0
        : _c.map((e) => GenerativeProvider.fromPartial(e))) || [];
    return message;
  },
};
function createBaseGenerativeSearch_Grouped() {
  return { task: '', properties: undefined, queries: [] };
}
export const GenerativeSearch_Grouped = {
  encode(message, writer = _m0.Writer.create()) {
    if (message.task !== '') {
      writer.uint32(10).string(message.task);
    }
    if (message.properties !== undefined) {
      TextArray.encode(message.properties, writer.uint32(18).fork()).ldelim();
    }
    for (const v of message.queries) {
      GenerativeProvider.encode(v, writer.uint32(26).fork()).ldelim();
    }
    return writer;
  },
  decode(input, length) {
    const reader = input instanceof _m0.Reader ? input : _m0.Reader.create(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseGenerativeSearch_Grouped();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1:
          if (tag !== 10) {
            break;
          }
          message.task = reader.string();
          continue;
        case 2:
          if (tag !== 18) {
            break;
          }
          message.properties = TextArray.decode(reader, reader.uint32());
          continue;
        case 3:
          if (tag !== 26) {
            break;
          }
          message.queries.push(GenerativeProvider.decode(reader, reader.uint32()));
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skipType(tag & 7);
    }
    return message;
  },
  fromJSON(object) {
    return {
      task: isSet(object.task) ? globalThis.String(object.task) : '',
      properties: isSet(object.properties) ? TextArray.fromJSON(object.properties) : undefined,
      queries: globalThis.Array.isArray(object === null || object === void 0 ? void 0 : object.queries)
        ? object.queries.map((e) => GenerativeProvider.fromJSON(e))
        : [],
    };
  },
  toJSON(message) {
    var _a;
    const obj = {};
    if (message.task !== '') {
      obj.task = message.task;
    }
    if (message.properties !== undefined) {
      obj.properties = TextArray.toJSON(message.properties);
    }
    if ((_a = message.queries) === null || _a === void 0 ? void 0 : _a.length) {
      obj.queries = message.queries.map((e) => GenerativeProvider.toJSON(e));
    }
    return obj;
  },
  create(base) {
    return GenerativeSearch_Grouped.fromPartial(base !== null && base !== void 0 ? base : {});
  },
  fromPartial(object) {
    var _a, _b;
    const message = createBaseGenerativeSearch_Grouped();
    message.task = (_a = object.task) !== null && _a !== void 0 ? _a : '';
    message.properties =
      object.properties !== undefined && object.properties !== null
        ? TextArray.fromPartial(object.properties)
        : undefined;
    message.queries =
      ((_b = object.queries) === null || _b === void 0
        ? void 0
        : _b.map((e) => GenerativeProvider.fromPartial(e))) || [];
    return message;
  },
};
function createBaseGenerativeProvider() {
  return {
    returnMetadata: false,
    anthropic: undefined,
    anyscale: undefined,
    aws: undefined,
    cohere: undefined,
    dummy: undefined,
    mistral: undefined,
    ollama: undefined,
    openai: undefined,
    google: undefined,
    databricks: undefined,
    friendliai: undefined,
    nvidia: undefined,
    xai: undefined,
  };
}
export const GenerativeProvider = {
  encode(message, writer = _m0.Writer.create()) {
    if (message.returnMetadata !== false) {
      writer.uint32(8).bool(message.returnMetadata);
    }
    if (message.anthropic !== undefined) {
      GenerativeAnthropic.encode(message.anthropic, writer.uint32(18).fork()).ldelim();
    }
    if (message.anyscale !== undefined) {
      GenerativeAnyscale.encode(message.anyscale, writer.uint32(26).fork()).ldelim();
    }
    if (message.aws !== undefined) {
      GenerativeAWS.encode(message.aws, writer.uint32(34).fork()).ldelim();
    }
    if (message.cohere !== undefined) {
      GenerativeCohere.encode(message.cohere, writer.uint32(42).fork()).ldelim();
    }
    if (message.dummy !== undefined) {
      GenerativeDummy.encode(message.dummy, writer.uint32(50).fork()).ldelim();
    }
    if (message.mistral !== undefined) {
      GenerativeMistral.encode(message.mistral, writer.uint32(58).fork()).ldelim();
    }
    if (message.ollama !== undefined) {
      GenerativeOllama.encode(message.ollama, writer.uint32(66).fork()).ldelim();
    }
    if (message.openai !== undefined) {
      GenerativeOpenAI.encode(message.openai, writer.uint32(74).fork()).ldelim();
    }
    if (message.google !== undefined) {
      GenerativeGoogle.encode(message.google, writer.uint32(82).fork()).ldelim();
    }
    if (message.databricks !== undefined) {
      GenerativeDatabricks.encode(message.databricks, writer.uint32(90).fork()).ldelim();
    }
    if (message.friendliai !== undefined) {
      GenerativeFriendliAI.encode(message.friendliai, writer.uint32(98).fork()).ldelim();
    }
    if (message.nvidia !== undefined) {
      GenerativeNvidia.encode(message.nvidia, writer.uint32(106).fork()).ldelim();
    }
    if (message.xai !== undefined) {
      GenerativeXAI.encode(message.xai, writer.uint32(114).fork()).ldelim();
    }
    return writer;
  },
  decode(input, length) {
    const reader = input instanceof _m0.Reader ? input : _m0.Reader.create(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseGenerativeProvider();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1:
          if (tag !== 8) {
            break;
          }
          message.returnMetadata = reader.bool();
          continue;
        case 2:
          if (tag !== 18) {
            break;
          }
          message.anthropic = GenerativeAnthropic.decode(reader, reader.uint32());
          continue;
        case 3:
          if (tag !== 26) {
            break;
          }
          message.anyscale = GenerativeAnyscale.decode(reader, reader.uint32());
          continue;
        case 4:
          if (tag !== 34) {
            break;
          }
          message.aws = GenerativeAWS.decode(reader, reader.uint32());
          continue;
        case 5:
          if (tag !== 42) {
            break;
          }
          message.cohere = GenerativeCohere.decode(reader, reader.uint32());
          continue;
        case 6:
          if (tag !== 50) {
            break;
          }
          message.dummy = GenerativeDummy.decode(reader, reader.uint32());
          continue;
        case 7:
          if (tag !== 58) {
            break;
          }
          message.mistral = GenerativeMistral.decode(reader, reader.uint32());
          continue;
        case 8:
          if (tag !== 66) {
            break;
          }
          message.ollama = GenerativeOllama.decode(reader, reader.uint32());
          continue;
        case 9:
          if (tag !== 74) {
            break;
          }
          message.openai = GenerativeOpenAI.decode(reader, reader.uint32());
          continue;
        case 10:
          if (tag !== 82) {
            break;
          }
          message.google = GenerativeGoogle.decode(reader, reader.uint32());
          continue;
        case 11:
          if (tag !== 90) {
            break;
          }
          message.databricks = GenerativeDatabricks.decode(reader, reader.uint32());
          continue;
        case 12:
          if (tag !== 98) {
            break;
          }
          message.friendliai = GenerativeFriendliAI.decode(reader, reader.uint32());
          continue;
        case 13:
          if (tag !== 106) {
            break;
          }
          message.nvidia = GenerativeNvidia.decode(reader, reader.uint32());
          continue;
        case 14:
          if (tag !== 114) {
            break;
          }
          message.xai = GenerativeXAI.decode(reader, reader.uint32());
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skipType(tag & 7);
    }
    return message;
  },
  fromJSON(object) {
    return {
      returnMetadata: isSet(object.returnMetadata) ? globalThis.Boolean(object.returnMetadata) : false,
      anthropic: isSet(object.anthropic) ? GenerativeAnthropic.fromJSON(object.anthropic) : undefined,
      anyscale: isSet(object.anyscale) ? GenerativeAnyscale.fromJSON(object.anyscale) : undefined,
      aws: isSet(object.aws) ? GenerativeAWS.fromJSON(object.aws) : undefined,
      cohere: isSet(object.cohere) ? GenerativeCohere.fromJSON(object.cohere) : undefined,
      dummy: isSet(object.dummy) ? GenerativeDummy.fromJSON(object.dummy) : undefined,
      mistral: isSet(object.mistral) ? GenerativeMistral.fromJSON(object.mistral) : undefined,
      ollama: isSet(object.ollama) ? GenerativeOllama.fromJSON(object.ollama) : undefined,
      openai: isSet(object.openai) ? GenerativeOpenAI.fromJSON(object.openai) : undefined,
      google: isSet(object.google) ? GenerativeGoogle.fromJSON(object.google) : undefined,
      databricks: isSet(object.databricks) ? GenerativeDatabricks.fromJSON(object.databricks) : undefined,
      friendliai: isSet(object.friendliai) ? GenerativeFriendliAI.fromJSON(object.friendliai) : undefined,
      nvidia: isSet(object.nvidia) ? GenerativeNvidia.fromJSON(object.nvidia) : undefined,
      xai: isSet(object.xai) ? GenerativeXAI.fromJSON(object.xai) : undefined,
    };
  },
  toJSON(message) {
    const obj = {};
    if (message.returnMetadata !== false) {
      obj.returnMetadata = message.returnMetadata;
    }
    if (message.anthropic !== undefined) {
      obj.anthropic = GenerativeAnthropic.toJSON(message.anthropic);
    }
    if (message.anyscale !== undefined) {
      obj.anyscale = GenerativeAnyscale.toJSON(message.anyscale);
    }
    if (message.aws !== undefined) {
      obj.aws = GenerativeAWS.toJSON(message.aws);
    }
    if (message.cohere !== undefined) {
      obj.cohere = GenerativeCohere.toJSON(message.cohere);
    }
    if (message.dummy !== undefined) {
      obj.dummy = GenerativeDummy.toJSON(message.dummy);
    }
    if (message.mistral !== undefined) {
      obj.mistral = GenerativeMistral.toJSON(message.mistral);
    }
    if (message.ollama !== undefined) {
      obj.ollama = GenerativeOllama.toJSON(message.ollama);
    }
    if (message.openai !== undefined) {
      obj.openai = GenerativeOpenAI.toJSON(message.openai);
    }
    if (message.google !== undefined) {
      obj.google = GenerativeGoogle.toJSON(message.google);
    }
    if (message.databricks !== undefined) {
      obj.databricks = GenerativeDatabricks.toJSON(message.databricks);
    }
    if (message.friendliai !== undefined) {
      obj.friendliai = GenerativeFriendliAI.toJSON(message.friendliai);
    }
    if (message.nvidia !== undefined) {
      obj.nvidia = GenerativeNvidia.toJSON(message.nvidia);
    }
    if (message.xai !== undefined) {
      obj.xai = GenerativeXAI.toJSON(message.xai);
    }
    return obj;
  },
  create(base) {
    return GenerativeProvider.fromPartial(base !== null && base !== void 0 ? base : {});
  },
  fromPartial(object) {
    var _a;
    const message = createBaseGenerativeProvider();
    message.returnMetadata = (_a = object.returnMetadata) !== null && _a !== void 0 ? _a : false;
    message.anthropic =
      object.anthropic !== undefined && object.anthropic !== null
        ? GenerativeAnthropic.fromPartial(object.anthropic)
        : undefined;
    message.anyscale =
      object.anyscale !== undefined && object.anyscale !== null
        ? GenerativeAnyscale.fromPartial(object.anyscale)
        : undefined;
    message.aws =
      object.aws !== undefined && object.aws !== null ? GenerativeAWS.fromPartial(object.aws) : undefined;
    message.cohere =
      object.cohere !== undefined && object.cohere !== null
        ? GenerativeCohere.fromPartial(object.cohere)
        : undefined;
    message.dummy =
      object.dummy !== undefined && object.dummy !== null
        ? GenerativeDummy.fromPartial(object.dummy)
        : undefined;
    message.mistral =
      object.mistral !== undefined && object.mistral !== null
        ? GenerativeMistral.fromPartial(object.mistral)
        : undefined;
    message.ollama =
      object.ollama !== undefined && object.ollama !== null
        ? GenerativeOllama.fromPartial(object.ollama)
        : undefined;
    message.openai =
      object.openai !== undefined && object.openai !== null
        ? GenerativeOpenAI.fromPartial(object.openai)
        : undefined;
    message.google =
      object.google !== undefined && object.google !== null
        ? GenerativeGoogle.fromPartial(object.google)
        : undefined;
    message.databricks =
      object.databricks !== undefined && object.databricks !== null
        ? GenerativeDatabricks.fromPartial(object.databricks)
        : undefined;
    message.friendliai =
      object.friendliai !== undefined && object.friendliai !== null
        ? GenerativeFriendliAI.fromPartial(object.friendliai)
        : undefined;
    message.nvidia =
      object.nvidia !== undefined && object.nvidia !== null
        ? GenerativeNvidia.fromPartial(object.nvidia)
        : undefined;
    message.xai =
      object.xai !== undefined && object.xai !== null ? GenerativeXAI.fromPartial(object.xai) : undefined;
    return message;
  },
};
function createBaseGenerativeAnthropic() {
  return {
    baseUrl: undefined,
    maxTokens: undefined,
    model: undefined,
    temperature: undefined,
    topK: undefined,
    topP: undefined,
    stopSequences: undefined,
    images: undefined,
    imageProperties: undefined,
  };
}
export const GenerativeAnthropic = {
  encode(message, writer = _m0.Writer.create()) {
    if (message.baseUrl !== undefined) {
      writer.uint32(10).string(message.baseUrl);
    }
    if (message.maxTokens !== undefined) {
      writer.uint32(16).int64(message.maxTokens);
    }
    if (message.model !== undefined) {
      writer.uint32(26).string(message.model);
    }
    if (message.temperature !== undefined) {
      writer.uint32(33).double(message.temperature);
    }
    if (message.topK !== undefined) {
      writer.uint32(40).int64(message.topK);
    }
    if (message.topP !== undefined) {
      writer.uint32(49).double(message.topP);
    }
    if (message.stopSequences !== undefined) {
      TextArray.encode(message.stopSequences, writer.uint32(58).fork()).ldelim();
    }
    if (message.images !== undefined) {
      TextArray.encode(message.images, writer.uint32(66).fork()).ldelim();
    }
    if (message.imageProperties !== undefined) {
      TextArray.encode(message.imageProperties, writer.uint32(74).fork()).ldelim();
    }
    return writer;
  },
  decode(input, length) {
    const reader = input instanceof _m0.Reader ? input : _m0.Reader.create(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseGenerativeAnthropic();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1:
          if (tag !== 10) {
            break;
          }
          message.baseUrl = reader.string();
          continue;
        case 2:
          if (tag !== 16) {
            break;
          }
          message.maxTokens = longToNumber(reader.int64());
          continue;
        case 3:
          if (tag !== 26) {
            break;
          }
          message.model = reader.string();
          continue;
        case 4:
          if (tag !== 33) {
            break;
          }
          message.temperature = reader.double();
          continue;
        case 5:
          if (tag !== 40) {
            break;
          }
          message.topK = longToNumber(reader.int64());
          continue;
        case 6:
          if (tag !== 49) {
            break;
          }
          message.topP = reader.double();
          continue;
        case 7:
          if (tag !== 58) {
            break;
          }
          message.stopSequences = TextArray.decode(reader, reader.uint32());
          continue;
        case 8:
          if (tag !== 66) {
            break;
          }
          message.images = TextArray.decode(reader, reader.uint32());
          continue;
        case 9:
          if (tag !== 74) {
            break;
          }
          message.imageProperties = TextArray.decode(reader, reader.uint32());
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skipType(tag & 7);
    }
    return message;
  },
  fromJSON(object) {
    return {
      baseUrl: isSet(object.baseUrl) ? globalThis.String(object.baseUrl) : undefined,
      maxTokens: isSet(object.maxTokens) ? globalThis.Number(object.maxTokens) : undefined,
      model: isSet(object.model) ? globalThis.String(object.model) : undefined,
      temperature: isSet(object.temperature) ? globalThis.Number(object.temperature) : undefined,
      topK: isSet(object.topK) ? globalThis.Number(object.topK) : undefined,
      topP: isSet(object.topP) ? globalThis.Number(object.topP) : undefined,
      stopSequences: isSet(object.stopSequences) ? TextArray.fromJSON(object.stopSequences) : undefined,
      images: isSet(object.images) ? TextArray.fromJSON(object.images) : undefined,
      imageProperties: isSet(object.imageProperties) ? TextArray.fromJSON(object.imageProperties) : undefined,
    };
  },
  toJSON(message) {
    const obj = {};
    if (message.baseUrl !== undefined) {
      obj.baseUrl = message.baseUrl;
    }
    if (message.maxTokens !== undefined) {
      obj.maxTokens = Math.round(message.maxTokens);
    }
    if (message.model !== undefined) {
      obj.model = message.model;
    }
    if (message.temperature !== undefined) {
      obj.temperature = message.temperature;
    }
    if (message.topK !== undefined) {
      obj.topK = Math.round(message.topK);
    }
    if (message.topP !== undefined) {
      obj.topP = message.topP;
    }
    if (message.stopSequences !== undefined) {
      obj.stopSequences = TextArray.toJSON(message.stopSequences);
    }
    if (message.images !== undefined) {
      obj.images = TextArray.toJSON(message.images);
    }
    if (message.imageProperties !== undefined) {
      obj.imageProperties = TextArray.toJSON(message.imageProperties);
    }
    return obj;
  },
  create(base) {
    return GenerativeAnthropic.fromPartial(base !== null && base !== void 0 ? base : {});
  },
  fromPartial(object) {
    var _a, _b, _c, _d, _e, _f;
    const message = createBaseGenerativeAnthropic();
    message.baseUrl = (_a = object.baseUrl) !== null && _a !== void 0 ? _a : undefined;
    message.maxTokens = (_b = object.maxTokens) !== null && _b !== void 0 ? _b : undefined;
    message.model = (_c = object.model) !== null && _c !== void 0 ? _c : undefined;
    message.temperature = (_d = object.temperature) !== null && _d !== void 0 ? _d : undefined;
    message.topK = (_e = object.topK) !== null && _e !== void 0 ? _e : undefined;
    message.topP = (_f = object.topP) !== null && _f !== void 0 ? _f : undefined;
    message.stopSequences =
      object.stopSequences !== undefined && object.stopSequences !== null
        ? TextArray.fromPartial(object.stopSequences)
        : undefined;
    message.images =
      object.images !== undefined && object.images !== null
        ? TextArray.fromPartial(object.images)
        : undefined;
    message.imageProperties =
      object.imageProperties !== undefined && object.imageProperties !== null
        ? TextArray.fromPartial(object.imageProperties)
        : undefined;
    return message;
  },
};
function createBaseGenerativeAnyscale() {
  return { baseUrl: undefined, model: undefined, temperature: undefined };
}
export const GenerativeAnyscale = {
  encode(message, writer = _m0.Writer.create()) {
    if (message.baseUrl !== undefined) {
      writer.uint32(10).string(message.baseUrl);
    }
    if (message.model !== undefined) {
      writer.uint32(18).string(message.model);
    }
    if (message.temperature !== undefined) {
      writer.uint32(25).double(message.temperature);
    }
    return writer;
  },
  decode(input, length) {
    const reader = input instanceof _m0.Reader ? input : _m0.Reader.create(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseGenerativeAnyscale();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1:
          if (tag !== 10) {
            break;
          }
          message.baseUrl = reader.string();
          continue;
        case 2:
          if (tag !== 18) {
            break;
          }
          message.model = reader.string();
          continue;
        case 3:
          if (tag !== 25) {
            break;
          }
          message.temperature = reader.double();
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skipType(tag & 7);
    }
    return message;
  },
  fromJSON(object) {
    return {
      baseUrl: isSet(object.baseUrl) ? globalThis.String(object.baseUrl) : undefined,
      model: isSet(object.model) ? globalThis.String(object.model) : undefined,
      temperature: isSet(object.temperature) ? globalThis.Number(object.temperature) : undefined,
    };
  },
  toJSON(message) {
    const obj = {};
    if (message.baseUrl !== undefined) {
      obj.baseUrl = message.baseUrl;
    }
    if (message.model !== undefined) {
      obj.model = message.model;
    }
    if (message.temperature !== undefined) {
      obj.temperature = message.temperature;
    }
    return obj;
  },
  create(base) {
    return GenerativeAnyscale.fromPartial(base !== null && base !== void 0 ? base : {});
  },
  fromPartial(object) {
    var _a, _b, _c;
    const message = createBaseGenerativeAnyscale();
    message.baseUrl = (_a = object.baseUrl) !== null && _a !== void 0 ? _a : undefined;
    message.model = (_b = object.model) !== null && _b !== void 0 ? _b : undefined;
    message.temperature = (_c = object.temperature) !== null && _c !== void 0 ? _c : undefined;
    return message;
  },
};
function createBaseGenerativeAWS() {
  return {
    model: undefined,
    temperature: undefined,
    service: undefined,
    region: undefined,
    endpoint: undefined,
    targetModel: undefined,
    targetVariant: undefined,
    images: undefined,
    imageProperties: undefined,
  };
}
export const GenerativeAWS = {
  encode(message, writer = _m0.Writer.create()) {
    if (message.model !== undefined) {
      writer.uint32(26).string(message.model);
    }
    if (message.temperature !== undefined) {
      writer.uint32(65).double(message.temperature);
    }
    if (message.service !== undefined) {
      writer.uint32(74).string(message.service);
    }
    if (message.region !== undefined) {
      writer.uint32(82).string(message.region);
    }
    if (message.endpoint !== undefined) {
      writer.uint32(90).string(message.endpoint);
    }
    if (message.targetModel !== undefined) {
      writer.uint32(98).string(message.targetModel);
    }
    if (message.targetVariant !== undefined) {
      writer.uint32(106).string(message.targetVariant);
    }
    if (message.images !== undefined) {
      TextArray.encode(message.images, writer.uint32(114).fork()).ldelim();
    }
    if (message.imageProperties !== undefined) {
      TextArray.encode(message.imageProperties, writer.uint32(122).fork()).ldelim();
    }
    return writer;
  },
  decode(input, length) {
    const reader = input instanceof _m0.Reader ? input : _m0.Reader.create(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseGenerativeAWS();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 3:
          if (tag !== 26) {
            break;
          }
          message.model = reader.string();
          continue;
        case 8:
          if (tag !== 65) {
            break;
          }
          message.temperature = reader.double();
          continue;
        case 9:
          if (tag !== 74) {
            break;
          }
          message.service = reader.string();
          continue;
        case 10:
          if (tag !== 82) {
            break;
          }
          message.region = reader.string();
          continue;
        case 11:
          if (tag !== 90) {
            break;
          }
          message.endpoint = reader.string();
          continue;
        case 12:
          if (tag !== 98) {
            break;
          }
          message.targetModel = reader.string();
          continue;
        case 13:
          if (tag !== 106) {
            break;
          }
          message.targetVariant = reader.string();
          continue;
        case 14:
          if (tag !== 114) {
            break;
          }
          message.images = TextArray.decode(reader, reader.uint32());
          continue;
        case 15:
          if (tag !== 122) {
            break;
          }
          message.imageProperties = TextArray.decode(reader, reader.uint32());
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skipType(tag & 7);
    }
    return message;
  },
  fromJSON(object) {
    return {
      model: isSet(object.model) ? globalThis.String(object.model) : undefined,
      temperature: isSet(object.temperature) ? globalThis.Number(object.temperature) : undefined,
      service: isSet(object.service) ? globalThis.String(object.service) : undefined,
      region: isSet(object.region) ? globalThis.String(object.region) : undefined,
      endpoint: isSet(object.endpoint) ? globalThis.String(object.endpoint) : undefined,
      targetModel: isSet(object.targetModel) ? globalThis.String(object.targetModel) : undefined,
      targetVariant: isSet(object.targetVariant) ? globalThis.String(object.targetVariant) : undefined,
      images: isSet(object.images) ? TextArray.fromJSON(object.images) : undefined,
      imageProperties: isSet(object.imageProperties) ? TextArray.fromJSON(object.imageProperties) : undefined,
    };
  },
  toJSON(message) {
    const obj = {};
    if (message.model !== undefined) {
      obj.model = message.model;
    }
    if (message.temperature !== undefined) {
      obj.temperature = message.temperature;
    }
    if (message.service !== undefined) {
      obj.service = message.service;
    }
    if (message.region !== undefined) {
      obj.region = message.region;
    }
    if (message.endpoint !== undefined) {
      obj.endpoint = message.endpoint;
    }
    if (message.targetModel !== undefined) {
      obj.targetModel = message.targetModel;
    }
    if (message.targetVariant !== undefined) {
      obj.targetVariant = message.targetVariant;
    }
    if (message.images !== undefined) {
      obj.images = TextArray.toJSON(message.images);
    }
    if (message.imageProperties !== undefined) {
      obj.imageProperties = TextArray.toJSON(message.imageProperties);
    }
    return obj;
  },
  create(base) {
    return GenerativeAWS.fromPartial(base !== null && base !== void 0 ? base : {});
  },
  fromPartial(object) {
    var _a, _b, _c, _d, _e, _f, _g;
    const message = createBaseGenerativeAWS();
    message.model = (_a = object.model) !== null && _a !== void 0 ? _a : undefined;
    message.temperature = (_b = object.temperature) !== null && _b !== void 0 ? _b : undefined;
    message.service = (_c = object.service) !== null && _c !== void 0 ? _c : undefined;
    message.region = (_d = object.region) !== null && _d !== void 0 ? _d : undefined;
    message.endpoint = (_e = object.endpoint) !== null && _e !== void 0 ? _e : undefined;
    message.targetModel = (_f = object.targetModel) !== null && _f !== void 0 ? _f : undefined;
    message.targetVariant = (_g = object.targetVariant) !== null && _g !== void 0 ? _g : undefined;
    message.images =
      object.images !== undefined && object.images !== null
        ? TextArray.fromPartial(object.images)
        : undefined;
    message.imageProperties =
      object.imageProperties !== undefined && object.imageProperties !== null
        ? TextArray.fromPartial(object.imageProperties)
        : undefined;
    return message;
  },
};
function createBaseGenerativeCohere() {
  return {
    baseUrl: undefined,
    frequencyPenalty: undefined,
    maxTokens: undefined,
    model: undefined,
    k: undefined,
    p: undefined,
    presencePenalty: undefined,
    stopSequences: undefined,
    temperature: undefined,
  };
}
export const GenerativeCohere = {
  encode(message, writer = _m0.Writer.create()) {
    if (message.baseUrl !== undefined) {
      writer.uint32(10).string(message.baseUrl);
    }
    if (message.frequencyPenalty !== undefined) {
      writer.uint32(17).double(message.frequencyPenalty);
    }
    if (message.maxTokens !== undefined) {
      writer.uint32(24).int64(message.maxTokens);
    }
    if (message.model !== undefined) {
      writer.uint32(34).string(message.model);
    }
    if (message.k !== undefined) {
      writer.uint32(40).int64(message.k);
    }
    if (message.p !== undefined) {
      writer.uint32(49).double(message.p);
    }
    if (message.presencePenalty !== undefined) {
      writer.uint32(57).double(message.presencePenalty);
    }
    if (message.stopSequences !== undefined) {
      TextArray.encode(message.stopSequences, writer.uint32(66).fork()).ldelim();
    }
    if (message.temperature !== undefined) {
      writer.uint32(73).double(message.temperature);
    }
    return writer;
  },
  decode(input, length) {
    const reader = input instanceof _m0.Reader ? input : _m0.Reader.create(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseGenerativeCohere();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1:
          if (tag !== 10) {
            break;
          }
          message.baseUrl = reader.string();
          continue;
        case 2:
          if (tag !== 17) {
            break;
          }
          message.frequencyPenalty = reader.double();
          continue;
        case 3:
          if (tag !== 24) {
            break;
          }
          message.maxTokens = longToNumber(reader.int64());
          continue;
        case 4:
          if (tag !== 34) {
            break;
          }
          message.model = reader.string();
          continue;
        case 5:
          if (tag !== 40) {
            break;
          }
          message.k = longToNumber(reader.int64());
          continue;
        case 6:
          if (tag !== 49) {
            break;
          }
          message.p = reader.double();
          continue;
        case 7:
          if (tag !== 57) {
            break;
          }
          message.presencePenalty = reader.double();
          continue;
        case 8:
          if (tag !== 66) {
            break;
          }
          message.stopSequences = TextArray.decode(reader, reader.uint32());
          continue;
        case 9:
          if (tag !== 73) {
            break;
          }
          message.temperature = reader.double();
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skipType(tag & 7);
    }
    return message;
  },
  fromJSON(object) {
    return {
      baseUrl: isSet(object.baseUrl) ? globalThis.String(object.baseUrl) : undefined,
      frequencyPenalty: isSet(object.frequencyPenalty)
        ? globalThis.Number(object.frequencyPenalty)
        : undefined,
      maxTokens: isSet(object.maxTokens) ? globalThis.Number(object.maxTokens) : undefined,
      model: isSet(object.model) ? globalThis.String(object.model) : undefined,
      k: isSet(object.k) ? globalThis.Number(object.k) : undefined,
      p: isSet(object.p) ? globalThis.Number(object.p) : undefined,
      presencePenalty: isSet(object.presencePenalty) ? globalThis.Number(object.presencePenalty) : undefined,
      stopSequences: isSet(object.stopSequences) ? TextArray.fromJSON(object.stopSequences) : undefined,
      temperature: isSet(object.temperature) ? globalThis.Number(object.temperature) : undefined,
    };
  },
  toJSON(message) {
    const obj = {};
    if (message.baseUrl !== undefined) {
      obj.baseUrl = message.baseUrl;
    }
    if (message.frequencyPenalty !== undefined) {
      obj.frequencyPenalty = message.frequencyPenalty;
    }
    if (message.maxTokens !== undefined) {
      obj.maxTokens = Math.round(message.maxTokens);
    }
    if (message.model !== undefined) {
      obj.model = message.model;
    }
    if (message.k !== undefined) {
      obj.k = Math.round(message.k);
    }
    if (message.p !== undefined) {
      obj.p = message.p;
    }
    if (message.presencePenalty !== undefined) {
      obj.presencePenalty = message.presencePenalty;
    }
    if (message.stopSequences !== undefined) {
      obj.stopSequences = TextArray.toJSON(message.stopSequences);
    }
    if (message.temperature !== undefined) {
      obj.temperature = message.temperature;
    }
    return obj;
  },
  create(base) {
    return GenerativeCohere.fromPartial(base !== null && base !== void 0 ? base : {});
  },
  fromPartial(object) {
    var _a, _b, _c, _d, _e, _f, _g, _h;
    const message = createBaseGenerativeCohere();
    message.baseUrl = (_a = object.baseUrl) !== null && _a !== void 0 ? _a : undefined;
    message.frequencyPenalty = (_b = object.frequencyPenalty) !== null && _b !== void 0 ? _b : undefined;
    message.maxTokens = (_c = object.maxTokens) !== null && _c !== void 0 ? _c : undefined;
    message.model = (_d = object.model) !== null && _d !== void 0 ? _d : undefined;
    message.k = (_e = object.k) !== null && _e !== void 0 ? _e : undefined;
    message.p = (_f = object.p) !== null && _f !== void 0 ? _f : undefined;
    message.presencePenalty = (_g = object.presencePenalty) !== null && _g !== void 0 ? _g : undefined;
    message.stopSequences =
      object.stopSequences !== undefined && object.stopSequences !== null
        ? TextArray.fromPartial(object.stopSequences)
        : undefined;
    message.temperature = (_h = object.temperature) !== null && _h !== void 0 ? _h : undefined;
    return message;
  },
};
function createBaseGenerativeDummy() {
  return {};
}
export const GenerativeDummy = {
  encode(_, writer = _m0.Writer.create()) {
    return writer;
  },
  decode(input, length) {
    const reader = input instanceof _m0.Reader ? input : _m0.Reader.create(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseGenerativeDummy();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skipType(tag & 7);
    }
    return message;
  },
  fromJSON(_) {
    return {};
  },
  toJSON(_) {
    const obj = {};
    return obj;
  },
  create(base) {
    return GenerativeDummy.fromPartial(base !== null && base !== void 0 ? base : {});
  },
  fromPartial(_) {
    const message = createBaseGenerativeDummy();
    return message;
  },
};
function createBaseGenerativeMistral() {
  return {
    baseUrl: undefined,
    maxTokens: undefined,
    model: undefined,
    temperature: undefined,
    topP: undefined,
  };
}
export const GenerativeMistral = {
  encode(message, writer = _m0.Writer.create()) {
    if (message.baseUrl !== undefined) {
      writer.uint32(10).string(message.baseUrl);
    }
    if (message.maxTokens !== undefined) {
      writer.uint32(16).int64(message.maxTokens);
    }
    if (message.model !== undefined) {
      writer.uint32(26).string(message.model);
    }
    if (message.temperature !== undefined) {
      writer.uint32(33).double(message.temperature);
    }
    if (message.topP !== undefined) {
      writer.uint32(41).double(message.topP);
    }
    return writer;
  },
  decode(input, length) {
    const reader = input instanceof _m0.Reader ? input : _m0.Reader.create(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseGenerativeMistral();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1:
          if (tag !== 10) {
            break;
          }
          message.baseUrl = reader.string();
          continue;
        case 2:
          if (tag !== 16) {
            break;
          }
          message.maxTokens = longToNumber(reader.int64());
          continue;
        case 3:
          if (tag !== 26) {
            break;
          }
          message.model = reader.string();
          continue;
        case 4:
          if (tag !== 33) {
            break;
          }
          message.temperature = reader.double();
          continue;
        case 5:
          if (tag !== 41) {
            break;
          }
          message.topP = reader.double();
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skipType(tag & 7);
    }
    return message;
  },
  fromJSON(object) {
    return {
      baseUrl: isSet(object.baseUrl) ? globalThis.String(object.baseUrl) : undefined,
      maxTokens: isSet(object.maxTokens) ? globalThis.Number(object.maxTokens) : undefined,
      model: isSet(object.model) ? globalThis.String(object.model) : undefined,
      temperature: isSet(object.temperature) ? globalThis.Number(object.temperature) : undefined,
      topP: isSet(object.topP) ? globalThis.Number(object.topP) : undefined,
    };
  },
  toJSON(message) {
    const obj = {};
    if (message.baseUrl !== undefined) {
      obj.baseUrl = message.baseUrl;
    }
    if (message.maxTokens !== undefined) {
      obj.maxTokens = Math.round(message.maxTokens);
    }
    if (message.model !== undefined) {
      obj.model = message.model;
    }
    if (message.temperature !== undefined) {
      obj.temperature = message.temperature;
    }
    if (message.topP !== undefined) {
      obj.topP = message.topP;
    }
    return obj;
  },
  create(base) {
    return GenerativeMistral.fromPartial(base !== null && base !== void 0 ? base : {});
  },
  fromPartial(object) {
    var _a, _b, _c, _d, _e;
    const message = createBaseGenerativeMistral();
    message.baseUrl = (_a = object.baseUrl) !== null && _a !== void 0 ? _a : undefined;
    message.maxTokens = (_b = object.maxTokens) !== null && _b !== void 0 ? _b : undefined;
    message.model = (_c = object.model) !== null && _c !== void 0 ? _c : undefined;
    message.temperature = (_d = object.temperature) !== null && _d !== void 0 ? _d : undefined;
    message.topP = (_e = object.topP) !== null && _e !== void 0 ? _e : undefined;
    return message;
  },
};
function createBaseGenerativeOllama() {
  return {
    apiEndpoint: undefined,
    model: undefined,
    temperature: undefined,
    images: undefined,
    imageProperties: undefined,
  };
}
export const GenerativeOllama = {
  encode(message, writer = _m0.Writer.create()) {
    if (message.apiEndpoint !== undefined) {
      writer.uint32(10).string(message.apiEndpoint);
    }
    if (message.model !== undefined) {
      writer.uint32(18).string(message.model);
    }
    if (message.temperature !== undefined) {
      writer.uint32(25).double(message.temperature);
    }
    if (message.images !== undefined) {
      TextArray.encode(message.images, writer.uint32(34).fork()).ldelim();
    }
    if (message.imageProperties !== undefined) {
      TextArray.encode(message.imageProperties, writer.uint32(42).fork()).ldelim();
    }
    return writer;
  },
  decode(input, length) {
    const reader = input instanceof _m0.Reader ? input : _m0.Reader.create(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseGenerativeOllama();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1:
          if (tag !== 10) {
            break;
          }
          message.apiEndpoint = reader.string();
          continue;
        case 2:
          if (tag !== 18) {
            break;
          }
          message.model = reader.string();
          continue;
        case 3:
          if (tag !== 25) {
            break;
          }
          message.temperature = reader.double();
          continue;
        case 4:
          if (tag !== 34) {
            break;
          }
          message.images = TextArray.decode(reader, reader.uint32());
          continue;
        case 5:
          if (tag !== 42) {
            break;
          }
          message.imageProperties = TextArray.decode(reader, reader.uint32());
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skipType(tag & 7);
    }
    return message;
  },
  fromJSON(object) {
    return {
      apiEndpoint: isSet(object.apiEndpoint) ? globalThis.String(object.apiEndpoint) : undefined,
      model: isSet(object.model) ? globalThis.String(object.model) : undefined,
      temperature: isSet(object.temperature) ? globalThis.Number(object.temperature) : undefined,
      images: isSet(object.images) ? TextArray.fromJSON(object.images) : undefined,
      imageProperties: isSet(object.imageProperties) ? TextArray.fromJSON(object.imageProperties) : undefined,
    };
  },
  toJSON(message) {
    const obj = {};
    if (message.apiEndpoint !== undefined) {
      obj.apiEndpoint = message.apiEndpoint;
    }
    if (message.model !== undefined) {
      obj.model = message.model;
    }
    if (message.temperature !== undefined) {
      obj.temperature = message.temperature;
    }
    if (message.images !== undefined) {
      obj.images = TextArray.toJSON(message.images);
    }
    if (message.imageProperties !== undefined) {
      obj.imageProperties = TextArray.toJSON(message.imageProperties);
    }
    return obj;
  },
  create(base) {
    return GenerativeOllama.fromPartial(base !== null && base !== void 0 ? base : {});
  },
  fromPartial(object) {
    var _a, _b, _c;
    const message = createBaseGenerativeOllama();
    message.apiEndpoint = (_a = object.apiEndpoint) !== null && _a !== void 0 ? _a : undefined;
    message.model = (_b = object.model) !== null && _b !== void 0 ? _b : undefined;
    message.temperature = (_c = object.temperature) !== null && _c !== void 0 ? _c : undefined;
    message.images =
      object.images !== undefined && object.images !== null
        ? TextArray.fromPartial(object.images)
        : undefined;
    message.imageProperties =
      object.imageProperties !== undefined && object.imageProperties !== null
        ? TextArray.fromPartial(object.imageProperties)
        : undefined;
    return message;
  },
};
function createBaseGenerativeOpenAI() {
  return {
    frequencyPenalty: undefined,
    maxTokens: undefined,
    model: undefined,
    n: undefined,
    presencePenalty: undefined,
    stop: undefined,
    temperature: undefined,
    topP: undefined,
    baseUrl: undefined,
    apiVersion: undefined,
    resourceName: undefined,
    deploymentId: undefined,
    isAzure: undefined,
    images: undefined,
    imageProperties: undefined,
  };
}
export const GenerativeOpenAI = {
  encode(message, writer = _m0.Writer.create()) {
    if (message.frequencyPenalty !== undefined) {
      writer.uint32(9).double(message.frequencyPenalty);
    }
    if (message.maxTokens !== undefined) {
      writer.uint32(16).int64(message.maxTokens);
    }
    if (message.model !== undefined) {
      writer.uint32(26).string(message.model);
    }
    if (message.n !== undefined) {
      writer.uint32(32).int64(message.n);
    }
    if (message.presencePenalty !== undefined) {
      writer.uint32(41).double(message.presencePenalty);
    }
    if (message.stop !== undefined) {
      TextArray.encode(message.stop, writer.uint32(50).fork()).ldelim();
    }
    if (message.temperature !== undefined) {
      writer.uint32(57).double(message.temperature);
    }
    if (message.topP !== undefined) {
      writer.uint32(65).double(message.topP);
    }
    if (message.baseUrl !== undefined) {
      writer.uint32(74).string(message.baseUrl);
    }
    if (message.apiVersion !== undefined) {
      writer.uint32(82).string(message.apiVersion);
    }
    if (message.resourceName !== undefined) {
      writer.uint32(90).string(message.resourceName);
    }
    if (message.deploymentId !== undefined) {
      writer.uint32(98).string(message.deploymentId);
    }
    if (message.isAzure !== undefined) {
      writer.uint32(104).bool(message.isAzure);
    }
    if (message.images !== undefined) {
      TextArray.encode(message.images, writer.uint32(114).fork()).ldelim();
    }
    if (message.imageProperties !== undefined) {
      TextArray.encode(message.imageProperties, writer.uint32(122).fork()).ldelim();
    }
    return writer;
  },
  decode(input, length) {
    const reader = input instanceof _m0.Reader ? input : _m0.Reader.create(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseGenerativeOpenAI();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1:
          if (tag !== 9) {
            break;
          }
          message.frequencyPenalty = reader.double();
          continue;
        case 2:
          if (tag !== 16) {
            break;
          }
          message.maxTokens = longToNumber(reader.int64());
          continue;
        case 3:
          if (tag !== 26) {
            break;
          }
          message.model = reader.string();
          continue;
        case 4:
          if (tag !== 32) {
            break;
          }
          message.n = longToNumber(reader.int64());
          continue;
        case 5:
          if (tag !== 41) {
            break;
          }
          message.presencePenalty = reader.double();
          continue;
        case 6:
          if (tag !== 50) {
            break;
          }
          message.stop = TextArray.decode(reader, reader.uint32());
          continue;
        case 7:
          if (tag !== 57) {
            break;
          }
          message.temperature = reader.double();
          continue;
        case 8:
          if (tag !== 65) {
            break;
          }
          message.topP = reader.double();
          continue;
        case 9:
          if (tag !== 74) {
            break;
          }
          message.baseUrl = reader.string();
          continue;
        case 10:
          if (tag !== 82) {
            break;
          }
          message.apiVersion = reader.string();
          continue;
        case 11:
          if (tag !== 90) {
            break;
          }
          message.resourceName = reader.string();
          continue;
        case 12:
          if (tag !== 98) {
            break;
          }
          message.deploymentId = reader.string();
          continue;
        case 13:
          if (tag !== 104) {
            break;
          }
          message.isAzure = reader.bool();
          continue;
        case 14:
          if (tag !== 114) {
            break;
          }
          message.images = TextArray.decode(reader, reader.uint32());
          continue;
        case 15:
          if (tag !== 122) {
            break;
          }
          message.imageProperties = TextArray.decode(reader, reader.uint32());
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skipType(tag & 7);
    }
    return message;
  },
  fromJSON(object) {
    return {
      frequencyPenalty: isSet(object.frequencyPenalty)
        ? globalThis.Number(object.frequencyPenalty)
        : undefined,
      maxTokens: isSet(object.maxTokens) ? globalThis.Number(object.maxTokens) : undefined,
      model: isSet(object.model) ? globalThis.String(object.model) : undefined,
      n: isSet(object.n) ? globalThis.Number(object.n) : undefined,
      presencePenalty: isSet(object.presencePenalty) ? globalThis.Number(object.presencePenalty) : undefined,
      stop: isSet(object.stop) ? TextArray.fromJSON(object.stop) : undefined,
      temperature: isSet(object.temperature) ? globalThis.Number(object.temperature) : undefined,
      topP: isSet(object.topP) ? globalThis.Number(object.topP) : undefined,
      baseUrl: isSet(object.baseUrl) ? globalThis.String(object.baseUrl) : undefined,
      apiVersion: isSet(object.apiVersion) ? globalThis.String(object.apiVersion) : undefined,
      resourceName: isSet(object.resourceName) ? globalThis.String(object.resourceName) : undefined,
      deploymentId: isSet(object.deploymentId) ? globalThis.String(object.deploymentId) : undefined,
      isAzure: isSet(object.isAzure) ? globalThis.Boolean(object.isAzure) : undefined,
      images: isSet(object.images) ? TextArray.fromJSON(object.images) : undefined,
      imageProperties: isSet(object.imageProperties) ? TextArray.fromJSON(object.imageProperties) : undefined,
    };
  },
  toJSON(message) {
    const obj = {};
    if (message.frequencyPenalty !== undefined) {
      obj.frequencyPenalty = message.frequencyPenalty;
    }
    if (message.maxTokens !== undefined) {
      obj.maxTokens = Math.round(message.maxTokens);
    }
    if (message.model !== undefined) {
      obj.model = message.model;
    }
    if (message.n !== undefined) {
      obj.n = Math.round(message.n);
    }
    if (message.presencePenalty !== undefined) {
      obj.presencePenalty = message.presencePenalty;
    }
    if (message.stop !== undefined) {
      obj.stop = TextArray.toJSON(message.stop);
    }
    if (message.temperature !== undefined) {
      obj.temperature = message.temperature;
    }
    if (message.topP !== undefined) {
      obj.topP = message.topP;
    }
    if (message.baseUrl !== undefined) {
      obj.baseUrl = message.baseUrl;
    }
    if (message.apiVersion !== undefined) {
      obj.apiVersion = message.apiVersion;
    }
    if (message.resourceName !== undefined) {
      obj.resourceName = message.resourceName;
    }
    if (message.deploymentId !== undefined) {
      obj.deploymentId = message.deploymentId;
    }
    if (message.isAzure !== undefined) {
      obj.isAzure = message.isAzure;
    }
    if (message.images !== undefined) {
      obj.images = TextArray.toJSON(message.images);
    }
    if (message.imageProperties !== undefined) {
      obj.imageProperties = TextArray.toJSON(message.imageProperties);
    }
    return obj;
  },
  create(base) {
    return GenerativeOpenAI.fromPartial(base !== null && base !== void 0 ? base : {});
  },
  fromPartial(object) {
    var _a, _b, _c, _d, _e, _f, _g, _h, _j, _k, _l, _m;
    const message = createBaseGenerativeOpenAI();
    message.frequencyPenalty = (_a = object.frequencyPenalty) !== null && _a !== void 0 ? _a : undefined;
    message.maxTokens = (_b = object.maxTokens) !== null && _b !== void 0 ? _b : undefined;
    message.model = (_c = object.model) !== null && _c !== void 0 ? _c : undefined;
    message.n = (_d = object.n) !== null && _d !== void 0 ? _d : undefined;
    message.presencePenalty = (_e = object.presencePenalty) !== null && _e !== void 0 ? _e : undefined;
    message.stop =
      object.stop !== undefined && object.stop !== null ? TextArray.fromPartial(object.stop) : undefined;
    message.temperature = (_f = object.temperature) !== null && _f !== void 0 ? _f : undefined;
    message.topP = (_g = object.topP) !== null && _g !== void 0 ? _g : undefined;
    message.baseUrl = (_h = object.baseUrl) !== null && _h !== void 0 ? _h : undefined;
    message.apiVersion = (_j = object.apiVersion) !== null && _j !== void 0 ? _j : undefined;
    message.resourceName = (_k = object.resourceName) !== null && _k !== void 0 ? _k : undefined;
    message.deploymentId = (_l = object.deploymentId) !== null && _l !== void 0 ? _l : undefined;
    message.isAzure = (_m = object.isAzure) !== null && _m !== void 0 ? _m : undefined;
    message.images =
      object.images !== undefined && object.images !== null
        ? TextArray.fromPartial(object.images)
        : undefined;
    message.imageProperties =
      object.imageProperties !== undefined && object.imageProperties !== null
        ? TextArray.fromPartial(object.imageProperties)
        : undefined;
    return message;
  },
};
function createBaseGenerativeGoogle() {
  return {
    frequencyPenalty: undefined,
    maxTokens: undefined,
    model: undefined,
    presencePenalty: undefined,
    temperature: undefined,
    topK: undefined,
    topP: undefined,
    stopSequences: undefined,
    apiEndpoint: undefined,
    projectId: undefined,
    endpointId: undefined,
    region: undefined,
    images: undefined,
    imageProperties: undefined,
  };
}
export const GenerativeGoogle = {
  encode(message, writer = _m0.Writer.create()) {
    if (message.frequencyPenalty !== undefined) {
      writer.uint32(9).double(message.frequencyPenalty);
    }
    if (message.maxTokens !== undefined) {
      writer.uint32(16).int64(message.maxTokens);
    }
    if (message.model !== undefined) {
      writer.uint32(26).string(message.model);
    }
    if (message.presencePenalty !== undefined) {
      writer.uint32(33).double(message.presencePenalty);
    }
    if (message.temperature !== undefined) {
      writer.uint32(41).double(message.temperature);
    }
    if (message.topK !== undefined) {
      writer.uint32(48).int64(message.topK);
    }
    if (message.topP !== undefined) {
      writer.uint32(57).double(message.topP);
    }
    if (message.stopSequences !== undefined) {
      TextArray.encode(message.stopSequences, writer.uint32(66).fork()).ldelim();
    }
    if (message.apiEndpoint !== undefined) {
      writer.uint32(74).string(message.apiEndpoint);
    }
    if (message.projectId !== undefined) {
      writer.uint32(82).string(message.projectId);
    }
    if (message.endpointId !== undefined) {
      writer.uint32(90).string(message.endpointId);
    }
    if (message.region !== undefined) {
      writer.uint32(98).string(message.region);
    }
    if (message.images !== undefined) {
      TextArray.encode(message.images, writer.uint32(106).fork()).ldelim();
    }
    if (message.imageProperties !== undefined) {
      TextArray.encode(message.imageProperties, writer.uint32(114).fork()).ldelim();
    }
    return writer;
  },
  decode(input, length) {
    const reader = input instanceof _m0.Reader ? input : _m0.Reader.create(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseGenerativeGoogle();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1:
          if (tag !== 9) {
            break;
          }
          message.frequencyPenalty = reader.double();
          continue;
        case 2:
          if (tag !== 16) {
            break;
          }
          message.maxTokens = longToNumber(reader.int64());
          continue;
        case 3:
          if (tag !== 26) {
            break;
          }
          message.model = reader.string();
          continue;
        case 4:
          if (tag !== 33) {
            break;
          }
          message.presencePenalty = reader.double();
          continue;
        case 5:
          if (tag !== 41) {
            break;
          }
          message.temperature = reader.double();
          continue;
        case 6:
          if (tag !== 48) {
            break;
          }
          message.topK = longToNumber(reader.int64());
          continue;
        case 7:
          if (tag !== 57) {
            break;
          }
          message.topP = reader.double();
          continue;
        case 8:
          if (tag !== 66) {
            break;
          }
          message.stopSequences = TextArray.decode(reader, reader.uint32());
          continue;
        case 9:
          if (tag !== 74) {
            break;
          }
          message.apiEndpoint = reader.string();
          continue;
        case 10:
          if (tag !== 82) {
            break;
          }
          message.projectId = reader.string();
          continue;
        case 11:
          if (tag !== 90) {
            break;
          }
          message.endpointId = reader.string();
          continue;
        case 12:
          if (tag !== 98) {
            break;
          }
          message.region = reader.string();
          continue;
        case 13:
          if (tag !== 106) {
            break;
          }
          message.images = TextArray.decode(reader, reader.uint32());
          continue;
        case 14:
          if (tag !== 114) {
            break;
          }
          message.imageProperties = TextArray.decode(reader, reader.uint32());
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skipType(tag & 7);
    }
    return message;
  },
  fromJSON(object) {
    return {
      frequencyPenalty: isSet(object.frequencyPenalty)
        ? globalThis.Number(object.frequencyPenalty)
        : undefined,
      maxTokens: isSet(object.maxTokens) ? globalThis.Number(object.maxTokens) : undefined,
      model: isSet(object.model) ? globalThis.String(object.model) : undefined,
      presencePenalty: isSet(object.presencePenalty) ? globalThis.Number(object.presencePenalty) : undefined,
      temperature: isSet(object.temperature) ? globalThis.Number(object.temperature) : undefined,
      topK: isSet(object.topK) ? globalThis.Number(object.topK) : undefined,
      topP: isSet(object.topP) ? globalThis.Number(object.topP) : undefined,
      stopSequences: isSet(object.stopSequences) ? TextArray.fromJSON(object.stopSequences) : undefined,
      apiEndpoint: isSet(object.apiEndpoint) ? globalThis.String(object.apiEndpoint) : undefined,
      projectId: isSet(object.projectId) ? globalThis.String(object.projectId) : undefined,
      endpointId: isSet(object.endpointId) ? globalThis.String(object.endpointId) : undefined,
      region: isSet(object.region) ? globalThis.String(object.region) : undefined,
      images: isSet(object.images) ? TextArray.fromJSON(object.images) : undefined,
      imageProperties: isSet(object.imageProperties) ? TextArray.fromJSON(object.imageProperties) : undefined,
    };
  },
  toJSON(message) {
    const obj = {};
    if (message.frequencyPenalty !== undefined) {
      obj.frequencyPenalty = message.frequencyPenalty;
    }
    if (message.maxTokens !== undefined) {
      obj.maxTokens = Math.round(message.maxTokens);
    }
    if (message.model !== undefined) {
      obj.model = message.model;
    }
    if (message.presencePenalty !== undefined) {
      obj.presencePenalty = message.presencePenalty;
    }
    if (message.temperature !== undefined) {
      obj.temperature = message.temperature;
    }
    if (message.topK !== undefined) {
      obj.topK = Math.round(message.topK);
    }
    if (message.topP !== undefined) {
      obj.topP = message.topP;
    }
    if (message.stopSequences !== undefined) {
      obj.stopSequences = TextArray.toJSON(message.stopSequences);
    }
    if (message.apiEndpoint !== undefined) {
      obj.apiEndpoint = message.apiEndpoint;
    }
    if (message.projectId !== undefined) {
      obj.projectId = message.projectId;
    }
    if (message.endpointId !== undefined) {
      obj.endpointId = message.endpointId;
    }
    if (message.region !== undefined) {
      obj.region = message.region;
    }
    if (message.images !== undefined) {
      obj.images = TextArray.toJSON(message.images);
    }
    if (message.imageProperties !== undefined) {
      obj.imageProperties = TextArray.toJSON(message.imageProperties);
    }
    return obj;
  },
  create(base) {
    return GenerativeGoogle.fromPartial(base !== null && base !== void 0 ? base : {});
  },
  fromPartial(object) {
    var _a, _b, _c, _d, _e, _f, _g, _h, _j, _k, _l;
    const message = createBaseGenerativeGoogle();
    message.frequencyPenalty = (_a = object.frequencyPenalty) !== null && _a !== void 0 ? _a : undefined;
    message.maxTokens = (_b = object.maxTokens) !== null && _b !== void 0 ? _b : undefined;
    message.model = (_c = object.model) !== null && _c !== void 0 ? _c : undefined;
    message.presencePenalty = (_d = object.presencePenalty) !== null && _d !== void 0 ? _d : undefined;
    message.temperature = (_e = object.temperature) !== null && _e !== void 0 ? _e : undefined;
    message.topK = (_f = object.topK) !== null && _f !== void 0 ? _f : undefined;
    message.topP = (_g = object.topP) !== null && _g !== void 0 ? _g : undefined;
    message.stopSequences =
      object.stopSequences !== undefined && object.stopSequences !== null
        ? TextArray.fromPartial(object.stopSequences)
        : undefined;
    message.apiEndpoint = (_h = object.apiEndpoint) !== null && _h !== void 0 ? _h : undefined;
    message.projectId = (_j = object.projectId) !== null && _j !== void 0 ? _j : undefined;
    message.endpointId = (_k = object.endpointId) !== null && _k !== void 0 ? _k : undefined;
    message.region = (_l = object.region) !== null && _l !== void 0 ? _l : undefined;
    message.images =
      object.images !== undefined && object.images !== null
        ? TextArray.fromPartial(object.images)
        : undefined;
    message.imageProperties =
      object.imageProperties !== undefined && object.imageProperties !== null
        ? TextArray.fromPartial(object.imageProperties)
        : undefined;
    return message;
  },
};
function createBaseGenerativeDatabricks() {
  return {
    endpoint: undefined,
    model: undefined,
    frequencyPenalty: undefined,
    logProbs: undefined,
    topLogProbs: undefined,
    maxTokens: undefined,
    n: undefined,
    presencePenalty: undefined,
    stop: undefined,
    temperature: undefined,
    topP: undefined,
  };
}
export const GenerativeDatabricks = {
  encode(message, writer = _m0.Writer.create()) {
    if (message.endpoint !== undefined) {
      writer.uint32(10).string(message.endpoint);
    }
    if (message.model !== undefined) {
      writer.uint32(18).string(message.model);
    }
    if (message.frequencyPenalty !== undefined) {
      writer.uint32(25).double(message.frequencyPenalty);
    }
    if (message.logProbs !== undefined) {
      writer.uint32(32).bool(message.logProbs);
    }
    if (message.topLogProbs !== undefined) {
      writer.uint32(40).int64(message.topLogProbs);
    }
    if (message.maxTokens !== undefined) {
      writer.uint32(48).int64(message.maxTokens);
    }
    if (message.n !== undefined) {
      writer.uint32(56).int64(message.n);
    }
    if (message.presencePenalty !== undefined) {
      writer.uint32(65).double(message.presencePenalty);
    }
    if (message.stop !== undefined) {
      TextArray.encode(message.stop, writer.uint32(74).fork()).ldelim();
    }
    if (message.temperature !== undefined) {
      writer.uint32(81).double(message.temperature);
    }
    if (message.topP !== undefined) {
      writer.uint32(89).double(message.topP);
    }
    return writer;
  },
  decode(input, length) {
    const reader = input instanceof _m0.Reader ? input : _m0.Reader.create(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseGenerativeDatabricks();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1:
          if (tag !== 10) {
            break;
          }
          message.endpoint = reader.string();
          continue;
        case 2:
          if (tag !== 18) {
            break;
          }
          message.model = reader.string();
          continue;
        case 3:
          if (tag !== 25) {
            break;
          }
          message.frequencyPenalty = reader.double();
          continue;
        case 4:
          if (tag !== 32) {
            break;
          }
          message.logProbs = reader.bool();
          continue;
        case 5:
          if (tag !== 40) {
            break;
          }
          message.topLogProbs = longToNumber(reader.int64());
          continue;
        case 6:
          if (tag !== 48) {
            break;
          }
          message.maxTokens = longToNumber(reader.int64());
          continue;
        case 7:
          if (tag !== 56) {
            break;
          }
          message.n = longToNumber(reader.int64());
          continue;
        case 8:
          if (tag !== 65) {
            break;
          }
          message.presencePenalty = reader.double();
          continue;
        case 9:
          if (tag !== 74) {
            break;
          }
          message.stop = TextArray.decode(reader, reader.uint32());
          continue;
        case 10:
          if (tag !== 81) {
            break;
          }
          message.temperature = reader.double();
          continue;
        case 11:
          if (tag !== 89) {
            break;
          }
          message.topP = reader.double();
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skipType(tag & 7);
    }
    return message;
  },
  fromJSON(object) {
    return {
      endpoint: isSet(object.endpoint) ? globalThis.String(object.endpoint) : undefined,
      model: isSet(object.model) ? globalThis.String(object.model) : undefined,
      frequencyPenalty: isSet(object.frequencyPenalty)
        ? globalThis.Number(object.frequencyPenalty)
        : undefined,
      logProbs: isSet(object.logProbs) ? globalThis.Boolean(object.logProbs) : undefined,
      topLogProbs: isSet(object.topLogProbs) ? globalThis.Number(object.topLogProbs) : undefined,
      maxTokens: isSet(object.maxTokens) ? globalThis.Number(object.maxTokens) : undefined,
      n: isSet(object.n) ? globalThis.Number(object.n) : undefined,
      presencePenalty: isSet(object.presencePenalty) ? globalThis.Number(object.presencePenalty) : undefined,
      stop: isSet(object.stop) ? TextArray.fromJSON(object.stop) : undefined,
      temperature: isSet(object.temperature) ? globalThis.Number(object.temperature) : undefined,
      topP: isSet(object.topP) ? globalThis.Number(object.topP) : undefined,
    };
  },
  toJSON(message) {
    const obj = {};
    if (message.endpoint !== undefined) {
      obj.endpoint = message.endpoint;
    }
    if (message.model !== undefined) {
      obj.model = message.model;
    }
    if (message.frequencyPenalty !== undefined) {
      obj.frequencyPenalty = message.frequencyPenalty;
    }
    if (message.logProbs !== undefined) {
      obj.logProbs = message.logProbs;
    }
    if (message.topLogProbs !== undefined) {
      obj.topLogProbs = Math.round(message.topLogProbs);
    }
    if (message.maxTokens !== undefined) {
      obj.maxTokens = Math.round(message.maxTokens);
    }
    if (message.n !== undefined) {
      obj.n = Math.round(message.n);
    }
    if (message.presencePenalty !== undefined) {
      obj.presencePenalty = message.presencePenalty;
    }
    if (message.stop !== undefined) {
      obj.stop = TextArray.toJSON(message.stop);
    }
    if (message.temperature !== undefined) {
      obj.temperature = message.temperature;
    }
    if (message.topP !== undefined) {
      obj.topP = message.topP;
    }
    return obj;
  },
  create(base) {
    return GenerativeDatabricks.fromPartial(base !== null && base !== void 0 ? base : {});
  },
  fromPartial(object) {
    var _a, _b, _c, _d, _e, _f, _g, _h, _j, _k;
    const message = createBaseGenerativeDatabricks();
    message.endpoint = (_a = object.endpoint) !== null && _a !== void 0 ? _a : undefined;
    message.model = (_b = object.model) !== null && _b !== void 0 ? _b : undefined;
    message.frequencyPenalty = (_c = object.frequencyPenalty) !== null && _c !== void 0 ? _c : undefined;
    message.logProbs = (_d = object.logProbs) !== null && _d !== void 0 ? _d : undefined;
    message.topLogProbs = (_e = object.topLogProbs) !== null && _e !== void 0 ? _e : undefined;
    message.maxTokens = (_f = object.maxTokens) !== null && _f !== void 0 ? _f : undefined;
    message.n = (_g = object.n) !== null && _g !== void 0 ? _g : undefined;
    message.presencePenalty = (_h = object.presencePenalty) !== null && _h !== void 0 ? _h : undefined;
    message.stop =
      object.stop !== undefined && object.stop !== null ? TextArray.fromPartial(object.stop) : undefined;
    message.temperature = (_j = object.temperature) !== null && _j !== void 0 ? _j : undefined;
    message.topP = (_k = object.topP) !== null && _k !== void 0 ? _k : undefined;
    return message;
  },
};
function createBaseGenerativeFriendliAI() {
  return {
    baseUrl: undefined,
    model: undefined,
    maxTokens: undefined,
    temperature: undefined,
    n: undefined,
    topP: undefined,
  };
}
export const GenerativeFriendliAI = {
  encode(message, writer = _m0.Writer.create()) {
    if (message.baseUrl !== undefined) {
      writer.uint32(10).string(message.baseUrl);
    }
    if (message.model !== undefined) {
      writer.uint32(18).string(message.model);
    }
    if (message.maxTokens !== undefined) {
      writer.uint32(24).int64(message.maxTokens);
    }
    if (message.temperature !== undefined) {
      writer.uint32(33).double(message.temperature);
    }
    if (message.n !== undefined) {
      writer.uint32(40).int64(message.n);
    }
    if (message.topP !== undefined) {
      writer.uint32(49).double(message.topP);
    }
    return writer;
  },
  decode(input, length) {
    const reader = input instanceof _m0.Reader ? input : _m0.Reader.create(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseGenerativeFriendliAI();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1:
          if (tag !== 10) {
            break;
          }
          message.baseUrl = reader.string();
          continue;
        case 2:
          if (tag !== 18) {
            break;
          }
          message.model = reader.string();
          continue;
        case 3:
          if (tag !== 24) {
            break;
          }
          message.maxTokens = longToNumber(reader.int64());
          continue;
        case 4:
          if (tag !== 33) {
            break;
          }
          message.temperature = reader.double();
          continue;
        case 5:
          if (tag !== 40) {
            break;
          }
          message.n = longToNumber(reader.int64());
          continue;
        case 6:
          if (tag !== 49) {
            break;
          }
          message.topP = reader.double();
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skipType(tag & 7);
    }
    return message;
  },
  fromJSON(object) {
    return {
      baseUrl: isSet(object.baseUrl) ? globalThis.String(object.baseUrl) : undefined,
      model: isSet(object.model) ? globalThis.String(object.model) : undefined,
      maxTokens: isSet(object.maxTokens) ? globalThis.Number(object.maxTokens) : undefined,
      temperature: isSet(object.temperature) ? globalThis.Number(object.temperature) : undefined,
      n: isSet(object.n) ? globalThis.Number(object.n) : undefined,
      topP: isSet(object.topP) ? globalThis.Number(object.topP) : undefined,
    };
  },
  toJSON(message) {
    const obj = {};
    if (message.baseUrl !== undefined) {
      obj.baseUrl = message.baseUrl;
    }
    if (message.model !== undefined) {
      obj.model = message.model;
    }
    if (message.maxTokens !== undefined) {
      obj.maxTokens = Math.round(message.maxTokens);
    }
    if (message.temperature !== undefined) {
      obj.temperature = message.temperature;
    }
    if (message.n !== undefined) {
      obj.n = Math.round(message.n);
    }
    if (message.topP !== undefined) {
      obj.topP = message.topP;
    }
    return obj;
  },
  create(base) {
    return GenerativeFriendliAI.fromPartial(base !== null && base !== void 0 ? base : {});
  },
  fromPartial(object) {
    var _a, _b, _c, _d, _e, _f;
    const message = createBaseGenerativeFriendliAI();
    message.baseUrl = (_a = object.baseUrl) !== null && _a !== void 0 ? _a : undefined;
    message.model = (_b = object.model) !== null && _b !== void 0 ? _b : undefined;
    message.maxTokens = (_c = object.maxTokens) !== null && _c !== void 0 ? _c : undefined;
    message.temperature = (_d = object.temperature) !== null && _d !== void 0 ? _d : undefined;
    message.n = (_e = object.n) !== null && _e !== void 0 ? _e : undefined;
    message.topP = (_f = object.topP) !== null && _f !== void 0 ? _f : undefined;
    return message;
  },
};
function createBaseGenerativeNvidia() {
  return {
    baseUrl: undefined,
    model: undefined,
    temperature: undefined,
    topP: undefined,
    maxTokens: undefined,
  };
}
export const GenerativeNvidia = {
  encode(message, writer = _m0.Writer.create()) {
    if (message.baseUrl !== undefined) {
      writer.uint32(10).string(message.baseUrl);
    }
    if (message.model !== undefined) {
      writer.uint32(18).string(message.model);
    }
    if (message.temperature !== undefined) {
      writer.uint32(25).double(message.temperature);
    }
    if (message.topP !== undefined) {
      writer.uint32(33).double(message.topP);
    }
    if (message.maxTokens !== undefined) {
      writer.uint32(40).int64(message.maxTokens);
    }
    return writer;
  },
  decode(input, length) {
    const reader = input instanceof _m0.Reader ? input : _m0.Reader.create(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseGenerativeNvidia();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1:
          if (tag !== 10) {
            break;
          }
          message.baseUrl = reader.string();
          continue;
        case 2:
          if (tag !== 18) {
            break;
          }
          message.model = reader.string();
          continue;
        case 3:
          if (tag !== 25) {
            break;
          }
          message.temperature = reader.double();
          continue;
        case 4:
          if (tag !== 33) {
            break;
          }
          message.topP = reader.double();
          continue;
        case 5:
          if (tag !== 40) {
            break;
          }
          message.maxTokens = longToNumber(reader.int64());
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skipType(tag & 7);
    }
    return message;
  },
  fromJSON(object) {
    return {
      baseUrl: isSet(object.baseUrl) ? globalThis.String(object.baseUrl) : undefined,
      model: isSet(object.model) ? globalThis.String(object.model) : undefined,
      temperature: isSet(object.temperature) ? globalThis.Number(object.temperature) : undefined,
      topP: isSet(object.topP) ? globalThis.Number(object.topP) : undefined,
      maxTokens: isSet(object.maxTokens) ? globalThis.Number(object.maxTokens) : undefined,
    };
  },
  toJSON(message) {
    const obj = {};
    if (message.baseUrl !== undefined) {
      obj.baseUrl = message.baseUrl;
    }
    if (message.model !== undefined) {
      obj.model = message.model;
    }
    if (message.temperature !== undefined) {
      obj.temperature = message.temperature;
    }
    if (message.topP !== undefined) {
      obj.topP = message.topP;
    }
    if (message.maxTokens !== undefined) {
      obj.maxTokens = Math.round(message.maxTokens);
    }
    return obj;
  },
  create(base) {
    return GenerativeNvidia.fromPartial(base !== null && base !== void 0 ? base : {});
  },
  fromPartial(object) {
    var _a, _b, _c, _d, _e;
    const message = createBaseGenerativeNvidia();
    message.baseUrl = (_a = object.baseUrl) !== null && _a !== void 0 ? _a : undefined;
    message.model = (_b = object.model) !== null && _b !== void 0 ? _b : undefined;
    message.temperature = (_c = object.temperature) !== null && _c !== void 0 ? _c : undefined;
    message.topP = (_d = object.topP) !== null && _d !== void 0 ? _d : undefined;
    message.maxTokens = (_e = object.maxTokens) !== null && _e !== void 0 ? _e : undefined;
    return message;
  },
};
function createBaseGenerativeXAI() {
  return {
    baseUrl: undefined,
    model: undefined,
    temperature: undefined,
    topP: undefined,
    maxTokens: undefined,
    images: undefined,
    imageProperties: undefined,
  };
}
export const GenerativeXAI = {
  encode(message, writer = _m0.Writer.create()) {
    if (message.baseUrl !== undefined) {
      writer.uint32(10).string(message.baseUrl);
    }
    if (message.model !== undefined) {
      writer.uint32(18).string(message.model);
    }
    if (message.temperature !== undefined) {
      writer.uint32(25).double(message.temperature);
    }
    if (message.topP !== undefined) {
      writer.uint32(33).double(message.topP);
    }
    if (message.maxTokens !== undefined) {
      writer.uint32(40).int64(message.maxTokens);
    }
    if (message.images !== undefined) {
      TextArray.encode(message.images, writer.uint32(50).fork()).ldelim();
    }
    if (message.imageProperties !== undefined) {
      TextArray.encode(message.imageProperties, writer.uint32(58).fork()).ldelim();
    }
    return writer;
  },
  decode(input, length) {
    const reader = input instanceof _m0.Reader ? input : _m0.Reader.create(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseGenerativeXAI();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1:
          if (tag !== 10) {
            break;
          }
          message.baseUrl = reader.string();
          continue;
        case 2:
          if (tag !== 18) {
            break;
          }
          message.model = reader.string();
          continue;
        case 3:
          if (tag !== 25) {
            break;
          }
          message.temperature = reader.double();
          continue;
        case 4:
          if (tag !== 33) {
            break;
          }
          message.topP = reader.double();
          continue;
        case 5:
          if (tag !== 40) {
            break;
          }
          message.maxTokens = longToNumber(reader.int64());
          continue;
        case 6:
          if (tag !== 50) {
            break;
          }
          message.images = TextArray.decode(reader, reader.uint32());
          continue;
        case 7:
          if (tag !== 58) {
            break;
          }
          message.imageProperties = TextArray.decode(reader, reader.uint32());
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skipType(tag & 7);
    }
    return message;
  },
  fromJSON(object) {
    return {
      baseUrl: isSet(object.baseUrl) ? globalThis.String(object.baseUrl) : undefined,
      model: isSet(object.model) ? globalThis.String(object.model) : undefined,
      temperature: isSet(object.temperature) ? globalThis.Number(object.temperature) : undefined,
      topP: isSet(object.topP) ? globalThis.Number(object.topP) : undefined,
      maxTokens: isSet(object.maxTokens) ? globalThis.Number(object.maxTokens) : undefined,
      images: isSet(object.images) ? TextArray.fromJSON(object.images) : undefined,
      imageProperties: isSet(object.imageProperties) ? TextArray.fromJSON(object.imageProperties) : undefined,
    };
  },
  toJSON(message) {
    const obj = {};
    if (message.baseUrl !== undefined) {
      obj.baseUrl = message.baseUrl;
    }
    if (message.model !== undefined) {
      obj.model = message.model;
    }
    if (message.temperature !== undefined) {
      obj.temperature = message.temperature;
    }
    if (message.topP !== undefined) {
      obj.topP = message.topP;
    }
    if (message.maxTokens !== undefined) {
      obj.maxTokens = Math.round(message.maxTokens);
    }
    if (message.images !== undefined) {
      obj.images = TextArray.toJSON(message.images);
    }
    if (message.imageProperties !== undefined) {
      obj.imageProperties = TextArray.toJSON(message.imageProperties);
    }
    return obj;
  },
  create(base) {
    return GenerativeXAI.fromPartial(base !== null && base !== void 0 ? base : {});
  },
  fromPartial(object) {
    var _a, _b, _c, _d, _e;
    const message = createBaseGenerativeXAI();
    message.baseUrl = (_a = object.baseUrl) !== null && _a !== void 0 ? _a : undefined;
    message.model = (_b = object.model) !== null && _b !== void 0 ? _b : undefined;
    message.temperature = (_c = object.temperature) !== null && _c !== void 0 ? _c : undefined;
    message.topP = (_d = object.topP) !== null && _d !== void 0 ? _d : undefined;
    message.maxTokens = (_e = object.maxTokens) !== null && _e !== void 0 ? _e : undefined;
    message.images =
      object.images !== undefined && object.images !== null
        ? TextArray.fromPartial(object.images)
        : undefined;
    message.imageProperties =
      object.imageProperties !== undefined && object.imageProperties !== null
        ? TextArray.fromPartial(object.imageProperties)
        : undefined;
    return message;
  },
};
function createBaseGenerativeAnthropicMetadata() {
  return { usage: undefined };
}
export const GenerativeAnthropicMetadata = {
  encode(message, writer = _m0.Writer.create()) {
    if (message.usage !== undefined) {
      GenerativeAnthropicMetadata_Usage.encode(message.usage, writer.uint32(10).fork()).ldelim();
    }
    return writer;
  },
  decode(input, length) {
    const reader = input instanceof _m0.Reader ? input : _m0.Reader.create(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseGenerativeAnthropicMetadata();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1:
          if (tag !== 10) {
            break;
          }
          message.usage = GenerativeAnthropicMetadata_Usage.decode(reader, reader.uint32());
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skipType(tag & 7);
    }
    return message;
  },
  fromJSON(object) {
    return {
      usage: isSet(object.usage) ? GenerativeAnthropicMetadata_Usage.fromJSON(object.usage) : undefined,
    };
  },
  toJSON(message) {
    const obj = {};
    if (message.usage !== undefined) {
      obj.usage = GenerativeAnthropicMetadata_Usage.toJSON(message.usage);
    }
    return obj;
  },
  create(base) {
    return GenerativeAnthropicMetadata.fromPartial(base !== null && base !== void 0 ? base : {});
  },
  fromPartial(object) {
    const message = createBaseGenerativeAnthropicMetadata();
    message.usage =
      object.usage !== undefined && object.usage !== null
        ? GenerativeAnthropicMetadata_Usage.fromPartial(object.usage)
        : undefined;
    return message;
  },
};
function createBaseGenerativeAnthropicMetadata_Usage() {
  return { inputTokens: 0, outputTokens: 0 };
}
export const GenerativeAnthropicMetadata_Usage = {
  encode(message, writer = _m0.Writer.create()) {
    if (message.inputTokens !== 0) {
      writer.uint32(8).int64(message.inputTokens);
    }
    if (message.outputTokens !== 0) {
      writer.uint32(16).int64(message.outputTokens);
    }
    return writer;
  },
  decode(input, length) {
    const reader = input instanceof _m0.Reader ? input : _m0.Reader.create(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseGenerativeAnthropicMetadata_Usage();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1:
          if (tag !== 8) {
            break;
          }
          message.inputTokens = longToNumber(reader.int64());
          continue;
        case 2:
          if (tag !== 16) {
            break;
          }
          message.outputTokens = longToNumber(reader.int64());
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skipType(tag & 7);
    }
    return message;
  },
  fromJSON(object) {
    return {
      inputTokens: isSet(object.inputTokens) ? globalThis.Number(object.inputTokens) : 0,
      outputTokens: isSet(object.outputTokens) ? globalThis.Number(object.outputTokens) : 0,
    };
  },
  toJSON(message) {
    const obj = {};
    if (message.inputTokens !== 0) {
      obj.inputTokens = Math.round(message.inputTokens);
    }
    if (message.outputTokens !== 0) {
      obj.outputTokens = Math.round(message.outputTokens);
    }
    return obj;
  },
  create(base) {
    return GenerativeAnthropicMetadata_Usage.fromPartial(base !== null && base !== void 0 ? base : {});
  },
  fromPartial(object) {
    var _a, _b;
    const message = createBaseGenerativeAnthropicMetadata_Usage();
    message.inputTokens = (_a = object.inputTokens) !== null && _a !== void 0 ? _a : 0;
    message.outputTokens = (_b = object.outputTokens) !== null && _b !== void 0 ? _b : 0;
    return message;
  },
};
function createBaseGenerativeAnyscaleMetadata() {
  return {};
}
export const GenerativeAnyscaleMetadata = {
  encode(_, writer = _m0.Writer.create()) {
    return writer;
  },
  decode(input, length) {
    const reader = input instanceof _m0.Reader ? input : _m0.Reader.create(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseGenerativeAnyscaleMetadata();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skipType(tag & 7);
    }
    return message;
  },
  fromJSON(_) {
    return {};
  },
  toJSON(_) {
    const obj = {};
    return obj;
  },
  create(base) {
    return GenerativeAnyscaleMetadata.fromPartial(base !== null && base !== void 0 ? base : {});
  },
  fromPartial(_) {
    const message = createBaseGenerativeAnyscaleMetadata();
    return message;
  },
};
function createBaseGenerativeAWSMetadata() {
  return {};
}
export const GenerativeAWSMetadata = {
  encode(_, writer = _m0.Writer.create()) {
    return writer;
  },
  decode(input, length) {
    const reader = input instanceof _m0.Reader ? input : _m0.Reader.create(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseGenerativeAWSMetadata();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skipType(tag & 7);
    }
    return message;
  },
  fromJSON(_) {
    return {};
  },
  toJSON(_) {
    const obj = {};
    return obj;
  },
  create(base) {
    return GenerativeAWSMetadata.fromPartial(base !== null && base !== void 0 ? base : {});
  },
  fromPartial(_) {
    const message = createBaseGenerativeAWSMetadata();
    return message;
  },
};
function createBaseGenerativeCohereMetadata() {
  return { apiVersion: undefined, billedUnits: undefined, tokens: undefined, warnings: undefined };
}
export const GenerativeCohereMetadata = {
  encode(message, writer = _m0.Writer.create()) {
    if (message.apiVersion !== undefined) {
      GenerativeCohereMetadata_ApiVersion.encode(message.apiVersion, writer.uint32(10).fork()).ldelim();
    }
    if (message.billedUnits !== undefined) {
      GenerativeCohereMetadata_BilledUnits.encode(message.billedUnits, writer.uint32(18).fork()).ldelim();
    }
    if (message.tokens !== undefined) {
      GenerativeCohereMetadata_Tokens.encode(message.tokens, writer.uint32(26).fork()).ldelim();
    }
    if (message.warnings !== undefined) {
      TextArray.encode(message.warnings, writer.uint32(34).fork()).ldelim();
    }
    return writer;
  },
  decode(input, length) {
    const reader = input instanceof _m0.Reader ? input : _m0.Reader.create(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseGenerativeCohereMetadata();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1:
          if (tag !== 10) {
            break;
          }
          message.apiVersion = GenerativeCohereMetadata_ApiVersion.decode(reader, reader.uint32());
          continue;
        case 2:
          if (tag !== 18) {
            break;
          }
          message.billedUnits = GenerativeCohereMetadata_BilledUnits.decode(reader, reader.uint32());
          continue;
        case 3:
          if (tag !== 26) {
            break;
          }
          message.tokens = GenerativeCohereMetadata_Tokens.decode(reader, reader.uint32());
          continue;
        case 4:
          if (tag !== 34) {
            break;
          }
          message.warnings = TextArray.decode(reader, reader.uint32());
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skipType(tag & 7);
    }
    return message;
  },
  fromJSON(object) {
    return {
      apiVersion: isSet(object.apiVersion)
        ? GenerativeCohereMetadata_ApiVersion.fromJSON(object.apiVersion)
        : undefined,
      billedUnits: isSet(object.billedUnits)
        ? GenerativeCohereMetadata_BilledUnits.fromJSON(object.billedUnits)
        : undefined,
      tokens: isSet(object.tokens) ? GenerativeCohereMetadata_Tokens.fromJSON(object.tokens) : undefined,
      warnings: isSet(object.warnings) ? TextArray.fromJSON(object.warnings) : undefined,
    };
  },
  toJSON(message) {
    const obj = {};
    if (message.apiVersion !== undefined) {
      obj.apiVersion = GenerativeCohereMetadata_ApiVersion.toJSON(message.apiVersion);
    }
    if (message.billedUnits !== undefined) {
      obj.billedUnits = GenerativeCohereMetadata_BilledUnits.toJSON(message.billedUnits);
    }
    if (message.tokens !== undefined) {
      obj.tokens = GenerativeCohereMetadata_Tokens.toJSON(message.tokens);
    }
    if (message.warnings !== undefined) {
      obj.warnings = TextArray.toJSON(message.warnings);
    }
    return obj;
  },
  create(base) {
    return GenerativeCohereMetadata.fromPartial(base !== null && base !== void 0 ? base : {});
  },
  fromPartial(object) {
    const message = createBaseGenerativeCohereMetadata();
    message.apiVersion =
      object.apiVersion !== undefined && object.apiVersion !== null
        ? GenerativeCohereMetadata_ApiVersion.fromPartial(object.apiVersion)
        : undefined;
    message.billedUnits =
      object.billedUnits !== undefined && object.billedUnits !== null
        ? GenerativeCohereMetadata_BilledUnits.fromPartial(object.billedUnits)
        : undefined;
    message.tokens =
      object.tokens !== undefined && object.tokens !== null
        ? GenerativeCohereMetadata_Tokens.fromPartial(object.tokens)
        : undefined;
    message.warnings =
      object.warnings !== undefined && object.warnings !== null
        ? TextArray.fromPartial(object.warnings)
        : undefined;
    return message;
  },
};
function createBaseGenerativeCohereMetadata_ApiVersion() {
  return { version: undefined, isDeprecated: undefined, isExperimental: undefined };
}
export const GenerativeCohereMetadata_ApiVersion = {
  encode(message, writer = _m0.Writer.create()) {
    if (message.version !== undefined) {
      writer.uint32(10).string(message.version);
    }
    if (message.isDeprecated !== undefined) {
      writer.uint32(16).bool(message.isDeprecated);
    }
    if (message.isExperimental !== undefined) {
      writer.uint32(24).bool(message.isExperimental);
    }
    return writer;
  },
  decode(input, length) {
    const reader = input instanceof _m0.Reader ? input : _m0.Reader.create(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseGenerativeCohereMetadata_ApiVersion();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1:
          if (tag !== 10) {
            break;
          }
          message.version = reader.string();
          continue;
        case 2:
          if (tag !== 16) {
            break;
          }
          message.isDeprecated = reader.bool();
          continue;
        case 3:
          if (tag !== 24) {
            break;
          }
          message.isExperimental = reader.bool();
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skipType(tag & 7);
    }
    return message;
  },
  fromJSON(object) {
    return {
      version: isSet(object.version) ? globalThis.String(object.version) : undefined,
      isDeprecated: isSet(object.isDeprecated) ? globalThis.Boolean(object.isDeprecated) : undefined,
      isExperimental: isSet(object.isExperimental) ? globalThis.Boolean(object.isExperimental) : undefined,
    };
  },
  toJSON(message) {
    const obj = {};
    if (message.version !== undefined) {
      obj.version = message.version;
    }
    if (message.isDeprecated !== undefined) {
      obj.isDeprecated = message.isDeprecated;
    }
    if (message.isExperimental !== undefined) {
      obj.isExperimental = message.isExperimental;
    }
    return obj;
  },
  create(base) {
    return GenerativeCohereMetadata_ApiVersion.fromPartial(base !== null && base !== void 0 ? base : {});
  },
  fromPartial(object) {
    var _a, _b, _c;
    const message = createBaseGenerativeCohereMetadata_ApiVersion();
    message.version = (_a = object.version) !== null && _a !== void 0 ? _a : undefined;
    message.isDeprecated = (_b = object.isDeprecated) !== null && _b !== void 0 ? _b : undefined;
    message.isExperimental = (_c = object.isExperimental) !== null && _c !== void 0 ? _c : undefined;
    return message;
  },
};
function createBaseGenerativeCohereMetadata_BilledUnits() {
  return {
    inputTokens: undefined,
    outputTokens: undefined,
    searchUnits: undefined,
    classifications: undefined,
  };
}
export const GenerativeCohereMetadata_BilledUnits = {
  encode(message, writer = _m0.Writer.create()) {
    if (message.inputTokens !== undefined) {
      writer.uint32(9).double(message.inputTokens);
    }
    if (message.outputTokens !== undefined) {
      writer.uint32(17).double(message.outputTokens);
    }
    if (message.searchUnits !== undefined) {
      writer.uint32(25).double(message.searchUnits);
    }
    if (message.classifications !== undefined) {
      writer.uint32(33).double(message.classifications);
    }
    return writer;
  },
  decode(input, length) {
    const reader = input instanceof _m0.Reader ? input : _m0.Reader.create(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseGenerativeCohereMetadata_BilledUnits();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1:
          if (tag !== 9) {
            break;
          }
          message.inputTokens = reader.double();
          continue;
        case 2:
          if (tag !== 17) {
            break;
          }
          message.outputTokens = reader.double();
          continue;
        case 3:
          if (tag !== 25) {
            break;
          }
          message.searchUnits = reader.double();
          continue;
        case 4:
          if (tag !== 33) {
            break;
          }
          message.classifications = reader.double();
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skipType(tag & 7);
    }
    return message;
  },
  fromJSON(object) {
    return {
      inputTokens: isSet(object.inputTokens) ? globalThis.Number(object.inputTokens) : undefined,
      outputTokens: isSet(object.outputTokens) ? globalThis.Number(object.outputTokens) : undefined,
      searchUnits: isSet(object.searchUnits) ? globalThis.Number(object.searchUnits) : undefined,
      classifications: isSet(object.classifications) ? globalThis.Number(object.classifications) : undefined,
    };
  },
  toJSON(message) {
    const obj = {};
    if (message.inputTokens !== undefined) {
      obj.inputTokens = message.inputTokens;
    }
    if (message.outputTokens !== undefined) {
      obj.outputTokens = message.outputTokens;
    }
    if (message.searchUnits !== undefined) {
      obj.searchUnits = message.searchUnits;
    }
    if (message.classifications !== undefined) {
      obj.classifications = message.classifications;
    }
    return obj;
  },
  create(base) {
    return GenerativeCohereMetadata_BilledUnits.fromPartial(base !== null && base !== void 0 ? base : {});
  },
  fromPartial(object) {
    var _a, _b, _c, _d;
    const message = createBaseGenerativeCohereMetadata_BilledUnits();
    message.inputTokens = (_a = object.inputTokens) !== null && _a !== void 0 ? _a : undefined;
    message.outputTokens = (_b = object.outputTokens) !== null && _b !== void 0 ? _b : undefined;
    message.searchUnits = (_c = object.searchUnits) !== null && _c !== void 0 ? _c : undefined;
    message.classifications = (_d = object.classifications) !== null && _d !== void 0 ? _d : undefined;
    return message;
  },
};
function createBaseGenerativeCohereMetadata_Tokens() {
  return { inputTokens: undefined, outputTokens: undefined };
}
export const GenerativeCohereMetadata_Tokens = {
  encode(message, writer = _m0.Writer.create()) {
    if (message.inputTokens !== undefined) {
      writer.uint32(9).double(message.inputTokens);
    }
    if (message.outputTokens !== undefined) {
      writer.uint32(17).double(message.outputTokens);
    }
    return writer;
  },
  decode(input, length) {
    const reader = input instanceof _m0.Reader ? input : _m0.Reader.create(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseGenerativeCohereMetadata_Tokens();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1:
          if (tag !== 9) {
            break;
          }
          message.inputTokens = reader.double();
          continue;
        case 2:
          if (tag !== 17) {
            break;
          }
          message.outputTokens = reader.double();
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skipType(tag & 7);
    }
    return message;
  },
  fromJSON(object) {
    return {
      inputTokens: isSet(object.inputTokens) ? globalThis.Number(object.inputTokens) : undefined,
      outputTokens: isSet(object.outputTokens) ? globalThis.Number(object.outputTokens) : undefined,
    };
  },
  toJSON(message) {
    const obj = {};
    if (message.inputTokens !== undefined) {
      obj.inputTokens = message.inputTokens;
    }
    if (message.outputTokens !== undefined) {
      obj.outputTokens = message.outputTokens;
    }
    return obj;
  },
  create(base) {
    return GenerativeCohereMetadata_Tokens.fromPartial(base !== null && base !== void 0 ? base : {});
  },
  fromPartial(object) {
    var _a, _b;
    const message = createBaseGenerativeCohereMetadata_Tokens();
    message.inputTokens = (_a = object.inputTokens) !== null && _a !== void 0 ? _a : undefined;
    message.outputTokens = (_b = object.outputTokens) !== null && _b !== void 0 ? _b : undefined;
    return message;
  },
};
function createBaseGenerativeDummyMetadata() {
  return {};
}
export const GenerativeDummyMetadata = {
  encode(_, writer = _m0.Writer.create()) {
    return writer;
  },
  decode(input, length) {
    const reader = input instanceof _m0.Reader ? input : _m0.Reader.create(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseGenerativeDummyMetadata();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skipType(tag & 7);
    }
    return message;
  },
  fromJSON(_) {
    return {};
  },
  toJSON(_) {
    const obj = {};
    return obj;
  },
  create(base) {
    return GenerativeDummyMetadata.fromPartial(base !== null && base !== void 0 ? base : {});
  },
  fromPartial(_) {
    const message = createBaseGenerativeDummyMetadata();
    return message;
  },
};
function createBaseGenerativeMistralMetadata() {
  return { usage: undefined };
}
export const GenerativeMistralMetadata = {
  encode(message, writer = _m0.Writer.create()) {
    if (message.usage !== undefined) {
      GenerativeMistralMetadata_Usage.encode(message.usage, writer.uint32(10).fork()).ldelim();
    }
    return writer;
  },
  decode(input, length) {
    const reader = input instanceof _m0.Reader ? input : _m0.Reader.create(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseGenerativeMistralMetadata();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1:
          if (tag !== 10) {
            break;
          }
          message.usage = GenerativeMistralMetadata_Usage.decode(reader, reader.uint32());
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skipType(tag & 7);
    }
    return message;
  },
  fromJSON(object) {
    return {
      usage: isSet(object.usage) ? GenerativeMistralMetadata_Usage.fromJSON(object.usage) : undefined,
    };
  },
  toJSON(message) {
    const obj = {};
    if (message.usage !== undefined) {
      obj.usage = GenerativeMistralMetadata_Usage.toJSON(message.usage);
    }
    return obj;
  },
  create(base) {
    return GenerativeMistralMetadata.fromPartial(base !== null && base !== void 0 ? base : {});
  },
  fromPartial(object) {
    const message = createBaseGenerativeMistralMetadata();
    message.usage =
      object.usage !== undefined && object.usage !== null
        ? GenerativeMistralMetadata_Usage.fromPartial(object.usage)
        : undefined;
    return message;
  },
};
function createBaseGenerativeMistralMetadata_Usage() {
  return { promptTokens: undefined, completionTokens: undefined, totalTokens: undefined };
}
export const GenerativeMistralMetadata_Usage = {
  encode(message, writer = _m0.Writer.create()) {
    if (message.promptTokens !== undefined) {
      writer.uint32(8).int64(message.promptTokens);
    }
    if (message.completionTokens !== undefined) {
      writer.uint32(16).int64(message.completionTokens);
    }
    if (message.totalTokens !== undefined) {
      writer.uint32(24).int64(message.totalTokens);
    }
    return writer;
  },
  decode(input, length) {
    const reader = input instanceof _m0.Reader ? input : _m0.Reader.create(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseGenerativeMistralMetadata_Usage();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1:
          if (tag !== 8) {
            break;
          }
          message.promptTokens = longToNumber(reader.int64());
          continue;
        case 2:
          if (tag !== 16) {
            break;
          }
          message.completionTokens = longToNumber(reader.int64());
          continue;
        case 3:
          if (tag !== 24) {
            break;
          }
          message.totalTokens = longToNumber(reader.int64());
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skipType(tag & 7);
    }
    return message;
  },
  fromJSON(object) {
    return {
      promptTokens: isSet(object.promptTokens) ? globalThis.Number(object.promptTokens) : undefined,
      completionTokens: isSet(object.completionTokens)
        ? globalThis.Number(object.completionTokens)
        : undefined,
      totalTokens: isSet(object.totalTokens) ? globalThis.Number(object.totalTokens) : undefined,
    };
  },
  toJSON(message) {
    const obj = {};
    if (message.promptTokens !== undefined) {
      obj.promptTokens = Math.round(message.promptTokens);
    }
    if (message.completionTokens !== undefined) {
      obj.completionTokens = Math.round(message.completionTokens);
    }
    if (message.totalTokens !== undefined) {
      obj.totalTokens = Math.round(message.totalTokens);
    }
    return obj;
  },
  create(base) {
    return GenerativeMistralMetadata_Usage.fromPartial(base !== null && base !== void 0 ? base : {});
  },
  fromPartial(object) {
    var _a, _b, _c;
    const message = createBaseGenerativeMistralMetadata_Usage();
    message.promptTokens = (_a = object.promptTokens) !== null && _a !== void 0 ? _a : undefined;
    message.completionTokens = (_b = object.completionTokens) !== null && _b !== void 0 ? _b : undefined;
    message.totalTokens = (_c = object.totalTokens) !== null && _c !== void 0 ? _c : undefined;
    return message;
  },
};
function createBaseGenerativeOllamaMetadata() {
  return {};
}
export const GenerativeOllamaMetadata = {
  encode(_, writer = _m0.Writer.create()) {
    return writer;
  },
  decode(input, length) {
    const reader = input instanceof _m0.Reader ? input : _m0.Reader.create(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseGenerativeOllamaMetadata();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skipType(tag & 7);
    }
    return message;
  },
  fromJSON(_) {
    return {};
  },
  toJSON(_) {
    const obj = {};
    return obj;
  },
  create(base) {
    return GenerativeOllamaMetadata.fromPartial(base !== null && base !== void 0 ? base : {});
  },
  fromPartial(_) {
    const message = createBaseGenerativeOllamaMetadata();
    return message;
  },
};
function createBaseGenerativeOpenAIMetadata() {
  return { usage: undefined };
}
export const GenerativeOpenAIMetadata = {
  encode(message, writer = _m0.Writer.create()) {
    if (message.usage !== undefined) {
      GenerativeOpenAIMetadata_Usage.encode(message.usage, writer.uint32(10).fork()).ldelim();
    }
    return writer;
  },
  decode(input, length) {
    const reader = input instanceof _m0.Reader ? input : _m0.Reader.create(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseGenerativeOpenAIMetadata();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1:
          if (tag !== 10) {
            break;
          }
          message.usage = GenerativeOpenAIMetadata_Usage.decode(reader, reader.uint32());
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skipType(tag & 7);
    }
    return message;
  },
  fromJSON(object) {
    return { usage: isSet(object.usage) ? GenerativeOpenAIMetadata_Usage.fromJSON(object.usage) : undefined };
  },
  toJSON(message) {
    const obj = {};
    if (message.usage !== undefined) {
      obj.usage = GenerativeOpenAIMetadata_Usage.toJSON(message.usage);
    }
    return obj;
  },
  create(base) {
    return GenerativeOpenAIMetadata.fromPartial(base !== null && base !== void 0 ? base : {});
  },
  fromPartial(object) {
    const message = createBaseGenerativeOpenAIMetadata();
    message.usage =
      object.usage !== undefined && object.usage !== null
        ? GenerativeOpenAIMetadata_Usage.fromPartial(object.usage)
        : undefined;
    return message;
  },
};
function createBaseGenerativeOpenAIMetadata_Usage() {
  return { promptTokens: undefined, completionTokens: undefined, totalTokens: undefined };
}
export const GenerativeOpenAIMetadata_Usage = {
  encode(message, writer = _m0.Writer.create()) {
    if (message.promptTokens !== undefined) {
      writer.uint32(8).int64(message.promptTokens);
    }
    if (message.completionTokens !== undefined) {
      writer.uint32(16).int64(message.completionTokens);
    }
    if (message.totalTokens !== undefined) {
      writer.uint32(24).int64(message.totalTokens);
    }
    return writer;
  },
  decode(input, length) {
    const reader = input instanceof _m0.Reader ? input : _m0.Reader.create(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseGenerativeOpenAIMetadata_Usage();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1:
          if (tag !== 8) {
            break;
          }
          message.promptTokens = longToNumber(reader.int64());
          continue;
        case 2:
          if (tag !== 16) {
            break;
          }
          message.completionTokens = longToNumber(reader.int64());
          continue;
        case 3:
          if (tag !== 24) {
            break;
          }
          message.totalTokens = longToNumber(reader.int64());
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skipType(tag & 7);
    }
    return message;
  },
  fromJSON(object) {
    return {
      promptTokens: isSet(object.promptTokens) ? globalThis.Number(object.promptTokens) : undefined,
      completionTokens: isSet(object.completionTokens)
        ? globalThis.Number(object.completionTokens)
        : undefined,
      totalTokens: isSet(object.totalTokens) ? globalThis.Number(object.totalTokens) : undefined,
    };
  },
  toJSON(message) {
    const obj = {};
    if (message.promptTokens !== undefined) {
      obj.promptTokens = Math.round(message.promptTokens);
    }
    if (message.completionTokens !== undefined) {
      obj.completionTokens = Math.round(message.completionTokens);
    }
    if (message.totalTokens !== undefined) {
      obj.totalTokens = Math.round(message.totalTokens);
    }
    return obj;
  },
  create(base) {
    return GenerativeOpenAIMetadata_Usage.fromPartial(base !== null && base !== void 0 ? base : {});
  },
  fromPartial(object) {
    var _a, _b, _c;
    const message = createBaseGenerativeOpenAIMetadata_Usage();
    message.promptTokens = (_a = object.promptTokens) !== null && _a !== void 0 ? _a : undefined;
    message.completionTokens = (_b = object.completionTokens) !== null && _b !== void 0 ? _b : undefined;
    message.totalTokens = (_c = object.totalTokens) !== null && _c !== void 0 ? _c : undefined;
    return message;
  },
};
function createBaseGenerativeGoogleMetadata() {
  return { metadata: undefined, usageMetadata: undefined };
}
export const GenerativeGoogleMetadata = {
  encode(message, writer = _m0.Writer.create()) {
    if (message.metadata !== undefined) {
      GenerativeGoogleMetadata_Metadata.encode(message.metadata, writer.uint32(10).fork()).ldelim();
    }
    if (message.usageMetadata !== undefined) {
      GenerativeGoogleMetadata_UsageMetadata.encode(message.usageMetadata, writer.uint32(18).fork()).ldelim();
    }
    return writer;
  },
  decode(input, length) {
    const reader = input instanceof _m0.Reader ? input : _m0.Reader.create(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseGenerativeGoogleMetadata();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1:
          if (tag !== 10) {
            break;
          }
          message.metadata = GenerativeGoogleMetadata_Metadata.decode(reader, reader.uint32());
          continue;
        case 2:
          if (tag !== 18) {
            break;
          }
          message.usageMetadata = GenerativeGoogleMetadata_UsageMetadata.decode(reader, reader.uint32());
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skipType(tag & 7);
    }
    return message;
  },
  fromJSON(object) {
    return {
      metadata: isSet(object.metadata)
        ? GenerativeGoogleMetadata_Metadata.fromJSON(object.metadata)
        : undefined,
      usageMetadata: isSet(object.usageMetadata)
        ? GenerativeGoogleMetadata_UsageMetadata.fromJSON(object.usageMetadata)
        : undefined,
    };
  },
  toJSON(message) {
    const obj = {};
    if (message.metadata !== undefined) {
      obj.metadata = GenerativeGoogleMetadata_Metadata.toJSON(message.metadata);
    }
    if (message.usageMetadata !== undefined) {
      obj.usageMetadata = GenerativeGoogleMetadata_UsageMetadata.toJSON(message.usageMetadata);
    }
    return obj;
  },
  create(base) {
    return GenerativeGoogleMetadata.fromPartial(base !== null && base !== void 0 ? base : {});
  },
  fromPartial(object) {
    const message = createBaseGenerativeGoogleMetadata();
    message.metadata =
      object.metadata !== undefined && object.metadata !== null
        ? GenerativeGoogleMetadata_Metadata.fromPartial(object.metadata)
        : undefined;
    message.usageMetadata =
      object.usageMetadata !== undefined && object.usageMetadata !== null
        ? GenerativeGoogleMetadata_UsageMetadata.fromPartial(object.usageMetadata)
        : undefined;
    return message;
  },
};
function createBaseGenerativeGoogleMetadata_TokenCount() {
  return { totalBillableCharacters: undefined, totalTokens: undefined };
}
export const GenerativeGoogleMetadata_TokenCount = {
  encode(message, writer = _m0.Writer.create()) {
    if (message.totalBillableCharacters !== undefined) {
      writer.uint32(8).int64(message.totalBillableCharacters);
    }
    if (message.totalTokens !== undefined) {
      writer.uint32(16).int64(message.totalTokens);
    }
    return writer;
  },
  decode(input, length) {
    const reader = input instanceof _m0.Reader ? input : _m0.Reader.create(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseGenerativeGoogleMetadata_TokenCount();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1:
          if (tag !== 8) {
            break;
          }
          message.totalBillableCharacters = longToNumber(reader.int64());
          continue;
        case 2:
          if (tag !== 16) {
            break;
          }
          message.totalTokens = longToNumber(reader.int64());
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skipType(tag & 7);
    }
    return message;
  },
  fromJSON(object) {
    return {
      totalBillableCharacters: isSet(object.totalBillableCharacters)
        ? globalThis.Number(object.totalBillableCharacters)
        : undefined,
      totalTokens: isSet(object.totalTokens) ? globalThis.Number(object.totalTokens) : undefined,
    };
  },
  toJSON(message) {
    const obj = {};
    if (message.totalBillableCharacters !== undefined) {
      obj.totalBillableCharacters = Math.round(message.totalBillableCharacters);
    }
    if (message.totalTokens !== undefined) {
      obj.totalTokens = Math.round(message.totalTokens);
    }
    return obj;
  },
  create(base) {
    return GenerativeGoogleMetadata_TokenCount.fromPartial(base !== null && base !== void 0 ? base : {});
  },
  fromPartial(object) {
    var _a, _b;
    const message = createBaseGenerativeGoogleMetadata_TokenCount();
    message.totalBillableCharacters =
      (_a = object.totalBillableCharacters) !== null && _a !== void 0 ? _a : undefined;
    message.totalTokens = (_b = object.totalTokens) !== null && _b !== void 0 ? _b : undefined;
    return message;
  },
};
function createBaseGenerativeGoogleMetadata_TokenMetadata() {
  return { inputTokenCount: undefined, outputTokenCount: undefined };
}
export const GenerativeGoogleMetadata_TokenMetadata = {
  encode(message, writer = _m0.Writer.create()) {
    if (message.inputTokenCount !== undefined) {
      GenerativeGoogleMetadata_TokenCount.encode(message.inputTokenCount, writer.uint32(10).fork()).ldelim();
    }
    if (message.outputTokenCount !== undefined) {
      GenerativeGoogleMetadata_TokenCount.encode(message.outputTokenCount, writer.uint32(18).fork()).ldelim();
    }
    return writer;
  },
  decode(input, length) {
    const reader = input instanceof _m0.Reader ? input : _m0.Reader.create(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseGenerativeGoogleMetadata_TokenMetadata();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1:
          if (tag !== 10) {
            break;
          }
          message.inputTokenCount = GenerativeGoogleMetadata_TokenCount.decode(reader, reader.uint32());
          continue;
        case 2:
          if (tag !== 18) {
            break;
          }
          message.outputTokenCount = GenerativeGoogleMetadata_TokenCount.decode(reader, reader.uint32());
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skipType(tag & 7);
    }
    return message;
  },
  fromJSON(object) {
    return {
      inputTokenCount: isSet(object.inputTokenCount)
        ? GenerativeGoogleMetadata_TokenCount.fromJSON(object.inputTokenCount)
        : undefined,
      outputTokenCount: isSet(object.outputTokenCount)
        ? GenerativeGoogleMetadata_TokenCount.fromJSON(object.outputTokenCount)
        : undefined,
    };
  },
  toJSON(message) {
    const obj = {};
    if (message.inputTokenCount !== undefined) {
      obj.inputTokenCount = GenerativeGoogleMetadata_TokenCount.toJSON(message.inputTokenCount);
    }
    if (message.outputTokenCount !== undefined) {
      obj.outputTokenCount = GenerativeGoogleMetadata_TokenCount.toJSON(message.outputTokenCount);
    }
    return obj;
  },
  create(base) {
    return GenerativeGoogleMetadata_TokenMetadata.fromPartial(base !== null && base !== void 0 ? base : {});
  },
  fromPartial(object) {
    const message = createBaseGenerativeGoogleMetadata_TokenMetadata();
    message.inputTokenCount =
      object.inputTokenCount !== undefined && object.inputTokenCount !== null
        ? GenerativeGoogleMetadata_TokenCount.fromPartial(object.inputTokenCount)
        : undefined;
    message.outputTokenCount =
      object.outputTokenCount !== undefined && object.outputTokenCount !== null
        ? GenerativeGoogleMetadata_TokenCount.fromPartial(object.outputTokenCount)
        : undefined;
    return message;
  },
};
function createBaseGenerativeGoogleMetadata_Metadata() {
  return { tokenMetadata: undefined };
}
export const GenerativeGoogleMetadata_Metadata = {
  encode(message, writer = _m0.Writer.create()) {
    if (message.tokenMetadata !== undefined) {
      GenerativeGoogleMetadata_TokenMetadata.encode(message.tokenMetadata, writer.uint32(10).fork()).ldelim();
    }
    return writer;
  },
  decode(input, length) {
    const reader = input instanceof _m0.Reader ? input : _m0.Reader.create(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseGenerativeGoogleMetadata_Metadata();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1:
          if (tag !== 10) {
            break;
          }
          message.tokenMetadata = GenerativeGoogleMetadata_TokenMetadata.decode(reader, reader.uint32());
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skipType(tag & 7);
    }
    return message;
  },
  fromJSON(object) {
    return {
      tokenMetadata: isSet(object.tokenMetadata)
        ? GenerativeGoogleMetadata_TokenMetadata.fromJSON(object.tokenMetadata)
        : undefined,
    };
  },
  toJSON(message) {
    const obj = {};
    if (message.tokenMetadata !== undefined) {
      obj.tokenMetadata = GenerativeGoogleMetadata_TokenMetadata.toJSON(message.tokenMetadata);
    }
    return obj;
  },
  create(base) {
    return GenerativeGoogleMetadata_Metadata.fromPartial(base !== null && base !== void 0 ? base : {});
  },
  fromPartial(object) {
    const message = createBaseGenerativeGoogleMetadata_Metadata();
    message.tokenMetadata =
      object.tokenMetadata !== undefined && object.tokenMetadata !== null
        ? GenerativeGoogleMetadata_TokenMetadata.fromPartial(object.tokenMetadata)
        : undefined;
    return message;
  },
};
function createBaseGenerativeGoogleMetadata_UsageMetadata() {
  return { promptTokenCount: undefined, candidatesTokenCount: undefined, totalTokenCount: undefined };
}
export const GenerativeGoogleMetadata_UsageMetadata = {
  encode(message, writer = _m0.Writer.create()) {
    if (message.promptTokenCount !== undefined) {
      writer.uint32(8).int64(message.promptTokenCount);
    }
    if (message.candidatesTokenCount !== undefined) {
      writer.uint32(16).int64(message.candidatesTokenCount);
    }
    if (message.totalTokenCount !== undefined) {
      writer.uint32(24).int64(message.totalTokenCount);
    }
    return writer;
  },
  decode(input, length) {
    const reader = input instanceof _m0.Reader ? input : _m0.Reader.create(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseGenerativeGoogleMetadata_UsageMetadata();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1:
          if (tag !== 8) {
            break;
          }
          message.promptTokenCount = longToNumber(reader.int64());
          continue;
        case 2:
          if (tag !== 16) {
            break;
          }
          message.candidatesTokenCount = longToNumber(reader.int64());
          continue;
        case 3:
          if (tag !== 24) {
            break;
          }
          message.totalTokenCount = longToNumber(reader.int64());
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skipType(tag & 7);
    }
    return message;
  },
  fromJSON(object) {
    return {
      promptTokenCount: isSet(object.promptTokenCount)
        ? globalThis.Number(object.promptTokenCount)
        : undefined,
      candidatesTokenCount: isSet(object.candidatesTokenCount)
        ? globalThis.Number(object.candidatesTokenCount)
        : undefined,
      totalTokenCount: isSet(object.totalTokenCount) ? globalThis.Number(object.totalTokenCount) : undefined,
    };
  },
  toJSON(message) {
    const obj = {};
    if (message.promptTokenCount !== undefined) {
      obj.promptTokenCount = Math.round(message.promptTokenCount);
    }
    if (message.candidatesTokenCount !== undefined) {
      obj.candidatesTokenCount = Math.round(message.candidatesTokenCount);
    }
    if (message.totalTokenCount !== undefined) {
      obj.totalTokenCount = Math.round(message.totalTokenCount);
    }
    return obj;
  },
  create(base) {
    return GenerativeGoogleMetadata_UsageMetadata.fromPartial(base !== null && base !== void 0 ? base : {});
  },
  fromPartial(object) {
    var _a, _b, _c;
    const message = createBaseGenerativeGoogleMetadata_UsageMetadata();
    message.promptTokenCount = (_a = object.promptTokenCount) !== null && _a !== void 0 ? _a : undefined;
    message.candidatesTokenCount =
      (_b = object.candidatesTokenCount) !== null && _b !== void 0 ? _b : undefined;
    message.totalTokenCount = (_c = object.totalTokenCount) !== null && _c !== void 0 ? _c : undefined;
    return message;
  },
};
function createBaseGenerativeDatabricksMetadata() {
  return { usage: undefined };
}
export const GenerativeDatabricksMetadata = {
  encode(message, writer = _m0.Writer.create()) {
    if (message.usage !== undefined) {
      GenerativeDatabricksMetadata_Usage.encode(message.usage, writer.uint32(10).fork()).ldelim();
    }
    return writer;
  },
  decode(input, length) {
    const reader = input instanceof _m0.Reader ? input : _m0.Reader.create(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseGenerativeDatabricksMetadata();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1:
          if (tag !== 10) {
            break;
          }
          message.usage = GenerativeDatabricksMetadata_Usage.decode(reader, reader.uint32());
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skipType(tag & 7);
    }
    return message;
  },
  fromJSON(object) {
    return {
      usage: isSet(object.usage) ? GenerativeDatabricksMetadata_Usage.fromJSON(object.usage) : undefined,
    };
  },
  toJSON(message) {
    const obj = {};
    if (message.usage !== undefined) {
      obj.usage = GenerativeDatabricksMetadata_Usage.toJSON(message.usage);
    }
    return obj;
  },
  create(base) {
    return GenerativeDatabricksMetadata.fromPartial(base !== null && base !== void 0 ? base : {});
  },
  fromPartial(object) {
    const message = createBaseGenerativeDatabricksMetadata();
    message.usage =
      object.usage !== undefined && object.usage !== null
        ? GenerativeDatabricksMetadata_Usage.fromPartial(object.usage)
        : undefined;
    return message;
  },
};
function createBaseGenerativeDatabricksMetadata_Usage() {
  return { promptTokens: undefined, completionTokens: undefined, totalTokens: undefined };
}
export const GenerativeDatabricksMetadata_Usage = {
  encode(message, writer = _m0.Writer.create()) {
    if (message.promptTokens !== undefined) {
      writer.uint32(8).int64(message.promptTokens);
    }
    if (message.completionTokens !== undefined) {
      writer.uint32(16).int64(message.completionTokens);
    }
    if (message.totalTokens !== undefined) {
      writer.uint32(24).int64(message.totalTokens);
    }
    return writer;
  },
  decode(input, length) {
    const reader = input instanceof _m0.Reader ? input : _m0.Reader.create(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseGenerativeDatabricksMetadata_Usage();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1:
          if (tag !== 8) {
            break;
          }
          message.promptTokens = longToNumber(reader.int64());
          continue;
        case 2:
          if (tag !== 16) {
            break;
          }
          message.completionTokens = longToNumber(reader.int64());
          continue;
        case 3:
          if (tag !== 24) {
            break;
          }
          message.totalTokens = longToNumber(reader.int64());
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skipType(tag & 7);
    }
    return message;
  },
  fromJSON(object) {
    return {
      promptTokens: isSet(object.promptTokens) ? globalThis.Number(object.promptTokens) : undefined,
      completionTokens: isSet(object.completionTokens)
        ? globalThis.Number(object.completionTokens)
        : undefined,
      totalTokens: isSet(object.totalTokens) ? globalThis.Number(object.totalTokens) : undefined,
    };
  },
  toJSON(message) {
    const obj = {};
    if (message.promptTokens !== undefined) {
      obj.promptTokens = Math.round(message.promptTokens);
    }
    if (message.completionTokens !== undefined) {
      obj.completionTokens = Math.round(message.completionTokens);
    }
    if (message.totalTokens !== undefined) {
      obj.totalTokens = Math.round(message.totalTokens);
    }
    return obj;
  },
  create(base) {
    return GenerativeDatabricksMetadata_Usage.fromPartial(base !== null && base !== void 0 ? base : {});
  },
  fromPartial(object) {
    var _a, _b, _c;
    const message = createBaseGenerativeDatabricksMetadata_Usage();
    message.promptTokens = (_a = object.promptTokens) !== null && _a !== void 0 ? _a : undefined;
    message.completionTokens = (_b = object.completionTokens) !== null && _b !== void 0 ? _b : undefined;
    message.totalTokens = (_c = object.totalTokens) !== null && _c !== void 0 ? _c : undefined;
    return message;
  },
};
function createBaseGenerativeFriendliAIMetadata() {
  return { usage: undefined };
}
export const GenerativeFriendliAIMetadata = {
  encode(message, writer = _m0.Writer.create()) {
    if (message.usage !== undefined) {
      GenerativeFriendliAIMetadata_Usage.encode(message.usage, writer.uint32(10).fork()).ldelim();
    }
    return writer;
  },
  decode(input, length) {
    const reader = input instanceof _m0.Reader ? input : _m0.Reader.create(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseGenerativeFriendliAIMetadata();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1:
          if (tag !== 10) {
            break;
          }
          message.usage = GenerativeFriendliAIMetadata_Usage.decode(reader, reader.uint32());
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skipType(tag & 7);
    }
    return message;
  },
  fromJSON(object) {
    return {
      usage: isSet(object.usage) ? GenerativeFriendliAIMetadata_Usage.fromJSON(object.usage) : undefined,
    };
  },
  toJSON(message) {
    const obj = {};
    if (message.usage !== undefined) {
      obj.usage = GenerativeFriendliAIMetadata_Usage.toJSON(message.usage);
    }
    return obj;
  },
  create(base) {
    return GenerativeFriendliAIMetadata.fromPartial(base !== null && base !== void 0 ? base : {});
  },
  fromPartial(object) {
    const message = createBaseGenerativeFriendliAIMetadata();
    message.usage =
      object.usage !== undefined && object.usage !== null
        ? GenerativeFriendliAIMetadata_Usage.fromPartial(object.usage)
        : undefined;
    return message;
  },
};
function createBaseGenerativeFriendliAIMetadata_Usage() {
  return { promptTokens: undefined, completionTokens: undefined, totalTokens: undefined };
}
export const GenerativeFriendliAIMetadata_Usage = {
  encode(message, writer = _m0.Writer.create()) {
    if (message.promptTokens !== undefined) {
      writer.uint32(8).int64(message.promptTokens);
    }
    if (message.completionTokens !== undefined) {
      writer.uint32(16).int64(message.completionTokens);
    }
    if (message.totalTokens !== undefined) {
      writer.uint32(24).int64(message.totalTokens);
    }
    return writer;
  },
  decode(input, length) {
    const reader = input instanceof _m0.Reader ? input : _m0.Reader.create(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseGenerativeFriendliAIMetadata_Usage();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1:
          if (tag !== 8) {
            break;
          }
          message.promptTokens = longToNumber(reader.int64());
          continue;
        case 2:
          if (tag !== 16) {
            break;
          }
          message.completionTokens = longToNumber(reader.int64());
          continue;
        case 3:
          if (tag !== 24) {
            break;
          }
          message.totalTokens = longToNumber(reader.int64());
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skipType(tag & 7);
    }
    return message;
  },
  fromJSON(object) {
    return {
      promptTokens: isSet(object.promptTokens) ? globalThis.Number(object.promptTokens) : undefined,
      completionTokens: isSet(object.completionTokens)
        ? globalThis.Number(object.completionTokens)
        : undefined,
      totalTokens: isSet(object.totalTokens) ? globalThis.Number(object.totalTokens) : undefined,
    };
  },
  toJSON(message) {
    const obj = {};
    if (message.promptTokens !== undefined) {
      obj.promptTokens = Math.round(message.promptTokens);
    }
    if (message.completionTokens !== undefined) {
      obj.completionTokens = Math.round(message.completionTokens);
    }
    if (message.totalTokens !== undefined) {
      obj.totalTokens = Math.round(message.totalTokens);
    }
    return obj;
  },
  create(base) {
    return GenerativeFriendliAIMetadata_Usage.fromPartial(base !== null && base !== void 0 ? base : {});
  },
  fromPartial(object) {
    var _a, _b, _c;
    const message = createBaseGenerativeFriendliAIMetadata_Usage();
    message.promptTokens = (_a = object.promptTokens) !== null && _a !== void 0 ? _a : undefined;
    message.completionTokens = (_b = object.completionTokens) !== null && _b !== void 0 ? _b : undefined;
    message.totalTokens = (_c = object.totalTokens) !== null && _c !== void 0 ? _c : undefined;
    return message;
  },
};
function createBaseGenerativeNvidiaMetadata() {
  return { usage: undefined };
}
export const GenerativeNvidiaMetadata = {
  encode(message, writer = _m0.Writer.create()) {
    if (message.usage !== undefined) {
      GenerativeNvidiaMetadata_Usage.encode(message.usage, writer.uint32(10).fork()).ldelim();
    }
    return writer;
  },
  decode(input, length) {
    const reader = input instanceof _m0.Reader ? input : _m0.Reader.create(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseGenerativeNvidiaMetadata();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1:
          if (tag !== 10) {
            break;
          }
          message.usage = GenerativeNvidiaMetadata_Usage.decode(reader, reader.uint32());
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skipType(tag & 7);
    }
    return message;
  },
  fromJSON(object) {
    return { usage: isSet(object.usage) ? GenerativeNvidiaMetadata_Usage.fromJSON(object.usage) : undefined };
  },
  toJSON(message) {
    const obj = {};
    if (message.usage !== undefined) {
      obj.usage = GenerativeNvidiaMetadata_Usage.toJSON(message.usage);
    }
    return obj;
  },
  create(base) {
    return GenerativeNvidiaMetadata.fromPartial(base !== null && base !== void 0 ? base : {});
  },
  fromPartial(object) {
    const message = createBaseGenerativeNvidiaMetadata();
    message.usage =
      object.usage !== undefined && object.usage !== null
        ? GenerativeNvidiaMetadata_Usage.fromPartial(object.usage)
        : undefined;
    return message;
  },
};
function createBaseGenerativeNvidiaMetadata_Usage() {
  return { promptTokens: undefined, completionTokens: undefined, totalTokens: undefined };
}
export const GenerativeNvidiaMetadata_Usage = {
  encode(message, writer = _m0.Writer.create()) {
    if (message.promptTokens !== undefined) {
      writer.uint32(8).int64(message.promptTokens);
    }
    if (message.completionTokens !== undefined) {
      writer.uint32(16).int64(message.completionTokens);
    }
    if (message.totalTokens !== undefined) {
      writer.uint32(24).int64(message.totalTokens);
    }
    return writer;
  },
  decode(input, length) {
    const reader = input instanceof _m0.Reader ? input : _m0.Reader.create(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseGenerativeNvidiaMetadata_Usage();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1:
          if (tag !== 8) {
            break;
          }
          message.promptTokens = longToNumber(reader.int64());
          continue;
        case 2:
          if (tag !== 16) {
            break;
          }
          message.completionTokens = longToNumber(reader.int64());
          continue;
        case 3:
          if (tag !== 24) {
            break;
          }
          message.totalTokens = longToNumber(reader.int64());
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skipType(tag & 7);
    }
    return message;
  },
  fromJSON(object) {
    return {
      promptTokens: isSet(object.promptTokens) ? globalThis.Number(object.promptTokens) : undefined,
      completionTokens: isSet(object.completionTokens)
        ? globalThis.Number(object.completionTokens)
        : undefined,
      totalTokens: isSet(object.totalTokens) ? globalThis.Number(object.totalTokens) : undefined,
    };
  },
  toJSON(message) {
    const obj = {};
    if (message.promptTokens !== undefined) {
      obj.promptTokens = Math.round(message.promptTokens);
    }
    if (message.completionTokens !== undefined) {
      obj.completionTokens = Math.round(message.completionTokens);
    }
    if (message.totalTokens !== undefined) {
      obj.totalTokens = Math.round(message.totalTokens);
    }
    return obj;
  },
  create(base) {
    return GenerativeNvidiaMetadata_Usage.fromPartial(base !== null && base !== void 0 ? base : {});
  },
  fromPartial(object) {
    var _a, _b, _c;
    const message = createBaseGenerativeNvidiaMetadata_Usage();
    message.promptTokens = (_a = object.promptTokens) !== null && _a !== void 0 ? _a : undefined;
    message.completionTokens = (_b = object.completionTokens) !== null && _b !== void 0 ? _b : undefined;
    message.totalTokens = (_c = object.totalTokens) !== null && _c !== void 0 ? _c : undefined;
    return message;
  },
};
function createBaseGenerativeXAIMetadata() {
  return { usage: undefined };
}
export const GenerativeXAIMetadata = {
  encode(message, writer = _m0.Writer.create()) {
    if (message.usage !== undefined) {
      GenerativeXAIMetadata_Usage.encode(message.usage, writer.uint32(10).fork()).ldelim();
    }
    return writer;
  },
  decode(input, length) {
    const reader = input instanceof _m0.Reader ? input : _m0.Reader.create(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseGenerativeXAIMetadata();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1:
          if (tag !== 10) {
            break;
          }
          message.usage = GenerativeXAIMetadata_Usage.decode(reader, reader.uint32());
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skipType(tag & 7);
    }
    return message;
  },
  fromJSON(object) {
    return { usage: isSet(object.usage) ? GenerativeXAIMetadata_Usage.fromJSON(object.usage) : undefined };
  },
  toJSON(message) {
    const obj = {};
    if (message.usage !== undefined) {
      obj.usage = GenerativeXAIMetadata_Usage.toJSON(message.usage);
    }
    return obj;
  },
  create(base) {
    return GenerativeXAIMetadata.fromPartial(base !== null && base !== void 0 ? base : {});
  },
  fromPartial(object) {
    const message = createBaseGenerativeXAIMetadata();
    message.usage =
      object.usage !== undefined && object.usage !== null
        ? GenerativeXAIMetadata_Usage.fromPartial(object.usage)
        : undefined;
    return message;
  },
};
function createBaseGenerativeXAIMetadata_Usage() {
  return { promptTokens: undefined, completionTokens: undefined, totalTokens: undefined };
}
export const GenerativeXAIMetadata_Usage = {
  encode(message, writer = _m0.Writer.create()) {
    if (message.promptTokens !== undefined) {
      writer.uint32(8).int64(message.promptTokens);
    }
    if (message.completionTokens !== undefined) {
      writer.uint32(16).int64(message.completionTokens);
    }
    if (message.totalTokens !== undefined) {
      writer.uint32(24).int64(message.totalTokens);
    }
    return writer;
  },
  decode(input, length) {
    const reader = input instanceof _m0.Reader ? input : _m0.Reader.create(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseGenerativeXAIMetadata_Usage();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1:
          if (tag !== 8) {
            break;
          }
          message.promptTokens = longToNumber(reader.int64());
          continue;
        case 2:
          if (tag !== 16) {
            break;
          }
          message.completionTokens = longToNumber(reader.int64());
          continue;
        case 3:
          if (tag !== 24) {
            break;
          }
          message.totalTokens = longToNumber(reader.int64());
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skipType(tag & 7);
    }
    return message;
  },
  fromJSON(object) {
    return {
      promptTokens: isSet(object.promptTokens) ? globalThis.Number(object.promptTokens) : undefined,
      completionTokens: isSet(object.completionTokens)
        ? globalThis.Number(object.completionTokens)
        : undefined,
      totalTokens: isSet(object.totalTokens) ? globalThis.Number(object.totalTokens) : undefined,
    };
  },
  toJSON(message) {
    const obj = {};
    if (message.promptTokens !== undefined) {
      obj.promptTokens = Math.round(message.promptTokens);
    }
    if (message.completionTokens !== undefined) {
      obj.completionTokens = Math.round(message.completionTokens);
    }
    if (message.totalTokens !== undefined) {
      obj.totalTokens = Math.round(message.totalTokens);
    }
    return obj;
  },
  create(base) {
    return GenerativeXAIMetadata_Usage.fromPartial(base !== null && base !== void 0 ? base : {});
  },
  fromPartial(object) {
    var _a, _b, _c;
    const message = createBaseGenerativeXAIMetadata_Usage();
    message.promptTokens = (_a = object.promptTokens) !== null && _a !== void 0 ? _a : undefined;
    message.completionTokens = (_b = object.completionTokens) !== null && _b !== void 0 ? _b : undefined;
    message.totalTokens = (_c = object.totalTokens) !== null && _c !== void 0 ? _c : undefined;
    return message;
  },
};
function createBaseGenerativeMetadata() {
  return {
    anthropic: undefined,
    anyscale: undefined,
    aws: undefined,
    cohere: undefined,
    dummy: undefined,
    mistral: undefined,
    ollama: undefined,
    openai: undefined,
    google: undefined,
    databricks: undefined,
    friendliai: undefined,
    nvidia: undefined,
    xai: undefined,
  };
}
export const GenerativeMetadata = {
  encode(message, writer = _m0.Writer.create()) {
    if (message.anthropic !== undefined) {
      GenerativeAnthropicMetadata.encode(message.anthropic, writer.uint32(10).fork()).ldelim();
    }
    if (message.anyscale !== undefined) {
      GenerativeAnyscaleMetadata.encode(message.anyscale, writer.uint32(18).fork()).ldelim();
    }
    if (message.aws !== undefined) {
      GenerativeAWSMetadata.encode(message.aws, writer.uint32(26).fork()).ldelim();
    }
    if (message.cohere !== undefined) {
      GenerativeCohereMetadata.encode(message.cohere, writer.uint32(34).fork()).ldelim();
    }
    if (message.dummy !== undefined) {
      GenerativeDummyMetadata.encode(message.dummy, writer.uint32(42).fork()).ldelim();
    }
    if (message.mistral !== undefined) {
      GenerativeMistralMetadata.encode(message.mistral, writer.uint32(50).fork()).ldelim();
    }
    if (message.ollama !== undefined) {
      GenerativeOllamaMetadata.encode(message.ollama, writer.uint32(58).fork()).ldelim();
    }
    if (message.openai !== undefined) {
      GenerativeOpenAIMetadata.encode(message.openai, writer.uint32(66).fork()).ldelim();
    }
    if (message.google !== undefined) {
      GenerativeGoogleMetadata.encode(message.google, writer.uint32(74).fork()).ldelim();
    }
    if (message.databricks !== undefined) {
      GenerativeDatabricksMetadata.encode(message.databricks, writer.uint32(82).fork()).ldelim();
    }
    if (message.friendliai !== undefined) {
      GenerativeFriendliAIMetadata.encode(message.friendliai, writer.uint32(90).fork()).ldelim();
    }
    if (message.nvidia !== undefined) {
      GenerativeNvidiaMetadata.encode(message.nvidia, writer.uint32(98).fork()).ldelim();
    }
    if (message.xai !== undefined) {
      GenerativeXAIMetadata.encode(message.xai, writer.uint32(106).fork()).ldelim();
    }
    return writer;
  },
  decode(input, length) {
    const reader = input instanceof _m0.Reader ? input : _m0.Reader.create(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseGenerativeMetadata();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1:
          if (tag !== 10) {
            break;
          }
          message.anthropic = GenerativeAnthropicMetadata.decode(reader, reader.uint32());
          continue;
        case 2:
          if (tag !== 18) {
            break;
          }
          message.anyscale = GenerativeAnyscaleMetadata.decode(reader, reader.uint32());
          continue;
        case 3:
          if (tag !== 26) {
            break;
          }
          message.aws = GenerativeAWSMetadata.decode(reader, reader.uint32());
          continue;
        case 4:
          if (tag !== 34) {
            break;
          }
          message.cohere = GenerativeCohereMetadata.decode(reader, reader.uint32());
          continue;
        case 5:
          if (tag !== 42) {
            break;
          }
          message.dummy = GenerativeDummyMetadata.decode(reader, reader.uint32());
          continue;
        case 6:
          if (tag !== 50) {
            break;
          }
          message.mistral = GenerativeMistralMetadata.decode(reader, reader.uint32());
          continue;
        case 7:
          if (tag !== 58) {
            break;
          }
          message.ollama = GenerativeOllamaMetadata.decode(reader, reader.uint32());
          continue;
        case 8:
          if (tag !== 66) {
            break;
          }
          message.openai = GenerativeOpenAIMetadata.decode(reader, reader.uint32());
          continue;
        case 9:
          if (tag !== 74) {
            break;
          }
          message.google = GenerativeGoogleMetadata.decode(reader, reader.uint32());
          continue;
        case 10:
          if (tag !== 82) {
            break;
          }
          message.databricks = GenerativeDatabricksMetadata.decode(reader, reader.uint32());
          continue;
        case 11:
          if (tag !== 90) {
            break;
          }
          message.friendliai = GenerativeFriendliAIMetadata.decode(reader, reader.uint32());
          continue;
        case 12:
          if (tag !== 98) {
            break;
          }
          message.nvidia = GenerativeNvidiaMetadata.decode(reader, reader.uint32());
          continue;
        case 13:
          if (tag !== 106) {
            break;
          }
          message.xai = GenerativeXAIMetadata.decode(reader, reader.uint32());
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skipType(tag & 7);
    }
    return message;
  },
  fromJSON(object) {
    return {
      anthropic: isSet(object.anthropic) ? GenerativeAnthropicMetadata.fromJSON(object.anthropic) : undefined,
      anyscale: isSet(object.anyscale) ? GenerativeAnyscaleMetadata.fromJSON(object.anyscale) : undefined,
      aws: isSet(object.aws) ? GenerativeAWSMetadata.fromJSON(object.aws) : undefined,
      cohere: isSet(object.cohere) ? GenerativeCohereMetadata.fromJSON(object.cohere) : undefined,
      dummy: isSet(object.dummy) ? GenerativeDummyMetadata.fromJSON(object.dummy) : undefined,
      mistral: isSet(object.mistral) ? GenerativeMistralMetadata.fromJSON(object.mistral) : undefined,
      ollama: isSet(object.ollama) ? GenerativeOllamaMetadata.fromJSON(object.ollama) : undefined,
      openai: isSet(object.openai) ? GenerativeOpenAIMetadata.fromJSON(object.openai) : undefined,
      google: isSet(object.google) ? GenerativeGoogleMetadata.fromJSON(object.google) : undefined,
      databricks: isSet(object.databricks)
        ? GenerativeDatabricksMetadata.fromJSON(object.databricks)
        : undefined,
      friendliai: isSet(object.friendliai)
        ? GenerativeFriendliAIMetadata.fromJSON(object.friendliai)
        : undefined,
      nvidia: isSet(object.nvidia) ? GenerativeNvidiaMetadata.fromJSON(object.nvidia) : undefined,
      xai: isSet(object.xai) ? GenerativeXAIMetadata.fromJSON(object.xai) : undefined,
    };
  },
  toJSON(message) {
    const obj = {};
    if (message.anthropic !== undefined) {
      obj.anthropic = GenerativeAnthropicMetadata.toJSON(message.anthropic);
    }
    if (message.anyscale !== undefined) {
      obj.anyscale = GenerativeAnyscaleMetadata.toJSON(message.anyscale);
    }
    if (message.aws !== undefined) {
      obj.aws = GenerativeAWSMetadata.toJSON(message.aws);
    }
    if (message.cohere !== undefined) {
      obj.cohere = GenerativeCohereMetadata.toJSON(message.cohere);
    }
    if (message.dummy !== undefined) {
      obj.dummy = GenerativeDummyMetadata.toJSON(message.dummy);
    }
    if (message.mistral !== undefined) {
      obj.mistral = GenerativeMistralMetadata.toJSON(message.mistral);
    }
    if (message.ollama !== undefined) {
      obj.ollama = GenerativeOllamaMetadata.toJSON(message.ollama);
    }
    if (message.openai !== undefined) {
      obj.openai = GenerativeOpenAIMetadata.toJSON(message.openai);
    }
    if (message.google !== undefined) {
      obj.google = GenerativeGoogleMetadata.toJSON(message.google);
    }
    if (message.databricks !== undefined) {
      obj.databricks = GenerativeDatabricksMetadata.toJSON(message.databricks);
    }
    if (message.friendliai !== undefined) {
      obj.friendliai = GenerativeFriendliAIMetadata.toJSON(message.friendliai);
    }
    if (message.nvidia !== undefined) {
      obj.nvidia = GenerativeNvidiaMetadata.toJSON(message.nvidia);
    }
    if (message.xai !== undefined) {
      obj.xai = GenerativeXAIMetadata.toJSON(message.xai);
    }
    return obj;
  },
  create(base) {
    return GenerativeMetadata.fromPartial(base !== null && base !== void 0 ? base : {});
  },
  fromPartial(object) {
    const message = createBaseGenerativeMetadata();
    message.anthropic =
      object.anthropic !== undefined && object.anthropic !== null
        ? GenerativeAnthropicMetadata.fromPartial(object.anthropic)
        : undefined;
    message.anyscale =
      object.anyscale !== undefined && object.anyscale !== null
        ? GenerativeAnyscaleMetadata.fromPartial(object.anyscale)
        : undefined;
    message.aws =
      object.aws !== undefined && object.aws !== null
        ? GenerativeAWSMetadata.fromPartial(object.aws)
        : undefined;
    message.cohere =
      object.cohere !== undefined && object.cohere !== null
        ? GenerativeCohereMetadata.fromPartial(object.cohere)
        : undefined;
    message.dummy =
      object.dummy !== undefined && object.dummy !== null
        ? GenerativeDummyMetadata.fromPartial(object.dummy)
        : undefined;
    message.mistral =
      object.mistral !== undefined && object.mistral !== null
        ? GenerativeMistralMetadata.fromPartial(object.mistral)
        : undefined;
    message.ollama =
      object.ollama !== undefined && object.ollama !== null
        ? GenerativeOllamaMetadata.fromPartial(object.ollama)
        : undefined;
    message.openai =
      object.openai !== undefined && object.openai !== null
        ? GenerativeOpenAIMetadata.fromPartial(object.openai)
        : undefined;
    message.google =
      object.google !== undefined && object.google !== null
        ? GenerativeGoogleMetadata.fromPartial(object.google)
        : undefined;
    message.databricks =
      object.databricks !== undefined && object.databricks !== null
        ? GenerativeDatabricksMetadata.fromPartial(object.databricks)
        : undefined;
    message.friendliai =
      object.friendliai !== undefined && object.friendliai !== null
        ? GenerativeFriendliAIMetadata.fromPartial(object.friendliai)
        : undefined;
    message.nvidia =
      object.nvidia !== undefined && object.nvidia !== null
        ? GenerativeNvidiaMetadata.fromPartial(object.nvidia)
        : undefined;
    message.xai =
      object.xai !== undefined && object.xai !== null
        ? GenerativeXAIMetadata.fromPartial(object.xai)
        : undefined;
    return message;
  },
};
function createBaseGenerativeReply() {
  return { result: '', debug: undefined, metadata: undefined };
}
export const GenerativeReply = {
  encode(message, writer = _m0.Writer.create()) {
    if (message.result !== '') {
      writer.uint32(10).string(message.result);
    }
    if (message.debug !== undefined) {
      GenerativeDebug.encode(message.debug, writer.uint32(18).fork()).ldelim();
    }
    if (message.metadata !== undefined) {
      GenerativeMetadata.encode(message.metadata, writer.uint32(26).fork()).ldelim();
    }
    return writer;
  },
  decode(input, length) {
    const reader = input instanceof _m0.Reader ? input : _m0.Reader.create(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseGenerativeReply();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1:
          if (tag !== 10) {
            break;
          }
          message.result = reader.string();
          continue;
        case 2:
          if (tag !== 18) {
            break;
          }
          message.debug = GenerativeDebug.decode(reader, reader.uint32());
          continue;
        case 3:
          if (tag !== 26) {
            break;
          }
          message.metadata = GenerativeMetadata.decode(reader, reader.uint32());
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skipType(tag & 7);
    }
    return message;
  },
  fromJSON(object) {
    return {
      result: isSet(object.result) ? globalThis.String(object.result) : '',
      debug: isSet(object.debug) ? GenerativeDebug.fromJSON(object.debug) : undefined,
      metadata: isSet(object.metadata) ? GenerativeMetadata.fromJSON(object.metadata) : undefined,
    };
  },
  toJSON(message) {
    const obj = {};
    if (message.result !== '') {
      obj.result = message.result;
    }
    if (message.debug !== undefined) {
      obj.debug = GenerativeDebug.toJSON(message.debug);
    }
    if (message.metadata !== undefined) {
      obj.metadata = GenerativeMetadata.toJSON(message.metadata);
    }
    return obj;
  },
  create(base) {
    return GenerativeReply.fromPartial(base !== null && base !== void 0 ? base : {});
  },
  fromPartial(object) {
    var _a;
    const message = createBaseGenerativeReply();
    message.result = (_a = object.result) !== null && _a !== void 0 ? _a : '';
    message.debug =
      object.debug !== undefined && object.debug !== null
        ? GenerativeDebug.fromPartial(object.debug)
        : undefined;
    message.metadata =
      object.metadata !== undefined && object.metadata !== null
        ? GenerativeMetadata.fromPartial(object.metadata)
        : undefined;
    return message;
  },
};
function createBaseGenerativeResult() {
  return { values: [] };
}
export const GenerativeResult = {
  encode(message, writer = _m0.Writer.create()) {
    for (const v of message.values) {
      GenerativeReply.encode(v, writer.uint32(10).fork()).ldelim();
    }
    return writer;
  },
  decode(input, length) {
    const reader = input instanceof _m0.Reader ? input : _m0.Reader.create(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseGenerativeResult();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1:
          if (tag !== 10) {
            break;
          }
          message.values.push(GenerativeReply.decode(reader, reader.uint32()));
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skipType(tag & 7);
    }
    return message;
  },
  fromJSON(object) {
    return {
      values: globalThis.Array.isArray(object === null || object === void 0 ? void 0 : object.values)
        ? object.values.map((e) => GenerativeReply.fromJSON(e))
        : [],
    };
  },
  toJSON(message) {
    var _a;
    const obj = {};
    if ((_a = message.values) === null || _a === void 0 ? void 0 : _a.length) {
      obj.values = message.values.map((e) => GenerativeReply.toJSON(e));
    }
    return obj;
  },
  create(base) {
    return GenerativeResult.fromPartial(base !== null && base !== void 0 ? base : {});
  },
  fromPartial(object) {
    var _a;
    const message = createBaseGenerativeResult();
    message.values =
      ((_a = object.values) === null || _a === void 0
        ? void 0
        : _a.map((e) => GenerativeReply.fromPartial(e))) || [];
    return message;
  },
};
function createBaseGenerativeDebug() {
  return { fullPrompt: undefined };
}
export const GenerativeDebug = {
  encode(message, writer = _m0.Writer.create()) {
    if (message.fullPrompt !== undefined) {
      writer.uint32(10).string(message.fullPrompt);
    }
    return writer;
  },
  decode(input, length) {
    const reader = input instanceof _m0.Reader ? input : _m0.Reader.create(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseGenerativeDebug();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1:
          if (tag !== 10) {
            break;
          }
          message.fullPrompt = reader.string();
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skipType(tag & 7);
    }
    return message;
  },
  fromJSON(object) {
    return { fullPrompt: isSet(object.fullPrompt) ? globalThis.String(object.fullPrompt) : undefined };
  },
  toJSON(message) {
    const obj = {};
    if (message.fullPrompt !== undefined) {
      obj.fullPrompt = message.fullPrompt;
    }
    return obj;
  },
  create(base) {
    return GenerativeDebug.fromPartial(base !== null && base !== void 0 ? base : {});
  },
  fromPartial(object) {
    var _a;
    const message = createBaseGenerativeDebug();
    message.fullPrompt = (_a = object.fullPrompt) !== null && _a !== void 0 ? _a : undefined;
    return message;
  },
};
function longToNumber(long) {
  if (long.gt(globalThis.Number.MAX_SAFE_INTEGER)) {
    throw new globalThis.Error('Value is larger than Number.MAX_SAFE_INTEGER');
  }
  return long.toNumber();
}
if (_m0.util.Long !== Long) {
  _m0.util.Long = Long;
  _m0.configure();
}
function isSet(value) {
  return value !== null && value !== undefined;
}
