import { Sorting } from './classes.js';
const sort = () => {
  return {
    byProperty(property, ascending = true) {
      return new Sorting().byProperty(property, ascending);
    },
    byId(ascending = true) {
      return new Sorting().byId(ascending);
    },
    byCreationTime(ascending = true) {
      return new Sorting().byCreationTime(ascending);
    },
    byUpdateTime(ascending = true) {
      return new Sorting().byUpdateTime(ascending);
    },
  };
};
export default sort;
export { Sorting };
