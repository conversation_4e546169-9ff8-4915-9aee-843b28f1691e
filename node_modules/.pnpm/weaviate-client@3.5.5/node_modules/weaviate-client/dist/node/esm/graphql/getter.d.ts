import Connection from '../connection/index.js';
import { ConsistencyLevel } from '../data/index.js';
import { WhereFilter } from '../openapi/types.js';
import { CommandBase } from '../validation/commandBase.js';
import { AskArgs } from './ask.js';
import { Bm25Args } from './bm25.js';
import { GenerateArgs } from './generate.js';
import { GroupArgs } from './group.js';
import { GroupByArgs } from './groupBy.js';
import { HybridArgs } from './hybrid.js';
import { NearImageArgs } from './nearImage.js';
import { NearAudioArgs, NearDepthArgs, NearIMUArgs, NearThermalArgs, NearVideoArgs } from './nearMedia.js';
import { NearObjectArgs } from './nearObject.js';
import { NearTextArgs } from './nearText.js';
import { NearVectorArgs } from './nearVector.js';
import { SortArgs } from './sort.js';
export { FusionType } from './hybrid.js';
export default class GraphQLGetter extends CommandBase {
  private after?;
  private askString?;
  private bm25String?;
  private className?;
  private fields?;
  private groupString?;
  private hybridString?;
  private includesNearMediaFilter;
  private limit?;
  private nearImageNotSet?;
  private nearMediaString?;
  private nearMediaType?;
  private nearObjectString?;
  private nearTextString?;
  private nearVectorString?;
  private offset?;
  private sortString?;
  private whereString?;
  private generateString?;
  private consistencyLevel?;
  private groupByString?;
  private tenant?;
  private autocut?;
  constructor(client: Connection);
  withFields: (fields: string) => this;
  withClassName: (className: string) => this;
  withAfter: (id: string) => this;
  withGroup: (args: GroupArgs) => this;
  withWhere: (whereObj: WhereFilter) => this;
  withNearText: (args: NearTextArgs) => this;
  withBm25: (args: Bm25Args) => this;
  withHybrid: (args: HybridArgs) => this;
  withNearObject: (args: NearObjectArgs) => this;
  withAsk: (askObj: AskArgs) => this;
  private withNearMedia;
  withNearImage: (args: NearImageArgs) => this;
  withNearAudio: (args: NearAudioArgs) => this;
  withNearVideo: (args: NearVideoArgs) => this;
  withNearThermal: (args: NearThermalArgs) => this;
  withNearDepth: (args: NearDepthArgs) => this;
  withNearIMU: (args: NearIMUArgs) => this;
  withNearVector: (args: NearVectorArgs) => this;
  withLimit: (limit: number) => this;
  withOffset: (offset: number) => this;
  withAutocut: (autocut: number) => this;
  withSort: (args: SortArgs[]) => this;
  withGenerate: (args: GenerateArgs) => this;
  withConsistencyLevel: (level: ConsistencyLevel) => this;
  withGroupBy: (args: GroupByArgs) => this;
  withTenant: (tenant: string) => this;
  validateIsSet: (prop: string | undefined | null, name: string, setter: string) => void;
  validate: () => void;
  do: () => Promise<{
    data: any;
  }>;
}
