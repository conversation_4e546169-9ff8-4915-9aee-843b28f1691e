import {
  AggregateFetchArgs,
  AggregateHybridArgs,
  AggregateNearImageArgs,
  AggregateNearObjectArgs,
  AggregateNearTextArgs,
  AggregateNearVectorArgs,
} from '../../grpc/aggregator.js';
import {
  SearchBm25Args,
  SearchFetchArgs,
  SearchHybridArgs,
  SearchNearAudioArgs,
  SearchNearDepthArgs,
  SearchNearIMUArgs,
  SearchNearImageArgs,
  SearchNearObjectArgs,
  SearchNearTextArgs,
  SearchNearThermalArgs,
  SearchNearVectorArgs,
  SearchNearVideoArgs,
} from '../../grpc/searcher.js';
import { WhereFilter } from '../../openapi/types.js';
import { AggregateRequest_GroupBy } from '../../proto/v1/aggregate.js';
import { Filters as FiltersGRPC } from '../../proto/v1/base.js';
import {
  BM25,
  Hybrid,
  NearAudioSearch,
  NearDepthSearch,
  NearIMUSearch,
  NearImageSearch,
  NearObject,
  NearTextSearch,
  NearThermalSearch,
  NearVector,
  NearVideoSearch,
  Targets,
} from '../../proto/v1/base_search.js';
import { GenerativeSearch } from '../../proto/v1/generative.js';
import { GroupBy } from '../../proto/v1/search_get.js';
import { FilterValue } from '../filters/index.js';
import {
  AggregateBaseOptions,
  AggregateHybridOptions,
  AggregateNearOptions,
  GenerativeConfigRuntime,
  GroupByAggregate,
  GroupedTask,
  PropertiesMetrics,
  SinglePrompt,
} from '../index.js';
import {
  BaseNearOptions,
  Bm25Options,
  Bm25SearchOptions,
  FetchObjectByIdOptions,
  FetchObjectsOptions,
  HybridNearTextSubSearch,
  HybridNearVectorSubSearch,
  HybridOptions,
  HybridSearchOptions,
  NearOptions,
  NearTextOptions,
  NearVectorInputType,
  TargetVectorInputType,
} from '../query/types.js';
import { TenantBC, TenantCreate, TenantUpdate } from '../tenants/types.js';
import {
  BatchObjects,
  DataObject,
  GenerateOptions,
  GroupByOptions,
  MetadataKeys,
  NestedProperties,
  NonReferenceInputs,
  PhoneNumberInput,
  QueryMetadata,
  ReferenceInput,
  WeaviateField,
} from '../types/index.js';
export declare class DataGuards {
  static isText: (argument?: WeaviateField) => argument is string;
  static isTextArray: (argument?: WeaviateField) => argument is string[];
  static isInt: (argument?: WeaviateField) => argument is number;
  static isIntArray: (argument?: WeaviateField) => argument is number[];
  static isFloat: (argument?: WeaviateField) => argument is number;
  static isFloatArray: (argument?: WeaviateField) => argument is number[];
  static isBoolean: (argument?: WeaviateField) => argument is boolean;
  static isBooleanArray: (argument?: WeaviateField) => argument is boolean[];
  static isDate: (argument?: WeaviateField) => argument is Date;
  static isDateArray: (argument?: WeaviateField) => argument is Date[];
  static isGeoCoordinate: (
    argument?: WeaviateField
  ) => argument is Required<import('../../proto/v1/properties.js').GeoCoordinate>;
  static isPhoneNumber: (argument?: WeaviateField) => argument is PhoneNumberInput;
  static isNested: (argument?: WeaviateField) => argument is NestedProperties;
  static isNestedArray: (argument?: WeaviateField) => argument is NestedProperties[];
  static isEmptyArray: (argument?: WeaviateField) => argument is [];
  static isDataObject: <T>(obj: DataObject<T> | NonReferenceInputs<T>) => obj is DataObject<T>;
}
export declare class MetadataGuards {
  static isKeys: (argument?: QueryMetadata) => argument is MetadataKeys;
  static isAll: (argument?: QueryMetadata) => argument is 'all';
  static isUndefined: (argument?: QueryMetadata) => argument is undefined;
}
declare class Aggregate {
  private static aggregations;
  private static common;
  static groupBy: <T>(groupBy?: GroupByAggregate<T> | undefined) => AggregateRequest_GroupBy;
  static hybrid: (
    query: string,
    opts?: AggregateHybridOptions<any, PropertiesMetrics<any>>
  ) => AggregateHybridArgs;
  static nearImage: (
    image: string,
    opts?: AggregateNearOptions<PropertiesMetrics<any>>
  ) => AggregateNearImageArgs;
  static nearObject: (
    id: string,
    opts?: AggregateNearOptions<PropertiesMetrics<any>>
  ) => AggregateNearObjectArgs;
  static nearText: (
    query: string | string[],
    opts?: AggregateNearOptions<PropertiesMetrics<any>>
  ) => AggregateNearTextArgs;
  static nearVector: (
    vector: NearVectorInputType,
    opts?: AggregateNearOptions<PropertiesMetrics<any>>
  ) => AggregateNearVectorArgs;
  static overAll: (opts?: AggregateBaseOptions<PropertiesMetrics<any>>) => AggregateFetchArgs;
}
declare class Search {
  private static queryProperties;
  private static metadata;
  private static sortBy;
  private static rerank;
  static groupBy: <T>(groupBy?: GroupByOptions<T> | undefined) => GroupBy;
  static isGroupBy: <T>(args: any) => args is T;
  private static common;
  static bm25: <T>(query: string, opts?: Bm25Options<T>) => SearchBm25Args;
  static fetchObjects: <T>(args?: FetchObjectsOptions<T> | undefined) => SearchFetchArgs;
  static fetchObjectById: <T>(
    args: {
      id: string;
    } & FetchObjectByIdOptions<T>
  ) => SearchFetchArgs;
  static hybrid: <T>(
    args: {
      query: string;
      supportsTargets: boolean;
      supportsVectorsForTargets: boolean;
      supportsWeightsForTargets: boolean;
    },
    opts?: HybridOptions<T>
  ) => SearchHybridArgs;
  static nearAudio: <T>(
    args: {
      audio: string;
      supportsTargets: boolean;
      supportsWeightsForTargets: boolean;
    },
    opts?: NearOptions<T>
  ) => SearchNearAudioArgs;
  static nearDepth: <T>(
    args: {
      depth: string;
      supportsTargets: boolean;
      supportsWeightsForTargets: boolean;
    },
    opts?: NearOptions<T>
  ) => SearchNearDepthArgs;
  static nearImage: <T>(
    args: {
      image: string;
      supportsTargets: boolean;
      supportsWeightsForTargets: boolean;
    },
    opts?: NearOptions<T>
  ) => SearchNearImageArgs;
  static nearIMU: <T>(
    args: {
      imu: string;
      supportsTargets: boolean;
      supportsWeightsForTargets: boolean;
    },
    opts?: NearOptions<T>
  ) => SearchNearIMUArgs;
  static nearObject: <T>(
    args: {
      id: string;
      supportsTargets: boolean;
      supportsWeightsForTargets: boolean;
    },
    opts?: NearOptions<T>
  ) => SearchNearObjectArgs;
  static nearText: <T>(
    args: {
      query: string | string[];
      supportsTargets: boolean;
      supportsWeightsForTargets: boolean;
    },
    opts?: NearTextOptions<T>
  ) => SearchNearTextArgs;
  static nearThermal: <T>(
    args: {
      thermal: string;
      supportsTargets: boolean;
      supportsWeightsForTargets: boolean;
    },
    opts?: NearOptions<T>
  ) => SearchNearThermalArgs;
  static nearVector: <T>(
    args: {
      vector: NearVectorInputType;
      supportsTargets: boolean;
      supportsVectorsForTargets: boolean;
      supportsWeightsForTargets: boolean;
    },
    opts?: NearOptions<T>
  ) => SearchNearVectorArgs;
  static nearVideo: <T>(
    args: {
      video: string;
      supportsTargets: boolean;
      supportsWeightsForTargets: boolean;
    },
    opts?: NearOptions<T>
  ) => SearchNearVideoArgs;
}
export declare class Serialize {
  static aggregate: typeof Aggregate;
  static search: typeof Search;
  static isNamedVectors: <T>(opts?: BaseNearOptions<T> | undefined) => boolean;
  static isMultiTarget: <T>(opts?: BaseNearOptions<T> | undefined) => boolean;
  static isMultiWeightPerTarget: <T>(opts?: BaseNearOptions<T> | undefined) => boolean;
  static isMultiVector: (vec?: NearVectorInputType) => boolean;
  static isMultiVectorPerTarget: (vec?: NearVectorInputType) => boolean;
  private static generativeQuery;
  static generative: <T>(
    args: {
      supportsSingleGrouped: boolean;
    },
    opts?: GenerateOptions<T, GenerativeConfigRuntime | undefined> | undefined
  ) => Promise<GenerativeSearch>;
  static isSinglePrompt(arg?: string | SinglePrompt): arg is SinglePrompt;
  static isGroupedTask<T>(arg?: string | GroupedTask<T>): arg is GroupedTask<T>;
  private static bm25QueryProperties;
  static bm25Search: <T>(
    args: {
      query: string;
    } & Bm25SearchOptions<T>
  ) => BM25;
  static isHybridVectorSearch: <T>(
    vector: NearVectorInputType | HybridNearTextSubSearch | HybridNearVectorSubSearch | undefined
  ) => vector is number[] | Record<string, number[] | number[][]>;
  static isHybridNearTextSearch: <T>(
    vector: NearVectorInputType | HybridNearTextSubSearch | HybridNearVectorSubSearch | undefined
  ) => vector is HybridNearTextSubSearch;
  static isHybridNearVectorSearch: <T>(
    vector: NearVectorInputType | HybridNearTextSubSearch | HybridNearVectorSubSearch | undefined
  ) => vector is HybridNearVectorSubSearch;
  private static hybridVector;
  static hybridSearch: <T>(
    args: {
      query: string;
      supportsTargets: boolean;
      supportsVectorsForTargets: boolean;
      supportsWeightsForTargets: boolean;
    } & HybridSearchOptions<T>
  ) => Hybrid;
  static nearAudioSearch: <T>(
    args: {
      audio: string;
      supportsTargets: boolean;
      supportsWeightsForTargets: boolean;
    } & NearOptions<T>
  ) => NearAudioSearch;
  static nearDepthSearch: <T>(
    args: {
      depth: string;
      supportsTargets: boolean;
      supportsWeightsForTargets: boolean;
    } & NearOptions<T>
  ) => NearDepthSearch;
  static nearImageSearch: <T>(
    args: {
      image: string;
      supportsTargets: boolean;
      supportsWeightsForTargets: boolean;
    } & NearOptions<T>
  ) => NearImageSearch;
  static nearIMUSearch: <T>(
    args: {
      imu: string;
      supportsTargets: boolean;
      supportsWeightsForTargets: boolean;
    } & NearOptions<T>
  ) => NearIMUSearch;
  static nearObjectSearch: <T>(
    args: {
      id: string;
      supportsTargets: boolean;
      supportsWeightsForTargets: boolean;
    } & NearOptions<T>
  ) => NearObject;
  static nearTextSearch: (args: {
    query: string | string[];
    supportsTargets: boolean;
    supportsWeightsForTargets: boolean;
    targetVector?: TargetVectorInputType;
    certainty?: number;
    distance?: number;
    moveAway?: {
      concepts?: string[];
      force?: number;
      objects?: string[];
    };
    moveTo?: {
      concepts?: string[];
      force?: number;
      objects?: string[];
    };
  }) => NearTextSearch;
  static nearThermalSearch: <T>(
    args: {
      thermal: string;
      supportsTargets: boolean;
      supportsWeightsForTargets: boolean;
    } & NearOptions<T>
  ) => NearThermalSearch;
  private static vectorToBytes;
  static nearVectorSearch: (args: {
    vector: NearVectorInputType;
    supportsTargets: boolean;
    supportsVectorsForTargets: boolean;
    supportsWeightsForTargets: boolean;
    certainty?: number;
    distance?: number;
    targetVector?: TargetVectorInputType;
  }) => NearVector;
  static targetVector: (args: {
    supportsTargets: boolean;
    supportsWeightsForTargets: boolean;
    targetVector?: TargetVectorInputType;
  }) => {
    targets?: Targets;
    targetVectors?: string[];
  };
  private static vectors;
  private static targets;
  static nearVideoSearch: <T>(
    args: {
      video: string;
      supportsTargets: boolean;
      supportsWeightsForTargets: boolean;
    } & NearOptions<T>
  ) => NearVideoSearch;
  static filtersGRPC: (filters: FilterValue) => FiltersGRPC;
  private static filtersGRPCValueText;
  private static filtersGRPCValueTextArray;
  private static filterTargetToREST;
  static filtersREST: (filters: FilterValue) => WhereFilter;
  private static operator;
  static restProperties: (
    properties: Record<string, WeaviateField | undefined>,
    references?: Record<string, ReferenceInput<any> | undefined>
  ) => Record<string, any>;
  private static batchProperties;
  static batchObjects: <T>(
    collection: string,
    objects: (DataObject<T> | NonReferenceInputs<T>)[],
    requiresInsertFix: boolean,
    tenant?: string
  ) => Promise<BatchObjects<T>>;
  static tenants<T, M>(tenants: T[], mapper: (tenant: T) => M): M[][];
  static tenantCreate<T extends TenantBC | TenantCreate>(
    tenant: T
  ): {
    name: string;
    activityStatus?: 'HOT' | 'COLD';
  };
  static tenantUpdate<T extends TenantBC | TenantUpdate>(
    tenant: T
  ): {
    name: string;
    activityStatus: 'HOT' | 'COLD' | 'FROZEN';
  };
}
export {};
