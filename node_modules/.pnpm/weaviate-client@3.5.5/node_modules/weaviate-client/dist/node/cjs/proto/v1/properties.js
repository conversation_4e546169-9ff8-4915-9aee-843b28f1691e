'use strict';
// Code generated by protoc-gen-ts_proto. DO NOT EDIT.
// versions:
//   protoc-gen-ts_proto  v1.176.0
//   protoc               v3.19.1
// source: v1/properties.proto
var __importDefault =
  (this && this.__importDefault) ||
  function (mod) {
    return mod && mod.__esModule ? mod : { default: mod };
  };
Object.defineProperty(exports, '__esModule', { value: true });
exports.PhoneNumber =
  exports.GeoCoordinate =
  exports.IntValues =
  exports.UuidValues =
  exports.DateValues =
  exports.ObjectValues =
  exports.BoolValues =
  exports.TextValues =
  exports.NumberValues =
  exports.ListValue =
  exports.Value =
  exports.Properties_FieldsEntry =
  exports.Properties =
  exports.protobufPackage =
    void 0;
/* eslint-disable */
const long_1 = __importDefault(require('long'));
const minimal_js_1 = __importDefault(require('protobufjs/minimal.js'));
const struct_js_1 = require('../google/protobuf/struct.js');
exports.protobufPackage = 'weaviate.v1';
function createBaseProperties() {
  return { fields: {} };
}
exports.Properties = {
  encode(message, writer = minimal_js_1.default.Writer.create()) {
    Object.entries(message.fields).forEach(([key, value]) => {
      exports.Properties_FieldsEntry.encode({ key: key, value }, writer.uint32(10).fork()).ldelim();
    });
    return writer;
  },
  decode(input, length) {
    const reader =
      input instanceof minimal_js_1.default.Reader ? input : minimal_js_1.default.Reader.create(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseProperties();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1:
          if (tag !== 10) {
            break;
          }
          const entry1 = exports.Properties_FieldsEntry.decode(reader, reader.uint32());
          if (entry1.value !== undefined) {
            message.fields[entry1.key] = entry1.value;
          }
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skipType(tag & 7);
    }
    return message;
  },
  fromJSON(object) {
    return {
      fields: isObject(object.fields)
        ? Object.entries(object.fields).reduce((acc, [key, value]) => {
            acc[key] = exports.Value.fromJSON(value);
            return acc;
          }, {})
        : {},
    };
  },
  toJSON(message) {
    const obj = {};
    if (message.fields) {
      const entries = Object.entries(message.fields);
      if (entries.length > 0) {
        obj.fields = {};
        entries.forEach(([k, v]) => {
          obj.fields[k] = exports.Value.toJSON(v);
        });
      }
    }
    return obj;
  },
  create(base) {
    return exports.Properties.fromPartial(base !== null && base !== void 0 ? base : {});
  },
  fromPartial(object) {
    var _a;
    const message = createBaseProperties();
    message.fields = Object.entries((_a = object.fields) !== null && _a !== void 0 ? _a : {}).reduce(
      (acc, [key, value]) => {
        if (value !== undefined) {
          acc[key] = exports.Value.fromPartial(value);
        }
        return acc;
      },
      {}
    );
    return message;
  },
};
function createBaseProperties_FieldsEntry() {
  return { key: '', value: undefined };
}
exports.Properties_FieldsEntry = {
  encode(message, writer = minimal_js_1.default.Writer.create()) {
    if (message.key !== '') {
      writer.uint32(10).string(message.key);
    }
    if (message.value !== undefined) {
      exports.Value.encode(message.value, writer.uint32(18).fork()).ldelim();
    }
    return writer;
  },
  decode(input, length) {
    const reader =
      input instanceof minimal_js_1.default.Reader ? input : minimal_js_1.default.Reader.create(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseProperties_FieldsEntry();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1:
          if (tag !== 10) {
            break;
          }
          message.key = reader.string();
          continue;
        case 2:
          if (tag !== 18) {
            break;
          }
          message.value = exports.Value.decode(reader, reader.uint32());
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skipType(tag & 7);
    }
    return message;
  },
  fromJSON(object) {
    return {
      key: isSet(object.key) ? globalThis.String(object.key) : '',
      value: isSet(object.value) ? exports.Value.fromJSON(object.value) : undefined,
    };
  },
  toJSON(message) {
    const obj = {};
    if (message.key !== '') {
      obj.key = message.key;
    }
    if (message.value !== undefined) {
      obj.value = exports.Value.toJSON(message.value);
    }
    return obj;
  },
  create(base) {
    return exports.Properties_FieldsEntry.fromPartial(base !== null && base !== void 0 ? base : {});
  },
  fromPartial(object) {
    var _a;
    const message = createBaseProperties_FieldsEntry();
    message.key = (_a = object.key) !== null && _a !== void 0 ? _a : '';
    message.value =
      object.value !== undefined && object.value !== null
        ? exports.Value.fromPartial(object.value)
        : undefined;
    return message;
  },
};
function createBaseValue() {
  return {
    numberValue: undefined,
    stringValue: undefined,
    boolValue: undefined,
    objectValue: undefined,
    listValue: undefined,
    dateValue: undefined,
    uuidValue: undefined,
    intValue: undefined,
    geoValue: undefined,
    blobValue: undefined,
    phoneValue: undefined,
    nullValue: undefined,
    textValue: undefined,
  };
}
exports.Value = {
  encode(message, writer = minimal_js_1.default.Writer.create()) {
    if (message.numberValue !== undefined) {
      writer.uint32(9).double(message.numberValue);
    }
    if (message.stringValue !== undefined) {
      writer.uint32(18).string(message.stringValue);
    }
    if (message.boolValue !== undefined) {
      writer.uint32(24).bool(message.boolValue);
    }
    if (message.objectValue !== undefined) {
      exports.Properties.encode(message.objectValue, writer.uint32(34).fork()).ldelim();
    }
    if (message.listValue !== undefined) {
      exports.ListValue.encode(message.listValue, writer.uint32(42).fork()).ldelim();
    }
    if (message.dateValue !== undefined) {
      writer.uint32(50).string(message.dateValue);
    }
    if (message.uuidValue !== undefined) {
      writer.uint32(58).string(message.uuidValue);
    }
    if (message.intValue !== undefined) {
      writer.uint32(64).int64(message.intValue);
    }
    if (message.geoValue !== undefined) {
      exports.GeoCoordinate.encode(message.geoValue, writer.uint32(74).fork()).ldelim();
    }
    if (message.blobValue !== undefined) {
      writer.uint32(82).string(message.blobValue);
    }
    if (message.phoneValue !== undefined) {
      exports.PhoneNumber.encode(message.phoneValue, writer.uint32(90).fork()).ldelim();
    }
    if (message.nullValue !== undefined) {
      writer.uint32(96).int32(message.nullValue);
    }
    if (message.textValue !== undefined) {
      writer.uint32(106).string(message.textValue);
    }
    return writer;
  },
  decode(input, length) {
    const reader =
      input instanceof minimal_js_1.default.Reader ? input : minimal_js_1.default.Reader.create(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseValue();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1:
          if (tag !== 9) {
            break;
          }
          message.numberValue = reader.double();
          continue;
        case 2:
          if (tag !== 18) {
            break;
          }
          message.stringValue = reader.string();
          continue;
        case 3:
          if (tag !== 24) {
            break;
          }
          message.boolValue = reader.bool();
          continue;
        case 4:
          if (tag !== 34) {
            break;
          }
          message.objectValue = exports.Properties.decode(reader, reader.uint32());
          continue;
        case 5:
          if (tag !== 42) {
            break;
          }
          message.listValue = exports.ListValue.decode(reader, reader.uint32());
          continue;
        case 6:
          if (tag !== 50) {
            break;
          }
          message.dateValue = reader.string();
          continue;
        case 7:
          if (tag !== 58) {
            break;
          }
          message.uuidValue = reader.string();
          continue;
        case 8:
          if (tag !== 64) {
            break;
          }
          message.intValue = longToNumber(reader.int64());
          continue;
        case 9:
          if (tag !== 74) {
            break;
          }
          message.geoValue = exports.GeoCoordinate.decode(reader, reader.uint32());
          continue;
        case 10:
          if (tag !== 82) {
            break;
          }
          message.blobValue = reader.string();
          continue;
        case 11:
          if (tag !== 90) {
            break;
          }
          message.phoneValue = exports.PhoneNumber.decode(reader, reader.uint32());
          continue;
        case 12:
          if (tag !== 96) {
            break;
          }
          message.nullValue = reader.int32();
          continue;
        case 13:
          if (tag !== 106) {
            break;
          }
          message.textValue = reader.string();
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skipType(tag & 7);
    }
    return message;
  },
  fromJSON(object) {
    return {
      numberValue: isSet(object.numberValue) ? globalThis.Number(object.numberValue) : undefined,
      stringValue: isSet(object.stringValue) ? globalThis.String(object.stringValue) : undefined,
      boolValue: isSet(object.boolValue) ? globalThis.Boolean(object.boolValue) : undefined,
      objectValue: isSet(object.objectValue) ? exports.Properties.fromJSON(object.objectValue) : undefined,
      listValue: isSet(object.listValue) ? exports.ListValue.fromJSON(object.listValue) : undefined,
      dateValue: isSet(object.dateValue) ? globalThis.String(object.dateValue) : undefined,
      uuidValue: isSet(object.uuidValue) ? globalThis.String(object.uuidValue) : undefined,
      intValue: isSet(object.intValue) ? globalThis.Number(object.intValue) : undefined,
      geoValue: isSet(object.geoValue) ? exports.GeoCoordinate.fromJSON(object.geoValue) : undefined,
      blobValue: isSet(object.blobValue) ? globalThis.String(object.blobValue) : undefined,
      phoneValue: isSet(object.phoneValue) ? exports.PhoneNumber.fromJSON(object.phoneValue) : undefined,
      nullValue: isSet(object.nullValue) ? (0, struct_js_1.nullValueFromJSON)(object.nullValue) : undefined,
      textValue: isSet(object.textValue) ? globalThis.String(object.textValue) : undefined,
    };
  },
  toJSON(message) {
    const obj = {};
    if (message.numberValue !== undefined) {
      obj.numberValue = message.numberValue;
    }
    if (message.stringValue !== undefined) {
      obj.stringValue = message.stringValue;
    }
    if (message.boolValue !== undefined) {
      obj.boolValue = message.boolValue;
    }
    if (message.objectValue !== undefined) {
      obj.objectValue = exports.Properties.toJSON(message.objectValue);
    }
    if (message.listValue !== undefined) {
      obj.listValue = exports.ListValue.toJSON(message.listValue);
    }
    if (message.dateValue !== undefined) {
      obj.dateValue = message.dateValue;
    }
    if (message.uuidValue !== undefined) {
      obj.uuidValue = message.uuidValue;
    }
    if (message.intValue !== undefined) {
      obj.intValue = Math.round(message.intValue);
    }
    if (message.geoValue !== undefined) {
      obj.geoValue = exports.GeoCoordinate.toJSON(message.geoValue);
    }
    if (message.blobValue !== undefined) {
      obj.blobValue = message.blobValue;
    }
    if (message.phoneValue !== undefined) {
      obj.phoneValue = exports.PhoneNumber.toJSON(message.phoneValue);
    }
    if (message.nullValue !== undefined) {
      obj.nullValue = (0, struct_js_1.nullValueToJSON)(message.nullValue);
    }
    if (message.textValue !== undefined) {
      obj.textValue = message.textValue;
    }
    return obj;
  },
  create(base) {
    return exports.Value.fromPartial(base !== null && base !== void 0 ? base : {});
  },
  fromPartial(object) {
    var _a, _b, _c, _d, _e, _f, _g, _h, _j;
    const message = createBaseValue();
    message.numberValue = (_a = object.numberValue) !== null && _a !== void 0 ? _a : undefined;
    message.stringValue = (_b = object.stringValue) !== null && _b !== void 0 ? _b : undefined;
    message.boolValue = (_c = object.boolValue) !== null && _c !== void 0 ? _c : undefined;
    message.objectValue =
      object.objectValue !== undefined && object.objectValue !== null
        ? exports.Properties.fromPartial(object.objectValue)
        : undefined;
    message.listValue =
      object.listValue !== undefined && object.listValue !== null
        ? exports.ListValue.fromPartial(object.listValue)
        : undefined;
    message.dateValue = (_d = object.dateValue) !== null && _d !== void 0 ? _d : undefined;
    message.uuidValue = (_e = object.uuidValue) !== null && _e !== void 0 ? _e : undefined;
    message.intValue = (_f = object.intValue) !== null && _f !== void 0 ? _f : undefined;
    message.geoValue =
      object.geoValue !== undefined && object.geoValue !== null
        ? exports.GeoCoordinate.fromPartial(object.geoValue)
        : undefined;
    message.blobValue = (_g = object.blobValue) !== null && _g !== void 0 ? _g : undefined;
    message.phoneValue =
      object.phoneValue !== undefined && object.phoneValue !== null
        ? exports.PhoneNumber.fromPartial(object.phoneValue)
        : undefined;
    message.nullValue = (_h = object.nullValue) !== null && _h !== void 0 ? _h : undefined;
    message.textValue = (_j = object.textValue) !== null && _j !== void 0 ? _j : undefined;
    return message;
  },
};
function createBaseListValue() {
  return {
    values: [],
    numberValues: undefined,
    boolValues: undefined,
    objectValues: undefined,
    dateValues: undefined,
    uuidValues: undefined,
    intValues: undefined,
    textValues: undefined,
  };
}
exports.ListValue = {
  encode(message, writer = minimal_js_1.default.Writer.create()) {
    for (const v of message.values) {
      exports.Value.encode(v, writer.uint32(10).fork()).ldelim();
    }
    if (message.numberValues !== undefined) {
      exports.NumberValues.encode(message.numberValues, writer.uint32(18).fork()).ldelim();
    }
    if (message.boolValues !== undefined) {
      exports.BoolValues.encode(message.boolValues, writer.uint32(26).fork()).ldelim();
    }
    if (message.objectValues !== undefined) {
      exports.ObjectValues.encode(message.objectValues, writer.uint32(34).fork()).ldelim();
    }
    if (message.dateValues !== undefined) {
      exports.DateValues.encode(message.dateValues, writer.uint32(42).fork()).ldelim();
    }
    if (message.uuidValues !== undefined) {
      exports.UuidValues.encode(message.uuidValues, writer.uint32(50).fork()).ldelim();
    }
    if (message.intValues !== undefined) {
      exports.IntValues.encode(message.intValues, writer.uint32(58).fork()).ldelim();
    }
    if (message.textValues !== undefined) {
      exports.TextValues.encode(message.textValues, writer.uint32(66).fork()).ldelim();
    }
    return writer;
  },
  decode(input, length) {
    const reader =
      input instanceof minimal_js_1.default.Reader ? input : minimal_js_1.default.Reader.create(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseListValue();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1:
          if (tag !== 10) {
            break;
          }
          message.values.push(exports.Value.decode(reader, reader.uint32()));
          continue;
        case 2:
          if (tag !== 18) {
            break;
          }
          message.numberValues = exports.NumberValues.decode(reader, reader.uint32());
          continue;
        case 3:
          if (tag !== 26) {
            break;
          }
          message.boolValues = exports.BoolValues.decode(reader, reader.uint32());
          continue;
        case 4:
          if (tag !== 34) {
            break;
          }
          message.objectValues = exports.ObjectValues.decode(reader, reader.uint32());
          continue;
        case 5:
          if (tag !== 42) {
            break;
          }
          message.dateValues = exports.DateValues.decode(reader, reader.uint32());
          continue;
        case 6:
          if (tag !== 50) {
            break;
          }
          message.uuidValues = exports.UuidValues.decode(reader, reader.uint32());
          continue;
        case 7:
          if (tag !== 58) {
            break;
          }
          message.intValues = exports.IntValues.decode(reader, reader.uint32());
          continue;
        case 8:
          if (tag !== 66) {
            break;
          }
          message.textValues = exports.TextValues.decode(reader, reader.uint32());
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skipType(tag & 7);
    }
    return message;
  },
  fromJSON(object) {
    return {
      values: globalThis.Array.isArray(object === null || object === void 0 ? void 0 : object.values)
        ? object.values.map((e) => exports.Value.fromJSON(e))
        : [],
      numberValues: isSet(object.numberValues)
        ? exports.NumberValues.fromJSON(object.numberValues)
        : undefined,
      boolValues: isSet(object.boolValues) ? exports.BoolValues.fromJSON(object.boolValues) : undefined,
      objectValues: isSet(object.objectValues)
        ? exports.ObjectValues.fromJSON(object.objectValues)
        : undefined,
      dateValues: isSet(object.dateValues) ? exports.DateValues.fromJSON(object.dateValues) : undefined,
      uuidValues: isSet(object.uuidValues) ? exports.UuidValues.fromJSON(object.uuidValues) : undefined,
      intValues: isSet(object.intValues) ? exports.IntValues.fromJSON(object.intValues) : undefined,
      textValues: isSet(object.textValues) ? exports.TextValues.fromJSON(object.textValues) : undefined,
    };
  },
  toJSON(message) {
    var _a;
    const obj = {};
    if ((_a = message.values) === null || _a === void 0 ? void 0 : _a.length) {
      obj.values = message.values.map((e) => exports.Value.toJSON(e));
    }
    if (message.numberValues !== undefined) {
      obj.numberValues = exports.NumberValues.toJSON(message.numberValues);
    }
    if (message.boolValues !== undefined) {
      obj.boolValues = exports.BoolValues.toJSON(message.boolValues);
    }
    if (message.objectValues !== undefined) {
      obj.objectValues = exports.ObjectValues.toJSON(message.objectValues);
    }
    if (message.dateValues !== undefined) {
      obj.dateValues = exports.DateValues.toJSON(message.dateValues);
    }
    if (message.uuidValues !== undefined) {
      obj.uuidValues = exports.UuidValues.toJSON(message.uuidValues);
    }
    if (message.intValues !== undefined) {
      obj.intValues = exports.IntValues.toJSON(message.intValues);
    }
    if (message.textValues !== undefined) {
      obj.textValues = exports.TextValues.toJSON(message.textValues);
    }
    return obj;
  },
  create(base) {
    return exports.ListValue.fromPartial(base !== null && base !== void 0 ? base : {});
  },
  fromPartial(object) {
    var _a;
    const message = createBaseListValue();
    message.values =
      ((_a = object.values) === null || _a === void 0
        ? void 0
        : _a.map((e) => exports.Value.fromPartial(e))) || [];
    message.numberValues =
      object.numberValues !== undefined && object.numberValues !== null
        ? exports.NumberValues.fromPartial(object.numberValues)
        : undefined;
    message.boolValues =
      object.boolValues !== undefined && object.boolValues !== null
        ? exports.BoolValues.fromPartial(object.boolValues)
        : undefined;
    message.objectValues =
      object.objectValues !== undefined && object.objectValues !== null
        ? exports.ObjectValues.fromPartial(object.objectValues)
        : undefined;
    message.dateValues =
      object.dateValues !== undefined && object.dateValues !== null
        ? exports.DateValues.fromPartial(object.dateValues)
        : undefined;
    message.uuidValues =
      object.uuidValues !== undefined && object.uuidValues !== null
        ? exports.UuidValues.fromPartial(object.uuidValues)
        : undefined;
    message.intValues =
      object.intValues !== undefined && object.intValues !== null
        ? exports.IntValues.fromPartial(object.intValues)
        : undefined;
    message.textValues =
      object.textValues !== undefined && object.textValues !== null
        ? exports.TextValues.fromPartial(object.textValues)
        : undefined;
    return message;
  },
};
function createBaseNumberValues() {
  return { values: new Uint8Array(0) };
}
exports.NumberValues = {
  encode(message, writer = minimal_js_1.default.Writer.create()) {
    if (message.values.length !== 0) {
      writer.uint32(10).bytes(message.values);
    }
    return writer;
  },
  decode(input, length) {
    const reader =
      input instanceof minimal_js_1.default.Reader ? input : minimal_js_1.default.Reader.create(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseNumberValues();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1:
          if (tag !== 10) {
            break;
          }
          message.values = reader.bytes();
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skipType(tag & 7);
    }
    return message;
  },
  fromJSON(object) {
    return { values: isSet(object.values) ? bytesFromBase64(object.values) : new Uint8Array(0) };
  },
  toJSON(message) {
    const obj = {};
    if (message.values.length !== 0) {
      obj.values = base64FromBytes(message.values);
    }
    return obj;
  },
  create(base) {
    return exports.NumberValues.fromPartial(base !== null && base !== void 0 ? base : {});
  },
  fromPartial(object) {
    var _a;
    const message = createBaseNumberValues();
    message.values = (_a = object.values) !== null && _a !== void 0 ? _a : new Uint8Array(0);
    return message;
  },
};
function createBaseTextValues() {
  return { values: [] };
}
exports.TextValues = {
  encode(message, writer = minimal_js_1.default.Writer.create()) {
    for (const v of message.values) {
      writer.uint32(10).string(v);
    }
    return writer;
  },
  decode(input, length) {
    const reader =
      input instanceof minimal_js_1.default.Reader ? input : minimal_js_1.default.Reader.create(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseTextValues();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1:
          if (tag !== 10) {
            break;
          }
          message.values.push(reader.string());
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skipType(tag & 7);
    }
    return message;
  },
  fromJSON(object) {
    return {
      values: globalThis.Array.isArray(object === null || object === void 0 ? void 0 : object.values)
        ? object.values.map((e) => globalThis.String(e))
        : [],
    };
  },
  toJSON(message) {
    var _a;
    const obj = {};
    if ((_a = message.values) === null || _a === void 0 ? void 0 : _a.length) {
      obj.values = message.values;
    }
    return obj;
  },
  create(base) {
    return exports.TextValues.fromPartial(base !== null && base !== void 0 ? base : {});
  },
  fromPartial(object) {
    var _a;
    const message = createBaseTextValues();
    message.values = ((_a = object.values) === null || _a === void 0 ? void 0 : _a.map((e) => e)) || [];
    return message;
  },
};
function createBaseBoolValues() {
  return { values: [] };
}
exports.BoolValues = {
  encode(message, writer = minimal_js_1.default.Writer.create()) {
    writer.uint32(10).fork();
    for (const v of message.values) {
      writer.bool(v);
    }
    writer.ldelim();
    return writer;
  },
  decode(input, length) {
    const reader =
      input instanceof minimal_js_1.default.Reader ? input : minimal_js_1.default.Reader.create(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseBoolValues();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1:
          if (tag === 8) {
            message.values.push(reader.bool());
            continue;
          }
          if (tag === 10) {
            const end2 = reader.uint32() + reader.pos;
            while (reader.pos < end2) {
              message.values.push(reader.bool());
            }
            continue;
          }
          break;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skipType(tag & 7);
    }
    return message;
  },
  fromJSON(object) {
    return {
      values: globalThis.Array.isArray(object === null || object === void 0 ? void 0 : object.values)
        ? object.values.map((e) => globalThis.Boolean(e))
        : [],
    };
  },
  toJSON(message) {
    var _a;
    const obj = {};
    if ((_a = message.values) === null || _a === void 0 ? void 0 : _a.length) {
      obj.values = message.values;
    }
    return obj;
  },
  create(base) {
    return exports.BoolValues.fromPartial(base !== null && base !== void 0 ? base : {});
  },
  fromPartial(object) {
    var _a;
    const message = createBaseBoolValues();
    message.values = ((_a = object.values) === null || _a === void 0 ? void 0 : _a.map((e) => e)) || [];
    return message;
  },
};
function createBaseObjectValues() {
  return { values: [] };
}
exports.ObjectValues = {
  encode(message, writer = minimal_js_1.default.Writer.create()) {
    for (const v of message.values) {
      exports.Properties.encode(v, writer.uint32(10).fork()).ldelim();
    }
    return writer;
  },
  decode(input, length) {
    const reader =
      input instanceof minimal_js_1.default.Reader ? input : minimal_js_1.default.Reader.create(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseObjectValues();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1:
          if (tag !== 10) {
            break;
          }
          message.values.push(exports.Properties.decode(reader, reader.uint32()));
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skipType(tag & 7);
    }
    return message;
  },
  fromJSON(object) {
    return {
      values: globalThis.Array.isArray(object === null || object === void 0 ? void 0 : object.values)
        ? object.values.map((e) => exports.Properties.fromJSON(e))
        : [],
    };
  },
  toJSON(message) {
    var _a;
    const obj = {};
    if ((_a = message.values) === null || _a === void 0 ? void 0 : _a.length) {
      obj.values = message.values.map((e) => exports.Properties.toJSON(e));
    }
    return obj;
  },
  create(base) {
    return exports.ObjectValues.fromPartial(base !== null && base !== void 0 ? base : {});
  },
  fromPartial(object) {
    var _a;
    const message = createBaseObjectValues();
    message.values =
      ((_a = object.values) === null || _a === void 0
        ? void 0
        : _a.map((e) => exports.Properties.fromPartial(e))) || [];
    return message;
  },
};
function createBaseDateValues() {
  return { values: [] };
}
exports.DateValues = {
  encode(message, writer = minimal_js_1.default.Writer.create()) {
    for (const v of message.values) {
      writer.uint32(10).string(v);
    }
    return writer;
  },
  decode(input, length) {
    const reader =
      input instanceof minimal_js_1.default.Reader ? input : minimal_js_1.default.Reader.create(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseDateValues();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1:
          if (tag !== 10) {
            break;
          }
          message.values.push(reader.string());
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skipType(tag & 7);
    }
    return message;
  },
  fromJSON(object) {
    return {
      values: globalThis.Array.isArray(object === null || object === void 0 ? void 0 : object.values)
        ? object.values.map((e) => globalThis.String(e))
        : [],
    };
  },
  toJSON(message) {
    var _a;
    const obj = {};
    if ((_a = message.values) === null || _a === void 0 ? void 0 : _a.length) {
      obj.values = message.values;
    }
    return obj;
  },
  create(base) {
    return exports.DateValues.fromPartial(base !== null && base !== void 0 ? base : {});
  },
  fromPartial(object) {
    var _a;
    const message = createBaseDateValues();
    message.values = ((_a = object.values) === null || _a === void 0 ? void 0 : _a.map((e) => e)) || [];
    return message;
  },
};
function createBaseUuidValues() {
  return { values: [] };
}
exports.UuidValues = {
  encode(message, writer = minimal_js_1.default.Writer.create()) {
    for (const v of message.values) {
      writer.uint32(10).string(v);
    }
    return writer;
  },
  decode(input, length) {
    const reader =
      input instanceof minimal_js_1.default.Reader ? input : minimal_js_1.default.Reader.create(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseUuidValues();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1:
          if (tag !== 10) {
            break;
          }
          message.values.push(reader.string());
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skipType(tag & 7);
    }
    return message;
  },
  fromJSON(object) {
    return {
      values: globalThis.Array.isArray(object === null || object === void 0 ? void 0 : object.values)
        ? object.values.map((e) => globalThis.String(e))
        : [],
    };
  },
  toJSON(message) {
    var _a;
    const obj = {};
    if ((_a = message.values) === null || _a === void 0 ? void 0 : _a.length) {
      obj.values = message.values;
    }
    return obj;
  },
  create(base) {
    return exports.UuidValues.fromPartial(base !== null && base !== void 0 ? base : {});
  },
  fromPartial(object) {
    var _a;
    const message = createBaseUuidValues();
    message.values = ((_a = object.values) === null || _a === void 0 ? void 0 : _a.map((e) => e)) || [];
    return message;
  },
};
function createBaseIntValues() {
  return { values: new Uint8Array(0) };
}
exports.IntValues = {
  encode(message, writer = minimal_js_1.default.Writer.create()) {
    if (message.values.length !== 0) {
      writer.uint32(10).bytes(message.values);
    }
    return writer;
  },
  decode(input, length) {
    const reader =
      input instanceof minimal_js_1.default.Reader ? input : minimal_js_1.default.Reader.create(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseIntValues();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1:
          if (tag !== 10) {
            break;
          }
          message.values = reader.bytes();
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skipType(tag & 7);
    }
    return message;
  },
  fromJSON(object) {
    return { values: isSet(object.values) ? bytesFromBase64(object.values) : new Uint8Array(0) };
  },
  toJSON(message) {
    const obj = {};
    if (message.values.length !== 0) {
      obj.values = base64FromBytes(message.values);
    }
    return obj;
  },
  create(base) {
    return exports.IntValues.fromPartial(base !== null && base !== void 0 ? base : {});
  },
  fromPartial(object) {
    var _a;
    const message = createBaseIntValues();
    message.values = (_a = object.values) !== null && _a !== void 0 ? _a : new Uint8Array(0);
    return message;
  },
};
function createBaseGeoCoordinate() {
  return { longitude: 0, latitude: 0 };
}
exports.GeoCoordinate = {
  encode(message, writer = minimal_js_1.default.Writer.create()) {
    if (message.longitude !== 0) {
      writer.uint32(13).float(message.longitude);
    }
    if (message.latitude !== 0) {
      writer.uint32(21).float(message.latitude);
    }
    return writer;
  },
  decode(input, length) {
    const reader =
      input instanceof minimal_js_1.default.Reader ? input : minimal_js_1.default.Reader.create(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseGeoCoordinate();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1:
          if (tag !== 13) {
            break;
          }
          message.longitude = reader.float();
          continue;
        case 2:
          if (tag !== 21) {
            break;
          }
          message.latitude = reader.float();
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skipType(tag & 7);
    }
    return message;
  },
  fromJSON(object) {
    return {
      longitude: isSet(object.longitude) ? globalThis.Number(object.longitude) : 0,
      latitude: isSet(object.latitude) ? globalThis.Number(object.latitude) : 0,
    };
  },
  toJSON(message) {
    const obj = {};
    if (message.longitude !== 0) {
      obj.longitude = message.longitude;
    }
    if (message.latitude !== 0) {
      obj.latitude = message.latitude;
    }
    return obj;
  },
  create(base) {
    return exports.GeoCoordinate.fromPartial(base !== null && base !== void 0 ? base : {});
  },
  fromPartial(object) {
    var _a, _b;
    const message = createBaseGeoCoordinate();
    message.longitude = (_a = object.longitude) !== null && _a !== void 0 ? _a : 0;
    message.latitude = (_b = object.latitude) !== null && _b !== void 0 ? _b : 0;
    return message;
  },
};
function createBasePhoneNumber() {
  return {
    countryCode: 0,
    defaultCountry: '',
    input: '',
    internationalFormatted: '',
    national: 0,
    nationalFormatted: '',
    valid: false,
  };
}
exports.PhoneNumber = {
  encode(message, writer = minimal_js_1.default.Writer.create()) {
    if (message.countryCode !== 0) {
      writer.uint32(8).uint64(message.countryCode);
    }
    if (message.defaultCountry !== '') {
      writer.uint32(18).string(message.defaultCountry);
    }
    if (message.input !== '') {
      writer.uint32(26).string(message.input);
    }
    if (message.internationalFormatted !== '') {
      writer.uint32(34).string(message.internationalFormatted);
    }
    if (message.national !== 0) {
      writer.uint32(40).uint64(message.national);
    }
    if (message.nationalFormatted !== '') {
      writer.uint32(50).string(message.nationalFormatted);
    }
    if (message.valid !== false) {
      writer.uint32(56).bool(message.valid);
    }
    return writer;
  },
  decode(input, length) {
    const reader =
      input instanceof minimal_js_1.default.Reader ? input : minimal_js_1.default.Reader.create(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBasePhoneNumber();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1:
          if (tag !== 8) {
            break;
          }
          message.countryCode = longToNumber(reader.uint64());
          continue;
        case 2:
          if (tag !== 18) {
            break;
          }
          message.defaultCountry = reader.string();
          continue;
        case 3:
          if (tag !== 26) {
            break;
          }
          message.input = reader.string();
          continue;
        case 4:
          if (tag !== 34) {
            break;
          }
          message.internationalFormatted = reader.string();
          continue;
        case 5:
          if (tag !== 40) {
            break;
          }
          message.national = longToNumber(reader.uint64());
          continue;
        case 6:
          if (tag !== 50) {
            break;
          }
          message.nationalFormatted = reader.string();
          continue;
        case 7:
          if (tag !== 56) {
            break;
          }
          message.valid = reader.bool();
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skipType(tag & 7);
    }
    return message;
  },
  fromJSON(object) {
    return {
      countryCode: isSet(object.countryCode) ? globalThis.Number(object.countryCode) : 0,
      defaultCountry: isSet(object.defaultCountry) ? globalThis.String(object.defaultCountry) : '',
      input: isSet(object.input) ? globalThis.String(object.input) : '',
      internationalFormatted: isSet(object.internationalFormatted)
        ? globalThis.String(object.internationalFormatted)
        : '',
      national: isSet(object.national) ? globalThis.Number(object.national) : 0,
      nationalFormatted: isSet(object.nationalFormatted) ? globalThis.String(object.nationalFormatted) : '',
      valid: isSet(object.valid) ? globalThis.Boolean(object.valid) : false,
    };
  },
  toJSON(message) {
    const obj = {};
    if (message.countryCode !== 0) {
      obj.countryCode = Math.round(message.countryCode);
    }
    if (message.defaultCountry !== '') {
      obj.defaultCountry = message.defaultCountry;
    }
    if (message.input !== '') {
      obj.input = message.input;
    }
    if (message.internationalFormatted !== '') {
      obj.internationalFormatted = message.internationalFormatted;
    }
    if (message.national !== 0) {
      obj.national = Math.round(message.national);
    }
    if (message.nationalFormatted !== '') {
      obj.nationalFormatted = message.nationalFormatted;
    }
    if (message.valid !== false) {
      obj.valid = message.valid;
    }
    return obj;
  },
  create(base) {
    return exports.PhoneNumber.fromPartial(base !== null && base !== void 0 ? base : {});
  },
  fromPartial(object) {
    var _a, _b, _c, _d, _e, _f, _g;
    const message = createBasePhoneNumber();
    message.countryCode = (_a = object.countryCode) !== null && _a !== void 0 ? _a : 0;
    message.defaultCountry = (_b = object.defaultCountry) !== null && _b !== void 0 ? _b : '';
    message.input = (_c = object.input) !== null && _c !== void 0 ? _c : '';
    message.internationalFormatted = (_d = object.internationalFormatted) !== null && _d !== void 0 ? _d : '';
    message.national = (_e = object.national) !== null && _e !== void 0 ? _e : 0;
    message.nationalFormatted = (_f = object.nationalFormatted) !== null && _f !== void 0 ? _f : '';
    message.valid = (_g = object.valid) !== null && _g !== void 0 ? _g : false;
    return message;
  },
};
function bytesFromBase64(b64) {
  if (globalThis.Buffer) {
    return Uint8Array.from(globalThis.Buffer.from(b64, 'base64'));
  } else {
    const bin = globalThis.atob(b64);
    const arr = new Uint8Array(bin.length);
    for (let i = 0; i < bin.length; ++i) {
      arr[i] = bin.charCodeAt(i);
    }
    return arr;
  }
}
function base64FromBytes(arr) {
  if (globalThis.Buffer) {
    return globalThis.Buffer.from(arr).toString('base64');
  } else {
    const bin = [];
    arr.forEach((byte) => {
      bin.push(globalThis.String.fromCharCode(byte));
    });
    return globalThis.btoa(bin.join(''));
  }
}
function longToNumber(long) {
  if (long.gt(globalThis.Number.MAX_SAFE_INTEGER)) {
    throw new globalThis.Error('Value is larger than Number.MAX_SAFE_INTEGER');
  }
  return long.toNumber();
}
if (minimal_js_1.default.util.Long !== long_1.default) {
  minimal_js_1.default.util.Long = long_1.default;
  minimal_js_1.default.configure();
}
function isObject(value) {
  return typeof value === 'object' && value !== null;
}
function isSet(value) {
  return value !== null && value !== undefined;
}
