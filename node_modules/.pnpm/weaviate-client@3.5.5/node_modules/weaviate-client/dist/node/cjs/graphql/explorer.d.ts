import Connection from '../connection/index.js';
import { CommandBase } from '../validation/commandBase.js';
import { AskArgs } from './ask.js';
import { NearImageArgs } from './nearImage.js';
import { NearAudioArgs, NearDepthArgs, NearIMUArgs, NearThermalArgs, NearVideoArgs } from './nearMedia.js';
import { NearObjectArgs } from './nearObject.js';
import { NearTextArgs } from './nearText.js';
import { NearVectorArgs } from './nearVector.js';
export default class Explorer extends CommandBase {
  private askString?;
  private fields?;
  private group?;
  private limit?;
  private includesNearMediaFilter;
  private nearMediaString?;
  private nearMediaType?;
  private nearObjectString?;
  private nearTextString?;
  private nearVectorString?;
  private params;
  constructor(client: Connection);
  withFields: (fields: string) => this;
  withLimit: (limit: number) => this;
  withNearText: (args: NearTextArgs) => this;
  withNearObject: (args: NearObjectArgs) => this;
  withAsk: (args: AskArgs) => this;
  private withNearMedia;
  withNearImage: (args: NearImageArgs) => this;
  withNearAudio: (args: NearAudioArgs) => this;
  withNearVideo: (args: NearVideoArgs) => this;
  withNearDepth: (args: NearDepthArgs) => this;
  withNearThermal: (args: NearThermalArgs) => this;
  withNearIMU: (args: NearIMUArgs) => this;
  withNearVector: (args: NearVectorArgs) => this;
  validateGroup: () => void;
  validateIsSet: (prop: string | undefined | null, name: string, setter: string) => void;
  validate: () => void;
  do: () => Promise<any>;
}
