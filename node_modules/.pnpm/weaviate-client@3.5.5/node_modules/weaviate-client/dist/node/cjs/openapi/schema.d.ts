/**
 * This file was auto-generated by openapi-typescript.
 * Do not make direct changes to the file.
 */
export interface paths {
  '/': {
    /** Get links to other endpoints to help discover the REST API */
    get: operations['weaviate.root'];
  };
  '/.well-known/live': {
    /** Determines whether the application is alive. Can be used for kubernetes liveness probe */
    get: operations['weaviate.wellknown.liveness'];
  };
  '/.well-known/ready': {
    /** Determines whether the application is ready to receive traffic. Can be used for kubernetes readiness probe. */
    get: operations['weaviate.wellknown.readiness'];
  };
  '/.well-known/openid-configuration': {
    /** OIDC Discovery page, redirects to the token issuer if one is configured */
    get: {
      responses: {
        /** Successful response, inspect body */
        200: {
          schema: {
            /** @description The Location to redirect to */
            href?: string;
            /** @description OAuth Client ID */
            clientId?: string;
            /** @description OAuth Scopes */
            scopes?: string[];
          };
        };
        /** Not found, no oidc provider present */
        404: unknown;
        /** An error has occurred while trying to fulfill the request. Most likely the ErrorResponse will contain more information about the error. */
        500: {
          schema: definitions['ErrorResponse'];
        };
      };
    };
  };
  '/replication/replicate': {
    post: operations['replicate'];
  };
  '/replication/replicate/{id}': {
    /** Returns the details of a replication operation for a given shard, identified by the provided replication operation id. */
    get: operations['replicationDetails'];
  };
  '/users/own-info': {
    get: operations['getOwnInfo'];
  };
  '/users/db': {
    get: operations['listAllUsers'];
  };
  '/users/db/{user_id}': {
    get: operations['getUserInfo'];
    post: operations['createUser'];
    delete: operations['deleteUser'];
  };
  '/users/db/{user_id}/rotate-key': {
    post: operations['rotateUserApiKey'];
  };
  '/users/db/{user_id}/activate': {
    post: operations['activateUser'];
  };
  '/users/db/{user_id}/deactivate': {
    post: operations['deactivateUser'];
  };
  '/authz/roles': {
    get: operations['getRoles'];
    post: operations['createRole'];
  };
  '/authz/roles/{id}/add-permissions': {
    post: operations['addPermissions'];
  };
  '/authz/roles/{id}/remove-permissions': {
    post: operations['removePermissions'];
  };
  '/authz/roles/{id}': {
    get: operations['getRole'];
    delete: operations['deleteRole'];
  };
  '/authz/roles/{id}/has-permission': {
    post: operations['hasPermission'];
  };
  '/authz/roles/{id}/users': {
    get: operations['getUsersForRoleDeprecated'];
  };
  '/authz/roles/{id}/user-assignments': {
    get: operations['getUsersForRole'];
  };
  '/authz/users/{id}/roles': {
    get: operations['getRolesForUserDeprecated'];
  };
  '/authz/users/{id}/roles/{userType}': {
    get: operations['getRolesForUser'];
  };
  '/authz/users/{id}/assign': {
    post: operations['assignRoleToUser'];
  };
  '/authz/users/{id}/revoke': {
    post: operations['revokeRoleFromUser'];
  };
  '/authz/groups/{id}/assign': {
    post: operations['assignRoleToGroup'];
  };
  '/authz/groups/{id}/revoke': {
    post: operations['revokeRoleFromGroup'];
  };
  '/objects': {
    /** Lists all Objects in reverse order of creation, owned by the user that belongs to the used token. */
    get: operations['objects.list'];
    /** Create a new object. <br/><br/>Meta-data and schema values are validated. <br/><br/>**Note: Use `/batch` for importing many objects**: <br/>If you plan on importing a large number of objects, it's much more efficient to use the `/batch` endpoint. Otherwise, sending multiple single requests sequentially would incur a large performance penalty. <br/><br/>**Note: idempotence of `/objects`**: <br/>POST /objects will fail if an id is provided which already exists in the class. To update an existing object with the objects endpoint, use the PUT or PATCH method. */
    post: operations['objects.create'];
  };
  '/objects/{id}': {
    /** Get a specific object based on its UUID. Also available as Websocket bus. */
    get: operations['objects.get'];
    /** Updates an object based on its UUID. Given meta-data and schema values are validated. LastUpdateTime is set to the time this function is called. */
    put: operations['objects.update'];
    /** Deletes an object from the database based on its UUID. */
    delete: operations['objects.delete'];
    /** Checks if an object exists in the system based on its UUID. */
    head: operations['objects.head'];
    /** Update an object based on its UUID (using patch semantics). This method supports json-merge style patch semantics (RFC 7396). Provided meta-data and schema values are validated. LastUpdateTime is set to the time this function is called. */
    patch: operations['objects.patch'];
  };
  '/objects/{className}/{id}': {
    /** Get a data object based on its collection and UUID. Also available as Websocket bus. */
    get: operations['objects.class.get'];
    /** Update an object based on its uuid and collection. This (`put`) method replaces the object with the provided object. */
    put: operations['objects.class.put'];
    /** Delete an object based on its collection and UUID. <br/><br/>Note: For backward compatibility, beacons also support an older, deprecated format without the collection name. As a result, when deleting a reference, the beacon specified has to match the beacon to be deleted exactly. In other words, if a beacon is present using the old format (without collection name) you also need to specify it the same way. <br/><br/>In the beacon format, you need to always use `localhost` as the host, rather than the actual hostname. `localhost` here refers to the fact that the beacon's target is on the same Weaviate instance, as opposed to a foreign instance. */
    delete: operations['objects.class.delete'];
    /** Checks if a data object exists based on its collection and uuid without retrieving it. <br/><br/>Internally it skips reading the object from disk other than checking if it is present. Thus it does not use resources on marshalling, parsing, etc., and is faster. Note the resulting HTTP request has no body; the existence of an object is indicated solely by the status code. */
    head: operations['objects.class.head'];
    /** Update an individual data object based on its class and uuid. This method supports json-merge style patch semantics (RFC 7396). Provided meta-data and schema values are validated. LastUpdateTime is set to the time this function is called. */
    patch: operations['objects.class.patch'];
  };
  '/objects/{id}/references/{propertyName}': {
    /** Replace all references in cross-reference property of an object. */
    put: operations['objects.references.update'];
    /** Add a cross-reference. */
    post: operations['objects.references.create'];
    /** Delete the single reference that is given in the body from the list of references that this property has. */
    delete: operations['objects.references.delete'];
  };
  '/objects/{className}/{id}/references/{propertyName}': {
    /** Replace **all** references in cross-reference property of an object. */
    put: operations['objects.class.references.put'];
    /** Add a single reference to an object. This adds a reference to the array of cross-references of the given property in the source object specified by its collection name and id */
    post: operations['objects.class.references.create'];
    /** Delete the single reference that is given in the body from the list of references that this property has. */
    delete: operations['objects.class.references.delete'];
  };
  '/objects/validate': {
    /** Validate an object's schema and meta-data without creating it. <br/><br/>If the schema of the object is valid, the request should return nothing with a plain RESTful request. Otherwise, an error object will be returned. */
    post: operations['objects.validate'];
  };
  '/batch/objects': {
    /** Create new objects in bulk. <br/><br/>Meta-data and schema values are validated. <br/><br/>**Note: idempotence of `/batch/objects`**: <br/>`POST /batch/objects` is idempotent, and will overwrite any existing object given the same id. */
    post: operations['batch.objects.create'];
    /** Batch delete objects that match a particular filter. <br/><br/>The request body takes a single `where` filter and will delete all objects matched. <br/><br/>Note that there is a limit to the number of objects to be deleted at once using this filter, in order to protect against unexpected memory surges and very-long-running requests. The default limit is 10,000 and may be configured by setting the `QUERY_MAXIMUM_RESULTS` environment variable. <br/><br/>Objects are deleted in the same order that they would be returned in an equivalent Get query. To delete more objects than the limit, run the same query multiple times. */
    delete: operations['batch.objects.delete'];
  };
  '/batch/references': {
    /** Batch create cross-references between collections items (objects or objects) in bulk. */
    post: operations['batch.references.create'];
  };
  '/graphql': {
    /** Get a response based on a GraphQL query */
    post: operations['graphql.post'];
  };
  '/graphql/batch': {
    /** Perform a batched GraphQL query */
    post: operations['graphql.batch'];
  };
  '/meta': {
    /** Returns meta information about the server. Can be used to provide information to another Weaviate instance that wants to interact with the current instance. */
    get: operations['meta.get'];
  };
  '/schema': {
    /** Fetch an array of all collection definitions from the schema. */
    get: operations['schema.dump'];
    /** Create a new data object collection. <br/><br/>If AutoSchema is enabled, Weaviate will attempt to infer the schema from the data at import time. However, manual schema definition is recommended for production environments. */
    post: operations['schema.objects.create'];
  };
  '/schema/{className}': {
    get: operations['schema.objects.get'];
    /** Add a property to an existing collection. */
    put: operations['schema.objects.update'];
    /** Remove a collection from the schema. This will also delete all the objects in the collection. */
    delete: operations['schema.objects.delete'];
  };
  '/schema/{className}/properties': {
    post: operations['schema.objects.properties.add'];
  };
  '/schema/{className}/shards': {
    /** Get the status of every shard in the cluster. */
    get: operations['schema.objects.shards.get'];
  };
  '/schema/{className}/shards/{shardName}': {
    /** Update a shard status for a collection. For example, a shard may have been marked as `READONLY` because its disk was full. After providing more disk space, use this endpoint to set the shard status to `READY` again. There is also a convenience function in each client to set the status of all shards of a collection. */
    put: operations['schema.objects.shards.update'];
  };
  '/schema/{className}/tenants': {
    /** get all tenants from a specific class */
    get: operations['tenants.get'];
    /** Update tenant of a specific class */
    put: operations['tenants.update'];
    /** Create a new tenant for a collection. Multi-tenancy must be enabled in the collection definition. */
    post: operations['tenants.create'];
    /** delete tenants from a specific class */
    delete: operations['tenants.delete'];
  };
  '/schema/{className}/tenants/{tenantName}': {
    /** get a specific tenant for the given class */
    get: operations['tenants.get.one'];
    /** Check if a tenant exists for a specific class */
    head: operations['tenant.exists'];
  };
  '/backups/{backend}': {
    /** [Coming soon] List all backups in progress not implemented yet. */
    get: operations['backups.list'];
    /** Start creating a backup for a set of collections. <br/><br/>Notes: <br/>- Weaviate uses gzip compression by default. <br/>- Weaviate stays usable while a backup process is ongoing. */
    post: operations['backups.create'];
  };
  '/backups/{backend}/{id}': {
    /** Returns status of backup creation attempt for a set of collections. <br/><br/>All client implementations have a `wait for completion` option which will poll the backup status in the background and only return once the backup has completed (successfully or unsuccessfully). If you set the `wait for completion` option to false, you can also check the status yourself using this endpoint. */
    get: operations['backups.create.status'];
    /** Cancel created backup with specified ID */
    delete: operations['backups.cancel'];
  };
  '/backups/{backend}/{id}/restore': {
    /** Returns status of a backup restoration attempt for a set of classes. <br/><br/>All client implementations have a `wait for completion` option which will poll the backup status in the background and only return once the backup has completed (successfully or unsuccessfully). If you set the `wait for completion` option to false, you can also check the status yourself using the this endpoint. */
    get: operations['backups.restore.status'];
    /** Starts a process of restoring a backup for a set of collections. <br/><br/>Any backup can be restored to any machine, as long as the number of nodes between source and target are identical.<br/><br/>Requrements:<br/><br/>- None of the collections to be restored already exist on the target restoration node(s).<br/>- The node names of the backed-up collections' must match those of the target restoration node(s). */
    post: operations['backups.restore'];
  };
  '/cluster/statistics': {
    /** Returns Raft cluster statistics of Weaviate DB. */
    get: operations['cluster.get.statistics'];
  };
  '/nodes': {
    /** Returns node information for the entire database. */
    get: operations['nodes.get'];
  };
  '/nodes/{className}': {
    /** Returns node information for the nodes relevant to the collection. */
    get: operations['nodes.get.class'];
  };
  '/classifications/': {
    /** Trigger a classification based on the specified params. Classifications will run in the background, use GET /classifications/<id> to retrieve the status of your classification. */
    post: operations['classifications.post'];
  };
  '/classifications/{id}': {
    /** Get status, results and metadata of a previously created classification */
    get: operations['classifications.get'];
  };
}
export interface definitions {
  /**
   * @description the type of user
   * @enum {string}
   */
  UserTypeInput: 'db' | 'oidc';
  /**
   * @description the type of user
   * @enum {string}
   */
  UserTypeOutput: 'db_user' | 'db_env_user' | 'oidc';
  UserOwnInfo: {
    /** @description The groups associated to the user */
    groups?: string[];
    roles?: definitions['Role'][];
    /** @description The username associated with the provided key */
    username: string;
  };
  DBUserInfo: {
    /** @description The role names associated to the user */
    roles: string[];
    /** @description The user id of the given user */
    userId: string;
    /**
     * @description type of the returned user
     * @enum {string}
     */
    dbUserType: 'db_user' | 'db_env_user';
    /** @description activity status of the returned user */
    active: boolean;
    /**
     * Format: date-time
     * @description Date and time in ISO 8601 format (YYYY-MM-DDTHH:MM:SSZ)
     */
    createdAt?: unknown;
    /** @description First 3 letters of the associated API-key */
    apiKeyFirstLetters?: unknown;
    /**
     * Format: date-time
     * @description Date and time in ISO 8601 format (YYYY-MM-DDTHH:MM:SSZ)
     */
    lastUsedAt?: unknown;
  };
  UserApiKey: {
    /** @description The apikey */
    apikey: string;
  };
  Role: {
    /** @description role name */
    name: string;
    permissions: definitions['Permission'][];
  };
  /** @description permissions attached to a role. */
  Permission: {
    /** @description resources applicable for backup actions */
    backups?: {
      /**
       * @description string or regex. if a specific collection name, if left empty it will be ALL or *
       * @default *
       */
      collection?: string;
    };
    /** @description resources applicable for data actions */
    data?: {
      /**
       * @description string or regex. if a specific collection name, if left empty it will be ALL or *
       * @default *
       */
      collection?: string;
      /**
       * @description string or regex. if a specific tenant name, if left empty it will be ALL or *
       * @default *
       */
      tenant?: string;
      /**
       * @description string or regex. if a specific object ID, if left empty it will be ALL or *
       * @default *
       */
      object?: string;
    };
    /** @description resources applicable for cluster actions */
    nodes?: {
      /**
       * @description whether to allow (verbose) returning shards and stats data in the response
       * @default minimal
       * @enum {string}
       */
      verbosity?: 'verbose' | 'minimal';
      /**
       * @description string or regex. if a specific collection name, if left empty it will be ALL or *
       * @default *
       */
      collection?: string;
    };
    /** @description resources applicable for user actions */
    users?: {
      /**
       * @description string or regex. if a specific name, if left empty it will be ALL or *
       * @default *
       */
      users?: string;
    };
    /** @description resources applicable for tenant actions */
    tenants?: {
      /**
       * @description string or regex. if a specific collection name, if left empty it will be ALL or *
       * @default *
       */
      collection?: string;
      /**
       * @description string or regex. if a specific tenant name, if left empty it will be ALL or *
       * @default *
       */
      tenant?: string;
    };
    /** @description resources applicable for role actions */
    roles?: {
      /**
       * @description string or regex. if a specific role name, if left empty it will be ALL or *
       * @default *
       */
      role?: string;
      /**
       * @description set the scope for the manage role permission
       * @default match
       * @enum {string}
       */
      scope?: 'all' | 'match';
    };
    /** @description resources applicable for collection and/or tenant actions */
    collections?: {
      /**
       * @description string or regex. if a specific collection name, if left empty it will be ALL or *
       * @default *
       */
      collection?: string;
    };
    /**
     * @description allowed actions in weaviate.
     * @enum {string}
     */
    action:
      | 'manage_backups'
      | 'read_cluster'
      | 'create_data'
      | 'read_data'
      | 'update_data'
      | 'delete_data'
      | 'read_nodes'
      | 'create_roles'
      | 'read_roles'
      | 'update_roles'
      | 'delete_roles'
      | 'create_collections'
      | 'read_collections'
      | 'update_collections'
      | 'delete_collections'
      | 'assign_and_revoke_users'
      | 'create_users'
      | 'read_users'
      | 'update_users'
      | 'delete_users'
      | 'create_tenants'
      | 'read_tenants'
      | 'update_tenants'
      | 'delete_tenants';
  };
  /** @description list of roles */
  RolesListResponse: definitions['Role'][];
  Link: {
    /** @description target of the link */
    href?: string;
    /** @description relationship if both resources are related, e.g. 'next', 'previous', 'parent', etc. */
    rel?: string;
    /** @description human readable name of the resource group */
    name?: string;
    /** @description weaviate documentation about this resource group */
    documentationHref?: string;
  };
  Principal: {
    /** @description The username that was extracted either from the authentication information */
    username?: string;
    groups?: string[];
    userType?: definitions['UserTypeInput'];
  };
  /** @description An array of available words and contexts. */
  C11yWordsResponse: {
    /** @description Weighted results for all words */
    concatenatedWord?: {
      concatenatedWord?: string;
      singleWords?: unknown[];
      concatenatedVector?: definitions['C11yVector'];
      concatenatedNearestNeighbors?: definitions['C11yNearestNeighbors'];
    };
    /** @description Weighted results for per individual word */
    individualWords?: {
      word?: string;
      present?: boolean;
      info?: {
        vector?: definitions['C11yVector'];
        nearestNeighbors?: definitions['C11yNearestNeighbors'];
      };
    }[];
  };
  /** @description A resource describing an extension to the contextinoary, containing both the identifier and the definition of the extension */
  C11yExtension: {
    /**
     * @description The new concept you want to extend. Must be an all-lowercase single word, or a space delimited compound word. Examples: 'foobarium', 'my custom concept'
     * @example foobarium
     */
    concept?: string;
    /** @description A list of space-delimited words or a sentence describing what the custom concept is about. Avoid using the custom concept itself. An Example definition for the custom concept 'foobarium': would be 'a naturally occurring element which can only be seen by programmers' */
    definition?: string;
    /**
     * Format: float
     * @description Weight of the definition of the new concept where 1='override existing definition entirely' and 0='ignore custom definition'. Note that if the custom concept is not present in the contextionary yet, the weight cannot be less than 1.
     */
    weight?: number;
  };
  /** @description C11y function to show the nearest neighbors to a word. */
  C11yNearestNeighbors: {
    word?: string;
    /** Format: float */
    distance?: number;
  }[];
  /** @description A vector representation of the object in the Contextionary. If provided at object creation, this wil take precedence over any vectorizer setting. */
  C11yVector: number[];
  /** @description A vector representation of the object. If provided at object creation, this wil take precedence over any vectorizer setting. */
  Vector: {
    [key: string]: unknown;
  };
  /** @description A map of named vectors for multi-vector representations. */
  Vectors: {
    [key: string]: definitions['Vector'];
  };
  /** @description Receive question based on array of classes, properties and values. */
  C11yVectorBasedQuestion: {
    /** @description Vectorized classname. */
    classVectors?: number[];
    /** @description Vectorized properties. */
    classProps?: {
      propsVectors?: number[];
      /** @description String with valuename. */
      value?: string;
    }[];
  }[];
  Deprecation: {
    /** @description The id that uniquely identifies this particular deprecations (mostly used internally) */
    id?: string;
    /** @description Whether the problematic API functionality is deprecated (planned to be removed) or already removed */
    status?: string;
    /** @description Describes which API is effected, usually one of: REST, GraphQL */
    apiType?: string;
    /** @description What this deprecation is about */
    msg?: string;
    /** @description User-required object to not be affected by the (planned) removal */
    mitigation?: string;
    /** @description The deprecation was introduced in this version */
    sinceVersion?: string;
    /** @description A best-effort guess of which upcoming version will remove the feature entirely */
    plannedRemovalVersion?: string;
    /** @description If the feature has already been removed, it was removed in this version */
    removedIn?: string;
    /**
     * Format: date-time
     * @description If the feature has already been removed, it was removed at this timestamp
     */
    removedTime?: string;
    /**
     * Format: date-time
     * @description The deprecation was introduced in this version
     */
    sinceTime?: string;
    /** @description The locations within the specified API affected by this deprecation */
    locations?: string[];
  };
  /** @description An error response given by Weaviate end-points. */
  ErrorResponse: {
    error?: {
      message?: string;
    }[];
  };
  /** @description An error response caused by a GraphQL query. */
  GraphQLError: {
    locations?: {
      /** Format: int64 */
      column?: number;
      /** Format: int64 */
      line?: number;
    }[];
    message?: string;
    path?: string[];
  };
  /** @description GraphQL query based on: http://facebook.github.io/graphql/. */
  GraphQLQuery: {
    /** @description The name of the operation if multiple exist in the query. */
    operationName?: string;
    /** @description Query based on GraphQL syntax. */
    query?: string;
    /** @description Additional variables for the query. */
    variables?: {
      [key: string]: unknown;
    };
  };
  /** @description A list of GraphQL queries. */
  GraphQLQueries: definitions['GraphQLQuery'][];
  /** @description GraphQL based response: http://facebook.github.io/graphql/. */
  GraphQLResponse: {
    /** @description GraphQL data object. */
    data?: {
      [key: string]: definitions['JsonObject'];
    };
    /** @description Array with errors. */
    errors?: definitions['GraphQLError'][];
  };
  /** @description A list of GraphQL responses. */
  GraphQLResponses: definitions['GraphQLResponse'][];
  /** @description Configure the inverted index built into Weaviate (default: 60). */
  InvertedIndexConfig: {
    /**
     * Format: int
     * @description Asynchronous index clean up happens every n seconds
     */
    cleanupIntervalSeconds?: number;
    bm25?: definitions['BM25Config'];
    stopwords?: definitions['StopwordConfig'];
    /** @description Index each object by its internal timestamps (default: 'false'). */
    indexTimestamps?: boolean;
    /** @description Index each object with the null state (default: 'false'). */
    indexNullState?: boolean;
    /** @description Index length of properties (default: 'false'). */
    indexPropertyLength?: boolean;
    /** @description Using BlockMax WAND for query execution (default: 'false', will be 'true' for new collections created after 1.30). */
    usingBlockMaxWAND?: boolean;
  };
  /** @description Configure how replication is executed in a cluster */
  ReplicationConfig: {
    /** @description Number of times a class is replicated (default: 1). */
    factor?: number;
    /** @description Enable asynchronous replication (default: false). */
    asyncEnabled?: boolean;
    /**
     * @description Conflict resolution strategy for deleted objects.
     * @enum {string}
     */
    deletionStrategy?: 'NoAutomatedResolution' | 'DeleteOnConflict' | 'TimeBasedResolution';
  };
  /** @description tuning parameters for the BM25 algorithm */
  BM25Config: {
    /**
     * Format: float
     * @description Calibrates term-weight scaling based on the term frequency within a document (default: 1.2).
     */
    k1?: number;
    /**
     * Format: float
     * @description Calibrates term-weight scaling based on the document length (default: 0.75).
     */
    b?: number;
  };
  /** @description fine-grained control over stopword list usage */
  StopwordConfig: {
    /** @description Pre-existing list of common words by language (default: 'en'). Options: ['en', 'none']. */
    preset?: string;
    /** @description Stopwords to be considered additionally (default: []). Can be any array of custom strings. */
    additions?: string[];
    /** @description Stopwords to be removed from consideration (default: []). Can be any array of custom strings. */
    removals?: string[];
  };
  /** @description Configuration related to multi-tenancy within a class */
  MultiTenancyConfig: {
    /** @description Whether or not multi-tenancy is enabled for this class (default: false). */
    enabled?: boolean;
    /** @description Nonexistent tenants should (not) be created implicitly (default: false). */
    autoTenantCreation?: boolean;
    /** @description Existing tenants should (not) be turned HOT implicitly when they are accessed and in another activity status (default: false). */
    autoTenantActivation?: boolean;
  };
  /** @description JSON object value. */
  JsonObject: {
    [key: string]: unknown;
  };
  /** @description Contains meta information of the current Weaviate instance. */
  Meta: {
    /**
     * Format: url
     * @description The url of the host.
     */
    hostname?: string;
    /** @description The Weaviate server version. */
    version?: string;
    /** @description Module-specific meta information. */
    modules?: {
      [key: string]: unknown;
    };
    /** @description Max message size for GRPC connection in bytes. */
    grpcMaxMessageSize?: number;
  };
  /** @description Multiple instances of references to other objects. */
  MultipleRef: definitions['SingleRef'][];
  /** @description Either a JSONPatch document as defined by RFC 6902 (from, op, path, value), or a merge document (RFC 7396). */
  PatchDocumentObject: {
    /** @description A string containing a JSON Pointer value. */
    from?: string;
    /**
     * @description The operation to be performed.
     * @enum {string}
     */
    op: 'add' | 'remove' | 'replace' | 'move' | 'copy' | 'test';
    /** @description A JSON-Pointer. */
    path: string;
    /** @description The value to be used within the operations. */
    value?: {
      [key: string]: unknown;
    };
    merge?: definitions['Object'];
  };
  /** @description Either a JSONPatch document as defined by RFC 6902 (from, op, path, value), or a merge document (RFC 7396). */
  PatchDocumentAction: {
    /** @description A string containing a JSON Pointer value. */
    from?: string;
    /**
     * @description The operation to be performed.
     * @enum {string}
     */
    op: 'add' | 'remove' | 'replace' | 'move' | 'copy' | 'test';
    /** @description A JSON-Pointer. */
    path: string;
    /** @description The value to be used within the operations. */
    value?: {
      [key: string]: unknown;
    };
    merge?: definitions['Object'];
  };
  /** @description Request body to add a replica of given shard of a given collection */
  ReplicationReplicateReplicaRequest: {
    /** @description The node containing the replica */
    sourceNodeName: string;
    /** @description The node to add a copy of the replica on */
    destinationNodeName: string;
    /** @description The collection name holding the shard */
    collectionId: string;
    /** @description The shard id holding the replica to be copied */
    shardId: string;
  };
  /** @description Request body to disable (soft-delete) a replica of given shard of a given collection */
  ReplicationDisableReplicaRequest: {
    /** @description The node containing the replica to be disabled */
    nodeName: string;
    /** @description The collection name holding the replica to be disabled */
    collectionId: string;
    /** @description The shard id holding the replica to be disabled */
    shardId: string;
  };
  /** @description Request body to delete a replica of given shard of a given collection */
  ReplicationDeleteReplicaRequest: {
    /** @description The node containing the replica to be deleted */
    nodeName: string;
    /** @description The collection name holding the replica to be delete */
    collectionId: string;
    /** @description The shard id holding the replica to be deleted */
    shardId: string;
  };
  /** @description The current status and details of a replication operation, including information about the resources involved in the replication process. */
  ReplicationReplicateDetailsReplicaResponse: {
    /** @description The unique id of the replication operation. */
    id: string;
    /** @description The id of the shard to collect replication details for. */
    shardId: string;
    /** @description The name of the collection holding data being replicated. */
    collection: string;
    /** @description The id of the node where the source replica is allocated. */
    sourceNodeId: string;
    /** @description The id of the node where the target replica is allocated. */
    targetNodeId: string;
    /**
     * @description The current status of the replication operation, indicating the replication phase the operation is in.
     * @enum {string}
     */
    status:
      | 'READY'
      | 'INDEXING'
      | 'REPLICATION_FINALIZING'
      | 'REPLICATION_HYDRATING'
      | 'REPLICATION_DEHYDRATING';
  };
  /** @description A single peer in the network. */
  PeerUpdate: {
    /**
     * Format: uuid
     * @description The session ID of the peer.
     */
    id?: string;
    /** @description Human readable name. */
    name?: string;
    /**
     * Format: uri
     * @description The location where the peer is exposed to the internet.
     */
    uri?: string;
    /** @description The latest known hash of the peer's schema. */
    schemaHash?: string;
  };
  /** @description List of known peers. */
  PeerUpdateList: definitions['PeerUpdate'][];
  /** @description Allow custom overrides of vector weights as math expressions. E.g. "pancake": "7" will set the weight for the word pancake to 7 in the vectorization, whereas "w * 3" would triple the originally calculated word. This is an open object, with OpenAPI Specification 3.0 this will be more detailed. See Weaviate docs for more info. In the future this will become a key/value (string/string) object. */
  VectorWeights: {
    [key: string]: unknown;
  };
  /** @description Names and values of an individual property. A returned response may also contain additional metadata, such as from classification or feature projection. */
  PropertySchema: {
    [key: string]: unknown;
  };
  /** @description This is an open object, with OpenAPI Specification 3.0 this will be more detailed. See Weaviate docs for more info. In the future this will become a key/value OR a SingleRef definition. */
  SchemaHistory: {
    [key: string]: unknown;
  };
  /** @description Definitions of semantic schemas (also see: https://github.com/weaviate/weaviate-semantic-schemas). */
  Schema: {
    /** @description Semantic classes that are available. */
    classes?: definitions['Class'][];
    /**
     * Format: email
     * @description Email of the maintainer.
     */
    maintainer?: string;
    /** @description Name of the schema. */
    name?: string;
  };
  /** @description Indicates the health of the schema in a cluster. */
  SchemaClusterStatus: {
    /** @description True if the cluster is in sync, false if there is an issue (see error). */
    healthy?: boolean;
    /** @description Contains the sync check error if one occurred */
    error?: string;
    /** @description Hostname of the coordinating node, i.e. the one that received the cluster. This can be useful information if the error message contains phrases such as 'other nodes agree, but local does not', etc. */
    hostname?: string;
    /**
     * Format: int
     * @description Number of nodes that participated in the sync check
     */
    nodeCount?: number;
    /** @description The cluster check at startup can be ignored (to recover from an out-of-sync situation). */
    ignoreSchemaSync?: boolean;
  };
  Class: {
    /** @description Name of the class (a.k.a. 'collection') (required). Multiple words should be concatenated in CamelCase, e.g. `ArticleAuthor`. */
    class?: string;
    /** @description Configure named vectors. Either use this field or `vectorizer`, `vectorIndexType`, and `vectorIndexConfig` fields. Available from `v1.24.0`. */
    vectorConfig?: {
      [key: string]: definitions['VectorConfig'];
    };
    /** @description Name of the vector index to use, eg. (HNSW) */
    vectorIndexType?: string;
    /** @description Vector-index config, that is specific to the type of index selected in vectorIndexType */
    vectorIndexConfig?: {
      [key: string]: unknown;
    };
    /** @description Manage how the index should be sharded and distributed in the cluster */
    shardingConfig?: {
      [key: string]: unknown;
    };
    replicationConfig?: definitions['ReplicationConfig'];
    invertedIndexConfig?: definitions['InvertedIndexConfig'];
    multiTenancyConfig?: definitions['MultiTenancyConfig'];
    /** @description Specify how the vectors for this class should be determined. The options are either 'none' - this means you have to import a vector with each object yourself - or the name of a module that provides vectorization capabilities, such as 'text2vec-contextionary'. If left empty, it will use the globally configured default which can itself either be 'none' or a specific module. */
    vectorizer?: string;
    /** @description Configuration specific to modules in a collection context. */
    moduleConfig?: {
      [key: string]: unknown;
    };
    /** @description Description of the collection for metadata purposes. */
    description?: string;
    /** @description Define properties of the collection. */
    properties?: definitions['Property'][];
  };
  Property: {
    /** @description Data type of the property (required). If it starts with a capital (for example Person), may be a reference to another type. */
    dataType?: string[];
    /** @description Description of the property. */
    description?: string;
    /** @description Configuration specific to modules this Weaviate instance has installed */
    moduleConfig?: {
      [key: string]: unknown;
    };
    /** @description The name of the property (required). Multiple words should be concatenated in camelCase, e.g. `nameOfAuthor`. */
    name?: string;
    /** @description (Deprecated). Whether to include this property in the inverted index. If `false`, this property cannot be used in `where` filters, `bm25` or `hybrid` search. <br/><br/>Unrelated to vectorization behavior (deprecated as of v1.19; use indexFilterable or/and indexSearchable instead) */
    indexInverted?: boolean;
    /** @description Whether to include this property in the filterable, Roaring Bitmap index. If `false`, this property cannot be used in `where` filters. <br/><br/>Note: Unrelated to vectorization behavior. */
    indexFilterable?: boolean;
    /** @description Optional. Should this property be indexed in the inverted index. Defaults to true. Applicable only to properties of data type text and text[]. If you choose false, you will not be able to use this property in bm25 or hybrid search. This property has no affect on vectorization decisions done by modules */
    indexSearchable?: boolean;
    /** @description Whether to include this property in the filterable, range-based Roaring Bitmap index. Provides better performance for range queries compared to filterable index in large datasets. Applicable only to properties of data type int, number, date. */
    indexRangeFilters?: boolean;
    /**
     * @description Determines tokenization of the property as separate words or whole field. Optional. Applies to text and text[] data types. Allowed values are `word` (default; splits on any non-alphanumerical, lowercases), `lowercase` (splits on white spaces, lowercases), `whitespace` (splits on white spaces), `field` (trims). Not supported for remaining data types
     * @enum {string}
     */
    tokenization?:
      | 'word'
      | 'lowercase'
      | 'whitespace'
      | 'field'
      | 'trigram'
      | 'gse'
      | 'kagome_kr'
      | 'kagome_ja'
      | 'gse_ch';
    /** @description The properties of the nested object(s). Applies to object and object[] data types. */
    nestedProperties?: definitions['NestedProperty'][];
  };
  VectorConfig: {
    /** @description Configuration of a specific vectorizer used by this vector */
    vectorizer?: {
      [key: string]: unknown;
    };
    /** @description Name of the vector index to use, eg. (HNSW) */
    vectorIndexType?: string;
    /** @description Vector-index config, that is specific to the type of index selected in vectorIndexType */
    vectorIndexConfig?: {
      [key: string]: unknown;
    };
  };
  NestedProperty: {
    dataType?: string[];
    description?: string;
    name?: string;
    indexFilterable?: boolean;
    indexSearchable?: boolean;
    indexRangeFilters?: boolean;
    /** @enum {string} */
    tokenization?:
      | 'word'
      | 'lowercase'
      | 'whitespace'
      | 'field'
      | 'trigram'
      | 'gse'
      | 'kagome_kr'
      | 'kagome_ja'
      | 'gse_ch';
    /** @description The properties of the nested object(s). Applies to object and object[] data types. */
    nestedProperties?: definitions['NestedProperty'][];
  };
  /** @description The status of all the shards of a Class */
  ShardStatusList: definitions['ShardStatusGetResponse'][];
  /** @description Response body of shard status get request */
  ShardStatusGetResponse: {
    /** @description Name of the shard */
    name?: string;
    /** @description Status of the shard */
    status?: string;
    /** @description Size of the vector queue of the shard */
    vectorQueueSize?: number;
  };
  /** @description The status of a single shard */
  ShardStatus: {
    /** @description Status of the shard */
    status?: string;
  };
  /** @description The definition of a backup create metadata */
  BackupCreateStatusResponse: {
    /** @description The ID of the backup. Must be URL-safe and work as a filesystem path, only lowercase, numbers, underscore, minus characters allowed. */
    id?: string;
    /** @description Backup backend name e.g. filesystem, gcs, s3. */
    backend?: string;
    /** @description destination path of backup files proper to selected backend */
    path?: string;
    /** @description error message if creation failed */
    error?: string;
    /**
     * @description phase of backup creation process
     * @default STARTED
     * @enum {string}
     */
    status?: 'STARTED' | 'TRANSFERRING' | 'TRANSFERRED' | 'SUCCESS' | 'FAILED' | 'CANCELED';
  };
  /** @description The definition of a backup restore metadata */
  BackupRestoreStatusResponse: {
    /** @description The ID of the backup. Must be URL-safe and work as a filesystem path, only lowercase, numbers, underscore, minus characters allowed. */
    id?: string;
    /** @description Backup backend name e.g. filesystem, gcs, s3. */
    backend?: string;
    /** @description destination path of backup files proper to selected backup backend, contains bucket and path */
    path?: string;
    /** @description error message if restoration failed */
    error?: string;
    /**
     * @description phase of backup restoration process
     * @default STARTED
     * @enum {string}
     */
    status?: 'STARTED' | 'TRANSFERRING' | 'TRANSFERRED' | 'SUCCESS' | 'FAILED' | 'CANCELED';
  };
  /** @description Backup custom configuration */
  BackupConfig: {
    /** @description name of the endpoint, e.g. s3.amazonaws.com */
    Endpoint?: string;
    /** @description Name of the bucket, container, volume, etc */
    Bucket?: string;
    /** @description Path or key within the bucket */
    Path?: string;
    /**
     * @description Desired CPU core utilization ranging from 1%-80%
     * @default 50
     */
    CPUPercentage?: number;
    /**
     * @description Aimed chunk size, with a minimum of 2MB, default of 128MB, and a maximum of 512MB. The actual chunk size may vary.
     * @default 128
     */
    ChunkSize?: number;
    /**
     * @description compression level used by compression algorithm
     * @default DefaultCompression
     * @enum {string}
     */
    CompressionLevel?: 'DefaultCompression' | 'BestSpeed' | 'BestCompression';
  };
  /** @description Backup custom configuration */
  RestoreConfig: {
    /** @description name of the endpoint, e.g. s3.amazonaws.com */
    Endpoint?: string;
    /** @description Name of the bucket, container, volume, etc */
    Bucket?: string;
    /** @description Path within the bucket */
    Path?: string;
    /**
     * @description Desired CPU core utilization ranging from 1%-80%
     * @default 50
     */
    CPUPercentage?: number;
  };
  /** @description Request body for creating a backup of a set of classes */
  BackupCreateRequest: {
    /** @description The ID of the backup (required). Must be URL-safe and work as a filesystem path, only lowercase, numbers, underscore, minus characters allowed. */
    id?: string;
    /** @description Custom configuration for the backup creation process */
    config?: definitions['BackupConfig'];
    /** @description List of collections to include in the backup creation process. If not set, all collections are included. Cannot be used together with `exclude`. */
    include?: string[];
    /** @description List of collections to exclude from the backup creation process. If not set, all collections are included. Cannot be used together with `include`. */
    exclude?: string[];
  };
  /** @description The definition of a backup create response body */
  BackupCreateResponse: {
    /** @description The ID of the backup. Must be URL-safe and work as a filesystem path, only lowercase, numbers, underscore, minus characters allowed. */
    id?: string;
    /** @description The list of classes for which the backup creation process was started */
    classes?: string[];
    /** @description Backup backend name e.g. filesystem, gcs, s3. */
    backend?: string;
    /** @description Name of the bucket, container, volume, etc */
    bucket?: string;
    /** @description Path within bucket of backup */
    path?: string;
    /** @description error message if creation failed */
    error?: string;
    /**
     * @description phase of backup creation process
     * @default STARTED
     * @enum {string}
     */
    status?: 'STARTED' | 'TRANSFERRING' | 'TRANSFERRED' | 'SUCCESS' | 'FAILED' | 'CANCELED';
  };
  /** @description The definition of a backup create response body */
  BackupListResponse: {
    /** @description The ID of the backup. Must be URL-safe and work as a filesystem path, only lowercase, numbers, underscore, minus characters allowed. */
    id?: string;
    /** @description destination path of backup files proper to selected backend */
    path?: string;
    /** @description The list of classes for which the existed backup process */
    classes?: string[];
    /**
     * @description status of backup process
     * @enum {string}
     */
    status?: 'STARTED' | 'TRANSFERRING' | 'TRANSFERRED' | 'SUCCESS' | 'FAILED' | 'CANCELED';
  }[];
  /** @description Request body for restoring a backup for a set of classes */
  BackupRestoreRequest: {
    /** @description Custom configuration for the backup restoration process */
    config?: definitions['RestoreConfig'];
    /** @description List of classes to include in the backup restoration process */
    include?: string[];
    /** @description List of classes to exclude from the backup restoration process */
    exclude?: string[];
    /** @description Allows overriding the node names stored in the backup with different ones. Useful when restoring backups to a different environment. */
    node_mapping?: {
      [key: string]: string;
    };
  };
  /** @description The definition of a backup restore response body */
  BackupRestoreResponse: {
    /** @description The ID of the backup. Must be URL-safe and work as a filesystem path, only lowercase, numbers, underscore, minus characters allowed. */
    id?: string;
    /** @description The list of classes for which the backup restoration process was started */
    classes?: string[];
    /** @description Backup backend name e.g. filesystem, gcs, s3. */
    backend?: string;
    /** @description destination path of backup files proper to selected backend */
    path?: string;
    /** @description error message if restoration failed */
    error?: string;
    /**
     * @description phase of backup restoration process
     * @default STARTED
     * @enum {string}
     */
    status?: 'STARTED' | 'TRANSFERRING' | 'TRANSFERRED' | 'SUCCESS' | 'FAILED' | 'CANCELED';
  };
  /** @description The summary of Weaviate's statistics. */
  NodeStats: {
    /**
     * Format: int
     * @description The count of Weaviate's shards. To see this value, set `output` to `verbose`.
     */
    shardCount?: number;
    /**
     * Format: int64
     * @description The total number of objects in DB.
     */
    objectCount?: number;
  };
  /** @description The summary of a nodes batch queue congestion status. */
  BatchStats: {
    /**
     * Format: int
     * @description How many objects are currently in the batch queue.
     */
    queueLength?: number;
    /**
     * Format: int
     * @description How many objects are approximately processed from the batch queue per second.
     */
    ratePerSecond?: number;
  };
  /** @description The definition of a node shard status response body */
  NodeShardStatus: {
    /** @description The name of the shard. */
    name?: string;
    /** @description The name of shard's class. */
    class?: string;
    /**
     * Format: int64
     * @description The number of objects in shard.
     */
    objectCount?: number;
    /**
     * Format: string
     * @description The status of the vector indexing process.
     */
    vectorIndexingStatus?: unknown;
    /**
     * Format: boolean
     * @description The status of vector compression/quantization.
     */
    compressed?: unknown;
    /**
     * Format: int64
     * @description The length of the vector indexing queue.
     */
    vectorQueueLength?: number;
    /** @description The load status of the shard. */
    loaded?: boolean;
  };
  /** @description The definition of a backup node status response body */
  NodeStatus: {
    /** @description The name of the node. */
    name?: string;
    /**
     * @description Node's status.
     * @default HEALTHY
     * @enum {string}
     */
    status?: 'HEALTHY' | 'UNHEALTHY' | 'UNAVAILABLE' | 'TIMEOUT';
    /** @description The version of Weaviate. */
    version?: string;
    /** @description The gitHash of Weaviate. */
    gitHash?: string;
    /** @description Weaviate overall statistics. */
    stats?: definitions['NodeStats'];
    /** @description Weaviate batch statistics. */
    batchStats?: definitions['BatchStats'];
    /** @description The list of the shards with it's statistics. */
    shards?: definitions['NodeShardStatus'][];
  };
  /** @description The status of all of the Weaviate nodes */
  NodesStatusResponse: {
    nodes?: definitions['NodeStatus'][];
  };
  /** @description The definition of Raft statistics. */
  RaftStatistics: {
    appliedIndex?: string;
    commitIndex?: string;
    fsmPending?: string;
    lastContact?: string;
    lastLogIndex?: string;
    lastLogTerm?: string;
    lastSnapshotIndex?: string;
    lastSnapshotTerm?: string;
    /** @description Weaviate Raft nodes. */
    latestConfiguration?: {
      [key: string]: unknown;
    };
    latestConfigurationIndex?: string;
    numPeers?: string;
    protocolVersion?: string;
    protocolVersionMax?: string;
    protocolVersionMin?: string;
    snapshotVersionMax?: string;
    snapshotVersionMin?: string;
    state?: string;
    term?: string;
  };
  /** @description The definition of node statistics. */
  Statistics: {
    /** @description The name of the node. */
    name?: string;
    /**
     * @description Node's status.
     * @default HEALTHY
     * @enum {string}
     */
    status?: 'HEALTHY' | 'UNHEALTHY' | 'UNAVAILABLE' | 'TIMEOUT';
    bootstrapped?: boolean;
    dbLoaded?: boolean;
    /** Format: uint64 */
    initialLastAppliedIndex?: number;
    lastAppliedIndex?: number;
    isVoter?: boolean;
    leaderId?: {
      [key: string]: unknown;
    };
    leaderAddress?: {
      [key: string]: unknown;
    };
    open?: boolean;
    ready?: boolean;
    candidates?: {
      [key: string]: unknown;
    };
    /** @description Weaviate Raft statistics. */
    raft?: definitions['RaftStatistics'];
  };
  /** @description The cluster statistics of all of the Weaviate nodes */
  ClusterStatisticsResponse: {
    statistics?: definitions['Statistics'][];
    synchronized?: boolean;
  };
  /** @description Either set beacon (direct reference) or set class and schema (concept reference) */
  SingleRef: {
    /**
     * Format: uri
     * @description If using a concept reference (rather than a direct reference), specify the desired class name here
     */
    class?: string;
    /** @description If using a concept reference (rather than a direct reference), specify the desired properties here */
    schema?: definitions['PropertySchema'];
    /**
     * Format: uri
     * @description If using a direct reference, specify the URI to point to the cross-ref here. Should be in the form of weaviate://localhost/<uuid> for the example of a local cross-ref to an object
     */
    beacon?: string;
    /**
     * Format: uri
     * @description If using a direct reference, this read-only fields provides a link to the referenced resource. If 'origin' is globally configured, an absolute URI is shown - a relative URI otherwise.
     */
    href?: string;
    /** @description Additional Meta information about classifications if the item was part of one */
    classification?: definitions['ReferenceMetaClassification'];
  };
  /** @description (Response only) Additional meta information about a single object. */
  AdditionalProperties: {
    [key: string]: {
      [key: string]: unknown;
    };
  };
  /** @description This meta field contains additional info about the classified reference property */
  ReferenceMetaClassification: {
    /**
     * Format: int64
     * @description overall neighbors checked as part of the classification. In most cases this will equal k, but could be lower than k - for example if not enough data was present
     */
    overallCount?: number;
    /**
     * Format: int64
     * @description size of the winning group, a number between 1..k
     */
    winningCount?: number;
    /**
     * Format: int64
     * @description size of the losing group, can be 0 if the winning group size equals k
     */
    losingCount?: number;
    /**
     * Format: float32
     * @description The lowest distance of any neighbor, regardless of whether they were in the winning or losing group
     */
    closestOverallDistance?: number;
    /**
     * Format: float32
     * @description deprecated - do not use, to be removed in 0.23.0
     */
    winningDistance?: number;
    /**
     * Format: float32
     * @description Mean distance of all neighbors from the winning group
     */
    meanWinningDistance?: number;
    /**
     * Format: float32
     * @description Closest distance of a neighbor from the winning group
     */
    closestWinningDistance?: number;
    /**
     * Format: float32
     * @description The lowest distance of a neighbor in the losing group. Optional. If k equals the size of the winning group, there is no losing group
     */
    closestLosingDistance?: number;
    /**
     * Format: float32
     * @description deprecated - do not use, to be removed in 0.23.0
     */
    losingDistance?: number;
    /**
     * Format: float32
     * @description Mean distance of all neighbors from the losing group. Optional. If k equals the size of the winning group, there is no losing group.
     */
    meanLosingDistance?: number;
  };
  BatchReference: {
    /**
     * Format: uri
     * @description Long-form beacon-style URI to identify the source of the cross-ref including the property name. Should be in the form of weaviate://localhost/<kinds>/<uuid>/<className>/<propertyName>, where <kinds> must be one of 'objects', 'objects' and <className> and <propertyName> must represent the cross-ref property of source class to be used.
     * @example weaviate://localhost/Zoo/a5d09582-4239-4702-81c9-92a6e0122bb4/hasAnimals
     */
    from?: string;
    /**
     * Format: uri
     * @description Short-form URI to point to the cross-ref. Should be in the form of weaviate://localhost/<uuid> for the example of a local cross-ref to an object
     * @example weaviate://localhost/97525810-a9a5-4eb0-858a-71449aeb007f
     */
    to?: string;
    /** @description Name of the reference tenant. */
    tenant?: string;
  };
  BatchReferenceResponse: definitions['BatchReference'] & {
    /**
     * Format: object
     * @description Results for this specific reference.
     */
    result?: {
      /**
       * @default SUCCESS
       * @enum {string}
       */
      status?: 'SUCCESS' | 'FAILED';
      errors?: definitions['ErrorResponse'];
    };
  };
  GeoCoordinates: {
    /**
     * Format: float
     * @description The latitude of the point on earth in decimal form
     */
    latitude?: number;
    /**
     * Format: float
     * @description The longitude of the point on earth in decimal form
     */
    longitude?: number;
  };
  PhoneNumber: {
    /** @description The raw input as the phone number is present in your raw data set. It will be parsed into the standardized formats if valid. */
    input?: string;
    /** @description Read-only. Parsed result in the international format (e.g. +49 123 ...) */
    internationalFormatted?: string;
    /** @description Optional. The ISO 3166-1 alpha-2 country code. This is used to figure out the correct countryCode and international format if only a national number (e.g. 0123 4567) is provided */
    defaultCountry?: string;
    /**
     * Format: uint64
     * @description Read-only. The numerical country code (e.g. 49)
     */
    countryCode?: number;
    /**
     * Format: uint64
     * @description Read-only. The numerical representation of the national part
     */
    national?: number;
    /** @description Read-only. Parsed result in the national format (e.g. 0123 456789) */
    nationalFormatted?: string;
    /** @description Read-only. Indicates whether the parsed number is a valid phone number */
    valid?: boolean;
  };
  Object: {
    /** @description Class of the Object, defined in the schema. */
    class?: string;
    vectorWeights?: definitions['VectorWeights'];
    properties?: definitions['PropertySchema'];
    /**
     * Format: uuid
     * @description ID of the Object.
     */
    id?: string;
    /**
     * Format: int64
     * @description (Response only) Timestamp of creation of this object in milliseconds since epoch UTC.
     */
    creationTimeUnix?: number;
    /**
     * Format: int64
     * @description (Response only) Timestamp of the last object update in milliseconds since epoch UTC.
     */
    lastUpdateTimeUnix?: number;
    /** @description This field returns vectors associated with the Object. C11yVector, Vector or Vectors values are possible. */
    vector?: definitions['C11yVector'];
    /** @description This field returns vectors associated with the Object. */
    vectors?: definitions['Vectors'];
    /** @description Name of the Objects tenant. */
    tenant?: string;
    additional?: definitions['AdditionalProperties'];
  };
  ObjectsGetResponse: definitions['Object'] & {
    deprecations?: definitions['Deprecation'][];
  } & {
    /**
     * Format: object
     * @description Results for this specific Object.
     */
    result?: {
      /**
       * @default SUCCESS
       * @enum {string}
       */
      status?: 'SUCCESS' | 'FAILED';
      errors?: definitions['ErrorResponse'];
    };
  };
  BatchDelete: {
    /** @description Outlines how to find the objects to be deleted. */
    match?: {
      /**
       * @description Class (name) which objects will be deleted.
       * @example City
       */
      class?: string;
      /** @description Filter to limit the objects to be deleted. */
      where?: definitions['WhereFilter'];
    };
    /**
     * @description Controls the verbosity of the output, possible values are: "minimal", "verbose". Defaults to "minimal".
     * @default minimal
     */
    output?: string;
    /**
     * Format: int64
     * @description Timestamp of deletion in milliseconds since epoch UTC.
     */
    deletionTimeUnixMilli?: number;
    /**
     * @description If true, the call will show which objects would be matched using the specified filter without deleting any objects. <br/><br/>Depending on the configured verbosity, you will either receive a count of affected objects, or a list of IDs.
     * @default false
     */
    dryRun?: boolean;
  };
  /** @description Delete Objects response. */
  BatchDeleteResponse: {
    /** @description Outlines how to find the objects to be deleted. */
    match?: {
      /**
       * @description Class (name) which objects will be deleted.
       * @example City
       */
      class?: string;
      /** @description Filter to limit the objects to be deleted. */
      where?: definitions['WhereFilter'];
    };
    /**
     * @description Controls the verbosity of the output, possible values are: "minimal", "verbose". Defaults to "minimal".
     * @default minimal
     */
    output?: string;
    /**
     * Format: int64
     * @description Timestamp of deletion in milliseconds since epoch UTC.
     */
    deletionTimeUnixMilli?: number;
    /**
     * @description If true, objects will not be deleted yet, but merely listed. Defaults to false.
     * @default false
     */
    dryRun?: boolean;
    results?: {
      /**
       * Format: int64
       * @description How many objects were matched by the filter.
       */
      matches?: number;
      /**
       * Format: int64
       * @description The most amount of objects that can be deleted in a single query, equals QUERY_MAXIMUM_RESULTS.
       */
      limit?: number;
      /**
       * Format: int64
       * @description How many objects were successfully deleted in this round.
       */
      successful?: number;
      /**
       * Format: int64
       * @description How many objects should have been deleted but could not be deleted.
       */
      failed?: number;
      /** @description With output set to "minimal" only objects with error occurred will the be described. Successfully deleted objects would be omitted. Output set to "verbose" will list all of the objets with their respective statuses. */
      objects?: {
        /**
         * Format: uuid
         * @description ID of the Object.
         */
        id?: string;
        /**
         * @default SUCCESS
         * @enum {string}
         */
        status?: 'SUCCESS' | 'DRYRUN' | 'FAILED';
        errors?: definitions['ErrorResponse'];
      }[];
    };
  };
  /** @description List of Objects. */
  ObjectsListResponse: {
    /** @description The actual list of Objects. */
    objects?: definitions['Object'][];
    deprecations?: definitions['Deprecation'][];
    /**
     * Format: int64
     * @description The total number of Objects for the query. The number of items in a response may be smaller due to paging.
     */
    totalResults?: number;
  };
  /** @description Manage classifications, trigger them and view status of past classifications. */
  Classification: {
    /**
     * Format: uuid
     * @description ID to uniquely identify this classification run
     * @example ee722219-b8ec-4db1-8f8d-5150bb1a9e0c
     */
    id?: string;
    /**
     * @description class (name) which is used in this classification
     * @example City
     */
    class?: string;
    /**
     * @description which ref-property to set as part of the classification
     * @example [
     *   "inCountry"
     * ]
     */
    classifyProperties?: string[];
    /**
     * @description base the text-based classification on these fields (of type text)
     * @example [
     *   "description"
     * ]
     */
    basedOnProperties?: string[];
    /**
     * @description status of this classification
     * @example running
     * @enum {string}
     */
    status?: 'running' | 'completed' | 'failed';
    /** @description additional meta information about the classification */
    meta?: definitions['ClassificationMeta'];
    /** @description which algorithm to use for classifications */
    type?: string;
    /** @description classification-type specific settings */
    settings?: {
      [key: string]: unknown;
    };
    /**
     * @description error message if status == failed
     * @default
     * @example classify xzy: something went wrong
     */
    error?: string;
    filters?: {
      /** @description limit the objects to be classified */
      sourceWhere?: definitions['WhereFilter'];
      /** @description Limit the training objects to be considered during the classification. Can only be used on types with explicit training sets, such as 'knn' */
      trainingSetWhere?: definitions['WhereFilter'];
      /** @description Limit the possible sources when using an algorithm which doesn't really on training data, e.g. 'contextual'. When using an algorithm with a training set, such as 'knn', limit the training set instead */
      targetWhere?: definitions['WhereFilter'];
    };
  };
  /** @description Additional information to a specific classification */
  ClassificationMeta: {
    /**
     * Format: date-time
     * @description time when this classification was started
     * @example 2017-07-21T17:32:28Z
     */
    started?: string;
    /**
     * Format: date-time
     * @description time when this classification finished
     * @example 2017-07-21T17:32:28Z
     */
    completed?: string;
    /**
     * @description number of objects which were taken into consideration for classification
     * @example 147
     */
    count?: number;
    /**
     * @description number of objects successfully classified
     * @example 140
     */
    countSucceeded?: number;
    /**
     * @description number of objects which could not be classified - see error message for details
     * @example 7
     */
    countFailed?: number;
  };
  /** @description Filter search results using a where filter */
  WhereFilter: {
    /** @description combine multiple where filters, requires 'And' or 'Or' operator */
    operands?: definitions['WhereFilter'][];
    /**
     * @description operator to use
     * @example GreaterThanEqual
     * @enum {string}
     */
    operator?:
      | 'And'
      | 'Or'
      | 'Equal'
      | 'Like'
      | 'NotEqual'
      | 'GreaterThan'
      | 'GreaterThanEqual'
      | 'LessThan'
      | 'LessThanEqual'
      | 'WithinGeoRange'
      | 'IsNull'
      | 'ContainsAny'
      | 'ContainsAll';
    /**
     * @description path to the property currently being filtered
     * @example [
     *   "inCity",
     *   "City",
     *   "name"
     * ]
     */
    path?: string[];
    /**
     * Format: int64
     * @description value as integer
     * @example 2000
     */
    valueInt?: number;
    /**
     * Format: float64
     * @description value as number/float
     * @example 3.14
     */
    valueNumber?: number;
    /**
     * @description value as boolean
     * @example false
     */
    valueBoolean?: boolean;
    /**
     * @description value as text (deprecated as of v1.19; alias for valueText)
     * @example my search term
     */
    valueString?: string;
    /**
     * @description value as text
     * @example my search term
     */
    valueText?: string;
    /**
     * @description value as date (as string)
     * @example TODO
     */
    valueDate?: string;
    /**
     * @description value as integer
     * @example [100, 200]
     */
    valueIntArray?: number[];
    /**
     * @description value as number/float
     * @example [
     *   3.14
     * ]
     */
    valueNumberArray?: number[];
    /**
     * @description value as boolean
     * @example [
     *   true,
     *   false
     * ]
     */
    valueBooleanArray?: boolean[];
    /**
     * @description value as text (deprecated as of v1.19; alias for valueText)
     * @example [
     *   "my search term"
     * ]
     */
    valueStringArray?: string[];
    /**
     * @description value as text
     * @example [
     *   "my search term"
     * ]
     */
    valueTextArray?: string[];
    /**
     * @description value as date (as string)
     * @example TODO
     */
    valueDateArray?: string[];
    /** @description value as geo coordinates and distance */
    valueGeoRange?: definitions['WhereFilterGeoRange'];
  };
  /** @description filter within a distance of a georange */
  WhereFilterGeoRange: {
    geoCoordinates?: definitions['GeoCoordinates'];
    distance?: {
      /** Format: float64 */
      max?: number;
    };
  };
  /** @description attributes representing a single tenant within weaviate */
  Tenant: {
    /** @description The name of the tenant (required). */
    name?: string;
    /**
     * @description activity status of the tenant's shard. Optional for creating tenant (implicit `ACTIVE`) and required for updating tenant. For creation, allowed values are `ACTIVE` - tenant is fully active and `INACTIVE` - tenant is inactive; no actions can be performed on tenant, tenant's files are stored locally. For updating, `ACTIVE`, `INACTIVE` and also `OFFLOADED` - as INACTIVE, but files are stored on cloud storage. The following values are read-only and are set by the server for internal use: `OFFLOADING` - tenant is transitioning from ACTIVE/INACTIVE to OFFLOADED, `ONLOADING` - tenant is transitioning from OFFLOADED to ACTIVE/INACTIVE. We still accept deprecated names `HOT` (now `ACTIVE`), `COLD` (now `INACTIVE`), `FROZEN` (now `OFFLOADED`), `FREEZING` (now `OFFLOADING`), `UNFREEZING` (now `ONLOADING`).
     * @enum {string}
     */
    activityStatus?:
      | 'ACTIVE'
      | 'INACTIVE'
      | 'OFFLOADED'
      | 'OFFLOADING'
      | 'ONLOADING'
      | 'HOT'
      | 'COLD'
      | 'FROZEN'
      | 'FREEZING'
      | 'UNFREEZING';
  };
  /** @description attributes representing a single tenant response within weaviate */
  TenantResponse: definitions['Tenant'] & {
    /** @description The list of nodes that owns that tenant data. */
    belongsToNodes?: string[];
  };
}
export interface parameters {
  /** @description A threshold UUID of the objects to retrieve after, using an UUID-based ordering. This object is not part of the set. <br/><br/>Must be used with `class`, typically in conjunction with `limit`. <br/><br/>Note `after` cannot be used with `offset` or `sort`. <br/><br/>For a null value similar to offset=0, set an empty string in the request, i.e. `after=` or `after`. */
  CommonAfterParameterQuery: string;
  /**
   * Format: int64
   * @description The starting index of the result window. Note `offset` will retrieve `offset+limit` results and return `limit` results from the object with index `offset` onwards. Limited by the value of `QUERY_MAXIMUM_RESULTS`. <br/><br/>Should be used in conjunction with `limit`. <br/><br/>Cannot be used with `after`.
   * @default 0
   */
  CommonOffsetParameterQuery: number;
  /**
   * Format: int64
   * @description The maximum number of items to be returned per page. The default is 25 unless set otherwise as an environment variable.
   */
  CommonLimitParameterQuery: number;
  /** @description Include additional information, such as classification infos. Allowed values include: classification, vector, interpretation */
  CommonIncludeParameterQuery: string;
  /** @description Determines how many replicas must acknowledge a request before it is considered successful */
  CommonConsistencyLevelParameterQuery: string;
  /** @description Specifies the tenant in a request targeting a multi-tenant class */
  CommonTenantParameterQuery: string;
  /** @description The target node which should fulfill the request */
  CommonNodeNameParameterQuery: string;
  /** @description Name(s) of the property to sort by - e.g. `city`, or `country,city`. */
  CommonSortParameterQuery: string;
  /** @description Order parameter to tell how to order (asc or desc) data within given field. Should be used in conjunction with `sort` parameter. If providing multiple `sort` values, provide multiple `order` values in corresponding order, e.g.: `sort=author_name,title&order=desc,asc`. */
  CommonOrderParameterQuery: string;
  /** @description The collection from which to query objects.  <br/><br/>Note that if `class` is not provided, the response will not include any objects. */
  CommonClassParameterQuery: string;
  /**
   * @description Controls the verbosity of the output, possible values are: "minimal", "verbose". Defaults to "minimal".
   * @default minimal
   */
  CommonOutputVerbosityParameterQuery: string;
}
export interface operations {
  /** Get links to other endpoints to help discover the REST API */
  'weaviate.root': {
    responses: {
      /** Weaviate is alive and ready to serve content */
      200: {
        schema: {
          links?: definitions['Link'][];
        };
      };
    };
  };
  /** Determines whether the application is alive. Can be used for kubernetes liveness probe */
  'weaviate.wellknown.liveness': {
    responses: {
      /** The application is able to respond to HTTP requests */
      200: unknown;
    };
  };
  /** Determines whether the application is ready to receive traffic. Can be used for kubernetes readiness probe. */
  'weaviate.wellknown.readiness': {
    responses: {
      /** The application has completed its start-up routine and is ready to accept traffic. */
      200: unknown;
      /** The application is currently not able to serve traffic. If other horizontal replicas of weaviate are available and they are capable of receiving traffic, all traffic should be redirected there instead. */
      503: unknown;
    };
  };
  replicate: {
    parameters: {
      body: {
        body: definitions['ReplicationReplicateReplicaRequest'];
      };
    };
    responses: {
      /** Replication operation registered successfully */
      200: unknown;
      /** Malformed request. */
      400: {
        schema: definitions['ErrorResponse'];
      };
      /** Unauthorized or invalid credentials. */
      401: unknown;
      /** Forbidden */
      403: {
        schema: definitions['ErrorResponse'];
      };
      /** Request body is well-formed (i.e., syntactically correct), but semantically erroneous. */
      422: {
        schema: definitions['ErrorResponse'];
      };
      /** An error has occurred while trying to fulfill the request. Most likely the ErrorResponse will contain more information about the error. */
      500: {
        schema: definitions['ErrorResponse'];
      };
    };
  };
  /** Returns the details of a replication operation for a given shard, identified by the provided replication operation id. */
  replicationDetails: {
    parameters: {
      path: {
        /** The replication operation id to get details for. */
        id: string;
      };
    };
    responses: {
      /** The details of the replication operation. */
      200: {
        schema: definitions['ReplicationReplicateDetailsReplicaResponse'];
      };
      /** Malformed request. */
      400: {
        schema: definitions['ErrorResponse'];
      };
      /** Unauthorized or invalid credentials. */
      401: unknown;
      /** Forbidden */
      403: {
        schema: definitions['ErrorResponse'];
      };
      /** Shard replica operation not found */
      404: unknown;
      /** An error has occurred while trying to fulfill the request. Most likely the ErrorResponse will contain more information about the error. */
      500: {
        schema: definitions['ErrorResponse'];
      };
    };
  };
  getOwnInfo: {
    responses: {
      /** Info about the user */
      200: {
        schema: definitions['UserOwnInfo'];
      };
      /** Unauthorized or invalid credentials. */
      401: unknown;
      /** An error has occurred while trying to fulfill the request. Most likely the ErrorResponse will contain more information about the error. */
      500: {
        schema: definitions['ErrorResponse'];
      };
    };
  };
  listAllUsers: {
    parameters: {
      query: {
        /** Whether to include the last used time of the users */
        includeLastUsedTime?: boolean;
      };
    };
    responses: {
      /** Info about the users */
      200: {
        schema: definitions['DBUserInfo'][];
      };
      /** Unauthorized or invalid credentials. */
      401: unknown;
      /** Forbidden */
      403: {
        schema: definitions['ErrorResponse'];
      };
      /** An error has occurred while trying to fulfill the request. Most likely the ErrorResponse will contain more information about the error. */
      500: {
        schema: definitions['ErrorResponse'];
      };
    };
  };
  getUserInfo: {
    parameters: {
      path: {
        /** user id */
        user_id: string;
      };
      query: {
        /** Whether to include the last used time of the given user */
        includeLastUsedTime?: boolean;
      };
    };
    responses: {
      /** Info about the user */
      200: {
        schema: definitions['DBUserInfo'];
      };
      /** Unauthorized or invalid credentials. */
      401: unknown;
      /** Forbidden */
      403: {
        schema: definitions['ErrorResponse'];
      };
      /** user not found */
      404: unknown;
      /** Request body is well-formed (i.e., syntactically correct), but semantically erroneous. */
      422: {
        schema: definitions['ErrorResponse'];
      };
      /** An error has occurred while trying to fulfill the request. Most likely the ErrorResponse will contain more information about the error. */
      500: {
        schema: definitions['ErrorResponse'];
      };
    };
  };
  createUser: {
    parameters: {
      path: {
        /** user id */
        user_id: string;
      };
    };
    responses: {
      /** User created successfully */
      201: {
        schema: definitions['UserApiKey'];
      };
      /** Malformed request. */
      400: {
        schema: definitions['ErrorResponse'];
      };
      /** Unauthorized or invalid credentials. */
      401: unknown;
      /** Forbidden */
      403: {
        schema: definitions['ErrorResponse'];
      };
      /** User already exists */
      409: {
        schema: definitions['ErrorResponse'];
      };
      /** Request body is well-formed (i.e., syntactically correct), but semantically erroneous. */
      422: {
        schema: definitions['ErrorResponse'];
      };
      /** An error has occurred while trying to fulfill the request. Most likely the ErrorResponse will contain more information about the error. */
      500: {
        schema: definitions['ErrorResponse'];
      };
    };
  };
  deleteUser: {
    parameters: {
      path: {
        /** user name */
        user_id: string;
      };
    };
    responses: {
      /** Successfully deleted. */
      204: never;
      /** Malformed request. */
      400: {
        schema: definitions['ErrorResponse'];
      };
      /** Unauthorized or invalid credentials. */
      401: unknown;
      /** Forbidden */
      403: {
        schema: definitions['ErrorResponse'];
      };
      /** user not found */
      404: unknown;
      /** Request body is well-formed (i.e., syntactically correct), but semantically erroneous. */
      422: {
        schema: definitions['ErrorResponse'];
      };
      /** An error has occurred while trying to fulfill the request. Most likely the ErrorResponse will contain more information about the error. */
      500: {
        schema: definitions['ErrorResponse'];
      };
    };
  };
  rotateUserApiKey: {
    parameters: {
      path: {
        /** user id */
        user_id: string;
      };
    };
    responses: {
      /** ApiKey successfully changed */
      200: {
        schema: definitions['UserApiKey'];
      };
      /** Malformed request. */
      400: {
        schema: definitions['ErrorResponse'];
      };
      /** Unauthorized or invalid credentials. */
      401: unknown;
      /** Forbidden */
      403: {
        schema: definitions['ErrorResponse'];
      };
      /** user not found */
      404: unknown;
      /** Request body is well-formed (i.e., syntactically correct), but semantically erroneous. */
      422: {
        schema: definitions['ErrorResponse'];
      };
      /** An error has occurred while trying to fulfill the request. Most likely the ErrorResponse will contain more information about the error. */
      500: {
        schema: definitions['ErrorResponse'];
      };
    };
  };
  activateUser: {
    parameters: {
      path: {
        /** user id */
        user_id: string;
      };
    };
    responses: {
      /** User successfully activated */
      200: unknown;
      /** Malformed request. */
      400: {
        schema: definitions['ErrorResponse'];
      };
      /** Unauthorized or invalid credentials. */
      401: unknown;
      /** Forbidden */
      403: {
        schema: definitions['ErrorResponse'];
      };
      /** user not found */
      404: unknown;
      /** user already activated */
      409: unknown;
      /** Request body is well-formed (i.e., syntactically correct), but semantically erroneous. */
      422: {
        schema: definitions['ErrorResponse'];
      };
      /** An error has occurred while trying to fulfill the request. Most likely the ErrorResponse will contain more information about the error. */
      500: {
        schema: definitions['ErrorResponse'];
      };
    };
  };
  deactivateUser: {
    parameters: {
      path: {
        /** user id */
        user_id: string;
      };
      body: {
        body?: {
          /**
           * @description if the key should be revoked when deactivating the user
           * @default false
           */
          revoke_key?: boolean;
        };
      };
    };
    responses: {
      /** users successfully deactivated */
      200: unknown;
      /** Malformed request. */
      400: {
        schema: definitions['ErrorResponse'];
      };
      /** Unauthorized or invalid credentials. */
      401: unknown;
      /** Forbidden */
      403: {
        schema: definitions['ErrorResponse'];
      };
      /** user not found */
      404: unknown;
      /** user already deactivated */
      409: unknown;
      /** Request body is well-formed (i.e., syntactically correct), but semantically erroneous. Are you sure the class is defined in the configuration file? */
      422: {
        schema: definitions['ErrorResponse'];
      };
      /** An error has occurred while trying to fulfill the request. Most likely the ErrorResponse will contain more information about the error. */
      500: {
        schema: definitions['ErrorResponse'];
      };
    };
  };
  getRoles: {
    responses: {
      /** Successful response. */
      200: {
        schema: definitions['RolesListResponse'];
      };
      /** Malformed request. */
      400: {
        schema: definitions['ErrorResponse'];
      };
      /** Unauthorized or invalid credentials. */
      401: unknown;
      /** Forbidden */
      403: {
        schema: definitions['ErrorResponse'];
      };
      /** An error has occurred while trying to fulfill the request. Most likely the ErrorResponse will contain more information about the error. */
      500: {
        schema: definitions['ErrorResponse'];
      };
    };
  };
  createRole: {
    parameters: {
      body: {
        body: definitions['Role'];
      };
    };
    responses: {
      /** Role created successfully */
      201: unknown;
      /** Malformed request. */
      400: {
        schema: definitions['ErrorResponse'];
      };
      /** Unauthorized or invalid credentials. */
      401: unknown;
      /** Forbidden */
      403: {
        schema: definitions['ErrorResponse'];
      };
      /** Role already exists */
      409: {
        schema: definitions['ErrorResponse'];
      };
      /** Request body is well-formed (i.e., syntactically correct), but semantically erroneous. Are you sure the class is defined in the configuration file? */
      422: {
        schema: definitions['ErrorResponse'];
      };
      /** An error has occurred while trying to fulfill the request. Most likely the ErrorResponse will contain more information about the error. */
      500: {
        schema: definitions['ErrorResponse'];
      };
    };
  };
  addPermissions: {
    parameters: {
      path: {
        /** role name */
        id: string;
      };
      body: {
        body: {
          /** @description permissions to be added to the role */
          permissions: definitions['Permission'][];
        } & {
          name: unknown;
        };
      };
    };
    responses: {
      /** Permissions added successfully */
      200: unknown;
      /** Malformed request. */
      400: {
        schema: definitions['ErrorResponse'];
      };
      /** Unauthorized or invalid credentials. */
      401: unknown;
      /** Forbidden */
      403: {
        schema: definitions['ErrorResponse'];
      };
      /** no role found */
      404: unknown;
      /** Request body is well-formed (i.e., syntactically correct), but semantically erroneous. Are you sure the class is defined in the configuration file? */
      422: {
        schema: definitions['ErrorResponse'];
      };
      /** An error has occurred while trying to fulfill the request. Most likely the ErrorResponse will contain more information about the error. */
      500: {
        schema: definitions['ErrorResponse'];
      };
    };
  };
  removePermissions: {
    parameters: {
      path: {
        /** role name */
        id: string;
      };
      body: {
        body: {
          /** @description permissions to remove from the role */
          permissions: definitions['Permission'][];
        };
      };
    };
    responses: {
      /** Permissions removed successfully */
      200: unknown;
      /** Malformed request. */
      400: {
        schema: definitions['ErrorResponse'];
      };
      /** Unauthorized or invalid credentials. */
      401: unknown;
      /** Forbidden */
      403: {
        schema: definitions['ErrorResponse'];
      };
      /** no role found */
      404: unknown;
      /** Request body is well-formed (i.e., syntactically correct), but semantically erroneous. Are you sure the class is defined in the configuration file? */
      422: {
        schema: definitions['ErrorResponse'];
      };
      /** An error has occurred while trying to fulfill the request. Most likely the ErrorResponse will contain more information about the error. */
      500: {
        schema: definitions['ErrorResponse'];
      };
    };
  };
  getRole: {
    parameters: {
      path: {
        /** role name */
        id: string;
      };
    };
    responses: {
      /** Successful response. */
      200: {
        schema: definitions['Role'];
      };
      /** Malformed request. */
      400: {
        schema: definitions['ErrorResponse'];
      };
      /** Unauthorized or invalid credentials. */
      401: unknown;
      /** Forbidden */
      403: {
        schema: definitions['ErrorResponse'];
      };
      /** no role found */
      404: unknown;
      /** An error has occurred while trying to fulfill the request. Most likely the ErrorResponse will contain more information about the error. */
      500: {
        schema: definitions['ErrorResponse'];
      };
    };
  };
  deleteRole: {
    parameters: {
      path: {
        /** role name */
        id: string;
      };
    };
    responses: {
      /** Successfully deleted. */
      204: never;
      /** Bad request */
      400: {
        schema: definitions['ErrorResponse'];
      };
      /** Unauthorized or invalid credentials. */
      401: unknown;
      /** Forbidden */
      403: {
        schema: definitions['ErrorResponse'];
      };
      /** An error has occurred while trying to fulfill the request. Most likely the ErrorResponse will contain more information about the error. */
      500: {
        schema: definitions['ErrorResponse'];
      };
    };
  };
  hasPermission: {
    parameters: {
      path: {
        /** role name */
        id: string;
      };
      body: {
        body: definitions['Permission'];
      };
    };
    responses: {
      /** Permission check was successful */
      200: {
        schema: boolean;
      };
      /** Malformed request. */
      400: {
        schema: definitions['ErrorResponse'];
      };
      /** Unauthorized or invalid credentials. */
      401: unknown;
      /** Forbidden */
      403: {
        schema: definitions['ErrorResponse'];
      };
      /** Request body is well-formed (i.e., syntactically correct), but semantically erroneous. Are you sure the class is defined in the configuration file? */
      422: {
        schema: definitions['ErrorResponse'];
      };
      /** An error has occurred while trying to fulfill the request. Most likely the ErrorResponse will contain more information about the error. */
      500: {
        schema: definitions['ErrorResponse'];
      };
    };
  };
  getUsersForRoleDeprecated: {
    parameters: {
      path: {
        /** role name */
        id: string;
      };
    };
    responses: {
      /** Users assigned to this role */
      200: {
        schema: string[];
      };
      /** Bad request */
      400: {
        schema: definitions['ErrorResponse'];
      };
      /** Unauthorized or invalid credentials. */
      401: unknown;
      /** Forbidden */
      403: {
        schema: definitions['ErrorResponse'];
      };
      /** no role found */
      404: unknown;
      /** An error has occurred while trying to fulfill the request. Most likely the ErrorResponse will contain more information about the error. */
      500: {
        schema: definitions['ErrorResponse'];
      };
    };
  };
  getUsersForRole: {
    parameters: {
      path: {
        /** role name */
        id: string;
      };
    };
    responses: {
      /** Users assigned to this role */
      200: {
        schema: ({
          userId?: string;
          userType: definitions['UserTypeOutput'];
        } & {
          name: unknown;
        })[];
      };
      /** Bad request */
      400: {
        schema: definitions['ErrorResponse'];
      };
      /** Unauthorized or invalid credentials. */
      401: unknown;
      /** Forbidden */
      403: {
        schema: definitions['ErrorResponse'];
      };
      /** no role found */
      404: unknown;
      /** An error has occurred while trying to fulfill the request. Most likely the ErrorResponse will contain more information about the error. */
      500: {
        schema: definitions['ErrorResponse'];
      };
    };
  };
  getRolesForUserDeprecated: {
    parameters: {
      path: {
        /** user name */
        id: string;
      };
    };
    responses: {
      /** Role assigned users */
      200: {
        schema: definitions['RolesListResponse'];
      };
      /** Bad request */
      400: {
        schema: definitions['ErrorResponse'];
      };
      /** Unauthorized or invalid credentials. */
      401: unknown;
      /** Forbidden */
      403: {
        schema: definitions['ErrorResponse'];
      };
      /** no role found for user */
      404: unknown;
      /** Request body is well-formed (i.e., syntactically correct), but semantically erroneous. Are you sure the class is defined in the configuration file? */
      422: {
        schema: definitions['ErrorResponse'];
      };
      /** An error has occurred while trying to fulfill the request. Most likely the ErrorResponse will contain more information about the error. */
      500: {
        schema: definitions['ErrorResponse'];
      };
    };
  };
  getRolesForUser: {
    parameters: {
      path: {
        /** user name */
        id: string;
        /** The type of user */
        userType: 'oidc' | 'db';
      };
      query: {
        /** Whether to include detailed role information needed the roles permission */
        includeFullRoles?: boolean;
      };
    };
    responses: {
      /** Role assigned users */
      200: {
        schema: definitions['RolesListResponse'];
      };
      /** Bad request */
      400: {
        schema: definitions['ErrorResponse'];
      };
      /** Unauthorized or invalid credentials. */
      401: unknown;
      /** Forbidden */
      403: {
        schema: definitions['ErrorResponse'];
      };
      /** no role found for user */
      404: unknown;
      /** Request body is well-formed (i.e., syntactically correct), but semantically erroneous. Are you sure the class is defined in the configuration file? */
      422: {
        schema: definitions['ErrorResponse'];
      };
      /** An error has occurred while trying to fulfill the request. Most likely the ErrorResponse will contain more information about the error. */
      500: {
        schema: definitions['ErrorResponse'];
      };
    };
  };
  assignRoleToUser: {
    parameters: {
      path: {
        /** user name */
        id: string;
      };
      body: {
        body: {
          /** @description the roles that assigned to user */
          roles?: string[];
          userType?: definitions['UserTypeInput'];
        };
      };
    };
    responses: {
      /** Role assigned successfully */
      200: unknown;
      /** Bad request */
      400: {
        schema: definitions['ErrorResponse'];
      };
      /** Unauthorized or invalid credentials. */
      401: unknown;
      /** Forbidden */
      403: {
        schema: definitions['ErrorResponse'];
      };
      /** role or user is not found. */
      404: {
        schema: definitions['ErrorResponse'];
      };
      /** An error has occurred while trying to fulfill the request. Most likely the ErrorResponse will contain more information about the error. */
      500: {
        schema: definitions['ErrorResponse'];
      };
    };
  };
  revokeRoleFromUser: {
    parameters: {
      path: {
        /** user name */
        id: string;
      };
      body: {
        body: {
          /** @description the roles that revoked from the key or user */
          roles?: string[];
          userType?: definitions['UserTypeInput'];
        };
      };
    };
    responses: {
      /** Role revoked successfully */
      200: unknown;
      /** Bad request */
      400: {
        schema: definitions['ErrorResponse'];
      };
      /** Unauthorized or invalid credentials. */
      401: unknown;
      /** Forbidden */
      403: {
        schema: definitions['ErrorResponse'];
      };
      /** role or user is not found. */
      404: {
        schema: definitions['ErrorResponse'];
      };
      /** An error has occurred while trying to fulfill the request. Most likely the ErrorResponse will contain more information about the error. */
      500: {
        schema: definitions['ErrorResponse'];
      };
    };
  };
  assignRoleToGroup: {
    parameters: {
      path: {
        /** group name */
        id: string;
      };
      body: {
        body: {
          /** @description the roles that assigned to group */
          roles?: string[];
        };
      };
    };
    responses: {
      /** Role assigned successfully */
      200: unknown;
      /** Bad request */
      400: {
        schema: definitions['ErrorResponse'];
      };
      /** Unauthorized or invalid credentials. */
      401: unknown;
      /** Forbidden */
      403: {
        schema: definitions['ErrorResponse'];
      };
      /** role or group is not found. */
      404: unknown;
      /** An error has occurred while trying to fulfill the request. Most likely the ErrorResponse will contain more information about the error. */
      500: {
        schema: definitions['ErrorResponse'];
      };
    };
  };
  revokeRoleFromGroup: {
    parameters: {
      path: {
        /** group name */
        id: string;
      };
      body: {
        body: {
          /** @description the roles that revoked from group */
          roles?: string[];
        };
      };
    };
    responses: {
      /** Role revoked successfully */
      200: unknown;
      /** Bad request */
      400: {
        schema: definitions['ErrorResponse'];
      };
      /** Unauthorized or invalid credentials. */
      401: unknown;
      /** Forbidden */
      403: {
        schema: definitions['ErrorResponse'];
      };
      /** role or group is not found. */
      404: unknown;
      /** An error has occurred while trying to fulfill the request. Most likely the ErrorResponse will contain more information about the error. */
      500: {
        schema: definitions['ErrorResponse'];
      };
    };
  };
  /** Lists all Objects in reverse order of creation, owned by the user that belongs to the used token. */
  'objects.list': {
    parameters: {
      query: {
        /** A threshold UUID of the objects to retrieve after, using an UUID-based ordering. This object is not part of the set. <br/><br/>Must be used with `class`, typically in conjunction with `limit`. <br/><br/>Note `after` cannot be used with `offset` or `sort`. <br/><br/>For a null value similar to offset=0, set an empty string in the request, i.e. `after=` or `after`. */
        after?: parameters['CommonAfterParameterQuery'];
        /** The starting index of the result window. Note `offset` will retrieve `offset+limit` results and return `limit` results from the object with index `offset` onwards. Limited by the value of `QUERY_MAXIMUM_RESULTS`. <br/><br/>Should be used in conjunction with `limit`. <br/><br/>Cannot be used with `after`. */
        offset?: parameters['CommonOffsetParameterQuery'];
        /** The maximum number of items to be returned per page. The default is 25 unless set otherwise as an environment variable. */
        limit?: parameters['CommonLimitParameterQuery'];
        /** Include additional information, such as classification infos. Allowed values include: classification, vector, interpretation */
        include?: parameters['CommonIncludeParameterQuery'];
        /** Name(s) of the property to sort by - e.g. `city`, or `country,city`. */
        sort?: parameters['CommonSortParameterQuery'];
        /** Order parameter to tell how to order (asc or desc) data within given field. Should be used in conjunction with `sort` parameter. If providing multiple `sort` values, provide multiple `order` values in corresponding order, e.g.: `sort=author_name,title&order=desc,asc`. */
        order?: parameters['CommonOrderParameterQuery'];
        /** The collection from which to query objects.  <br/><br/>Note that if `class` is not provided, the response will not include any objects. */
        class?: parameters['CommonClassParameterQuery'];
        /** Specifies the tenant in a request targeting a multi-tenant class */
        tenant?: parameters['CommonTenantParameterQuery'];
      };
    };
    responses: {
      /** Successful response. <br/><br/>If `class` is not provided, the response will not include any objects. */
      200: {
        schema: definitions['ObjectsListResponse'];
      };
      /** Malformed request. */
      400: {
        schema: definitions['ErrorResponse'];
      };
      /** Unauthorized or invalid credentials. */
      401: unknown;
      /** Forbidden */
      403: {
        schema: definitions['ErrorResponse'];
      };
      /** Successful query result but no resource was found. */
      404: unknown;
      /** Request body is well-formed (i.e., syntactically correct), but semantically erroneous. Are you sure the class is defined in the configuration file? */
      422: {
        schema: definitions['ErrorResponse'];
      };
      /** An error has occurred while trying to fulfill the request. Most likely the ErrorResponse will contain more information about the error. */
      500: {
        schema: definitions['ErrorResponse'];
      };
    };
  };
  /** Create a new object. <br/><br/>Meta-data and schema values are validated. <br/><br/>**Note: Use `/batch` for importing many objects**: <br/>If you plan on importing a large number of objects, it's much more efficient to use the `/batch` endpoint. Otherwise, sending multiple single requests sequentially would incur a large performance penalty. <br/><br/>**Note: idempotence of `/objects`**: <br/>POST /objects will fail if an id is provided which already exists in the class. To update an existing object with the objects endpoint, use the PUT or PATCH method. */
  'objects.create': {
    parameters: {
      body: {
        body: definitions['Object'];
      };
      query: {
        /** Determines how many replicas must acknowledge a request before it is considered successful */
        consistency_level?: parameters['CommonConsistencyLevelParameterQuery'];
      };
    };
    responses: {
      /** Object created. */
      200: {
        schema: definitions['Object'];
      };
      /** Malformed request. */
      400: {
        schema: definitions['ErrorResponse'];
      };
      /** Unauthorized or invalid credentials. */
      401: unknown;
      /** Forbidden */
      403: {
        schema: definitions['ErrorResponse'];
      };
      /** Request body is well-formed (i.e., syntactically correct), but semantically erroneous. Are you sure the class is defined in the configuration file? */
      422: {
        schema: definitions['ErrorResponse'];
      };
      /** An error has occurred while trying to fulfill the request. Most likely the ErrorResponse will contain more information about the error. */
      500: {
        schema: definitions['ErrorResponse'];
      };
    };
  };
  /** Get a specific object based on its UUID. Also available as Websocket bus. */
  'objects.get': {
    parameters: {
      path: {
        /** Unique ID of the Object. */
        id: string;
      };
      query: {
        /** Include additional information, such as classification infos. Allowed values include: classification, vector, interpretation */
        include?: parameters['CommonIncludeParameterQuery'];
      };
    };
    responses: {
      /** Successful response. */
      200: {
        schema: definitions['Object'];
      };
      /** Malformed request. */
      400: {
        schema: definitions['ErrorResponse'];
      };
      /** Unauthorized or invalid credentials. */
      401: unknown;
      /** Forbidden */
      403: {
        schema: definitions['ErrorResponse'];
      };
      /** Successful query result but no resource was found. */
      404: unknown;
      /** An error has occurred while trying to fulfill the request. Most likely the ErrorResponse will contain more information about the error. */
      500: {
        schema: definitions['ErrorResponse'];
      };
    };
  };
  /** Updates an object based on its UUID. Given meta-data and schema values are validated. LastUpdateTime is set to the time this function is called. */
  'objects.update': {
    parameters: {
      path: {
        /** Unique ID of the Object. */
        id: string;
      };
      body: {
        body: definitions['Object'];
      };
      query: {
        /** Determines how many replicas must acknowledge a request before it is considered successful */
        consistency_level?: parameters['CommonConsistencyLevelParameterQuery'];
      };
    };
    responses: {
      /** Successfully received. */
      200: {
        schema: definitions['Object'];
      };
      /** Unauthorized or invalid credentials. */
      401: unknown;
      /** Forbidden */
      403: {
        schema: definitions['ErrorResponse'];
      };
      /** Successful query result but no resource was found. */
      404: unknown;
      /** Request body is well-formed (i.e., syntactically correct), but semantically erroneous. Are you sure the class is defined in the configuration file? */
      422: {
        schema: definitions['ErrorResponse'];
      };
      /** An error has occurred while trying to fulfill the request. Most likely the ErrorResponse will contain more information about the error. */
      500: {
        schema: definitions['ErrorResponse'];
      };
    };
  };
  /** Deletes an object from the database based on its UUID. */
  'objects.delete': {
    parameters: {
      path: {
        /** Unique ID of the Object. */
        id: string;
      };
      query: {
        /** Determines how many replicas must acknowledge a request before it is considered successful */
        consistency_level?: parameters['CommonConsistencyLevelParameterQuery'];
        /** Specifies the tenant in a request targeting a multi-tenant class */
        tenant?: parameters['CommonTenantParameterQuery'];
      };
    };
    responses: {
      /** Successfully deleted. */
      204: never;
      /** Unauthorized or invalid credentials. */
      401: unknown;
      /** Forbidden */
      403: {
        schema: definitions['ErrorResponse'];
      };
      /** Successful query result but no resource was found. */
      404: unknown;
      /** An error has occurred while trying to fulfill the request. Most likely the ErrorResponse will contain more information about the error. */
      500: {
        schema: definitions['ErrorResponse'];
      };
    };
  };
  /** Checks if an object exists in the system based on its UUID. */
  'objects.head': {
    parameters: {
      path: {
        /** Unique ID of the Object. */
        id: string;
      };
    };
    responses: {
      /** Object exists. */
      204: never;
      /** Unauthorized or invalid credentials. */
      401: unknown;
      /** Forbidden */
      403: {
        schema: definitions['ErrorResponse'];
      };
      /** Object doesn't exist. */
      404: unknown;
      /** An error has occurred while trying to fulfill the request. Most likely the ErrorResponse will contain more information about the error. */
      500: {
        schema: definitions['ErrorResponse'];
      };
    };
  };
  /** Update an object based on its UUID (using patch semantics). This method supports json-merge style patch semantics (RFC 7396). Provided meta-data and schema values are validated. LastUpdateTime is set to the time this function is called. */
  'objects.patch': {
    parameters: {
      path: {
        /** Unique ID of the Object. */
        id: string;
      };
      body: {
        /** RFC 7396-style patch, the body contains the object to merge into the existing object. */
        body?: definitions['Object'];
      };
      query: {
        /** Determines how many replicas must acknowledge a request before it is considered successful */
        consistency_level?: parameters['CommonConsistencyLevelParameterQuery'];
      };
    };
    responses: {
      /** Successfully applied. No content provided. */
      204: never;
      /** The patch-JSON is malformed. */
      400: unknown;
      /** Unauthorized or invalid credentials. */
      401: unknown;
      /** Forbidden */
      403: {
        schema: definitions['ErrorResponse'];
      };
      /** Successful query result but no resource was found. */
      404: unknown;
      /** The patch-JSON is valid but unprocessable. */
      422: {
        schema: definitions['ErrorResponse'];
      };
      /** An error has occurred while trying to fulfill the request. Most likely the ErrorResponse will contain more information about the error. */
      500: {
        schema: definitions['ErrorResponse'];
      };
    };
  };
  /** Get a data object based on its collection and UUID. Also available as Websocket bus. */
  'objects.class.get': {
    parameters: {
      path: {
        className: string;
        /** Unique ID of the Object. */
        id: string;
      };
      query: {
        /** Include additional information, such as classification infos. Allowed values include: classification, vector, interpretation */
        include?: parameters['CommonIncludeParameterQuery'];
        /** Determines how many replicas must acknowledge a request before it is considered successful */
        consistency_level?: parameters['CommonConsistencyLevelParameterQuery'];
        /** The target node which should fulfill the request */
        node_name?: parameters['CommonNodeNameParameterQuery'];
        /** Specifies the tenant in a request targeting a multi-tenant class */
        tenant?: parameters['CommonTenantParameterQuery'];
      };
    };
    responses: {
      /** Successful response. */
      200: {
        schema: definitions['Object'];
      };
      /** Malformed request. */
      400: {
        schema: definitions['ErrorResponse'];
      };
      /** Unauthorized or invalid credentials. */
      401: unknown;
      /** Forbidden */
      403: {
        schema: definitions['ErrorResponse'];
      };
      /** Successful query result but no resource was found. */
      404: unknown;
      /** Request is well-formed (i.e., syntactically correct), but erroneous. */
      422: {
        schema: definitions['ErrorResponse'];
      };
      /** An error has occurred while trying to fulfill the request. Most likely the ErrorResponse will contain more information about the error. */
      500: {
        schema: definitions['ErrorResponse'];
      };
    };
  };
  /** Update an object based on its uuid and collection. This (`put`) method replaces the object with the provided object. */
  'objects.class.put': {
    parameters: {
      path: {
        className: string;
        /** The uuid of the data object to update. */
        id: string;
      };
      body: {
        body: definitions['Object'];
      };
      query: {
        /** Determines how many replicas must acknowledge a request before it is considered successful */
        consistency_level?: parameters['CommonConsistencyLevelParameterQuery'];
      };
    };
    responses: {
      /** Successfully received. */
      200: {
        schema: definitions['Object'];
      };
      /** Unauthorized or invalid credentials. */
      401: unknown;
      /** Forbidden */
      403: {
        schema: definitions['ErrorResponse'];
      };
      /** Successful query result but no resource was found. */
      404: unknown;
      /** Request body is well-formed (i.e., syntactically correct), but semantically erroneous. Are you sure the class is defined in the configuration file? */
      422: {
        schema: definitions['ErrorResponse'];
      };
      /** An error has occurred while trying to fulfill the request. Most likely the ErrorResponse will contain more information about the error. */
      500: {
        schema: definitions['ErrorResponse'];
      };
    };
  };
  /** Delete an object based on its collection and UUID. <br/><br/>Note: For backward compatibility, beacons also support an older, deprecated format without the collection name. As a result, when deleting a reference, the beacon specified has to match the beacon to be deleted exactly. In other words, if a beacon is present using the old format (without collection name) you also need to specify it the same way. <br/><br/>In the beacon format, you need to always use `localhost` as the host, rather than the actual hostname. `localhost` here refers to the fact that the beacon's target is on the same Weaviate instance, as opposed to a foreign instance. */
  'objects.class.delete': {
    parameters: {
      path: {
        className: string;
        /** Unique ID of the Object. */
        id: string;
      };
      query: {
        /** Determines how many replicas must acknowledge a request before it is considered successful */
        consistency_level?: parameters['CommonConsistencyLevelParameterQuery'];
        /** Specifies the tenant in a request targeting a multi-tenant class */
        tenant?: parameters['CommonTenantParameterQuery'];
      };
    };
    responses: {
      /** Successfully deleted. */
      204: never;
      /** Malformed request. */
      400: {
        schema: definitions['ErrorResponse'];
      };
      /** Unauthorized or invalid credentials. */
      401: unknown;
      /** Forbidden */
      403: {
        schema: definitions['ErrorResponse'];
      };
      /** Successful query result but no resource was found. */
      404: unknown;
      /** Request is well-formed (i.e., syntactically correct), but erroneous. */
      422: {
        schema: definitions['ErrorResponse'];
      };
      /** An error has occurred while trying to fulfill the request. Most likely the ErrorResponse will contain more information about the error. */
      500: {
        schema: definitions['ErrorResponse'];
      };
    };
  };
  /** Checks if a data object exists based on its collection and uuid without retrieving it. <br/><br/>Internally it skips reading the object from disk other than checking if it is present. Thus it does not use resources on marshalling, parsing, etc., and is faster. Note the resulting HTTP request has no body; the existence of an object is indicated solely by the status code. */
  'objects.class.head': {
    parameters: {
      path: {
        /** The class name as defined in the schema */
        className: string;
        /** The uuid of the data object */
        id: string;
      };
      query: {
        /** Determines how many replicas must acknowledge a request before it is considered successful */
        consistency_level?: parameters['CommonConsistencyLevelParameterQuery'];
        /** Specifies the tenant in a request targeting a multi-tenant class */
        tenant?: parameters['CommonTenantParameterQuery'];
      };
    };
    responses: {
      /** Object exists. */
      204: never;
      /** Unauthorized or invalid credentials. */
      401: unknown;
      /** Forbidden */
      403: {
        schema: definitions['ErrorResponse'];
      };
      /** Object doesn't exist. */
      404: unknown;
      /** Request is well-formed (i.e., syntactically correct), but erroneous. */
      422: {
        schema: definitions['ErrorResponse'];
      };
      /** An error has occurred while trying to fulfill the request. Most likely the ErrorResponse will contain more information about the error. */
      500: {
        schema: definitions['ErrorResponse'];
      };
    };
  };
  /** Update an individual data object based on its class and uuid. This method supports json-merge style patch semantics (RFC 7396). Provided meta-data and schema values are validated. LastUpdateTime is set to the time this function is called. */
  'objects.class.patch': {
    parameters: {
      path: {
        /** The class name as defined in the schema */
        className: string;
        /** The uuid of the data object to update. */
        id: string;
      };
      body: {
        /** RFC 7396-style patch, the body contains the object to merge into the existing object. */
        body?: definitions['Object'];
      };
      query: {
        /** Determines how many replicas must acknowledge a request before it is considered successful */
        consistency_level?: parameters['CommonConsistencyLevelParameterQuery'];
      };
    };
    responses: {
      /** Successfully applied. No content provided. */
      204: never;
      /** The patch-JSON is malformed. */
      400: {
        schema: definitions['ErrorResponse'];
      };
      /** Unauthorized or invalid credentials. */
      401: unknown;
      /** Forbidden */
      403: {
        schema: definitions['ErrorResponse'];
      };
      /** Successful query result but no resource was found. */
      404: unknown;
      /** The patch-JSON is valid but unprocessable. */
      422: {
        schema: definitions['ErrorResponse'];
      };
      /** An error has occurred while trying to fulfill the request. Most likely the ErrorResponse will contain more information about the error. */
      500: {
        schema: definitions['ErrorResponse'];
      };
    };
  };
  /** Replace all references in cross-reference property of an object. */
  'objects.references.update': {
    parameters: {
      path: {
        /** Unique ID of the Object. */
        id: string;
        /** Unique name of the property related to the Object. */
        propertyName: string;
      };
      body: {
        body: definitions['MultipleRef'];
      };
      query: {
        /** Specifies the tenant in a request targeting a multi-tenant class */
        tenant?: parameters['CommonTenantParameterQuery'];
      };
    };
    responses: {
      /** Successfully replaced all the references. */
      200: unknown;
      /** Unauthorized or invalid credentials. */
      401: unknown;
      /** Forbidden */
      403: {
        schema: definitions['ErrorResponse'];
      };
      /** Request body is well-formed (i.e., syntactically correct), but semantically erroneous. Are you sure the property exists or that it is a class? */
      422: {
        schema: definitions['ErrorResponse'];
      };
      /** An error has occurred while trying to fulfill the request. Most likely the ErrorResponse will contain more information about the error. */
      500: {
        schema: definitions['ErrorResponse'];
      };
    };
  };
  /** Add a cross-reference. */
  'objects.references.create': {
    parameters: {
      path: {
        /** Unique ID of the Object. */
        id: string;
        /** Unique name of the property related to the Object. */
        propertyName: string;
      };
      body: {
        body: definitions['SingleRef'];
      };
      query: {
        /** Specifies the tenant in a request targeting a multi-tenant class */
        tenant?: parameters['CommonTenantParameterQuery'];
      };
    };
    responses: {
      /** Successfully added the reference. */
      200: unknown;
      /** Unauthorized or invalid credentials. */
      401: unknown;
      /** Forbidden */
      403: {
        schema: definitions['ErrorResponse'];
      };
      /** Request body is well-formed (i.e., syntactically correct), but semantically erroneous. Are you sure the property exists or that it is a class? */
      422: {
        schema: definitions['ErrorResponse'];
      };
      /** An error has occurred while trying to fulfill the request. Most likely the ErrorResponse will contain more information about the error. */
      500: {
        schema: definitions['ErrorResponse'];
      };
    };
  };
  /** Delete the single reference that is given in the body from the list of references that this property has. */
  'objects.references.delete': {
    parameters: {
      path: {
        /** Unique ID of the Object. */
        id: string;
        /** Unique name of the property related to the Object. */
        propertyName: string;
      };
      body: {
        body: definitions['SingleRef'];
      };
      query: {
        /** Specifies the tenant in a request targeting a multi-tenant class */
        tenant?: parameters['CommonTenantParameterQuery'];
      };
    };
    responses: {
      /** Successfully deleted. */
      204: never;
      /** Unauthorized or invalid credentials. */
      401: unknown;
      /** Forbidden */
      403: {
        schema: definitions['ErrorResponse'];
      };
      /** Successful query result but no resource was found. */
      404: {
        schema: definitions['ErrorResponse'];
      };
      /** An error has occurred while trying to fulfill the request. Most likely the ErrorResponse will contain more information about the error. */
      500: {
        schema: definitions['ErrorResponse'];
      };
    };
  };
  /** Replace **all** references in cross-reference property of an object. */
  'objects.class.references.put': {
    parameters: {
      path: {
        /** The class name as defined in the schema */
        className: string;
        /** Unique ID of the Object. */
        id: string;
        /** Unique name of the property related to the Object. */
        propertyName: string;
      };
      body: {
        body: definitions['MultipleRef'];
      };
      query: {
        /** Determines how many replicas must acknowledge a request before it is considered successful */
        consistency_level?: parameters['CommonConsistencyLevelParameterQuery'];
        /** Specifies the tenant in a request targeting a multi-tenant class */
        tenant?: parameters['CommonTenantParameterQuery'];
      };
    };
    responses: {
      /** Successfully replaced all the references. */
      200: unknown;
      /** Malformed request. */
      400: {
        schema: definitions['ErrorResponse'];
      };
      /** Unauthorized or invalid credentials. */
      401: unknown;
      /** Forbidden */
      403: {
        schema: definitions['ErrorResponse'];
      };
      /** Source object doesn't exist. */
      404: unknown;
      /** Request body is well-formed (i.e., syntactically correct), but semantically erroneous. Are you sure the property exists or that it is a class? */
      422: {
        schema: definitions['ErrorResponse'];
      };
      /** An error has occurred while trying to fulfill the request. Most likely the ErrorResponse will contain more information about the error. */
      500: {
        schema: definitions['ErrorResponse'];
      };
    };
  };
  /** Add a single reference to an object. This adds a reference to the array of cross-references of the given property in the source object specified by its collection name and id */
  'objects.class.references.create': {
    parameters: {
      path: {
        /** The class name as defined in the schema */
        className: string;
        /** Unique ID of the Object. */
        id: string;
        /** Unique name of the property related to the Object. */
        propertyName: string;
      };
      body: {
        body: definitions['SingleRef'];
      };
      query: {
        /** Determines how many replicas must acknowledge a request before it is considered successful */
        consistency_level?: parameters['CommonConsistencyLevelParameterQuery'];
        /** Specifies the tenant in a request targeting a multi-tenant class */
        tenant?: parameters['CommonTenantParameterQuery'];
      };
    };
    responses: {
      /** Successfully added the reference. */
      200: unknown;
      /** Malformed request. */
      400: {
        schema: definitions['ErrorResponse'];
      };
      /** Unauthorized or invalid credentials. */
      401: unknown;
      /** Forbidden */
      403: {
        schema: definitions['ErrorResponse'];
      };
      /** Source object doesn't exist. */
      404: unknown;
      /** Request body is well-formed (i.e., syntactically correct), but semantically erroneous. Are you sure the property exists or that it is a class? */
      422: {
        schema: definitions['ErrorResponse'];
      };
      /** An error has occurred while trying to fulfill the request. Most likely the ErrorResponse will contain more information about the error. */
      500: {
        schema: definitions['ErrorResponse'];
      };
    };
  };
  /** Delete the single reference that is given in the body from the list of references that this property has. */
  'objects.class.references.delete': {
    parameters: {
      path: {
        /** The class name as defined in the schema */
        className: string;
        /** Unique ID of the Object. */
        id: string;
        /** Unique name of the property related to the Object. */
        propertyName: string;
      };
      body: {
        body: definitions['SingleRef'];
      };
      query: {
        /** Determines how many replicas must acknowledge a request before it is considered successful */
        consistency_level?: parameters['CommonConsistencyLevelParameterQuery'];
        /** Specifies the tenant in a request targeting a multi-tenant class */
        tenant?: parameters['CommonTenantParameterQuery'];
      };
    };
    responses: {
      /** Successfully deleted. */
      204: never;
      /** Malformed request. */
      400: {
        schema: definitions['ErrorResponse'];
      };
      /** Unauthorized or invalid credentials. */
      401: unknown;
      /** Forbidden */
      403: {
        schema: definitions['ErrorResponse'];
      };
      /** Successful query result but no resource was found. */
      404: {
        schema: definitions['ErrorResponse'];
      };
      /** Request body is well-formed (i.e., syntactically correct), but semantically erroneous. Are you sure the property exists or that it is a class? */
      422: {
        schema: definitions['ErrorResponse'];
      };
      /** An error has occurred while trying to fulfill the request. Most likely the ErrorResponse will contain more information about the error. */
      500: {
        schema: definitions['ErrorResponse'];
      };
    };
  };
  /** Validate an object's schema and meta-data without creating it. <br/><br/>If the schema of the object is valid, the request should return nothing with a plain RESTful request. Otherwise, an error object will be returned. */
  'objects.validate': {
    parameters: {
      body: {
        body: definitions['Object'];
      };
    };
    responses: {
      /** Successfully validated. */
      200: unknown;
      /** Unauthorized or invalid credentials. */
      401: unknown;
      /** Forbidden */
      403: {
        schema: definitions['ErrorResponse'];
      };
      /** Request body is well-formed (i.e., syntactically correct), but semantically erroneous. Are you sure the class is defined in the configuration file? */
      422: {
        schema: definitions['ErrorResponse'];
      };
      /** An error has occurred while trying to fulfill the request. Most likely the ErrorResponse will contain more information about the error. */
      500: {
        schema: definitions['ErrorResponse'];
      };
    };
  };
  /** Create new objects in bulk. <br/><br/>Meta-data and schema values are validated. <br/><br/>**Note: idempotence of `/batch/objects`**: <br/>`POST /batch/objects` is idempotent, and will overwrite any existing object given the same id. */
  'batch.objects.create': {
    parameters: {
      body: {
        body: {
          /** @description Define which fields need to be returned. Default value is ALL */
          fields?: ('ALL' | 'class' | 'schema' | 'id' | 'creationTimeUnix')[];
          objects?: definitions['Object'][];
        };
      };
      query: {
        /** Determines how many replicas must acknowledge a request before it is considered successful */
        consistency_level?: parameters['CommonConsistencyLevelParameterQuery'];
      };
    };
    responses: {
      /** Request succeeded, see response body to get detailed information about each batched item. */
      200: {
        schema: definitions['ObjectsGetResponse'][];
      };
      /** Malformed request. */
      400: {
        schema: definitions['ErrorResponse'];
      };
      /** Unauthorized or invalid credentials. */
      401: unknown;
      /** Forbidden */
      403: {
        schema: definitions['ErrorResponse'];
      };
      /** Request body is well-formed (i.e., syntactically correct), but semantically erroneous. Are you sure the class is defined in the configuration file? */
      422: {
        schema: definitions['ErrorResponse'];
      };
      /** An error has occurred while trying to fulfill the request. Most likely the ErrorResponse will contain more information about the error. */
      500: {
        schema: definitions['ErrorResponse'];
      };
    };
  };
  /** Batch delete objects that match a particular filter. <br/><br/>The request body takes a single `where` filter and will delete all objects matched. <br/><br/>Note that there is a limit to the number of objects to be deleted at once using this filter, in order to protect against unexpected memory surges and very-long-running requests. The default limit is 10,000 and may be configured by setting the `QUERY_MAXIMUM_RESULTS` environment variable. <br/><br/>Objects are deleted in the same order that they would be returned in an equivalent Get query. To delete more objects than the limit, run the same query multiple times. */
  'batch.objects.delete': {
    parameters: {
      body: {
        body: definitions['BatchDelete'];
      };
      query: {
        /** Determines how many replicas must acknowledge a request before it is considered successful */
        consistency_level?: parameters['CommonConsistencyLevelParameterQuery'];
        /** Specifies the tenant in a request targeting a multi-tenant class */
        tenant?: parameters['CommonTenantParameterQuery'];
      };
    };
    responses: {
      /** Request succeeded, see response body to get detailed information about each batched item. */
      200: {
        schema: definitions['BatchDeleteResponse'];
      };
      /** Malformed request. */
      400: {
        schema: definitions['ErrorResponse'];
      };
      /** Unauthorized or invalid credentials. */
      401: unknown;
      /** Forbidden */
      403: {
        schema: definitions['ErrorResponse'];
      };
      /** Request body is well-formed (i.e., syntactically correct), but semantically erroneous. Are you sure the class is defined in the configuration file? */
      422: {
        schema: definitions['ErrorResponse'];
      };
      /** An error has occurred while trying to fulfill the request. Most likely the ErrorResponse will contain more information about the error. */
      500: {
        schema: definitions['ErrorResponse'];
      };
    };
  };
  /** Batch create cross-references between collections items (objects or objects) in bulk. */
  'batch.references.create': {
    parameters: {
      body: {
        /** A list of references to be batched. The ideal size depends on the used database connector. Please see the documentation of the used connector for help */
        body: definitions['BatchReference'][];
      };
      query: {
        /** Determines how many replicas must acknowledge a request before it is considered successful */
        consistency_level?: parameters['CommonConsistencyLevelParameterQuery'];
      };
    };
    responses: {
      /** Request Successful. Warning: A successful request does not guarantee that every batched reference was successfully created. Inspect the response body to see which references succeeded and which failed. */
      200: {
        schema: definitions['BatchReferenceResponse'][];
      };
      /** Malformed request. */
      400: {
        schema: definitions['ErrorResponse'];
      };
      /** Unauthorized or invalid credentials. */
      401: unknown;
      /** Forbidden */
      403: {
        schema: definitions['ErrorResponse'];
      };
      /** Request body is well-formed (i.e., syntactically correct), but semantically erroneous. Are you sure the class is defined in the configuration file? */
      422: {
        schema: definitions['ErrorResponse'];
      };
      /** An error has occurred while trying to fulfill the request. Most likely the ErrorResponse will contain more information about the error. */
      500: {
        schema: definitions['ErrorResponse'];
      };
    };
  };
  /** Get a response based on a GraphQL query */
  'graphql.post': {
    parameters: {
      body: {
        /** The GraphQL query request parameters. */
        body: definitions['GraphQLQuery'];
      };
    };
    responses: {
      /** Successful query (with select). */
      200: {
        schema: definitions['GraphQLResponse'];
      };
      /** Unauthorized or invalid credentials. */
      401: unknown;
      /** Forbidden */
      403: {
        schema: definitions['ErrorResponse'];
      };
      /** Request body is well-formed (i.e., syntactically correct), but semantically erroneous. Are you sure the class is defined in the configuration file? */
      422: {
        schema: definitions['ErrorResponse'];
      };
      /** An error has occurred while trying to fulfill the request. Most likely the ErrorResponse will contain more information about the error. */
      500: {
        schema: definitions['ErrorResponse'];
      };
    };
  };
  /** Perform a batched GraphQL query */
  'graphql.batch': {
    parameters: {
      body: {
        /** The GraphQL queries. */
        body: definitions['GraphQLQueries'];
      };
    };
    responses: {
      /** Successful query (with select). */
      200: {
        schema: definitions['GraphQLResponses'];
      };
      /** Unauthorized or invalid credentials. */
      401: unknown;
      /** Forbidden */
      403: {
        schema: definitions['ErrorResponse'];
      };
      /** Request body is well-formed (i.e., syntactically correct), but semantically erroneous. Are you sure the class is defined in the configuration file? */
      422: {
        schema: definitions['ErrorResponse'];
      };
      /** An error has occurred while trying to fulfill the request. Most likely the ErrorResponse will contain more information about the error. */
      500: {
        schema: definitions['ErrorResponse'];
      };
    };
  };
  /** Returns meta information about the server. Can be used to provide information to another Weaviate instance that wants to interact with the current instance. */
  'meta.get': {
    responses: {
      /** Successful response. */
      200: {
        schema: definitions['Meta'];
      };
      /** Unauthorized or invalid credentials. */
      401: unknown;
      /** Forbidden */
      403: {
        schema: definitions['ErrorResponse'];
      };
      /** An error has occurred while trying to fulfill the request. Most likely the ErrorResponse will contain more information about the error. */
      500: {
        schema: definitions['ErrorResponse'];
      };
    };
  };
  /** Fetch an array of all collection definitions from the schema. */
  'schema.dump': {
    parameters: {
      header: {
        /** If consistency is true, the request will be proxied to the leader to ensure strong schema consistency */
        consistency?: boolean;
      };
    };
    responses: {
      /** Successfully dumped the database schema. */
      200: {
        schema: definitions['Schema'];
      };
      /** Unauthorized or invalid credentials. */
      401: unknown;
      /** Forbidden */
      403: {
        schema: definitions['ErrorResponse'];
      };
      /** An error has occurred while trying to fulfill the request. Most likely the ErrorResponse will contain more information about the error. */
      500: {
        schema: definitions['ErrorResponse'];
      };
    };
  };
  /** Create a new data object collection. <br/><br/>If AutoSchema is enabled, Weaviate will attempt to infer the schema from the data at import time. However, manual schema definition is recommended for production environments. */
  'schema.objects.create': {
    parameters: {
      body: {
        objectClass: definitions['Class'];
      };
    };
    responses: {
      /** Added the new Object class to the schema. */
      200: {
        schema: definitions['Class'];
      };
      /** Unauthorized or invalid credentials. */
      401: unknown;
      /** Forbidden */
      403: {
        schema: definitions['ErrorResponse'];
      };
      /** Invalid Object class */
      422: {
        schema: definitions['ErrorResponse'];
      };
      /** An error has occurred while trying to fulfill the request. Most likely the ErrorResponse will contain more information about the error. */
      500: {
        schema: definitions['ErrorResponse'];
      };
    };
  };
  'schema.objects.get': {
    parameters: {
      path: {
        className: string;
      };
      header: {
        /** If consistency is true, the request will be proxied to the leader to ensure strong schema consistency */
        consistency?: boolean;
      };
    };
    responses: {
      /** Found the Class, returned as body */
      200: {
        schema: definitions['Class'];
      };
      /** Unauthorized or invalid credentials. */
      401: unknown;
      /** Forbidden */
      403: {
        schema: definitions['ErrorResponse'];
      };
      /** This class does not exist */
      404: unknown;
      /** An error has occurred while trying to fulfill the request. Most likely the ErrorResponse will contain more information about the error. */
      500: {
        schema: definitions['ErrorResponse'];
      };
    };
  };
  /** Add a property to an existing collection. */
  'schema.objects.update': {
    parameters: {
      path: {
        className: string;
      };
      body: {
        objectClass: definitions['Class'];
      };
    };
    responses: {
      /** Class was updated successfully */
      200: {
        schema: definitions['Class'];
      };
      /** Unauthorized or invalid credentials. */
      401: unknown;
      /** Forbidden */
      403: {
        schema: definitions['ErrorResponse'];
      };
      /** Class to be updated does not exist */
      404: {
        schema: definitions['ErrorResponse'];
      };
      /** Invalid update attempt */
      422: {
        schema: definitions['ErrorResponse'];
      };
      /** An error has occurred while trying to fulfill the request. Most likely the ErrorResponse will contain more information about the error. */
      500: {
        schema: definitions['ErrorResponse'];
      };
    };
  };
  /** Remove a collection from the schema. This will also delete all the objects in the collection. */
  'schema.objects.delete': {
    parameters: {
      path: {
        className: string;
      };
    };
    responses: {
      /** Removed the Object class from the schema. */
      200: unknown;
      /** Could not delete the Object class. */
      400: {
        schema: definitions['ErrorResponse'];
      };
      /** Unauthorized or invalid credentials. */
      401: unknown;
      /** Forbidden */
      403: {
        schema: definitions['ErrorResponse'];
      };
      /** An error has occurred while trying to fulfill the request. Most likely the ErrorResponse will contain more information about the error. */
      500: {
        schema: definitions['ErrorResponse'];
      };
    };
  };
  'schema.objects.properties.add': {
    parameters: {
      path: {
        className: string;
      };
      body: {
        body: definitions['Property'];
      };
    };
    responses: {
      /** Added the property. */
      200: {
        schema: definitions['Property'];
      };
      /** Unauthorized or invalid credentials. */
      401: unknown;
      /** Forbidden */
      403: {
        schema: definitions['ErrorResponse'];
      };
      /** Invalid property. */
      422: {
        schema: definitions['ErrorResponse'];
      };
      /** An error has occurred while trying to fulfill the request. Most likely the ErrorResponse will contain more information about the error. */
      500: {
        schema: definitions['ErrorResponse'];
      };
    };
  };
  /** Get the status of every shard in the cluster. */
  'schema.objects.shards.get': {
    parameters: {
      path: {
        className: string;
      };
      query: {
        tenant?: string;
      };
    };
    responses: {
      /** Found the status of the shards, returned as body */
      200: {
        schema: definitions['ShardStatusList'];
      };
      /** Unauthorized or invalid credentials. */
      401: unknown;
      /** Forbidden */
      403: {
        schema: definitions['ErrorResponse'];
      };
      /** This class does not exist */
      404: {
        schema: definitions['ErrorResponse'];
      };
      /** An error has occurred while trying to fulfill the request. Most likely the ErrorResponse will contain more information about the error. */
      500: {
        schema: definitions['ErrorResponse'];
      };
    };
  };
  /** Update a shard status for a collection. For example, a shard may have been marked as `READONLY` because its disk was full. After providing more disk space, use this endpoint to set the shard status to `READY` again. There is also a convenience function in each client to set the status of all shards of a collection. */
  'schema.objects.shards.update': {
    parameters: {
      path: {
        className: string;
        shardName: string;
      };
      body: {
        body: definitions['ShardStatus'];
      };
    };
    responses: {
      /** Shard status was updated successfully */
      200: {
        schema: definitions['ShardStatus'];
      };
      /** Unauthorized or invalid credentials. */
      401: unknown;
      /** Forbidden */
      403: {
        schema: definitions['ErrorResponse'];
      };
      /** Shard to be updated does not exist */
      404: {
        schema: definitions['ErrorResponse'];
      };
      /** Invalid update attempt */
      422: {
        schema: definitions['ErrorResponse'];
      };
      /** An error has occurred while trying to fulfill the request. Most likely the ErrorResponse will contain more information about the error. */
      500: {
        schema: definitions['ErrorResponse'];
      };
    };
  };
  /** get all tenants from a specific class */
  'tenants.get': {
    parameters: {
      path: {
        className: string;
      };
      header: {
        /** If consistency is true, the request will be proxied to the leader to ensure strong schema consistency */
        consistency?: boolean;
      };
    };
    responses: {
      /** tenants from specified class. */
      200: {
        schema: definitions['Tenant'][];
      };
      /** Unauthorized or invalid credentials. */
      401: unknown;
      /** Forbidden */
      403: {
        schema: definitions['ErrorResponse'];
      };
      /** Invalid Tenant class */
      422: {
        schema: definitions['ErrorResponse'];
      };
      /** An error has occurred while trying to fulfill the request. Most likely the ErrorResponse will contain more information about the error. */
      500: {
        schema: definitions['ErrorResponse'];
      };
    };
  };
  /** Update tenant of a specific class */
  'tenants.update': {
    parameters: {
      path: {
        className: string;
      };
      body: {
        body: definitions['Tenant'][];
      };
    };
    responses: {
      /** Updated tenants of the specified class */
      200: {
        schema: definitions['Tenant'][];
      };
      /** Unauthorized or invalid credentials. */
      401: unknown;
      /** Forbidden */
      403: {
        schema: definitions['ErrorResponse'];
      };
      /** Invalid Tenant class */
      422: {
        schema: definitions['ErrorResponse'];
      };
      /** An error has occurred while trying to fulfill the request. Most likely the ErrorResponse will contain more information about the error. */
      500: {
        schema: definitions['ErrorResponse'];
      };
    };
  };
  /** Create a new tenant for a collection. Multi-tenancy must be enabled in the collection definition. */
  'tenants.create': {
    parameters: {
      path: {
        className: string;
      };
      body: {
        body: definitions['Tenant'][];
      };
    };
    responses: {
      /** Added new tenants to the specified class */
      200: {
        schema: definitions['Tenant'][];
      };
      /** Unauthorized or invalid credentials. */
      401: unknown;
      /** Forbidden */
      403: {
        schema: definitions['ErrorResponse'];
      };
      /** Invalid Tenant class */
      422: {
        schema: definitions['ErrorResponse'];
      };
      /** An error has occurred while trying to fulfill the request. Most likely the ErrorResponse will contain more information about the error. */
      500: {
        schema: definitions['ErrorResponse'];
      };
    };
  };
  /** delete tenants from a specific class */
  'tenants.delete': {
    parameters: {
      path: {
        className: string;
      };
      body: {
        tenants: string[];
      };
    };
    responses: {
      /** Deleted tenants from specified class. */
      200: unknown;
      /** Unauthorized or invalid credentials. */
      401: unknown;
      /** Forbidden */
      403: {
        schema: definitions['ErrorResponse'];
      };
      /** Invalid Tenant class */
      422: {
        schema: definitions['ErrorResponse'];
      };
      /** An error has occurred while trying to fulfill the request. Most likely the ErrorResponse will contain more information about the error. */
      500: {
        schema: definitions['ErrorResponse'];
      };
    };
  };
  /** get a specific tenant for the given class */
  'tenants.get.one': {
    parameters: {
      path: {
        className: string;
        tenantName: string;
      };
      header: {
        /** If consistency is true, the request will be proxied to the leader to ensure strong schema consistency */
        consistency?: boolean;
      };
    };
    responses: {
      /** load the tenant given the specified class */
      200: {
        schema: definitions['TenantResponse'];
      };
      /** Unauthorized or invalid credentials. */
      401: unknown;
      /** Forbidden */
      403: {
        schema: definitions['ErrorResponse'];
      };
      /** Tenant not found */
      404: unknown;
      /** Invalid tenant or class */
      422: {
        schema: definitions['ErrorResponse'];
      };
      /** An error has occurred while trying to fulfill the request. Most likely the ErrorResponse will contain more information about the error. */
      500: {
        schema: definitions['ErrorResponse'];
      };
    };
  };
  /** Check if a tenant exists for a specific class */
  'tenant.exists': {
    parameters: {
      path: {
        className: string;
        tenantName: string;
      };
      header: {
        /** If consistency is true, the request will be proxied to the leader to ensure strong schema consistency */
        consistency?: boolean;
      };
    };
    responses: {
      /** The tenant exists in the specified class */
      200: unknown;
      /** Unauthorized or invalid credentials. */
      401: unknown;
      /** Forbidden */
      403: {
        schema: definitions['ErrorResponse'];
      };
      /** The tenant not found */
      404: unknown;
      /** Invalid Tenant class */
      422: {
        schema: definitions['ErrorResponse'];
      };
      /** An error has occurred while trying to fulfill the request. Most likely the ErrorResponse will contain more information about the error. */
      500: {
        schema: definitions['ErrorResponse'];
      };
    };
  };
  /** [Coming soon] List all backups in progress not implemented yet. */
  'backups.list': {
    parameters: {
      path: {
        /** Backup backend name e.g. filesystem, gcs, s3. */
        backend: string;
      };
    };
    responses: {
      /** Existed backups */
      200: {
        schema: definitions['BackupListResponse'];
      };
      /** Unauthorized or invalid credentials. */
      401: unknown;
      /** Forbidden */
      403: {
        schema: definitions['ErrorResponse'];
      };
      /** Invalid backup list. */
      422: {
        schema: definitions['ErrorResponse'];
      };
      /** An error has occurred while trying to fulfill the request. Most likely the ErrorResponse will contain more information about the error. */
      500: {
        schema: definitions['ErrorResponse'];
      };
    };
  };
  /** Start creating a backup for a set of collections. <br/><br/>Notes: <br/>- Weaviate uses gzip compression by default. <br/>- Weaviate stays usable while a backup process is ongoing. */
  'backups.create': {
    parameters: {
      path: {
        /** Backup backend name e.g. `filesystem`, `gcs`, `s3`, `azure`. */
        backend: string;
      };
      body: {
        body: definitions['BackupCreateRequest'];
      };
    };
    responses: {
      /** Backup create process successfully started. */
      200: {
        schema: definitions['BackupCreateResponse'];
      };
      /** Unauthorized or invalid credentials. */
      401: unknown;
      /** Forbidden */
      403: {
        schema: definitions['ErrorResponse'];
      };
      /** Invalid backup creation attempt. */
      422: {
        schema: definitions['ErrorResponse'];
      };
      /** An error has occurred while trying to fulfill the request. Most likely the ErrorResponse will contain more information about the error. */
      500: {
        schema: definitions['ErrorResponse'];
      };
    };
  };
  /** Returns status of backup creation attempt for a set of collections. <br/><br/>All client implementations have a `wait for completion` option which will poll the backup status in the background and only return once the backup has completed (successfully or unsuccessfully). If you set the `wait for completion` option to false, you can also check the status yourself using this endpoint. */
  'backups.create.status': {
    parameters: {
      path: {
        /** Backup backend name e.g. filesystem, gcs, s3. */
        backend: string;
        /** The ID of a backup. Must be URL-safe and work as a filesystem path, only lowercase, numbers, underscore, minus characters allowed. */
        id: string;
      };
      query: {
        /** Name of the bucket, container, volume, etc */
        bucket?: string;
        /** The path within the bucket */
        path?: string;
      };
    };
    responses: {
      /** Backup creation status successfully returned */
      200: {
        schema: definitions['BackupCreateStatusResponse'];
      };
      /** Unauthorized or invalid credentials. */
      401: unknown;
      /** Forbidden */
      403: {
        schema: definitions['ErrorResponse'];
      };
      /** Not Found - Backup does not exist */
      404: {
        schema: definitions['ErrorResponse'];
      };
      /** Invalid backup restoration status attempt. */
      422: {
        schema: definitions['ErrorResponse'];
      };
      /** An error has occurred while trying to fulfill the request. Most likely the ErrorResponse will contain more information about the error. */
      500: {
        schema: definitions['ErrorResponse'];
      };
    };
  };
  /** Cancel created backup with specified ID */
  'backups.cancel': {
    parameters: {
      path: {
        /** Backup backend name e.g. filesystem, gcs, s3. */
        backend: string;
        /** The ID of a backup. Must be URL-safe and work as a filesystem path, only lowercase, numbers, underscore, minus characters allowed. */
        id: string;
      };
      query: {
        /** Name of the bucket, container, volume, etc */
        bucket?: string;
        /** The path within the bucket */
        path?: string;
      };
    };
    responses: {
      /** Successfully deleted. */
      204: never;
      /** Unauthorized or invalid credentials. */
      401: unknown;
      /** Forbidden */
      403: {
        schema: definitions['ErrorResponse'];
      };
      /** Invalid backup cancellation attempt. */
      422: {
        schema: definitions['ErrorResponse'];
      };
      /** An error has occurred while trying to fulfill the request. Most likely the ErrorResponse will contain more information about the error. */
      500: {
        schema: definitions['ErrorResponse'];
      };
    };
  };
  /** Returns status of a backup restoration attempt for a set of classes. <br/><br/>All client implementations have a `wait for completion` option which will poll the backup status in the background and only return once the backup has completed (successfully or unsuccessfully). If you set the `wait for completion` option to false, you can also check the status yourself using the this endpoint. */
  'backups.restore.status': {
    parameters: {
      path: {
        /** Backup backend name e.g. `filesystem`, `gcs`, `s3`, `azure`. */
        backend: string;
        /** The ID of a backup. Must be URL-safe and work as a filesystem path, only lowercase, numbers, underscore, minus characters allowed. */
        id: string;
      };
      query: {
        /** Name of the bucket, container, volume, etc */
        bucket?: string;
        /** The path within the bucket */
        path?: string;
      };
    };
    responses: {
      /** Backup restoration status successfully returned */
      200: {
        schema: definitions['BackupRestoreStatusResponse'];
      };
      /** Unauthorized or invalid credentials. */
      401: unknown;
      /** Forbidden */
      403: {
        schema: definitions['ErrorResponse'];
      };
      /** Not Found - Backup does not exist */
      404: {
        schema: definitions['ErrorResponse'];
      };
      /** An error has occurred while trying to fulfill the request. Most likely the ErrorResponse will contain more information about the error. */
      500: {
        schema: definitions['ErrorResponse'];
      };
    };
  };
  /** Starts a process of restoring a backup for a set of collections. <br/><br/>Any backup can be restored to any machine, as long as the number of nodes between source and target are identical.<br/><br/>Requrements:<br/><br/>- None of the collections to be restored already exist on the target restoration node(s).<br/>- The node names of the backed-up collections' must match those of the target restoration node(s). */
  'backups.restore': {
    parameters: {
      path: {
        /** Backup backend name e.g. `filesystem`, `gcs`, `s3`, `azure`. */
        backend: string;
        /** The ID of a backup. Must be URL-safe and work as a filesystem path, only lowercase, numbers, underscore, minus characters allowed. */
        id: string;
      };
      body: {
        body: definitions['BackupRestoreRequest'];
      };
    };
    responses: {
      /** Backup restoration process successfully started. */
      200: {
        schema: definitions['BackupRestoreResponse'];
      };
      /** Unauthorized or invalid credentials. */
      401: unknown;
      /** Forbidden */
      403: {
        schema: definitions['ErrorResponse'];
      };
      /** Not Found - Backup does not exist */
      404: {
        schema: definitions['ErrorResponse'];
      };
      /** Invalid backup restoration attempt. */
      422: {
        schema: definitions['ErrorResponse'];
      };
      /** An error has occurred while trying to fulfill the request. Most likely the ErrorResponse will contain more information about the error. */
      500: {
        schema: definitions['ErrorResponse'];
      };
    };
  };
  /** Returns Raft cluster statistics of Weaviate DB. */
  'cluster.get.statistics': {
    responses: {
      /** Cluster statistics successfully returned */
      200: {
        schema: definitions['ClusterStatisticsResponse'];
      };
      /** Unauthorized or invalid credentials. */
      401: unknown;
      /** Forbidden */
      403: {
        schema: definitions['ErrorResponse'];
      };
      /** Invalid backup restoration status attempt. */
      422: {
        schema: definitions['ErrorResponse'];
      };
      /** An error has occurred while trying to fulfill the request. Most likely the ErrorResponse will contain more information about the error. */
      500: {
        schema: definitions['ErrorResponse'];
      };
    };
  };
  /** Returns node information for the entire database. */
  'nodes.get': {
    parameters: {
      query: {
        /** Controls the verbosity of the output, possible values are: "minimal", "verbose". Defaults to "minimal". */
        output?: parameters['CommonOutputVerbosityParameterQuery'];
      };
    };
    responses: {
      /** Nodes status successfully returned */
      200: {
        schema: definitions['NodesStatusResponse'];
      };
      /** Unauthorized or invalid credentials. */
      401: unknown;
      /** Forbidden */
      403: {
        schema: definitions['ErrorResponse'];
      };
      /** Not Found - Backup does not exist */
      404: {
        schema: definitions['ErrorResponse'];
      };
      /** Invalid backup restoration status attempt. */
      422: {
        schema: definitions['ErrorResponse'];
      };
      /** An error has occurred while trying to fulfill the request. Most likely the ErrorResponse will contain more information about the error. */
      500: {
        schema: definitions['ErrorResponse'];
      };
    };
  };
  /** Returns node information for the nodes relevant to the collection. */
  'nodes.get.class': {
    parameters: {
      path: {
        className: string;
      };
      query: {
        /** Controls the verbosity of the output, possible values are: "minimal", "verbose". Defaults to "minimal". */
        output?: parameters['CommonOutputVerbosityParameterQuery'];
      };
    };
    responses: {
      /** Nodes status successfully returned */
      200: {
        schema: definitions['NodesStatusResponse'];
      };
      /** Unauthorized or invalid credentials. */
      401: unknown;
      /** Forbidden */
      403: {
        schema: definitions['ErrorResponse'];
      };
      /** Not Found - Backup does not exist */
      404: {
        schema: definitions['ErrorResponse'];
      };
      /** Invalid backup restoration status attempt. */
      422: {
        schema: definitions['ErrorResponse'];
      };
      /** An error has occurred while trying to fulfill the request. Most likely the ErrorResponse will contain more information about the error. */
      500: {
        schema: definitions['ErrorResponse'];
      };
    };
  };
  /** Trigger a classification based on the specified params. Classifications will run in the background, use GET /classifications/<id> to retrieve the status of your classification. */
  'classifications.post': {
    parameters: {
      body: {
        /** parameters to start a classification */
        params: definitions['Classification'];
      };
    };
    responses: {
      /** Successfully started classification. */
      201: {
        schema: definitions['Classification'];
      };
      /** Incorrect request */
      400: {
        schema: definitions['ErrorResponse'];
      };
      /** Unauthorized or invalid credentials. */
      401: unknown;
      /** Forbidden */
      403: {
        schema: definitions['ErrorResponse'];
      };
      /** An error has occurred while trying to fulfill the request. Most likely the ErrorResponse will contain more information about the error. */
      500: {
        schema: definitions['ErrorResponse'];
      };
    };
  };
  /** Get status, results and metadata of a previously created classification */
  'classifications.get': {
    parameters: {
      path: {
        /** classification id */
        id: string;
      };
    };
    responses: {
      /** Found the classification, returned as body */
      200: {
        schema: definitions['Classification'];
      };
      /** Unauthorized or invalid credentials. */
      401: unknown;
      /** Forbidden */
      403: {
        schema: definitions['ErrorResponse'];
      };
      /** Not Found - Classification does not exist */
      404: unknown;
      /** An error has occurred while trying to fulfill the request. Most likely the ErrorResponse will contain more information about the error. */
      500: {
        schema: definitions['ErrorResponse'];
      };
    };
  };
}
export interface external {}
