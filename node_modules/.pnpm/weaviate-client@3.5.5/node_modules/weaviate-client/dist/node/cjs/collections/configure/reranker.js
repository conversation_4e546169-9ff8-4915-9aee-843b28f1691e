'use strict';
Object.defineProperty(exports, '__esModule', { value: true });
exports.default = {
  /**
   * Create a `ModuleConfig<'reranker-cohere', RerankerCohereConfig>` object for use when reranking using the `reranker-cohere` module.
   *
   * See the [documentation](https://weaviate.io/developers/weaviate/model-providers/cohere/reranker) for detailed usage.
   *
   * @param {RerankerCohereConfig} [config] The configuration for the `reranker-cohere` module.
   * @returns {ModuleConfig<'reranker-cohere', RerankerCohereConfig>} The configuration object.
   */
  cohere: (config) => {
    return {
      name: 'reranker-cohere',
      config: config,
    };
  },
  /**
   * Create a `ModuleConfig<'reranker-jinaai', RerankerJinaAIConfig>` object for use when reranking using the `reranker-jinaai` module.
   *
   * See the [documentation](https://weaviate.io/developers/weaviate/model-providers/jinaai/reranker) for detailed usage.
   *
   * @param {RerankerJinaAIConfig} [config] The configuration for the `reranker-jinaai` module.
   * @returns {ModuleConfig<'reranker-jinaai', RerankerJinaAIConfig | undefined>} The configuration object.
   */
  jinaai: (config) => {
    return {
      name: 'reranker-jinaai',
      config: config,
    };
  },
  /**
   * Create a `ModuleConfig<'reranker-nvidia', RerankerNvidiaConfig>` object for use when reranking using the `reranker-nvidia` module.
   *
   * See the [documentation](https://weaviate.io/developers/weaviate/model-providers/nvidia/reranker) for detailed usage.
   *
   * @param {RerankerNvidiaConfig} [config] The configuration for the `reranker-nvidia` module.
   * @returns {ModuleConfig<'reranker-nvidia', RerankerNvidiaConfig | undefined>} The configuration object.
   */
  nvidia: (config) => {
    return {
      name: 'reranker-nvidia',
      config: config,
    };
  },
  /**
   * Create a `ModuleConfig<'reranker-transformers', Record<string, never>>` object for use when reranking using the `reranker-transformers` module.
   *
   * See the [documentation](https://weaviate.io/developers/weaviate/model-providers/transformers/reranker) for detailed usage.
   *
   * @returns {ModuleConfig<'reranker-transformers', Record<string, never>>} The configuration object.
   */
  transformers: () => {
    return {
      name: 'reranker-transformers',
      config: {},
    };
  },
  /**
   * Create a `ModuleConfig<'reranker-voyageai', RerankerVoyageAIConfig>` object for use when reranking using the `reranker-voyageai` module.
   *
   * See the [documentation](https://weaviate.io/developers/weaviate/model-providers/voyageai/reranker) for detailed usage.
   *
   * @param {RerankerVoyageAIConfig} [config] The configuration for the `reranker-voyage-ai` module.
   * @returns {ModuleConfig<'reranker-voyage-ai', RerankerVoyageAIConfig | undefined>} The configuration object.
   */
  voyageAI: (config) => {
    return {
      name: 'reranker-voyageai',
      config: config,
    };
  },
};
