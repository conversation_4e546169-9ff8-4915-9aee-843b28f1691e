import {
  ModuleConfig,
  RerankerCohereConfig,
  RerankerJinaAIConfig,
  RerankerNvidiaConfig,
  RerankerVoyageAIConfig,
} from '../config/types/index.js';
declare const _default: {
  /**
   * Create a `ModuleConfig<'reranker-cohere', RerankerCohereConfig>` object for use when reranking using the `reranker-cohere` module.
   *
   * See the [documentation](https://weaviate.io/developers/weaviate/model-providers/cohere/reranker) for detailed usage.
   *
   * @param {RerankerCohereConfig} [config] The configuration for the `reranker-cohere` module.
   * @returns {ModuleConfig<'reranker-cohere', RerankerCohereConfig>} The configuration object.
   */
  cohere: (
    config?: RerankerCohereConfig
  ) => ModuleConfig<'reranker-cohere', RerankerCohereConfig | undefined>;
  /**
   * Create a `ModuleConfig<'reranker-jinaai', RerankerJinaAIConfig>` object for use when reranking using the `reranker-jinaai` module.
   *
   * See the [documentation](https://weaviate.io/developers/weaviate/model-providers/jinaai/reranker) for detailed usage.
   *
   * @param {RerankerJinaAIConfig} [config] The configuration for the `reranker-jinaai` module.
   * @returns {ModuleConfig<'reranker-jinaai', RerankerJinaAIConfig | undefined>} The configuration object.
   */
  jinaai: (
    config?: RerankerJinaAIConfig
  ) => ModuleConfig<'reranker-jinaai', RerankerJinaAIConfig | undefined>;
  /**
   * Create a `ModuleConfig<'reranker-nvidia', RerankerNvidiaConfig>` object for use when reranking using the `reranker-nvidia` module.
   *
   * See the [documentation](https://weaviate.io/developers/weaviate/model-providers/nvidia/reranker) for detailed usage.
   *
   * @param {RerankerNvidiaConfig} [config] The configuration for the `reranker-nvidia` module.
   * @returns {ModuleConfig<'reranker-nvidia', RerankerNvidiaConfig | undefined>} The configuration object.
   */
  nvidia: (
    config?: RerankerNvidiaConfig
  ) => ModuleConfig<'reranker-nvidia', RerankerNvidiaConfig | undefined>;
  /**
   * Create a `ModuleConfig<'reranker-transformers', Record<string, never>>` object for use when reranking using the `reranker-transformers` module.
   *
   * See the [documentation](https://weaviate.io/developers/weaviate/model-providers/transformers/reranker) for detailed usage.
   *
   * @returns {ModuleConfig<'reranker-transformers', Record<string, never>>} The configuration object.
   */
  transformers: () => ModuleConfig<'reranker-transformers', Record<string, never>>;
  /**
   * Create a `ModuleConfig<'reranker-voyageai', RerankerVoyageAIConfig>` object for use when reranking using the `reranker-voyageai` module.
   *
   * See the [documentation](https://weaviate.io/developers/weaviate/model-providers/voyageai/reranker) for detailed usage.
   *
   * @param {RerankerVoyageAIConfig} [config] The configuration for the `reranker-voyage-ai` module.
   * @returns {ModuleConfig<'reranker-voyage-ai', RerankerVoyageAIConfig | undefined>} The configuration object.
   */
  voyageAI: (
    config?: RerankerVoyageAIConfig
  ) => ModuleConfig<'reranker-voyageai', RerankerVoyageAIConfig | undefined>;
};
export default _default;
