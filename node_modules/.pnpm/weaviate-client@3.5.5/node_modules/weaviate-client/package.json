{"name": "weaviate-client", "version": "3.5.5", "description": "JS/TS client for Weaviate", "main": "dist/node/cjs/index.js", "type": "module", "exports": {".": {"types": {"require": "./dist/node/cjs/index.d.ts", "default": "./dist/node/esm/index.d.ts"}, "default": {"require": "./dist/node/cjs/index.js", "default": "./dist/node/esm/index.js"}}}, "engines": {"node": ">=18.0.0"}, "scripts": {"test": "jest --no-cache --useStderr --runInBand --detectOpenHandles", "test:coverage": "npm run test -- --coverage", "build": "npm run build:node", "build:web": "tsup", "build:cjs": "tsc --module commonjs --moduleResolution node10 --outDir dist/node/cjs && touch dist/node/cjs/package.json && echo '{\"type\": \"commonjs\"}' > dist/node/cjs/package.json", "build:esm": "tsc --outDir dist/node/esm && touch dist/node/esm/package.json && echo '{\"type\": \"module\"}' > dist/node/esm/package.json", "build:node": "npm run lint && npm run build:cjs && npm run build:esm && prettier --write --no-error-on-unmatched-pattern '**/dist/**/*.{ts,js}'", "prepack": "npm run build", "lint": "eslint --ext .ts,.js .", "lint:fix": "npm run lint -- --fix", "format": "prettier --write --no-error-on-unmatched-pattern '**/*.{ts,js}' '!dist/**'", "format:check": "prettier --check --no-error-on-unmatched-pattern '**/*.{ts,js}' '!dist/**'", "format:dist": "prettier --write --no-error-on-unmatched-pattern '**/dist/**/*.{ts,js}'", "refresh-schema": "./tools/refresh_schema.sh", "refresh-protos": "./tools/refresh_protos.sh", "docs": "typedoc --plugin typedoc-plugin-extras --favicon public/favicon.ico --out docs/ src/"}, "repository": {"type": "git", "url": "git+https://github.com/weaviate/typescript-client.git"}, "keywords": ["weaviate"], "author": "Weaviate", "license": "SEE LICENSE IN LICENSE", "bugs": {"url": "https://github.com/weaviate/typescript-client/issues"}, "homepage": "https://github.com/weaviate/typescript-client#readme", "dependencies": {"abort-controller-x": "^0.4.3", "graphql": "^16.10.0", "graphql-request": "^6.1.0", "long": "^5.2.4", "nice-grpc": "^2.1.11", "nice-grpc-client-middleware-retry": "^3.1.10", "nice-grpc-common": "^2.0.2", "uuid": "^9.0.1"}, "devDependencies": {"@babel/core": "^7.20.12", "@babel/preset-typescript": "^7.18.6", "@babel/runtime": "^7.20.7", "@curveball/bodyparser": "0.5.0", "@curveball/core": "0.20.0", "@curveball/kernel": "0.20.1", "@rollup/plugin-babel": "^5.3.1", "@testcontainers/weaviate": "^10.14.0", "@types/express": "^4.17.21", "@types/isomorphic-fetch": "^0.0.36", "@types/jest": "^29.4.0", "@types/node": "^18.14.0", "@types/uuid": "^9.0.1", "@typescript-eslint/eslint-plugin": "^6.21.0", "@typescript-eslint/parser": "^6.21.0", "babel-jest": "^29.4.3", "eslint": "^8.35.0", "eslint-config-prettier": "^8.7.0", "eslint-plugin-prettier": "^4.2.1", "express": "^4.19.2", "grpc-tools": "^1.12.4", "husky": "^8.0.3", "jest": "^29.4.3", "lint-staged": "^13.2.0", "openapi-typescript": "^5.4.1", "prettier": "^2.8.4", "prettier-plugin-organize-imports": "^3.2.4", "protobufjs": "^7.2.6", "ts-jest": "^29.0.5", "ts-node": "^10.9.2", "ts-proto": "^1.163.0", "tsup": "^8.0.2", "typedoc": "^0.25.12", "typedoc-plugin-extras": "^3.0.0", "typescript": "^5.3.3"}, "lint-staged": {"*.{ts,js}": ["npm run format:check", "npm run lint -- --cache"]}}