{"version": 3, "sources": ["../src/index.ts", "../src/apply/default.ts", "../src/utils.ts", "../src/verify/verify.ts", "../src/transform/http.ts", "../src/run/http-request.ts", "../src/transform/sse.ts", "../src/transform/proto.ts", "../src/legacy/convert.ts", "../src/legacy/types.ts", "../src/agent/agent.ts", "../src/chunks/transform.ts", "../src/agent/http.ts"], "sourcesContent": ["export * from \"./apply\";\nexport * from \"./verify\";\nexport * from \"./transform\";\nexport * from \"./run\";\nexport * from \"./legacy\";\nexport * from \"./agent\";\nexport * from \"@ag-ui/core\";\n", "import {\n  ApplyEvents,\n  EventType,\n  TextMessageStartEvent,\n  TextMessageContentEvent,\n  Message,\n  ToolCallStartEvent,\n  ToolCallArgsEvent,\n  StateSnapshotEvent,\n  StateDeltaEvent,\n  MessagesSnapshotEvent,\n  CustomEvent,\n  BaseEvent,\n  AssistantMessage,\n} from \"@ag-ui/core\";\nimport { mergeMap } from \"rxjs/operators\";\nimport { structuredClone_ } from \"../utils\";\nimport { applyPatch } from \"fast-json-patch\";\nimport untruncateJson from \"untruncate-json\";\nimport { AgentState } from \"@ag-ui/core\";\nimport { Observable } from \"rxjs\";\n\ninterface PredictStateValue {\n  state_key: string;\n  tool: string;\n  tool_argument: string;\n}\n\nexport const defaultApplyEvents = (...args: Parameters<ApplyEvents>): ReturnType<ApplyEvents> => {\n  const [input, events$] = args;\n\n  let messages = structuredClone_(input.messages);\n  let state = structuredClone_(input.state);\n  let predictState: PredictStateValue[] | undefined;\n\n  // Helper function to emit state updates with proper cloning\n  const emitUpdate = (agentState: AgentState) => [structuredClone_(agentState)];\n\n  const emitNoUpdate = () => [];\n\n  return events$.pipe(\n    mergeMap((event) => {\n      switch (event.type) {\n        case EventType.TEXT_MESSAGE_START: {\n          const { messageId, role } = event as TextMessageStartEvent;\n\n          // Create a new message using properties from the event\n          const newMessage: Message = {\n            id: messageId,\n            role: role,\n            content: \"\",\n          };\n\n          // Add the new message to the messages array\n          messages.push(newMessage);\n\n          return emitUpdate({ messages });\n        }\n\n        case EventType.TEXT_MESSAGE_CONTENT: {\n          const { delta } = event as TextMessageContentEvent;\n\n          // Get the last message and append the content\n          const lastMessage = messages[messages.length - 1];\n          lastMessage.content = lastMessage.content! + delta;\n\n          return emitUpdate({ messages });\n        }\n\n        case EventType.TEXT_MESSAGE_END: {\n          return emitNoUpdate();\n        }\n\n        case EventType.TOOL_CALL_START: {\n          const { toolCallId, toolCallName, parentMessageId } = event as ToolCallStartEvent;\n\n          let targetMessage: AssistantMessage;\n\n          // Use last message if parentMessageId exists, we have messages, and the parentMessageId matches the last message's id\n          if (\n            parentMessageId &&\n            messages.length > 0 &&\n            messages[messages.length - 1].id === parentMessageId\n          ) {\n            targetMessage = messages[messages.length - 1];\n          } else {\n            // Create a new message otherwise\n            targetMessage = {\n              id: parentMessageId || toolCallId,\n              role: \"assistant\",\n              toolCalls: [],\n            };\n            messages.push(targetMessage);\n          }\n\n          targetMessage.toolCalls ??= [];\n\n          // Add the new tool call\n          targetMessage.toolCalls.push({\n            id: toolCallId,\n            type: \"function\",\n            function: {\n              name: toolCallName,\n              arguments: \"\",\n            },\n          });\n\n          return emitUpdate({ messages });\n        }\n\n        case EventType.TOOL_CALL_ARGS: {\n          const { delta } = event as ToolCallArgsEvent;\n\n          // Get the last message\n          const lastMessage = messages[messages.length - 1];\n\n          // Get the last tool call\n          const lastToolCall = lastMessage.toolCalls[lastMessage.toolCalls.length - 1];\n\n          // Append the arguments\n          lastToolCall.function.arguments += delta;\n\n          if (predictState) {\n            const config = predictState.find((p) => p.tool === lastToolCall.function.name);\n            if (config) {\n              try {\n                const lastToolCallArguments = JSON.parse(\n                  untruncateJson(lastToolCall.function.arguments),\n                );\n                if (config.tool_argument && config.tool_argument in lastToolCallArguments) {\n                  state = {\n                    ...state,\n                    [config.state_key]: lastToolCallArguments[config.tool_argument],\n                  };\n                  return emitUpdate({ messages, state });\n                } else {\n                  state = {\n                    ...state,\n                    [config.state_key]: lastToolCallArguments,\n                  };\n                  return emitUpdate({ messages, state });\n                }\n              } catch (_) {}\n            }\n          }\n\n          return emitUpdate({ messages });\n        }\n\n        case EventType.TOOL_CALL_END: {\n          return emitNoUpdate();\n        }\n\n        case EventType.STATE_SNAPSHOT: {\n          const { snapshot } = event as StateSnapshotEvent;\n\n          // Replace state with the literal snapshot\n          state = snapshot;\n\n          return emitUpdate({ state });\n        }\n\n        case EventType.STATE_DELTA: {\n          const { delta } = event as StateDeltaEvent;\n\n          try {\n            // Apply the JSON Patch operations to the current state without mutating the original\n            const result = applyPatch(state, delta, true, false);\n            state = result.newDocument;\n            return emitUpdate({ state });\n          } catch (error: unknown) {\n            const errorMessage = error instanceof Error ? error.message : String(error);\n            console.warn(\n              `Failed to apply state patch:\\n` +\n                `Current state: ${JSON.stringify(state, null, 2)}\\n` +\n                `Patch operations: ${JSON.stringify(delta, null, 2)}\\n` +\n                `Error: ${errorMessage}`,\n            );\n            return emitNoUpdate();\n          }\n        }\n\n        case EventType.MESSAGES_SNAPSHOT: {\n          const { messages: newMessages } = event as MessagesSnapshotEvent;\n\n          // Replace messages with the snapshot\n          messages = newMessages;\n\n          return emitUpdate({ messages });\n        }\n\n        case EventType.RAW: {\n          return emitNoUpdate();\n        }\n\n        case EventType.CUSTOM: {\n          const customEvent = event as CustomEvent;\n\n          if (customEvent.name === \"PredictState\") {\n            predictState = customEvent.value as PredictStateValue[];\n            return emitNoUpdate();\n          }\n\n          return emitNoUpdate();\n        }\n\n        case EventType.RUN_STARTED: {\n          return emitNoUpdate();\n        }\n\n        case EventType.RUN_FINISHED: {\n          return emitNoUpdate();\n        }\n\n        case EventType.RUN_ERROR: {\n          return emitNoUpdate();\n        }\n\n        case EventType.STEP_STARTED: {\n          return emitNoUpdate();\n        }\n\n        case EventType.STEP_FINISHED: {\n          // reset predictive state after step is finished\n          predictState = undefined;\n          return emitNoUpdate();\n        }\n\n        case EventType.TEXT_MESSAGE_CHUNK: {\n          throw new Error(\"TEXT_MESSAGE_CHUNK must be tranformed before being applied\");\n        }\n\n        case EventType.TOOL_CALL_CHUNK: {\n          throw new Error(\"TOOL_CALL_CHUNK must be tranformed before being applied\");\n        }\n      }\n\n      // This makes TypeScript check that the switch is exhaustive\n      // If a new EventType is added, this will cause a compile error\n      const _exhaustiveCheck: never = event.type;\n      return emitNoUpdate();\n    }),\n  );\n};\n", "export const structuredClone_ = (obj: any) => {\n  if (typeof structuredClone === \"function\") {\n    return structuredClone(obj);\n  }\n\n  try {\n    return JSON.parse(JSON.stringify(obj));\n  } catch (err) {\n    return { ...obj };\n  }\n};\n", "import { BaseEvent, EventType, AGUIError } from \"@ag-ui/core\";\nimport { Observable, throwError, of } from \"rxjs\";\nimport { mergeMap } from \"rxjs/operators\";\n\nexport const verifyEvents =\n  (debug: boolean) =>\n  (source$: Observable<BaseEvent>): Observable<BaseEvent> => {\n    // Declare variables in closure to maintain state across events\n    let activeMessageId: string | undefined;\n    let activeToolCallId: string | undefined;\n    let runFinished = false;\n    let runError = false; // New flag to track if RUN_ERROR has been sent\n    // New flags to track first/last event requirements\n    let firstEventReceived = false;\n    // Track active steps\n    let activeSteps = new Map<string, boolean>(); // Map of step name -> active status\n\n    return source$.pipe(\n      // Process each event through our state machine\n      mergeMap((event) => {\n        const eventType = event.type;\n\n        if (debug) {\n          console.debug(\"[VERIFY]:\", JSON.stringify(event));\n        }\n\n        // Check if run has errored\n        if (runError) {\n          return throwError(\n            () =>\n              new AGUIError(\n                `Cannot send event type '${eventType}': The run has already errored with 'RUN_ERROR'. No further events can be sent.`,\n              ),\n          );\n        }\n\n        // Check if run has already finished\n        if (runFinished && eventType !== EventType.RUN_ERROR) {\n          return throwError(\n            () =>\n              new AGUIError(\n                `Cannot send event type '${eventType}': The run has already finished with 'RUN_FINISHED'. Start a new run with 'RUN_STARTED'.`,\n              ),\n          );\n        }\n\n        // Forbid lifecycle events and tool events inside a text message\n        if (activeMessageId !== undefined) {\n          // Define allowed event types inside a text message\n          const allowedEventTypes = [\n            EventType.TEXT_MESSAGE_CONTENT,\n            EventType.TEXT_MESSAGE_END,\n            EventType.RAW,\n          ];\n\n          // If the event type is not in the allowed list, throw an error\n          if (!allowedEventTypes.includes(eventType)) {\n            return throwError(\n              () =>\n                new AGUIError(\n                  `Cannot send event type '${eventType}' after 'TEXT_MESSAGE_START': Send 'TEXT_MESSAGE_END' first.`,\n                ),\n            );\n          }\n        }\n\n        // Forbid lifecycle events and text message events inside a tool call\n        if (activeToolCallId !== undefined) {\n          // Define allowed event types inside a tool call\n          const allowedEventTypes = [\n            EventType.TOOL_CALL_ARGS,\n            EventType.TOOL_CALL_END,\n            EventType.RAW,\n          ];\n\n          // If the event type is not in the allowed list, throw an error\n          if (!allowedEventTypes.includes(eventType)) {\n            // Special handling for nested tool calls for better error message\n            if (eventType === EventType.TOOL_CALL_START) {\n              return throwError(\n                () =>\n                  new AGUIError(\n                    `Cannot send 'TOOL_CALL_START' event: A tool call is already in progress. Complete it with 'TOOL_CALL_END' first.`,\n                  ),\n              );\n            }\n\n            return throwError(\n              () =>\n                new AGUIError(\n                  `Cannot send event type '${eventType}' after 'TOOL_CALL_START': Send 'TOOL_CALL_END' first.`,\n                ),\n            );\n          }\n        }\n\n        // Handle first event requirement and prevent multiple RUN_STARTED\n        if (!firstEventReceived) {\n          firstEventReceived = true;\n          if (eventType !== EventType.RUN_STARTED && eventType !== EventType.RUN_ERROR) {\n            return throwError(() => new AGUIError(`First event must be 'RUN_STARTED'`));\n          }\n        } else if (eventType === EventType.RUN_STARTED) {\n          // Prevent multiple RUN_STARTED events\n          return throwError(\n            () =>\n              new AGUIError(\n                `Cannot send multiple 'RUN_STARTED' events: A 'RUN_STARTED' event was already sent. Each run must have exactly one 'RUN_STARTED' event at the beginning.`,\n              ),\n          );\n        }\n\n        // Validate event based on type and current state\n        switch (eventType) {\n          // Text message flow\n          case EventType.TEXT_MESSAGE_START: {\n            // Can't start a message if one is already in progress\n            if (activeMessageId !== undefined) {\n              return throwError(\n                () =>\n                  new AGUIError(\n                    `Cannot send 'TEXT_MESSAGE_START' event: A text message is already in progress. Complete it with 'TEXT_MESSAGE_END' first.`,\n                  ),\n              );\n            }\n\n            activeMessageId = (event as any).messageId;\n            return of(event);\n          }\n\n          case EventType.TEXT_MESSAGE_CONTENT: {\n            // Must be in a message and IDs must match\n            if (activeMessageId === undefined) {\n              return throwError(\n                () =>\n                  new AGUIError(\n                    `Cannot send 'TEXT_MESSAGE_CONTENT' event: No active text message found. Start a text message with 'TEXT_MESSAGE_START' first.`,\n                  ),\n              );\n            }\n\n            if ((event as any).messageId !== activeMessageId) {\n              return throwError(\n                () =>\n                  new AGUIError(\n                    `Cannot send 'TEXT_MESSAGE_CONTENT' event: Message ID mismatch. The ID '${(event as any).messageId}' doesn't match the active message ID '${activeMessageId}'.`,\n                  ),\n              );\n            }\n\n            return of(event);\n          }\n\n          case EventType.TEXT_MESSAGE_END: {\n            // Must be in a message and IDs must match\n            if (activeMessageId === undefined) {\n              return throwError(\n                () =>\n                  new AGUIError(\n                    `Cannot send 'TEXT_MESSAGE_END' event: No active text message found. A 'TEXT_MESSAGE_START' event must be sent first.`,\n                  ),\n              );\n            }\n\n            if ((event as any).messageId !== activeMessageId) {\n              return throwError(\n                () =>\n                  new AGUIError(\n                    `Cannot send 'TEXT_MESSAGE_END' event: Message ID mismatch. The ID '${(event as any).messageId}' doesn't match the active message ID '${activeMessageId}'.`,\n                  ),\n              );\n            }\n\n            // Reset message state\n            activeMessageId = undefined;\n            return of(event);\n          }\n\n          // Tool call flow\n          case EventType.TOOL_CALL_START: {\n            // Can't start a tool call if one is already in progress\n            if (activeToolCallId !== undefined) {\n              return throwError(\n                () =>\n                  new AGUIError(\n                    `Cannot send 'TOOL_CALL_START' event: A tool call is already in progress. Complete it with 'TOOL_CALL_END' first.`,\n                  ),\n              );\n            }\n\n            activeToolCallId = (event as any).toolCallId;\n            return of(event);\n          }\n\n          case EventType.TOOL_CALL_ARGS: {\n            // Must be in a tool call and IDs must match\n            if (activeToolCallId === undefined) {\n              return throwError(\n                () =>\n                  new AGUIError(\n                    `Cannot send 'TOOL_CALL_ARGS' event: No active tool call found. Start a tool call with 'TOOL_CALL_START' first.`,\n                  ),\n              );\n            }\n\n            if ((event as any).toolCallId !== activeToolCallId) {\n              return throwError(\n                () =>\n                  new AGUIError(\n                    `Cannot send 'TOOL_CALL_ARGS' event: Tool call ID mismatch. The ID '${(event as any).toolCallId}' doesn't match the active tool call ID '${activeToolCallId}'.`,\n                  ),\n              );\n            }\n\n            return of(event);\n          }\n\n          case EventType.TOOL_CALL_END: {\n            // Must be in a tool call and IDs must match\n            if (activeToolCallId === undefined) {\n              return throwError(\n                () =>\n                  new AGUIError(\n                    `Cannot send 'TOOL_CALL_END' event: No active tool call found. A 'TOOL_CALL_START' event must be sent first.`,\n                  ),\n              );\n            }\n\n            if ((event as any).toolCallId !== activeToolCallId) {\n              return throwError(\n                () =>\n                  new AGUIError(\n                    `Cannot send 'TOOL_CALL_END' event: Tool call ID mismatch. The ID '${(event as any).toolCallId}' doesn't match the active tool call ID '${activeToolCallId}'.`,\n                  ),\n              );\n            }\n\n            // Reset tool call state\n            activeToolCallId = undefined;\n            return of(event);\n          }\n\n          // Step flow\n          case EventType.STEP_STARTED: {\n            const stepName = (event as any).name;\n            if (activeSteps.has(stepName)) {\n              return throwError(\n                () => new AGUIError(`Step \"${stepName}\" is already active for 'STEP_STARTED'`),\n              );\n            }\n            activeSteps.set(stepName, true);\n            return of(event);\n          }\n\n          case EventType.STEP_FINISHED: {\n            const stepName = (event as any).name;\n            if (!activeSteps.has(stepName)) {\n              return throwError(\n                () =>\n                  new AGUIError(\n                    `Cannot send 'STEP_FINISHED' for step \"${stepName}\" that was not started`,\n                  ),\n              );\n            }\n            activeSteps.delete(stepName);\n            return of(event);\n          }\n\n          // Run flow\n          case EventType.RUN_STARTED: {\n            // We've already validated this above\n            return of(event);\n          }\n\n          case EventType.RUN_FINISHED: {\n            // Can't be the first event (already checked)\n            // and can't happen after already being finished (already checked)\n\n            // Check that all steps are finished before run ends\n            if (activeSteps.size > 0) {\n              const unfinishedSteps = Array.from(activeSteps.keys()).join(\", \");\n              return throwError(\n                () =>\n                  new AGUIError(\n                    `Cannot send 'RUN_FINISHED' while steps are still active: ${unfinishedSteps}`,\n                  ),\n              );\n            }\n\n            runFinished = true;\n            return of(event);\n          }\n\n          case EventType.RUN_ERROR: {\n            // RUN_ERROR can happen at any time\n            runError = true; // Set flag to prevent any further events\n            return of(event);\n          }\n\n          case EventType.CUSTOM: {\n            return of(event);\n          }\n\n          default: {\n            return of(event);\n          }\n        }\n      }),\n    );\n  };\n", "import { BaseEvent, EventSchemas } from \"@ag-ui/core\";\nimport { Subject, ReplaySubject, Observable } from \"rxjs\";\nimport { HttpEvent, HttpEventType } from \"../run/http-request\";\nimport { parseSSEStream } from \"./sse\";\nimport { parseProtoStream } from \"./proto\";\nimport * as proto from \"@ag-ui/proto\";\n\n/**\n * Transforms HTTP events into BaseEvents using the appropriate format parser based on content type.\n */\nexport const transformHttpEventStream = (source$: Observable<HttpEvent>): Observable<BaseEvent> => {\n  const eventSubject = new Subject<BaseEvent>();\n\n  // Use ReplaySubject to buffer events until we decide on the parser\n  const bufferSubject = new ReplaySubject<HttpEvent>();\n\n  // Flag to track whether we've set up the parser\n  let parserInitialized = false;\n\n  // Subscribe to source and buffer events while we determine the content type\n  source$.subscribe({\n    next: (event: HttpEvent) => {\n      // Forward event to buffer\n      bufferSubject.next(event);\n\n      // If we get headers and haven't initialized a parser yet, check content type\n      if (event.type === HttpEventType.HEADERS && !parserInitialized) {\n        parserInitialized = true;\n        const contentType = event.headers.get(\"content-type\");\n\n        // Choose parser based on content type\n        if (contentType === proto.AGUI_MEDIA_TYPE) {\n          // Use protocol buffer parser\n          parseProtoStream(bufferSubject).subscribe({\n            next: (event) => eventSubject.next(event),\n            error: (err) => eventSubject.error(err),\n            complete: () => eventSubject.complete(),\n          });\n        } else {\n          // Use SSE JSON parser for all other cases\n          parseSSEStream(bufferSubject).subscribe({\n            next: (json) => {\n              try {\n                const parsedEvent = EventSchemas.parse(json);\n                eventSubject.next(parsedEvent as BaseEvent);\n              } catch (err) {\n                eventSubject.error(err);\n              }\n            },\n            error: (err) => eventSubject.error(err),\n            complete: () => eventSubject.complete(),\n          });\n        }\n      } else if (!parserInitialized) {\n        eventSubject.error(new Error(\"No headers event received before data events\"));\n      }\n    },\n    error: (err) => {\n      bufferSubject.error(err);\n      eventSubject.error(err);\n    },\n    complete: () => {\n      bufferSubject.complete();\n    },\n  });\n\n  return eventSubject.asObservable();\n};\n", "import { Observable, from, defer, throwError } from \"rxjs\";\nimport { switchMap } from \"rxjs/operators\";\n\nexport enum HttpEventType {\n  HEADERS = \"headers\",\n  DATA = \"data\",\n}\n\nexport interface HttpDataEvent {\n  type: HttpEventType.DATA;\n  data?: Uint8Array;\n}\n\nexport interface HttpHeadersEvent {\n  type: HttpEventType.HEADERS;\n  status: number;\n  headers: Headers;\n}\n\nexport type HttpEvent = HttpDataEvent | HttpHeadersEvent;\n\nexport const runHttpRequest = (url: string, requestInit: RequestInit): Observable<HttpEvent> => {\n  // Defer the fetch so that it's executed when subscribed to\n  return defer(() => from(fetch(url, requestInit))).pipe(\n    switchMap((response) => {\n      // Emit headers event first\n      const headersEvent: HttpHeadersEvent = {\n        type: HttpEventType.HEADERS,\n        status: response.status,\n        headers: response.headers,\n      };\n\n      const reader = response.body?.getReader();\n      if (!reader) {\n        return throwError(() => new Error(\"Failed to getReader() from response\"));\n      }\n\n      return new Observable<HttpEvent>((subscriber) => {\n        // Emit headers event first\n        subscriber.next(headersEvent);\n\n        (async () => {\n          try {\n            while (true) {\n              const { done, value } = await reader.read();\n              if (done) break;\n              // Emit data event instead of raw Uint8Array\n              const dataEvent: HttpDataEvent = {\n                type: HttpEventType.DATA,\n                data: value,\n              };\n              subscriber.next(dataEvent);\n            }\n            subscriber.complete();\n          } catch (error) {\n            subscriber.error(error);\n          }\n        })();\n\n        return () => {\n          reader.cancel();\n        };\n      });\n    }),\n  );\n};\n", "import { Observable, Subject } from \"rxjs\";\nimport { HttpEvent, HttpEventType } from \"../run/http-request\";\n\n/**\n * Parses a stream of HTTP events into a stream of JSON objects using Server-Sent Events (SSE) format.\n * Strictly follows the SSE standard where:\n * - Events are separated by double newlines ('\\n\\n')\n * - Only 'data:' prefixed lines are processed\n * - Multi-line data events are supported and joined\n * - Non-data fields (event, id, retry) are ignored\n */\nexport const parseSSEStream = (source$: Observable<HttpEvent>): Observable<any> => {\n  const jsonSubject = new Subject<any>();\n  // Create TextDecoder with stream option set to true to handle split UTF-8 characters\n  const decoder = new TextDecoder(\"utf-8\", { fatal: false });\n  let buffer = \"\";\n\n  // Subscribe to the source once and multicast to all subscribers\n  source$.subscribe({\n    next: (event: HttpEvent) => {\n      if (event.type === HttpEventType.HEADERS) {\n        return;\n      }\n\n      if (event.type === HttpEventType.DATA && event.data) {\n        // Decode chunk carefully to handle UTF-8\n        const text = decoder.decode(event.data, { stream: true });\n        buffer += text;\n\n        // Process complete events (separated by double newlines)\n        const events = buffer.split(/\\n\\n/);\n        // Keep the last potentially incomplete event in buffer\n        buffer = events.pop() || \"\";\n\n        for (const event of events) {\n          processSSEEvent(event);\n        }\n      }\n    },\n    error: (err) => jsonSubject.error(err),\n    complete: () => {\n      // Use the final call to decoder.decode() to flush any remaining bytes\n      if (buffer) {\n        buffer += decoder.decode();\n        // Process any remaining SSE event data\n        processSSEEvent(buffer);\n      }\n      jsonSubject.complete();\n    },\n  });\n\n  /**\n   * Helper function to process an SSE event.\n   * Extracts and joins data lines, then parses the result as JSON.\n   * Follows the SSE spec by only processing 'data:' prefixed lines.\n   * @param eventText The raw event text to process\n   */\n  function processSSEEvent(eventText: string) {\n    const lines = eventText.split(\"\\n\");\n    const dataLines: string[] = [];\n\n    for (const line of lines) {\n      if (line.startsWith(\"data: \")) {\n        // Extract data content (remove 'data: ' prefix)\n        dataLines.push(line.slice(6));\n      }\n    }\n\n    // Only process if we have data lines\n    if (dataLines.length > 0) {\n      try {\n        // Join multi-line data and parse JSON\n        const jsonStr = dataLines.join(\"\\n\");\n        const json = JSON.parse(jsonStr);\n        jsonSubject.next(json);\n      } catch (err) {\n        jsonSubject.error(err);\n      }\n    }\n  }\n\n  return jsonSubject.asObservable();\n};\n", "import { Observable, Subject } from \"rxjs\";\nimport { HttpEvent, HttpEventType } from \"../run/http-request\";\nimport { BaseEvent } from \"@ag-ui/core\";\nimport * as proto from \"@ag-ui/proto\";\n\n/**\n * Parses a stream of HTTP events into a stream of BaseEvent objects using Protocol Buffer format.\n * Each message is prefixed with a 4-byte length header (uint32 in big-endian format)\n * followed by the protocol buffer encoded message.\n */\nexport const parseProtoStream = (source$: Observable<HttpEvent>): Observable<BaseEvent> => {\n  const eventSubject = new Subject<BaseEvent>();\n  let buffer = new Uint8Array(0);\n\n  source$.subscribe({\n    next: (event: HttpEvent) => {\n      if (event.type === HttpEventType.HEADERS) {\n        return;\n      }\n\n      if (event.type === HttpEventType.DATA && event.data) {\n        // Append the new data to our buffer\n        const newBuffer = new Uint8Array(buffer.length + event.data.length);\n        newBuffer.set(buffer, 0);\n        newBuffer.set(event.data, buffer.length);\n        buffer = newBuffer;\n\n        // Process as many complete messages as possible\n        processBuffer();\n      }\n    },\n    error: (err) => eventSubject.error(err),\n    complete: () => {\n      // Try to process any remaining data in the buffer\n      if (buffer.length > 0) {\n        try {\n          processBuffer();\n        } catch (error: unknown) {\n          console.warn(\"Incomplete or invalid protocol buffer data at stream end\");\n        }\n      }\n      eventSubject.complete();\n    },\n  });\n\n  /**\n   * Process as many complete messages as possible from the buffer\n   */\n  function processBuffer() {\n    // Keep processing while we have enough data for at least a header (4 bytes)\n    while (buffer.length >= 4) {\n      // Read message length from the first 4 bytes (big-endian uint32)\n      const view = new DataView(buffer.buffer, buffer.byteOffset, 4);\n      const messageLength = view.getUint32(0, false); // false = big-endian\n\n      // Check if we have the complete message (header + message body)\n      const totalLength = 4 + messageLength;\n      if (buffer.length < totalLength) {\n        // Not enough data yet, wait for more\n        break;\n      }\n\n      try {\n        // Extract the message (skipping the 4-byte header)\n        const message = buffer.slice(4, totalLength);\n\n        // Decode the protocol buffer message using the imported decode function\n        const event = proto.decode(message);\n\n        // Emit the parsed event\n        eventSubject.next(event);\n\n        // Remove the processed message from the buffer\n        buffer = buffer.slice(totalLength);\n      } catch (error: unknown) {\n        const errorMessage = error instanceof Error ? error.message : String(error);\n        eventSubject.error(new Error(`Failed to decode protocol buffer message: ${errorMessage}`));\n        return;\n      }\n    }\n  }\n\n  return eventSubject.asObservable();\n};\n", "import { mergeMap } from \"rxjs/operators\";\nimport { applyPatch } from \"fast-json-patch\";\n\nimport {\n  BaseEvent,\n  EventType,\n  TextMessageStartEvent,\n  TextMessageContentEvent,\n  TextMessageEndEvent,\n  ToolCallStartEvent,\n  ToolCallArgsEvent,\n  ToolCallEndEvent,\n  CustomEvent,\n  StateSnapshotEvent,\n  StepStartedEvent,\n  Message,\n  StateDeltaEvent,\n  MessagesSnapshotEvent,\n  ToolCall,\n} from \"@ag-ui/core\";\nimport { Observable } from \"rxjs\";\nimport {\n  LegacyTextMessageStart,\n  LegacyTextMessageContent,\n  LegacyTextMessageEnd,\n  LegacyActionExecutionStart,\n  LegacyActionExecutionArgs,\n  LegacyActionExecutionEnd,\n  LegacyRuntimeEventTypes,\n  LegacyRuntimeProtocolEvent,\n  LegacyMetaEvent,\n  LegacyAgentStateMessage,\n  LegacyMessage,\n  LegacyTextMessage,\n  LegacyActionExecutionMessage,\n  LegacyResultMessage,\n} from \"./types\";\nimport untruncateJson from \"untruncate-json\";\n\ninterface PredictStateValue {\n  state_key: string;\n  tool: string;\n  tool_argument: string;\n}\n\nexport const convertToLegacyEvents =\n  (threadId: string, runId: string, agentName: string) =>\n  (events$: Observable<BaseEvent>): Observable<LegacyRuntimeProtocolEvent> => {\n    let currentState: any = {};\n    let running = true;\n    let active = true;\n    let nodeName = \"\";\n    let syncedMessages: Message[] | null = null;\n    let predictState: PredictStateValue[] | null = null;\n    let currentToolCalls: ToolCall[] = [];\n\n    const updateCurrentState = (newState: any) => {\n      // the legacy protocol will only support object state\n      if (typeof newState === \"object\" && newState !== null) {\n        if (\"messages\" in newState) {\n          delete newState.messages;\n        }\n        currentState = newState;\n      }\n    };\n\n    return events$.pipe(\n      mergeMap((event) => {\n        switch (event.type) {\n          case EventType.TEXT_MESSAGE_START: {\n            const startEvent = event as TextMessageStartEvent;\n            return [\n              {\n                type: LegacyRuntimeEventTypes.enum.TextMessageStart,\n                messageId: startEvent.messageId,\n              } as LegacyTextMessageStart,\n            ];\n          }\n          case EventType.TEXT_MESSAGE_CONTENT: {\n            const contentEvent = event as TextMessageContentEvent;\n            return [\n              {\n                type: LegacyRuntimeEventTypes.enum.TextMessageContent,\n                messageId: contentEvent.messageId,\n                content: contentEvent.delta,\n              } as LegacyTextMessageContent,\n            ];\n          }\n          case EventType.TEXT_MESSAGE_END: {\n            const endEvent = event as TextMessageEndEvent;\n            return [\n              {\n                type: LegacyRuntimeEventTypes.enum.TextMessageEnd,\n                messageId: endEvent.messageId,\n              } as LegacyTextMessageEnd,\n            ];\n          }\n          case EventType.TOOL_CALL_START: {\n            const startEvent = event as ToolCallStartEvent;\n\n            currentToolCalls.push({\n              id: startEvent.toolCallId,\n              type: \"function\",\n              function: {\n                name: startEvent.toolCallName,\n                arguments: \"\",\n              },\n            });\n\n            active = true;\n\n            return [\n              {\n                type: LegacyRuntimeEventTypes.enum.ActionExecutionStart,\n                actionExecutionId: startEvent.toolCallId,\n                actionName: startEvent.toolCallName,\n                parentMessageId: startEvent.parentMessageId,\n              } as LegacyActionExecutionStart,\n            ];\n          }\n          case EventType.TOOL_CALL_ARGS: {\n            const argsEvent = event as ToolCallArgsEvent;\n\n            const currentToolCall = currentToolCalls[currentToolCalls.length - 1];\n            currentToolCall.function.arguments += argsEvent.delta;\n            let didUpdateState = false;\n\n            if (predictState) {\n              let currentPredictState = predictState.find(\n                (s) => s.tool == currentToolCall.function.name,\n              );\n\n              if (currentPredictState) {\n                try {\n                  const currentArgs = JSON.parse(\n                    untruncateJson(currentToolCall.function.arguments),\n                  );\n                  if (\n                    currentPredictState.tool_argument &&\n                    currentPredictState.tool_argument in currentArgs\n                  ) {\n                    updateCurrentState({\n                      ...currentState,\n                      [currentPredictState.state_key]:\n                        currentArgs[currentPredictState.tool_argument],\n                    });\n                    didUpdateState = true;\n                  } else if (!currentPredictState.tool_argument) {\n                    updateCurrentState({\n                      ...currentState,\n                      [currentPredictState.state_key]: currentArgs,\n                    });\n                    didUpdateState = true;\n                  }\n                } catch (e) {}\n              }\n            }\n\n            return [\n              {\n                type: LegacyRuntimeEventTypes.enum.ActionExecutionArgs,\n                actionExecutionId: argsEvent.toolCallId,\n                args: argsEvent.delta,\n              } as LegacyActionExecutionArgs,\n              ...(didUpdateState\n                ? [\n                    {\n                      type: LegacyRuntimeEventTypes.enum.AgentStateMessage,\n                      threadId,\n                      agentName,\n                      nodeName,\n                      runId,\n                      running,\n                      role: \"assistant\",\n                      state: JSON.stringify(currentState),\n                      active,\n                    },\n                  ]\n                : []),\n            ];\n          }\n          case EventType.TOOL_CALL_END: {\n            const endEvent = event as ToolCallEndEvent;\n            return [\n              {\n                type: LegacyRuntimeEventTypes.enum.ActionExecutionEnd,\n                actionExecutionId: endEvent.toolCallId,\n              } as LegacyActionExecutionEnd,\n            ];\n          }\n          case EventType.RAW: {\n            // The legacy protocol doesn't support raw events\n            return [];\n          }\n          case EventType.CUSTOM: {\n            const customEvent = event as CustomEvent;\n            switch (customEvent.name) {\n              case \"Exit\":\n                running = false;\n                break;\n              case \"PredictState\":\n                predictState = customEvent.value as PredictStateValue[];\n                break;\n            }\n\n            return [\n              {\n                type: LegacyRuntimeEventTypes.enum.MetaEvent,\n                name: customEvent.name,\n                value: customEvent.value,\n              } as LegacyMetaEvent,\n            ];\n          }\n          case EventType.STATE_SNAPSHOT: {\n            const stateEvent = event as StateSnapshotEvent;\n            updateCurrentState(stateEvent.snapshot);\n\n            return [\n              {\n                type: LegacyRuntimeEventTypes.enum.AgentStateMessage,\n                threadId,\n                agentName,\n                nodeName,\n                runId,\n                running,\n                role: \"assistant\",\n                state: JSON.stringify(currentState),\n                active,\n              } as LegacyAgentStateMessage,\n            ];\n          }\n          case EventType.STATE_DELTA: {\n            const deltaEvent = event as StateDeltaEvent;\n            const result = applyPatch(currentState, deltaEvent.delta, true, false);\n            if (!result) {\n              return [];\n            }\n            updateCurrentState(result.newDocument);\n\n            return [\n              {\n                type: LegacyRuntimeEventTypes.enum.AgentStateMessage,\n                threadId,\n                agentName,\n                nodeName,\n                runId,\n                running,\n                role: \"assistant\",\n                state: JSON.stringify(currentState),\n                active,\n              } as LegacyAgentStateMessage,\n            ];\n          }\n          case EventType.MESSAGES_SNAPSHOT: {\n            const messagesSnapshot = event as MessagesSnapshotEvent;\n            syncedMessages = messagesSnapshot.messages;\n            return [\n              {\n                type: LegacyRuntimeEventTypes.enum.AgentStateMessage,\n                threadId,\n                agentName,\n                nodeName,\n                runId,\n                running,\n                role: \"assistant\",\n                state: JSON.stringify({\n                  ...currentState,\n                  ...(syncedMessages ? { messages: syncedMessages } : {}),\n                }),\n                active: true,\n              } as LegacyAgentStateMessage,\n            ];\n          }\n          case EventType.RUN_STARTED: {\n            // There is nothing to do in the legacy protocol\n            return [];\n          }\n          case EventType.RUN_FINISHED: {\n            if (syncedMessages) {\n              currentState.messages = syncedMessages;\n            }\n\n            return [\n              {\n                type: LegacyRuntimeEventTypes.enum.AgentStateMessage,\n                threadId,\n                agentName,\n                nodeName,\n                runId,\n                running,\n                role: \"assistant\",\n                state: JSON.stringify({\n                  ...currentState,\n                  ...(syncedMessages\n                    ? {\n                        messages: convertMessagesToLegacyFormat(syncedMessages),\n                      }\n                    : {}),\n                }),\n                active: false,\n              } as LegacyAgentStateMessage,\n            ];\n          }\n          case EventType.RUN_ERROR: {\n            // legacy protocol does not have an event for errors\n            console.error(\"Run error\", event);\n            return [];\n          }\n          case EventType.STEP_STARTED: {\n            const stepStarted = event as StepStartedEvent;\n            nodeName = stepStarted.stepName;\n\n            currentToolCalls = [];\n            predictState = null;\n\n            return [\n              {\n                type: LegacyRuntimeEventTypes.enum.AgentStateMessage,\n                threadId,\n                agentName,\n                nodeName,\n                runId,\n                running,\n                role: \"assistant\",\n                state: JSON.stringify(currentState),\n                active: true,\n              } as LegacyAgentStateMessage,\n            ];\n          }\n          case EventType.STEP_FINISHED: {\n            currentToolCalls = [];\n            predictState = null;\n\n            return [\n              {\n                type: LegacyRuntimeEventTypes.enum.AgentStateMessage,\n                threadId,\n                agentName,\n                nodeName,\n                runId,\n                running,\n                role: \"assistant\",\n                state: JSON.stringify(currentState),\n                active: false,\n              } as LegacyAgentStateMessage,\n            ];\n          }\n          default: {\n            return [];\n          }\n        }\n      }),\n    );\n  };\n\nexport function convertMessagesToLegacyFormat(messages: Message[]): LegacyMessage[] {\n  const result: LegacyMessage[] = [];\n\n  for (const message of messages) {\n    if (message.role === \"assistant\" || message.role === \"user\" || message.role === \"system\") {\n      if (message.content) {\n        const textMessage: LegacyTextMessage = {\n          id: message.id,\n          role: message.role,\n          content: message.content,\n        };\n        result.push(textMessage);\n      }\n      if (message.role === \"assistant\" && message.toolCalls && message.toolCalls.length > 0) {\n        for (const toolCall of message.toolCalls) {\n          const actionExecutionMessage: LegacyActionExecutionMessage = {\n            id: toolCall.id,\n            name: toolCall.function.name,\n            arguments: JSON.parse(toolCall.function.arguments),\n            parentMessageId: message.id,\n          };\n          result.push(actionExecutionMessage);\n        }\n      }\n    } else if (message.role === \"tool\") {\n      let actionName = \"unknown\";\n      for (const m of messages) {\n        if (m.role === \"assistant\" && m.toolCalls?.length) {\n          for (const toolCall of m.toolCalls) {\n            if (toolCall.id === message.toolCallId) {\n              actionName = toolCall.function.name;\n              break;\n            }\n          }\n        }\n      }\n      const toolMessage: LegacyResultMessage = {\n        id: message.id,\n        result: message.content,\n        actionExecutionId: message.toolCallId,\n        actionName,\n      };\n      result.push(toolMessage);\n    }\n  }\n\n  return result;\n}\n", "import { z } from \"zod\";\n\n// Protocol Events\nexport const LegacyRuntimeEventTypes = z.enum([\n  \"TextMessageStart\",\n  \"TextMessageContent\",\n  \"TextMessageEnd\",\n  \"ActionExecutionStart\",\n  \"ActionExecutionArgs\",\n  \"ActionExecutionEnd\",\n  \"ActionExecutionResult\",\n  \"AgentStateMessage\",\n  \"MetaEvent\",\n  \"RunStarted\",\n  \"RunFinished\",\n  \"RunError\",\n  \"NodeStarted\",\n  \"NodeFinished\",\n]);\n\nexport const LegacyRuntimeMetaEventName = z.enum([\n  \"LangGraphInterruptEvent\",\n  \"PredictState\",\n  \"Exit\",\n]);\n\nexport const LegacyTextMessageStart = z.object({\n  type: z.literal(LegacyRuntimeEventTypes.enum.TextMessageStart),\n  messageId: z.string(),\n  parentMessageId: z.string().optional(),\n});\n\nexport const LegacyTextMessageContent = z.object({\n  type: z.literal(LegacyRuntimeEventTypes.enum.TextMessageContent),\n  messageId: z.string(),\n  content: z.string(),\n});\n\nexport const LegacyTextMessageEnd = z.object({\n  type: z.literal(LegacyRuntimeEventTypes.enum.TextMessageEnd),\n  messageId: z.string(),\n});\n\nexport const LegacyActionExecutionStart = z.object({\n  type: z.literal(LegacyRuntimeEventTypes.enum.ActionExecutionStart),\n  actionExecutionId: z.string(),\n  actionName: z.string(),\n  parentMessageId: z.string().optional(),\n});\n\nexport const LegacyActionExecutionArgs = z.object({\n  type: z.literal(LegacyRuntimeEventTypes.enum.ActionExecutionArgs),\n  actionExecutionId: z.string(),\n  args: z.string(),\n});\n\nexport const LegacyActionExecutionEnd = z.object({\n  type: z.literal(LegacyRuntimeEventTypes.enum.ActionExecutionEnd),\n  actionExecutionId: z.string(),\n});\n\nexport const LegacyActionExecutionResult = z.object({\n  type: z.literal(LegacyRuntimeEventTypes.enum.ActionExecutionResult),\n  actionName: z.string(),\n  actionExecutionId: z.string(),\n  result: z.string(),\n});\n\nexport const LegacyAgentStateMessage = z.object({\n  type: z.literal(LegacyRuntimeEventTypes.enum.AgentStateMessage),\n  threadId: z.string(),\n  agentName: z.string(),\n  nodeName: z.string(),\n  runId: z.string(),\n  active: z.boolean(),\n  role: z.string(),\n  state: z.string(),\n  running: z.boolean(),\n});\n\nexport const LegacyMetaEvent = z.object({\n  type: z.literal(LegacyRuntimeEventTypes.enum.MetaEvent),\n  name: LegacyRuntimeMetaEventName,\n  value: z.any(),\n});\n\nexport const LegacyRuntimeProtocolEvent = z.discriminatedUnion(\"type\", [\n  LegacyTextMessageStart,\n  LegacyTextMessageContent,\n  LegacyTextMessageEnd,\n  LegacyActionExecutionStart,\n  LegacyActionExecutionArgs,\n  LegacyActionExecutionEnd,\n  LegacyActionExecutionResult,\n  LegacyAgentStateMessage,\n  LegacyMetaEvent,\n]);\n\n// Protocol Event type exports\nexport type RuntimeEventTypes = z.infer<typeof LegacyRuntimeEventTypes>;\nexport type RuntimeMetaEventName = z.infer<typeof LegacyRuntimeMetaEventName>;\nexport type LegacyTextMessageStart = z.infer<typeof LegacyTextMessageStart>;\nexport type LegacyTextMessageContent = z.infer<typeof LegacyTextMessageContent>;\nexport type LegacyTextMessageEnd = z.infer<typeof LegacyTextMessageEnd>;\nexport type LegacyActionExecutionStart = z.infer<typeof LegacyActionExecutionStart>;\nexport type LegacyActionExecutionArgs = z.infer<typeof LegacyActionExecutionArgs>;\nexport type LegacyActionExecutionEnd = z.infer<typeof LegacyActionExecutionEnd>;\nexport type LegacyActionExecutionResult = z.infer<typeof LegacyActionExecutionResult>;\nexport type LegacyAgentStateMessage = z.infer<typeof LegacyAgentStateMessage>;\nexport type LegacyMetaEvent = z.infer<typeof LegacyMetaEvent>;\nexport type LegacyRuntimeProtocolEvent = z.infer<typeof LegacyRuntimeProtocolEvent>;\n\n// Message schemas (with kind discriminator)\nexport const LegacyTextMessageSchema = z.object({\n  id: z.string(),\n  role: z.string(),\n  content: z.string(),\n  parentMessageId: z.string().optional(),\n});\n\nexport const LegacyActionExecutionMessageSchema = z.object({\n  id: z.string(),\n  name: z.string(),\n  arguments: z.any(),\n  parentMessageId: z.string().optional(),\n});\n\nexport const LegacyResultMessageSchema = z.object({\n  id: z.string(),\n  result: z.any(),\n  actionExecutionId: z.string(),\n  actionName: z.string(),\n});\n\n// Message type exports\nexport type LegacyTextMessage = z.infer<typeof LegacyTextMessageSchema>;\nexport type LegacyActionExecutionMessage = z.infer<typeof LegacyActionExecutionMessageSchema>;\nexport type LegacyResultMessage = z.infer<typeof LegacyResultMessageSchema>;\nexport type LegacyMessage = LegacyTextMessage | LegacyActionExecutionMessage | LegacyResultMessage;\n", "import { defaultApplyEvents } from \"@/apply/default\";\nimport { Message, State, RunAgentInput, RunAgent, ApplyEvents, BaseEvent } from \"@ag-ui/core\";\n\nimport { AgentConfig, RunAgentParameters } from \"./types\";\nimport { v4 as uuidv4 } from \"uuid\";\nimport { structuredClone_ } from \"@/utils\";\nimport { catchError, map, tap } from \"rxjs/operators\";\nimport { finalize } from \"rxjs/operators\";\nimport { throwError, pipe, Observable } from \"rxjs\";\nimport { verifyEvents } from \"@/verify\";\nimport { convertToLegacyEvents } from \"@/legacy/convert\";\nimport { LegacyRuntimeProtocolEvent } from \"@/legacy/types\";\nimport { lastValueFrom, of } from \"rxjs\";\nimport { transformChunks } from \"@/chunks\";\n\nexport abstract class AbstractAgent {\n  public agentId?: string;\n  public description: string;\n  public threadId: string;\n  public messages: Message[];\n  public state: State;\n  /** @internal – internal debugging flag */\n  private debug: boolean = false;\n\n  constructor({\n    agentId,\n    description,\n    threadId,\n    initialMessages,\n    initialState,\n    debug,\n  }: AgentConfig = {}) {\n    this.agentId = agentId;\n    this.description = description ?? \"\";\n    this.threadId = threadId ?? uuidv4();\n    this.messages = structuredClone_(initialMessages ?? []);\n    this.state = structuredClone_(initialState ?? {});\n    this.debug = debug ?? false;\n  }\n\n  protected abstract run(...args: Parameters<RunAgent>): ReturnType<RunAgent>;\n\n  public async runAgent(parameters?: RunAgentParameters): Promise<void> {\n    this.agentId = this.agentId ?? uuidv4();\n    const input = this.prepareRunAgentInput(parameters);\n\n    const pipeline = pipe(\n      () => this.run(input),\n      transformChunks(this.debug),\n      verifyEvents(this.debug),\n      (source$) => this.apply(input, source$),\n      (source$) => this.processApplyEvents(input, source$),\n      catchError((error) => {\n        this.onError(error);\n        return throwError(() => error);\n      }),\n      finalize(() => {\n        this.onFinalize();\n      }),\n    );\n\n    return lastValueFrom(pipeline(of(null))).then(() => {});\n  }\n\n  public abortRun() {}\n\n  protected apply(...args: Parameters<ApplyEvents>): ReturnType<ApplyEvents> {\n    return defaultApplyEvents(...args);\n  }\n\n  protected processApplyEvents(\n    input: RunAgentInput,\n    events$: ReturnType<ApplyEvents>,\n  ): ReturnType<ApplyEvents> {\n    return events$.pipe(\n      tap((event) => {\n        if (event.messages) {\n          this.messages = event.messages;\n        }\n        if (event.state) {\n          this.state = event.state;\n        }\n      }),\n    );\n  }\n\n  protected prepareRunAgentInput(parameters?: RunAgentParameters): RunAgentInput {\n    return {\n      threadId: this.threadId,\n      runId: parameters?.runId || uuidv4(),\n      tools: structuredClone_(parameters?.tools ?? []),\n      context: structuredClone_(parameters?.context ?? []),\n      forwardedProps: structuredClone_(parameters?.forwardedProps ?? {}),\n      state: structuredClone_(this.state),\n      messages: structuredClone_(this.messages),\n    };\n  }\n\n  protected onError(error: Error) {\n    console.error(\"Agent execution failed:\", error);\n  }\n\n  protected onFinalize() {}\n\n  public clone() {\n    const cloned = Object.create(Object.getPrototypeOf(this));\n\n    for (const key of Object.getOwnPropertyNames(this)) {\n      const value = (this as any)[key];\n      if (typeof value !== \"function\") {\n        cloned[key] = structuredClone_(value);\n      }\n    }\n\n    return cloned;\n  }\n\n  public legacy_to_be_removed_runAgentBridged(\n    config?: RunAgentParameters,\n  ): Observable<LegacyRuntimeProtocolEvent> {\n    this.agentId = this.agentId ?? uuidv4();\n    const input = this.prepareRunAgentInput(config);\n\n    return this.run(input).pipe(\n      transformChunks(this.debug),\n      verifyEvents(this.debug),\n      convertToLegacyEvents(this.threadId, input.runId, this.agentId),\n      (events$: Observable<LegacyRuntimeProtocolEvent>) => {\n        return events$.pipe(\n          map((event) => {\n            if (this.debug) {\n              console.debug(\"[LEGACY]:\", JSON.stringify(event));\n            }\n            return event;\n          }),\n        );\n      },\n    );\n  }\n}\n", "import { mergeMap, Observable, finalize } from \"rxjs\";\nimport {\n  BaseEvent,\n  TextMessageChunkEvent,\n  TextMessageContentEvent,\n  TextMessageEndEvent,\n  TextMessageStartEvent,\n  ToolCallArgsEvent,\n  ToolCallChunkEvent,\n  ToolCallEndEvent,\n  ToolCallStartEvent,\n} from \"@ag-ui/core\";\nimport { EventType } from \"@ag-ui/core\";\n\ninterface TextMessageFields {\n  messageId: string;\n}\n\ninterface ToolCallFields {\n  toolCallId: string;\n  toolCallName: string;\n  parentMessageId?: string;\n}\n\nexport const transformChunks =\n  (debug: boolean) =>\n  (events$: Observable<BaseEvent>): Observable<BaseEvent> => {\n    let textMessageFields: TextMessageFields | undefined;\n    let toolCallFields: ToolCallFields | undefined;\n    let mode: \"text\" | \"tool\" | undefined;\n\n    const closeTextMessage = () => {\n      if (!textMessageFields || mode !== \"text\") {\n        throw new Error(\"No text message to close\");\n      }\n      const event = {\n        type: EventType.TEXT_MESSAGE_END,\n        messageId: textMessageFields.messageId,\n      } as TextMessageEndEvent;\n      mode = undefined;\n      textMessageFields = undefined;\n\n      if (debug) {\n        console.debug(\"[TRANSFORM]: TEXT_MESSAGE_END\", JSON.stringify(event));\n      }\n\n      return event;\n    };\n\n    const closeToolCall = () => {\n      if (!toolCallFields || mode !== \"tool\") {\n        throw new Error(\"No tool call to close\");\n      }\n      const event = {\n        type: EventType.TOOL_CALL_END,\n        toolCallId: toolCallFields.toolCallId,\n      } as ToolCallEndEvent;\n      mode = undefined;\n      toolCallFields = undefined;\n\n      if (debug) {\n        console.debug(\"[TRANSFORM]: TOOL_CALL_END\", JSON.stringify(event));\n      }\n\n      return event;\n    };\n\n    const closePendingEvent = () => {\n      if (mode === \"text\") {\n        return [closeTextMessage()];\n      }\n      if (mode === \"tool\") {\n        return [closeToolCall()];\n      }\n      return [];\n    };\n\n    return events$.pipe(\n      mergeMap((event) => {\n        switch (event.type) {\n          case EventType.TEXT_MESSAGE_START:\n          case EventType.TEXT_MESSAGE_CONTENT:\n          case EventType.TEXT_MESSAGE_END:\n          case EventType.TOOL_CALL_START:\n          case EventType.TOOL_CALL_ARGS:\n          case EventType.TOOL_CALL_END:\n          case EventType.STATE_SNAPSHOT:\n          case EventType.STATE_DELTA:\n          case EventType.MESSAGES_SNAPSHOT:\n          case EventType.CUSTOM:\n          case EventType.RUN_STARTED:\n          case EventType.RUN_FINISHED:\n          case EventType.RUN_ERROR:\n          case EventType.STEP_STARTED:\n          case EventType.STEP_FINISHED:\n            return [...closePendingEvent(), event];\n          case EventType.RAW:\n            return [event];\n          case EventType.TEXT_MESSAGE_CHUNK:\n            const messageChunkEvent = event as TextMessageChunkEvent;\n            const textMessageResult = [];\n            if (\n              // we are not in a text message\n              mode !== \"text\" ||\n              // or the message id is different\n              (messageChunkEvent.messageId !== undefined &&\n                messageChunkEvent.messageId !== textMessageFields?.messageId)\n            ) {\n              // close the current message if any\n              textMessageResult.push(...closePendingEvent());\n            }\n\n            // we are not in a text message, start a new one\n            if (mode !== \"text\") {\n              if (messageChunkEvent.messageId === undefined) {\n                throw new Error(\"First TEXT_MESSAGE_CHUNK must have a messageId\");\n              }\n\n              textMessageFields = {\n                messageId: messageChunkEvent.messageId,\n              };\n              mode = \"text\";\n\n              const textMessageStartEvent = {\n                type: EventType.TEXT_MESSAGE_START,\n                messageId: messageChunkEvent.messageId,\n                role: \"assistant\",\n              } as TextMessageStartEvent;\n\n              textMessageResult.push(textMessageStartEvent);\n\n              if (debug) {\n                console.debug(\n                  \"[TRANSFORM]: TEXT_MESSAGE_START\",\n                  JSON.stringify(textMessageStartEvent),\n                );\n              }\n            }\n\n            if (messageChunkEvent.delta !== undefined) {\n              const textMessageContentEvent = {\n                type: EventType.TEXT_MESSAGE_CONTENT,\n                messageId: textMessageFields!.messageId,\n                delta: messageChunkEvent.delta,\n              } as TextMessageContentEvent;\n\n              textMessageResult.push(textMessageContentEvent);\n\n              if (debug) {\n                console.debug(\n                  \"[TRANSFORM]: TEXT_MESSAGE_CONTENT\",\n                  JSON.stringify(textMessageContentEvent),\n                );\n              }\n            }\n\n            return textMessageResult;\n          case EventType.TOOL_CALL_CHUNK:\n            const toolCallChunkEvent = event as ToolCallChunkEvent;\n            const toolMessageResult = [];\n            if (\n              // we are not in a text message\n              mode !== \"tool\" ||\n              // or the tool call id is different\n              (toolCallChunkEvent.toolCallId !== undefined &&\n                toolCallChunkEvent.toolCallId !== toolCallFields?.toolCallId)\n            ) {\n              // close the current message if any\n              toolMessageResult.push(...closePendingEvent());\n            }\n\n            if (mode !== \"tool\") {\n              if (toolCallChunkEvent.toolCallId === undefined) {\n                throw new Error(\"First TOOL_CALL_CHUNK must have a toolCallId\");\n              }\n              if (toolCallChunkEvent.toolCallName === undefined) {\n                throw new Error(\"First TOOL_CALL_CHUNK must have a toolCallName\");\n              }\n              toolCallFields = {\n                toolCallId: toolCallChunkEvent.toolCallId,\n                toolCallName: toolCallChunkEvent.toolCallName,\n                parentMessageId: toolCallChunkEvent.parentMessageId,\n              };\n              mode = \"tool\";\n\n              const toolCallStartEvent = {\n                type: EventType.TOOL_CALL_START,\n                toolCallId: toolCallChunkEvent.toolCallId,\n                toolCallName: toolCallChunkEvent.toolCallName,\n                parentMessageId: toolCallChunkEvent.parentMessageId,\n              } as ToolCallStartEvent;\n\n              toolMessageResult.push(toolCallStartEvent);\n\n              if (debug) {\n                console.debug(\"[TRANSFORM]: TOOL_CALL_START\", JSON.stringify(toolCallStartEvent));\n              }\n            }\n\n            if (toolCallChunkEvent.delta !== undefined) {\n              const toolCallArgsEvent = {\n                type: EventType.TOOL_CALL_ARGS,\n                toolCallId: toolCallFields!.toolCallId,\n                delta: toolCallChunkEvent.delta,\n              } as ToolCallArgsEvent;\n\n              toolMessageResult.push(toolCallArgsEvent);\n\n              if (debug) {\n                console.debug(\"[TRANSFORM]: TOOL_CALL_ARGS\", JSON.stringify(toolCallArgsEvent));\n              }\n            }\n\n            return toolMessageResult;\n        }\n        const _exhaustiveCheck: never = event.type;\n      }),\n      finalize(() => {\n        // This ensures that we close any pending events when the source observable completes\n        return closePendingEvent();\n      }),\n    );\n  };\n", "import { AbstractAgent } from \"./agent\";\nimport { runHttpRequest, HttpEvent } from \"@/run/http-request\";\nimport { HttpAgentConfig, RunAgentParameters } from \"./types\";\nimport { RunAgent, RunAgentInput, BaseEvent } from \"@ag-ui/core\";\nimport { structuredClone_ } from \"@/utils\";\nimport { transformHttpEventStream } from \"@/transform/http\";\nimport { Observable } from \"rxjs\";\n\ninterface RunHttpAgentConfig extends RunAgentParameters {\n  abortController?: AbortController;\n}\n\nexport class HttpAgent extends AbstractAgent {\n  public url: string;\n  public headers: Record<string, string>;\n  public abortController: AbortController = new AbortController();\n\n  /**\n   * Returns the fetch config for the http request.\n   * Override this to customize the request.\n   *\n   * @returns The fetch config for the http request.\n   */\n  protected requestInit(input: RunAgentInput): RequestInit {\n    return {\n      method: \"POST\",\n      headers: {\n        ...this.headers,\n        \"Content-Type\": \"application/json\",\n        Accept: \"text/event-stream\",\n      },\n      body: JSON.stringify(input),\n      signal: this.abortController.signal,\n    };\n  }\n\n  public runAgent(parameters?: RunHttpAgentConfig) {\n    this.abortController = parameters?.abortController ?? new AbortController();\n    return super.runAgent(parameters);\n  }\n\n  abortRun() {\n    this.abortController.abort();\n    super.abortRun();\n  }\n\n  constructor(config: HttpAgentConfig) {\n    super(config);\n    this.url = config.url;\n    this.headers = structuredClone_(config.headers ?? {});\n  }\n\n  run(input: RunAgentInput): Observable<BaseEvent> {\n    const httpEvents = runHttpRequest(this.url, this.requestInit(input));\n    return transformHttpEventStream(httpEvents);\n  }\n}\n"], "mappings": "+8BAAA,IAAAA,EAAA,GAAAC,GAAAD,EAAA,mBAAAE,EAAA,cAAAC,EAAA,0BAAAC,EAAA,uBAAAC,EAAA,qBAAAC,EAAA,mBAAAC,EAAA,mBAAAC,EAAA,6BAAAC,EAAA,iBAAAC,IAAA,eAAAC,GAAAX,GCAA,IAAAY,EAcO,uBACPC,EAAyB,0BCflB,IAAMC,EAAoBC,GAAa,CAC5C,GAAI,OAAO,iBAAoB,WAC7B,OAAO,gBAAgBA,CAAG,EAG5B,GAAI,CACF,OAAO,KAAK,MAAM,KAAK,UAAUA,CAAG,CAAC,CACvC,OAASC,EAAK,CACZ,OAAOC,EAAA,GAAKF,EACd,CACF,EDOA,IAAAG,GAA2B,2BAC3BC,GAA2B,8BAUpB,IAAMC,EAAqB,IAAIC,IAA2D,CAC/F,GAAM,CAACC,EAAOC,CAAO,EAAIF,EAErBG,EAAWC,EAAiBH,EAAM,QAAQ,EAC1CI,EAAQD,EAAiBH,EAAM,KAAK,EACpCK,EAGEC,EAAcC,GAA2B,CAACJ,EAAiBI,CAAU,CAAC,EAEtEC,EAAe,IAAM,CAAC,EAE5B,OAAOP,EAAQ,QACb,YAAUQ,GAAU,CAzCxB,IAAAC,EA0CM,OAAQD,EAAM,KAAM,CAClB,KAAK,YAAU,mBAAoB,CACjC,GAAM,CAAE,UAAAE,EAAW,KAAAC,CAAK,EAAIH,EAGtBI,EAAsB,CAC1B,GAAIF,EACJ,KAAMC,EACN,QAAS,EACX,EAGA,OAAAV,EAAS,KAAKW,CAAU,EAEjBP,EAAW,CAAE,SAAAJ,CAAS,CAAC,CAChC,CAEA,KAAK,YAAU,qBAAsB,CACnC,GAAM,CAAE,MAAAY,CAAM,EAAIL,EAGZM,EAAcb,EAASA,EAAS,OAAS,CAAC,EAChD,OAAAa,EAAY,QAAUA,EAAY,QAAWD,EAEtCR,EAAW,CAAE,SAAAJ,CAAS,CAAC,CAChC,CAEA,KAAK,YAAU,iBACb,OAAOM,EAAa,EAGtB,KAAK,YAAU,gBAAiB,CAC9B,GAAM,CAAE,WAAAQ,EAAY,aAAAC,EAAc,gBAAAC,CAAgB,EAAIT,EAElDU,EAGJ,OACED,GACAhB,EAAS,OAAS,GAClBA,EAASA,EAAS,OAAS,CAAC,EAAE,KAAOgB,EAErCC,EAAgBjB,EAASA,EAAS,OAAS,CAAC,GAG5CiB,EAAgB,CACd,GAAID,GAAmBF,EACvB,KAAM,YACN,UAAW,CAAC,CACd,EACAd,EAAS,KAAKiB,CAAa,IAG7BT,EAAAS,EAAc,YAAd,OAAAA,EAAc,UAAc,CAAC,GAG7BA,EAAc,UAAU,KAAK,CAC3B,GAAIH,EACJ,KAAM,WACN,SAAU,CACR,KAAMC,EACN,UAAW,EACb,CACF,CAAC,EAEMX,EAAW,CAAE,SAAAJ,CAAS,CAAC,CAChC,CAEA,KAAK,YAAU,eAAgB,CAC7B,GAAM,CAAE,MAAAY,CAAM,EAAIL,EAGZM,EAAcb,EAASA,EAAS,OAAS,CAAC,EAG1CkB,EAAeL,EAAY,UAAUA,EAAY,UAAU,OAAS,CAAC,EAK3E,GAFAK,EAAa,SAAS,WAAaN,EAE/BT,EAAc,CAChB,IAAMgB,EAAShB,EAAa,KAAMiB,GAAMA,EAAE,OAASF,EAAa,SAAS,IAAI,EAC7E,GAAIC,EACF,GAAI,CACF,IAAME,EAAwB,KAAK,SACjC,GAAAC,SAAeJ,EAAa,SAAS,SAAS,CAChD,EACA,OAAIC,EAAO,eAAiBA,EAAO,iBAAiBE,GAClDnB,EAAQqB,EAAAC,EAAA,GACHtB,GADG,CAEN,CAACiB,EAAO,SAAS,EAAGE,EAAsBF,EAAO,aAAa,CAChE,GACOf,EAAW,CAAE,SAAAJ,EAAU,MAAAE,CAAM,CAAC,IAErCA,EAAQqB,EAAAC,EAAA,GACHtB,GADG,CAEN,CAACiB,EAAO,SAAS,EAAGE,CACtB,GACOjB,EAAW,CAAE,SAAAJ,EAAU,MAAAE,CAAM,CAAC,EAEzC,OAASuB,EAAG,CAAC,CAEjB,CAEA,OAAOrB,EAAW,CAAE,SAAAJ,CAAS,CAAC,CAChC,CAEA,KAAK,YAAU,cACb,OAAOM,EAAa,EAGtB,KAAK,YAAU,eAAgB,CAC7B,GAAM,CAAE,SAAAoB,CAAS,EAAInB,EAGrB,OAAAL,EAAQwB,EAEDtB,EAAW,CAAE,MAAAF,CAAM,CAAC,CAC7B,CAEA,KAAK,YAAU,YAAa,CAC1B,GAAM,CAAE,MAAAU,CAAM,EAAIL,EAElB,GAAI,CAGF,OAAAL,KADe,eAAWA,EAAOU,EAAO,GAAM,EAAK,EACpC,YACRR,EAAW,CAAE,MAAAF,CAAM,CAAC,CAC7B,OAASyB,EAAgB,CACvB,IAAMC,EAAeD,aAAiB,MAAQA,EAAM,QAAU,OAAOA,CAAK,EAC1E,eAAQ,KACN;AAAA,iBACoB,KAAK,UAAUzB,EAAO,KAAM,CAAC,CAAC;AAAA,oBAC3B,KAAK,UAAUU,EAAO,KAAM,CAAC,CAAC;AAAA,SACzCgB,CAAY,EAC1B,EACOtB,EAAa,CACtB,CACF,CAEA,KAAK,YAAU,kBAAmB,CAChC,GAAM,CAAE,SAAUuB,CAAY,EAAItB,EAGlC,OAAAP,EAAW6B,EAEJzB,EAAW,CAAE,SAAAJ,CAAS,CAAC,CAChC,CAEA,KAAK,YAAU,IACb,OAAOM,EAAa,EAGtB,KAAK,YAAU,OAAQ,CACrB,IAAMwB,EAAcvB,EAEpB,OAAIuB,EAAY,OAAS,iBACvB3B,EAAe2B,EAAY,OACpBxB,EAAa,CAIxB,CAEA,KAAK,YAAU,YACb,OAAOA,EAAa,EAGtB,KAAK,YAAU,aACb,OAAOA,EAAa,EAGtB,KAAK,YAAU,UACb,OAAOA,EAAa,EAGtB,KAAK,YAAU,aACb,OAAOA,EAAa,EAGtB,KAAK,YAAU,cAEb,OAAAH,EAAe,OACRG,EAAa,EAGtB,KAAK,YAAU,mBACb,MAAM,IAAI,MAAM,4DAA4D,EAG9E,KAAK,YAAU,gBACb,MAAM,IAAI,MAAM,yDAAyD,CAE7E,CAIA,IAAMyB,EAA0BxB,EAAM,KACtC,OAAOD,EAAa,CACtB,CAAC,CACH,CACF,EEnPA,IAAA0B,EAAgD,uBAChDC,EAA2C,gBAC3CC,GAAyB,0BAEZC,EACVC,GACAC,GAA0D,CAEzD,IAAIC,EACAC,EACAC,EAAc,GACdC,EAAW,GAEXC,EAAqB,GAErBC,EAAc,IAAI,IAEtB,OAAON,EAAQ,QAEb,aAAUO,GAAU,CAClB,IAAMC,EAAYD,EAAM,KAOxB,GALIR,GACF,QAAQ,MAAM,YAAa,KAAK,UAAUQ,CAAK,CAAC,EAI9CH,EACF,SAAO,cACL,IACE,IAAI,YACF,2BAA2BI,CAAS,iFACtC,CACJ,EAIF,GAAIL,GAAeK,IAAc,YAAU,UACzC,SAAO,cACL,IACE,IAAI,YACF,2BAA2BA,CAAS,0FACtC,CACJ,EAIF,GAAIP,IAAoB,QASlB,CAPsB,CACxB,YAAU,qBACV,YAAU,iBACV,YAAU,GACZ,EAGuB,SAASO,CAAS,EACvC,SAAO,cACL,IACE,IAAI,YACF,2BAA2BA,CAAS,8DACtC,CACJ,EAKJ,GAAIN,IAAqB,QASnB,CAPsB,CACxB,YAAU,eACV,YAAU,cACV,YAAU,GACZ,EAGuB,SAASM,CAAS,EAEvC,OAAIA,IAAc,YAAU,mBACnB,cACL,IACE,IAAI,YACF,kHACF,CACJ,KAGK,cACL,IACE,IAAI,YACF,2BAA2BA,CAAS,wDACtC,CACJ,EAKJ,GAAKH,GAKE,GAAIG,IAAc,YAAU,YAEjC,SAAO,cACL,IACE,IAAI,YACF,yJACF,CACJ,UAXAH,EAAqB,GACjBG,IAAc,YAAU,aAAeA,IAAc,YAAU,UACjE,SAAO,cAAW,IAAM,IAAI,YAAU,mCAAmC,CAAC,EAa9E,OAAQA,EAAW,CAEjB,KAAK,YAAU,mBAEb,OAAIP,IAAoB,UACf,cACL,IACE,IAAI,YACF,2HACF,CACJ,GAGFA,EAAmBM,EAAc,aAC1B,MAAGA,CAAK,GAGjB,KAAK,YAAU,qBAEb,OAAIN,IAAoB,UACf,cACL,IACE,IAAI,YACF,+HACF,CACJ,EAGGM,EAAc,YAAcN,KACxB,cACL,IACE,IAAI,YACF,0EAA2EM,EAAc,SAAS,0CAA0CN,CAAe,IAC7J,CACJ,KAGK,MAAGM,CAAK,EAGjB,KAAK,YAAU,iBAEb,OAAIN,IAAoB,UACf,cACL,IACE,IAAI,YACF,sHACF,CACJ,EAGGM,EAAc,YAAcN,KACxB,cACL,IACE,IAAI,YACF,sEAAuEM,EAAc,SAAS,0CAA0CN,CAAe,IACzJ,CACJ,GAIFA,EAAkB,UACX,MAAGM,CAAK,GAIjB,KAAK,YAAU,gBAEb,OAAIL,IAAqB,UAChB,cACL,IACE,IAAI,YACF,kHACF,CACJ,GAGFA,EAAoBK,EAAc,cAC3B,MAAGA,CAAK,GAGjB,KAAK,YAAU,eAEb,OAAIL,IAAqB,UAChB,cACL,IACE,IAAI,YACF,gHACF,CACJ,EAGGK,EAAc,aAAeL,KACzB,cACL,IACE,IAAI,YACF,sEAAuEK,EAAc,UAAU,4CAA4CL,CAAgB,IAC7J,CACJ,KAGK,MAAGK,CAAK,EAGjB,KAAK,YAAU,cAEb,OAAIL,IAAqB,UAChB,cACL,IACE,IAAI,YACF,6GACF,CACJ,EAGGK,EAAc,aAAeL,KACzB,cACL,IACE,IAAI,YACF,qEAAsEK,EAAc,UAAU,4CAA4CL,CAAgB,IAC5J,CACJ,GAIFA,EAAmB,UACZ,MAAGK,CAAK,GAIjB,KAAK,YAAU,aAAc,CAC3B,IAAME,EAAYF,EAAc,KAChC,OAAID,EAAY,IAAIG,CAAQ,KACnB,cACL,IAAM,IAAI,YAAU,SAASA,CAAQ,wCAAwC,CAC/E,GAEFH,EAAY,IAAIG,EAAU,EAAI,KACvB,MAAGF,CAAK,EACjB,CAEA,KAAK,YAAU,cAAe,CAC5B,IAAME,EAAYF,EAAc,KAChC,OAAKD,EAAY,IAAIG,CAAQ,GAQ7BH,EAAY,OAAOG,CAAQ,KACpB,MAAGF,CAAK,MARN,cACL,IACE,IAAI,YACF,yCAAyCE,CAAQ,wBACnD,CACJ,CAIJ,CAGA,KAAK,YAAU,YAEb,SAAO,MAAGF,CAAK,EAGjB,KAAK,YAAU,aAAc,CAK3B,GAAID,EAAY,KAAO,EAAG,CACxB,IAAMI,EAAkB,MAAM,KAAKJ,EAAY,KAAK,CAAC,EAAE,KAAK,IAAI,EAChE,SAAO,cACL,IACE,IAAI,YACF,4DAA4DI,CAAe,EAC7E,CACJ,CACF,CAEA,OAAAP,EAAc,MACP,MAAGI,CAAK,CACjB,CAEA,KAAK,YAAU,UAEb,OAAAH,EAAW,MACJ,MAAGG,CAAK,EAGjB,KAAK,YAAU,OACb,SAAO,MAAGA,CAAK,EAGjB,QACE,SAAO,MAAGA,CAAK,CAEnB,CACF,CAAC,CACH,CACF,ECrTF,IAAAI,GAAwC,uBACxCC,EAAmD,gBCDnD,IAAAC,EAAoD,gBACpDC,GAA0B,0BAoBnB,IAAMC,EAAiB,CAACC,EAAaC,OAEnC,SAAM,OAAM,QAAK,MAAMD,EAAKC,CAAW,CAAC,CAAC,EAAE,QAChD,cAAWC,GAAa,CAxB5B,IAAAC,EA0BM,IAAMC,EAAiC,CACrC,KAAM,UACN,OAAQF,EAAS,OACjB,QAASA,EAAS,OACpB,EAEMG,GAASF,EAAAD,EAAS,OAAT,YAAAC,EAAe,YAC9B,OAAKE,EAIE,IAAI,aAAuBC,IAEhCA,EAAW,KAAKF,CAAY,GAE3B,SAAY,CACX,GAAI,CACF,OAAa,CACX,GAAM,CAAE,KAAAG,EAAM,MAAAC,CAAM,EAAI,MAAMH,EAAO,KAAK,EAC1C,GAAIE,EAAM,MAEV,IAAME,EAA2B,CAC/B,KAAM,OACN,KAAMD,CACR,EACAF,EAAW,KAAKG,CAAS,CAC3B,CACAH,EAAW,SAAS,CACtB,OAASI,EAAO,CACdJ,EAAW,MAAMI,CAAK,CACxB,CACF,GAAG,EAEI,IAAM,CACXL,EAAO,OAAO,CAChB,EACD,KA5BQ,cAAW,IAAM,IAAI,MAAM,qCAAqC,CAAC,CA6B5E,CAAC,CACH,EChEF,IAAAM,GAAoC,gBAW7B,IAAMC,EAAkBC,GAAoD,CACjF,IAAMC,EAAc,IAAI,WAElBC,EAAU,IAAI,YAAY,QAAS,CAAE,MAAO,EAAM,CAAC,EACrDC,EAAS,GAGbH,EAAQ,UAAU,CAChB,KAAOI,GAAqB,CAC1B,GAAIA,EAAM,OAAS,WAIfA,EAAM,OAAS,QAAsBA,EAAM,KAAM,CAEnD,IAAMC,EAAOH,EAAQ,OAAOE,EAAM,KAAM,CAAE,OAAQ,EAAK,CAAC,EACxDD,GAAUE,EAGV,IAAMC,EAASH,EAAO,MAAM,MAAM,EAElCA,EAASG,EAAO,IAAI,GAAK,GAEzB,QAAWF,KAASE,EAClBC,EAAgBH,CAAK,CAEzB,CACF,EACA,MAAQI,GAAQP,EAAY,MAAMO,CAAG,EACrC,SAAU,IAAM,CAEVL,IACFA,GAAUD,EAAQ,OAAO,EAEzBK,EAAgBJ,CAAM,GAExBF,EAAY,SAAS,CACvB,CACF,CAAC,EAQD,SAASM,EAAgBE,EAAmB,CAC1C,IAAMC,EAAQD,EAAU,MAAM;AAAA,CAAI,EAC5BE,EAAsB,CAAC,EAE7B,QAAWC,KAAQF,EACbE,EAAK,WAAW,QAAQ,GAE1BD,EAAU,KAAKC,EAAK,MAAM,CAAC,CAAC,EAKhC,GAAID,EAAU,OAAS,EACrB,GAAI,CAEF,IAAME,EAAUF,EAAU,KAAK;AAAA,CAAI,EAC7BG,EAAO,KAAK,MAAMD,CAAO,EAC/BZ,EAAY,KAAKa,CAAI,CACvB,OAASN,EAAK,CACZP,EAAY,MAAMO,CAAG,CACvB,CAEJ,CAEA,OAAOP,EAAY,aAAa,CAClC,EClFA,IAAAc,GAAoC,gBAGpC,IAAAC,GAAuB,2BAOVC,EAAoBC,GAA0D,CACzF,IAAMC,EAAe,IAAI,WACrBC,EAAS,IAAI,WAAW,CAAC,EAE7BF,EAAQ,UAAU,CAChB,KAAOG,GAAqB,CAC1B,GAAIA,EAAM,OAAS,WAIfA,EAAM,OAAS,QAAsBA,EAAM,KAAM,CAEnD,IAAMC,EAAY,IAAI,WAAWF,EAAO,OAASC,EAAM,KAAK,MAAM,EAClEC,EAAU,IAAIF,EAAQ,CAAC,EACvBE,EAAU,IAAID,EAAM,KAAMD,EAAO,MAAM,EACvCA,EAASE,EAGTC,EAAc,CAChB,CACF,EACA,MAAQC,GAAQL,EAAa,MAAMK,CAAG,EACtC,SAAU,IAAM,CAEd,GAAIJ,EAAO,OAAS,EAClB,GAAI,CACFG,EAAc,CAChB,OAASE,EAAgB,CACvB,QAAQ,KAAK,0DAA0D,CACzE,CAEFN,EAAa,SAAS,CACxB,CACF,CAAC,EAKD,SAASI,GAAgB,CAEvB,KAAOH,EAAO,QAAU,GAAG,CAMzB,IAAMM,EAAc,EAJP,IAAI,SAASN,EAAO,OAAQA,EAAO,WAAY,CAAC,EAClC,UAAU,EAAG,EAAK,EAI7C,GAAIA,EAAO,OAASM,EAElB,MAGF,GAAI,CAEF,IAAMC,EAAUP,EAAO,MAAM,EAAGM,CAAW,EAGrCL,EAAc,UAAOM,CAAO,EAGlCR,EAAa,KAAKE,CAAK,EAGvBD,EAASA,EAAO,MAAMM,CAAW,CACnC,OAASD,EAAgB,CACvB,IAAMG,EAAeH,aAAiB,MAAQA,EAAM,QAAU,OAAOA,CAAK,EAC1EN,EAAa,MAAM,IAAI,MAAM,6CAA6CS,CAAY,EAAE,CAAC,EACzF,MACF,CACF,CACF,CAEA,OAAOT,EAAa,aAAa,CACnC,EH9EA,IAAAU,GAAuB,2BAKVC,EAA4BC,GAA0D,CACjG,IAAMC,EAAe,IAAI,UAGnBC,EAAgB,IAAI,gBAGtBC,EAAoB,GAGxB,OAAAH,EAAQ,UAAU,CAChB,KAAOI,GAAqB,CAE1BF,EAAc,KAAKE,CAAK,EAGpBA,EAAM,OAAS,WAAyB,CAACD,GAC3CA,EAAoB,GACAC,EAAM,QAAQ,IAAI,cAAc,IAG1B,mBAExBC,EAAiBH,CAAa,EAAE,UAAU,CACxC,KAAOE,GAAUH,EAAa,KAAKG,CAAK,EACxC,MAAQE,GAAQL,EAAa,MAAMK,CAAG,EACtC,SAAU,IAAML,EAAa,SAAS,CACxC,CAAC,EAGDM,EAAeL,CAAa,EAAE,UAAU,CACtC,KAAOM,GAAS,CACd,GAAI,CACF,IAAMC,EAAc,gBAAa,MAAMD,CAAI,EAC3CP,EAAa,KAAKQ,CAAwB,CAC5C,OAASH,EAAK,CACZL,EAAa,MAAMK,CAAG,CACxB,CACF,EACA,MAAQA,GAAQL,EAAa,MAAMK,CAAG,EACtC,SAAU,IAAML,EAAa,SAAS,CACxC,CAAC,GAEOE,GACVF,EAAa,MAAM,IAAI,MAAM,8CAA8C,CAAC,CAEhF,EACA,MAAQK,GAAQ,CACdJ,EAAc,MAAMI,CAAG,EACvBL,EAAa,MAAMK,CAAG,CACxB,EACA,SAAU,IAAM,CACdJ,EAAc,SAAS,CACzB,CACF,CAAC,EAEMD,EAAa,aAAa,CACnC,EInEA,IAAAS,GAAyB,0BACzBC,GAA2B,2BAE3BC,EAgBO,uBCnBP,IAAAC,EAAkB,eAGLC,EAA0B,IAAE,KAAK,CAC5C,mBACA,qBACA,iBACA,uBACA,sBACA,qBACA,wBACA,oBACA,YACA,aACA,cACA,WACA,cACA,cACF,CAAC,EAEYC,GAA6B,IAAE,KAAK,CAC/C,0BACA,eACA,MACF,CAAC,EAEYC,GAAyB,IAAE,OAAO,CAC7C,KAAM,IAAE,QAAQF,EAAwB,KAAK,gBAAgB,EAC7D,UAAW,IAAE,OAAO,EACpB,gBAAiB,IAAE,OAAO,EAAE,SAAS,CACvC,CAAC,EAEYG,GAA2B,IAAE,OAAO,CAC/C,KAAM,IAAE,QAAQH,EAAwB,KAAK,kBAAkB,EAC/D,UAAW,IAAE,OAAO,EACpB,QAAS,IAAE,OAAO,CACpB,CAAC,EAEYI,GAAuB,IAAE,OAAO,CAC3C,KAAM,IAAE,QAAQJ,EAAwB,KAAK,cAAc,EAC3D,UAAW,IAAE,OAAO,CACtB,CAAC,EAEYK,GAA6B,IAAE,OAAO,CACjD,KAAM,IAAE,QAAQL,EAAwB,KAAK,oBAAoB,EACjE,kBAAmB,IAAE,OAAO,EAC5B,WAAY,IAAE,OAAO,EACrB,gBAAiB,IAAE,OAAO,EAAE,SAAS,CACvC,CAAC,EAEYM,GAA4B,IAAE,OAAO,CAChD,KAAM,IAAE,QAAQN,EAAwB,KAAK,mBAAmB,EAChE,kBAAmB,IAAE,OAAO,EAC5B,KAAM,IAAE,OAAO,CACjB,CAAC,EAEYO,GAA2B,IAAE,OAAO,CAC/C,KAAM,IAAE,QAAQP,EAAwB,KAAK,kBAAkB,EAC/D,kBAAmB,IAAE,OAAO,CAC9B,CAAC,EAEYQ,GAA8B,IAAE,OAAO,CAClD,KAAM,IAAE,QAAQR,EAAwB,KAAK,qBAAqB,EAClE,WAAY,IAAE,OAAO,EACrB,kBAAmB,IAAE,OAAO,EAC5B,OAAQ,IAAE,OAAO,CACnB,CAAC,EAEYS,GAA0B,IAAE,OAAO,CAC9C,KAAM,IAAE,QAAQT,EAAwB,KAAK,iBAAiB,EAC9D,SAAU,IAAE,OAAO,EACnB,UAAW,IAAE,OAAO,EACpB,SAAU,IAAE,OAAO,EACnB,MAAO,IAAE,OAAO,EAChB,OAAQ,IAAE,QAAQ,EAClB,KAAM,IAAE,OAAO,EACf,MAAO,IAAE,OAAO,EAChB,QAAS,IAAE,QAAQ,CACrB,CAAC,EAEYU,GAAkB,IAAE,OAAO,CACtC,KAAM,IAAE,QAAQV,EAAwB,KAAK,SAAS,EACtD,KAAMC,GACN,MAAO,IAAE,IAAI,CACf,CAAC,EAEYU,GAA6B,IAAE,mBAAmB,OAAQ,CACrET,GACAC,GACAC,GACAC,GACAC,GACAC,GACAC,GACAC,GACAC,EACF,CAAC,EAiBYE,GAA0B,IAAE,OAAO,CAC9C,GAAI,IAAE,OAAO,EACb,KAAM,IAAE,OAAO,EACf,QAAS,IAAE,OAAO,EAClB,gBAAiB,IAAE,OAAO,EAAE,SAAS,CACvC,CAAC,EAEYC,GAAqC,IAAE,OAAO,CACzD,GAAI,IAAE,OAAO,EACb,KAAM,IAAE,OAAO,EACf,UAAW,IAAE,IAAI,EACjB,gBAAiB,IAAE,OAAO,EAAE,SAAS,CACvC,CAAC,EAEYC,GAA4B,IAAE,OAAO,CAChD,GAAI,IAAE,OAAO,EACb,OAAQ,IAAE,IAAI,EACd,kBAAmB,IAAE,OAAO,EAC5B,WAAY,IAAE,OAAO,CACvB,CAAC,ED/FD,IAAAC,GAA2B,8BAQpB,IAAMC,EACX,CAACC,EAAkBC,EAAeC,IACjCC,GAA2E,CAC1E,IAAIC,EAAoB,CAAC,EACrBC,EAAU,GACVC,EAAS,GACTC,EAAW,GACXC,EAAmC,KACnCC,EAA2C,KAC3CC,EAA+B,CAAC,EAE9BC,EAAsBC,GAAkB,CAExC,OAAOA,GAAa,UAAYA,IAAa,OAC3C,aAAcA,GAChB,OAAOA,EAAS,SAElBR,EAAeQ,EAEnB,EAEA,OAAOT,EAAQ,QACb,aAAUU,GAAU,CAClB,OAAQA,EAAM,KAAM,CAClB,KAAK,YAAU,mBAAoB,CACjC,IAAMC,EAAaD,EACnB,MAAO,CACL,CACE,KAAME,EAAwB,KAAK,iBACnC,UAAWD,EAAW,SACxB,CACF,CACF,CACA,KAAK,YAAU,qBAAsB,CACnC,IAAME,EAAeH,EACrB,MAAO,CACL,CACE,KAAME,EAAwB,KAAK,mBACnC,UAAWC,EAAa,UACxB,QAASA,EAAa,KACxB,CACF,CACF,CACA,KAAK,YAAU,iBAAkB,CAC/B,IAAMC,EAAWJ,EACjB,MAAO,CACL,CACE,KAAME,EAAwB,KAAK,eACnC,UAAWE,EAAS,SACtB,CACF,CACF,CACA,KAAK,YAAU,gBAAiB,CAC9B,IAAMH,EAAaD,EAEnB,OAAAH,EAAiB,KAAK,CACpB,GAAII,EAAW,WACf,KAAM,WACN,SAAU,CACR,KAAMA,EAAW,aACjB,UAAW,EACb,CACF,CAAC,EAEDR,EAAS,GAEF,CACL,CACE,KAAMS,EAAwB,KAAK,qBACnC,kBAAmBD,EAAW,WAC9B,WAAYA,EAAW,aACvB,gBAAiBA,EAAW,eAC9B,CACF,CACF,CACA,KAAK,YAAU,eAAgB,CAC7B,IAAMI,EAAYL,EAEZM,EAAkBT,EAAiBA,EAAiB,OAAS,CAAC,EACpES,EAAgB,SAAS,WAAaD,EAAU,MAChD,IAAIE,EAAiB,GAErB,GAAIX,EAAc,CAChB,IAAIY,EAAsBZ,EAAa,KACpCa,GAAMA,EAAE,MAAQH,EAAgB,SAAS,IAC5C,EAEA,GAAIE,EACF,GAAI,CACF,IAAME,EAAc,KAAK,SACvB,GAAAC,SAAeL,EAAgB,SAAS,SAAS,CACnD,EAEEE,EAAoB,eACpBA,EAAoB,iBAAiBE,GAErCZ,EAAmBc,EAAAC,EAAA,GACdtB,GADc,CAEjB,CAACiB,EAAoB,SAAS,EAC5BE,EAAYF,EAAoB,aAAa,CACjD,EAAC,EACDD,EAAiB,IACPC,EAAoB,gBAC9BV,EAAmBc,EAAAC,EAAA,GACdtB,GADc,CAEjB,CAACiB,EAAoB,SAAS,EAAGE,CACnC,EAAC,EACDH,EAAiB,GAErB,OAASO,EAAG,CAAC,CAEjB,CAEA,MAAO,CACL,CACE,KAAMZ,EAAwB,KAAK,oBACnC,kBAAmBG,EAAU,WAC7B,KAAMA,EAAU,KAClB,EACA,GAAIE,EACA,CACE,CACE,KAAML,EAAwB,KAAK,kBACnC,SAAAf,EACA,UAAAE,EACA,SAAAK,EACA,MAAAN,EACA,QAAAI,EACA,KAAM,YACN,MAAO,KAAK,UAAUD,CAAY,EAClC,OAAAE,CACF,CACF,EACA,CAAC,CACP,CACF,CACA,KAAK,YAAU,cAAe,CAC5B,IAAMW,EAAWJ,EACjB,MAAO,CACL,CACE,KAAME,EAAwB,KAAK,mBACnC,kBAAmBE,EAAS,UAC9B,CACF,CACF,CACA,KAAK,YAAU,IAEb,MAAO,CAAC,EAEV,KAAK,YAAU,OAAQ,CACrB,IAAMW,EAAcf,EACpB,OAAQe,EAAY,KAAM,CACxB,IAAK,OACHvB,EAAU,GACV,MACF,IAAK,eACHI,EAAemB,EAAY,MAC3B,KACJ,CAEA,MAAO,CACL,CACE,KAAMb,EAAwB,KAAK,UACnC,KAAMa,EAAY,KAClB,MAAOA,EAAY,KACrB,CACF,CACF,CACA,KAAK,YAAU,eAEb,OAAAjB,EADmBE,EACW,QAAQ,EAE/B,CACL,CACE,KAAME,EAAwB,KAAK,kBACnC,SAAAf,EACA,UAAAE,EACA,SAAAK,EACA,MAAAN,EACA,QAAAI,EACA,KAAM,YACN,MAAO,KAAK,UAAUD,CAAY,EAClC,OAAAE,CACF,CACF,EAEF,KAAK,YAAU,YAAa,CAE1B,IAAMuB,KAAS,eAAWzB,EADPS,EACgC,MAAO,GAAM,EAAK,EACrE,OAAKgB,GAGLlB,EAAmBkB,EAAO,WAAW,EAE9B,CACL,CACE,KAAMd,EAAwB,KAAK,kBACnC,SAAAf,EACA,UAAAE,EACA,SAAAK,EACA,MAAAN,EACA,QAAAI,EACA,KAAM,YACN,MAAO,KAAK,UAAUD,CAAY,EAClC,OAAAE,CACF,CACF,GAhBS,CAAC,CAiBZ,CACA,KAAK,YAAU,kBAEb,OAAAE,EADyBK,EACS,SAC3B,CACL,CACE,KAAME,EAAwB,KAAK,kBACnC,SAAAf,EACA,UAAAE,EACA,SAAAK,EACA,MAAAN,EACA,QAAAI,EACA,KAAM,YACN,MAAO,KAAK,UAAUqB,IAAA,GACjBtB,GACCI,EAAiB,CAAE,SAAUA,CAAe,EAAI,CAAC,EACtD,EACD,OAAQ,EACV,CACF,EAEF,KAAK,YAAU,YAEb,MAAO,CAAC,EAEV,KAAK,YAAU,aACb,OAAIA,IACFJ,EAAa,SAAWI,GAGnB,CACL,CACE,KAAMO,EAAwB,KAAK,kBACnC,SAAAf,EACA,UAAAE,EACA,SAAAK,EACA,MAAAN,EACA,QAAAI,EACA,KAAM,YACN,MAAO,KAAK,UAAUqB,IAAA,GACjBtB,GACCI,EACA,CACE,SAAUsB,GAA8BtB,CAAc,CACxD,EACA,CAAC,EACN,EACD,OAAQ,EACV,CACF,EAEF,KAAK,YAAU,UAEb,eAAQ,MAAM,YAAaK,CAAK,EACzB,CAAC,EAEV,KAAK,YAAU,aAEb,OAAAN,EADoBM,EACG,SAEvBH,EAAmB,CAAC,EACpBD,EAAe,KAER,CACL,CACE,KAAMM,EAAwB,KAAK,kBACnC,SAAAf,EACA,UAAAE,EACA,SAAAK,EACA,MAAAN,EACA,QAAAI,EACA,KAAM,YACN,MAAO,KAAK,UAAUD,CAAY,EAClC,OAAQ,EACV,CACF,EAEF,KAAK,YAAU,cACb,OAAAM,EAAmB,CAAC,EACpBD,EAAe,KAER,CACL,CACE,KAAMM,EAAwB,KAAK,kBACnC,SAAAf,EACA,UAAAE,EACA,SAAAK,EACA,MAAAN,EACA,QAAAI,EACA,KAAM,YACN,MAAO,KAAK,UAAUD,CAAY,EAClC,OAAQ,EACV,CACF,EAEF,QACE,MAAO,CAAC,CAEZ,CACF,CAAC,CACH,CACF,EAEK,SAAS0B,GAA8BC,EAAsC,CAnWpF,IAAAC,EAoWE,IAAMH,EAA0B,CAAC,EAEjC,QAAWI,KAAWF,EACpB,GAAIE,EAAQ,OAAS,aAAeA,EAAQ,OAAS,QAAUA,EAAQ,OAAS,SAAU,CACxF,GAAIA,EAAQ,QAAS,CACnB,IAAMC,EAAiC,CACrC,GAAID,EAAQ,GACZ,KAAMA,EAAQ,KACd,QAASA,EAAQ,OACnB,EACAJ,EAAO,KAAKK,CAAW,CACzB,CACA,GAAID,EAAQ,OAAS,aAAeA,EAAQ,WAAaA,EAAQ,UAAU,OAAS,EAClF,QAAWE,KAAYF,EAAQ,UAAW,CACxC,IAAMG,EAAuD,CAC3D,GAAID,EAAS,GACb,KAAMA,EAAS,SAAS,KACxB,UAAW,KAAK,MAAMA,EAAS,SAAS,SAAS,EACjD,gBAAiBF,EAAQ,EAC3B,EACAJ,EAAO,KAAKO,CAAsB,CACpC,CAEJ,SAAWH,EAAQ,OAAS,OAAQ,CAClC,IAAII,EAAa,UACjB,QAAWC,KAAKP,EACd,GAAIO,EAAE,OAAS,eAAeN,EAAAM,EAAE,YAAF,MAAAN,EAAa,SACzC,QAAWG,KAAYG,EAAE,UACvB,GAAIH,EAAS,KAAOF,EAAQ,WAAY,CACtCI,EAAaF,EAAS,SAAS,KAC/B,KACF,EAIN,IAAMI,EAAmC,CACvC,GAAIN,EAAQ,GACZ,OAAQA,EAAQ,QAChB,kBAAmBA,EAAQ,WAC3B,WAAAI,CACF,EACAR,EAAO,KAAKU,CAAW,CACzB,CAGF,OAAOV,CACT,CE9YA,IAAAW,EAA6B,gBAE7B,IAAAC,EAAqC,0BACrCA,GAAyB,0BACzBC,EAA6C,gBAI7C,IAAAC,EAAkC,gBCZlC,IAAAC,EAA+C,gBAY/CC,EAA0B,uBAYbC,EACVC,GACAC,GAA0D,CACzD,IAAIC,EACAC,EACAC,EAEEC,EAAmB,IAAM,CAC7B,GAAI,CAACH,GAAqBE,IAAS,OACjC,MAAM,IAAI,MAAM,0BAA0B,EAE5C,IAAME,EAAQ,CACZ,KAAM,YAAU,iBAChB,UAAWJ,EAAkB,SAC/B,EACA,OAAAE,EAAO,OACPF,EAAoB,OAEhBF,GACF,QAAQ,MAAM,gCAAiC,KAAK,UAAUM,CAAK,CAAC,EAG/DA,CACT,EAEMC,EAAgB,IAAM,CAC1B,GAAI,CAACJ,GAAkBC,IAAS,OAC9B,MAAM,IAAI,MAAM,uBAAuB,EAEzC,IAAME,EAAQ,CACZ,KAAM,YAAU,cAChB,WAAYH,EAAe,UAC7B,EACA,OAAAC,EAAO,OACPD,EAAiB,OAEbH,GACF,QAAQ,MAAM,6BAA8B,KAAK,UAAUM,CAAK,CAAC,EAG5DA,CACT,EAEME,EAAoB,IACpBJ,IAAS,OACJ,CAACC,EAAiB,CAAC,EAExBD,IAAS,OACJ,CAACG,EAAc,CAAC,EAElB,CAAC,EAGV,OAAON,EAAQ,QACb,YAAUK,GAAU,CAClB,OAAQA,EAAM,KAAM,CAClB,KAAK,YAAU,mBACf,KAAK,YAAU,qBACf,KAAK,YAAU,iBACf,KAAK,YAAU,gBACf,KAAK,YAAU,eACf,KAAK,YAAU,cACf,KAAK,YAAU,eACf,KAAK,YAAU,YACf,KAAK,YAAU,kBACf,KAAK,YAAU,OACf,KAAK,YAAU,YACf,KAAK,YAAU,aACf,KAAK,YAAU,UACf,KAAK,YAAU,aACf,KAAK,YAAU,cACb,MAAO,CAAC,GAAGE,EAAkB,EAAGF,CAAK,EACvC,KAAK,YAAU,IACb,MAAO,CAACA,CAAK,EACf,KAAK,YAAU,mBACb,IAAMG,EAAoBH,EACpBI,EAAoB,CAAC,EAa3B,IAVEN,IAAS,QAERK,EAAkB,YAAc,QAC/BA,EAAkB,aAAcP,GAAA,YAAAA,EAAmB,aAGrDQ,EAAkB,KAAK,GAAGF,EAAkB,CAAC,EAI3CJ,IAAS,OAAQ,CACnB,GAAIK,EAAkB,YAAc,OAClC,MAAM,IAAI,MAAM,gDAAgD,EAGlEP,EAAoB,CAClB,UAAWO,EAAkB,SAC/B,EACAL,EAAO,OAEP,IAAMO,EAAwB,CAC5B,KAAM,YAAU,mBAChB,UAAWF,EAAkB,UAC7B,KAAM,WACR,EAEAC,EAAkB,KAAKC,CAAqB,EAExCX,GACF,QAAQ,MACN,kCACA,KAAK,UAAUW,CAAqB,CACtC,CAEJ,CAEA,GAAIF,EAAkB,QAAU,OAAW,CACzC,IAAMG,EAA0B,CAC9B,KAAM,YAAU,qBAChB,UAAWV,EAAmB,UAC9B,MAAOO,EAAkB,KAC3B,EAEAC,EAAkB,KAAKE,CAAuB,EAE1CZ,GACF,QAAQ,MACN,oCACA,KAAK,UAAUY,CAAuB,CACxC,CAEJ,CAEA,OAAOF,EACT,KAAK,YAAU,gBACb,IAAMG,EAAqBP,EACrBQ,EAAoB,CAAC,EAY3B,IATEV,IAAS,QAERS,EAAmB,aAAe,QACjCA,EAAmB,cAAeV,GAAA,YAAAA,EAAgB,cAGpDW,EAAkB,KAAK,GAAGN,EAAkB,CAAC,EAG3CJ,IAAS,OAAQ,CACnB,GAAIS,EAAmB,aAAe,OACpC,MAAM,IAAI,MAAM,8CAA8C,EAEhE,GAAIA,EAAmB,eAAiB,OACtC,MAAM,IAAI,MAAM,gDAAgD,EAElEV,EAAiB,CACf,WAAYU,EAAmB,WAC/B,aAAcA,EAAmB,aACjC,gBAAiBA,EAAmB,eACtC,EACAT,EAAO,OAEP,IAAMW,EAAqB,CACzB,KAAM,YAAU,gBAChB,WAAYF,EAAmB,WAC/B,aAAcA,EAAmB,aACjC,gBAAiBA,EAAmB,eACtC,EAEAC,EAAkB,KAAKC,CAAkB,EAErCf,GACF,QAAQ,MAAM,+BAAgC,KAAK,UAAUe,CAAkB,CAAC,CAEpF,CAEA,GAAIF,EAAmB,QAAU,OAAW,CAC1C,IAAMG,EAAoB,CACxB,KAAM,YAAU,eAChB,WAAYb,EAAgB,WAC5B,MAAOU,EAAmB,KAC5B,EAEAC,EAAkB,KAAKE,CAAiB,EAEpChB,GACF,QAAQ,MAAM,8BAA+B,KAAK,UAAUgB,CAAiB,CAAC,CAElF,CAEA,OAAOF,CACX,CACA,IAAMG,EAA0BX,EAAM,IACxC,CAAC,KACD,YAAS,IAEAE,EAAkB,CAC1B,CACH,CACF,ED/MK,IAAeU,EAAf,KAA6B,CASlC,YAAY,CACV,QAAAC,EACA,YAAAC,EACA,SAAAC,EACA,gBAAAC,EACA,aAAAC,EACA,MAAAC,CACF,EAAiB,CAAC,EAAG,CATrB,KAAQ,MAAiB,GAUvB,KAAK,QAAUL,EACf,KAAK,YAAcC,GAAA,KAAAA,EAAe,GAClC,KAAK,SAAWC,GAAA,KAAAA,KAAY,EAAAI,IAAO,EACnC,KAAK,SAAWC,EAAiBJ,GAAA,KAAAA,EAAmB,CAAC,CAAC,EACtD,KAAK,MAAQI,EAAiBH,GAAA,KAAAA,EAAgB,CAAC,CAAC,EAChD,KAAK,MAAQC,GAAA,KAAAA,EAAS,EACxB,CAIA,MAAa,SAASG,EAAgD,CA1CxE,IAAAC,EA2CI,KAAK,SAAUA,EAAA,KAAK,UAAL,KAAAA,KAAgB,EAAAH,IAAO,EACtC,IAAMI,EAAQ,KAAK,qBAAqBF,CAAU,EAE5CG,KAAW,QACf,IAAM,KAAK,IAAID,CAAK,EACpBE,EAAgB,KAAK,KAAK,EAC1BC,EAAa,KAAK,KAAK,EACtBC,GAAY,KAAK,MAAMJ,EAAOI,CAAO,EACrCA,GAAY,KAAK,mBAAmBJ,EAAOI,CAAO,KACnD,cAAYC,IACV,KAAK,QAAQA,CAAK,KACX,cAAW,IAAMA,CAAK,EAC9B,KACD,aAAS,IAAM,CACb,KAAK,WAAW,CAClB,CAAC,CACH,EAEA,SAAO,iBAAcJ,KAAS,MAAG,IAAI,CAAC,CAAC,EAAE,KAAK,IAAM,CAAC,CAAC,CACxD,CAEO,UAAW,CAAC,CAET,SAASK,EAAwD,CACzE,OAAOC,EAAmB,GAAGD,CAAI,CACnC,CAEU,mBACRN,EACAQ,EACyB,CACzB,OAAOA,EAAQ,QACb,OAAKC,GAAU,CACTA,EAAM,WACR,KAAK,SAAWA,EAAM,UAEpBA,EAAM,QACR,KAAK,MAAQA,EAAM,MAEvB,CAAC,CACH,CACF,CAEU,qBAAqBX,EAAgD,CAtFjF,IAAAC,EAAAW,EAAAC,EAuFI,MAAO,CACL,SAAU,KAAK,SACf,OAAOb,GAAA,YAAAA,EAAY,WAAS,EAAAF,IAAO,EACnC,MAAOC,GAAiBE,EAAAD,GAAA,YAAAA,EAAY,QAAZ,KAAAC,EAAqB,CAAC,CAAC,EAC/C,QAASF,GAAiBa,EAAAZ,GAAA,YAAAA,EAAY,UAAZ,KAAAY,EAAuB,CAAC,CAAC,EACnD,eAAgBb,GAAiBc,EAAAb,GAAA,YAAAA,EAAY,iBAAZ,KAAAa,EAA8B,CAAC,CAAC,EACjE,MAAOd,EAAiB,KAAK,KAAK,EAClC,SAAUA,EAAiB,KAAK,QAAQ,CAC1C,CACF,CAEU,QAAQQ,EAAc,CAC9B,QAAQ,MAAM,0BAA2BA,CAAK,CAChD,CAEU,YAAa,CAAC,CAEjB,OAAQ,CACb,IAAMO,EAAS,OAAO,OAAO,OAAO,eAAe,IAAI,CAAC,EAExD,QAAWC,KAAO,OAAO,oBAAoB,IAAI,EAAG,CAClD,IAAMC,EAAS,KAAaD,CAAG,EAC3B,OAAOC,GAAU,aACnBF,EAAOC,CAAG,EAAIhB,EAAiBiB,CAAK,EAExC,CAEA,OAAOF,CACT,CAEO,qCACLG,EACwC,CAvH5C,IAAAhB,EAwHI,KAAK,SAAUA,EAAA,KAAK,UAAL,KAAAA,KAAgB,EAAAH,IAAO,EACtC,IAAMI,EAAQ,KAAK,qBAAqBe,CAAM,EAE9C,OAAO,KAAK,IAAIf,CAAK,EAAE,KACrBE,EAAgB,KAAK,KAAK,EAC1BC,EAAa,KAAK,KAAK,EACvBa,EAAsB,KAAK,SAAUhB,EAAM,MAAO,KAAK,OAAO,EAC7DQ,GACQA,EAAQ,QACb,OAAKC,IACC,KAAK,OACP,QAAQ,MAAM,YAAa,KAAK,UAAUA,CAAK,CAAC,EAE3CA,EACR,CACH,CAEJ,CACF,CACF,EE/HO,IAAMQ,EAAN,cAAwBC,CAAc,CAkC3C,YAAYC,EAAyB,CA9CvC,IAAAC,EA+CI,MAAMD,CAAM,EAhCd,KAAO,gBAAmC,IAAI,gBAiC5C,KAAK,IAAMA,EAAO,IAClB,KAAK,QAAUE,GAAiBD,EAAAD,EAAO,UAAP,KAAAC,EAAkB,CAAC,CAAC,CACtD,CA3BU,YAAYE,EAAmC,CACvD,MAAO,CACL,OAAQ,OACR,QAASC,EAAAC,EAAA,GACJ,KAAK,SADD,CAEP,eAAgB,mBAChB,OAAQ,mBACV,GACA,KAAM,KAAK,UAAUF,CAAK,EAC1B,OAAQ,KAAK,gBAAgB,MAC/B,CACF,CAEO,SAASG,EAAiC,CApCnD,IAAAL,EAqCI,YAAK,iBAAkBA,EAAAK,GAAA,YAAAA,EAAY,kBAAZ,KAAAL,EAA+B,IAAI,gBACnD,MAAM,SAASK,CAAU,CAClC,CAEA,UAAW,CACT,KAAK,gBAAgB,MAAM,EAC3B,MAAM,SAAS,CACjB,CAQA,IAAIH,EAA6C,CAC/C,IAAMI,EAAaC,EAAe,KAAK,IAAK,KAAK,YAAYL,CAAK,CAAC,EACnE,OAAOM,EAAyBF,CAAU,CAC5C,CACF,EZlDAG,EAAAC,EAAc,uBANd", "names": ["index_exports", "__export", "AbstractAgent", "HttpAgent", "convertToLegacyEvents", "defaultApplyEvents", "parseProtoStream", "parseSSEStream", "runHttpRequest", "transformHttpEventStream", "verifyEvents", "__toCommonJS", "import_core", "import_operators", "structuredClone_", "obj", "err", "__spreadValues", "import_fast_json_patch", "import_untruncate_json", "defaultApplyEvents", "args", "input", "events$", "messages", "structuredClone_", "state", "predictState", "emitUpdate", "agentState", "emitNoUpdate", "event", "_a", "messageId", "role", "newMessage", "delta", "lastMessage", "toolCallId", "toolCallName", "parentMessageId", "targetMessage", "lastToolCall", "config", "p", "lastToolCallArguments", "untruncate<PERSON><PERSON>", "__spreadProps", "__spreadValues", "_", "snapshot", "error", "errorMessage", "newMessages", "customEvent", "_exhaustiveCheck", "import_core", "import_rxjs", "import_operators", "verifyEvents", "debug", "source$", "activeMessageId", "activeToolCallId", "runFinished", "runError", "firstEventReceived", "activeSteps", "event", "eventType", "<PERSON><PERSON><PERSON>", "unfinishedSteps", "import_core", "import_rxjs", "import_rxjs", "import_operators", "runHttpRequest", "url", "requestInit", "response", "_a", "headersEvent", "reader", "subscriber", "done", "value", "dataEvent", "error", "import_rxjs", "parseSSEStream", "source$", "jsonSubject", "decoder", "buffer", "event", "text", "events", "processSSEEvent", "err", "eventText", "lines", "dataLines", "line", "jsonStr", "json", "import_rxjs", "proto", "parseProtoStream", "source$", "eventSubject", "buffer", "event", "new<PERSON>uffer", "processBuffer", "err", "error", "totalLength", "message", "errorMessage", "proto", "transformHttpEventStream", "source$", "eventSubject", "bufferSubject", "parserInitialized", "event", "parseProtoStream", "err", "parseSSEStream", "json", "parsedEvent", "import_operators", "import_fast_json_patch", "import_core", "import_zod", "LegacyRuntimeEventTypes", "LegacyRuntimeMetaEventName", "LegacyTextMessageStart", "LegacyTextMessageContent", "LegacyTextMessageEnd", "LegacyActionExecutionStart", "LegacyActionExecutionArgs", "LegacyActionExecutionEnd", "LegacyActionExecutionResult", "LegacyAgentStateMessage", "LegacyMetaEvent", "LegacyRuntimeProtocolEvent", "LegacyTextMessageSchema", "LegacyActionExecutionMessageSchema", "LegacyResultMessageSchema", "import_untruncate_json", "convertToLegacyEvents", "threadId", "runId", "<PERSON><PERSON><PERSON>", "events$", "currentState", "running", "active", "nodeName", "syncedMessages", "predictState", "currentToolCalls", "updateCurrentState", "newState", "event", "startEvent", "LegacyRuntimeEventTypes", "contentEvent", "endEvent", "argsEvent", "currentToolCall", "didUpdateState", "currentPredictState", "s", "currentArgs", "untruncate<PERSON><PERSON>", "__spreadProps", "__spreadValues", "e", "customEvent", "result", "convertMessagesToLegacyFormat", "messages", "_a", "message", "textMessage", "toolCall", "actionExecutionMessage", "actionName", "m", "toolMessage", "import_uuid", "import_operators", "import_rxjs", "import_rxjs", "import_rxjs", "import_core", "transformChunks", "debug", "events$", "text<PERSON><PERSON>age<PERSON><PERSON>s", "toolCallFields", "mode", "closeTextMessage", "event", "closeToolCall", "closePendingEvent", "messageChunkEvent", "textMessageResult", "textMessageStartEvent", "textMessageContentEvent", "toolCallChunkEvent", "toolMessageResult", "toolCallStartEvent", "toolCallArgsEvent", "_exhaustiveCheck", "AbstractAgent", "agentId", "description", "threadId", "initialMessages", "initialState", "debug", "uuidv4", "structuredClone_", "parameters", "_a", "input", "pipeline", "transformChunks", "verifyEvents", "source$", "error", "args", "defaultApplyEvents", "events$", "event", "_b", "_c", "cloned", "key", "value", "config", "convertToLegacyEvents", "HttpAgent", "AbstractAgent", "config", "_a", "structuredClone_", "input", "__spreadProps", "__spreadValues", "parameters", "httpEvents", "runHttpRequest", "transformHttpEventStream", "__reExport", "index_exports"]}