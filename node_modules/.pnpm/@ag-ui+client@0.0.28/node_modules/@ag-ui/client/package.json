{"name": "@ag-ui/client", "author": "<PERSON> <<EMAIL>>", "version": "0.0.28", "private": false, "publishConfig": {"access": "public"}, "main": "./dist/index.js", "module": "./dist/index.mjs", "types": "./dist/index.d.ts", "sideEffects": false, "files": ["dist/**"], "dependencies": {"@types/uuid": "^10.0.0", "fast-json-patch": "^3.1.1", "rxjs": "7.8.1", "untruncate-json": "^0.0.1", "uuid": "^11.1.0", "zod": "^3.22.4", "@ag-ui/core": "0.0.28", "@ag-ui/proto": "0.0.28", "@ag-ui/encoder": "0.0.28"}, "devDependencies": {"@types/jest": "^29.5.14", "@types/node": "^20.11.19", "jest": "^29.7.0", "ts-jest": "^29.1.2", "tsup": "^8.0.2", "typescript": "^5.3.3"}, "scripts": {"build": "tsup", "dev": "tsup --watch", "clean": "rm -rf dist", "typecheck": "tsc --noEmit", "test": "jest", "link:global": "pnpm link --global", "unlink:global": "pnpm unlink --global"}}