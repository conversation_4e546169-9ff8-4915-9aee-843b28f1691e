import { BaseEvent } from '@ag-ui/core';
export { AGUI_MEDIA_TYPE } from '@ag-ui/proto';

interface EventEncoderParams {
    accept?: string;
}
declare class EventEncoder {
    private acceptsProtobuf;
    constructor(params?: EventEncoderParams);
    getContentType(): string;
    encode(event: BaseEvent): string;
    encodeSSE(event: BaseEvent): string;
    encodeBinary(event: BaseEvent): Uint8Array;
    encodeProtobuf(event: BaseEvent): Uint8Array;
    private isProtobufAccepted;
}

export { EventEncoder, type EventEncoderParams };
