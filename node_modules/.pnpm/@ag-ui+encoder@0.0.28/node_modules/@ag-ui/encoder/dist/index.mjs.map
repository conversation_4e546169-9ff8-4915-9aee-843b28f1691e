{"version": 3, "sources": ["../src/encoder.ts", "../src/media-type.ts", "../src/index.ts"], "sourcesContent": ["import { BaseEvent } from \"@ag-ui/core\";\nimport * as proto from \"@ag-ui/proto\";\nimport { preferredMediaTypes } from \"./media-type\";\n\nexport interface EventEncoderParams {\n  accept?: string;\n}\n\nexport class EventEncoder {\n  private acceptsProtobuf: boolean;\n\n  constructor(params?: EventEncoderParams) {\n    this.acceptsProtobuf = params?.accept ? this.isProtobufAccepted(params.accept) : false;\n  }\n\n  getContentType(): string {\n    if (this.acceptsProtobuf) {\n      return proto.AGUI_MEDIA_TYPE;\n    } else {\n      return \"text/event-stream\";\n    }\n  }\n\n  encode(event: BaseEvent): string {\n    return this.encodeSSE(event);\n  }\n\n  encodeSSE(event: BaseEvent): string {\n    return `data: ${JSON.stringify(event)}\\n\\n`;\n  }\n\n  encodeBinary(event: BaseEvent): Uint8Array {\n    if (this.acceptsProtobuf) {\n      return this.encodeProtobuf(event);\n    } else {\n      const sseString = this.encodeSSE(event);\n      // Convert string to Uint8Array using TextEncoder\n      const encoder = new TextEncoder();\n      return encoder.encode(sseString);\n    }\n  }\n\n  encodeProtobuf(event: BaseEvent): Uint8Array {\n    const messageBytes = proto.encode(event);\n    const length = messageBytes.length;\n\n    // Create a buffer for 4 bytes (for the uint32 length) plus the message bytes\n    const buffer = new ArrayBuffer(4 + length);\n    const dataView = new DataView(buffer);\n\n    // Write the length as a uint32\n    // Set the third parameter to `false` for big-endian or `true` for little-endian\n    dataView.setUint32(0, length, false);\n\n    // Create a Uint8Array view and copy in the message bytes after the 4-byte header\n    const result = new Uint8Array(buffer);\n    result.set(messageBytes, 4);\n\n    return result;\n  }\n\n  private isProtobufAccepted(acceptHeader: string): boolean {\n    // Pass the Accept header and an array with your media type\n    const preferred = preferredMediaTypes(acceptHeader, [proto.AGUI_MEDIA_TYPE]);\n\n    // If the returned array includes your media type, it's acceptable\n    return preferred.includes(proto.AGUI_MEDIA_TYPE);\n  }\n}\n", "/**\n * negotiator\n * Copyright(c) 2012 <PERSON>\n * Copyright(c) 2014 <PERSON>\n * Copyright(c) 2014-2015 <PERSON>\n * MIT Licensed\n */\n\n// modified from https://github.com/jshttp/negotiator/blob/master/lib/mediaType.js\n\n/**\n * Module exports.\n * @public\n */\n\nexport function preferredMediaTypes(accept?: string, provided?: string[]): string[] {\n  // RFC 2616 sec 14.2: no header = */*\n  const accepts = parseAccept(accept === undefined ? \"*/*\" : accept || \"\");\n\n  if (!provided) {\n    // sorted list of all types\n    return accepts\n      .filter((spec): spec is MediaType => spec.q > 0)\n      .sort((a, b) => {\n        return b.q - a.q || b.i - a.i || 0;\n      })\n      .map(getFullType);\n  }\n\n  const priorities = provided.map(function getPriority(type: string, index: number) {\n    return getMediaTypePriority(type, accepts, index);\n  });\n\n  // sorted list of accepted types\n  return priorities\n    .filter((spec): spec is Priority => spec.q > 0)\n    .sort(compareSpecs)\n    .map(function getType(priority: Priority) {\n      return provided[priorities.indexOf(priority)];\n    });\n}\n\n/**\n * Module variables.\n * @private\n */\n\nconst simpleMediaTypeRegExp = /^\\s*([^\\s\\/;]+)\\/([^;\\s]+)\\s*(?:;(.*))?$/;\n\n/**\n * Media type interface\n * @private\n */\ninterface MediaType {\n  type: string;\n  subtype: string;\n  params: Record<string, string>;\n  q: number;\n  i: number;\n}\n\n/**\n * Priority interface\n * @private\n */\ninterface Priority {\n  o: number;\n  q: number;\n  s: number;\n  i?: number;\n}\n\n/**\n * Parse the Accept header.\n * @private\n */\nfunction parseAccept(accept: string): MediaType[] {\n  const accepts = splitMediaTypes(accept);\n  const result: MediaType[] = [];\n\n  for (let i = 0, j = 0; i < accepts.length; i++) {\n    const mediaType = parseMediaType(accepts[i].trim(), i);\n\n    if (mediaType) {\n      result[j++] = mediaType;\n    }\n  }\n\n  return result;\n}\n\n/**\n * Parse a media type from the Accept header.\n * @private\n */\nfunction parseMediaType(str: string, i: number): MediaType | null {\n  const match = simpleMediaTypeRegExp.exec(str);\n  if (!match) return null;\n\n  const params: Record<string, string> = Object.create(null);\n  let q = 1;\n  const subtype = match[2];\n  const type = match[1];\n\n  if (match[3]) {\n    const kvps = splitParameters(match[3]).map(splitKeyValuePair);\n\n    for (let j = 0; j < kvps.length; j++) {\n      const pair = kvps[j];\n      const key = pair[0].toLowerCase();\n      const val = pair[1];\n\n      // get the value, unwrapping quotes\n      const value = val && val[0] === '\"' && val[val.length - 1] === '\"' ? val.slice(1, -1) : val;\n\n      if (key === \"q\") {\n        q = parseFloat(value);\n        break;\n      }\n\n      // store parameter\n      params[key] = value;\n    }\n  }\n\n  return {\n    type: type,\n    subtype: subtype,\n    params: params,\n    q: q,\n    i: i,\n  };\n}\n\n/**\n * Get the priority of a media type.\n * @private\n */\nfunction getMediaTypePriority(type: string, accepted: MediaType[], index: number): Priority {\n  const priority: Priority = { o: -1, q: 0, s: 0 };\n\n  for (let i = 0; i < accepted.length; i++) {\n    const spec = specify(type, accepted[i], index);\n\n    if (spec && (priority.s - spec.s || priority.q - spec.q || priority.o - spec.o) < 0) {\n      priority.o = spec.o;\n      priority.q = spec.q;\n      priority.s = spec.s;\n      priority.i = spec.i;\n    }\n  }\n\n  return priority;\n}\n\n/**\n * Get the specificity of the media type.\n * @private\n */\nfunction specify(type: string, spec: MediaType, index: number): Priority | null {\n  const p = parseMediaType(type, 0);\n  let s = 0;\n\n  if (!p) {\n    return null;\n  }\n\n  if (spec.type.toLowerCase() == p.type.toLowerCase()) {\n    s |= 4;\n  } else if (spec.type != \"*\") {\n    return null;\n  }\n\n  if (spec.subtype.toLowerCase() == p.subtype.toLowerCase()) {\n    s |= 2;\n  } else if (spec.subtype != \"*\") {\n    return null;\n  }\n\n  const keys = Object.keys(spec.params);\n  if (keys.length > 0) {\n    if (\n      keys.every(function (k) {\n        return (\n          spec.params[k] == \"*\" ||\n          (spec.params[k] || \"\").toLowerCase() == (p.params[k] || \"\").toLowerCase()\n        );\n      })\n    ) {\n      s |= 1;\n    } else {\n      return null;\n    }\n  }\n\n  return {\n    i: index,\n    o: spec.i,\n    q: spec.q,\n    s: s,\n  };\n}\n\n/**\n * Compare two specs.\n * @private\n */\nfunction compareSpecs(a: Priority, b: Priority): number {\n  return b.q - a.q || b.s - a.s || (a.o || 0) - (b.o || 0) || (a.i || 0) - (b.i || 0) || 0;\n}\n\n/**\n * Get full type string.\n * @private\n */\nfunction getFullType(spec: MediaType): string {\n  return spec.type + \"/\" + spec.subtype;\n}\n\n/**\n * Check if a spec has any quality.\n * @private\n */\nfunction isQuality(spec: Priority | MediaType): boolean {\n  return spec.q > 0;\n}\n\n/**\n * Count the number of quotes in a string.\n * @private\n */\nfunction quoteCount(string: string): number {\n  let count = 0;\n  let index = 0;\n\n  while ((index = string.indexOf('\"', index)) !== -1) {\n    count++;\n    index++;\n  }\n\n  return count;\n}\n\n/**\n * Split a key value pair.\n * @private\n */\nfunction splitKeyValuePair(str: string): [string, string] {\n  const index = str.indexOf(\"=\");\n  let key: string;\n  let val: string = \"\";\n\n  if (index === -1) {\n    key = str;\n  } else {\n    key = str.slice(0, index);\n    val = str.slice(index + 1);\n  }\n\n  return [key, val];\n}\n\n/**\n * Split an Accept header into media types.\n * @private\n */\nfunction splitMediaTypes(accept: string): string[] {\n  const accepts = accept.split(\",\");\n  const result: string[] = [accepts[0]];\n\n  for (let i = 1, j = 0; i < accepts.length; i++) {\n    if (quoteCount(result[j]) % 2 == 0) {\n      result[++j] = accepts[i];\n    } else {\n      result[j] += \",\" + accepts[i];\n    }\n  }\n\n  // trim result\n  return result;\n}\n\n/**\n * Split a string of parameters.\n * @private\n */\nfunction splitParameters(str: string): string[] {\n  const parameters = str.split(\";\");\n  const result: string[] = [parameters[0]];\n\n  for (let i = 1, j = 0; i < parameters.length; i++) {\n    if (quoteCount(result[j]) % 2 == 0) {\n      result[++j] = parameters[i];\n    } else {\n      result[j] += \";\" + parameters[i];\n    }\n  }\n\n  // trim parameters\n  for (let i = 0; i < result.length; i++) {\n    result[i] = result[i].trim();\n  }\n\n  return result;\n}\n", "export * from \"./encoder\";\nexport { AGUI_MEDIA_TYPE } from \"@ag-ui/proto\";\n"], "mappings": ";AACA,YAAY,WAAW;;;ACchB,SAAS,oBAAoB,QAAiB,UAA+B;AAElF,QAAM,UAAU,YAAY,WAAW,SAAY,QAAQ,UAAU,EAAE;AAEvE,MAAI,CAAC,UAAU;AAEb,WAAO,QACJ,OAAO,CAAC,SAA4B,KAAK,IAAI,CAAC,EAC9C,KAAK,CAAC,GAAG,MAAM;AACd,aAAO,EAAE,IAAI,EAAE,KAAK,EAAE,IAAI,EAAE,KAAK;AAAA,IACnC,CAAC,EACA,IAAI,WAAW;AAAA,EACpB;AAEA,QAAM,aAAa,SAAS,IAAI,SAAS,YAAY,MAAc,OAAe;AAChF,WAAO,qBAAqB,MAAM,SAAS,KAAK;AAAA,EAClD,CAAC;AAGD,SAAO,WACJ,OAAO,CAAC,SAA2B,KAAK,IAAI,CAAC,EAC7C,KAAK,YAAY,EACjB,IAAI,SAAS,QAAQ,UAAoB;AACxC,WAAO,SAAS,WAAW,QAAQ,QAAQ,CAAC;AAAA,EAC9C,CAAC;AACL;AAOA,IAAM,wBAAwB;AA6B9B,SAAS,YAAY,QAA6B;AAChD,QAAM,UAAU,gBAAgB,MAAM;AACtC,QAAM,SAAsB,CAAC;AAE7B,WAAS,IAAI,GAAG,IAAI,GAAG,IAAI,QAAQ,QAAQ,KAAK;AAC9C,UAAM,YAAY,eAAe,QAAQ,CAAC,EAAE,KAAK,GAAG,CAAC;AAErD,QAAI,WAAW;AACb,aAAO,GAAG,IAAI;AAAA,IAChB;AAAA,EACF;AAEA,SAAO;AACT;AAMA,SAAS,eAAe,KAAa,GAA6B;AAChE,QAAM,QAAQ,sBAAsB,KAAK,GAAG;AAC5C,MAAI,CAAC,MAAO,QAAO;AAEnB,QAAM,SAAiC,uBAAO,OAAO,IAAI;AACzD,MAAI,IAAI;AACR,QAAM,UAAU,MAAM,CAAC;AACvB,QAAM,OAAO,MAAM,CAAC;AAEpB,MAAI,MAAM,CAAC,GAAG;AACZ,UAAM,OAAO,gBAAgB,MAAM,CAAC,CAAC,EAAE,IAAI,iBAAiB;AAE5D,aAAS,IAAI,GAAG,IAAI,KAAK,QAAQ,KAAK;AACpC,YAAM,OAAO,KAAK,CAAC;AACnB,YAAM,MAAM,KAAK,CAAC,EAAE,YAAY;AAChC,YAAM,MAAM,KAAK,CAAC;AAGlB,YAAM,QAAQ,OAAO,IAAI,CAAC,MAAM,OAAO,IAAI,IAAI,SAAS,CAAC,MAAM,MAAM,IAAI,MAAM,GAAG,EAAE,IAAI;AAExF,UAAI,QAAQ,KAAK;AACf,YAAI,WAAW,KAAK;AACpB;AAAA,MACF;AAGA,aAAO,GAAG,IAAI;AAAA,IAChB;AAAA,EACF;AAEA,SAAO;AAAA,IACL;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF;AACF;AAMA,SAAS,qBAAqB,MAAc,UAAuB,OAAyB;AAC1F,QAAM,WAAqB,EAAE,GAAG,IAAI,GAAG,GAAG,GAAG,EAAE;AAE/C,WAAS,IAAI,GAAG,IAAI,SAAS,QAAQ,KAAK;AACxC,UAAM,OAAO,QAAQ,MAAM,SAAS,CAAC,GAAG,KAAK;AAE7C,QAAI,SAAS,SAAS,IAAI,KAAK,KAAK,SAAS,IAAI,KAAK,KAAK,SAAS,IAAI,KAAK,KAAK,GAAG;AACnF,eAAS,IAAI,KAAK;AAClB,eAAS,IAAI,KAAK;AAClB,eAAS,IAAI,KAAK;AAClB,eAAS,IAAI,KAAK;AAAA,IACpB;AAAA,EACF;AAEA,SAAO;AACT;AAMA,SAAS,QAAQ,MAAc,MAAiB,OAAgC;AAC9E,QAAM,IAAI,eAAe,MAAM,CAAC;AAChC,MAAI,IAAI;AAER,MAAI,CAAC,GAAG;AACN,WAAO;AAAA,EACT;AAEA,MAAI,KAAK,KAAK,YAAY,KAAK,EAAE,KAAK,YAAY,GAAG;AACnD,SAAK;AAAA,EACP,WAAW,KAAK,QAAQ,KAAK;AAC3B,WAAO;AAAA,EACT;AAEA,MAAI,KAAK,QAAQ,YAAY,KAAK,EAAE,QAAQ,YAAY,GAAG;AACzD,SAAK;AAAA,EACP,WAAW,KAAK,WAAW,KAAK;AAC9B,WAAO;AAAA,EACT;AAEA,QAAM,OAAO,OAAO,KAAK,KAAK,MAAM;AACpC,MAAI,KAAK,SAAS,GAAG;AACnB,QACE,KAAK,MAAM,SAAU,GAAG;AACtB,aACE,KAAK,OAAO,CAAC,KAAK,QACjB,KAAK,OAAO,CAAC,KAAK,IAAI,YAAY,MAAM,EAAE,OAAO,CAAC,KAAK,IAAI,YAAY;AAAA,IAE5E,CAAC,GACD;AACA,WAAK;AAAA,IACP,OAAO;AACL,aAAO;AAAA,IACT;AAAA,EACF;AAEA,SAAO;AAAA,IACL,GAAG;AAAA,IACH,GAAG,KAAK;AAAA,IACR,GAAG,KAAK;AAAA,IACR;AAAA,EACF;AACF;AAMA,SAAS,aAAa,GAAa,GAAqB;AACtD,SAAO,EAAE,IAAI,EAAE,KAAK,EAAE,IAAI,EAAE,MAAM,EAAE,KAAK,MAAM,EAAE,KAAK,OAAO,EAAE,KAAK,MAAM,EAAE,KAAK,MAAM;AACzF;AAMA,SAAS,YAAY,MAAyB;AAC5C,SAAO,KAAK,OAAO,MAAM,KAAK;AAChC;AAcA,SAAS,WAAW,QAAwB;AAC1C,MAAI,QAAQ;AACZ,MAAI,QAAQ;AAEZ,UAAQ,QAAQ,OAAO,QAAQ,KAAK,KAAK,OAAO,IAAI;AAClD;AACA;AAAA,EACF;AAEA,SAAO;AACT;AAMA,SAAS,kBAAkB,KAA+B;AACxD,QAAM,QAAQ,IAAI,QAAQ,GAAG;AAC7B,MAAI;AACJ,MAAI,MAAc;AAElB,MAAI,UAAU,IAAI;AAChB,UAAM;AAAA,EACR,OAAO;AACL,UAAM,IAAI,MAAM,GAAG,KAAK;AACxB,UAAM,IAAI,MAAM,QAAQ,CAAC;AAAA,EAC3B;AAEA,SAAO,CAAC,KAAK,GAAG;AAClB;AAMA,SAAS,gBAAgB,QAA0B;AACjD,QAAM,UAAU,OAAO,MAAM,GAAG;AAChC,QAAM,SAAmB,CAAC,QAAQ,CAAC,CAAC;AAEpC,WAAS,IAAI,GAAG,IAAI,GAAG,IAAI,QAAQ,QAAQ,KAAK;AAC9C,QAAI,WAAW,OAAO,CAAC,CAAC,IAAI,KAAK,GAAG;AAClC,aAAO,EAAE,CAAC,IAAI,QAAQ,CAAC;AAAA,IACzB,OAAO;AACL,aAAO,CAAC,KAAK,MAAM,QAAQ,CAAC;AAAA,IAC9B;AAAA,EACF;AAGA,SAAO;AACT;AAMA,SAAS,gBAAgB,KAAuB;AAC9C,QAAM,aAAa,IAAI,MAAM,GAAG;AAChC,QAAM,SAAmB,CAAC,WAAW,CAAC,CAAC;AAEvC,WAAS,IAAI,GAAG,IAAI,GAAG,IAAI,WAAW,QAAQ,KAAK;AACjD,QAAI,WAAW,OAAO,CAAC,CAAC,IAAI,KAAK,GAAG;AAClC,aAAO,EAAE,CAAC,IAAI,WAAW,CAAC;AAAA,IAC5B,OAAO;AACL,aAAO,CAAC,KAAK,MAAM,WAAW,CAAC;AAAA,IACjC;AAAA,EACF;AAGA,WAAS,IAAI,GAAG,IAAI,OAAO,QAAQ,KAAK;AACtC,WAAO,CAAC,IAAI,OAAO,CAAC,EAAE,KAAK;AAAA,EAC7B;AAEA,SAAO;AACT;;;ADxSO,IAAM,eAAN,MAAmB;AAAA,EAGxB,YAAY,QAA6B;AACvC,SAAK,mBAAkB,iCAAQ,UAAS,KAAK,mBAAmB,OAAO,MAAM,IAAI;AAAA,EACnF;AAAA,EAEA,iBAAyB;AACvB,QAAI,KAAK,iBAAiB;AACxB,aAAa;AAAA,IACf,OAAO;AACL,aAAO;AAAA,IACT;AAAA,EACF;AAAA,EAEA,OAAO,OAA0B;AAC/B,WAAO,KAAK,UAAU,KAAK;AAAA,EAC7B;AAAA,EAEA,UAAU,OAA0B;AAClC,WAAO,SAAS,KAAK,UAAU,KAAK,CAAC;AAAA;AAAA;AAAA,EACvC;AAAA,EAEA,aAAa,OAA8B;AACzC,QAAI,KAAK,iBAAiB;AACxB,aAAO,KAAK,eAAe,KAAK;AAAA,IAClC,OAAO;AACL,YAAM,YAAY,KAAK,UAAU,KAAK;AAEtC,YAAM,UAAU,IAAI,YAAY;AAChC,aAAO,QAAQ,OAAO,SAAS;AAAA,IACjC;AAAA,EACF;AAAA,EAEA,eAAe,OAA8B;AAC3C,UAAM,eAAqB,aAAO,KAAK;AACvC,UAAM,SAAS,aAAa;AAG5B,UAAM,SAAS,IAAI,YAAY,IAAI,MAAM;AACzC,UAAM,WAAW,IAAI,SAAS,MAAM;AAIpC,aAAS,UAAU,GAAG,QAAQ,KAAK;AAGnC,UAAM,SAAS,IAAI,WAAW,MAAM;AACpC,WAAO,IAAI,cAAc,CAAC;AAE1B,WAAO;AAAA,EACT;AAAA,EAEQ,mBAAmB,cAA+B;AAExD,UAAM,YAAY,oBAAoB,cAAc,CAAO,qBAAe,CAAC;AAG3E,WAAO,UAAU,SAAe,qBAAe;AAAA,EACjD;AACF;;;AEnEA,SAAS,mBAAAA,wBAAuB;", "names": ["AGUI_MEDIA_TYPE"]}