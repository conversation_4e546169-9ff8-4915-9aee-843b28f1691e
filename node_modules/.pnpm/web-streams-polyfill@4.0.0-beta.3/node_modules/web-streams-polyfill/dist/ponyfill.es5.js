/**
 * @license
 * web-streams-polyfill v4.0.0-beta.3
 * Copyright 2021 <PERSON><PERSON>, <PERSON><PERSON><PERSON> and other contributors.
 * This code is released under the MIT license.
 * SPDX-License-Identifier: MIT
 */
!function(e,r){"object"==typeof exports&&"undefined"!=typeof module?r(exports):"function"==typeof define&&define.amd?define(["exports"],r):r((e="undefined"!=typeof globalThis?globalThis:e||self).WebStreamsPolyfill={})}(this,(function(e){"use strict";var r="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?Symbol:function(e){return"Symbol(".concat(e,")")};function t(){}function n(e){return"object"==typeof e&&null!==e||"function"==typeof e}var o=t;function a(e,r){try{Object.defineProperty(e,"name",{value:r,configurable:!0})}catch(e){}}var i=Promise,l=Promise.prototype.then,u=Promise.resolve.bind(i),s=Promise.reject.bind(i);function c(e){return new i(e)}function d(e){return u(e)}function f(e){return s(e)}function b(e,r,t){return l.call(e,r,t)}function p(e,r,t){b(b(e,r,t),void 0,o)}function h(e,r){p(e,r)}function _(e,r){p(e,void 0,r)}function y(e,r,t){return b(e,r,t)}function m(e){b(e,void 0,o)}var v=function(e){if("function"==typeof queueMicrotask)v=queueMicrotask;else{var r=d(void 0);v=function(e){return b(r,e)}}return v(e)};function g(e,r,t){if("function"!=typeof e)throw new TypeError("Argument is not a function");return Function.prototype.apply.call(e,r,t)}function w(e,r,t){try{return d(g(e,r,t))}catch(e){return f(e)}}var S=function(){function e(){this._cursor=0,this._size=0,this._front={_elements:[],_next:void 0},this._back=this._front,this._cursor=0,this._size=0}return Object.defineProperty(e.prototype,"length",{get:function(){return this._size},enumerable:!1,configurable:!0}),e.prototype.push=function(e){var r=this._back,t=r;16383===r._elements.length&&(t={_elements:[],_next:void 0}),r._elements.push(e),t!==r&&(this._back=t,r._next=t),++this._size},e.prototype.shift=function(){var e=this._front,r=e,t=this._cursor,n=t+1,o=e._elements,a=o[t];return 16384===n&&(r=e._next,n=0),--this._size,this._cursor=n,e!==r&&(this._front=r),o[t]=void 0,a},e.prototype.forEach=function(e){for(var r=this._cursor,t=this._front,n=t._elements;!(r===n.length&&void 0===t._next||r===n.length&&(r=0,0===(n=(t=t._next)._elements).length));)e(n[r]),++r},e.prototype.peek=function(){var e=this._front,r=this._cursor;return e._elements[r]},e}(),R=r("[[AbortSteps]]"),T=r("[[ErrorSteps]]"),q=r("[[CancelSteps]]"),P=r("[[PullSteps]]"),C=r("[[ReleaseSteps]]");function E(e,r){e._ownerReadableStream=r,r._reader=e,"readable"===r._state?j(e):"closed"===r._state?function(e){j(e),z(e)}(e):B(e,r._storedError)}function W(e,r){return it(e._ownerReadableStream,r)}function O(e){var r=e._ownerReadableStream;"readable"===r._state?A(e,new TypeError("Reader was released and can no longer be used to monitor the stream's closedness")):function(e,r){B(e,r)}(e,new TypeError("Reader was released and can no longer be used to monitor the stream's closedness")),r._readableStreamController[C](),r._reader=void 0,e._ownerReadableStream=void 0}function k(e){return new TypeError("Cannot "+e+" a stream using a released reader")}function j(e){e._closedPromise=c((function(r,t){e._closedPromise_resolve=r,e._closedPromise_reject=t}))}function B(e,r){j(e),A(e,r)}function A(e,r){void 0!==e._closedPromise_reject&&(m(e._closedPromise),e._closedPromise_reject(r),e._closedPromise_resolve=void 0,e._closedPromise_reject=void 0)}function z(e){void 0!==e._closedPromise_resolve&&(e._closedPromise_resolve(void 0),e._closedPromise_resolve=void 0,e._closedPromise_reject=void 0)}var L=Number.isFinite||function(e){return"number"==typeof e&&isFinite(e)},F=Math.trunc||function(e){return e<0?Math.ceil(e):Math.floor(e)};function I(e,r){if(void 0!==e&&("object"!=typeof(t=e)&&"function"!=typeof t))throw new TypeError("".concat(r," is not an object."));var t}function D(e,r){if("function"!=typeof e)throw new TypeError("".concat(r," is not a function."))}function M(e,r){if(!function(e){return"object"==typeof e&&null!==e||"function"==typeof e}(e))throw new TypeError("".concat(r," is not an object."))}function Q(e,r,t){if(void 0===e)throw new TypeError("Parameter ".concat(r," is required in '").concat(t,"'."))}function Y(e,r,t){if(void 0===e)throw new TypeError("".concat(r," is required in '").concat(t,"'."))}function N(e){return Number(e)}function x(e){return 0===e?0:e}function H(e,r){var t=Number.MAX_SAFE_INTEGER,n=Number(e);if(n=x(n),!L(n))throw new TypeError("".concat(r," is not a finite number"));if((n=function(e){return x(F(e))}(n))<0||n>t)throw new TypeError("".concat(r," is outside the accepted range of ").concat(0," to ").concat(t,", inclusive"));return L(n)&&0!==n?n:0}function V(e){if(!n(e))return!1;if("function"!=typeof e.getReader)return!1;try{return"boolean"==typeof e.locked}catch(e){return!1}}function U(e){if(!n(e))return!1;if("function"!=typeof e.getWriter)return!1;try{return"boolean"==typeof e.locked}catch(e){return!1}}function G(e,r){if(!ot(e))throw new TypeError("".concat(r," is not a ReadableStream."))}function X(e,r){e._reader._readRequests.push(r)}function J(e,r,t){var n=e._reader._readRequests.shift();t?n._closeSteps():n._chunkSteps(r)}function K(e){return e._reader._readRequests.length}function Z(e){var r=e._reader;return void 0!==r&&!!ee(r)}var $=function(){function ReadableStreamDefaultReader(e){if(Q(e,1,"ReadableStreamDefaultReader"),G(e,"First parameter"),at(e))throw new TypeError("This stream has already been locked for exclusive reading by another reader");E(this,e),this._readRequests=new S}return Object.defineProperty(ReadableStreamDefaultReader.prototype,"closed",{get:function(){return ee(this)?this._closedPromise:f(te("closed"))},enumerable:!1,configurable:!0}),ReadableStreamDefaultReader.prototype.cancel=function(e){return void 0===e&&(e=void 0),ee(this)?void 0===this._ownerReadableStream?f(k("cancel")):W(this,e):f(te("cancel"))},ReadableStreamDefaultReader.prototype.read=function(){if(!ee(this))return f(te("read"));if(void 0===this._ownerReadableStream)return f(k("read from"));var e,r,t=c((function(t,n){e=t,r=n}));return function(e,r){var t=e._ownerReadableStream;t._disturbed=!0,"closed"===t._state?r._closeSteps():"errored"===t._state?r._errorSteps(t._storedError):t._readableStreamController[P](r)}(this,{_chunkSteps:function(r){return e({value:r,done:!1})},_closeSteps:function(){return e({value:void 0,done:!0})},_errorSteps:function(e){return r(e)}}),t},ReadableStreamDefaultReader.prototype.releaseLock=function(){if(!ee(this))throw te("releaseLock");void 0!==this._ownerReadableStream&&function(e){O(e);var r=new TypeError("Reader was released");re(e,r)}(this)},ReadableStreamDefaultReader}();function ee(e){return!!n(e)&&(!!Object.prototype.hasOwnProperty.call(e,"_readRequests")&&e instanceof $)}function re(e,r){var t=e._readRequests;e._readRequests=new S,t.forEach((function(e){e._errorSteps(r)}))}function te(e){return new TypeError("ReadableStreamDefaultReader.prototype.".concat(e," can only be used on a ReadableStreamDefaultReader"))}Object.defineProperties($.prototype,{cancel:{enumerable:!0},read:{enumerable:!0},releaseLock:{enumerable:!0},closed:{enumerable:!0}}),a($.prototype.cancel,"cancel"),a($.prototype.read,"read"),a($.prototype.releaseLock,"releaseLock"),"symbol"==typeof r.toStringTag&&Object.defineProperty($.prototype,r.toStringTag,{value:"ReadableStreamDefaultReader",configurable:!0});var ne=function(){function e(e,r){this._ongoingPromise=void 0,this._isFinished=!1,this._reader=e,this._preventCancel=r}return e.prototype.next=function(){var e=this,r=function(){return e._nextSteps()};return this._ongoingPromise=this._ongoingPromise?y(this._ongoingPromise,r,r):r(),this._ongoingPromise},e.prototype.return=function(e){var r=this,t=function(){return r._returnSteps(e)};return this._ongoingPromise?y(this._ongoingPromise,t,t):t()},e.prototype._nextSteps=function(){var e=this;if(this._isFinished)return Promise.resolve({value:void 0,done:!0});var r=this._reader;return void 0===r?f(k("iterate")):b(r.read(),(function(r){var t;return e._ongoingPromise=void 0,r.done&&(e._isFinished=!0,null===(t=e._reader)||void 0===t||t.releaseLock(),e._reader=void 0),r}),(function(r){var t;throw e._ongoingPromise=void 0,e._isFinished=!0,null===(t=e._reader)||void 0===t||t.releaseLock(),e._reader=void 0,r}))},e.prototype._returnSteps=function(e){if(this._isFinished)return Promise.resolve({value:e,done:!0});this._isFinished=!0;var r=this._reader;if(void 0===r)return f(k("finish iterating"));if(this._reader=void 0,!this._preventCancel){var t=r.cancel(e);return r.releaseLock(),y(t,(function(){return{value:e,done:!0}}))}return r.releaseLock(),d({value:e,done:!0})},e}(),oe={next:function(){return ae(this)?this._asyncIteratorImpl.next():f(ie("next"))},return:function(e){return ae(this)?this._asyncIteratorImpl.return(e):f(ie("return"))}};function ae(e){if(!n(e))return!1;if(!Object.prototype.hasOwnProperty.call(e,"_asyncIteratorImpl"))return!1;try{return e._asyncIteratorImpl instanceof ne}catch(e){return!1}}function ie(e){return new TypeError("ReadableStreamAsyncIterator.".concat(e," can only be used on a ReadableSteamAsyncIterator"))}"symbol"==typeof r.asyncIterator&&Object.defineProperty(oe,r.asyncIterator,{value:function(){return this},writable:!0,configurable:!0});var le=Number.isNaN||function(e){return e!=e};function ue(e,r,t,n,o){new Uint8Array(e).set(new Uint8Array(t,n,o),r)}function se(e){var r=function(e,r,t){if(e.slice)return e.slice(r,t);var n=t-r,o=new ArrayBuffer(n);return ue(o,0,e,r,n),o}(e.buffer,e.byteOffset,e.byteOffset+e.byteLength);return new Uint8Array(r)}function ce(e){var r=e._queue.shift();return e._queueTotalSize-=r.size,e._queueTotalSize<0&&(e._queueTotalSize=0),r.value}function de(e,r,t){if("number"!=typeof(n=t)||le(n)||n<0||t===1/0)throw new RangeError("Size must be a finite, non-NaN, non-negative number.");var n;e._queue.push({value:r,size:t}),e._queueTotalSize+=t}function fe(e){e._queue=new S,e._queueTotalSize=0}var be=function(){function ReadableStreamBYOBRequest(){throw new TypeError("Illegal constructor")}return Object.defineProperty(ReadableStreamBYOBRequest.prototype,"view",{get:function(){if(!_e(this))throw Le("view");return this._view},enumerable:!1,configurable:!0}),ReadableStreamBYOBRequest.prototype.respond=function(e){if(!_e(this))throw Le("respond");if(Q(e,1,"respond"),e=H(e,"First parameter"),void 0===this._associatedReadableByteStreamController)throw new TypeError("This BYOB request has been invalidated");this._view.buffer,function(e,r){var t=e._pendingPullIntos.peek();if("closed"===e._controlledReadableByteStream._state){if(0!==r)throw new TypeError("bytesWritten must be 0 when calling respond() on a closed stream")}else{if(0===r)throw new TypeError("bytesWritten must be greater than 0 when calling respond() on a readable stream");if(t.bytesFilled+r>t.byteLength)throw new RangeError("bytesWritten out of range")}t.buffer=t.buffer,We(e,r)}(this._associatedReadableByteStreamController,e)},ReadableStreamBYOBRequest.prototype.respondWithNewView=function(e){if(!_e(this))throw Le("respondWithNewView");if(Q(e,1,"respondWithNewView"),!ArrayBuffer.isView(e))throw new TypeError("You can only respond with array buffer views");if(void 0===this._associatedReadableByteStreamController)throw new TypeError("This BYOB request has been invalidated");e.buffer,function(e,r){var t=e._pendingPullIntos.peek();if("closed"===e._controlledReadableByteStream._state){if(0!==r.byteLength)throw new TypeError("The view's length must be 0 when calling respondWithNewView() on a closed stream")}else if(0===r.byteLength)throw new TypeError("The view's length must be greater than 0 when calling respondWithNewView() on a readable stream");if(t.byteOffset+t.bytesFilled!==r.byteOffset)throw new RangeError("The region specified by view does not match byobRequest");if(t.bufferByteLength!==r.buffer.byteLength)throw new RangeError("The buffer of view has different capacity than byobRequest");if(t.bytesFilled+r.byteLength>t.byteLength)throw new RangeError("The region specified by view is larger than byobRequest");var n=r.byteLength;t.buffer=r.buffer,We(e,n)}(this._associatedReadableByteStreamController,e)},ReadableStreamBYOBRequest}();Object.defineProperties(be.prototype,{respond:{enumerable:!0},respondWithNewView:{enumerable:!0},view:{enumerable:!0}}),a(be.prototype.respond,"respond"),a(be.prototype.respondWithNewView,"respondWithNewView"),"symbol"==typeof r.toStringTag&&Object.defineProperty(be.prototype,r.toStringTag,{value:"ReadableStreamBYOBRequest",configurable:!0});var pe=function(){function ReadableByteStreamController(){throw new TypeError("Illegal constructor")}return Object.defineProperty(ReadableByteStreamController.prototype,"byobRequest",{get:function(){if(!he(this))throw Fe("byobRequest");return function(e){if(null===e._byobRequest&&e._pendingPullIntos.length>0){var r=e._pendingPullIntos.peek(),t=new Uint8Array(r.buffer,r.byteOffset+r.bytesFilled,r.byteLength-r.bytesFilled),n=Object.create(be.prototype);!function(e,r,t){e._associatedReadableByteStreamController=r,e._view=t}(n,e,t),e._byobRequest=n}return e._byobRequest}(this)},enumerable:!1,configurable:!0}),Object.defineProperty(ReadableByteStreamController.prototype,"desiredSize",{get:function(){if(!he(this))throw Fe("desiredSize");return Ae(this)},enumerable:!1,configurable:!0}),ReadableByteStreamController.prototype.close=function(){if(!he(this))throw Fe("close");if(this._closeRequested)throw new TypeError("The stream has already been closed; do not close it again!");var e=this._controlledReadableByteStream._state;if("readable"!==e)throw new TypeError("The stream (in ".concat(e," state) is not in the readable state and cannot be closed"));!function(e){var r=e._controlledReadableByteStream;if(e._closeRequested||"readable"!==r._state)return;if(e._queueTotalSize>0)return void(e._closeRequested=!0);if(e._pendingPullIntos.length>0){if(e._pendingPullIntos.peek().bytesFilled>0){var t=new TypeError("Insufficient bytes to fill elements in the given buffer");throw je(e,t),t}}ke(e),lt(r)}(this)},ReadableByteStreamController.prototype.enqueue=function(e){if(!he(this))throw Fe("enqueue");if(Q(e,1,"enqueue"),!ArrayBuffer.isView(e))throw new TypeError("chunk must be an array buffer view");if(0===e.byteLength)throw new TypeError("chunk must have non-zero byteLength");if(0===e.buffer.byteLength)throw new TypeError("chunk's buffer must have non-zero byteLength");if(this._closeRequested)throw new TypeError("stream is closed or draining");var r=this._controlledReadableByteStream._state;if("readable"!==r)throw new TypeError("The stream (in ".concat(r," state) is not in the readable state and cannot be enqueued to"));!function(e,r){var t=e._controlledReadableByteStream;if(e._closeRequested||"readable"!==t._state)return;var n=r.buffer,o=r.byteOffset,a=r.byteLength,i=n;if(e._pendingPullIntos.length>0){var l=e._pendingPullIntos.peek();l.buffer,0,Ce(e),l.buffer=l.buffer,"none"===l.readerType&&Re(e,l)}if(Z(t)){if(function(e){var r=e._controlledReadableByteStream._reader;for(;r._readRequests.length>0;){if(0===e._queueTotalSize)return;Be(e,r._readRequests.shift())}}(e),0===K(t))we(e,i,o,a);else e._pendingPullIntos.length>0&&Oe(e),J(t,new Uint8Array(i,o,a),!1)}else Me(t)?(we(e,i,o,a),Ee(e)):we(e,i,o,a);ye(e)}(this,e)},ReadableByteStreamController.prototype.error=function(e){if(void 0===e&&(e=void 0),!he(this))throw Fe("error");je(this,e)},ReadableByteStreamController.prototype[q]=function(e){me(this),fe(this);var r=this._cancelAlgorithm(e);return ke(this),r},ReadableByteStreamController.prototype[P]=function(e){var r=this._controlledReadableByteStream;if(this._queueTotalSize>0)Be(this,e);else{var t=this._autoAllocateChunkSize;if(void 0!==t){var n=void 0;try{n=new ArrayBuffer(t)}catch(r){return void e._errorSteps(r)}var o={buffer:n,bufferByteLength:t,byteOffset:0,byteLength:t,bytesFilled:0,elementSize:1,viewConstructor:Uint8Array,readerType:"default"};this._pendingPullIntos.push(o)}X(r,e),ye(this)}},ReadableByteStreamController.prototype[C]=function(){if(this._pendingPullIntos.length>0){var e=this._pendingPullIntos.peek();e.readerType="none",this._pendingPullIntos=new S,this._pendingPullIntos.push(e)}},ReadableByteStreamController}();function he(e){return!!n(e)&&(!!Object.prototype.hasOwnProperty.call(e,"_controlledReadableByteStream")&&e instanceof pe)}function _e(e){return!!n(e)&&(!!Object.prototype.hasOwnProperty.call(e,"_associatedReadableByteStreamController")&&e instanceof be)}function ye(e){var r=function(e){var r=e._controlledReadableByteStream;if("readable"!==r._state)return!1;if(e._closeRequested)return!1;if(!e._started)return!1;if(Z(r)&&K(r)>0)return!0;if(Me(r)&&De(r)>0)return!0;if(Ae(e)>0)return!0;return!1}(e);r&&(e._pulling?e._pullAgain=!0:(e._pulling=!0,p(e._pullAlgorithm(),(function(){return e._pulling=!1,e._pullAgain&&(e._pullAgain=!1,ye(e)),null}),(function(r){return je(e,r),null}))))}function me(e){Ce(e),e._pendingPullIntos=new S}function ve(e,r){var t=!1;"closed"===e._state&&(t=!0);var n=ge(r);"default"===r.readerType?J(e,n,t):function(e,r,t){var n=e._reader._readIntoRequests.shift();t?n._closeSteps(r):n._chunkSteps(r)}(e,n,t)}function ge(e){var r=e.bytesFilled,t=e.elementSize;return new e.viewConstructor(e.buffer,e.byteOffset,r/t)}function we(e,r,t,n){e._queue.push({buffer:r,byteOffset:t,byteLength:n}),e._queueTotalSize+=n}function Se(e,r,t,n){var o;try{o=r.slice(t,t+n)}catch(r){throw je(e,r),r}we(e,o,0,n)}function Re(e,r){r.bytesFilled>0&&Se(e,r.buffer,r.byteOffset,r.bytesFilled),Oe(e)}function Te(e,r){var t=r.elementSize,n=r.bytesFilled-r.bytesFilled%t,o=Math.min(e._queueTotalSize,r.byteLength-r.bytesFilled),a=r.bytesFilled+o,i=a-a%t,l=o,u=!1;i>n&&(l=i-r.bytesFilled,u=!0);for(var s=e._queue;l>0;){var c=s.peek(),d=Math.min(l,c.byteLength),f=r.byteOffset+r.bytesFilled;ue(r.buffer,f,c.buffer,c.byteOffset,d),c.byteLength===d?s.shift():(c.byteOffset+=d,c.byteLength-=d),e._queueTotalSize-=d,qe(e,d,r),l-=d}return u}function qe(e,r,t){t.bytesFilled+=r}function Pe(e){0===e._queueTotalSize&&e._closeRequested?(ke(e),lt(e._controlledReadableByteStream)):ye(e)}function Ce(e){null!==e._byobRequest&&(e._byobRequest._associatedReadableByteStreamController=void 0,e._byobRequest._view=null,e._byobRequest=null)}function Ee(e){for(;e._pendingPullIntos.length>0;){if(0===e._queueTotalSize)return;var r=e._pendingPullIntos.peek();Te(e,r)&&(Oe(e),ve(e._controlledReadableByteStream,r))}}function We(e,r){var t=e._pendingPullIntos.peek();Ce(e),"closed"===e._controlledReadableByteStream._state?function(e,r){"none"===r.readerType&&Oe(e);var t=e._controlledReadableByteStream;if(Me(t))for(;De(t)>0;)ve(t,Oe(e))}(e,t):function(e,r,t){if(qe(0,r,t),"none"===t.readerType)return Re(e,t),void Ee(e);if(!(t.bytesFilled<t.elementSize)){Oe(e);var n=t.bytesFilled%t.elementSize;if(n>0){var o=t.byteOffset+t.bytesFilled;Se(e,t.buffer,o-n,n)}t.bytesFilled-=n,ve(e._controlledReadableByteStream,t),Ee(e)}}(e,r,t),ye(e)}function Oe(e){return e._pendingPullIntos.shift()}function ke(e){e._pullAlgorithm=void 0,e._cancelAlgorithm=void 0}function je(e,r){var t=e._controlledReadableByteStream;"readable"===t._state&&(me(e),fe(e),ke(e),ut(t,r))}function Be(e,r){var t=e._queue.shift();e._queueTotalSize-=t.byteLength,Pe(e);var n=new Uint8Array(t.buffer,t.byteOffset,t.byteLength);r._chunkSteps(n)}function Ae(e){var r=e._controlledReadableByteStream._state;return"errored"===r?null:"closed"===r?0:e._strategyHWM-e._queueTotalSize}function ze(e,r,t){var n,o,a,i=Object.create(pe.prototype);n=void 0!==r.start?function(){return r.start(i)}:function(){},o=void 0!==r.pull?function(){return r.pull(i)}:function(){return d(void 0)},a=void 0!==r.cancel?function(e){return r.cancel(e)}:function(){return d(void 0)};var l=r.autoAllocateChunkSize;if(0===l)throw new TypeError("autoAllocateChunkSize must be greater than 0");!function(e,r,t,n,o,a,i){r._controlledReadableByteStream=e,r._pullAgain=!1,r._pulling=!1,r._byobRequest=null,r._queue=r._queueTotalSize=void 0,fe(r),r._closeRequested=!1,r._started=!1,r._strategyHWM=a,r._pullAlgorithm=n,r._cancelAlgorithm=o,r._autoAllocateChunkSize=i,r._pendingPullIntos=new S,e._readableStreamController=r,p(d(t()),(function(){return r._started=!0,ye(r),null}),(function(e){return je(r,e),null}))}(e,i,n,o,a,t,l)}function Le(e){return new TypeError("ReadableStreamBYOBRequest.prototype.".concat(e," can only be used on a ReadableStreamBYOBRequest"))}function Fe(e){return new TypeError("ReadableByteStreamController.prototype.".concat(e," can only be used on a ReadableByteStreamController"))}function Ie(e,r){e._reader._readIntoRequests.push(r)}function De(e){return e._reader._readIntoRequests.length}function Me(e){var r=e._reader;return void 0!==r&&!!Ye(r)}Object.defineProperties(pe.prototype,{close:{enumerable:!0},enqueue:{enumerable:!0},error:{enumerable:!0},byobRequest:{enumerable:!0},desiredSize:{enumerable:!0}}),a(pe.prototype.close,"close"),a(pe.prototype.enqueue,"enqueue"),a(pe.prototype.error,"error"),"symbol"==typeof r.toStringTag&&Object.defineProperty(pe.prototype,r.toStringTag,{value:"ReadableByteStreamController",configurable:!0});var Qe=function(){function ReadableStreamBYOBReader(e){if(Q(e,1,"ReadableStreamBYOBReader"),G(e,"First parameter"),at(e))throw new TypeError("This stream has already been locked for exclusive reading by another reader");if(!he(e._readableStreamController))throw new TypeError("Cannot construct a ReadableStreamBYOBReader for a stream not constructed with a byte source");E(this,e),this._readIntoRequests=new S}return Object.defineProperty(ReadableStreamBYOBReader.prototype,"closed",{get:function(){return Ye(this)?this._closedPromise:f(xe("closed"))},enumerable:!1,configurable:!0}),ReadableStreamBYOBReader.prototype.cancel=function(e){return void 0===e&&(e=void 0),Ye(this)?void 0===this._ownerReadableStream?f(k("cancel")):W(this,e):f(xe("cancel"))},ReadableStreamBYOBReader.prototype.read=function(e){if(!Ye(this))return f(xe("read"));if(!ArrayBuffer.isView(e))return f(new TypeError("view must be an array buffer view"));if(0===e.byteLength)return f(new TypeError("view must have non-zero byteLength"));if(0===e.buffer.byteLength)return f(new TypeError("view's buffer must have non-zero byteLength"));if(e.buffer,void 0===this._ownerReadableStream)return f(k("read from"));var r,t,n=c((function(e,n){r=e,t=n}));return function(e,r,t){var n=e._ownerReadableStream;n._disturbed=!0,"errored"===n._state?t._errorSteps(n._storedError):function(e,r,t){var n=e._controlledReadableByteStream,o=1;r.constructor!==DataView&&(o=r.constructor.BYTES_PER_ELEMENT);var a=r.constructor,i=r.buffer,l={buffer:i,bufferByteLength:i.byteLength,byteOffset:r.byteOffset,byteLength:r.byteLength,bytesFilled:0,elementSize:o,viewConstructor:a,readerType:"byob"};if(e._pendingPullIntos.length>0)return e._pendingPullIntos.push(l),void Ie(n,t);if("closed"!==n._state){if(e._queueTotalSize>0){if(Te(e,l)){var u=ge(l);return Pe(e),void t._chunkSteps(u)}if(e._closeRequested){var s=new TypeError("Insufficient bytes to fill elements in the given buffer");return je(e,s),void t._errorSteps(s)}}e._pendingPullIntos.push(l),Ie(n,t),ye(e)}else{var c=new a(l.buffer,l.byteOffset,0);t._closeSteps(c)}}(n._readableStreamController,r,t)}(this,e,{_chunkSteps:function(e){return r({value:e,done:!1})},_closeSteps:function(e){return r({value:e,done:!0})},_errorSteps:function(e){return t(e)}}),n},ReadableStreamBYOBReader.prototype.releaseLock=function(){if(!Ye(this))throw xe("releaseLock");void 0!==this._ownerReadableStream&&function(e){O(e);var r=new TypeError("Reader was released");Ne(e,r)}(this)},ReadableStreamBYOBReader}();function Ye(e){return!!n(e)&&(!!Object.prototype.hasOwnProperty.call(e,"_readIntoRequests")&&e instanceof Qe)}function Ne(e,r){var t=e._readIntoRequests;e._readIntoRequests=new S,t.forEach((function(e){e._errorSteps(r)}))}function xe(e){return new TypeError("ReadableStreamBYOBReader.prototype.".concat(e," can only be used on a ReadableStreamBYOBReader"))}function He(e,r){var t=e.highWaterMark;if(void 0===t)return r;if(le(t)||t<0)throw new RangeError("Invalid highWaterMark");return t}function Ve(e){var r=e.size;return r||function(){return 1}}function Ue(e,r){I(e,r);var t=null==e?void 0:e.highWaterMark,n=null==e?void 0:e.size;return{highWaterMark:void 0===t?void 0:N(t),size:void 0===n?void 0:Ge(n,"".concat(r," has member 'size' that"))}}function Ge(e,r){return D(e,r),function(r){return N(e(r))}}function Xe(e,r,t){return D(e,t),function(t){return w(e,r,[t])}}function Je(e,r,t){return D(e,t),function(){return w(e,r,[])}}function Ke(e,r,t){return D(e,t),function(t){return g(e,r,[t])}}function Ze(e,r,t){return D(e,t),function(t,n){return w(e,r,[t,n])}}Object.defineProperties(Qe.prototype,{cancel:{enumerable:!0},read:{enumerable:!0},releaseLock:{enumerable:!0},closed:{enumerable:!0}}),a(Qe.prototype.cancel,"cancel"),a(Qe.prototype.read,"read"),a(Qe.prototype.releaseLock,"releaseLock"),"symbol"==typeof r.toStringTag&&Object.defineProperty(Qe.prototype,r.toStringTag,{value:"ReadableStreamBYOBReader",configurable:!0});var $e="function"==typeof AbortController;var er=function(){function WritableStream(e,r){void 0===e&&(e={}),void 0===r&&(r={}),void 0===e?e=null:M(e,"First parameter");var t,n=Ue(r,"Second parameter"),o=function(e,r){I(e,r);var t=null==e?void 0:e.abort,n=null==e?void 0:e.close,o=null==e?void 0:e.start,a=null==e?void 0:e.type,i=null==e?void 0:e.write;return{abort:void 0===t?void 0:Xe(t,e,"".concat(r," has member 'abort' that")),close:void 0===n?void 0:Je(n,e,"".concat(r," has member 'close' that")),start:void 0===o?void 0:Ke(o,e,"".concat(r," has member 'start' that")),write:void 0===i?void 0:Ze(i,e,"".concat(r," has member 'write' that")),type:a}}(e,"First parameter");if((t=this)._state="writable",t._storedError=void 0,t._writer=void 0,t._writableStreamController=void 0,t._writeRequests=new S,t._inFlightWriteRequest=void 0,t._closeRequest=void 0,t._inFlightCloseRequest=void 0,t._pendingAbortRequest=void 0,t._backpressure=!1,void 0!==o.type)throw new RangeError("Invalid type is specified");var a=Ve(n);!function(e,r,t,n){var o,a,i,l,u=Object.create(hr.prototype);o=void 0!==r.start?function(){return r.start(u)}:function(){};a=void 0!==r.write?function(e){return r.write(e,u)}:function(){return d(void 0)};i=void 0!==r.close?function(){return r.close()}:function(){return d(void 0)};l=void 0!==r.abort?function(e){return r.abort(e)}:function(){return d(void 0)};!function(e,r,t,n,o,a,i,l){r._controlledWritableStream=e,e._writableStreamController=r,r._queue=void 0,r._queueTotalSize=void 0,fe(r),r._abortReason=void 0,r._abortController=function(){if($e)return new AbortController}(),r._started=!1,r._strategySizeAlgorithm=l,r._strategyHWM=i,r._writeAlgorithm=n,r._closeAlgorithm=o,r._abortAlgorithm=a;var u=wr(r);cr(e,u),p(d(t()),(function(){return r._started=!0,vr(r),null}),(function(t){return r._started=!0,ar(e,t),null}))}(e,u,o,a,i,l,t,n)}(this,o,He(n,1),a)}return Object.defineProperty(WritableStream.prototype,"locked",{get:function(){if(!rr(this))throw Rr("locked");return tr(this)},enumerable:!1,configurable:!0}),WritableStream.prototype.abort=function(e){return void 0===e&&(e=void 0),rr(this)?tr(this)?f(new TypeError("Cannot abort a stream that already has a writer")):nr(this,e):f(Rr("abort"))},WritableStream.prototype.close=function(){return rr(this)?tr(this)?f(new TypeError("Cannot close a stream that already has a writer")):ur(this)?f(new TypeError("Cannot close an already-closing stream")):or(this):f(Rr("close"))},WritableStream.prototype.getWriter=function(){if(!rr(this))throw Rr("getWriter");return new dr(this)},WritableStream}();function rr(e){return!!n(e)&&(!!Object.prototype.hasOwnProperty.call(e,"_writableStreamController")&&e instanceof er)}function tr(e){return void 0!==e._writer}function nr(e,r){var t;if("closed"===e._state||"errored"===e._state)return d(void 0);e._writableStreamController._abortReason=r,null===(t=e._writableStreamController._abortController)||void 0===t||t.abort(r);var n=e._state;if("closed"===n||"errored"===n)return d(void 0);if(void 0!==e._pendingAbortRequest)return e._pendingAbortRequest._promise;var o=!1;"erroring"===n&&(o=!0,r=void 0);var a=c((function(t,n){e._pendingAbortRequest={_promise:void 0,_resolve:t,_reject:n,_reason:r,_wasAlreadyErroring:o}}));return e._pendingAbortRequest._promise=a,o||ir(e,r),a}function or(e){var r=e._state;if("closed"===r||"errored"===r)return f(new TypeError("The stream (in ".concat(r," state) is not in the writable state and cannot be closed")));var t,n=c((function(r,t){var n={_resolve:r,_reject:t};e._closeRequest=n})),o=e._writer;return void 0!==o&&e._backpressure&&"writable"===r&&zr(o),de(t=e._writableStreamController,pr,0),vr(t),n}function ar(e,r){"writable"!==e._state?lr(e):ir(e,r)}function ir(e,r){var t=e._writableStreamController;e._state="erroring",e._storedError=r;var n=e._writer;void 0!==n&&br(n,r),!function(e){if(void 0===e._inFlightWriteRequest&&void 0===e._inFlightCloseRequest)return!1;return!0}(e)&&t._started&&lr(e)}function lr(e){e._state="errored",e._writableStreamController[T]();var r=e._storedError;if(e._writeRequests.forEach((function(e){e._reject(r)})),e._writeRequests=new S,void 0!==e._pendingAbortRequest){var t=e._pendingAbortRequest;if(e._pendingAbortRequest=void 0,t._wasAlreadyErroring)return t._reject(r),void sr(e);p(e._writableStreamController[R](t._reason),(function(){return t._resolve(),sr(e),null}),(function(r){return t._reject(r),sr(e),null}))}else sr(e)}function ur(e){return void 0!==e._closeRequest||void 0!==e._inFlightCloseRequest}function sr(e){void 0!==e._closeRequest&&(e._closeRequest._reject(e._storedError),e._closeRequest=void 0);var r=e._writer;void 0!==r&&Wr(r,e._storedError)}function cr(e,r){var t=e._writer;void 0!==t&&r!==e._backpressure&&(r?function(e){kr(e)}(t):zr(t)),e._backpressure=r}Object.defineProperties(er.prototype,{abort:{enumerable:!0},close:{enumerable:!0},getWriter:{enumerable:!0},locked:{enumerable:!0}}),a(er.prototype.abort,"abort"),a(er.prototype.close,"close"),a(er.prototype.getWriter,"getWriter"),"symbol"==typeof r.toStringTag&&Object.defineProperty(er.prototype,r.toStringTag,{value:"WritableStream",configurable:!0});var dr=function(){function WritableStreamDefaultWriter(e){if(Q(e,1,"WritableStreamDefaultWriter"),function(e,r){if(!rr(e))throw new TypeError("".concat(r," is not a WritableStream."))}(e,"First parameter"),tr(e))throw new TypeError("This stream has already been locked for exclusive writing by another writer");this._ownerWritableStream=e,e._writer=this;var r,t=e._state;if("writable"===t)!ur(e)&&e._backpressure?kr(this):Br(this),Cr(this);else if("erroring"===t)jr(this,e._storedError),Cr(this);else if("closed"===t)Br(this),Cr(r=this),Or(r);else{var n=e._storedError;jr(this,n),Er(this,n)}}return Object.defineProperty(WritableStreamDefaultWriter.prototype,"closed",{get:function(){return fr(this)?this._closedPromise:f(qr("closed"))},enumerable:!1,configurable:!0}),Object.defineProperty(WritableStreamDefaultWriter.prototype,"desiredSize",{get:function(){if(!fr(this))throw qr("desiredSize");if(void 0===this._ownerWritableStream)throw Pr("desiredSize");return function(e){var r=e._ownerWritableStream,t=r._state;if("errored"===t||"erroring"===t)return null;if("closed"===t)return 0;return mr(r._writableStreamController)}(this)},enumerable:!1,configurable:!0}),Object.defineProperty(WritableStreamDefaultWriter.prototype,"ready",{get:function(){return fr(this)?this._readyPromise:f(qr("ready"))},enumerable:!1,configurable:!0}),WritableStreamDefaultWriter.prototype.abort=function(e){return void 0===e&&(e=void 0),fr(this)?void 0===this._ownerWritableStream?f(Pr("abort")):function(e,r){return nr(e._ownerWritableStream,r)}(this,e):f(qr("abort"))},WritableStreamDefaultWriter.prototype.close=function(){if(!fr(this))return f(qr("close"));var e=this._ownerWritableStream;return void 0===e?f(Pr("close")):ur(e)?f(new TypeError("Cannot close an already-closing stream")):or(this._ownerWritableStream)},WritableStreamDefaultWriter.prototype.releaseLock=function(){if(!fr(this))throw qr("releaseLock");void 0!==this._ownerWritableStream&&function(e){var r=e._ownerWritableStream,t=new TypeError("Writer was released and can no longer be used to monitor the stream's closedness");br(e,t),function(e,r){"pending"===e._closedPromiseState?Wr(e,r):function(e,r){Er(e,r)}(e,r)}(e,t),r._writer=void 0,e._ownerWritableStream=void 0}(this)},WritableStreamDefaultWriter.prototype.write=function(e){return void 0===e&&(e=void 0),fr(this)?void 0===this._ownerWritableStream?f(Pr("write to")):function(e,r){var t=e._ownerWritableStream,n=t._writableStreamController,o=function(e,r){try{return e._strategySizeAlgorithm(r)}catch(r){return gr(e,r),1}}(n,r);if(t!==e._ownerWritableStream)return f(Pr("write to"));var a=t._state;if("errored"===a)return f(t._storedError);if(ur(t)||"closed"===a)return f(new TypeError("The stream is closing or closed and cannot be written to"));if("erroring"===a)return f(t._storedError);var i=function(e){return c((function(r,t){var n={_resolve:r,_reject:t};e._writeRequests.push(n)}))}(t);return function(e,r,t){try{de(e,r,t)}catch(r){return void gr(e,r)}var n=e._controlledWritableStream;if(!ur(n)&&"writable"===n._state){cr(n,wr(e))}vr(e)}(n,r,o),i}(this,e):f(qr("write"))},WritableStreamDefaultWriter}();function fr(e){return!!n(e)&&(!!Object.prototype.hasOwnProperty.call(e,"_ownerWritableStream")&&e instanceof dr)}function br(e,r){"pending"===e._readyPromiseState?Ar(e,r):function(e,r){jr(e,r)}(e,r)}Object.defineProperties(dr.prototype,{abort:{enumerable:!0},close:{enumerable:!0},releaseLock:{enumerable:!0},write:{enumerable:!0},closed:{enumerable:!0},desiredSize:{enumerable:!0},ready:{enumerable:!0}}),a(dr.prototype.abort,"abort"),a(dr.prototype.close,"close"),a(dr.prototype.releaseLock,"releaseLock"),a(dr.prototype.write,"write"),"symbol"==typeof r.toStringTag&&Object.defineProperty(dr.prototype,r.toStringTag,{value:"WritableStreamDefaultWriter",configurable:!0});var pr={},hr=function(){function WritableStreamDefaultController(){throw new TypeError("Illegal constructor")}return Object.defineProperty(WritableStreamDefaultController.prototype,"abortReason",{get:function(){if(!_r(this))throw Tr("abortReason");return this._abortReason},enumerable:!1,configurable:!0}),Object.defineProperty(WritableStreamDefaultController.prototype,"signal",{get:function(){if(!_r(this))throw Tr("signal");if(void 0===this._abortController)throw new TypeError("WritableStreamDefaultController.prototype.signal is not supported");return this._abortController.signal},enumerable:!1,configurable:!0}),WritableStreamDefaultController.prototype.error=function(e){if(void 0===e&&(e=void 0),!_r(this))throw Tr("error");"writable"===this._controlledWritableStream._state&&Sr(this,e)},WritableStreamDefaultController.prototype[R]=function(e){var r=this._abortAlgorithm(e);return yr(this),r},WritableStreamDefaultController.prototype[T]=function(){fe(this)},WritableStreamDefaultController}();function _r(e){return!!n(e)&&(!!Object.prototype.hasOwnProperty.call(e,"_controlledWritableStream")&&e instanceof hr)}function yr(e){e._writeAlgorithm=void 0,e._closeAlgorithm=void 0,e._abortAlgorithm=void 0,e._strategySizeAlgorithm=void 0}function mr(e){return e._strategyHWM-e._queueTotalSize}function vr(e){var r=e._controlledWritableStream;if(e._started&&void 0===r._inFlightWriteRequest)if("erroring"!==r._state){if(0!==e._queue.length){var t=e._queue.peek().value;t===pr?function(e){var r=e._controlledWritableStream;(function(e){e._inFlightCloseRequest=e._closeRequest,e._closeRequest=void 0})(r),ce(e);var t=e._closeAlgorithm();yr(e),p(t,(function(){return function(e){e._inFlightCloseRequest._resolve(void 0),e._inFlightCloseRequest=void 0,"erroring"===e._state&&(e._storedError=void 0,void 0!==e._pendingAbortRequest&&(e._pendingAbortRequest._resolve(),e._pendingAbortRequest=void 0)),e._state="closed";var r=e._writer;void 0!==r&&Or(r)}(r),null}),(function(e){return function(e,r){e._inFlightCloseRequest._reject(r),e._inFlightCloseRequest=void 0,void 0!==e._pendingAbortRequest&&(e._pendingAbortRequest._reject(r),e._pendingAbortRequest=void 0),ar(e,r)}(r,e),null}))}(e):function(e,r){var t=e._controlledWritableStream;(function(e){e._inFlightWriteRequest=e._writeRequests.shift()})(t),p(e._writeAlgorithm(r),(function(){!function(e){e._inFlightWriteRequest._resolve(void 0),e._inFlightWriteRequest=void 0}(t);var r=t._state;if(ce(e),!ur(t)&&"writable"===r){var n=wr(e);cr(t,n)}return vr(e),null}),(function(r){return"writable"===t._state&&yr(e),function(e,r){e._inFlightWriteRequest._reject(r),e._inFlightWriteRequest=void 0,ar(e,r)}(t,r),null}))}(e,t)}}else lr(r)}function gr(e,r){"writable"===e._controlledWritableStream._state&&Sr(e,r)}function wr(e){return mr(e)<=0}function Sr(e,r){var t=e._controlledWritableStream;yr(e),ir(t,r)}function Rr(e){return new TypeError("WritableStream.prototype.".concat(e," can only be used on a WritableStream"))}function Tr(e){return new TypeError("WritableStreamDefaultController.prototype.".concat(e," can only be used on a WritableStreamDefaultController"))}function qr(e){return new TypeError("WritableStreamDefaultWriter.prototype.".concat(e," can only be used on a WritableStreamDefaultWriter"))}function Pr(e){return new TypeError("Cannot "+e+" a stream using a released writer")}function Cr(e){e._closedPromise=c((function(r,t){e._closedPromise_resolve=r,e._closedPromise_reject=t,e._closedPromiseState="pending"}))}function Er(e,r){Cr(e),Wr(e,r)}function Wr(e,r){void 0!==e._closedPromise_reject&&(m(e._closedPromise),e._closedPromise_reject(r),e._closedPromise_resolve=void 0,e._closedPromise_reject=void 0,e._closedPromiseState="rejected")}function Or(e){void 0!==e._closedPromise_resolve&&(e._closedPromise_resolve(void 0),e._closedPromise_resolve=void 0,e._closedPromise_reject=void 0,e._closedPromiseState="resolved")}function kr(e){e._readyPromise=c((function(r,t){e._readyPromise_resolve=r,e._readyPromise_reject=t})),e._readyPromiseState="pending"}function jr(e,r){kr(e),Ar(e,r)}function Br(e){kr(e),zr(e)}function Ar(e,r){void 0!==e._readyPromise_reject&&(m(e._readyPromise),e._readyPromise_reject(r),e._readyPromise_resolve=void 0,e._readyPromise_reject=void 0,e._readyPromiseState="rejected")}function zr(e){void 0!==e._readyPromise_resolve&&(e._readyPromise_resolve(void 0),e._readyPromise_resolve=void 0,e._readyPromise_reject=void 0,e._readyPromiseState="fulfilled")}Object.defineProperties(hr.prototype,{abortReason:{enumerable:!0},signal:{enumerable:!0},error:{enumerable:!0}}),"symbol"==typeof r.toStringTag&&Object.defineProperty(hr.prototype,r.toStringTag,{value:"WritableStreamDefaultController",configurable:!0});var Lr="undefined"!=typeof DOMException?DOMException:void 0;var Fr,Ir=function(e){if("function"!=typeof e&&"object"!=typeof e)return!1;try{return new e,!0}catch(e){return!1}}(Lr)?Lr:((Fr=function(e,r){this.message=e||"",this.name=r||"Error",Error.captureStackTrace&&Error.captureStackTrace(this,this.constructor)}).prototype=Object.create(Error.prototype),Object.defineProperty(Fr.prototype,"constructor",{value:Fr,writable:!0,configurable:!0}),Fr);function Dr(e,r,t,n,o,a){var i=e.getReader(),l=r.getWriter();ot(e)&&(e._disturbed=!0);var u,s,_,g=!1,w=!1,S="readable",R="writable",T=!1,q=!1,P=c((function(e){_=e})),C=Promise.resolve(void 0);return c((function(E,W){var O;function k(){if(!g){var e=c((function(e,r){!function t(n){n?e():b(function(){if(g)return d(!0);return b(l.ready,(function(){return b(i.read(),(function(e){return!!e.done||(m(C=l.write(e.value)),!1)}))}))}(),t,r)}(!1)}));m(e)}}function j(){return S="closed",t?F():L((function(){return rr(r)&&(T=ur(r),R=r._state),T||"closed"===R?d(void 0):"erroring"===R||"errored"===R?f(s):(T=!0,l.close())}),!1,void 0),null}function B(e){return g||(S="errored",u=e,n?F(!0,e):L((function(){return l.abort(e)}),!0,e)),null}function A(e){return w||(R="errored",s=e,o?F(!0,e):L((function(){return i.cancel(e)}),!0,e)),null}if(void 0!==a&&(O=function(){var e=void 0!==a.reason?a.reason:new Ir("Aborted","AbortError"),r=[];n||r.push((function(){return"writable"===R?l.abort(e):d(void 0)})),o||r.push((function(){return"readable"===S?i.cancel(e):d(void 0)})),L((function(){return Promise.all(r.map((function(e){return e()})))}),!0,e)},a.aborted?O():a.addEventListener("abort",O)),ot(e)&&(S=e._state,u=e._storedError),rr(r)&&(R=r._state,s=r._storedError,T=ur(r)),ot(e)&&rr(r)&&(q=!0,_()),"errored"===S)B(u);else if("erroring"===R||"errored"===R)A(s);else if("closed"===S)j();else if(T||"closed"===R){var z=new TypeError("the destination writable stream closed before all data could be piped to it");o?F(!0,z):L((function(){return i.cancel(z)}),!0,z)}function L(e,r,t){function n(){var e;return"writable"!==R||T?o():h(d(function r(){if(e!==C)return e=C,y(C,r,r)}()),o),null}function o(){return e?p(e(),(function(){return I(r,t)}),(function(e){return I(!0,e)})):I(r,t),null}g||(g=!0,q?n():h(P,n))}function F(e,r){L(void 0,e,r)}function I(e,r){return w=!0,l.releaseLock(),i.releaseLock(),void 0!==a&&a.removeEventListener("abort",O),e?W(r):E(void 0),null}g||(p(i.closed,j,B),p(l.closed,(function(){return w||(R="closed"),null}),A)),q?k():v((function(){q=!0,_(),k()}))}))}function Mr(e,r){return function(e){try{return e.getReader({mode:"byob"}).releaseLock(),!0}catch(e){return!1}}(e)?function(e){var r,t,n,o,a,i=e.getReader(),l=!1,u=!1,s=!1,f=!1,b=!1,h=!1,y=c((function(e){a=e}));function m(e){_(e.closed,(function(r){return e!==i||(n.error(r),o.error(r),b&&h||a(void 0)),null}))}function v(){l&&(i.releaseLock(),m(i=e.getReader()),l=!1),p(i.read(),(function(e){var r,t;if(s=!1,f=!1,e.done)return b||n.close(),h||o.close(),null===(r=n.byobRequest)||void 0===r||r.respond(0),null===(t=o.byobRequest)||void 0===t||t.respond(0),b&&h||a(void 0),null;var l=e.value,c=l,d=l;if(!b&&!h)try{d=se(l)}catch(e){return n.error(e),o.error(e),a(i.cancel(e)),null}return b||n.enqueue(c),h||o.enqueue(d),u=!1,s?w():f&&S(),null}),(function(){return u=!1,null}))}function g(r,t){l||(i.releaseLock(),m(i=e.getReader({mode:"byob"})),l=!0);var c=t?o:n,d=t?n:o;p(i.read(r),(function(e){var r;s=!1,f=!1;var n=t?h:b,o=t?b:h;if(e.done){n||c.close(),o||d.close();var l=e.value;return void 0!==l&&(n||c.byobRequest.respondWithNewView(l),o||null===(r=d.byobRequest)||void 0===r||r.respond(0)),n&&o||a(void 0),null}var p=e.value;if(o)n||c.byobRequest.respondWithNewView(p);else{var _=void 0;try{_=se(p)}catch(e){return c.error(e),d.error(e),a(i.cancel(e)),null}n||c.byobRequest.respondWithNewView(p),d.enqueue(_)}return u=!1,s?w():f&&S(),null}),(function(){return u=!1,null}))}function w(){if(u)return s=!0,d(void 0);u=!0;var e=n.byobRequest;return null===e?v():g(e.view,!1),d(void 0)}function S(){if(u)return f=!0,d(void 0);u=!0;var e=o.byobRequest;return null===e?v():g(e.view,!0),d(void 0)}function R(e){if(b=!0,r=e,h){var n=[r,t],o=i.cancel(n);a(o)}return y}function T(e){if(h=!0,t=e,b){var n=[r,t],o=i.cancel(n);a(o)}return y}var q=new nt({type:"bytes",start:function(e){n=e},pull:w,cancel:R}),P=new nt({type:"bytes",start:function(e){o=e},pull:S,cancel:T});return m(i),[q,P]}(e):function(e,r){var t,n,o,a,i,l=e.getReader(),u=!1,s=!1,f=!1,b=!1,h=c((function(e){i=e}));function y(){return u?(s=!0,d(void 0)):(u=!0,p(l.read(),(function(e){if(s=!1,e.done)return f||o.close(),b||a.close(),f&&b||i(void 0),null;var r=e.value,t=r,n=r;return f||o.enqueue(t),b||a.enqueue(n),u=!1,s&&y(),null}),(function(){return u=!1,null})),d(void 0))}function m(e){if(f=!0,t=e,b){var r=[t,n],o=l.cancel(r);i(o)}return h}function v(e){if(b=!0,n=e,f){var r=[t,n],o=l.cancel(r);i(o)}return h}var g=new nt({start:function(e){o=e},pull:y,cancel:m}),w=new nt({start:function(e){a=e},pull:y,cancel:v});return _(l.closed,(function(e){return o.error(e),a.error(e),f&&b||i(void 0),null})),[g,w]}(e)}var Qr=function(){function ReadableStreamDefaultController(){throw new TypeError("Illegal constructor")}return Object.defineProperty(ReadableStreamDefaultController.prototype,"desiredSize",{get:function(){if(!Yr(this))throw Xr("desiredSize");return Vr(this)},enumerable:!1,configurable:!0}),ReadableStreamDefaultController.prototype.close=function(){if(!Yr(this))throw Xr("close");if(!Ur(this))throw new TypeError("The stream is not in a state that permits close");!function(e){if(!Ur(e))return;var r=e._controlledReadableStream;e._closeRequested=!0,0===e._queue.length&&(xr(e),lt(r))}(this)},ReadableStreamDefaultController.prototype.enqueue=function(e){if(void 0===e&&(e=void 0),!Yr(this))throw Xr("enqueue");if(!Ur(this))throw new TypeError("The stream is not in a state that permits enqueue");return function(e,r){if(!Ur(e))return;var t=e._controlledReadableStream;if(at(t)&&K(t)>0)J(t,r,!1);else{var n=void 0;try{n=e._strategySizeAlgorithm(r)}catch(r){throw Hr(e,r),r}try{de(e,r,n)}catch(r){throw Hr(e,r),r}}Nr(e)}(this,e)},ReadableStreamDefaultController.prototype.error=function(e){if(void 0===e&&(e=void 0),!Yr(this))throw Xr("error");Hr(this,e)},ReadableStreamDefaultController.prototype[q]=function(e){fe(this);var r=this._cancelAlgorithm(e);return xr(this),r},ReadableStreamDefaultController.prototype[P]=function(e){var r=this._controlledReadableStream;if(this._queue.length>0){var t=ce(this);this._closeRequested&&0===this._queue.length?(xr(this),lt(r)):Nr(this),e._chunkSteps(t)}else X(r,e),Nr(this)},ReadableStreamDefaultController.prototype[C]=function(){},ReadableStreamDefaultController}();function Yr(e){return!!n(e)&&(!!Object.prototype.hasOwnProperty.call(e,"_controlledReadableStream")&&e instanceof Qr)}function Nr(e){var r=function(e){var r=e._controlledReadableStream;if(!Ur(e))return!1;if(!e._started)return!1;if(at(r)&&K(r)>0)return!0;if(Vr(e)>0)return!0;return!1}(e);r&&(e._pulling?e._pullAgain=!0:(e._pulling=!0,p(e._pullAlgorithm(),(function(){return e._pulling=!1,e._pullAgain&&(e._pullAgain=!1,Nr(e)),null}),(function(r){return Hr(e,r),null}))))}function xr(e){e._pullAlgorithm=void 0,e._cancelAlgorithm=void 0,e._strategySizeAlgorithm=void 0}function Hr(e,r){var t=e._controlledReadableStream;"readable"===t._state&&(fe(e),xr(e),ut(t,r))}function Vr(e){var r=e._controlledReadableStream._state;return"errored"===r?null:"closed"===r?0:e._strategyHWM-e._queueTotalSize}function Ur(e){return!e._closeRequested&&"readable"===e._controlledReadableStream._state}function Gr(e,r,t,n){var o,a,i,l=Object.create(Qr.prototype);o=void 0!==r.start?function(){return r.start(l)}:function(){},a=void 0!==r.pull?function(){return r.pull(l)}:function(){return d(void 0)},i=void 0!==r.cancel?function(e){return r.cancel(e)}:function(){return d(void 0)},function(e,r,t,n,o,a,i){r._controlledReadableStream=e,r._queue=void 0,r._queueTotalSize=void 0,fe(r),r._started=!1,r._closeRequested=!1,r._pullAgain=!1,r._pulling=!1,r._strategySizeAlgorithm=i,r._strategyHWM=a,r._pullAlgorithm=n,r._cancelAlgorithm=o,e._readableStreamController=r,p(d(t()),(function(){return r._started=!0,Nr(r),null}),(function(e){return Hr(r,e),null}))}(e,l,o,a,i,t,n)}function Xr(e){return new TypeError("ReadableStreamDefaultController.prototype.".concat(e," can only be used on a ReadableStreamDefaultController"))}function Jr(e,r,t){return D(e,t),function(t){return w(e,r,[t])}}function Kr(e,r,t){return D(e,t),function(t){return w(e,r,[t])}}function Zr(e,r,t){return D(e,t),function(t){return g(e,r,[t])}}function $r(e,r){if("bytes"!==(e="".concat(e)))throw new TypeError("".concat(r," '").concat(e,"' is not a valid enumeration value for ReadableStreamType"));return e}function et(e,r){if("byob"!==(e="".concat(e)))throw new TypeError("".concat(r," '").concat(e,"' is not a valid enumeration value for ReadableStreamReaderMode"));return e}function rt(e,r){I(e,r);var t=null==e?void 0:e.preventAbort,n=null==e?void 0:e.preventCancel,o=null==e?void 0:e.preventClose,a=null==e?void 0:e.signal;return void 0!==a&&function(e,r){if(!function(e){if("object"!=typeof e||null===e)return!1;try{return"boolean"==typeof e.aborted}catch(e){return!1}}(e))throw new TypeError("".concat(r," is not an AbortSignal."))}(a,"".concat(r," has member 'signal' that")),{preventAbort:Boolean(t),preventCancel:Boolean(n),preventClose:Boolean(o),signal:a}}function tt(e,r){I(e,r);var t=null==e?void 0:e.readable;Y(t,"readable","ReadableWritablePair"),function(e,r){if(!V(e))throw new TypeError("".concat(r," is not a ReadableStream."))}(t,"".concat(r," has member 'readable' that"));var n=null==e?void 0:e.writable;return Y(n,"writable","ReadableWritablePair"),function(e,r){if(!U(e))throw new TypeError("".concat(r," is not a WritableStream."))}(n,"".concat(r," has member 'writable' that")),{readable:t,writable:n}}Object.defineProperties(Qr.prototype,{close:{enumerable:!0},enqueue:{enumerable:!0},error:{enumerable:!0},desiredSize:{enumerable:!0}}),a(Qr.prototype.close,"close"),a(Qr.prototype.enqueue,"enqueue"),a(Qr.prototype.error,"error"),"symbol"==typeof r.toStringTag&&Object.defineProperty(Qr.prototype,r.toStringTag,{value:"ReadableStreamDefaultController",configurable:!0});var nt=function(){function ReadableStream(e,r){void 0===e&&(e={}),void 0===r&&(r={}),void 0===e?e=null:M(e,"First parameter");var t,n=Ue(r,"Second parameter"),o=function(e,r){I(e,r);var t=e,n=null==t?void 0:t.autoAllocateChunkSize,o=null==t?void 0:t.cancel,a=null==t?void 0:t.pull,i=null==t?void 0:t.start,l=null==t?void 0:t.type;return{autoAllocateChunkSize:void 0===n?void 0:H(n,"".concat(r," has member 'autoAllocateChunkSize' that")),cancel:void 0===o?void 0:Jr(o,t,"".concat(r," has member 'cancel' that")),pull:void 0===a?void 0:Kr(a,t,"".concat(r," has member 'pull' that")),start:void 0===i?void 0:Zr(i,t,"".concat(r," has member 'start' that")),type:void 0===l?void 0:$r(l,"".concat(r," has member 'type' that"))}}(e,"First parameter");if((t=this)._state="readable",t._reader=void 0,t._storedError=void 0,t._disturbed=!1,"bytes"===o.type){if(void 0!==n.size)throw new RangeError("The strategy for a byte stream cannot have a size function");ze(this,o,He(n,0))}else{var a=Ve(n);Gr(this,o,He(n,1),a)}}return Object.defineProperty(ReadableStream.prototype,"locked",{get:function(){if(!ot(this))throw st("locked");return at(this)},enumerable:!1,configurable:!0}),ReadableStream.prototype.cancel=function(e){return void 0===e&&(e=void 0),ot(this)?at(this)?f(new TypeError("Cannot cancel a stream that already has a reader")):it(this,e):f(st("cancel"))},ReadableStream.prototype.getReader=function(e){if(void 0===e&&(e=void 0),!ot(this))throw st("getReader");return void 0===function(e,r){I(e,r);var t=null==e?void 0:e.mode;return{mode:void 0===t?void 0:et(t,"".concat(r," has member 'mode' that"))}}(e,"First parameter").mode?new $(this):function(e){return new Qe(e)}(this)},ReadableStream.prototype.pipeThrough=function(e,r){if(void 0===r&&(r={}),!V(this))throw st("pipeThrough");Q(e,1,"pipeThrough");var t=tt(e,"First parameter"),n=rt(r,"Second parameter");if(this.locked)throw new TypeError("ReadableStream.prototype.pipeThrough cannot be used on a locked ReadableStream");if(t.writable.locked)throw new TypeError("ReadableStream.prototype.pipeThrough cannot be used on a locked WritableStream");return m(Dr(this,t.writable,n.preventClose,n.preventAbort,n.preventCancel,n.signal)),t.readable},ReadableStream.prototype.pipeTo=function(e,r){if(void 0===r&&(r={}),!V(this))return f(st("pipeTo"));if(void 0===e)return f("Parameter 1 is required in 'pipeTo'.");if(!U(e))return f(new TypeError("ReadableStream.prototype.pipeTo's first argument must be a WritableStream"));var t;try{t=rt(r,"Second parameter")}catch(e){return f(e)}return this.locked?f(new TypeError("ReadableStream.prototype.pipeTo cannot be used on a locked ReadableStream")):e.locked?f(new TypeError("ReadableStream.prototype.pipeTo cannot be used on a locked WritableStream")):Dr(this,e,t.preventClose,t.preventAbort,t.preventCancel,t.signal)},ReadableStream.prototype.tee=function(){if(!V(this))throw st("tee");if(this.locked)throw new TypeError("Cannot tee a stream that already has a reader");return Mr(this)},ReadableStream.prototype.values=function(e){if(void 0===e&&(e=void 0),!V(this))throw st("values");var r,t,n,o,a,i=function(e,r){I(e,r);var t=null==e?void 0:e.preventCancel;return{preventCancel:Boolean(t)}}(e,"First parameter");return r=this,t=i.preventCancel,n=r.getReader(),o=new ne(n,t),(a=Object.create(oe))._asyncIteratorImpl=o,a},ReadableStream}();function ot(e){return!!n(e)&&(!!Object.prototype.hasOwnProperty.call(e,"_readableStreamController")&&e instanceof nt)}function at(e){return void 0!==e._reader}function it(e,r){if(e._disturbed=!0,"closed"===e._state)return d(void 0);if("errored"===e._state)return f(e._storedError);lt(e);var n=e._reader;if(void 0!==n&&Ye(n)){var o=n._readIntoRequests;n._readIntoRequests=new S,o.forEach((function(e){e._closeSteps(void 0)}))}return y(e._readableStreamController[q](r),t)}function lt(e){e._state="closed";var r=e._reader;if(void 0!==r&&(z(r),ee(r))){var t=r._readRequests;r._readRequests=new S,t.forEach((function(e){e._closeSteps()}))}}function ut(e,r){e._state="errored",e._storedError=r;var t=e._reader;void 0!==t&&(A(t,r),ee(t)?re(t,r):Ne(t,r))}function st(e){return new TypeError("ReadableStream.prototype.".concat(e," can only be used on a ReadableStream"))}function ct(e,r){I(e,r);var t=null==e?void 0:e.highWaterMark;return Y(t,"highWaterMark","QueuingStrategyInit"),{highWaterMark:N(t)}}Object.defineProperties(nt.prototype,{cancel:{enumerable:!0},getReader:{enumerable:!0},pipeThrough:{enumerable:!0},pipeTo:{enumerable:!0},tee:{enumerable:!0},values:{enumerable:!0},locked:{enumerable:!0}}),a(nt.prototype.cancel,"cancel"),a(nt.prototype.getReader,"getReader"),a(nt.prototype.pipeThrough,"pipeThrough"),a(nt.prototype.pipeTo,"pipeTo"),a(nt.prototype.tee,"tee"),a(nt.prototype.values,"values"),"symbol"==typeof r.toStringTag&&Object.defineProperty(nt.prototype,r.toStringTag,{value:"ReadableStream",configurable:!0}),"symbol"==typeof r.asyncIterator&&Object.defineProperty(nt.prototype,r.asyncIterator,{value:nt.prototype.values,writable:!0,configurable:!0});var dt=function(e){return e.byteLength};a(dt,"size");var ft=function(){function ByteLengthQueuingStrategy(e){Q(e,1,"ByteLengthQueuingStrategy"),e=ct(e,"First parameter"),this._byteLengthQueuingStrategyHighWaterMark=e.highWaterMark}return Object.defineProperty(ByteLengthQueuingStrategy.prototype,"highWaterMark",{get:function(){if(!pt(this))throw bt("highWaterMark");return this._byteLengthQueuingStrategyHighWaterMark},enumerable:!1,configurable:!0}),Object.defineProperty(ByteLengthQueuingStrategy.prototype,"size",{get:function(){if(!pt(this))throw bt("size");return dt},enumerable:!1,configurable:!0}),ByteLengthQueuingStrategy}();function bt(e){return new TypeError("ByteLengthQueuingStrategy.prototype.".concat(e," can only be used on a ByteLengthQueuingStrategy"))}function pt(e){return!!n(e)&&(!!Object.prototype.hasOwnProperty.call(e,"_byteLengthQueuingStrategyHighWaterMark")&&e instanceof ft)}Object.defineProperties(ft.prototype,{highWaterMark:{enumerable:!0},size:{enumerable:!0}}),"symbol"==typeof r.toStringTag&&Object.defineProperty(ft.prototype,r.toStringTag,{value:"ByteLengthQueuingStrategy",configurable:!0});var ht=function(){return 1};a(ht,"size");var _t=function(){function CountQueuingStrategy(e){Q(e,1,"CountQueuingStrategy"),e=ct(e,"First parameter"),this._countQueuingStrategyHighWaterMark=e.highWaterMark}return Object.defineProperty(CountQueuingStrategy.prototype,"highWaterMark",{get:function(){if(!mt(this))throw yt("highWaterMark");return this._countQueuingStrategyHighWaterMark},enumerable:!1,configurable:!0}),Object.defineProperty(CountQueuingStrategy.prototype,"size",{get:function(){if(!mt(this))throw yt("size");return ht},enumerable:!1,configurable:!0}),CountQueuingStrategy}();function yt(e){return new TypeError("CountQueuingStrategy.prototype.".concat(e," can only be used on a CountQueuingStrategy"))}function mt(e){return!!n(e)&&(!!Object.prototype.hasOwnProperty.call(e,"_countQueuingStrategyHighWaterMark")&&e instanceof _t)}function vt(e,r,t){return D(e,t),function(t){return w(e,r,[t])}}function gt(e,r,t){return D(e,t),function(t){return g(e,r,[t])}}function wt(e,r,t){return D(e,t),function(t,n){return w(e,r,[t,n])}}Object.defineProperties(_t.prototype,{highWaterMark:{enumerable:!0},size:{enumerable:!0}}),"symbol"==typeof r.toStringTag&&Object.defineProperty(_t.prototype,r.toStringTag,{value:"CountQueuingStrategy",configurable:!0});var St=function(){function TransformStream(e,r,t){void 0===e&&(e={}),void 0===r&&(r={}),void 0===t&&(t={}),void 0===e&&(e=null);var n=Ue(r,"Second parameter"),o=Ue(t,"Third parameter"),a=function(e,r){I(e,r);var t=null==e?void 0:e.flush,n=null==e?void 0:e.readableType,o=null==e?void 0:e.start,a=null==e?void 0:e.transform,i=null==e?void 0:e.writableType;return{flush:void 0===t?void 0:vt(t,e,"".concat(r," has member 'flush' that")),readableType:n,start:void 0===o?void 0:gt(o,e,"".concat(r," has member 'start' that")),transform:void 0===a?void 0:wt(a,e,"".concat(r," has member 'transform' that")),writableType:i}}(e,"First parameter");if(void 0!==a.readableType)throw new RangeError("Invalid readableType specified");if(void 0!==a.writableType)throw new RangeError("Invalid writableType specified");var i,l=He(o,0),u=Ve(o),s=He(n,1),b=Ve(n);!function(e,r,t,n,o,a){function i(){return r}function l(r){return function(e,r){var t=e._transformStreamController;if(e._backpressure){return y(e._backpressureChangePromise,(function(){if("erroring"===(rr(e._writable)?e._writable._state:e._writableState))throw rr(e._writable)?e._writable._storedError:e._writableStoredError;return kt(t,r)}))}return kt(t,r)}(e,r)}function u(r){return function(e,r){return Tt(e,r),d(void 0)}(e,r)}function s(){return function(e){var r=e._transformStreamController,t=r._flushAlgorithm();return Wt(r),y(t,(function(){if("errored"===e._readableState)throw e._readableStoredError;At(e)&&zt(e)}),(function(r){throw Tt(e,r),e._readableStoredError}))}(e)}function c(){return function(e){return Pt(e,!1),e._backpressureChangePromise}(e)}function f(r){return qt(e,r),d(void 0)}e._writableState="writable",e._writableStoredError=void 0,e._writableHasInFlightOperation=!1,e._writableStarted=!1,e._writable=function(e,r,t,n,o,a,i){return new er({start:function(t){e._writableController=t;try{var n=t.signal;void 0!==n&&n.addEventListener("abort",(function(){"writable"===e._writableState&&(e._writableState="erroring",n.reason&&(e._writableStoredError=n.reason))}))}catch(e){}return y(r(),(function(){return e._writableStarted=!0,Qt(e),null}),(function(r){throw e._writableStarted=!0,It(e,r),r}))},write:function(r){return function(e){e._writableHasInFlightOperation=!0}(e),y(t(r),(function(){return function(e){e._writableHasInFlightOperation=!1}(e),Qt(e),null}),(function(r){throw function(e,r){e._writableHasInFlightOperation=!1,It(e,r)}(e,r),r}))},close:function(){return function(e){e._writableHasInFlightOperation=!0}(e),y(n(),(function(){return function(e){e._writableHasInFlightOperation=!1,"erroring"===e._writableState&&(e._writableStoredError=void 0);e._writableState="closed"}(e),null}),(function(r){throw function(e,r){e._writableHasInFlightOperation=!1,e._writableState,It(e,r)}(e,r),r}))},abort:function(r){return e._writableState="errored",e._writableStoredError=r,o(r)}},{highWaterMark:a,size:i})}(e,i,l,s,u,t,n),e._readableState="readable",e._readableStoredError=void 0,e._readableCloseRequested=!1,e._readablePulling=!1,e._readable=function(e,r,t,n,o,a){return new nt({start:function(t){return e._readableController=t,r().catch((function(r){Lt(e,r)}))},pull:function(){return e._readablePulling=!0,t().catch((function(r){Lt(e,r)}))},cancel:function(r){return e._readableState="closed",n(r)}},{highWaterMark:o,size:a})}(e,i,c,f,o,a),e._backpressure=void 0,e._backpressureChangePromise=void 0,e._backpressureChangePromise_resolve=void 0,Pt(e,!0),e._transformStreamController=void 0}(this,c((function(e){i=e})),s,b,l,u),function(e,r){var t,n,o=Object.create(Ct.prototype);t=void 0!==r.transform?function(e){return r.transform(e,o)}:function(e){try{return Ot(o,e),d(void 0)}catch(e){return f(e)}};n=void 0!==r.flush?function(){return r.flush(o)}:function(){return d(void 0)};!function(e,r,t,n){r._controlledTransformStream=e,e._transformStreamController=r,r._transformAlgorithm=t,r._flushAlgorithm=n}(e,o,t,n)}(this,a),void 0!==a.start?i(a.start(this._transformStreamController)):i(void 0)}return Object.defineProperty(TransformStream.prototype,"readable",{get:function(){if(!Rt(this))throw Bt("readable");return this._readable},enumerable:!1,configurable:!0}),Object.defineProperty(TransformStream.prototype,"writable",{get:function(){if(!Rt(this))throw Bt("writable");return this._writable},enumerable:!1,configurable:!0}),TransformStream}();function Rt(e){return!!n(e)&&(!!Object.prototype.hasOwnProperty.call(e,"_transformStreamController")&&e instanceof St)}function Tt(e,r){Lt(e,r),qt(e,r)}function qt(e,r){Wt(e._transformStreamController),function(e,r){e._writableController.error(r),"writable"===e._writableState&&Dt(e,r)}(e,r),e._backpressure&&Pt(e,!1)}function Pt(e,r){void 0!==e._backpressureChangePromise&&e._backpressureChangePromise_resolve(),e._backpressureChangePromise=c((function(r){e._backpressureChangePromise_resolve=r})),e._backpressure=r}Object.defineProperties(St.prototype,{readable:{enumerable:!0},writable:{enumerable:!0}}),"symbol"==typeof r.toStringTag&&Object.defineProperty(St.prototype,r.toStringTag,{value:"TransformStream",configurable:!0});var Ct=function(){function TransformStreamDefaultController(){throw new TypeError("Illegal constructor")}return Object.defineProperty(TransformStreamDefaultController.prototype,"desiredSize",{get:function(){if(!Et(this))throw jt("desiredSize");return Ft(this._controlledTransformStream)},enumerable:!1,configurable:!0}),TransformStreamDefaultController.prototype.enqueue=function(e){if(void 0===e&&(e=void 0),!Et(this))throw jt("enqueue");Ot(this,e)},TransformStreamDefaultController.prototype.error=function(e){if(void 0===e&&(e=void 0),!Et(this))throw jt("error");var r;r=e,Tt(this._controlledTransformStream,r)},TransformStreamDefaultController.prototype.terminate=function(){if(!Et(this))throw jt("terminate");!function(e){var r=e._controlledTransformStream;At(r)&&zt(r);var t=new TypeError("TransformStream terminated");qt(r,t)}(this)},TransformStreamDefaultController}();function Et(e){return!!n(e)&&(!!Object.prototype.hasOwnProperty.call(e,"_controlledTransformStream")&&e instanceof Ct)}function Wt(e){e._transformAlgorithm=void 0,e._flushAlgorithm=void 0}function Ot(e,r){var t=e._controlledTransformStream;if(!At(t))throw new TypeError("Readable side is not in a state that permits enqueue");try{!function(e,r){e._readablePulling=!1;try{e._readableController.enqueue(r)}catch(r){throw Lt(e,r),r}}(t,r)}catch(e){throw qt(t,e),t._readableStoredError}var n=function(e){return!function(e){if(!At(e))return!1;if(e._readablePulling)return!0;if(Ft(e)>0)return!0;return!1}(e)}(t);n!==t._backpressure&&Pt(t,!0)}function kt(e,r){return y(e._transformAlgorithm(r),void 0,(function(r){throw Tt(e._controlledTransformStream,r),r}))}function jt(e){return new TypeError("TransformStreamDefaultController.prototype.".concat(e," can only be used on a TransformStreamDefaultController"))}function Bt(e){return new TypeError("TransformStream.prototype.".concat(e," can only be used on a TransformStream"))}function At(e){return!e._readableCloseRequested&&"readable"===e._readableState}function zt(e){e._readableState="closed",e._readableCloseRequested=!0,e._readableController.close()}function Lt(e,r){"readable"===e._readableState&&(e._readableState="errored",e._readableStoredError=r),e._readableController.error(r)}function Ft(e){return e._readableController.desiredSize}function It(e,r){"writable"!==e._writableState?Mt(e):Dt(e,r)}function Dt(e,r){e._writableState="erroring",e._writableStoredError=r,!function(e){return e._writableHasInFlightOperation}(e)&&e._writableStarted&&Mt(e)}function Mt(e){e._writableState="errored"}function Qt(e){"erroring"===e._writableState&&Mt(e)}Object.defineProperties(Ct.prototype,{enqueue:{enumerable:!0},error:{enumerable:!0},terminate:{enumerable:!0},desiredSize:{enumerable:!0}}),a(Ct.prototype.enqueue,"enqueue"),a(Ct.prototype.error,"error"),a(Ct.prototype.terminate,"terminate"),"symbol"==typeof r.toStringTag&&Object.defineProperty(Ct.prototype,r.toStringTag,{value:"TransformStreamDefaultController",configurable:!0}),e.ByteLengthQueuingStrategy=ft,e.CountQueuingStrategy=_t,e.ReadableByteStreamController=pe,e.ReadableStream=nt,e.ReadableStreamBYOBReader=Qe,e.ReadableStreamBYOBRequest=be,e.ReadableStreamDefaultController=Qr,e.ReadableStreamDefaultReader=$,e.TransformStream=St,e.TransformStreamDefaultController=Ct,e.WritableStream=er,e.WritableStreamDefaultController=hr,e.WritableStreamDefaultWriter=dr,Object.defineProperty(e,"__esModule",{value:!0})}));
