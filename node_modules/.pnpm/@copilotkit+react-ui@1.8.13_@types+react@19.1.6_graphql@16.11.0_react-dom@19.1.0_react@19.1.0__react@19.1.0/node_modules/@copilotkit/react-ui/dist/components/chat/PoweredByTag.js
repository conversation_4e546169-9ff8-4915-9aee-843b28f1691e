"use strict";
var __defProp = Object.defineProperty;
var __getOwnPropDesc = Object.getOwnPropertyDescriptor;
var __getOwnPropNames = Object.getOwnPropertyNames;
var __hasOwnProp = Object.prototype.hasOwnProperty;
var __export = (target, all) => {
  for (var name in all)
    __defProp(target, name, { get: all[name], enumerable: true });
};
var __copyProps = (to, from, except, desc) => {
  if (from && typeof from === "object" || typeof from === "function") {
    for (let key of __getOwnPropNames(from))
      if (!__hasOwnProp.call(to, key) && key !== except)
        __defProp(to, key, { get: () => from[key], enumerable: !(desc = __getOwnPropDesc(from, key)) || desc.enumerable });
  }
  return to;
};
var __toCommonJS = (mod) => __copyProps(__defProp({}, "__esModule", { value: true }), mod);

// src/components/chat/PoweredByTag.tsx
var PoweredByTag_exports = {};
__export(PoweredByTag_exports, {
  PoweredByTag: () => PoweredByTag
});
module.exports = __toCommonJS(PoweredByTag_exports);

// src/hooks/use-dark-mode.ts
var useDarkMode = () => {
  if (typeof window === "undefined")
    return false;
  return document.documentElement.classList.contains("dark") || document.body.classList.contains("dark") || document.documentElement.getAttribute("data-theme") === "dark" || document.body.getAttribute("data-theme") === "dark" || window.matchMedia("(prefers-color-scheme: dark)").matches;
};

// src/components/chat/PoweredByTag.tsx
var import_jsx_runtime = require("react/jsx-runtime");
function PoweredByTag({ showPoweredBy = true }) {
  const isDark = useDarkMode();
  if (!showPoweredBy) {
    return null;
  }
  const poweredByStyle = {
    visibility: "visible",
    display: "block",
    position: "static",
    textAlign: "center",
    fontSize: "12px",
    padding: "3px 0",
    color: isDark ? "rgb(69, 69, 69)" : "rgb(214, 214, 214)"
  };
  return /* @__PURE__ */ (0, import_jsx_runtime.jsx)("div", { children: /* @__PURE__ */ (0, import_jsx_runtime.jsx)("p", { className: "poweredBy", style: poweredByStyle, children: "Powered by CopilotKit" }) });
}
// Annotate the CommonJS export names for ESM import in node:
0 && (module.exports = {
  PoweredByTag
});
//# sourceMappingURL=PoweredByTag.js.map