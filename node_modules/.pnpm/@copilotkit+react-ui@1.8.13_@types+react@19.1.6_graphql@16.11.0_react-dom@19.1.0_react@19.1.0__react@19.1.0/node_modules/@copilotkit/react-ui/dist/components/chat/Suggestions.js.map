{"version": 3, "sources": ["../../../src/components/chat/Suggestions.tsx", "../../../src/components/chat/Suggestion.tsx", "../../../src/components/chat/Icons.tsx"], "sourcesContent": ["import { Suggestion } from \"./Suggestion\";\nimport React from \"react\";\nimport { RenderSuggestionsListProps } from \"./props\";\n\nexport function Suggestions({ suggestions, onSuggestionClick }: RenderSuggestionsListProps) {\n  return (\n    <div className=\"suggestions\">\n      {suggestions.map((suggestion, index) => (\n        <Suggestion\n          key={index}\n          title={suggestion.title}\n          message={suggestion.message}\n          partial={suggestion.partial}\n          className={suggestion.className}\n          onClick={() => onSuggestionClick(suggestion.message)}\n        />\n      ))}\n    </div>\n  );\n}\n", "import {\n  CopilotContextParams,\n  extract,\n  CopilotChatSuggestionConfiguration,\n  CopilotMessagesContextParams,\n} from \"@copilotkit/react-core\";\nimport { SmallSpinnerIcon } from \"./Icons\";\nimport { CopilotChatSuggestion } from \"../../types/suggestions\";\nimport { actionParametersToJsonSchema } from \"@copilotkit/shared\";\nimport { CopilotRequestType } from \"@copilotkit/runtime-client-gql\";\n\ninterface SuggestionsProps {\n  title: string;\n  message: string;\n  partial?: boolean;\n  className?: string;\n  onClick: () => void;\n}\n\nexport function Suggestion({ title, onClick, partial, className }: SuggestionsProps) {\n  return (\n    <button\n      disabled={partial}\n      onClick={(e) => {\n        e.preventDefault();\n        onClick();\n      }}\n      className={className || (partial ? \"suggestion loading\" : \"suggestion\")}\n      data-test-id=\"suggestion\"\n    >\n      {partial ? SmallSpinnerIcon : <span>{title}</span>}\n    </button>\n  );\n}\n\nexport const reloadSuggestions = async (\n  context: CopilotContextParams & CopilotMessagesContextParams,\n  chatSuggestionConfiguration: { [key: string]: CopilotChatSuggestionConfiguration },\n  setCurrentSuggestions: (suggestions: { title: string; message: string }[]) => void,\n  abortControllerRef: React.MutableRefObject<AbortController | null>,\n) => {\n  const abortController = abortControllerRef.current;\n\n  const tools = JSON.stringify(\n    Object.values(context.actions).map((action) => ({\n      name: action.name,\n      description: action.description,\n      jsonSchema: JSON.stringify(actionParametersToJsonSchema(action.parameters)),\n    })),\n  );\n\n  const allSuggestions: CopilotChatSuggestion[] = [];\n\n  for (const config of Object.values(chatSuggestionConfiguration)) {\n    try {\n      const numOfSuggestionsInstructions =\n        config.minSuggestions === 0\n          ? `Produce up to ${config.maxSuggestions} suggestions. ` +\n            `If there are no highly relevant suggestions you can think of, provide an empty array.`\n          : `Produce between ${config.minSuggestions} and ${config.maxSuggestions} suggestions.`;\n\n      const result = await extract({\n        context,\n        instructions:\n          \"Suggest what the user could say next. Provide clear, highly relevant suggestions. Do not literally suggest function calls. \",\n        data:\n          config.instructions +\n          \"\\n\\n\" +\n          numOfSuggestionsInstructions +\n          \"\\n\\n\" +\n          \"Available tools: \" +\n          tools +\n          \"\\n\\n\",\n        requestType: CopilotRequestType.Task,\n        parameters: [\n          {\n            name: \"suggestions\",\n            type: \"object[]\",\n            attributes: [\n              {\n                name: \"title\",\n                description:\n                  \"The title of the suggestion. This is shown as a button and should be short.\",\n                type: \"string\",\n              },\n              {\n                name: \"message\",\n                description:\n                  \"The message to send when the suggestion is clicked. This should be a clear, complete sentence and will be sent as an instruction to the AI.\",\n                type: \"string\",\n              },\n            ],\n          },\n        ],\n        include: {\n          messages: true,\n          readable: true,\n        },\n        abortSignal: abortController?.signal,\n        stream: ({ status, args }) => {\n          const suggestions = args.suggestions || [];\n          const newSuggestions: CopilotChatSuggestion[] = [];\n          for (let i = 0; i < suggestions.length; i++) {\n            // if GPT provides too many suggestions, limit the number of suggestions\n            if (config.maxSuggestions !== undefined && i >= config.maxSuggestions) {\n              break;\n            }\n            const { title, message } = suggestions[i];\n\n            // If this is the last suggestion and the status is not complete, mark it as partial\n            const partial = i == suggestions.length - 1 && status !== \"complete\";\n\n            newSuggestions.push({\n              title,\n              message,\n              partial,\n              className: config.className,\n            });\n          }\n          setCurrentSuggestions([...allSuggestions, ...newSuggestions]);\n        },\n      });\n      allSuggestions.push(...result.suggestions);\n    } catch (error) {\n      console.error(\"Error loading suggestions\", error);\n    }\n  }\n\n  if (abortControllerRef.current === abortController) {\n    abortControllerRef.current = null;\n  }\n};\n", "import React from \"react\";\n\nexport const OpenIcon = (\n  <svg\n    xmlns=\"http://www.w3.org/2000/svg\"\n    viewBox=\"0 0 24 24\"\n    fill=\"currentColor\"\n    width=\"24\"\n    height=\"24\"\n  >\n    <g transform=\"translate(24, 0) scale(-1, 1)\">\n      <path\n        fillRule=\"evenodd\"\n        d=\"M5.337 21.718a6.707 6.707 0 01-.533-.074.75.75 0 01-.44-1.223 3.73 3.73 0 00.814-1.686c.023-.115-.022-.317-.254-.543C3.274 16.587 2.25 14.41 2.25 12c0-5.03 4.428-9 9.75-9s9.75 3.97 9.75 9c0 5.03-4.428 9-9.75 9-.833 0-1.643-.097-2.417-.279a6.721 6.721 0 01-4.246.997z\"\n        clipRule=\"evenodd\"\n      />\n    </g>\n  </svg>\n);\n\nexport const CloseIcon = (\n  <svg\n    xmlns=\"http://www.w3.org/2000/svg\"\n    fill=\"none\"\n    viewBox=\"0 0 24 24\"\n    strokeWidth=\"1.5\"\n    stroke=\"currentColor\"\n    width=\"24\"\n    height=\"24\"\n  >\n    <path strokeLinecap=\"round\" strokeLinejoin=\"round\" d=\"M19.5 8.25l-7.5 7.5-7.5-7.5\" />\n  </svg>\n);\n\nexport const HeaderCloseIcon = (\n  <svg\n    xmlns=\"http://www.w3.org/2000/svg\"\n    fill=\"none\"\n    viewBox=\"0 0 24 24\"\n    strokeWidth=\"1.5\"\n    stroke=\"currentColor\"\n    width=\"24\"\n    height=\"24\"\n  >\n    <path strokeLinecap=\"round\" strokeLinejoin=\"round\" d=\"M6 18L18 6M6 6l12 12\" />\n  </svg>\n);\n\nexport const SendIcon = (\n  <svg\n    xmlns=\"http://www.w3.org/2000/svg\"\n    fill=\"none\"\n    viewBox=\"0 0 24 24\"\n    strokeWidth=\"1.5\"\n    stroke=\"currentColor\"\n    width=\"24\"\n    height=\"24\"\n  >\n    <path strokeLinecap=\"round\" strokeLinejoin=\"round\" d=\"M12 19V5m0 0l-7 7m7-7l7 7\" />\n  </svg>\n);\n\nexport const MicrophoneIcon = (\n  <svg\n    xmlns=\"http://www.w3.org/2000/svg\"\n    fill=\"none\"\n    viewBox=\"0 0 24 24\"\n    strokeWidth=\"1.5\"\n    stroke=\"currentColor\"\n    width=\"24\"\n    height=\"24\"\n  >\n    <path\n      strokeLinecap=\"round\"\n      strokeLinejoin=\"round\"\n      d=\"M12 18.75a6 6 0 006-6v-1.5m-6 7.5a6 6 0 01-6-6v-1.5m6 7.5v3.75m-3.75 0h7.5M12 15.75a3 3 0 01-3-3V4.5a3 3 0 116 0v8.25a3 3 0 01-3 3z\"\n    />\n  </svg>\n);\n\nexport const StopIcon = (\n  <svg\n    xmlns=\"http://www.w3.org/2000/svg\"\n    fill=\"none\"\n    viewBox=\"0 0 24 24\"\n    strokeWidth=\"1.5\"\n    stroke=\"currentColor\"\n    width=\"24\"\n    height=\"24\"\n  >\n    <path\n      strokeLinecap=\"round\"\n      strokeLinejoin=\"round\"\n      d=\"M5.25 7.5A2.25 2.25 0 017.5 5.25h9a2.25 2.25 0 012.25 2.25v9a2.25 2.25 0 01-2.25 2.25h-9a2.25 2.25 0 01-2.25-2.25v-9z\"\n    />\n  </svg>\n);\n\nexport const RegenerateIcon = (\n  <svg\n    xmlns=\"http://www.w3.org/2000/svg\"\n    fill=\"none\"\n    viewBox=\"0 0 24 24\"\n    strokeWidth=\"2\"\n    stroke=\"currentColor\"\n    width=\"16\"\n    height=\"16\"\n    style={{ minWidth: \"16px\", minHeight: \"16px\" }}\n  >\n    <path\n      strokeLinecap=\"round\"\n      strokeLinejoin=\"round\"\n      d=\"M16.023 9.348h4.992v-.001M2.985 19.644v-4.992m0 0h4.992m-4.993 0l3.181 3.183a8.25 8.25 0 0013.803-3.7M4.031 9.865a8.25 8.25 0 0113.803-3.7l3.181 3.182m0-4.991v4.99\"\n    />\n  </svg>\n);\n\nexport const CopyIcon = (\n  <svg\n    xmlns=\"http://www.w3.org/2000/svg\"\n    fill=\"none\"\n    viewBox=\"0 0 24 24\"\n    strokeWidth=\"2\"\n    stroke=\"currentColor\"\n    width=\"16\"\n    height=\"16\"\n    style={{ minWidth: \"16px\", minHeight: \"16px\" }}\n  >\n    <path\n      strokeLinecap=\"round\"\n      strokeLinejoin=\"round\"\n      d=\"M15.75 17.25v3.375c0 .621-.504 1.125-1.125 1.125h-9.75a1.125 1.125 0 01-1.125-1.125V7.875c0-.621.504-1.125 1.125-1.125H6.75a9.06 9.06 0 011.5.124m7.5 10.376h3.375c.621 0 1.125-.504 1.125-1.125V11.25c0-4.46-3.243-8.161-7.5-8.876a9.06 9.06 0 00-1.5-.124H9.375c-.621 0-1.125.504-1.125 1.125v3.5m7.5 10.375H9.375a1.125 1.125 0 01-1.125-1.125v-9.25m12 6.625v-1.875a3.375 3.375 0 00-3.375-3.375h-1.5a1.125 1.125 0 01-1.125-1.125v-1.5a3.375 3.375 0 00-3.375-3.375H9.75\"\n    />\n  </svg>\n);\n\nexport const SmallSpinnerIcon = (\n  <span className=\"copilotKitSpinner\" style={{ width: \"13px\", height: \"13px\" }}></span>\n);\n\nexport const SpinnerIcon = (\n  <span className=\"copilotKitSpinner\" style={{ width: \"24px\", height: \"24px\" }}></span>\n);\n\nexport const ActivityIcon = (\n  <div style={{ display: \"flex\", alignItems: \"center\", gap: \"4px\" }}>\n    <span className=\"copilotKitActivityDot\" style={{ animationDelay: \"0s\" }}></span>\n    <span className=\"copilotKitActivityDot\" style={{ animationDelay: \"0.2s\" }}></span>\n    <span className=\"copilotKitActivityDot\" style={{ animationDelay: \"0.4s\" }}></span>\n  </div>\n);\n\nexport const ThumbsUpIcon = (\n  <svg\n    xmlns=\"http://www.w3.org/2000/svg\"\n    fill=\"none\"\n    viewBox=\"0 0 24 24\"\n    strokeWidth=\"2\"\n    stroke=\"currentColor\"\n    width=\"16\"\n    height=\"16\"\n    style={{ minWidth: \"16px\", minHeight: \"16px\" }}\n  >\n    <path\n      strokeLinecap=\"round\"\n      strokeLinejoin=\"round\"\n      d=\"M6.633 10.5c.806 0 1.533-.446 2.031-1.08a9.041 9.041 0 012.861-2.4c.723-.384 1.35-.956 1.653-1.715a4.498 4.498 0 00.322-1.672V3a.75.75 0 01.75-.75A2.25 2.25 0 0116.5 4.5c0 1.152-.26 2.243-.723 3.218-.266.558.107 1.282.725 1.282h3.126c1.026 0 1.945.694 2.054 1.715.045.422.068.85.068 1.285a11.95 11.95 0 01-2.649 7.521c-.388.482-.987.729-1.605.729H13.48c-.483 0-.964-.078-1.423-.23l-3.114-1.04a4.501 4.501 0 00-1.423-.23H5.904M14.25 9h2.25M5.904 18.75c.083.205.173.405.27.602.197.4-.078.898-.523.898h-.908c-.889 0-1.713-.518-1.972-1.368a12 12 0 01-.521-3.507c0-1.553.295-3.036.831-4.398C3.387 10.203 4.167 9.75 5 9.75h1.053c.472 0 .745.556.5.96a8.958 8.958 0 00-1.302 4.665c0 1.194.232 2.333.654 3.375z\"\n    />\n  </svg>\n);\n\nexport const ThumbsDownIcon = (\n  <svg\n    xmlns=\"http://www.w3.org/2000/svg\"\n    fill=\"none\"\n    viewBox=\"0 0 24 24\"\n    strokeWidth=\"2\"\n    stroke=\"currentColor\"\n    width=\"16\"\n    height=\"16\"\n    style={{ minWidth: \"16px\", minHeight: \"16px\" }}\n  >\n    <path\n      strokeLinecap=\"round\"\n      strokeLinejoin=\"round\"\n      d=\"M7.5 15h2.25m8.024-9.75c.011.05.028.1.052.148.591 1.2.924 2.55.924 3.977a8.96 8.96 0 01-.999 4.125m.023-8.25c-.076-.365.183-.75.575-.75h.908c.889 0 1.713.518 1.972 1.368.339 1.11.521 2.287.521 3.507 0 1.553-.295 3.036-.831 4.398C20.613 14.547 19.833 15 19 15h-1.053c-.472 0-.745-.556-.5-.96a8.95 8.95 0 00.303-.54m.023-8.25H16.48a4.5 4.5 0 01-1.423-.23l-3.114-1.04a4.5 4.5 0 00-1.423-.23H6.504c-.618 0-1.217.247-1.605.729A11.95 11.95 0 002.25 12c0 .434.023.863.068 1.285C2.427 14.306 3.346 15 4.372 15h3.126c.618 0 .991.724.725 1.282A7.471 7.471 0 007.5 19.5a2.25 2.25 0 002.25 2.25.75.75 0 00.75-.75v-.633c0-.573.11-1.14.322-1.672.304-.76.93-1.33 1.653-1.715a9.04 9.04 0 002.86-2.4c.498-.634 1.226-1.08 2.032-1.08h.384\"\n    />\n  </svg>\n);\n\nexport const DownloadIcon = (\n  <svg\n    xmlns=\"http://www.w3.org/2000/svg\"\n    fill=\"none\"\n    viewBox=\"0 0 24 24\"\n    strokeWidth=\"2\"\n    stroke=\"currentColor\"\n    width=\"16\"\n    height=\"16\"\n    style={{ minWidth: \"16px\", minHeight: \"16px\" }}\n  >\n    <path\n      strokeLinecap=\"round\"\n      strokeLinejoin=\"round\"\n      d=\"M3 16.5v2.25A2.25 2.25 0 005.25 21h13.5A2.25 2.25 0 0021 18.75V16.5M16.5 12L12 16.5m0 0L7.5 12m4.5 4.5V3\"\n    />\n  </svg>\n);\n\nexport const UploadIcon = (\n  <svg\n    xmlns=\"http://www.w3.org/2000/svg\"\n    fill=\"none\"\n    viewBox=\"0 0 24 24\"\n    strokeWidth=\"1.5\"\n    stroke=\"currentColor\"\n    width=\"24\"\n    height=\"24\"\n  >\n    <path strokeLinecap=\"round\" strokeLinejoin=\"round\" d=\"M12 4.5v15m7.5-7.5h-15\" />\n  </svg>\n);\n\nexport const CheckIcon = (\n  <svg\n    xmlns=\"http://www.w3.org/2000/svg\"\n    fill=\"none\"\n    viewBox=\"0 0 24 24\"\n    strokeWidth=\"2\"\n    stroke=\"currentColor\"\n    width=\"16\"\n    height=\"16\"\n    style={{ minWidth: \"16px\", minHeight: \"16px\" }}\n  >\n    <path strokeLinecap=\"round\" strokeLinejoin=\"round\" d=\"M4.5 12.75l6 6 9-13.5\" />\n  </svg>\n);\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;;;ACAA,wBAKO;;;ACMD;AA6HC,IAAM,mBACX,4CAAC,UAAK,WAAU,qBAAoB,OAAO,EAAE,OAAO,QAAQ,QAAQ,OAAO,GAAG;;;ADjIhF,oBAA6C;AAC7C,gCAAmC;AAqBC,IAAAA,sBAAA;AAX7B,SAAS,WAAW,EAAE,OAAO,SAAS,SAAS,UAAU,GAAqB;AACnF,SACE;AAAA,IAAC;AAAA;AAAA,MACC,UAAU;AAAA,MACV,SAAS,CAAC,MAAM;AACd,UAAE,eAAe;AACjB,gBAAQ;AAAA,MACV;AAAA,MACA,WAAW,cAAc,UAAU,uBAAuB;AAAA,MAC1D,gBAAa;AAAA,MAEZ,oBAAU,mBAAmB,6CAAC,UAAM,iBAAM;AAAA;AAAA,EAC7C;AAEJ;;;ADzBQ,IAAAC,sBAAA;AAJD,SAAS,YAAY,EAAE,aAAa,kBAAkB,GAA+B;AAC1F,SACE,6CAAC,SAAI,WAAU,eACZ,sBAAY,IAAI,CAAC,YAAY,UAC5B;AAAA,IAAC;AAAA;AAAA,MAEC,OAAO,WAAW;AAAA,MAClB,SAAS,WAAW;AAAA,MACpB,SAAS,WAAW;AAAA,MACpB,WAAW,WAAW;AAAA,MACtB,SAAS,MAAM,kBAAkB,WAAW,OAAO;AAAA;AAAA,IAL9C;AAAA,EAMP,CACD,GACH;AAEJ;", "names": ["import_jsx_runtime", "import_jsx_runtime"]}