{"version": 3, "sources": ["../../../src/components/chat/Modal.tsx", "../../../src/components/chat/ChatContext.tsx", "../../../src/components/chat/Icons.tsx", "../../../src/components/chat/Window.tsx", "../../../src/components/chat/Button.tsx", "../../../src/components/dev-console/utils.ts", "../../../src/components/dev-console/console.tsx", "../../../src/components/dev-console/icons.tsx", "../../../src/components/help-modal/modal.tsx", "../../../src/components/help-modal/icons.tsx", "../../../src/components/chat/Header.tsx", "../../../src/components/chat/Messages.tsx", "../../../src/components/chat/Input.tsx", "../../../src/components/chat/Textarea.tsx", "../../../src/hooks/use-push-to-talk.tsx", "../../../src/hooks/use-dark-mode.ts", "../../../src/components/chat/PoweredByTag.tsx", "../../../src/components/chat/messages/UserMessage.tsx", "../../../src/components/chat/Markdown.tsx", "../../../src/components/chat/CodeBlock.tsx", "../../../src/hooks/use-copy-to-clipboard.tsx", "../../../src/components/chat/messages/AssistantMessage.tsx", "../../../src/components/chat/messages/RenderTextMessage.tsx", "../../../src/components/chat/messages/RenderActionExecutionMessage.tsx", "../../../src/components/chat/messages/RenderResultMessage.tsx", "../../../src/components/chat/messages/RenderAgentStateMessage.tsx", "../../../src/components/chat/messages/RenderImageMessage.tsx", "../../../src/components/chat/Chat.tsx", "../../../src/components/chat/Suggestion.tsx", "../../../src/components/chat/ImageUploadQueue.tsx", "../../../src/components/chat/Suggestions.tsx"], "sourcesContent": ["import React from \"react\";\nimport { ChatContextProvider } from \"./ChatContext\";\nimport { ButtonProps, HeaderProps, WindowProps } from \"./props\";\nimport { Window as DefaultWindow } from \"./Window\";\nimport { Button as DefaultButton } from \"./Button\";\nimport { Header as DefaultHeader } from \"./Header\";\nimport { Messages as DefaultMessages } from \"./Messages\";\nimport { Input as DefaultInput } from \"./Input\";\nimport { CopilotChat, CopilotChatProps } from \"./Chat\";\nimport { AssistantMessage as DefaultAssistantMessage } from \"./messages/AssistantMessage\";\nimport { UserMessage as DefaultUserMessage } from \"./messages/UserMessage\";\n\nexport interface CopilotModalProps extends CopilotChatProps {\n  /**\n   * Whether the chat window should be open by default.\n   * @default false\n   */\n  defaultOpen?: boolean;\n\n  /**\n   * If the chat window should close when the user clicks outside of it.\n   * @default true\n   */\n  clickOutsideToClose?: boolean;\n\n  /**\n   * If the chat window should close when the user hits the Escape key.\n   * @default true\n   */\n  hitEscapeToClose?: boolean;\n\n  /**\n   * The shortcut key to open the chat window.\n   * Uses Command-[shortcut] on a Mac and Ctrl-[shortcut] on Windows.\n   * @default '/'\n   */\n  shortcut?: string;\n\n  /**\n   * A callback that gets called when the chat window opens or closes.\n   */\n  onSetOpen?: (open: boolean) => void;\n\n  /**\n   * A custom Window component to use instead of the default.\n   */\n  Window?: React.ComponentType<WindowProps>;\n\n  /**\n   * A custom Button component to use instead of the default.\n   */\n  Button?: React.ComponentType<ButtonProps>;\n\n  /**\n   * A custom Header component to use instead of the default.\n   */\n  Header?: React.ComponentType<HeaderProps>;\n}\n\nexport const CopilotModal = ({\n  instructions,\n  defaultOpen = false,\n  clickOutsideToClose = true,\n  hitEscapeToClose = true,\n  onSetOpen,\n  onSubmitMessage,\n  onStopGeneration,\n  onReloadMessages,\n  shortcut = \"/\",\n  icons,\n  labels,\n  makeSystemMessage,\n  onInProgress,\n  Window = DefaultWindow,\n  Button = DefaultButton,\n  Header = DefaultHeader,\n  Messages = DefaultMessages,\n  Input = DefaultInput,\n  AssistantMessage = DefaultAssistantMessage,\n  UserMessage = DefaultUserMessage,\n  onThumbsUp,\n  onThumbsDown,\n  onCopy,\n  onRegenerate,\n  markdownTagRenderers,\n  className,\n  children,\n  ...props\n}: CopilotModalProps) => {\n  const [openState, setOpenState] = React.useState(defaultOpen);\n\n  const setOpen = (open: boolean) => {\n    onSetOpen?.(open);\n    setOpenState(open);\n  };\n\n  return (\n    <ChatContextProvider icons={icons} labels={labels} open={openState} setOpen={setOpen}>\n      {children}\n      <div className={className}>\n        <Button></Button>\n        <Window\n          clickOutsideToClose={clickOutsideToClose}\n          shortcut={shortcut}\n          hitEscapeToClose={hitEscapeToClose}\n        >\n          <Header />\n          <CopilotChat\n            {...props}\n            instructions={instructions}\n            onSubmitMessage={onSubmitMessage}\n            onStopGeneration={onStopGeneration}\n            onReloadMessages={onReloadMessages}\n            makeSystemMessage={makeSystemMessage}\n            onInProgress={onInProgress}\n            Messages={Messages}\n            Input={Input}\n            AssistantMessage={AssistantMessage}\n            UserMessage={UserMessage}\n            onThumbsUp={onThumbsUp}\n            onThumbsDown={onThumbsDown}\n            onCopy={onCopy}\n            onRegenerate={onRegenerate}\n            markdownTagRenderers={markdownTagRenderers}\n          />\n        </Window>\n      </div>\n    </ChatContextProvider>\n  );\n};\n", "import React, { use<PERSON>em<PERSON>, useState } from \"react\";\nimport * as DefaultIcons from \"./Icons\";\nimport { ThumbsDownIcon, ThumbsUpIcon } from \"./Icons\";\n\n/**\n * Icons for CopilotChat component.\n */\nexport interface CopilotChatIcons {\n  /**\n   * The icon to use for the open chat button.\n   * @default <OpenIcon />\n   */\n  openIcon?: React.ReactNode;\n\n  /**\n   * The icon to use for the close chat button.\n   * @default <CloseIcon />\n   */\n  closeIcon?: React.ReactNode;\n\n  /**\n   * The icon to use for the close chat button in the header.\n   * @default <HeaderCloseIcon />\n   */\n  headerCloseIcon?: React.ReactNode;\n\n  /**\n   * The icon to use for the send button.\n   * @default <SendIcon />\n   */\n  sendIcon?: React.ReactNode;\n\n  /**\n   * The icon to use for the activity indicator.\n   * @default <ActivityIcon />\n   */\n  activityIcon?: React.ReactNode;\n\n  /**\n   * The icon to use for the spinner.\n   * @default <SpinnerIcon />\n   */\n  spinnerIcon?: React.ReactNode;\n\n  /**\n   * The icon to use for the stop button.\n   * @default <StopIcon />\n   */\n  stopIcon?: React.ReactNode;\n\n  /**\n   * The icon to use for the regenerate button.\n   * @default <RegenerateIcon />\n   */\n  regenerateIcon?: React.ReactNode;\n\n  /**\n   * The icons to use for push to talk.\n   * @default <PushToTalkIcon />\n   */\n\n  pushToTalkIcon?: React.ReactNode;\n\n  /**\n   * The icons to use for copy assistant response\n   * @default <CopyIcon />\n   */\n\n  copyIcon?: React.ReactNode;\n\n  /**\n   * The icon to use for thumbs up/response approval.\n   * @default <ThumbsUpIcon />\n   */\n\n  thumbsUpIcon?: React.ReactNode;\n\n  /**\n   * The icon to use for thumbs down/response rejection.\n   * @default <ThumbsDownIcon />\n   */\n\n  thumbsDownIcon?: React.ReactNode;\n\n  /**\n   * The icon to use for the upload button.\n   * @default <UploadIcon />\n   */\n  uploadIcon?: React.ReactNode;\n}\n\n/**\n * Labels for CopilotChat component.\n */\nexport interface CopilotChatLabels {\n  /**\n   * The initial message(s) to display in the chat window.\n   */\n  initial?: string | string[];\n\n  /**\n   * The title to display in the header.\n   * @default \"CopilotKit\"\n   */\n  title?: string;\n\n  /**\n   * The placeholder to display in the input.\n   * @default \"Type a message...\"\n   */\n  placeholder?: string;\n\n  /**\n   * The message to display when an error occurs.\n   * @default \"❌ An error occurred. Please try again.\"\n   */\n  error?: string;\n\n  /**\n   * The label to display on the stop button.\n   * @default \"Stop generating\"\n   */\n  stopGenerating?: string;\n\n  /**\n   * The label to display on the regenerate button.\n   * @default \"Regenerate response\"\n   */\n  regenerateResponse?: string;\n\n  /**\n   * The label for the copy button.\n   * @default \"Copy to clipboard\"\n   */\n  copyToClipboard?: string;\n\n  /**\n   * The label for the thumbs up button.\n   * @default \"Thumbs up\"\n   */\n  thumbsUp?: string;\n\n  /**\n   * The label for the thumbs down button.\n   * @default \"Thumbs down\"\n   */\n  thumbsDown?: string;\n\n  /**\n   * The text to display when content is copied.\n   * @default \"Copied!\"\n   */\n  copied?: string;\n}\n\ninterface ChatContext {\n  labels: Required<CopilotChatLabels>;\n  icons: Required<CopilotChatIcons>;\n  open: boolean;\n  setOpen: (open: boolean) => void;\n}\n\nexport const ChatContext = React.createContext<ChatContext | undefined>(undefined);\n\nexport function useChatContext(): ChatContext {\n  const context = React.useContext(ChatContext);\n  if (context === undefined) {\n    throw new Error(\n      \"Context not found. Did you forget to wrap your app in a <ChatContextProvider> component?\",\n    );\n  }\n  return context;\n}\n\ninterface ChatContextProps {\n  // temperature?: number;\n  // instructions?: string;\n  // maxFeedback?: number;\n  labels?: CopilotChatLabels;\n  icons?: CopilotChatIcons;\n  children?: React.ReactNode;\n  open: boolean;\n  setOpen: (open: boolean) => void;\n}\n\nexport const ChatContextProvider = ({\n  // temperature,\n  // instructions,\n  // maxFeedback,\n  labels,\n  icons,\n  children,\n  open,\n  setOpen,\n}: ChatContextProps) => {\n  const memoizedLabels = useMemo(\n    () => ({\n      ...{\n        initial: \"\",\n        title: \"CopilotKit\",\n        placeholder: \"Type a message...\",\n        error: \"❌ An error occurred. Please try again.\",\n        stopGenerating: \"Stop generating\",\n        regenerateResponse: \"Regenerate response\",\n        copyToClipboard: \"Copy to clipboard\",\n        thumbsUp: \"Thumbs up\",\n        thumbsDown: \"Thumbs down\",\n        copied: \"Copied!\",\n      },\n      ...labels,\n    }),\n    [labels],\n  );\n\n  const memoizedIcons = useMemo(\n    () => ({\n      ...{\n        openIcon: DefaultIcons.OpenIcon,\n        closeIcon: DefaultIcons.CloseIcon,\n        headerCloseIcon: DefaultIcons.HeaderCloseIcon,\n        sendIcon: DefaultIcons.SendIcon,\n        activityIcon: DefaultIcons.ActivityIcon,\n        spinnerIcon: DefaultIcons.SpinnerIcon,\n        stopIcon: DefaultIcons.StopIcon,\n        regenerateIcon: DefaultIcons.RegenerateIcon,\n        pushToTalkIcon: DefaultIcons.MicrophoneIcon,\n        copyIcon: DefaultIcons.CopyIcon,\n        thumbsUpIcon: DefaultIcons.ThumbsUpIcon,\n        thumbsDownIcon: DefaultIcons.ThumbsDownIcon,\n        uploadIcon: DefaultIcons.UploadIcon,\n      },\n      ...icons,\n    }),\n    [icons],\n  );\n\n  const context = useMemo(\n    () => ({\n      labels: memoizedLabels,\n      icons: memoizedIcons,\n      open,\n      setOpen,\n    }),\n    [memoizedLabels, memoizedIcons, open, setOpen],\n  );\n\n  return <ChatContext.Provider value={context}>{children}</ChatContext.Provider>;\n};\n", "import React from \"react\";\n\nexport const OpenIcon = (\n  <svg\n    xmlns=\"http://www.w3.org/2000/svg\"\n    viewBox=\"0 0 24 24\"\n    fill=\"currentColor\"\n    width=\"24\"\n    height=\"24\"\n  >\n    <g transform=\"translate(24, 0) scale(-1, 1)\">\n      <path\n        fillRule=\"evenodd\"\n        d=\"M5.337 21.718a6.707 6.707 0 01-.533-.074.75.75 0 01-.44-1.223 3.73 3.73 0 00.814-1.686c.023-.115-.022-.317-.254-.543C3.274 16.587 2.25 14.41 2.25 12c0-5.03 4.428-9 9.75-9s9.75 3.97 9.75 9c0 5.03-4.428 9-9.75 9-.833 0-1.643-.097-2.417-.279a6.721 6.721 0 01-4.246.997z\"\n        clipRule=\"evenodd\"\n      />\n    </g>\n  </svg>\n);\n\nexport const CloseIcon = (\n  <svg\n    xmlns=\"http://www.w3.org/2000/svg\"\n    fill=\"none\"\n    viewBox=\"0 0 24 24\"\n    strokeWidth=\"1.5\"\n    stroke=\"currentColor\"\n    width=\"24\"\n    height=\"24\"\n  >\n    <path strokeLinecap=\"round\" strokeLinejoin=\"round\" d=\"M19.5 8.25l-7.5 7.5-7.5-7.5\" />\n  </svg>\n);\n\nexport const HeaderCloseIcon = (\n  <svg\n    xmlns=\"http://www.w3.org/2000/svg\"\n    fill=\"none\"\n    viewBox=\"0 0 24 24\"\n    strokeWidth=\"1.5\"\n    stroke=\"currentColor\"\n    width=\"24\"\n    height=\"24\"\n  >\n    <path strokeLinecap=\"round\" strokeLinejoin=\"round\" d=\"M6 18L18 6M6 6l12 12\" />\n  </svg>\n);\n\nexport const SendIcon = (\n  <svg\n    xmlns=\"http://www.w3.org/2000/svg\"\n    fill=\"none\"\n    viewBox=\"0 0 24 24\"\n    strokeWidth=\"1.5\"\n    stroke=\"currentColor\"\n    width=\"24\"\n    height=\"24\"\n  >\n    <path strokeLinecap=\"round\" strokeLinejoin=\"round\" d=\"M12 19V5m0 0l-7 7m7-7l7 7\" />\n  </svg>\n);\n\nexport const MicrophoneIcon = (\n  <svg\n    xmlns=\"http://www.w3.org/2000/svg\"\n    fill=\"none\"\n    viewBox=\"0 0 24 24\"\n    strokeWidth=\"1.5\"\n    stroke=\"currentColor\"\n    width=\"24\"\n    height=\"24\"\n  >\n    <path\n      strokeLinecap=\"round\"\n      strokeLinejoin=\"round\"\n      d=\"M12 18.75a6 6 0 006-6v-1.5m-6 7.5a6 6 0 01-6-6v-1.5m6 7.5v3.75m-3.75 0h7.5M12 15.75a3 3 0 01-3-3V4.5a3 3 0 116 0v8.25a3 3 0 01-3 3z\"\n    />\n  </svg>\n);\n\nexport const StopIcon = (\n  <svg\n    xmlns=\"http://www.w3.org/2000/svg\"\n    fill=\"none\"\n    viewBox=\"0 0 24 24\"\n    strokeWidth=\"1.5\"\n    stroke=\"currentColor\"\n    width=\"24\"\n    height=\"24\"\n  >\n    <path\n      strokeLinecap=\"round\"\n      strokeLinejoin=\"round\"\n      d=\"M5.25 7.5A2.25 2.25 0 017.5 5.25h9a2.25 2.25 0 012.25 2.25v9a2.25 2.25 0 01-2.25 2.25h-9a2.25 2.25 0 01-2.25-2.25v-9z\"\n    />\n  </svg>\n);\n\nexport const RegenerateIcon = (\n  <svg\n    xmlns=\"http://www.w3.org/2000/svg\"\n    fill=\"none\"\n    viewBox=\"0 0 24 24\"\n    strokeWidth=\"2\"\n    stroke=\"currentColor\"\n    width=\"16\"\n    height=\"16\"\n    style={{ minWidth: \"16px\", minHeight: \"16px\" }}\n  >\n    <path\n      strokeLinecap=\"round\"\n      strokeLinejoin=\"round\"\n      d=\"M16.023 9.348h4.992v-.001M2.985 19.644v-4.992m0 0h4.992m-4.993 0l3.181 3.183a8.25 8.25 0 0013.803-3.7M4.031 9.865a8.25 8.25 0 0113.803-3.7l3.181 3.182m0-4.991v4.99\"\n    />\n  </svg>\n);\n\nexport const CopyIcon = (\n  <svg\n    xmlns=\"http://www.w3.org/2000/svg\"\n    fill=\"none\"\n    viewBox=\"0 0 24 24\"\n    strokeWidth=\"2\"\n    stroke=\"currentColor\"\n    width=\"16\"\n    height=\"16\"\n    style={{ minWidth: \"16px\", minHeight: \"16px\" }}\n  >\n    <path\n      strokeLinecap=\"round\"\n      strokeLinejoin=\"round\"\n      d=\"M15.75 17.25v3.375c0 .621-.504 1.125-1.125 1.125h-9.75a1.125 1.125 0 01-1.125-1.125V7.875c0-.621.504-1.125 1.125-1.125H6.75a9.06 9.06 0 011.5.124m7.5 10.376h3.375c.621 0 1.125-.504 1.125-1.125V11.25c0-4.46-3.243-8.161-7.5-8.876a9.06 9.06 0 00-1.5-.124H9.375c-.621 0-1.125.504-1.125 1.125v3.5m7.5 10.375H9.375a1.125 1.125 0 01-1.125-1.125v-9.25m12 6.625v-1.875a3.375 3.375 0 00-3.375-3.375h-1.5a1.125 1.125 0 01-1.125-1.125v-1.5a3.375 3.375 0 00-3.375-3.375H9.75\"\n    />\n  </svg>\n);\n\nexport const SmallSpinnerIcon = (\n  <span className=\"copilotKitSpinner\" style={{ width: \"13px\", height: \"13px\" }}></span>\n);\n\nexport const SpinnerIcon = (\n  <span className=\"copilotKitSpinner\" style={{ width: \"24px\", height: \"24px\" }}></span>\n);\n\nexport const ActivityIcon = (\n  <div style={{ display: \"flex\", alignItems: \"center\", gap: \"4px\" }}>\n    <span className=\"copilotKitActivityDot\" style={{ animationDelay: \"0s\" }}></span>\n    <span className=\"copilotKitActivityDot\" style={{ animationDelay: \"0.2s\" }}></span>\n    <span className=\"copilotKitActivityDot\" style={{ animationDelay: \"0.4s\" }}></span>\n  </div>\n);\n\nexport const ThumbsUpIcon = (\n  <svg\n    xmlns=\"http://www.w3.org/2000/svg\"\n    fill=\"none\"\n    viewBox=\"0 0 24 24\"\n    strokeWidth=\"2\"\n    stroke=\"currentColor\"\n    width=\"16\"\n    height=\"16\"\n    style={{ minWidth: \"16px\", minHeight: \"16px\" }}\n  >\n    <path\n      strokeLinecap=\"round\"\n      strokeLinejoin=\"round\"\n      d=\"M6.633 10.5c.806 0 1.533-.446 2.031-1.08a9.041 9.041 0 012.861-2.4c.723-.384 1.35-.956 1.653-1.715a4.498 4.498 0 00.322-1.672V3a.75.75 0 01.75-.75A2.25 2.25 0 0116.5 4.5c0 1.152-.26 2.243-.723 3.218-.266.558.107 1.282.725 1.282h3.126c1.026 0 1.945.694 2.054 1.715.045.422.068.85.068 1.285a11.95 11.95 0 01-2.649 7.521c-.388.482-.987.729-1.605.729H13.48c-.483 0-.964-.078-1.423-.23l-3.114-1.04a4.501 4.501 0 00-1.423-.23H5.904M14.25 9h2.25M5.904 18.75c.083.205.173.405.27.602.197.4-.078.898-.523.898h-.908c-.889 0-1.713-.518-1.972-1.368a12 12 0 01-.521-3.507c0-1.553.295-3.036.831-4.398C3.387 10.203 4.167 9.75 5 9.75h1.053c.472 0 .745.556.5.96a8.958 8.958 0 00-1.302 4.665c0 1.194.232 2.333.654 3.375z\"\n    />\n  </svg>\n);\n\nexport const ThumbsDownIcon = (\n  <svg\n    xmlns=\"http://www.w3.org/2000/svg\"\n    fill=\"none\"\n    viewBox=\"0 0 24 24\"\n    strokeWidth=\"2\"\n    stroke=\"currentColor\"\n    width=\"16\"\n    height=\"16\"\n    style={{ minWidth: \"16px\", minHeight: \"16px\" }}\n  >\n    <path\n      strokeLinecap=\"round\"\n      strokeLinejoin=\"round\"\n      d=\"M7.5 15h2.25m8.024-9.75c.011.05.028.1.052.148.591 1.2.924 2.55.924 3.977a8.96 8.96 0 01-.999 4.125m.023-8.25c-.076-.365.183-.75.575-.75h.908c.889 0 1.713.518 1.972 1.368.339 1.11.521 2.287.521 3.507 0 1.553-.295 3.036-.831 4.398C20.613 14.547 19.833 15 19 15h-1.053c-.472 0-.745-.556-.5-.96a8.95 8.95 0 00.303-.54m.023-8.25H16.48a4.5 4.5 0 01-1.423-.23l-3.114-1.04a4.5 4.5 0 00-1.423-.23H6.504c-.618 0-1.217.247-1.605.729A11.95 11.95 0 002.25 12c0 .434.023.863.068 1.285C2.427 14.306 3.346 15 4.372 15h3.126c.618 0 .991.724.725 1.282A7.471 7.471 0 007.5 19.5a2.25 2.25 0 002.25 2.25.75.75 0 00.75-.75v-.633c0-.573.11-1.14.322-1.672.304-.76.93-1.33 1.653-1.715a9.04 9.04 0 002.86-2.4c.498-.634 1.226-1.08 2.032-1.08h.384\"\n    />\n  </svg>\n);\n\nexport const DownloadIcon = (\n  <svg\n    xmlns=\"http://www.w3.org/2000/svg\"\n    fill=\"none\"\n    viewBox=\"0 0 24 24\"\n    strokeWidth=\"2\"\n    stroke=\"currentColor\"\n    width=\"16\"\n    height=\"16\"\n    style={{ minWidth: \"16px\", minHeight: \"16px\" }}\n  >\n    <path\n      strokeLinecap=\"round\"\n      strokeLinejoin=\"round\"\n      d=\"M3 16.5v2.25A2.25 2.25 0 005.25 21h13.5A2.25 2.25 0 0021 18.75V16.5M16.5 12L12 16.5m0 0L7.5 12m4.5 4.5V3\"\n    />\n  </svg>\n);\n\nexport const UploadIcon = (\n  <svg\n    xmlns=\"http://www.w3.org/2000/svg\"\n    fill=\"none\"\n    viewBox=\"0 0 24 24\"\n    strokeWidth=\"1.5\"\n    stroke=\"currentColor\"\n    width=\"24\"\n    height=\"24\"\n  >\n    <path strokeLinecap=\"round\" strokeLinejoin=\"round\" d=\"M12 4.5v15m7.5-7.5h-15\" />\n  </svg>\n);\n\nexport const CheckIcon = (\n  <svg\n    xmlns=\"http://www.w3.org/2000/svg\"\n    fill=\"none\"\n    viewBox=\"0 0 24 24\"\n    strokeWidth=\"2\"\n    stroke=\"currentColor\"\n    width=\"16\"\n    height=\"16\"\n    style={{ minWidth: \"16px\", minHeight: \"16px\" }}\n  >\n    <path strokeLinecap=\"round\" strokeLinejoin=\"round\" d=\"M4.5 12.75l6 6 9-13.5\" />\n  </svg>\n);\n", "import React, { useCallback, useEffect } from \"react\";\nimport { WindowProps } from \"./props\";\nimport { useChatContext } from \"./ChatContext\";\nimport { useCopilotContext } from \"@copilotkit/react-core\";\n\nexport const Window = ({\n  children,\n  clickOutsideToClose,\n  shortcut,\n  hitEscapeToClose,\n}: WindowProps) => {\n  const windowRef = React.useRef<HTMLDivElement>(null);\n  const context = useCopilotContext();\n\n  const { open, setOpen } = useChatContext();\n\n  const handleClickOutside = useCallback(\n    (event: MouseEvent) => {\n      if (!clickOutsideToClose) {\n        return;\n      }\n\n      const parentElement = windowRef.current?.parentElement;\n\n      let className = \"\";\n      if (event.target instanceof HTMLElement) {\n        className = event.target.className;\n      }\n\n      if (\n        open &&\n        parentElement &&\n        !parentElement.contains(event.target as any) &&\n        // prevent closing the window when clicking on the debug menu\n        !className.includes(\"copilotKitDebugMenu\")\n      ) {\n        setOpen(false);\n      }\n    },\n    [clickOutsideToClose, open, setOpen],\n  );\n\n  const handleKeyDown = useCallback(\n    (event: KeyboardEvent) => {\n      const target = event.target as HTMLElement;\n      const isInput =\n        target.tagName === \"INPUT\" ||\n        target.tagName === \"SELECT\" ||\n        target.tagName === \"TEXTAREA\" ||\n        target.isContentEditable;\n\n      const isDescendantOfWrapper = windowRef.current?.contains(target);\n\n      if (\n        open &&\n        event.key === \"Escape\" &&\n        (!isInput || isDescendantOfWrapper) &&\n        hitEscapeToClose\n      ) {\n        setOpen(false);\n      } else if (\n        event.key === shortcut &&\n        ((isMacOS() && event.metaKey) || (!isMacOS() && event.ctrlKey)) &&\n        (!isInput || isDescendantOfWrapper)\n      ) {\n        setOpen(!open);\n      }\n    },\n    [hitEscapeToClose, shortcut, open, setOpen],\n  );\n\n  const adjustForMobile = useCallback(() => {\n    const copilotKitWindow = windowRef.current;\n    const vv = window.visualViewport;\n    if (!copilotKitWindow || !vv) {\n      return;\n    }\n\n    if (window.innerWidth < 640 && open) {\n      copilotKitWindow.style.height = `${vv.height}px`;\n      copilotKitWindow.style.left = `${vv.offsetLeft}px`;\n      copilotKitWindow.style.top = `${vv.offsetTop}px`;\n\n      document.body.style.position = \"fixed\";\n      document.body.style.width = \"100%\";\n      document.body.style.height = `${window.innerHeight}px`;\n      document.body.style.overflow = \"hidden\";\n      document.body.style.touchAction = \"none\";\n\n      // Prevent scrolling on iOS\n      document.body.addEventListener(\"touchmove\", preventScroll, {\n        passive: false,\n      });\n    } else {\n      copilotKitWindow.style.height = \"\";\n      copilotKitWindow.style.left = \"\";\n      copilotKitWindow.style.top = \"\";\n      document.body.style.position = \"\";\n      document.body.style.height = \"\";\n      document.body.style.width = \"\";\n      document.body.style.overflow = \"\";\n      document.body.style.top = \"\";\n      document.body.style.touchAction = \"\";\n\n      document.body.removeEventListener(\"touchmove\", preventScroll);\n    }\n  }, [open]);\n\n  useEffect(() => {\n    document.addEventListener(\"mousedown\", handleClickOutside);\n    document.addEventListener(\"keydown\", handleKeyDown);\n    if (window.visualViewport) {\n      window.visualViewport.addEventListener(\"resize\", adjustForMobile);\n      adjustForMobile();\n    }\n\n    return () => {\n      document.removeEventListener(\"mousedown\", handleClickOutside);\n      document.removeEventListener(\"keydown\", handleKeyDown);\n      if (window.visualViewport) {\n        window.visualViewport.removeEventListener(\"resize\", adjustForMobile);\n      }\n    };\n  }, [adjustForMobile, handleClickOutside, handleKeyDown]);\n\n  return (\n    <div className={`copilotKitWindow ${open ? \" open\" : \"\"}`} ref={windowRef}>\n      {children}\n    </div>\n  );\n};\n\nconst preventScroll = (event: TouchEvent): void => {\n  let targetElement = event.target as Element;\n\n  // Function to check if the target has the parent with a given class\n  const hasParentWithClass = (element: Element, className: string): boolean => {\n    while (element && element !== document.body) {\n      if (element.classList.contains(className)) {\n        return true;\n      }\n      element = element.parentElement!;\n    }\n    return false;\n  };\n\n  // Check if the target of the touch event is inside an element with the 'copilotKitMessages' class\n  if (!hasParentWithClass(targetElement, \"copilotKitMessages\")) {\n    event.preventDefault();\n  }\n};\n\nfunction isMacOS() {\n  return /Mac|iMac|Macintosh/i.test(navigator.userAgent);\n}\n", "import { ButtonProps } from \"./props\";\nimport { useChatContext } from \"./ChatContext\";\n\nexport const Button = ({}: ButtonProps) => {\n  const { open, setOpen, icons } = useChatContext();\n\n  return (\n    <div onClick={() => setOpen(!open)}>\n      <button\n        className={`copilotKitButton ${open ? \"open\" : \"\"}`}\n        aria-label={open ? \"Close Chat\" : \"Open Chat\"}\n      >\n        <div className=\"copilotKitButtonIcon copilotKitButtonIconOpen\">{icons.openIcon}</div>\n        <div className=\"copilotKitButtonIcon copilotKitButtonIconClose\">{icons.closeIcon}</div>\n      </button>\n    </div>\n  );\n};\n", "import {\n  CopilotContextParams,\n  CopilotMessagesContextParams,\n  defaultCopilotContextCategories,\n} from \"@copilotkit/react-core\";\nimport { CopilotKitVersion } from \"./types\";\nimport { ActionExecutionMessage, ResultMessage, TextMessage } from \"@copilotkit/runtime-client-gql\";\nimport { AgentStateMessage } from \"@copilotkit/runtime-client-gql\";\n\nexport function shouldShowDevConsole(showDevConsole: boolean | \"auto\"): boolean {\n  if (typeof showDevConsole === \"boolean\") {\n    return showDevConsole;\n  }\n  return (\n    getHostname() === \"localhost\" ||\n    getHostname() === \"127.0.0.1\" ||\n    getHostname() === \"0.0.0.0\" ||\n    getHostname() === \"::1\"\n  );\n}\n\nfunction getHostname(): string {\n  if (typeof window !== \"undefined\" && window.location) {\n    return window.location.hostname;\n  }\n  return \"\";\n}\n\nexport async function getPublishedCopilotKitVersion(\n  current: string,\n  forceCheck: boolean = false,\n): Promise<CopilotKitVersion> {\n  const LOCAL_STORAGE_KEY = \"__copilotkit_version_check__\";\n  const serializedVersion = localStorage.getItem(LOCAL_STORAGE_KEY);\n  if (serializedVersion && !forceCheck) {\n    try {\n      const parsedVersion: CopilotKitVersion = JSON.parse(serializedVersion);\n      const oneHour = 60 * 60 * 1000;\n      const now = new Date().getTime();\n\n      if (\n        parsedVersion.current === current &&\n        now - new Date(parsedVersion.lastChecked).getTime() < oneHour\n      ) {\n        return parsedVersion;\n      }\n    } catch (error) {\n      console.error(\"Failed to parse CopilotKitVersion from localStorage\", error);\n    }\n  }\n\n  try {\n    const response = await fetch(\"https://api.cloud.copilotkit.ai/check-for-updates\", {\n      method: \"POST\",\n      headers: {\n        \"Content-Type\": \"application/json\",\n      },\n      body: JSON.stringify({\n        packages: [\n          {\n            packageName: \"@copilotkit/shared\",\n            packageVersion: current,\n          },\n        ],\n      }),\n    });\n\n    const data = await response.json();\n\n    const version: CopilotKitVersion = {\n      current,\n      lastChecked: new Date().getTime(),\n      latest: data.packages[0].latestVersion,\n      severity: data.packages[0].severity,\n      advisory: data.packages[0].advisory || null,\n    };\n\n    localStorage.setItem(LOCAL_STORAGE_KEY, JSON.stringify(version));\n    return version;\n  } catch (error) {\n    console.error(\"Failed to check for updates\", error);\n    throw error;\n  }\n}\n\nexport function logReadables(context: CopilotContextParams) {\n  console.log(\"%cCurrent Readables:\", \"font-size: 16px; font-weight: bold;\");\n\n  const readables = context.getContextString([], defaultCopilotContextCategories).trim();\n  if (readables.length === 0) {\n    console.log(\"No readables found\");\n    return;\n  }\n  console.log(readables);\n}\n\nexport function logActions(context: CopilotContextParams) {\n  console.log(\"%cCurrent Actions:\", \"font-size: 16px; font-weight: bold;\");\n\n  if (Object.values(context.actions).length === 0) {\n    console.log(\"No actions found\");\n    return;\n  }\n  for (const action of Object.values(context.actions)) {\n    console.group(action.name);\n    console.log(\"name\", action.name);\n    console.log(\"description\", action.description);\n    console.log(\"parameters\", action.parameters);\n\n    console.groupEnd();\n  }\n}\n\nexport function logMessages(context: CopilotMessagesContextParams) {\n  console.log(\"%cCurrent Messages:\", \"font-size: 16px; font-weight: bold;\");\n\n  if (context.messages.length === 0) {\n    console.log(\"No messages found\");\n    return;\n  }\n\n  const tableData = context.messages.map((message) => {\n    if (message.isTextMessage()) {\n      return {\n        id: message.id,\n        type: \"TextMessage\",\n        role: message.role,\n        name: undefined,\n        scope: undefined,\n        content: message.content,\n      };\n    } else if (message.isActionExecutionMessage()) {\n      return {\n        id: message.id,\n        type: \"ActionExecutionMessage\",\n        role: undefined,\n        name: message.name,\n        scope: message.parentMessageId,\n        content: message.arguments,\n      };\n    } else if (message.isResultMessage()) {\n      return {\n        id: message.id,\n        type: \"ResultMessage\",\n        role: undefined,\n        name: message.actionName,\n        scope: message.actionExecutionId,\n        content: message.result,\n      };\n    } else if (message.isAgentStateMessage()) {\n      return {\n        id: message.id,\n        type: `AgentStateMessage (running: ${message.running})`,\n        role: message.role,\n        name: undefined,\n        scope: message.threadId,\n        content: message.state,\n      };\n    }\n  });\n  console.table(tableData);\n}\n", "\"use client\";\n\nimport { useCopilotContext, useCopilotMessagesContext } from \"@copilotkit/react-core\";\nimport {\n  getPublishedCopilotKitVersion,\n  logActions,\n  logMessages,\n  logReadables,\n  shouldShowDevConsole,\n} from \"./utils\";\nimport React, { useEffect, useRef, useState } from \"react\";\nimport {\n  CheckIcon,\n  ChevronDownIcon,\n  CopilotKitIcon,\n  ExclamationMarkIcon,\n  ExclamationMarkTriangleIcon,\n} from \"./icons\";\nimport { Menu, MenuButton, MenuItem, MenuItems } from \"@headlessui/react\";\nimport { COPILOTKIT_VERSION } from \"@copilotkit/shared\";\nimport { SmallSpinnerIcon } from \"../chat/Icons\";\nimport { CopilotKitHelpModal } from \"../help-modal\";\n\ntype VersionStatus = \"unknown\" | \"checking\" | \"latest\" | \"update-available\" | \"outdated\";\n\nexport function CopilotDevConsole() {\n  const currentVersion = COPILOTKIT_VERSION;\n  const context = useCopilotContext();\n\n  // to prevent hydration errors, ensure that the component renders the same content\n  // server-side as it does during the initial client-side render to prevent a hydration\n  // mismatch\n  // see: https://nextjs.org/docs/messages/react-hydration-error#solution-1-using-useeffect-to-run-on-the-client-only\n\n  const [showDevConsole, setShowDevConsole] = useState(false);\n\n  useEffect(() => {\n    setShowDevConsole(shouldShowDevConsole(context.showDevConsole));\n  }, [context.showDevConsole]);\n\n  const dontRunTwiceInDevMode = useRef(false);\n  const [versionStatus, setVersionStatus] = useState<VersionStatus>(\"unknown\");\n  const [latestVersion, setLatestVersion] = useState<string>(\"\");\n  const consoleRef = useRef<HTMLDivElement>(null);\n  const [debugButtonMode, setDebugButtonMode] = useState<\"full\" | \"compact\">(\"full\");\n\n  const checkForUpdates = (force: boolean = false) => {\n    setVersionStatus(\"checking\");\n\n    getPublishedCopilotKitVersion(currentVersion, force)\n      .then((v) => {\n        setLatestVersion(v.latest);\n        let versionOk = false;\n\n        // match exact version or a version with a letter (e.g. 1.0.0-alpha.1)\n        if (v.current === v.latest) {\n          versionOk = true;\n        } else if (/[a-zA-Z]/.test(v.current)) {\n          versionOk = true;\n        }\n\n        if (versionOk) {\n          setVersionStatus(\"latest\");\n        } else if (v.severity !== \"low\") {\n          setVersionStatus(\"outdated\");\n        } else {\n          setVersionStatus(\"update-available\");\n        }\n      })\n      .catch((e) => {\n        console.error(e);\n        setVersionStatus(\"unknown\");\n      });\n  };\n\n  useEffect(() => {\n    if (dontRunTwiceInDevMode.current === true) {\n      return;\n    }\n    dontRunTwiceInDevMode.current = true;\n\n    checkForUpdates();\n  }, []);\n\n  if (!showDevConsole) {\n    return null;\n  }\n  return (\n    <div\n      ref={consoleRef}\n      className={\n        \"copilotKitDevConsole \" +\n        (versionStatus === \"update-available\" ? \"copilotKitDevConsoleUpgrade\" : \"\") +\n        (versionStatus === \"outdated\" ? \"copilotKitDevConsoleWarnOutdated\" : \"\")\n      }\n    >\n      <VersionInfo\n        showDevConsole={context.showDevConsole}\n        versionStatus={versionStatus}\n        currentVersion={currentVersion}\n        latestVersion={latestVersion}\n      />\n\n      <CopilotKitHelpModal />\n\n      <DebugMenuButton\n        setShowDevConsole={setShowDevConsole}\n        checkForUpdates={checkForUpdates}\n        mode={debugButtonMode}\n      />\n    </div>\n  );\n}\n\nfunction VersionInfo({\n  showDevConsole,\n  versionStatus,\n  currentVersion,\n  latestVersion,\n}: {\n  showDevConsole: boolean | \"auto\";\n  versionStatus: VersionStatus;\n  currentVersion: string;\n  latestVersion: string;\n}) {\n  const [copyStatus, setCopyStatus] = useState<string>(\"\");\n\n  let versionLabel = \"\";\n  let versionIcon: any = \"\";\n  let currentVersionLabel = currentVersion;\n\n  if (versionStatus === \"latest\") {\n    versionLabel = \"latest\";\n    versionIcon = CheckIcon;\n  } else if (versionStatus === \"checking\") {\n    versionLabel = \"checking\";\n    versionIcon = SmallSpinnerIcon;\n  } else if (versionStatus === \"update-available\") {\n    versionLabel = \"update available\";\n    versionIcon = ExclamationMarkIcon;\n    currentVersionLabel = `${currentVersion} → ${latestVersion}`;\n  } else if (versionStatus === \"outdated\") {\n    versionLabel = \"outdated\";\n    versionIcon = ExclamationMarkTriangleIcon;\n    currentVersionLabel = `${currentVersion} → ${latestVersion}`;\n  }\n\n  let asideLabel = \"\";\n  if (showDevConsole === \"auto\") {\n    asideLabel = \"(localhost only)\";\n  } else if (showDevConsole === true) {\n    asideLabel = \"(always on)\";\n  }\n\n  const installCommand = [\n    `npm install`,\n    `@copilotkit/react-core@${latestVersion}`,\n    `@copilotkit/react-ui@${latestVersion}`,\n    `@copilotkit/react-textarea@${latestVersion}`,\n    `&& npm install @copilotkit/runtime@${latestVersion}`,\n  ].join(\" \");\n\n  const handleCopyClick = () => {\n    navigator.clipboard.writeText(installCommand.trim()).then(() => {\n      setCopyStatus(\"Command copied to clipboard!\");\n      setTimeout(() => setCopyStatus(\"\"), 1000);\n    });\n  };\n\n  if (versionStatus === \"update-available\" || versionStatus === \"outdated\") {\n    return (\n      <div className=\"copilotKitVersionInfo\">\n        <p>\n          {currentVersionLabel} {versionIcon}\n        </p>\n        <button onClick={handleCopyClick}>{copyStatus || installCommand}</button>\n      </div>\n    );\n  }\n\n  return null;\n}\n\nexport default function DebugMenuButton({\n  setShowDevConsole,\n  checkForUpdates,\n  mode,\n}: {\n  setShowDevConsole: (show: boolean) => void;\n  checkForUpdates: (force: boolean) => void;\n  mode: \"full\" | \"compact\";\n}) {\n  const context = useCopilotContext();\n  const messagesContext = useCopilotMessagesContext();\n\n  return (\n    <>\n      <Menu>\n        <MenuButton\n          className={`copilotKitDebugMenuTriggerButton ${mode === \"compact\" ? \"compact\" : \"\"}`}\n        >\n          {mode == \"compact\" ? \"Debug\" : <>Debug {ChevronDownIcon}</>}\n        </MenuButton>\n\n        <MenuItems\n          transition\n          anchor=\"bottom end\"\n          className=\"copilotKitDebugMenu\"\n          style={{ zIndex: 40 }}\n        >\n          <MenuItem>\n            <button className=\"copilotKitDebugMenuItem\" onClick={() => logReadables(context)}>\n              Log Readables\n            </button>\n          </MenuItem>\n          <MenuItem>\n            <button className=\"copilotKitDebugMenuItem\" onClick={() => logActions(context)}>\n              Log Actions\n            </button>\n          </MenuItem>\n          <MenuItem>\n            <button\n              className=\"copilotKitDebugMenuItem\"\n              onClick={() => logMessages(messagesContext)}\n            >\n              Log Messages\n            </button>\n          </MenuItem>\n          <MenuItem>\n            <button className=\"copilotKitDebugMenuItem\" onClick={() => checkForUpdates(true)}>\n              Check for Updates\n            </button>\n          </MenuItem>\n          <hr />\n          <MenuItem>\n            <button className=\"copilotKitDebugMenuItem\" onClick={() => setShowDevConsole(false)}>\n              Hide Dev Console\n            </button>\n          </MenuItem>\n        </MenuItems>\n      </Menu>\n    </>\n  );\n}\n", "export const ExclamationMarkTriangleIcon = (\n  <svg\n    width=\"13.3967723px\"\n    height=\"12px\"\n    viewBox=\"0 0 13.3967723 12\"\n    version=\"1.1\"\n    xmlns=\"http://www.w3.org/2000/svg\"\n  >\n    <g id=\"Page-1\" stroke=\"none\" strokeWidth=\"1\" fill=\"none\" fillRule=\"evenodd\">\n      <g id=\"exclamation-triangle\" fill=\"#CD2121\">\n        <path\n          d=\"M5.39935802,0.75 C5.97670802,-0.25 7.42007802,-0.25 7.99742802,0.75 L13.193588,9.75 C13.770888,10.75 13.049288,12 11.894588,12 L1.50223802,12 C0.34753802,12 -0.37414898,10.75 0.20319802,9.75 L5.39935802,0.75 Z M6.69838802,2.5 C7.11260802,2.5 7.44838802,2.83579 7.44838802,3.25 L7.44838802,6.25 C7.44838802,6.66421 7.11260802,7 6.69838802,7 C6.28417802,7 5.94838802,6.66421 5.94838802,6.25 L5.94838802,3.25 C5.94838802,2.83579 6.28417802,2.5 6.69838802,2.5 Z M6.69838802,10.5 C7.25067802,10.5 7.69838802,10.0523 7.69838802,9.5 C7.69838802,8.9477 7.25067802,8.5 6.69838802,8.5 C6.14610802,8.5 5.69838802,8.9477 5.69838802,9.5 C5.69838802,10.0523 6.14610802,10.5 6.69838802,10.5 Z\"\n          id=\"Shape\"\n        ></path>\n      </g>\n    </g>\n  </svg>\n);\n\nexport const ExclamationMarkIcon = (\n  <svg\n    width=\"14px\"\n    height=\"14px\"\n    viewBox=\"0 0 14 14\"\n    version=\"1.1\"\n    xmlns=\"http://www.w3.org/2000/svg\"\n  >\n    <g id=\"Page-1\" stroke=\"none\" strokeWidth=\"1\" fill=\"none\" fillRule=\"evenodd\">\n      <g id=\"exclamation-circle\" fill=\"#EC662C\">\n        <path\n          d=\"M7,14 C10.866,14 14,10.866 14,7 C14,3.13401 10.866,0 7,0 C3.13401,0 0,3.13401 0,7 C0,10.866 3.13401,14 7,14 Z M7,3 C7.41421,3 7.75,3.33579 7.75,3.75 L7.75,6.75 C7.75,7.16421 7.41421,7.5 7,7.5 C6.58579,7.5 6.25,7.16421 6.25,6.75 L6.25,3.75 C6.25,3.33579 6.58579,3 7,3 Z M7,11 C7.55228,11 8,10.5523 8,10 C8,9.4477 7.55228,9 7,9 C6.44772,9 6,9.4477 6,10 C6,10.5523 6.44772,11 7,11 Z\"\n          id=\"Shape\"\n        ></path>\n      </g>\n    </g>\n  </svg>\n);\n\nexport const ChevronDownIcon = (\n  <svg\n    width=\"7px\"\n    height=\"4px\"\n    viewBox=\"0 0 7 4\"\n    version=\"1.1\"\n    xmlns=\"http://www.w3.org/2000/svg\"\n    fill=\"currentColor\"\n  >\n    <g id=\"Page-1\" stroke=\"none\" strokeWidth=\"1\" fill=\"none\" fillRule=\"evenodd\">\n      <g id=\"Group\" fill=\"currentColor\" fillRule=\"nonzero\">\n        <path\n          d=\"M3.71690723,3.90271086 C3.59268176,4.03242971 3.39143629,4.03242971 3.26721082,3.90271086 L0.0853966595,0.57605615 C-0.0314221035,0.444981627 -0.0279751448,0.240725043 0.0931934622,0.114040675 C0.214362069,-0.0126436935 0.409725445,-0.0162475626 0.535093061,0.105888951 L3.49205902,3.19746006 L6.44902499,0.105888951 C6.52834574,0.0168884389 6.64780588,-0.0197473458 6.7605411,0.0103538404 C6.87327633,0.0404550266 6.96130636,0.132492308 6.99009696,0.250359396 C7.01888756,0.368226483 6.98384687,0.493124608 6.89872139,0.57605615 L3.71690723,3.90271086 Z\"\n          id=\"Path\"\n        ></path>\n      </g>\n    </g>\n  </svg>\n);\n\nexport const CheckIcon = (\n  <svg\n    width=\"14px\"\n    height=\"14px\"\n    viewBox=\"0 0 14 14\"\n    version=\"1.1\"\n    xmlns=\"http://www.w3.org/2000/svg\"\n  >\n    <g id=\"Page-1\" stroke=\"none\" strokeWidth=\"1\" fill=\"none\" fillRule=\"evenodd\">\n      <g id=\"Group-2\" transform=\"translate(-118, 0)\" fill=\"#1BC030\" fillRule=\"nonzero\">\n        <g id=\"Group\" transform=\"translate(118, 0)\">\n          <path\n            d=\"M0,7 C0,3.13384615 3.13384615,0 7,0 C10.8661538,0 14,3.13384615 14,7 C14,10.8661538 10.8661538,14 7,14 C3.13384615,14 0,10.8661538 0,7 Z M9.59179487,5.69764103 C9.70905818,5.54139023 9.73249341,5.33388318 9.65303227,5.15541491 C9.57357113,4.97694665 9.40367989,4.85551619 9.20909814,4.83811118 C9.01451638,4.82070616 8.82577109,4.91005717 8.71589744,5.07158974 L6.39261538,8.32389744 L5.22666667,7.15794872 C5.01450582,6.96025518 4.68389046,6.9660885 4.47883563,7.17114332 C4.27378081,7.37619815 4.26794748,7.70681351 4.46564103,7.91897436 L6.08102564,9.53435897 C6.19289944,9.64614839 6.3482622,9.70310251 6.50588106,9.69010587 C6.66349993,9.67710922 6.80743532,9.59547613 6.89948718,9.46687179 L9.59179487,5.69764103 L9.59179487,5.69764103 Z\"\n            id=\"Shape\"\n          ></path>\n        </g>\n      </g>\n    </g>\n  </svg>\n);\n\nexport const CopilotKitIcon = (\n  <svg\n    width=\"33px\"\n    height=\"35px\"\n    viewBox=\"0 0 33 35\"\n    version=\"1.1\"\n    xmlns=\"http://www.w3.org/2000/svg\"\n  >\n    <title>bd5c9079-929b-4d55-bdc9-16d1c8181b71</title>\n    <g id=\"Page-1\" stroke=\"none\" strokeWidth=\"1\" fill=\"none\" fillRule=\"evenodd\">\n      <image\n        x=\"0\"\n        y=\"0\"\n        width=\"33\"\n        height=\"35\"\n        xlinkHref=\"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAI0AAACXCAYAAAAoE9hYAAAAAXNSR0IArs4c6QAAAERlWElmTU0AKgAAAAgAAYdpAAQAAAABAAAAGgAAAAAAA6ABAAMAAAABAAEAAKACAAQAAAABAAAAjaADAAQAAAABAAAAlwAAAACI8Oz3AABAAElEQVR4Ae2dCYAlVXnvv6q79d7TMz37sIMgKKBCkLhkSKK4xJUMQVzAqJBgNDGJa2LsvJfERI0aiEbwRXnoQ2VYRASiQUFRUGQm7DAwzDBbT+/rvbfvXu/3P1V1+84CzEw3MNPD6a579nOqzvev73znO0t59rzZbQu88sXndp2w4gVLp5oG+6+47ivDu010kAb6B+lzP+Vjr1zZk0xV/ddMVIufap/34g+e+s9rX9351U1dT5npIIpMHkTPusePmto+tKhctVWPDW1/c27hIVNbCsFbCpa7/aR/f/SWFV2ZX974zsNG97iwOZjwedDshqiZlL3AD9JH9Y8PpTYMPJEuLj7+pclE5vBquXZ6MV/56Zu/ueGnmWpqzer3HzKym+xzPsib80+4lw/Y09Pjr7lm9GO+eR+eKI4tXdeRsomXv8a6Dj3eqn6iRoPl5qW83o60vyaT9O5IWPX2llrH+hsuXJbfy6oO2OTPg2Yn0p1/6ueWFK1wWTKReo1XDZoeqw7YxhcfYfNf9NtWSHTYVLVqxSAIqoFlU76NzEt6mzqSCQBkt3up1NrlHUdsXX22V92p2DnlfR40O5Az8N57+pfPgcv8YzLZdHjKS3i9uS1254Kitf3271qq81ATGkq1wPJCDVexEtS8oFZKpBLZeWl/w7yE98uUZ3cnLPhNefSFmx7s8Uo7VDEHPM+DpoGI71v5xaODkn02kUi9Ne03Jz3Ps2Jh3O4ubbCB006yzqNPsYrfYhbUrBwEBnZstBLYBFIzSDIXQB5LeOY3JXoXJv3/afP9H9dqdtv8XHb9mp5T5kQX9rwg3ACaaiF4ZcL3T0x6KQATaiNS6RZbmm+yib7tFiwfM7+1ySTYJACMuE6zktFP5X3PKgDIyoCnULFavrKs3/eW9af8N3qZRN9wU9N9R33hgZ8kveRvyuXs2g2fOGW8oeoDyvk8p4nI9Z7f/eLyZMn/AkR9azrR3ASvMHgJsXRD2X67q3nUci87yVoPOQluk4apBI7blNVVkSyLXRHrURZYiwlAdF8EhmFqaV5RL+XnM+nEffN8W2OJxG2e1R5MpPz++dUFxWMP+3lh9dln7/fy0POggZYaMT3xo67XpnzvX1PJzPFJP2PIusAFonNVK1P2QOEJ23z0Qms/+dVWycwHNDUHnBJJCoBl3PVQ5HDAIZD8rrvCcragUI0BRGCSpk95401pf327F/w64QcP1FLN92Rq3qZM81h2xchUaeVnVpZ6PE8w3K/M86CBHBe86tKlQa3wlwk/+cFUoqVZXVMAKAQYcRSDbn3ZrfZAe8EqLz/dMguPtrKHqEuUZBsxkzxgEbcpi7u4rPw4O/KL7MrAvwOPQCSOFHEg870KHKevxfc2pBOJe2F066pWe6g5mehrSVo2KLdM+C21wvoPHV225xhIBz1oVq26KjGvt/+Nnnl/m0o2n5pI0IeIsIDGWVBedr44bg9Vttn2E4+wjhecYuVMZ8hAAIp6InGbCTBQhJuEeCOQsJDjqDxd8mPrR7ZAFaeJw0URBGlL+oVEwgaafX+T79umivkbkKWeyPjJrS1+ZSRhifFMMjNR7U5MPLHp8JL1PHsc6aAXhDu2TnQGvvdKRkwvSAIYDw5SC2ADIp4jJDaOTLLJFpeabWRgwCrLRiyZ7rAy7ECDJQ8wpEjVjLuMQFxVRknLngrA9uVXObgVF0S2hGgVoHAllRGwwq6sqeoFh2a5LGGvskStAAcc8xLFrRXP206Ovlott63Sm3y8LbOud8nFj/UlvMmxtnRtbM0FLys8k9xId3zQGsdl+gdP84PEF1KJ9OmpRMa1RbUmqom44jL6Q36hK8kWRuy+1KBNnPhCaznyZVZJNiPritMg9wCKEnaWrHnyqedxQBHbESAcR1GxKjcs3rlxOqOw3RnlazQRxgBFYAl/Cq40giy2vdn3NgaJxBMJv/ZgS8bvbc/4A8lydaw14eeqqdzEmgtPKTcWMxP3Qc1p2scmFyc8/3UJL/FiAcZHlpGAK/2MBOHQhFSSnNOUarPu8pils0PGINxGLAND8WEEGLhJAnw0wT2qcJIC+R1DcdxF8VySdxxnaSw7dquQnYyqhnOFJrZJrywBBVdqLVaxFtCwAg53qiWq5UzCHylXaqMTU9brIw+1+tbfWWldd9IXHxpIJoLBZCY9kilXpyrJYi7d3zGx8jOH77WwHd9JdGMHj9Wz8tZkf/Dwa7wg8Zl0svk0N2KCEkwQOOA4ARhWEY+iZNdqZZsIhm3By5qtdNwLbU1hqY0DH1FQHMdxGzhDEaJOYGsILkZDIeFVd8dhje0dgaExqE6dyOGsRrfKacig7lBIFUABm+f7tWY/KACkQbA65vneIIL2cMazyXTCG0glElsyCZvgxZmyWmXKTyfGWpPJscHc1NT/jCUqlsoFJw4+UFm06RfFyuCjudtuu62i2g5aTjNU3rDISyTfgODruEw8YqqpxSGyGEIAAdQLiBDqpESNxQvm2wsOa7LtqSnLTObQu2QizqRoyTbkASxViJYjyPUJyurYjsrCTbyTYyL6h4THE/uJDRM2BDQ4w3RR+jjccbAozOV3Jfj5mteSD4LDCDrM5/6yPBDjvmoq4RUSXpBtTnilZq9aSfpW8CvVXD5ZG68EyUnzikzB1aqPZw4tVg77/bHjW4/+1etftfC2m29fPXRQgub8ld9sCqrVF9ItrUz56Rbf1/BZbCCkm7onEVI9QBgSERnCd3Q3WWtXxiaGxq00mbJUV4cFKPscJkiNvsXlaRYwAE4Wn4bkrkCVqyL147hPFC5rFyN2gXHCtHNEeRUWXWFhkd8VvEucerc06WSrRCwYjie6t3EPbRXJYAQmEdwD6irAHlO1YiWVG6mWhweCyuQAPGi8XCoUTignukdXrVp160EJmiYrLPH8zNmJZPK4FJzCGahO22F8GjjSz7gQhYdAgN1b17K0+anA+h7rs4lK3lo6llg1AWigiBchR9xJwHP5CM/icsARoJQmtgUc1SHryYyAFpvYraDYrbg4SRTmgxCN5gSSBD9pbBFawEmQWMl0aWSvzA5QvDR+ecqq+WFvcnBLakHvllStr8/aJkdtHiUUUi3ztle212657YbaQQeaj5x+VXMtUTwTAeVt6WQm6QMEJ/TSgHXahQigOSXjqF0VWbNMm29ti5JWypattGmK6YSCVQ8ftWSGlxYSSa5RcobwTjhOwWFaCUFWNsbAWlIR9kwCjgqOQCbSuYzOsfMPafl3ZmdbgS5MhAcouB04qE+gkdsBBDsGjGzlEagVnwjQAE1NWHmyz/L9W6za32udA+O2rNJirbVWeqiKsUykmEk0PZzIDj9AluCgAg0k9S5MfGthc83elkgmFkiRp0YV8eqACduUcHQwDgR6X3kLSdDayQiqw2x8O0SZzFirN27V0QFLdyxkGqnFEU7UV9oK2fRWCxut/IifFQCjgCNBuRYDh3BXue7jyYy7RyJ1K85oZkyzEPzwAOIYGsFpZkJ2IgKEezb8SivgiveF4CEsQNqaGrXSWJ8Vejdapr/fusbytqDSZIsSS6y9eR65zIa4ysZUv+fdc+zjfzF8n93hwOYiD4afj772Wy1NleC1NN1vo5eByYRDbPfC0+CiQf0XArtuRqo6JSBt+5KkJaBUdoChdaXZumrj1j/Ub7VFK8xvb7EahEnx45R7gEOE8slbBiQiXELgwV/ALwFZQ/NQz6N6n9owke7AoFSurDpAFB7etcLFcZwKAHcIFKUHLspPvYlakS5ozAqjvTbVu8FSA0O2aKJoi3ie7tQKa2lpRRkdCfe8LAlUEZVqYYzmeHC1hZOpBxWnqdUC1v4m/iKdTranWQgMDc2XAKg/GtsNanCLdYsAjjwkqjAMT6GA6VrhWxk2Md5XsXSiybqCVts+OGrFsUFrbetmxMTst/JTpt5yn7wChwRRAUplJumy1HUILJKPBRyNY52sjL07Q1Euj8qUkeW6HrnxxBwkjhdwZEIbENEFJUpTFoD27HCvFbc+YW2A5Yhszbr9DutuWmpNTa0Ai1K5r/Av5JJowkvmTW2s+JP3u0L5OWhA86HX35SplUY+7Cf8E1LpFIwDQroRDqSkoTRKEetmHR7ekIQSbBOMrKpQta2bkROcZnx7xfKjUsamrCPRZl3jfTY50GteN9r+FuCgMhxAICXFqEy3OoLy1V3pPwSMwBpWLZ2f3LEhiZI5IAloMgJjnF+RcbjSiZO4fxcuCKlAngfB1grIK/Snk8PbrYhgO28sZ0dMJW2hv9A62+ZZiukRdcVhlmnoqowqYGPWf6TcvPBXP/j5lwfdjfBzUIAGQdf7+GuvfmXF89+cTCctlQofO34z1RhqavACBWhw5yAMQjtdDeDpWIzg2BzY5GDNWClhgA+1Hl1UIWW5wSEWXTEf1dxOphQEFolpXBAjW7/wHziZuAs1qR6MMEvRrttQikbj8hMQhyJ94Qu7HuVxBlvgCWP0q1rgKuWSBVMjlh/aYtnBXkv3D1k7XdBR1Ywt9hdYZ0snwm0IFpW/Q91hMa5eacdZlDYy0dZ5N8nqiDooQPPRM7+1MKglP5FOp+enm3lkNQwm5Cw0GlTQCMq9o7ghr2siyTxV1LySYzqXI//AcRhkGC+g+WjDUgBkHtxmYGzMSsN9TiD2mf2uggpxMRFXLc3cUESEEDghgNwtuHjpRwQmmYhmYeRufqNbdzHqfuSXwG7VogXFrFUmBy0/0mvV7b0WjIzb8nxgy4M2m5/qthZGeX4Cbqjn1Z/yycSFOpsf/qUZ94JqrZhKPjbc5K8JE4a/cx40LLBKTt6ReT3d9avTzSjjkChdt+QaLXzrpcF1wq7ai3Z0RFX7QHGPvqa5kxEQ3dPUeGBTYyRwxJKdtLYkXVRx0gYGB6y6iHmpjLiNhOgQMFiOQAKPAykEk1v0chexZHBhOJwJ+RRORdUpKmcoa4U5NEpDM1cRV5m04ni/FYe2W7m/zzLjWQeWRV6HdaQ6rJmJVZ/u1IFBRYYFO1fdqxuN6nIvj+7aD4rFVGLNkYmugUbUzHnQTNx53Akpz7sg1YQkk0YqgJ+H8oC4iwhH40RgUc+kPsNNJahrQigRdxGXSbUENrYJGqHiTdA1KU4FpJEJukttNtzP8HUZ3GbeYt7mZleJ4wAqEmKE77YIE5JGdTaCwgHJxUYpuBclCQEUcir5fdWLkFWVrJIdtfJIv1WHByw5NGYLciVbWEpZZ6Lb2ptaLEUXZCz1kOFp+FUJDWYH7w4emqEWMO0wNpH07/7x6h2XoM5p0Hx81VWdXs7eiT7m5AzL3xKMTQUUdqY4sKgdJew64KhNFYkRR1D70mx0TTXrXEY4tMoOkLaSABSKUxrgBQtrS7VaW27MxuA2vOLmt2gSUybkIGrkGBTKJtDKFnBc3bjjYTJOZxSve3BCrgthIF8uWA2uUpoYtiKCbW1o2NKjE7aARcrd1SYE825ra251Qrp7OygkBIsrIPpxJVO2HnJXE4bqt0Yvm+hdUq49uHOqOQuaq1iR9z9Tidf4SW9VpjnZlExBFtqrsa0cRlzDRsDRyAl2o7UzbrgK98+g0GtfUbXCJFsrhxBEWU7nqE5aAUHD8wyg6co328TgsFXGUfY1tQMmlPdwK3Evx8lAgMgVcphpUmqorwgXF1HHhRHicx+1aokeKM88F0AZHbTKQJ+lhsdtXrZkXeWkdVm7tdL9ZFLN4IR7E71VXvQCREWG4c5DAqXZyYTSEVHcj56fOyz5yeTdR1ZO7N0p6dwdPd1v6eOTyeCdybS/nG4pVlk0PD/N5Bo3bmc1Jk1FmJvxFjtgLNzazfRBR2AjjyWskhfnEcHV6iAqIk6CuaeuRLsNTgzaFAJxav5S85sBjeJdsQIMHkz4GwHIhfBDmjicjXe85GyBqbIeOTdu+fEhFHGMdgcHLc1weUGuZvMrSevwu6wZoGgdkBsyx2WHBYUPFZe/O5REcTFYKMSlEmicWI4OkvHi2p7bzqCD3tHMSU7zyVU3LfRrpYsYXr8i1ZxMSSejFzo2cbtGlKq/lSEXQNjkha0BGI9epm0p4ABA+UHkoarPPBPkr6LJVSNja1gqrW97upPh96Rt6N1urUsPtWQLs98RIWJIqD7VLWCGwItvCqmH1YLqfkqUMQVQ8qNDVgQoLcMT1pkvA5S0dRrzQckWOBkLxugW4+dw9/0UwIifuw6QOK0A3eBWV+gGAXqmlD+YSgR3xnkb7TkHGinxMlZ6jZ/wzkg2JeZruFxv3YhGcUM5rwjoiCgOE3KEkAPR7bTUrKUbjfAUr90EsgxlaSMAEqJLGwKHRVt0VSzkctrVvmFGMSN91rxghdWSmnESaaOKHUmoI7olx1UQamsMlYu5UZtATikPDVkwzJrAyZItoN6FNea42EPeJK7CCEgz7SoxBF0jKZ/MHT+t4nX/8b04r0JcRtn6cxyRBqCre7jW5W90kTv9zDnQdDRPnYbk8X64zFHwGMY5NEpMpPjhd9dwIKdODLnpJZrnMzrqrDBNwGLzfMK0ikIAc1wI8IiANbiYdDnS6XQ2zbdulHyD2/ssWDFuibZFACQUe52conopIKE1yBW4CkCZHB+0yf5tDJWHLDmaQzYKrLuGUJuErySZB0Kv4lR60TPoHp/ckAgT/k67ph8XWEx7SEdK9x/mYFEIL0CVZ2HGPOn/8pLVF+W+ah90ZTb+zCnQfHbV9UcXzT6EHPNbmUwyqW5F635l1NhqGicghm2l4GkD0WUECqXxEElaFtGAaWalWasmfY1AI3rXmPnTdINPFyYAJdRdVRPWyjzOolKXjaBUq2aHAQ2b6ihIJaMoM5/5nwpD5SxAGUf2ybLV1x+atA7mgA5FWzs/iZySbrWMFrgzVHb3q3vSje3WhMQOf5Ug8tcDYn9kuzJwu/84LLRBE8HcqcceC98rsWbkbkJ2W/WcAU3Pqv+aX/HLLHnwXpnJpFrontxb5cAStlPYqFEzNLxwLrzuJ17LFvxMzdJdyBkVFPPZFAKwdDyARfKOZFUpBLnEZRicAhwAxNqE7nK3LSzkrDAG56CLYt0tKycnbAoheWRgK9MQfVYcGKb7KdqSYsq6mfSchwKuKd0cDpUhXdhNToMlJq+j+c4/jc8Wxe3ITfR4KkGgCI1zRQ88XXYoz/BUzJr7m8e9zC5D7Sj73Bg9reSMvIRfXIle4Wy4zCJkDxn3jG7YNN1a0dskZMRtGUeqWTFiC4wX0m10TS1VlGhMWE6FmmTpZcIph2nQ+Exfo22PgIQiEC3swrFWe2yg35q6nrBaqslGezfZ8JYt5g1OWDtrKw9HTulKzLOOTAeykHZBTAu17qYh3PRdhSHR3cWeuh0/ZxwQ5uOX528sI+QiYarpmCi1aytAA4dVe/mp5G8OO+29w3bzH8fF7mDPCU7zu0tfeiyPegES/ylpZrClxHPtoEfVyEncBVvvbtjIalS8siLbuaNmFm6aOljRlq5ZaYQhFENcFL8ug+ve6pyGSUkaWmGsObZiscDWXOSUYNIe2bjdmkf7bSkASQ+X7egiM8upZdbGNEMKIMG2VCB3FLI+9xs6XWhjnNy6zfjXOeMfFxHDIEwVpozcWHFsY34XVo+j9WiIqqYlCNPJFj09vCFPYg5o0EAs7+/P++EhiVrij9C5vTrdFHMEnlYNQkOEoMCDCZU1zqmWCbkN3kjTLhcA4BewpLoqbmhdmUwbq/yYoIzIq26JRG7GmpFPqVK0bClrfbkR24hi7/HJEdvC0LmA/DI6mbeXJA61o1qOZQjO/A9iuTTJ8KUQaJSjTqHeEYW3Gd1gBCjdkDN6ILmVKEy4U/IwWZy2MY1LGIJU0a5diFfbhIDSL92wx4vi+1kv5d3ZUNguzgMaNB9/3w9Y5pJaGST8d6QyfjOXk/xdo6pBXKO4VgoZjUNQ2AZyat2LjAvG7/Ih4CZbWBfbzFAa7lArhKv1xBgEFu2+rARFy5VzNsTa2s0IvJsZBT2SnbTxEkBDj2IdC0AGfdwkW3gZJje3tMNYWKCFMKRhti5BsOZeZoFQdTeAJ8KJu0d3X7z0Lkye+FKeaRPf/nRI+HDT4QKJ66SiJCFQXH+sRFGdcOm+TN5/bLqcXV0HLGgu/tBNmeKU9zLGtB9JphNHp5vYEk+35AyNowZXs8Rt7GIIDBVh9eCoRaJ0+JQu2Va2RKbK4iW6EVT1ASegTbGgSRxlOwDZlh+3zZNjtrlQsCGwEWiolV4MWFhAnG6jAPwsVTC40PrCmB3v51HKcRiSph64PMlB/GllnxZ8aWmE6+Lc3TSAJ7ofETa877jH0F26J3I59FP3OUcEiCgwhE88ixUGht10mFOpxf30C6zue8n85rF6wbtxHLCgKU3VXuR56Q8m0t5JDjCsb3ENETWUQCPkqLHVaDFYGruoKIkShk0DvTy6IYGGiR+WU1dtmC24A+heNjD305ebsI1TU9bPot+a1MWsUWEPCxNUrQCFYbJUya4sFUStTfOsd2oTuzCz6F26HFjcUlDN7ZBEo68AdueAoy7L3YXyqvsLbyn+1R0Cs8i7Y2T8crjniTO4+3AxhAgwKjd6TnyhM/S7dkOY19kHTO7ed/ZOs9r1IiPHAQeanp7A7+q9ZQXnI14Ijd7MZCQbJQUYPRE/ssO2ICxunCiI8Ph9U5qQBEqs0U/FCkXOVGwuWmsr229zZfvJo5vtsW2j1lsqWS8cIvAARmYpi2vgJk1cblJSQBERucJ+JvRKUGrttqnJfttQHrblzYvAFF0XlYrTCBQ6ItRxGHEeaRNViitDb7zIHIbpDlVDaKZdcYieTn+hCW3llmJRvjAOl4uK4ndwh7XRzedGkxmt0ntKc8CBZsWWO+dNpr03JYLE21jsnQ71J9EzCiRyhu3iGsuBKfK7AZRC1S0w4ajRQplZ5ByzyGPoVrZnWZOyNGWLU122abBkN20bJxziq+vR3pUMRNcwyikCVSgEjFmCaOmCcET1GbPf4jYbSuP24sq4daOLEctz3I9kjFXIzv1oKtwBCcEYZ4AyKFxqGnOWqEjFkSesRw75wsrCl0ZhoZH0Mh3WmGZHt9K4MsXp/MTWfMp/PC7jyewDDjS5xOSbmDb862ST351i9zp7TONWCpuPVlCIGkO21Pfh8gQUdQixZTbxCySTpZwNMjk4MJVDkM3acKFko6R9w6GL3RLhe9nXlG89BjkF7iJVsBsiuxIpNJYtVFFYffgrj+KwxTF0QFL7IhsYGrX1pSFbkOkGb+zGFJX40b1LUeiKw+0EZMk9SiDZx8k6YcmkqkNEzxYSWnHh8+56H8TU7w1H+O8CXXAUJ25cQ12gEqeSTfd01vyRuMYnsw8o0Fz8J//9EkSAD6L1PTwJYKT1Vcvo+XdsiNAv9l5jWFyAm0wy2hlGhT9UyNoWB5KijZbLNoY2dwL5pOrNt/kL2m3pohZGRr5tmGqzcgtcxrU8JIqBImqFQ6mwTesVK0IX3EFEj8PhUOVUuz3CSVovas0h22ibS8htxFUc5pVFWFM3pXySdRzLoRjSqDj+wyKxZXYAxA5+F6uf6TRqI3c/LjhyRwGyqEA8jlMAHso35wthqif/PWBA80/v+NFxtYT32VTaf5l2FGhRlZ43bgyam7amy0E2qcJNsuhJJhjtDOQnbBvcZAMC7ASySQ6QTPocFcL6F/bZwkXocphBlnxy9ArPjpxfYBjt21iOUVM0H+W4h2QUBwaRL6KiKKwwGVFX4Y4IYh9RuA4+QrYZGN9k/aUR6+AELWaxiBah4u4oeo4IPO7tV/6o63Npo+JVQSj3uEpdfaoydjh3GEDbyBFxPpcmqieGn6J1gdhqKpktesHa3m1sZXgac0CA5h/OvfEw9JV/zl6j32FeiQVl6uvpbnhYrZovViocPz/lup3hKXU1OXuikLeREgIt5/pOIiOM1Uc7AokuuhwWT7kRj4hPH3ZIZ5GF2IH9bJKTEYuqg0uNGpv4tRc46pdr9dDr0hHnujKliQxbXEu5AbqoUTuktthamLmuiaCOrQAckklnFHajeKhHKhzFqBQtSXVdVgREt0Q1Kjp+aUKvOIraRHZ4425BWT1tFIY/XB0oh6Cr5abB9kTN37hy5fml2257b5Rj99Z+D5o3vvJPu1K11F966co7UulKE4p9G+folJw4STmPXJJ3INmIzmQE5RrnPpMiZXmUbJXUPAs00nEgkQDLiny0sg4Mje1Bq3UwQXlMpw40MntwnE1xVZpGSV2T4lDLytS163qDCXQUV2T0RjvCKtxFOKIwiWWV1gX2RLbPBovDdlgruxnDrW8h8URkygrntbCpTp/ucLsmKEpdl0pzjCeqRn7Vr+pCfIQhSqllGs5EQfIJn6olNnIpn2Qq1T2VTj7itdvAU00fxHn3a9CcfPjJ8w5rPuI9A1PbzimVi51BqmLDgKWvVGQYTBeEvqRAd1Ni3rWQaEV2oJthYTXqXIACSMRJJIy6N1+PPN1oYQOI2BhabymTk4e0VW2kmLCRPJzAvfrE1UEgtxJjZKvFGzmPinZpccR5lFbhcJagucsmc4O2qThih7QsYZ0Me79FcKWRgap65wUcBxbKdvIOaULlHyRXGsJd8VGeEDCuhPAnCog5TXxLcT3T6eFjBEqp5yAW+BvaptBG7oHZn0GTOGbx7523oTJ50SNDmxeNoJWtZjmGlX2NFRq8mumymgCiYa00sLFyzSnYePKYsE/ZCLSaXkHElRUdNZvHnNNdgxnLsWvSUUygcK3N6+jKU7kKkKfhcuBRuNiAwiNTdxKHLDOZbreHy5N2QpBlp2MLRUsSI1GUTgTWJWKKu6gLcfJLvegwratO96FwMii741VRUFy9bJWlKzQhUGKfbI3Yiik/V0wk7vO7DjvgQRM8Vhyd2pAoNnPyMmtVkENajmDR7pIQINopKOG0PhRWC3CpBWVcQ/Hj7DBoJ08YSIsmAMvCDg3JPbs/m7YJdU0OMFFhIVWi9Hhi6rrKFBldoo6jdpTGgUhh+HW/TV02PjEBlxyzhYDel7JQWXWP2OIOys5tOLc4jm5Doy11i47TCJeEx9xHwFIaGRUTG92KjIbtO5s4Lrxt1s/43lDKr25ZvOxlGns/raHV91sT9DWNPlhMdD8YdK5YwGTQChRlSZt3GNwFOUUngAkw7lKjisnGfmz16/V4+SOANYZFeY5tr9qZy3KOidzey0kQU3Autay7yOvKjfzOTZBr+TgM2xFWdqOJ4wlzZbE2h/XATdW8HdbcZk16EQjXX2yUTFMdApDcLhbbgSNGWD1eonJYtEup9Fw6W8/lVWSDCcNcDle+HliLyDgf8K5Uqnb1Z7/6ooGG5E/qVIvsv+aJJwp2z5U3WWv72VYr/aFN9F5rE5vZADQeDgudYAt4BAhdEnLdpbAoXPuU3F6lKI1LG8ULeKRf1lG2pc0Vewwu01/k7XdlNeSTXOTKiMpU2eJyse3c+OuAVJyjXkhFF44fjXCldT5LJ0q2tcicIN8SY4M91dFRcR/axOcuKfp4FCn/HDPVreCWgKt1yRHWqUNu0jgQCSjIQQ2AiYETxodgiYkt2anKNIaHYm+iKf34xg6USHtoePoDwPzg42xVsxvttA/dbi3jf8TTnmNNhZej2m9C6FVr08I7PcfO/sZotaYM/UAqWbVFbRVjVYXdk83YQFndHvEuDay9Xg4JXNdDPtkuImb98suQxhnCFeW6hihOfoGRaYWxXL89BmiOZolFSzKFXimqRFZcpHPjaYwTkFS+5Bl1UXLXbzDkOtN+F/mUPzryhB0V1c6p0hMZK+7xt6jip3zKwvebyF9fMsHXLL5hlfIfs1blf9lE/10cATbmliGIyLx54YU7JnyjHcc7QIRpFnN8yIs7SnwpzrdRQBOglQ25xO7KEDgVj63LsQPZevdiP/EhmyBMZUTl4XRpGP5PcbJELyqCYQ6yhvoht3EcI8zisvHjOISK436J3sEOuYfCwjziQKEH/9MZ8jjIaX2PZ+PoqB8+Jts28XTZ4nhqOsCMpu2vff8mVkJdworvj7Gr7DrO1ljP6YlFt/BJj6OWjC+1dnzFYbENK+9qqtjidMUGWHA1ykLvaSI35AspRhxlO7cIpCsGiuqLw2TLH4WFmaJ45acONtaNwC02s9amxKfspm8vBIfL4ooIgeM2+5HIyTqyuaa7pbCq8LGVSa6nNi6JmFgo0CPHeP0fOu31OrRrj8yB0T3t7lFuuFDs9HZ72xXrrDbyarjNWZZpp8tqXWGJJuYZIOhTGYiaYSvKoe1Fa+Z8+keRZ0bY3B8TLMy6EwXkdX2CfvAIGOFQh3C6IQWLEAp3XVXUjbnkhClOHIG93mOFNnucKY5jWsdtSTPDb0DEEh5XhDQ2MfG1/kbZXVdHVqVxpTpQhundL4nCdLoHZdiN0W05o6E8paj7TPqPe4nasNdT11rGiZ7U5jYOcHPdewbYCX+9lXP/m67qK5YbvQtBOWvxFErUuCGHoNWcX7bZgnTVjmsr8rabbc6nmZeKQbNjujqQ6nkb4sUmduAyalKuOK0DD2likMmW8pEuarBcse1wG2m5pzlHyFGUX7ka6w79UbeFR12USxdaSo2RJ3TFv64khbkLwGA7bDOOZzFrX7LWzNlee24OXE7T+IyXXSjW+pCt+spmjs68k/Nb/oCjFl7P9MGxcB6EZR7TNVj0CqrVMN18iuKIlhKqfUZNU2iQlUgTQGG0SzP9+sob5Rex5FTL1xNH71+sF3FR/LiylJ5L4BKf0KirqZNlGQP2KCdtHtWet84UxzlG0Uorp4rQr6rTj5TUTo2sYuMicbt5JOpVsulbElTCEggOMzhH+BMqDb0C2qmNNJ4GGnts5gZo4sdd/UGdKv9LO+c7G5nFXAt4Xm+18mtZjrnUMm7VeZzSncG7HMDMY2ri/mybbSsJWDS7QNNoYko4WxF1R+RuTAxVY9CIYKKy4/rKwxVTVMRn+F1iycR2phVG2EzXgbZY54y7w5IEAN2Gsqh4fhxIFOSKCiPcyeridOqvSORwKScZXT4VgAndzunKVPpoADjhJf2tRy1c/rTLIaLcztLtzz3z3Xf02pX+tRwT/WmE5L/laMubLTtWMdbPhC3rs64lsONai/RagW3MZfhUMqBRa4RUkWP6Upi7CHKcKPIrvbvwi3juoouLR1aKjLuuMGHkV1oEYuaj+jj/+/H8KKO3qWjwFwm6qoI/bSt23ZArSnGxXodY6nMDQul3qNN1cbofjG5XYNkBMApXGMN1nXaBSx8b692+h5pgMjgTVRF755LNKOuK89hZn7jailN/Z1PZ/wA4W20K+ZmhZlu6bIc2lyyL8DskDbATaKMmVsvGV2OTxMARG6iDJ04bZVAaZa6DRW4uNyRWYQ3x7FyYYpP/FhaGjZcmSCblHAAgfQgAgSbyk2+XYXYU56pTNXHZqgajandnNGpS90R521nQNrInM9uN5cyt7qnxyWL36rPVZa21VVdtgXK3o+N5F4fAvJYlLk2Lm8q2Ltdk2zTUfjITN3z9lRV1Yo9sJYj8sVPUcl1TFB/JUNRPWr3hURlab4yybwsfrdjMjoclLQsZ9Omo1iiZFp4rNT9RSY5RKr9L4yJCqGguqp4ovg/y7mzUQ4aLuJysNMRXY/ZKnlF5c5jT7NRcq88eZLvBj+HNn17k5y4+pXliq9ZybaBrGmZvk6P9Tll28IoQupyJHC4sJicRomRIzYizaDSmJtYVxUVZnT8afo8ycbkpN2l5lqRKGgmLmU4fc5i46sZqdiiWCPfn7sGl3uHH5aN8dU+UrqmYTfw+5R6nHQqIPHOf0zQ+9eqzx/He9+FLrk4vnld55UghsWLDEDsmp1i5ldEkJbFOWG3MtJPbpVFY5JDlOE89AozgdlhSGICRFbOBcAikAMKJYNKyyne+Hy1ut5ciFLdxKIDP+cTq/qS7ccfVqgg3naBuRfnCvA4b1K0wBwhsRTujNHVPFOaCiHAZbIqtP5tZhCROvFdGr8BBZW699dbkicuD5Qua/SNZzfLoyNjUFTaORplloiFd96BJRBBnIoes+GrM7igpikeRzo/b+VUAbk2E0lcO11K2MTvCJ5rzIQBII+yFspGyhFwEsVgZozRyRHHOdjH1sDid7EYTYWmCRSHDS5f27rEmOC6j8RHjsDltDw4ONhWrdhocet6ituBHr1pa+TBHZ15oYyM/sMmxKacUdEIszeCIFtk7t0pIuzDSuUWKiHKidiMw5HbdVBTu/LhdekiAhriGsu8xdnAOFkZDTqDYenI5VATAIVA53Y/cLlGYuJ4+TBGmicpRFhmnuIbTkK8frXDfhaGOK4zcw9+DDjTJZLIFQfCl1VrAunPv3ss+QZf1nbN/yjkhF1l29F9sfPQBy+c0Jo0IE7Wko9ROrVoPw6GWjLsphety42EcAowDErYDZJwgspnptpYuW4dq+hG2/1Y1H6X0AokECGyXXUW5K8zn3IpTOjzucvehREqrdNNGvVK4GJ8uzTM+zZzc40nK6VK4nUbPXHcDFr9SqRxCY57INcqZwI/Un3n12dvMy32BA4P/lhMT77RsljNZ1cqkCGlUT7qDo5EujekcRZWSQEc82XFzR2FxuGbItYOTI/M3cgxsluG35A5g4KoWYEKwyY7A0VB0XIyzwxoVS9roit3YAg4R2p0zwe3s8XII5YpN/BSxf07bq1evZoWVnc6RH13Yj4+Pjz+6wwN/6z05u3LV9cyef9ByY1+3iTGmJdjf4DSupBQRdmdcOD+OSBHK6mEK1xU1teM0uJ3fJaJEbA2/mxfaRnZUbM4NsRO0GHEK5eXfXZGbAMdV6vcSgksJXS0uGT8yO1rhO8CXEYnYNqIvxu2DOahAMzU11QJ3ORWt6gSNvratrW33W1CvfOc9lpv8rGVHLreJiXWWyzPEAjkiQESEXdrahfPTmCZ2i+Kuf1Fz61K6hkuFqR9qmccnkJvtMQ5GyvNR1R2AEqFBYXH20BH7VR7/9UtACsGl4p2h+9Q+dtJwFr/Xf2Lrwuc5Tdw2T2a3trYur1arL4fTIAQGvz77bLTGT2auffd2W7Lin21i8uOA57+wxzmkBv4uqu3GOLav8JB4O9gKE1gcl4ncrhy54wunzrZp6rYHJyetH27jdDaRvKLsEpEch4ltqB8CIyxeRYVriaNisZyJqpDlFHueN0YRA5sO1+mCe28OKj0NDXYyTXQonOZXuPuuvPLKbgRjtaU1NzcH5XI5gBvVWlr45Ioz/cxztt3+wVsmtk6M1y7kkIC3sqN/CTqdaDxMohhErhT5yRoDSG6o48LUxTnhxAWE+ZTX6V+IUyYNv9sWcA5Orz3Ad5uO7FzBclQ21gEOR+woq1LLiKs4GQVbbgnETn5XuP6iBPV00a1R2hBp+3p6znjylyasYre/BxtoTqUV0nCaZRDifACThRgiBaKLPkfKTplksgp42DXrhkIsBqwEX35ForRxohL8vD87nq+WFgfpJg4ShcAYtyvSucIfNw/owvUTEQ/AiLgiqOa9HAAcuCK3COyoTwLOEw7mL7ZMMcdWmnFbwL5zN9QW31H34hBAObo9QCevCncAoRwXzU/UDbk0qlZcK7SFUziNn2Bs74Kw9s4cNKC57rrrjmLk9HKBhIY/kmb6Yy69/2p2EU1tKqIoLPa6M2SgT3BYm1VWtQToeAq0dNF9uVYbH5TJXS67SBOZyFGPd4Wq4DiBbIFl2h87vWCR9fNNhIn1HExdnc/x9uF8lG7Ryc+AxTE4gUilCDwqS57GyxVNgACmFLJAHmVwOnZtn4bbKvKgAM1VV121BFnmUzzvcVz3crmDlQGIDF4xgBprs9ERiwI7GScDk1Bf/2ni+wicG+xtz9UWb8n7JxQttQDu5EAUZgzf6HoREMrRytmhR35RUIQOY2O/IuilWDbRn89b7zgfVm/tsm52j+quImi4Lq9+k3Rv4SOIy8Sh2tKrZwH/BE0Dk/q8gINUvN6SFwyHte3975wHzY9+9KPWycnJdwOGt3FtoYk+VyqV7uF7lgKIazE4kGttgOX8gCdu/V1atEYcIAnWDQcLb9sSvGG0kj43yLQuSzQxZI5yOdiERTtMhEtXQq7i6lQ/pTN7Q/Ustk57hOjufkIwlfj0YblvzI5bNmrz+eaCx+hKxavsEBtATgHiNtiO24glxrdOHep32epOEsEzrJ9MBTZcDKYT1b2e3Y4bY06DBg6TQBdzDiC4iLcwCSg+v3jx4mvPOOOMvVqpFjfWDnbPrUnbNvi4JYvb+PD2+63ZPwFpGt4NO4pfbdkCj+vwZMsfXTXS6UMLDjjq50jkgKQMmCofzqxk7OGJfju8HRmHbbyavKxzGweGMKnQxL+rSraMKwU0Obi4wJivBVOIY/29XaN7tG87LG3HX4FxzppUKvUqwPLnvN2Hcn0T8MwOYNRiPXw864SHtppNfZuz8D/HYvZ7rZDVhvBwxCSyiVharKVWlhucOLf84gisuHO23C4NNv/iHu7E0NaFdl+eswDdfBRfr3UgIF5JSO84jkuvfOqmoq4q9NbT1/OFoJ2k9IHLLrtgn4bbqluPMSfN97///dPgLD2A5TQa7SaG0n997rnn7l6Zt68tcNttgT1wTd7mv24d3+/awM7PZXCTQ9jsz7GAUBXqQE0uOWRHJhabXDw/zlZc7MDWnipYQgVO0+VX7AgA5Ou8Ypm4SNft4I2yxQxOCdQdxcaFg+Ua+7YxT6TS3nW3PXT85jh+b23he86Z73znO4cgp4jDvATArMX+l3e/+92Dz9iD3vbegrUd9d9WKn3acuO/tOwE81biOBFQZNUvHDu45W+4xHIcCyEPi83LbKx7iIOuJzgh1GkBxE30IPqJk7rs01xGcVGqMB1+QQi5h5G5n6ML3eeRk6qeU6ABHN7111+/jKmCv8J9JtdWrr/mOe8CPNOvnp58ts1lp5Ttu6vuYBh2nuUnvm6TfMa2iAZZxBUI1E2FntBfB0pEeQcU3AKaiyM55+5U25fa1rJn6ya3w8gKrgSV4/6UlfS6FBF2Q/jljsLljtfgsNOhwgEBfeVJjuOagZlToPnGN77Rxsjo3bTH22nAPMPo/0TT+xumC8JTJmbQUHuc9cqzNiGxfpYPPF3FUosx4zzAcLws6kW4xRlSVtTV5aiPHddSdzDG72SrS4c9nB3nuLhJkmi0FAJDqZXSXfzUw6NAFe1iZSNr8doUAz85WPYzz3MaNQ0jpXRnZ+dKnOdzLaQBvwWX+f6b3vSmfZqUo4x9N98+azufSv07lll8i5nyvLGTMhofhxSOS3bE5acOGr3DApDCFInhGLhKywLbNFVkemEYyCAQuwiAI5c8cdooXIFxUFyUhuTINlMsJB9uyXBo4QzMnOA0PT09SUCyEs7yYYByiEZJjJy+vm7dum0zaJuZZf02E575xN+z+uBiOA5fQ2WUL0ajFneElh05YjsGQT2B0iAQo6fJBhm7j/moXDkbxtbLCMuLweFsglxRcVcnP5Xzxbgsa423fWb1qr1e4umKiH4OeNAAEv+44447EtCcx3UKz/Vrwr6+ffv2rYDJDRcaH/hZdV/39mFrHf1nOM4VCMe9bimpE22guIguUye+wuSJUFVHAX52Y+boptahJR5mCYyOwQ35TPwbF6QiprmMC43qcSOoRJDztdm/Pt5Sir03B/SQG1D4rPk9DM5yAUD5Q+wnaLTP4/7FeeedN3MF3t6356451vywaC9541pWAU4hJB+PyrYjPHE0oqZQE4k6YWZ5dggACZBJ+h8+RNbNztClACilk0sbUqr7cfkas+N2XvQzVc5TZtJtg5/2b7z1AbYtz8Ac0JzmxBNP7GKk9BZAcjZgmaR7Wo0g/AsE373eljGDNnz6rFecN2Ktwf/hDJ3/sDyrAYsc0qBX33EWssf4cfKMSNLAbVS60rHWJs8JoY/nszbKclC3OS7mKkQrSZ3LqLzGy3lpJc+yfD5Rs9szMgcsaBB829DFnEFTXEBjcS6sfZvrShR4QzNqkWcmc2CXsYDdz1/KN5b/3vLjD7JlhsXrcI86cUV1Ko9VvXVAKRCDQFxt6eagggob61h8zvcewhiXqZ5NgQ48ziH4SQB2807VZCIxWPWTM1ZwHpCg0d4lGuJUGucD2EuxfwrHueqhh6TW34/NFech49Su4YtzX0XOuZdj7nRWv6gcAcZRPASO4zaQpw4euig20o0y0bU+N+Y+h+g6nyire+q4HDyhMyqPKuAyZbjTMMfZz2i4rXoOONBEcszhAOUi7v80QHM7XdJlXV1djzzngq9a9OmMOI7mq4LSP8Jt1jJfxcJ1cRwIXDcRsR0vUbjAw8Vx+YU036LS8JuTJmp8OEScxAFEyZSt7ncuF6bRAGmKLJfoP+oFR+zxKZ7129nJccCB5vjjj18AYN7Pc6zk2owccyVLFe5i5nqfJ+B2apNn3vv/3jXBQUs/snJBHOduN9GpZRnhImDsCDQCSoiE8J50GBK7Mcc4nmQzQnGhogOswplvBxeXzSHHpQ+5DWxGs+OeX/CT/uBDPatn3E4HFGjgKlooJRnmQuws9v/Bvn6/E3xDEj/17zfeMsln7a62aulfAM1dVpjgWHboGQPFcZcYPLENufiud45lEo9PTfLtqlE3/Bau6sYlFZfB8OOG2jgZOfHVB7+/x2auhjigQHPNNde8Hs7yp4Cnmesq3FcBmL06L67euPuDQ8egTIz/xCr5r6AE/CVTD3RVmq+CLI19joOAYADX0EfGMvNsG/h6YmoMQSUWiIl2gIkezLmFmgg4vpUtU9vnhVeNzXXAgOa73/3uS7nxf4W7aKPbDwDNV1atWtXf+DAHpPsH75u0tqOv4uiKv7Pc8N0ccVtyi7PUVYnbxEhwIMLvuqgOG7ZmW883N3MsQJehXUgppPAfTWC6CMVxJRNe0a/5s6KKOCBAw/B6OdMCHwUox/D8a+iSPg9gttJQvEdzwGiGvCP/K6vlP2VT479hQRefx4tkHAcekT02uPWNbw4M6CtVrZ/TQStwm52NQOTQoghOtMc7Ua66HQg7J91r/34PGuljaIAeuqI383QjAOZvzzrrrN8QpkHB3DE6veGq993KKsA/s8nBtZYfroSrACGRk3MiW2Bw3KbLtgRpewidTY7DkELZGV4jfOmS5Txy8M2MpD9cSo3Mig5rvwbNTTfd1AFI/gwOI43vFNdfApifuxaZqz+r33sPXOYDVhy+ybIDJY62DUFTR4M4CGTTgQF8CmhzLs/OBfR1Wl/sTAgcB5gIQAjBZfzjR3YdO6PZ7bjJ91vQXHrppSmWaGqK4N08cBJO838BzHfiG5/T9jXn3cdxth+zwtgvOJ0UGUf79SGVgOMunl4fRgM02ziXbxNnEZeqnFQaN0oEFlmR7lD910hTW3JGs9tx8fV64oD9wb777rtT3d3dr2SN719wP4cAnKtwSwiOX6f94Taf2XtY/b51KADfj3zzAwTkbP2jIa7vERwgHcPvLF/91XzUiOajtNCKGCcQi7J4aDuSesXxpnTv2T0n7Cr87MNT7Heg4SH99evXHwtn+RNAcjT+texR+uI555zTtw/Pd2BnWf2BjaxiYKdD/haG5EXXVemJ6tyGk1OY8d4mgXhq1A2/HVKUJH5yJD/2QpW2tjbPeM4pLnK/Ag0A8W644YbD0PB+iBs8A9Dcj/1v/f39j+CeW4JvTIGns2uta5l2YBXgxK02NVZAGRjlABY65JpVfUMcT/Lw5BAfgmVEHaMF2zn5SQReaXmBY9FnyexXoOHQoW7mkd4Kl3kDAMpxXQ6Afn7hhe7bB7P0yAdYMfpU0eD2h+mePsfSil9YcRLgRKKJOI4+H42yb91Ugc8ojjDgqkSMyEEmfFjPKouL5bkHGnYRtMNN3s5T/jE2gyb/CkBz/dvfzuq3g93c1lPh4wl3mhX/HuBwPuB4Ify2ldgIAjHf/O6rpuxxDkMq1VCQR3jhTAqt0QtqST872pTao+9T7klT7xechqUOTcVi8VRAci7XMgBzA9zmahR4TwMYLVdzS9b25FkP7DSXs7eqNX23Vae+xiKu2wFPNFdFE+gwJD7OsYHdmDouPxwy8bjIwFpNU04lx29rmzcrOho14nMOGkCSGBoaeilA0UTkidzTHbi/Pjo6+vRyjPTBPXqvDiLg+PNvsXL2S8yOr7VitA1Yw+/WbtM3eDbwwfiKht8M0aUvZ0qBMwtsPO+VZmXe6TkHDYDxrr322hVwlXNw/y6gecQPgm/PmzfvwT2SYzSN0CMBWc1zkJjVTNCmAr7xUPxPRlQPWYnja8VS+KJLiQ/bP8p3FnLgQy2iRqFxau1BbfwN4/lZUeyplZ9TTnPzzTd3CzCA5TzuJY8m9GvJTOamWTnVQU83V42+a1UofA/h+AuMqNbBcdxyUJ0O+mC+ZBvz6onCAwOSCb86lkkNHr9y5aytBnjOQBNpfF8Hh5HgW+S6KlWt/tdb3sI6k+fN07fAzR9Gmzf1A5ZVfN2KE4+yoMst0MqyHHQDx+VX2fwgblMtVmvrzB87my+bP32he5biOQGNdkMuXLhQSzU/gvyyCMBcwxrfr775He+YNQl/zx7/AE/1/Y8wxZ34GjLOxVYYGXQHPbYu4kt5Ods63GfjQzn26RWqI9kC0vHsdeHPCWgAymFMC/w1oNFSh/8BNF9/61vfuhlb3fDzZm9aQF8NLvlXwmmuhePwpTGzIfZ9r9n2BPvzsoy3a7WWoDor62ji23rWQSMuQ+V/x/VmrvU80+eYiFwLYGaNfcYPd9DYN140yofPPmfDG2+xie3lWmnK1kz22mTAAq2E1U4qyDF75lkFDVMELXCZv4HDvBOQ9HL9K7qYH83e4xzEJd34kQ1WGu9h1d+vmY+qjLDGZlupnyF3rZaoZQ9MTiMOw1m9bwQwfw5pRwHMN4eHh6/Ffr5Lmi2s//xLzNVV/5GTuB7TUopNfMK5EBSC3tK6Ge9AaLzFZ4XTaKTEHNLvUPFnuJoAzreQaS5GF/PsHwPS+PRz0X3b5/8LBc0/Waq1b1Mlx1ddttXK2dKs6WjUZGykeWaNNrcxUjqaLbQfo2s6Ghnm1wDoq8wpPXPHmT2zj7T/l95WXG3WeWS+OPmn94ysH1lYHpw1HY0e/hkHDZv0BRQNrV8Fh1lHd/RJAPPo/t/yB/Ad3nxJ0Y78/X8rtrTlHh0fmN8/8eCsraV5xkGD4KulDu8CLK+nskGA8xVGSnccwORAN8881yU3p21kUTvatBZLsn2xorNWOc4zz+mMLfoKrRa2/LBgz+X5OBtuGbdTz7rOVrzpdeObDk/Y+JWz1uyM6p8Zg+DbCVfRnNInsPkChfd5OM7lbG6bVdQ/M3e/m1Lf+qV5fIliMWfFHM4moqM5xeE4y3CMQ1NzYJm2wFo7OAY2U2ODtdZdbkDRdpclyttsuGOLXXLMrMoUu7m73Qe95/qXWNvCf2dy8wZr7fyqXfLyWVlT84x0T9/85jc5893eyPUBwMK8vX2DL5t8lyWbe/0N6N23xrMYeuY/LrVycaVlx1/J6iaUkd4S7EVcXey99gFPn7W0P2qdS8atfd6xrNs9kinmPODZzDrLrdaZ+4V98r5f8ZGDe+wTR+3Tl9z2+Wlbuk5iH/cpgJrZzMIm67l1tTs0e58LDDPOOmjgMMwIJE6Gq5xHFccCmu9zXc35dwPYB44CDwHebvd+xyrls+AaK+Eeh9I3taJi1ZcqioxQWPhd+xWzzL9kbcuj7tvICf94SzWfy0lXK0l7MmlOwH6J+ZU38E3MX9qn197AUYn32RdOmlVl25NiQPUHJZSpwdHcy/m2tXoPaR9+0vR7GDHr3RMnhb+YkZLO8X0H1294Gf/q8MMPX3vKKewiPFDMmy7lTI9+VhEGf0Vj68st4pyh4YOphP+QaeX/NK/0sBpEFgAADklJREFUMJ+2HrebL9HC3cBWfbHZjn31MvNSfwygLiJkXpRLuyik4n8c+4eWrHzLek7dEMU9M9YFl6as9oIfcDzt63Rr/HNaQO3Llkl90b56xoyUfbPKaeAyC9G/vFOCL1zlcbjN11Hg3f+Hf/iHBw5gTv9Is031nQeBL6ArOllzOdPGk2xyBcdk/ocNLthga3Zau7z6LzW03WCfuvvf4TbowALAY4sIYwW4deE+GXupVdKL7W/u/4b944vvxv8MmcO6WUpzBEDhNsTga53Mc59mU7UX4Fk7k0pnTbnHR7jmAZazAMq7uKES7n/js343XnDBBQ06Aq2w07WfmlUApm3+e1hm9FfQma6FW3XbRWT7PId/I8eDfMFeXn1sF8BMPxKqtVO2W6LwWWSaf5sOdq4kwFlO9/ZONLf/2z629pSd4mfPW0suRK5awAVeYHQ1dauVY1i89TLqnxENZgU0F198cQZB99VwFx1nxvJ4+wFd1I/WrFkzQhi8MTaN7jhsP7FXXZWwye6z2BbyAQTcQwFLKjxkiCZyuxsTD8FhvmArrXePhtI9jFRSua+BuyvdE6oV4pYIgnbe/ldb0vuofew3v/WMvEjVYrMFZT5qBWAct5FdhesFJ9nZqztm0uozBo0E32XLlkng/VNu5Hiu23BfgeC7dffHme2nwJlc/1sMpz8AWF4MWAAMPYqOYtXF4btcl9mhC+7eI8DEFOk5XSdp9gCWh+KgyBbr4uNQ9iZOxPqUffpejoqd2du/U/l8K6raBEiSIZcBMDqFolptY7vvEZaxhbuk34uAGcs0jJRWIMd8GKCcSb1aG/MlZJhf78U9PPdJ3/TFE2jMD9FzvgLOIKSE9xQzcc/7obUccjkndO6lbMYL8g/2mP3NPf8L4HydtxyFYGTYXYJfwHkLZ4VM2MfXfhb3jEc2cfGAfz7PlHL+WK4J9GUyZKua11lPtw+OGXEauMx8uqM/YYR0NnXfi30xe7DZn3MAmTO/OJ9GfAcc5XVwk4Q7GFpfwk3wPsFwmDHuI/wfbPUMPspRnfwxQsW/AxxO86Rt3JlmOOIuK7C3w93eaT0b49HWzBvQq4jT+G5jnY5lm77SgHR6NLgPNe0zaK644opW6tNSh/O5pGn8LkLw9w+oAxN7+LxyxnshBPt9AIKyTkCJLoEmwfHgfupmu+EvfrMPbTud5fHttE/iSsDyqAOMwKIBTQygmvQ/wR+xSPwVdsHdIXeYzr1vLr4NDVDYL0e3pCPZBBon37CrLlHM7FuhYS5aZu+N5Bi4ihaFf5pLH7O4hk36l7MofM81niKYPZG2sbEma+EhauWSNf1kfK9khr2/9R1z3Pu1bvOb0WPUXuwiNFIKHfRQcnvDfG7nu1HgvlvaWvuxX2xi4/X/pby/oRtEEBZiGkwNBZyh2+m07Whu75ux5haZARmGSgCN0Kn6wjrZP+eO12qofO+c+wQaqngZYNGc0nKu1QDoKwCm/ymrvvixjPUNz7OAYw6migssdy/DwWAJE37L2bCDWj79gI2/9nvIjc/O3JSUX2Opl9Cwr6MRW0KQ6Aki4LDJjC7r+9bkrXnK59rTyM+9ImufvPc6vofNMDt4K9k0/J42jqDe79EmD9kYuh7jd2+MBOnGkWrAl8VqfCtX4w7Np8Y2IjLA3UvZbMcb2WvQ6FN/AOWvKeZEuiN96u8bCL5PL8BtzR9u1eTbzK8ei5ywnDdgKfSZTzmdNF4L153sS5bS6dkRoifbqbfwKrqf42hVqm3kMnjN30b3dJU1TarrnQUD1TIbN3MQ47fgNCcBnGMcaBqBY3yfx/Pegrx8tV0QrLXLvL0lrh4iLLFUZs7PCb5hUJ3TMAVS9fMzeaC9kmkk+LKA6nwqfDNgeRz70rGxsT0TfINSNw3yHh7pfFD/GuwXkX8ZVyv04q92KIR6kZ1/64yENMp7eiOdTM1fDmAAjd8WCr+8P6Ecg53mbUz82CY67jadhTdbpueIgiWaf4mi7Sbe/smwu4DGjqDQW9itBUcR8EfWsgaN7l6YkMtMQzCoLUY/g0KP7knf/XYKvipyTrWfCdihvSh5l6R7DBp9FJ3c5wOWD2Bn1SXxJbfVe7R9VtWWkw9y93fIOW30jPHFzLGh8JrXccR0/DPk6rI29pa+HKH3ZIATjpJ2sP2NTFJ+305tnf2Ne+vvH2Mn22rA8Us4Dksp4iYQeHDXkPU8NMYBXdW+GvdSVF7gwAJO3CcRdTxJrcJZJNV7ranct69FK98egUZrfCcmJs6gWzqfaz7XdwHN6jPPPHPPZ2u//JIxCPQdeMrgNFCiW3e44ajKgI98JbguCGZnBLH7lvHYALwEUPwe3KTDjZY8uExdkecjy6R+akHq/hkLo7urX0KxZR8EHNfCcXrr70y9+yBToKUXtXfbRx5U9733pjK8AMCc5BR6bqhNlSGn2cqnEdfa6o/M6FtYewQadC+/A4f5c+5ek103I8t8jiWbe78bspyD03hXcOnolF2wQwCsufZ6a1172N631B7muOBSBNDUqbwvrI9JcCs0wQ5XYhtE+6G1pQH3M2S+fMaYeW0/pFu6lmsYAPHY1KU2cV0UdtVexYHUf7RPd1AtvhzQSGEZg4Wy2TAXwOmrrXfxwKppn83Tgoaj5Y8EMNpvrRHT3Vz/weq7zftU45d+WxuM/5O8DzxJ/gwN9wpU679tH/rVjOZHnqR8XvLlCIgG61d3uIuR8u0X7JF+yC77g6ldYmczYNsDA7SFhvP/TZ15B5gdScncUe0Cu+jOF+5VtW/7d7hM7e2M0pQ/BE0AKmtVXobqLdbRtfcv+0438JSg0TeuAYkmIV9Hvn6mDD7LuTG371TG3nlbXrqOB7qYTDvqdDT14qZfvOVoMc9lRZxmY5/y/vauYlKvvDVpSWZ5fT1PAJvZ2TDcNQjZlt0y07dx55J38aubmte2BmL+G0LCLcRPj2jqXVWgebBP2EV3Ldkl/+4CJMsUS+9BZnqD647qk5W1IdryOsu3/ATNNn3VzMyTEoWRkr7cdhbFn82ldSTfRF/0sz0WfJ/svnrUNVV+AlGucSCpg0UZeNU45AjrdJR9Z9nHHtodN3iykp8+/KjBDqYM3kUdu5breehFvNVWTd8xqyOmp7orHdE6OXUPz3spXdRPsAvcW0MO13/+PnLeR+2iW58aOCt7kpbf8ka4yZ8CmIUNXKaM+wYr175pt31wz2XQhrvY2blb0ACYNAlfA0j0QdFuhN6vcX1v1j6RM+9Hm61UgdsE97pOPH6z1F7h1cHw/F0cFXa2ffzuGU2u7fDABZ+XANaNNi+uKLTRswc15oeqV9mxf/DsKBfjG7v8jILNP4W67ZNclwMadR+0glQuzrUMgZnZ95Z/tT+7c6VJKdlopNR76+eOspbWv0Hw/Ree4xiAQwlOOOqnvP9A4P8ne1VpvSutMe8+uqM7m84NSDwWVL0CYfeTXL9LzI3Ynz766KPXz+qSzQ+hIU6Ov49+/dO8/bu+RRwwCIE3AZ6PWnXwBrvkDTNb0f/e7x/CyOF6Sj2ZFm14bqHUbjev8k/WMvmTZ43LTDd56FLXkj601RbYK63iv5F2+S2AcxLPH4LE8wF2sInrGk4w/5mNPz5skxNo1UuvACTSJAOWGnKgG3kiL9nPWE/z/6yS+oXNP3psNrql+JYbGi8Muvrqq4/B9VnA8xbsn8FhPnP//fffufu1MXEx+2h/4r4u+uB3kPszEHORYBKayCHLQ6VetY+wMPvHprdyX8253/s6Wd/vXuKw3LCkIHiEsI9ZJXnTbDbsvt5mPd/5t84zf+p0Dpx+JUA5HtnmSPQsyxlRsbOAD3SX8nyyuYImneUVQXWCUddDPMd6wu5AEXy7/eRT+J8ZUwcNIPG/973vHYGw+wGquojrcUZMn2DJ5m1veMMM3/KnuveP3NFsXvIsVNt/AWc5CSaw49RGSGA0mN7lVit8ybqXj1jP4XCdPRg2inW/7fJOa2mSJvrz3Abdrti2jNb7Bg/TU33VxgevsJs/PDNOFhb6zPyKC6WGOmy82MX9L4GrMAXCbLXnw4+10L06yJqu7XbaSPbZmPB1oIm6pPl0Q1oUrp0EnAjpfxXAXApgJp6Zlmgotae3xUa2vQau8nc0AtsumIPZnfGC/7IqK+iK5TtQyo3YpS/TScsRW9opg5YYlO5fYKX0ByjzgwBEanUSOTEui2MDjf0vlrLr7VvvmRUBcac7mLNeBxqNlADJmTzlPwMYzXlcgv8yttBufdaeXBynhsIt8OEK3qupVyOcpnr9Dhruh1EOKnjztT2Ds1ja+q0yPm5fOp2uCwD1MKzesLnTyv6plHUuwDiH8FAu0CIoC6SFvZH837ar3ill4+5BV6/4ecfOLeBAw/HyryLiMwDmFdg3Min5aTS+Tz9zvXNps+H/s18fwQ7FcwDOayDosXAHWLIH54l0Nlrn4oRkLR1g9BX4t8I81iAbPW5VvgE5NbyMyf8zzKsySnL5NXMspd0w9v3cIvM+CMSr39s3G7d7MJbhwWUWIrt8hYeXAu9ndFGfZ2j9C8Lizv/Zb5ce5qEm7j2cibbT6cN/i+7qOMAxDzejA38hdiu6CKYDYBLVWolRQh9bMzZYOcdHtQqHErYEQHEmqgMWk3TeFgDzU5jKf9v3znv02X+guVVjEsFXH+A6kcdaD1Aub29v//VzChi1b49X4vdRhLr11vsHN1pz7Qj8bDKrLULpdyJzKocw1OwCKBm2zYKPCh1PiQ87sujILdnw1gKQrQiMDNmTj5nP0P2F714fHlStCp43M2kBT2t9EXjfBlBydEu37Pfn+J7/zXkIt50IxO3oK1rgKgEaU5YYAJhaUOCLawjufOW8i0Pnlm1/bo/7mAll9uO8/x9teQya14m6TgAAAABJRU5ErkJggg==\"\n      ></image>\n    </g>\n  </svg>\n);\n", "import React, { useMemo, useState, useRef, useEffect } from \"react\";\nimport { CloseIcon } from \"./icons\";\n\nexport function CopilotKitHelpModal() {\n  const [showHelpModal, setShowHelpModal] = useState(false);\n  const buttonRef = useRef<HTMLButtonElement>(null);\n  const popoverRef = useRef<HTMLDivElement>(null);\n\n  // Close popover when clicking outside\n  useEffect(() => {\n    const handleClickOutside = (event: MouseEvent) => {\n      if (\n        popoverRef.current &&\n        !popoverRef.current.contains(event.target as Node) &&\n        buttonRef.current &&\n        !buttonRef.current.contains(event.target as Node)\n      ) {\n        setShowHelpModal(false);\n      }\n    };\n\n    if (showHelpModal) {\n      document.addEventListener(\"mousedown\", handleClickOutside);\n    }\n\n    return () => {\n      document.removeEventListener(\"mousedown\", handleClickOutside);\n    };\n  }, [showHelpModal]);\n\n  const HelpButton = () => (\n    <button\n      ref={buttonRef}\n      onClick={() => setShowHelpModal(!showHelpModal)}\n      className=\"copilotKitDebugMenuTriggerButton relative\"\n      aria-label=\"Open Help\"\n    >\n      Help\n    </button>\n  );\n\n  return (\n    <div className=\"relative\">\n      <HelpButton />\n      {showHelpModal && (\n        <div\n          ref={popoverRef}\n          className=\"absolute mt-2 z-50\"\n          style={{\n            top: \"100%\",\n            right: \"-120px\",\n            width: \"380px\",\n          }}\n        >\n          <div className=\"copilotKitHelpModal rounded-lg shadow-xl w-full p-4 flex-col relative\">\n            <button\n              className=\"copilotKitHelpModalCloseButton absolute text-gray-400 hover:text-gray-600 focus:outline-none\"\n              style={{ top: \"10px\", right: \"10px\" }}\n              onClick={() => setShowHelpModal(false)}\n              aria-label=\"Close\"\n            >\n              <CloseIcon />\n            </button>\n            <div className=\"w-full flex mb-6 justify-center\">\n              <h2 className=\"text-2xl font-bold\">Help Options</h2>\n            </div>\n            <div className=\"space-y-4 mb-4\">\n              <div className=\"copilotKitHelpItemButton\">\n                <a\n                  href=\"https://docs.copilotkit.ai/coagents/troubleshooting/common-issues\"\n                  target=\"_blank\"\n                  rel=\"noopener noreferrer\"\n                >\n                  Visit the Troubleshooting and FAQ section in the docs\n                </a>\n              </div>\n              <div className=\"copilotKitHelpItemButton\">\n                <a\n                  href=\"https://go.copilotkit.ai/dev-console-support-discord\"\n                  target=\"_blank\"\n                  rel=\"noopener noreferrer\"\n                >\n                  Go to Discord Support Channel (Community Support)\n                </a>\n              </div>\n              <div className=\"copilotKitHelpItemButton\">\n                <a\n                  href=\"https://go.copilotkit.ai/dev-console-support-slack\"\n                  target=\"_blank\"\n                  rel=\"noopener noreferrer\"\n                >\n                  Apply for Priority Direct Slack Support\n                </a>\n              </div>\n            </div>\n          </div>\n        </div>\n      )}\n    </div>\n  );\n}\n", "import React from \"react\";\n\nexport const LifeBuoyIcon = () => (\n  <svg\n    xmlns=\"http://www.w3.org/2000/svg\"\n    width=\"24\"\n    height=\"24\"\n    viewBox=\"0 0 24 24\"\n    fill=\"none\"\n    stroke=\"currentColor\"\n    strokeWidth=\"2\"\n    strokeLinecap=\"round\"\n    strokeLinejoin=\"round\"\n    className=\"icon icon-tabler icons-tabler-outline icon-tabler-lifebuoy\"\n  >\n    <g transform=\"translate(0, -1)\">\n      <path stroke=\"none\" d=\"M0 0h24v24H0z\" fill=\"none\" />\n      <path d=\"M12 12m-4 0a4 4 0 1 0 8 0a4 4 0 1 0 -8 0\" />\n      <path d=\"M12 12m-9 0a9 9 0 1 0 18 0a9 9 0 1 0 -18 0\" />\n      <path d=\"M15 15l3.35 3.35\" />\n      <path d=\"M9 15l-3.35 3.35\" />\n      <path d=\"M5.65 5.65l3.35 3.35\" />\n      <path d=\"M18.35 5.65l-3.35 3.35\" />\n    </g>\n  </svg>\n);\n\nexport const CloseIcon = () => (\n  <svg\n    xmlns=\"http://www.w3.org/2000/svg\"\n    fill=\"none\"\n    viewBox=\"0 0 24 24\"\n    strokeWidth=\"1.5\"\n    stroke=\"currentColor\"\n    width=\"20\"\n    height=\"20\"\n  >\n    <path strokeLinecap=\"round\" strokeLinejoin=\"round\" d=\"M6 18L18 6M6 6l12 12\" />\n  </svg>\n);\n\nexport const LoadingSpinnerIcon = ({ color = \"rgb(107 114 128)\" }: { color?: string }) => (\n  <svg\n    style={{\n      animation: \"copilotKitSpinAnimation 1s linear infinite\",\n      color,\n    }}\n    width=\"24\"\n    height=\"24\"\n    xmlns=\"http://www.w3.org/2000/svg\"\n    fill=\"none\"\n    viewBox=\"0 0 24 24\"\n  >\n    <circle\n      style={{ opacity: 0.25 }}\n      cx=\"12\"\n      cy=\"12\"\n      r=\"10\"\n      stroke=\"currentColor\"\n      strokeWidth=\"4\"\n    ></circle>\n    <path\n      style={{ opacity: 0.75 }}\n      fill=\"currentColor\"\n      d=\"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\"\n    ></path>\n  </svg>\n);\n", "import { HeaderProps } from \"./props\";\nimport { useChatContext } from \"./ChatContext\";\nimport { CopilotDevConsole } from \"../dev-console\";\nimport React from \"react\";\n\nexport const Header = ({}: HeaderProps) => {\n  const { setOpen, icons, labels } = useChatContext();\n\n  return (\n    <div className=\"copilotKitHeader\">\n      <div>{labels.title}</div>\n      <div className=\"copilotKitHeaderControls\">\n        <CopilotDevConsole />\n        <button\n          onClick={() => setOpen(false)}\n          aria-label=\"Close\"\n          className=\"copilotKitHeaderCloseButton\"\n        >\n          {icons.headerCloseIcon}\n        </button>\n      </div>\n    </div>\n  );\n};\n", "import { useEffect, useMemo, useRef } from \"react\";\nimport { MessagesProps } from \"./props\";\nimport { useChatContext } from \"./ChatContext\";\nimport { Message, ResultMessage, TextMessage, Role } from \"@copilotkit/runtime-client-gql\";\nimport { useLangGraphInterruptRender } from \"@copilotkit/react-core\";\n\nexport const Messages = ({\n  messages,\n  inProgress,\n  children,\n  RenderTextMessage,\n  RenderActionExecutionMessage,\n  RenderAgentStateMessage,\n  RenderResultMessage,\n  RenderImageMessage,\n  AssistantMessage,\n  UserMessage,\n  onRegenerate,\n  onCopy,\n  onThumbsUp,\n  onThumbsDown,\n  markdownTagRenderers,\n}: MessagesProps) => {\n  const context = useChatContext();\n  const initialMessages = useMemo(\n    () => makeInitialMessages(context.labels.initial),\n    [context.labels.initial],\n  );\n\n  messages = [...initialMessages, ...messages];\n\n  const actionResults: Record<string, string> = {};\n\n  for (let i = 0; i < messages.length; i++) {\n    if (messages[i].isActionExecutionMessage()) {\n      const id = messages[i].id;\n      const resultMessage: ResultMessage | undefined = messages.find(\n        (message) => message.isResultMessage() && message.actionExecutionId === id,\n      ) as ResultMessage | undefined;\n\n      if (resultMessage) {\n        actionResults[id] = ResultMessage.decodeResult(resultMessage.result || \"\");\n      }\n    }\n  }\n\n  const { messagesContainerRef, messagesEndRef } = useScrollToBottom(messages);\n\n  const interrupt = useLangGraphInterruptRender();\n\n  return (\n    <div className=\"copilotKitMessages\" ref={messagesContainerRef}>\n      <div className=\"copilotKitMessagesContainer\">\n        {messages.map((message, index) => {\n          const isCurrentMessage = index === messages.length - 1;\n\n          if (message.isTextMessage()) {\n            return (\n              <RenderTextMessage\n                key={index}\n                message={message}\n                inProgress={inProgress}\n                index={index}\n                isCurrentMessage={isCurrentMessage}\n                AssistantMessage={AssistantMessage}\n                UserMessage={UserMessage}\n                onRegenerate={onRegenerate}\n                onCopy={onCopy}\n                onThumbsUp={onThumbsUp}\n                onThumbsDown={onThumbsDown}\n                markdownTagRenderers={markdownTagRenderers}\n              />\n            );\n          } else if (message.isActionExecutionMessage()) {\n            return (\n              <RenderActionExecutionMessage\n                key={index}\n                message={message}\n                inProgress={inProgress}\n                index={index}\n                isCurrentMessage={isCurrentMessage}\n                actionResult={actionResults[message.id]}\n                AssistantMessage={AssistantMessage}\n                UserMessage={UserMessage}\n              />\n            );\n          } else if (message.isAgentStateMessage()) {\n            return (\n              <RenderAgentStateMessage\n                key={index}\n                message={message}\n                inProgress={inProgress}\n                index={index}\n                isCurrentMessage={isCurrentMessage}\n                AssistantMessage={AssistantMessage}\n                UserMessage={UserMessage}\n              />\n            );\n          } else if (message.isResultMessage()) {\n            return (\n              <RenderResultMessage\n                key={index}\n                message={message}\n                inProgress={inProgress}\n                index={index}\n                isCurrentMessage={isCurrentMessage}\n                AssistantMessage={AssistantMessage}\n                UserMessage={UserMessage}\n              />\n            );\n          } else if (message.isImageMessage && message.isImageMessage()) {\n            return (\n              <RenderImageMessage\n                key={index}\n                message={message}\n                inProgress={inProgress}\n                index={index}\n                isCurrentMessage={isCurrentMessage}\n                AssistantMessage={AssistantMessage}\n                UserMessage={UserMessage}\n                onRegenerate={onRegenerate}\n                onCopy={onCopy}\n                onThumbsUp={onThumbsUp}\n                onThumbsDown={onThumbsDown}\n              />\n            );\n          }\n        })}\n        {interrupt}\n      </div>\n      <footer className=\"copilotKitMessagesFooter\" ref={messagesEndRef}>\n        {children}\n      </footer>\n    </div>\n  );\n};\n\nfunction makeInitialMessages(initial?: string | string[]): Message[] {\n  let initialArray: string[] = [];\n  if (initial) {\n    if (Array.isArray(initial)) {\n      initialArray.push(...initial);\n    } else {\n      initialArray.push(initial);\n    }\n  }\n\n  return initialArray.map(\n    (message) =>\n      new TextMessage({\n        role: Role.Assistant,\n        content: message,\n      }),\n  );\n}\n\nexport function useScrollToBottom(messages: any[]) {\n  const messagesEndRef = useRef<HTMLDivElement>(null);\n  const messagesContainerRef = useRef<HTMLDivElement | null>(null);\n  const isProgrammaticScrollRef = useRef(false);\n  const isUserScrollUpRef = useRef(false);\n\n  const scrollToBottom = () => {\n    if (messagesContainerRef.current && messagesEndRef.current) {\n      isProgrammaticScrollRef.current = true;\n      messagesContainerRef.current.scrollTop = messagesContainerRef.current.scrollHeight;\n    }\n  };\n\n  const handleScroll = () => {\n    if (isProgrammaticScrollRef.current) {\n      isProgrammaticScrollRef.current = false;\n      return;\n    }\n\n    if (messagesContainerRef.current) {\n      const { scrollTop, scrollHeight, clientHeight } = messagesContainerRef.current;\n      isUserScrollUpRef.current = scrollTop + clientHeight < scrollHeight;\n    }\n  };\n\n  useEffect(() => {\n    const container = messagesContainerRef.current;\n    if (container) {\n      container.addEventListener(\"scroll\", handleScroll);\n    }\n    return () => {\n      if (container) {\n        container.removeEventListener(\"scroll\", handleScroll);\n      }\n    };\n  }, []);\n\n  useEffect(() => {\n    const container = messagesContainerRef.current;\n    if (!container) {\n      return;\n    }\n\n    const mutationObserver = new MutationObserver(() => {\n      if (!isUserScrollUpRef.current) {\n        scrollToBottom();\n      }\n    });\n\n    mutationObserver.observe(container, {\n      childList: true,\n      subtree: true,\n      characterData: true,\n    });\n\n    return () => {\n      mutationObserver.disconnect();\n    };\n  }, []);\n\n  useEffect(() => {\n    isUserScrollUpRef.current = false;\n    scrollToBottom();\n  }, [messages.filter((m) => m.isTextMessage() && m.role === Role.User).length]);\n\n  return { messagesEndRef, messagesContainerRef };\n}\n", "import React, { useRef, useState } from \"react\";\nimport { InputProps } from \"./props\";\nimport { useChatContext } from \"./ChatContext\";\nimport AutoResizingTextarea from \"./Textarea\";\nimport { usePushToTalk } from \"../../hooks/use-push-to-talk\";\nimport { useCopilotContext } from \"@copilotkit/react-core\";\nimport { PoweredByTag } from \"./PoweredByTag\";\n\nconst MAX_NEWLINES = 6;\n\nexport const Input = ({ inProgress, onSend, isVisible = false, onStop, onUpload }: InputProps) => {\n  const context = useChatContext();\n  const copilotContext = useCopilotContext();\n\n  const showPoweredBy = !copilotContext.copilotApiConfig?.publicApiKey;\n\n  const pushToTalkConfigured =\n    copilotContext.copilotApiConfig.textToSpeechUrl !== undefined &&\n    copilotContext.copilotApiConfig.transcribeAudioUrl !== undefined;\n\n  const textareaRef = useRef<HTMLTextAreaElement>(null);\n\n  const handleDivClick = (event: React.MouseEvent<HTMLDivElement>) => {\n    const target = event.target as HTMLElement;\n\n    // If the user clicked a button or inside a button, don't focus the textarea\n    if (target.closest(\"button\")) return;\n\n    // If the user clicked the textarea, do nothing (it's already focused)\n    if (target.tagName === \"TEXTAREA\") return;\n\n    // Otherwise, focus the textarea\n    textareaRef.current?.focus();\n  };\n\n  const [text, setText] = useState(\"\");\n  const send = () => {\n    if (inProgress) return;\n    onSend(text);\n    setText(\"\");\n\n    textareaRef.current?.focus();\n  };\n\n  // tylerslaton:\n  //\n  // This scrolls CopilotKit into view always. Reading the commit history, it was likely\n  // added to fix a bug but it is causing issues now.\n  //\n  // For the future, if we want this behavior again, we will need to find a way to do it without\n  // forcing CopilotKit to always be in view. This code causes this because focusing an element\n  // in most browsers will scroll that element into view.\n  //\n  // useEffect(() => {\n  //   if (isVisible) {\n  //     textareaRef.current?.focus();\n  //   }\n  // }, [isVisible]);\n\n  const { pushToTalkState, setPushToTalkState } = usePushToTalk({\n    sendFunction: onSend,\n    inProgress,\n  });\n\n  const isInProgress = inProgress || pushToTalkState === \"transcribing\";\n  const buttonIcon = isInProgress ? context.icons.stopIcon : context.icons.sendIcon;\n  const showPushToTalk =\n    pushToTalkConfigured &&\n    (pushToTalkState === \"idle\" || pushToTalkState === \"recording\") &&\n    !inProgress;\n\n  const canSend = () => {\n    const interruptEvent = copilotContext.langGraphInterruptAction?.event;\n    const interruptInProgress =\n      interruptEvent?.name === \"LangGraphInterruptEvent\" && !interruptEvent?.response;\n\n    return (\n      (isInProgress || (!isInProgress && text.trim().length > 0)) &&\n      pushToTalkState === \"idle\" &&\n      !interruptInProgress\n    );\n  };\n\n  const sendDisabled = !canSend();\n\n  return (\n    <div className={`copilotKitInputContainer ${showPoweredBy ? \"poweredByContainer\" : \"\"}`}>\n      <div className=\"copilotKitInput\" onClick={handleDivClick}>\n        <AutoResizingTextarea\n          ref={textareaRef}\n          placeholder={context.labels.placeholder}\n          autoFocus={false}\n          maxRows={MAX_NEWLINES}\n          value={text}\n          onChange={(event) => setText(event.target.value)}\n          onKeyDown={(event) => {\n            if (event.key === \"Enter\" && !event.shiftKey) {\n              event.preventDefault();\n              if (canSend()) {\n                send();\n              }\n            }\n          }}\n        />\n        <div className=\"copilotKitInputControls\">\n          {onUpload && (\n            <button onClick={onUpload} className=\"copilotKitInputControlButton\">\n              {context.icons.uploadIcon}\n            </button>\n          )}\n\n          <div style={{ flexGrow: 1 }} />\n\n          {showPushToTalk && (\n            <button\n              onClick={() =>\n                setPushToTalkState(pushToTalkState === \"idle\" ? \"recording\" : \"transcribing\")\n              }\n              className={\n                pushToTalkState === \"recording\"\n                  ? \"copilotKitInputControlButton copilotKitPushToTalkRecording\"\n                  : \"copilotKitInputControlButton\"\n              }\n            >\n              {context.icons.pushToTalkIcon}\n            </button>\n          )}\n          <button\n            disabled={sendDisabled}\n            onClick={isInProgress ? onStop : send}\n            data-copilotkit-in-progress={inProgress}\n            data-test-id={inProgress ? \"copilot-chat-request-in-progress\" : \"copilot-chat-ready\"}\n            className=\"copilotKitInputControlButton\"\n          >\n            {buttonIcon}\n          </button>\n        </div>\n      </div>\n      <PoweredByTag showPoweredBy={showPoweredBy} />\n    </div>\n  );\n};\n", "import React, { useState, useRef, useEffect, forwardRef, useImperativeHandle } from \"react\";\n\ninterface AutoResizingTextareaProps {\n  maxRows?: number;\n  placeholder?: string;\n  value: string;\n  onChange: (event: React.ChangeEvent<HTMLTextAreaElement>) => void;\n  onKeyDown?: (event: React.KeyboardEvent<HTMLTextAreaElement>) => void;\n  autoFocus?: boolean;\n}\n\nconst AutoResizingTextarea = forwardRef<HTMLTextAreaElement, AutoResizingTextareaProps>(\n  ({ maxRows = 1, placeholder, value, onChange, onKeyDown, autoFocus }, ref) => {\n    const internalTextareaRef = useRef<HTMLTextAreaElement>(null);\n    const [maxHeight, setMaxHeight] = useState<number>(0);\n\n    useImperativeHandle(ref, () => internalTextareaRef.current as HTMLTextAreaElement);\n\n    useEffect(() => {\n      const calculateMaxHeight = () => {\n        const textarea = internalTextareaRef.current;\n        if (textarea) {\n          textarea.style.height = \"auto\";\n          const singleRowHeight = textarea.scrollHeight;\n          setMaxHeight(singleRowHeight * maxRows);\n          if (autoFocus) {\n            textarea.focus();\n          }\n        }\n      };\n\n      calculateMaxHeight();\n    }, [maxRows]);\n\n    useEffect(() => {\n      const textarea = internalTextareaRef.current;\n      if (textarea) {\n        textarea.style.height = \"auto\";\n        textarea.style.height = `${Math.min(textarea.scrollHeight, maxHeight)}px`;\n      }\n    }, [value, maxHeight]);\n\n    return (\n      <textarea\n        ref={internalTextareaRef}\n        value={value}\n        onChange={onChange}\n        onKeyDown={onKeyDown}\n        placeholder={placeholder}\n        style={{\n          overflow: \"auto\",\n          resize: \"none\",\n          maxHeight: `${maxHeight}px`,\n        }}\n        rows={1}\n      />\n    );\n  },\n);\n\nexport default AutoResizingTextarea;\n", "import { useCopilotContext, useCopilotMessagesContext } from \"@copilotkit/react-core\";\nimport { Message, TextMessage } from \"@copilotkit/runtime-client-gql\";\nimport { MutableRefObject, useEffect, useRef, useState } from \"react\";\n\nexport const checkMicrophonePermission = async () => {\n  try {\n    const permissionStatus = await navigator.permissions.query({\n      name: \"microphone\" as PermissionName,\n    });\n    if (permissionStatus.state === \"granted\") {\n      return true;\n    } else {\n      return false;\n    }\n  } catch (err) {\n    console.error(\"Error checking microphone permission\", err);\n  }\n};\n\nexport const requestMicAndPlaybackPermission = async () => {\n  try {\n    const stream = await navigator.mediaDevices.getUserMedia({ audio: true });\n    const audioContext = new window.AudioContext();\n    await audioContext.resume();\n    return { stream, audioContext };\n  } catch (err) {\n    console.error(\"Error requesting microphone and playback permissions\", err);\n    return null;\n  }\n};\n\nconst startRecording = async (\n  mediaStreamRef: MutableRefObject<MediaStream | null>,\n  mediaRecorderRef: MutableRefObject<MediaRecorder | null>,\n  audioContextRef: MutableRefObject<AudioContext | null>,\n  recordedChunks: Blob[],\n  onStop: () => void,\n) => {\n  if (!mediaStreamRef.current || !audioContextRef.current) {\n    mediaStreamRef.current = await navigator.mediaDevices.getUserMedia({ audio: true });\n    audioContextRef.current = new window.AudioContext();\n    await audioContextRef.current.resume();\n  }\n\n  mediaRecorderRef.current = new MediaRecorder(mediaStreamRef.current!);\n  mediaRecorderRef.current.start(1000);\n  mediaRecorderRef.current.ondataavailable = (event) => {\n    recordedChunks.push(event.data);\n  };\n  mediaRecorderRef.current.onstop = onStop;\n};\n\nconst stopRecording = (mediaRecorderRef: MutableRefObject<MediaRecorder | null>) => {\n  if (mediaRecorderRef.current && mediaRecorderRef.current.state !== \"inactive\") {\n    mediaRecorderRef.current.stop();\n  }\n};\n\nconst transcribeAudio = async (recordedChunks: Blob[], transcribeAudioUrl: string) => {\n  const completeBlob = new Blob(recordedChunks, { type: \"audio/mp4\" });\n  const formData = new FormData();\n  formData.append(\"file\", completeBlob, \"recording.mp4\");\n\n  const response = await fetch(transcribeAudioUrl, {\n    method: \"POST\",\n    body: formData,\n  });\n\n  if (!response.ok) {\n    throw new Error(`Error: ${response.statusText}`);\n  }\n\n  const transcription = await response.json();\n  return transcription.text;\n};\n\nconst playAudioResponse = (text: string, textToSpeechUrl: string, audioContext: AudioContext) => {\n  const encodedText = encodeURIComponent(text);\n  const url = `${textToSpeechUrl}?text=${encodedText}`;\n\n  fetch(url)\n    .then((response) => response.arrayBuffer())\n    .then((arrayBuffer) => audioContext.decodeAudioData(arrayBuffer))\n    .then((audioBuffer) => {\n      const source = audioContext.createBufferSource();\n      source.buffer = audioBuffer;\n      source.connect(audioContext.destination);\n      source.start(0);\n    })\n    .catch((error) => {\n      console.error(\"Error with decoding audio data\", error);\n    });\n};\n\nexport type PushToTalkState = \"idle\" | \"recording\" | \"transcribing\";\n\nexport type SendFunction = (text: string) => Promise<Message>;\n\nexport const usePushToTalk = ({\n  sendFunction,\n  inProgress,\n}: {\n  sendFunction: SendFunction;\n  inProgress: boolean;\n}) => {\n  const [pushToTalkState, setPushToTalkState] = useState<PushToTalkState>(\"idle\");\n  const mediaStreamRef = useRef<MediaStream | null>(null);\n  const audioContextRef = useRef<AudioContext | null>(null);\n  const mediaRecorderRef = useRef<MediaRecorder | null>(null);\n  const recordedChunks = useRef<Blob[]>([]);\n  const generalContext = useCopilotContext();\n  const messagesContext = useCopilotMessagesContext();\n  const context = { ...generalContext, ...messagesContext };\n  const [startReadingFromMessageId, setStartReadingFromMessageId] = useState<string | null>(null);\n\n  useEffect(() => {\n    if (pushToTalkState === \"recording\") {\n      startRecording(\n        mediaStreamRef,\n        mediaRecorderRef,\n        audioContextRef,\n        recordedChunks.current,\n        () => {\n          setPushToTalkState(\"transcribing\");\n        },\n      );\n    } else {\n      stopRecording(mediaRecorderRef);\n      if (pushToTalkState === \"transcribing\") {\n        transcribeAudio(recordedChunks.current, context.copilotApiConfig.transcribeAudioUrl!).then(\n          async (transcription) => {\n            recordedChunks.current = [];\n            setPushToTalkState(\"idle\");\n            const message = await sendFunction(transcription);\n            setStartReadingFromMessageId(message.id);\n          },\n        );\n      }\n    }\n\n    return () => {\n      stopRecording(mediaRecorderRef);\n    };\n  }, [pushToTalkState]);\n\n  useEffect(() => {\n    if (inProgress === false && startReadingFromMessageId) {\n      const lastMessageIndex = context.messages.findIndex(\n        (message) => message.id === startReadingFromMessageId,\n      );\n\n      const messagesAfterLast = context.messages\n        .slice(lastMessageIndex + 1)\n        .filter(\n          (message) => message.isTextMessage() && message.role === \"assistant\",\n        ) as TextMessage[];\n\n      const text = messagesAfterLast.map((message) => message.content).join(\"\\n\");\n      playAudioResponse(text, context.copilotApiConfig.textToSpeechUrl!, audioContextRef.current!);\n\n      setStartReadingFromMessageId(null);\n    }\n  }, [startReadingFromMessageId, inProgress]);\n\n  return { pushToTalkState, setPushToTalkState };\n};\n", "export const useDarkMode = () => {\n  if (typeof window === \"undefined\") return false;\n  return (\n    document.documentElement.classList.contains(\"dark\") ||\n    document.body.classList.contains(\"dark\") ||\n    document.documentElement.getAttribute(\"data-theme\") === \"dark\" ||\n    document.body.getAttribute(\"data-theme\") === \"dark\" ||\n    window.matchMedia(\"(prefers-color-scheme: dark)\").matches\n  );\n};\n", "import React from \"react\";\nimport { useDarkMode } from \"../../hooks/use-dark-mode\";\n\nexport function PoweredByTag({ showPoweredBy = true }: { showPoweredBy?: boolean }) {\n  const isDark = useDarkMode();\n\n  if (!showPoweredBy) {\n    return null;\n  }\n\n  const poweredByStyle = {\n    visibility: \"visible\",\n    display: \"block\",\n    position: \"static\",\n    textAlign: \"center\",\n    fontSize: \"12px\",\n    padding: \"3px 0\",\n    color: isDark ? \"rgb(69, 69, 69)\" : \"rgb(214, 214, 214)\",\n  };\n\n  return (\n    <div>\n      {/*@ts-expect-error -- expecting position not to be a string, but it can be.*/}\n      <p className=\"poweredBy\" style={poweredByStyle}>\n        Powered by CopilotKit\n      </p>\n    </div>\n  );\n}\n", "import { UserMessageProps } from \"../props\";\n\nexport const UserMessage = (props: UserMessageProps) => {\n  return (\n    <div className=\"copilotKitMessage copilotKitUserMessage\">\n      {props.subComponent || props.message}\n    </div>\n  );\n};\n", "import { FC, memo } from \"react\";\nimport ReactMarkdown, { Options, Components } from \"react-markdown\";\nimport { CodeBlock } from \"./CodeBlock\";\nimport remarkGfm from \"remark-gfm\";\nimport remarkMath from \"remark-math\";\nimport rehypeRaw from \"rehype-raw\";\n\nconst defaultComponents: Components = {\n  a({ children, ...props }) {\n    return (\n      <a className=\"copilotKitMarkdownElement\" {...props} target=\"_blank\" rel=\"noopener noreferrer\">\n        {children}\n      </a>\n    );\n  },\n  // @ts-expect-error -- inline\n  code({ children, className, inline, ...props }) {\n    if (Array.isArray(children) && children.length) {\n      if (children[0] == \"▍\") {\n        return (\n          <span\n            style={{\n              animation: \"pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite\",\n              marginTop: \"0.25rem\",\n            }}\n          >\n            ▍\n          </span>\n        );\n      }\n\n      children[0] = (children?.[0] as string).replace(\"`▍`\", \"▍\");\n    }\n\n    const match = /language-(\\w+)/.exec(className || \"\");\n\n    if (inline) {\n      return (\n        <code className={className} {...props}>\n          {children}\n        </code>\n      );\n    }\n\n    return (\n      <CodeBlock\n        key={Math.random()}\n        language={(match && match[1]) || \"\"}\n        value={String(children).replace(/\\n$/, \"\")}\n        {...props}\n      />\n    );\n  },\n  h1: ({ children, ...props }) => (\n    <h1 className=\"copilotKitMarkdownElement\" {...props}>\n      {children}\n    </h1>\n  ),\n  h2: ({ children, ...props }) => (\n    <h2 className=\"copilotKitMarkdownElement\" {...props}>\n      {children}\n    </h2>\n  ),\n  h3: ({ children, ...props }) => (\n    <h3 className=\"copilotKitMarkdownElement\" {...props}>\n      {children}\n    </h3>\n  ),\n  h4: ({ children, ...props }) => (\n    <h4 className=\"copilotKitMarkdownElement\" {...props}>\n      {children}\n    </h4>\n  ),\n  h5: ({ children, ...props }) => (\n    <h5 className=\"copilotKitMarkdownElement\" {...props}>\n      {children}\n    </h5>\n  ),\n  h6: ({ children, ...props }) => (\n    <h6 className=\"copilotKitMarkdownElement\" {...props}>\n      {children}\n    </h6>\n  ),\n  p: ({ children, ...props }) => (\n    <p className=\"copilotKitMarkdownElement\" {...props}>\n      {children}\n    </p>\n  ),\n  pre: ({ children, ...props }) => (\n    <pre className=\"copilotKitMarkdownElement\" {...props}>\n      {children}\n    </pre>\n  ),\n  blockquote: ({ children, ...props }) => (\n    <blockquote className=\"copilotKitMarkdownElement\" {...props}>\n      {children}\n    </blockquote>\n  ),\n  ul: ({ children, ...props }) => (\n    <ul className=\"copilotKitMarkdownElement\" {...props}>\n      {children}\n    </ul>\n  ),\n  li: ({ children, ...props }) => (\n    <li className=\"copilotKitMarkdownElement\" {...props}>\n      {children}\n    </li>\n  ),\n};\n\nconst MemoizedReactMarkdown: FC<Options> = memo(\n  ReactMarkdown,\n  (prevProps, nextProps) =>\n    prevProps.children === nextProps.children && prevProps.components === nextProps.components,\n);\n\ntype MarkdownProps = {\n  content: string;\n  components?: Components;\n};\n\nexport const Markdown = ({ content, components }: MarkdownProps) => {\n  return (\n    <div className=\"copilotKitMarkdown\">\n      <MemoizedReactMarkdown\n        components={{ ...defaultComponents, ...components }}\n        remarkPlugins={[remarkGfm, remarkMath]}\n        rehypePlugins={[rehypeRaw]}\n      >\n        {content}\n      </MemoizedReactMarkdown>\n    </div>\n  );\n};\n", "import { FC, memo } from \"react\";\nimport { Prism as Synta<PERSON><PERSON><PERSON><PERSON>er } from \"react-syntax-highlighter\";\nimport { useCopyToClipboard } from \"../../hooks/use-copy-to-clipboard\";\nimport { CheckIcon, CopyIcon, DownloadIcon } from \"./Icons\";\n\ninterface CodeActionButtonProps {\n  onClick: () => void;\n  children: React.ReactNode;\n}\n\ninterface Props {\n  language: string;\n  value: string;\n}\n\ninterface languageMap {\n  [key: string]: string | undefined;\n}\n\nexport const programmingLanguages: languageMap = {\n  javascript: \".js\",\n  python: \".py\",\n  java: \".java\",\n  c: \".c\",\n  cpp: \".cpp\",\n  \"c++\": \".cpp\",\n  \"c#\": \".cs\",\n  ruby: \".rb\",\n  php: \".php\",\n  swift: \".swift\",\n  \"objective-c\": \".m\",\n  kotlin: \".kt\",\n  typescript: \".ts\",\n  go: \".go\",\n  perl: \".pl\",\n  rust: \".rs\",\n  scala: \".scala\",\n  haskell: \".hs\",\n  lua: \".lua\",\n  shell: \".sh\",\n  sql: \".sql\",\n  html: \".html\",\n  css: \".css\",\n  // add more file extensions here, make sure the key is same as language prop in CodeBlock.tsx component\n};\n\nexport const generateRandomString = (length: number, lowercase = false) => {\n  const chars = \"ABCDEFGHJKLMNPQRSTUVWXY3456789\"; // excluding similar looking characters like Z, 2, I, 1, O, 0\n  let result = \"\";\n  for (let i = 0; i < length; i++) {\n    result += chars.charAt(Math.floor(Math.random() * chars.length));\n  }\n  return lowercase ? result.toLowerCase() : result;\n};\n\nconst CodeBlock: FC<Props> = memo(({ language, value }) => {\n  const { isCopied, copyToClipboard } = useCopyToClipboard({ timeout: 2000 });\n\n  const downloadAsFile = () => {\n    if (typeof window === \"undefined\") {\n      return;\n    }\n    const fileExtension = programmingLanguages[language] || \".file\";\n    const suggestedFileName = `file-${generateRandomString(3, true)}${fileExtension}`;\n    const fileName = window.prompt(\"Enter file name\" || \"\", suggestedFileName);\n\n    if (!fileName) {\n      // User pressed cancel on prompt.\n      return;\n    }\n\n    const blob = new Blob([value], { type: \"text/plain\" });\n    const url = URL.createObjectURL(blob);\n    const link = document.createElement(\"a\");\n    link.download = fileName;\n    link.href = url;\n    link.style.display = \"none\";\n    document.body.appendChild(link);\n    link.click();\n    document.body.removeChild(link);\n    URL.revokeObjectURL(url);\n  };\n\n  const onCopy = () => {\n    if (isCopied) return;\n    copyToClipboard(value);\n  };\n\n  return (\n    <div className=\"copilotKitCodeBlock\">\n      <div className=\"copilotKitCodeBlockToolbar\">\n        <span className=\"copilotKitCodeBlockToolbarLanguage\">{language}</span>\n        <div className=\"copilotKitCodeBlockToolbarButtons\">\n          <button className=\"copilotKitCodeBlockToolbarButton\" onClick={downloadAsFile}>\n            {DownloadIcon}\n          </button>\n          <button className=\"copilotKitCodeBlockToolbarButton\" onClick={onCopy}>\n            {isCopied ? CheckIcon : CopyIcon}\n          </button>\n        </div>\n      </div>\n      <SyntaxHighlighter\n        language={language}\n        style={highlightStyle}\n        PreTag=\"div\"\n        customStyle={{\n          margin: 0,\n          borderBottomLeftRadius: \"0.375rem\",\n          borderBottomRightRadius: \"0.375rem\",\n        }}\n      >\n        {value}\n      </SyntaxHighlighter>\n    </div>\n  );\n});\nCodeBlock.displayName = \"CodeBlock\";\n\nexport { CodeBlock };\n\n// import { vscDarkPlus as highlightStyle } from \"react-syntax-highlighter/dist/esm/styles/prism\";\n// As a workaround, we inline the vscDarkPlus from react-syntax-highlighter.\n// Importing it as recommended in the documentation leads to build errors in the non app router\n// (Next.js classic) setup.\nconst highlightStyle: any = {\n  'pre[class*=\"language-\"]': {\n    color: \"#d4d4d4\",\n    fontSize: \"13px\",\n    textShadow: \"none\",\n    fontFamily: 'Menlo, Monaco, Consolas, \"Andale Mono\", \"Ubuntu Mono\", \"Courier New\", monospace',\n    direction: \"ltr\",\n    textAlign: \"left\",\n    whiteSpace: \"pre\",\n    wordSpacing: \"normal\",\n    wordBreak: \"normal\",\n    lineHeight: \"1.5\",\n    MozTabSize: \"4\",\n    OTabSize: \"4\",\n    tabSize: \"4\",\n    WebkitHyphens: \"none\",\n    MozHyphens: \"none\",\n    msHyphens: \"none\",\n    hyphens: \"none\",\n    padding: \"1em\",\n    margin: \".5em 0\",\n    overflow: \"auto\",\n    background: \"#1e1e1e\",\n  },\n  'code[class*=\"language-\"]': {\n    color: \"#d4d4d4\",\n    fontSize: \"13px\",\n    textShadow: \"none\",\n    fontFamily: 'Menlo, Monaco, Consolas, \"Andale Mono\", \"Ubuntu Mono\", \"Courier New\", monospace',\n    direction: \"ltr\",\n    textAlign: \"left\",\n    whiteSpace: \"pre\",\n    wordSpacing: \"normal\",\n    wordBreak: \"normal\",\n    lineHeight: \"1.5\",\n    MozTabSize: \"4\",\n    OTabSize: \"4\",\n    tabSize: \"4\",\n    WebkitHyphens: \"none\",\n    MozHyphens: \"none\",\n    msHyphens: \"none\",\n    hyphens: \"none\",\n  },\n  'pre[class*=\"language-\"]::selection': {\n    textShadow: \"none\",\n    background: \"#264F78\",\n  },\n  'code[class*=\"language-\"]::selection': {\n    textShadow: \"none\",\n    background: \"#264F78\",\n  },\n  'pre[class*=\"language-\"] *::selection': {\n    textShadow: \"none\",\n    background: \"#264F78\",\n  },\n  'code[class*=\"language-\"] *::selection': {\n    textShadow: \"none\",\n    background: \"#264F78\",\n  },\n  ':not(pre) > code[class*=\"language-\"]': {\n    padding: \".1em .3em\",\n    borderRadius: \".3em\",\n    color: \"#db4c69\",\n    background: \"#1e1e1e\",\n  },\n  \".namespace\": {\n    Opacity: \".7\",\n  },\n  \"doctype.doctype-tag\": {\n    color: \"#569CD6\",\n  },\n  \"doctype.name\": {\n    color: \"#9cdcfe\",\n  },\n  comment: {\n    color: \"#6a9955\",\n  },\n  prolog: {\n    color: \"#6a9955\",\n  },\n  punctuation: {\n    color: \"#d4d4d4\",\n  },\n  \".language-html .language-css .token.punctuation\": {\n    color: \"#d4d4d4\",\n  },\n  \".language-html .language-javascript .token.punctuation\": {\n    color: \"#d4d4d4\",\n  },\n  property: {\n    color: \"#9cdcfe\",\n  },\n  tag: {\n    color: \"#569cd6\",\n  },\n  boolean: {\n    color: \"#569cd6\",\n  },\n  number: {\n    color: \"#b5cea8\",\n  },\n  constant: {\n    color: \"#9cdcfe\",\n  },\n  symbol: {\n    color: \"#b5cea8\",\n  },\n  inserted: {\n    color: \"#b5cea8\",\n  },\n  unit: {\n    color: \"#b5cea8\",\n  },\n  selector: {\n    color: \"#d7ba7d\",\n  },\n  \"attr-name\": {\n    color: \"#9cdcfe\",\n  },\n  string: {\n    color: \"#ce9178\",\n  },\n  char: {\n    color: \"#ce9178\",\n  },\n  builtin: {\n    color: \"#ce9178\",\n  },\n  deleted: {\n    color: \"#ce9178\",\n  },\n  \".language-css .token.string.url\": {\n    textDecoration: \"underline\",\n  },\n  operator: {\n    color: \"#d4d4d4\",\n  },\n  entity: {\n    color: \"#569cd6\",\n  },\n  \"operator.arrow\": {\n    color: \"#569CD6\",\n  },\n  atrule: {\n    color: \"#ce9178\",\n  },\n  \"atrule.rule\": {\n    color: \"#c586c0\",\n  },\n  \"atrule.url\": {\n    color: \"#9cdcfe\",\n  },\n  \"atrule.url.function\": {\n    color: \"#dcdcaa\",\n  },\n  \"atrule.url.punctuation\": {\n    color: \"#d4d4d4\",\n  },\n  keyword: {\n    color: \"#569CD6\",\n  },\n  \"keyword.module\": {\n    color: \"#c586c0\",\n  },\n  \"keyword.control-flow\": {\n    color: \"#c586c0\",\n  },\n  function: {\n    color: \"#dcdcaa\",\n  },\n  \"function.maybe-class-name\": {\n    color: \"#dcdcaa\",\n  },\n  regex: {\n    color: \"#d16969\",\n  },\n  important: {\n    color: \"#569cd6\",\n  },\n  italic: {\n    fontStyle: \"italic\",\n  },\n  \"class-name\": {\n    color: \"#4ec9b0\",\n  },\n  \"maybe-class-name\": {\n    color: \"#4ec9b0\",\n  },\n  console: {\n    color: \"#9cdcfe\",\n  },\n  parameter: {\n    color: \"#9cdcfe\",\n  },\n  interpolation: {\n    color: \"#9cdcfe\",\n  },\n  \"punctuation.interpolation-punctuation\": {\n    color: \"#569cd6\",\n  },\n  variable: {\n    color: \"#9cdcfe\",\n  },\n  \"imports.maybe-class-name\": {\n    color: \"#9cdcfe\",\n  },\n  \"exports.maybe-class-name\": {\n    color: \"#9cdcfe\",\n  },\n  escape: {\n    color: \"#d7ba7d\",\n  },\n  \"tag.punctuation\": {\n    color: \"#808080\",\n  },\n  cdata: {\n    color: \"#808080\",\n  },\n  \"attr-value\": {\n    color: \"#ce9178\",\n  },\n  \"attr-value.punctuation\": {\n    color: \"#ce9178\",\n  },\n  \"attr-value.punctuation.attr-equals\": {\n    color: \"#d4d4d4\",\n  },\n  namespace: {\n    color: \"#4ec9b0\",\n  },\n  'pre[class*=\"language-javascript\"]': {\n    color: \"#9cdcfe\",\n  },\n  'code[class*=\"language-javascript\"]': {\n    color: \"#9cdcfe\",\n  },\n  'pre[class*=\"language-jsx\"]': {\n    color: \"#9cdcfe\",\n  },\n  'code[class*=\"language-jsx\"]': {\n    color: \"#9cdcfe\",\n  },\n  'pre[class*=\"language-typescript\"]': {\n    color: \"#9cdcfe\",\n  },\n  'code[class*=\"language-typescript\"]': {\n    color: \"#9cdcfe\",\n  },\n  'pre[class*=\"language-tsx\"]': {\n    color: \"#9cdcfe\",\n  },\n  'code[class*=\"language-tsx\"]': {\n    color: \"#9cdcfe\",\n  },\n  'pre[class*=\"language-css\"]': {\n    color: \"#ce9178\",\n  },\n  'code[class*=\"language-css\"]': {\n    color: \"#ce9178\",\n  },\n  'pre[class*=\"language-html\"]': {\n    color: \"#d4d4d4\",\n  },\n  'code[class*=\"language-html\"]': {\n    color: \"#d4d4d4\",\n  },\n  \".language-regex .token.anchor\": {\n    color: \"#dcdcaa\",\n  },\n  \".language-html .token.punctuation\": {\n    color: \"#808080\",\n  },\n  'pre[class*=\"language-\"] > code[class*=\"language-\"]': {\n    position: \"relative\",\n    zIndex: \"1\",\n  },\n  \".line-highlight.line-highlight\": {\n    background: \"#f7ebc6\",\n    boxShadow: \"inset 5px 0 0 #f7d87c\",\n    zIndex: \"0\",\n  },\n};\n", "import * as React from \"react\";\n\nexport interface useCopyToClipboardProps {\n  timeout?: number;\n}\n\nexport function useCopyToClipboard({ timeout = 2000 }: useCopyToClipboardProps) {\n  const [isCopied, setIsCopied] = React.useState<Boolean>(false);\n\n  const copyToClipboard = (value: string) => {\n    if (typeof window === \"undefined\" || !navigator.clipboard?.writeText) {\n      return;\n    }\n\n    if (!value) {\n      return;\n    }\n\n    navigator.clipboard.writeText(value).then(() => {\n      setIsCopied(true);\n\n      setTimeout(() => {\n        setIsCopied(false);\n      }, timeout);\n    });\n  };\n\n  return { isCopied, copyToClipboard };\n}\n", "import { AssistantMessageProps } from \"../props\";\nimport { useChatContext } from \"../ChatContext\";\nimport { Markdown } from \"../Markdown\";\nimport { useState } from \"react\";\n\nexport const AssistantMessage = (props: AssistantMessageProps) => {\n  const { icons, labels } = useChatContext();\n  const {\n    message,\n    isLoading,\n    subComponent,\n    onRegenerate,\n    onCopy,\n    onThumbsUp,\n    onThumbsDown,\n    isCurrentMessage,\n    markdownTagRenderers,\n  } = props;\n  const [copied, setCopied] = useState(false);\n\n  const handleCopy = () => {\n    if (message && onCopy) {\n      navigator.clipboard.writeText(message);\n      setCopied(true);\n      onCopy(message);\n      setTimeout(() => setCopied(false), 2000);\n    } else if (message) {\n      navigator.clipboard.writeText(message);\n      setCopied(true);\n      setTimeout(() => setCopied(false), 2000);\n    }\n  };\n\n  const handleRegenerate = () => {\n    if (onRegenerate) {\n      onRegenerate();\n    }\n  };\n\n  const handleThumbsUp = () => {\n    if (onThumbsUp && message) {\n      onThumbsUp(message);\n    }\n  };\n\n  const handleThumbsDown = () => {\n    if (onThumbsDown && message) {\n      onThumbsDown(message);\n    }\n  };\n\n  const LoadingIcon = () => <span>{icons.activityIcon}</span>;\n\n  return (\n    <>\n      {(message || isLoading) && (\n        <div className=\"copilotKitMessage copilotKitAssistantMessage\">\n          {message && <Markdown content={message || \"\"} components={markdownTagRenderers} />}\n          {isLoading && <LoadingIcon />}\n\n          {message && !isLoading && (\n            <div\n              className={`copilotKitMessageControls ${isCurrentMessage ? \"currentMessage\" : \"\"}`}\n            >\n              <button\n                className=\"copilotKitMessageControlButton\"\n                onClick={handleRegenerate}\n                aria-label={labels.regenerateResponse}\n                title={labels.regenerateResponse}\n              >\n                {icons.regenerateIcon}\n              </button>\n              <button\n                className=\"copilotKitMessageControlButton\"\n                onClick={handleCopy}\n                aria-label={labels.copyToClipboard}\n                title={labels.copyToClipboard}\n              >\n                {copied ? (\n                  <span style={{ fontSize: \"10px\", fontWeight: \"bold\" }}>✓</span>\n                ) : (\n                  icons.copyIcon\n                )}\n              </button>\n              {onThumbsUp && (\n                <button\n                  className=\"copilotKitMessageControlButton\"\n                  onClick={handleThumbsUp}\n                  aria-label={labels.thumbsUp}\n                  title={labels.thumbsUp}\n                >\n                  {icons.thumbsUpIcon}\n                </button>\n              )}\n              {onThumbsDown && (\n                <button\n                  className=\"copilotKitMessageControlButton\"\n                  onClick={handleThumbsDown}\n                  aria-label={labels.thumbsDown}\n                  title={labels.thumbsDown}\n                >\n                  {icons.thumbsDownIcon}\n                </button>\n              )}\n            </div>\n          )}\n        </div>\n      )}\n      <div style={{ marginBottom: \"0.5rem\" }}>{subComponent}</div>\n    </>\n  );\n};\n", "import { RenderMessageProps } from \"../props\";\nimport { UserMessage as DefaultUserMessage } from \"./UserMessage\";\nimport { AssistantMessage as DefaultAssistantMessage } from \"./AssistantMessage\";\n\nexport function RenderTextMessage({\n  UserMessage = DefaultUserMessage,\n  AssistantMessage = DefaultAssistantMessage,\n  ...props\n}: RenderMessageProps) {\n  const {\n    message,\n    inProgress,\n    index,\n    isCurrentMessage,\n    onRegenerate,\n    onCopy,\n    onThumbsUp,\n    onThumbsDown,\n    markdownTagRenderers,\n  } = props;\n\n  if (message.isTextMessage()) {\n    if (message.role === \"user\") {\n      return (\n        <UserMessage\n          key={index}\n          data-message-role=\"user\"\n          message={message.content}\n          rawData={message}\n        />\n      );\n    } else if (message.role == \"assistant\") {\n      return (\n        <AssistantMessage\n          key={index}\n          data-message-role=\"assistant\"\n          message={message.content}\n          rawData={message}\n          isLoading={inProgress && isCurrentMessage && !message.content}\n          isGenerating={inProgress && isCurrentMessage && !!message.content}\n          isCurrentMessage={isCurrentMessage}\n          onRegenerate={() => onRegenerate?.(message.id)}\n          onCopy={onCopy}\n          onThumbsUp={onThumbsUp}\n          onThumbsDown={onThumbsDown}\n          markdownTagRenderers={markdownTagRenderers}\n        />\n      );\n    }\n  }\n}\n", "import { MessageStatusCode } from \"@copilotkit/runtime-client-gql\";\nimport { RenderMessageProps } from \"../props\";\nimport { RenderFunctionStatus, useCopilotContext } from \"@copilotkit/react-core\";\nimport { AssistantMessage as DefaultAssistantMessage } from \"./AssistantMessage\";\n\nexport function RenderActionExecutionMessage({\n  AssistantMessage = DefaultAssistantMessage,\n  ...props\n}: RenderMessageProps) {\n  const { chatComponentsCache } = useCopilotContext();\n  const { message, inProgress, index, isCurrentMessage, actionResult } = props;\n\n  if (message.isActionExecutionMessage()) {\n    if (\n      chatComponentsCache.current !== null &&\n      (chatComponentsCache.current.actions[message.name] ||\n        chatComponentsCache.current.actions[\"*\"])\n    ) {\n      const render =\n        chatComponentsCache.current.actions[message.name] ||\n        chatComponentsCache.current.actions[\"*\"];\n      // render a static string\n      if (typeof render === \"string\") {\n        // when render is static, we show it only when in progress\n        if (isCurrentMessage && inProgress) {\n          return (\n            <AssistantMessage\n              rawData={message}\n              key={index}\n              data-message-role=\"assistant\"\n              isLoading={false}\n              isGenerating={true}\n              message={render}\n            />\n          );\n        }\n        // Done - silent by default to avoid a series of \"done\" messages\n        else {\n          return null;\n        }\n      }\n      // render is a function\n      else {\n        const args = message.arguments;\n\n        let status: RenderFunctionStatus = \"inProgress\";\n\n        if (actionResult !== undefined) {\n          status = \"complete\";\n        } else if (message.status.code !== MessageStatusCode.Pending) {\n          status = \"executing\";\n        }\n\n        try {\n          const toRender = render({\n            status: status as any,\n            args,\n            result: actionResult,\n            name: message.name,\n          });\n          // No result and complete: stay silent\n          if (!toRender && status === \"complete\") {\n            return null;\n          }\n          if (typeof toRender === \"string\") {\n            return (\n              <AssistantMessage\n                rawData={message}\n                data-message-role=\"assistant\"\n                key={index}\n                isLoading={false}\n                isGenerating={false}\n                message={toRender}\n              />\n            );\n          } else {\n            return (\n              <AssistantMessage\n                rawData={message}\n                data-message-role=\"action-render\"\n                key={index}\n                isLoading={false}\n                isGenerating={false}\n                subComponent={toRender}\n              />\n            );\n          }\n        } catch (e) {\n          console.error(`Error executing render function for action ${message.name}: ${e}`);\n          return (\n            <AssistantMessage\n              rawData={message}\n              data-message-role=\"assistant\"\n              key={index}\n              isLoading={false}\n              isGenerating={false}\n              subComponent={\n                <div className=\"copilotKitMessage copilotKitAssistantMessage\">\n                  <b>❌ Error executing render function for action {message.name}:</b>\n                  <pre>{e instanceof Error ? e.message : String(e)}</pre>\n                </div>\n              }\n            />\n          );\n        }\n      }\n    }\n    // No render function found- show the default message\n    else if (!inProgress || !isCurrentMessage) {\n      // Done - silent by default to avoid a series of \"done\" messages\n      return null;\n    } else {\n      // In progress\n      return (\n        <AssistantMessage\n          rawData={message}\n          key={index}\n          data-message-role=\"assistant\"\n          isLoading={true}\n          isGenerating={true}\n        />\n      );\n    }\n  }\n}\n", "import { RenderMessageProps } from \"../props\";\nimport { AssistantMessage as DefaultAssistantMessage } from \"./AssistantMessage\";\n\nexport function RenderResultMessage({\n  AssistantMessage = DefaultAssistantMessage,\n  ...props\n}: RenderMessageProps) {\n  const { message, inProgress, index, isCurrentMessage } = props;\n\n  if (message.isResultMessage() && inProgress && isCurrentMessage) {\n    return (\n      <AssistantMessage\n        key={index}\n        data-message-role=\"assistant\"\n        rawData={message}\n        isLoading={true}\n        isGenerating={true}\n      />\n    );\n  }\n\n  // Avoid 'Nothing was returned from render' React error\n  else {\n    return null;\n  }\n}\n", "import { RenderMessageProps } from \"../props\";\nimport { CoagentInChatRenderFunction, useCopilotContext } from \"@copilotkit/react-core\";\nimport { AssistantMessage as DefaultAssistantMessage } from \"./AssistantMessage\";\n\nexport function RenderAgentStateMessage({\n  AssistantMessage = DefaultAssistantMessage,\n  ...props\n}: RenderMessageProps) {\n  const { chatComponentsCache } = useCopilotContext();\n  const { message, inProgress, index, isCurrentMessage } = props;\n\n  if (message.isAgentStateMessage()) {\n    let render: string | CoagentInChatRenderFunction | undefined;\n\n    if (chatComponentsCache.current !== null) {\n      render =\n        chatComponentsCache.current.coAgentStateRenders[\n          `${message.agentName}-${message.nodeName}`\n        ] || chatComponentsCache.current.coAgentStateRenders[`${message.agentName}-global`];\n    }\n\n    if (render) {\n      // render a static string\n      if (typeof render === \"string\") {\n        // when render is static, we show it only when in progress\n        if (isCurrentMessage && inProgress) {\n          return (\n            <AssistantMessage\n              rawData={message}\n              message={render}\n              data-message-role=\"assistant\"\n              key={index}\n              isLoading={true}\n              isGenerating={true}\n            />\n          );\n        }\n        // Done - silent by default to avoid a series of \"done\" messages\n        else {\n          return null;\n        }\n      }\n      // render is a function\n      else {\n        const state = message.state;\n\n        let status = message.active ? \"inProgress\" : \"complete\";\n\n        const toRender = render({\n          status: status as any,\n          state,\n          nodeName: message.nodeName,\n        });\n\n        // No result and complete: stay silent\n        if (!toRender && status === \"complete\") {\n          return null;\n        }\n\n        if (!toRender && isCurrentMessage && inProgress) {\n          return (\n            <AssistantMessage\n              data-message-role=\"assistant\"\n              key={index}\n              rawData={message}\n              isLoading={true}\n              isGenerating={true}\n            />\n          );\n        } else if (!toRender) {\n          return null;\n        }\n\n        if (typeof toRender === \"string\") {\n          return (\n            <AssistantMessage\n              rawData={message}\n              message={toRender}\n              isLoading={true}\n              isGenerating={true}\n              data-message-role=\"assistant\"\n              key={index}\n            />\n          );\n        } else {\n          return (\n            <AssistantMessage\n              rawData={message}\n              data-message-role=\"agent-state-render\"\n              key={index}\n              isLoading={false}\n              isGenerating={false}\n              subComponent={toRender}\n            />\n          );\n        }\n      }\n    }\n    // No render function found- show the default message\n    else if (!inProgress || !isCurrentMessage) {\n      // Done - silent by default to avoid a series of \"done\" messages\n      return null;\n    } else {\n      // In progress\n      return (\n        <AssistantMessage\n          rawData={message}\n          isLoading={true}\n          isGenerating={true}\n          data-message-role=\"assistant\"\n          key={index}\n        />\n      );\n    }\n  }\n}\n", "import { RenderMessageProps } from \"../props\";\nimport { UserMessage as DefaultUserMessage } from \"./UserMessage\";\nimport { AssistantMessage as DefaultAssistantMessage } from \"./AssistantMessage\";\n\nexport function RenderImageMessage({\n  UserMessage = DefaultUserMessage,\n  AssistantMessage = DefaultAssistantMessage,\n  ...props\n}: RenderMessageProps) {\n  const {\n    message,\n    inProgress,\n    index,\n    isCurrentMessage,\n    onRegenerate,\n    onCopy,\n    onThumbsUp,\n    onThumbsDown,\n  } = props;\n\n  if (message.isImageMessage()) {\n    const imageData = `data:${message.format};base64,${message.bytes}`;\n    const imageComponent = (\n      <div className=\"copilotKitImage\">\n        <img\n          src={imageData}\n          alt=\"User uploaded image\"\n          style={{ maxWidth: \"100%\", maxHeight: \"300px\", borderRadius: \"8px\" }}\n        />\n      </div>\n    );\n\n    if (message.role === \"user\") {\n      return (\n        <UserMessage\n          key={index}\n          data-message-role=\"user\"\n          message=\"\"\n          rawData={message}\n          subComponent={imageComponent}\n        />\n      );\n    } else if (message.role === \"assistant\") {\n      return (\n        <AssistantMessage\n          key={index}\n          data-message-role=\"assistant\"\n          message=\"\"\n          rawData={message}\n          subComponent={imageComponent}\n          isLoading={inProgress && isCurrentMessage && !message.bytes}\n          isGenerating={inProgress && isCurrentMessage && !!message.bytes}\n          isCurrentMessage={isCurrentMessage}\n          onRegenerate={() => onRegenerate?.(message.id)}\n          onCopy={onCopy}\n          onThumbsUp={onThumbsUp}\n          onThumbsDown={onThumbsDown}\n        />\n      );\n    }\n  }\n\n  return null;\n}\n", "/**\n * <br/>\n * <img src=\"/images/CopilotChat.gif\" width=\"500\" />\n *\n * A chatbot panel component for the CopilotKit framework. The component allows for a high degree\n * of customization through various props and custom CSS.\n *\n * ## Install Dependencies\n *\n * This component is part of the [@copilotkit/react-ui](https://npmjs.com/package/@copilotkit/react-ui) package.\n *\n * ```shell npm2yarn \\\"@copilotkit/react-ui\"\\\n * npm install @copilotkit/react-core @copilotkit/react-ui\n * ```\n *\n * ## Usage\n *\n * ```tsx\n * import { CopilotChat } from \"@copilotkit/react-ui\";\n * import \"@copilotkit/react-ui/styles.css\";\n *\n * <CopilotChat\n *   labels={{\n *     title: \"Your Assistant\",\n *     initial: \"Hi! 👋 How can I assist you today?\",\n *   }}\n * />\n * ```\n *\n * ### Look & Feel\n *\n * By default, CopilotKit components do not have any styles. You can import CopilotKit's stylesheet at the root of your project:\n * ```tsx title=\"YourRootComponent.tsx\"\n * ...\n * import \"@copilotkit/react-ui/styles.css\"; // [!code highlight]\n *\n * export function YourRootComponent() {\n *   return (\n *     <CopilotKit>\n *       ...\n *     </CopilotKit>\n *   );\n * }\n * ```\n * For more information about how to customize the styles, check out the [Customize Look & Feel](/guides/custom-look-and-feel/customize-built-in-ui-components) guide.\n */\n\nimport {\n  ChatContext,\n  ChatContextProvider,\n  CopilotChatIcons,\n  CopilotChatLabels,\n} from \"./ChatContext\";\nimport { Messages as DefaultMessages } from \"./Messages\";\nimport { Input as DefaultInput } from \"./Input\";\nimport { RenderTextMessage as DefaultRenderTextMessage } from \"./messages/RenderTextMessage\";\nimport { RenderActionExecutionMessage as DefaultRenderActionExecutionMessage } from \"./messages/RenderActionExecutionMessage\";\nimport { RenderResultMessage as DefaultRenderResultMessage } from \"./messages/RenderResultMessage\";\nimport { RenderAgentStateMessage as DefaultRenderAgentStateMessage } from \"./messages/RenderAgentStateMessage\";\nimport { RenderImageMessage as DefaultRenderImageMessage } from \"./messages/RenderImageMessage\";\nimport { AssistantMessage as DefaultAssistantMessage } from \"./messages/AssistantMessage\";\nimport { UserMessage as DefaultUserMessage } from \"./messages/UserMessage\";\nimport React, { useEffect, useRef, useState } from \"react\";\nimport {\n  SystemMessageFunction,\n  useCopilotChat,\n  useCopilotContext,\n  useCopilotMessagesContext,\n} from \"@copilotkit/react-core\";\nimport { reloadSuggestions } from \"./Suggestion\";\nimport { CopilotChatSuggestion } from \"../../types/suggestions\";\nimport { Message, Role, TextMessage, ImageMessage } from \"@copilotkit/runtime-client-gql\";\nimport { randomId } from \"@copilotkit/shared\";\nimport {\n  AssistantMessageProps,\n  ComponentsMap,\n  InputProps,\n  MessagesProps,\n  RenderMessageProps,\n  RenderSuggestionsListProps,\n  UserMessageProps,\n} from \"./props\";\n\nimport { HintFunction, runAgent, stopAgent } from \"@copilotkit/react-core\";\nimport { ImageUploadQueue } from \"./ImageUploadQueue\";\nimport { Suggestions as DefaultRenderSuggestionsList } from \"./Suggestions\";\n\n/**\n * Props for CopilotChat component.\n */\nexport interface CopilotChatProps {\n  /**\n   * Custom instructions to be added to the system message. Use this property to\n   * provide additional context or guidance to the language model, influencing\n   * its responses. These instructions can include specific directions,\n   * preferences, or criteria that the model should consider when generating\n   * its output, thereby tailoring the conversation more precisely to the\n   * user's needs or the application's requirements.\n   */\n  instructions?: string;\n\n  /**\n   * A callback that gets called when the in progress state changes.\n   */\n  onInProgress?: (inProgress: boolean) => void;\n\n  /**\n   * A callback that gets called when a new message it submitted.\n   */\n  onSubmitMessage?: (message: string) => void | Promise<void>;\n\n  /**\n   * A custom stop generation function.\n   */\n  onStopGeneration?: OnStopGeneration;\n\n  /**\n   * A custom reload messages function.\n   */\n  onReloadMessages?: OnReloadMessages;\n\n  /**\n   * A callback function to regenerate the assistant's response\n   */\n  onRegenerate?: (messageId: string) => void;\n\n  /**\n   * A callback function when the message is copied\n   */\n  onCopy?: (message: string) => void;\n\n  /**\n   * A callback function for thumbs up feedback\n   */\n  onThumbsUp?: (message: string) => void;\n\n  /**\n   * A callback function for thumbs down feedback\n   */\n  onThumbsDown?: (message: string) => void;\n\n  /**\n   * A list of markdown components to render in assistant message.\n   * Useful when you want to render custom elements in the message (e.g a reference tag element)\n   */\n  markdownTagRenderers?: ComponentsMap;\n\n  /**\n   * Icons can be used to set custom icons for the chat window.\n   */\n  icons?: CopilotChatIcons;\n\n  /**\n   * Labels can be used to set custom labels for the chat window.\n   */\n  labels?: CopilotChatLabels;\n\n  /**\n   * Enable image upload button (image inputs only supported on some models)\n   */\n  imageUploadsEnabled?: boolean;\n\n  /**\n   * The 'accept' attribute for the file input used for image uploads.\n   * Defaults to \"image/*\".\n   */\n  inputFileAccept?: string;\n\n  /**\n   * A function that takes in context string and instructions and returns\n   * the system message to include in the chat request.\n   * Use this to completely override the system message, when providing\n   * instructions is not enough.\n   */\n  makeSystemMessage?: SystemMessageFunction;\n\n  /**\n   * A custom assistant message component to use instead of the default.\n   */\n  AssistantMessage?: React.ComponentType<AssistantMessageProps>;\n\n  /**\n   * A custom user message component to use instead of the default.\n   */\n  UserMessage?: React.ComponentType<UserMessageProps>;\n\n  /**\n   * A custom Messages component to use instead of the default.\n   */\n  Messages?: React.ComponentType<MessagesProps>;\n\n  /**\n   * A custom RenderTextMessage component to use instead of the default.\n   */\n  RenderTextMessage?: React.ComponentType<RenderMessageProps>;\n\n  /**\n   * A custom RenderActionExecutionMessage component to use instead of the default.\n   */\n  RenderActionExecutionMessage?: React.ComponentType<RenderMessageProps>;\n\n  /**\n   * A custom RenderAgentStateMessage component to use instead of the default.\n   */\n  RenderAgentStateMessage?: React.ComponentType<RenderMessageProps>;\n\n  /**\n   * A custom RenderResultMessage component to use instead of the default.\n   */\n  RenderResultMessage?: React.ComponentType<RenderMessageProps>;\n\n  /**\n   * A custom RenderImageMessage component to use instead of the default.\n   */\n  RenderImageMessage?: React.ComponentType<RenderMessageProps>;\n\n  /**\n   * A custom suggestions list component to use instead of the default.\n   */\n  RenderSuggestionsList?: React.ComponentType<RenderSuggestionsListProps>;\n\n  /**\n   * A custom Input component to use instead of the default.\n   */\n  Input?: React.ComponentType<InputProps>;\n\n  /**\n   * A class name to apply to the root element.\n   */\n  className?: string;\n\n  /**\n   * Children to render.\n   */\n  children?: React.ReactNode;\n}\n\ninterface OnStopGenerationArguments {\n  /**\n   * The name of the currently executing agent.\n   */\n  currentAgentName: string | undefined;\n\n  /**\n   * The messages in the chat.\n   */\n  messages: Message[];\n\n  /**\n   * Set the messages in the chat.\n   */\n  setMessages: (messages: Message[]) => void;\n\n  /**\n   * Stop chat generation.\n   */\n  stopGeneration: () => void;\n\n  /**\n   * Restart the currently executing agent.\n   */\n  restartCurrentAgent: () => void;\n\n  /**\n   * Stop the currently executing agent.\n   */\n  stopCurrentAgent: () => void;\n\n  /**\n   * Run the currently executing agent.\n   */\n  runCurrentAgent: (hint?: HintFunction) => Promise<void>;\n\n  /**\n   * Set the state of the currently executing agent.\n   */\n  setCurrentAgentState: (state: any) => void;\n}\n\nexport type OnReloadMessagesArguments = OnStopGenerationArguments & {\n  /**\n   * The message on which \"regenerate\" was pressed\n   */\n  messageId: string;\n};\n\nexport type OnStopGeneration = (args: OnStopGenerationArguments) => void;\n\nexport type OnReloadMessages = (args: OnReloadMessagesArguments) => void;\n\nexport type ImageUpload = {\n  contentType: string;\n  bytes: string;\n};\n\nexport function CopilotChat({\n  instructions,\n  onSubmitMessage,\n  makeSystemMessage,\n  onInProgress,\n  onStopGeneration,\n  onReloadMessages,\n  onRegenerate,\n  onCopy,\n  onThumbsUp,\n  onThumbsDown,\n  markdownTagRenderers,\n  Messages = DefaultMessages,\n  RenderTextMessage = DefaultRenderTextMessage,\n  RenderActionExecutionMessage = DefaultRenderActionExecutionMessage,\n  RenderAgentStateMessage = DefaultRenderAgentStateMessage,\n  RenderResultMessage = DefaultRenderResultMessage,\n  RenderImageMessage = DefaultRenderImageMessage,\n  RenderSuggestionsList = DefaultRenderSuggestionsList,\n  Input = DefaultInput,\n  className,\n  icons,\n  labels,\n  AssistantMessage = DefaultAssistantMessage,\n  UserMessage = DefaultUserMessage,\n  imageUploadsEnabled,\n  inputFileAccept = \"image/*\",\n}: CopilotChatProps) {\n  const { additionalInstructions, setChatInstructions } = useCopilotContext();\n  const [selectedImages, setSelectedImages] = useState<Array<ImageUpload>>([]);\n  const fileInputRef = useRef<HTMLInputElement>(null);\n\n  // Clipboard paste handler\n  useEffect(() => {\n    if (!imageUploadsEnabled) return;\n\n    const handlePaste = async (e: ClipboardEvent) => {\n      const target = e.target as HTMLElement;\n      if (!target.parentElement?.classList.contains(\"copilotKitInput\")) return;\n\n      const items = Array.from(e.clipboardData?.items || []);\n      const imageItems = items.filter((item) => item.type.startsWith(\"image/\"));\n\n      if (imageItems.length === 0) return;\n\n      e.preventDefault(); // Prevent default paste behavior for images\n\n      const imagePromises: Promise<ImageUpload | null>[] = imageItems.map((item) => {\n        const file = item.getAsFile();\n        if (!file) return Promise.resolve(null);\n\n        return new Promise<ImageUpload | null>((resolve, reject) => {\n          const reader = new FileReader();\n          reader.onload = (e) => {\n            const base64String = (e.target?.result as string)?.split(\",\")[1];\n            if (base64String) {\n              resolve({\n                contentType: file.type,\n                bytes: base64String,\n              });\n            } else {\n              resolve(null);\n            }\n          };\n          reader.onerror = reject;\n          reader.readAsDataURL(file);\n        });\n      });\n\n      try {\n        const loadedImages = (await Promise.all(imagePromises)).filter((img) => img !== null);\n        setSelectedImages((prev) => [...prev, ...loadedImages]);\n      } catch (error) {\n        // TODO: Show an error message to the user\n        console.error(\"Error processing pasted images:\", error);\n      }\n    };\n\n    document.addEventListener(\"paste\", handlePaste);\n    return () => document.removeEventListener(\"paste\", handlePaste);\n  }, [imageUploadsEnabled]);\n\n  useEffect(() => {\n    if (!additionalInstructions?.length) {\n      setChatInstructions(instructions || \"\");\n      return;\n    }\n\n    /*\n      Will result in a prompt like:\n\n      You are a helpful assistant. \n      Additionally, follow these instructions:\n      - Do not answer questions about the weather.\n      - Do not answer questions about the stock market.\"\n    */\n    const combinedAdditionalInstructions = [\n      instructions,\n      \"Additionally, follow these instructions:\",\n      ...additionalInstructions.map((instruction) => `- ${instruction}`),\n    ];\n\n    console.log(\"combinedAdditionalInstructions\", combinedAdditionalInstructions);\n\n    setChatInstructions(combinedAdditionalInstructions.join(\"\\n\") || \"\");\n  }, [instructions, additionalInstructions]);\n\n  const {\n    visibleMessages,\n    isLoading,\n    currentSuggestions,\n    sendMessage,\n    stopGeneration,\n    reloadMessages,\n  } = useCopilotChatLogic(\n    makeSystemMessage,\n    onInProgress,\n    onSubmitMessage,\n    onStopGeneration,\n    onReloadMessages,\n  );\n\n  // Wrapper for sendMessage to clear selected images\n  const handleSendMessage = (text: string) => {\n    const images = selectedImages;\n    setSelectedImages([]);\n    if (fileInputRef.current) {\n      fileInputRef.current.value = \"\";\n    }\n\n    return sendMessage(text, images);\n  };\n\n  const chatContext = React.useContext(ChatContext);\n  const isVisible = chatContext ? chatContext.open : true;\n\n  const handleRegenerate = (messageId: string) => {\n    if (onRegenerate) {\n      onRegenerate(messageId);\n    }\n\n    reloadMessages(messageId);\n  };\n\n  const handleCopy = (message: string) => {\n    if (onCopy) {\n      onCopy(message);\n    }\n  };\n\n  const handleImageUpload = async (event: React.ChangeEvent<HTMLInputElement>) => {\n    if (!event.target.files || event.target.files.length === 0) {\n      return;\n    }\n\n    const files = Array.from(event.target.files).filter((file) => file.type.startsWith(\"image/\"));\n    if (files.length === 0) return;\n\n    const fileReadPromises = files.map((file) => {\n      return new Promise<{ contentType: string; bytes: string }>((resolve, reject) => {\n        const reader = new FileReader();\n        reader.onload = (e) => {\n          const base64String = (e.target?.result as string)?.split(\",\")[1] || \"\";\n          if (base64String) {\n            resolve({\n              contentType: file.type,\n              bytes: base64String,\n            });\n          }\n        };\n        reader.onerror = reject;\n        reader.readAsDataURL(file);\n      });\n    });\n\n    try {\n      const loadedImages = await Promise.all(fileReadPromises);\n      setSelectedImages((prev) => [...prev, ...loadedImages]);\n    } catch (error) {\n      // TODO: Show an error message to the user\n      console.error(\"Error reading files:\", error);\n    }\n  };\n\n  const removeSelectedImage = (index: number) => {\n    setSelectedImages((prev) => prev.filter((_, i) => i !== index));\n  };\n\n  return (\n    <WrappedCopilotChat icons={icons} labels={labels} className={className}>\n      <Messages\n        AssistantMessage={AssistantMessage}\n        UserMessage={UserMessage}\n        RenderTextMessage={RenderTextMessage}\n        RenderActionExecutionMessage={RenderActionExecutionMessage}\n        RenderAgentStateMessage={RenderAgentStateMessage}\n        RenderResultMessage={RenderResultMessage}\n        RenderImageMessage={RenderImageMessage}\n        messages={visibleMessages}\n        inProgress={isLoading}\n        onRegenerate={handleRegenerate}\n        onCopy={handleCopy}\n        onThumbsUp={onThumbsUp}\n        onThumbsDown={onThumbsDown}\n        markdownTagRenderers={markdownTagRenderers}\n      >\n        {currentSuggestions.length > 0 && (\n          <RenderSuggestionsList\n            onSuggestionClick={handleSendMessage}\n            suggestions={currentSuggestions}\n          />\n        )}\n      </Messages>\n\n      {imageUploadsEnabled && (\n        <>\n          <ImageUploadQueue images={selectedImages} onRemoveImage={removeSelectedImage} />\n          <input\n            type=\"file\"\n            multiple\n            ref={fileInputRef}\n            onChange={handleImageUpload}\n            accept={inputFileAccept}\n            style={{ display: \"none\" }}\n          />\n        </>\n      )}\n\n      <Input\n        inProgress={isLoading}\n        onSend={handleSendMessage}\n        isVisible={isVisible}\n        onStop={stopGeneration}\n        onUpload={imageUploadsEnabled ? () => fileInputRef.current?.click() : undefined}\n      />\n    </WrappedCopilotChat>\n  );\n}\n\nexport function WrappedCopilotChat({\n  children,\n  icons,\n  labels,\n  className,\n}: {\n  children: React.ReactNode;\n  icons?: CopilotChatIcons;\n  labels?: CopilotChatLabels;\n  className?: string;\n}) {\n  const chatContext = React.useContext(ChatContext);\n  if (!chatContext) {\n    return (\n      <ChatContextProvider icons={icons} labels={labels} open={true} setOpen={() => {}}>\n        <div className={`copilotKitChat ${className ?? \"\"}`}>{children}</div>\n      </ChatContextProvider>\n    );\n  }\n  return <>{children}</>;\n}\n\nconst SUGGESTIONS_DEBOUNCE_TIMEOUT = 1000;\n\nexport const useCopilotChatLogic = (\n  makeSystemMessage?: SystemMessageFunction,\n  onInProgress?: (isLoading: boolean) => void,\n  onSubmitMessage?: (messageContent: string) => Promise<void> | void,\n  onStopGeneration?: OnStopGeneration,\n  onReloadMessages?: OnReloadMessages,\n) => {\n  const {\n    visibleMessages,\n    appendMessage,\n    reloadMessages: defaultReloadMessages,\n    stopGeneration: defaultStopGeneration,\n    runChatCompletion,\n    isLoading,\n  } = useCopilotChat({\n    id: randomId(),\n    makeSystemMessage,\n  });\n\n  const [currentSuggestions, setCurrentSuggestions] = useState<CopilotChatSuggestion[]>([]);\n  const suggestionsAbortControllerRef = useRef<AbortController | null>(null);\n  const debounceTimerRef = useRef<any>();\n\n  const abortSuggestions = () => {\n    suggestionsAbortControllerRef.current?.abort();\n    suggestionsAbortControllerRef.current = null;\n  };\n\n  const generalContext = useCopilotContext();\n  const messagesContext = useCopilotMessagesContext();\n  const context = { ...generalContext, ...messagesContext };\n\n  useEffect(() => {\n    onInProgress?.(isLoading);\n\n    abortSuggestions();\n\n    debounceTimerRef.current = setTimeout(\n      () => {\n        if (!isLoading && Object.keys(context.chatSuggestionConfiguration).length !== 0) {\n          suggestionsAbortControllerRef.current = new AbortController();\n          reloadSuggestions(\n            context,\n            context.chatSuggestionConfiguration,\n            setCurrentSuggestions,\n            suggestionsAbortControllerRef,\n          );\n        }\n      },\n      currentSuggestions.length == 0 ? 0 : SUGGESTIONS_DEBOUNCE_TIMEOUT,\n    );\n\n    return () => {\n      clearTimeout(debounceTimerRef.current);\n    };\n  }, [\n    isLoading,\n    context.chatSuggestionConfiguration,\n    // hackish way to trigger suggestions reload on reset, but better than moving suggestions to the\n    // global context\n    visibleMessages.length == 0,\n  ]);\n\n  const sendMessage = async (\n    messageContent: string,\n    imagesToUse?: Array<{ contentType: string; bytes: string }>,\n  ) => {\n    // Use images passed in the call OR the ones from the state (passed via props)\n    const images = imagesToUse || [];\n\n    abortSuggestions();\n    setCurrentSuggestions([]);\n\n    let firstMessage: Message | null = null;\n\n    // If there's text content, send a text message first\n    if (messageContent.trim().length > 0) {\n      const textMessage = new TextMessage({\n        content: messageContent,\n        role: Role.User,\n      });\n\n      if (onSubmitMessage) {\n        try {\n          // Call onSubmitMessage only with text, as image handling is internal right now\n          await onSubmitMessage(messageContent);\n        } catch (error) {\n          console.error(\"Error in onSubmitMessage:\", error);\n        }\n      }\n\n      await appendMessage(textMessage, { followUp: images.length === 0 });\n\n      if (!firstMessage) {\n        firstMessage = textMessage;\n      }\n    }\n\n    // Send image messages\n    if (images.length > 0) {\n      for (let i = 0; i < images.length; i++) {\n        const imageMessage = new ImageMessage({\n          format: images[i].contentType.replace(\"image/\", \"\"),\n          bytes: images[i].bytes,\n          role: Role.User,\n        });\n        await appendMessage(imageMessage, { followUp: i === images.length - 1 });\n        if (!firstMessage) {\n          firstMessage = imageMessage;\n        }\n      }\n    }\n\n    if (!firstMessage) {\n      // Should not happen if send button is properly disabled, but handle just in case\n      return new TextMessage({ content: \"\", role: Role.User }); // Return a dummy message\n    }\n\n    // The hook implicitly triggers API call on appendMessage.\n    // We return the first message sent (either text or first image)\n    return firstMessage;\n  };\n\n  const messages = visibleMessages;\n  const { setMessages } = messagesContext;\n  const currentAgentName = generalContext.agentSession?.agentName;\n  const restartCurrentAgent = async (hint?: HintFunction) => {\n    if (generalContext.agentSession) {\n      generalContext.setAgentSession({\n        ...generalContext.agentSession,\n        nodeName: undefined,\n        threadId: undefined,\n      });\n      generalContext.setCoagentStates((prevAgentStates) => {\n        return {\n          ...prevAgentStates,\n          [generalContext.agentSession!.agentName]: {\n            ...prevAgentStates[generalContext.agentSession!.agentName],\n            threadId: undefined,\n            nodeName: undefined,\n            runId: undefined,\n          },\n        };\n      });\n    }\n  };\n  const runCurrentAgent = async (hint?: HintFunction) => {\n    if (generalContext.agentSession) {\n      await runAgent(\n        generalContext.agentSession.agentName,\n        context,\n        appendMessage,\n        runChatCompletion,\n        hint,\n      );\n    }\n  };\n  const stopCurrentAgent = () => {\n    if (generalContext.agentSession) {\n      stopAgent(generalContext.agentSession.agentName, context);\n    }\n  };\n  const setCurrentAgentState = (state: any) => {\n    if (generalContext.agentSession) {\n      generalContext.setCoagentStates((prevAgentStates) => {\n        return {\n          ...prevAgentStates,\n          [generalContext.agentSession!.agentName]: {\n            state,\n          },\n        } as any;\n      });\n    }\n  };\n\n  function stopGeneration() {\n    if (onStopGeneration) {\n      onStopGeneration({\n        messages,\n        setMessages,\n        stopGeneration: defaultStopGeneration,\n        currentAgentName,\n        restartCurrentAgent,\n        stopCurrentAgent,\n        runCurrentAgent,\n        setCurrentAgentState,\n      });\n    } else {\n      defaultStopGeneration();\n    }\n  }\n  function reloadMessages(messageId: string) {\n    if (onReloadMessages) {\n      onReloadMessages({\n        messages,\n        setMessages,\n        stopGeneration: defaultStopGeneration,\n        currentAgentName,\n        restartCurrentAgent,\n        stopCurrentAgent,\n        runCurrentAgent,\n        setCurrentAgentState,\n        messageId,\n      });\n    } else {\n      defaultReloadMessages(messageId);\n    }\n  }\n\n  return {\n    visibleMessages,\n    isLoading,\n    currentSuggestions,\n    sendMessage,\n    stopGeneration,\n    reloadMessages,\n  };\n};\n", "import {\n  CopilotContextParams,\n  extract,\n  CopilotChatSuggestionConfiguration,\n  CopilotMessagesContextParams,\n} from \"@copilotkit/react-core\";\nimport { SmallSpinnerIcon } from \"./Icons\";\nimport { CopilotChatSuggestion } from \"../../types/suggestions\";\nimport { actionParametersToJsonSchema } from \"@copilotkit/shared\";\nimport { CopilotRequestType } from \"@copilotkit/runtime-client-gql\";\n\ninterface SuggestionsProps {\n  title: string;\n  message: string;\n  partial?: boolean;\n  className?: string;\n  onClick: () => void;\n}\n\nexport function Suggestion({ title, onClick, partial, className }: SuggestionsProps) {\n  return (\n    <button\n      disabled={partial}\n      onClick={(e) => {\n        e.preventDefault();\n        onClick();\n      }}\n      className={className || (partial ? \"suggestion loading\" : \"suggestion\")}\n      data-test-id=\"suggestion\"\n    >\n      {partial ? SmallSpinnerIcon : <span>{title}</span>}\n    </button>\n  );\n}\n\nexport const reloadSuggestions = async (\n  context: CopilotContextParams & CopilotMessagesContextParams,\n  chatSuggestionConfiguration: { [key: string]: CopilotChatSuggestionConfiguration },\n  setCurrentSuggestions: (suggestions: { title: string; message: string }[]) => void,\n  abortControllerRef: React.MutableRefObject<AbortController | null>,\n) => {\n  const abortController = abortControllerRef.current;\n\n  const tools = JSON.stringify(\n    Object.values(context.actions).map((action) => ({\n      name: action.name,\n      description: action.description,\n      jsonSchema: JSON.stringify(actionParametersToJsonSchema(action.parameters)),\n    })),\n  );\n\n  const allSuggestions: CopilotChatSuggestion[] = [];\n\n  for (const config of Object.values(chatSuggestionConfiguration)) {\n    try {\n      const numOfSuggestionsInstructions =\n        config.minSuggestions === 0\n          ? `Produce up to ${config.maxSuggestions} suggestions. ` +\n            `If there are no highly relevant suggestions you can think of, provide an empty array.`\n          : `Produce between ${config.minSuggestions} and ${config.maxSuggestions} suggestions.`;\n\n      const result = await extract({\n        context,\n        instructions:\n          \"Suggest what the user could say next. Provide clear, highly relevant suggestions. Do not literally suggest function calls. \",\n        data:\n          config.instructions +\n          \"\\n\\n\" +\n          numOfSuggestionsInstructions +\n          \"\\n\\n\" +\n          \"Available tools: \" +\n          tools +\n          \"\\n\\n\",\n        requestType: CopilotRequestType.Task,\n        parameters: [\n          {\n            name: \"suggestions\",\n            type: \"object[]\",\n            attributes: [\n              {\n                name: \"title\",\n                description:\n                  \"The title of the suggestion. This is shown as a button and should be short.\",\n                type: \"string\",\n              },\n              {\n                name: \"message\",\n                description:\n                  \"The message to send when the suggestion is clicked. This should be a clear, complete sentence and will be sent as an instruction to the AI.\",\n                type: \"string\",\n              },\n            ],\n          },\n        ],\n        include: {\n          messages: true,\n          readable: true,\n        },\n        abortSignal: abortController?.signal,\n        stream: ({ status, args }) => {\n          const suggestions = args.suggestions || [];\n          const newSuggestions: CopilotChatSuggestion[] = [];\n          for (let i = 0; i < suggestions.length; i++) {\n            // if GPT provides too many suggestions, limit the number of suggestions\n            if (config.maxSuggestions !== undefined && i >= config.maxSuggestions) {\n              break;\n            }\n            const { title, message } = suggestions[i];\n\n            // If this is the last suggestion and the status is not complete, mark it as partial\n            const partial = i == suggestions.length - 1 && status !== \"complete\";\n\n            newSuggestions.push({\n              title,\n              message,\n              partial,\n              className: config.className,\n            });\n          }\n          setCurrentSuggestions([...allSuggestions, ...newSuggestions]);\n        },\n      });\n      allSuggestions.push(...result.suggestions);\n    } catch (error) {\n      console.error(\"Error loading suggestions\", error);\n    }\n  }\n\n  if (abortControllerRef.current === abortController) {\n    abortControllerRef.current = null;\n  }\n};\n", "import React from \"react\";\n\ninterface ImageUploadQueueProps {\n  images: Array<{ contentType: string; bytes: string }>;\n  onRemoveImage: (index: number) => void;\n  className?: string;\n}\n\nexport const ImageUploadQueue: React.FC<ImageUploadQueueProps> = ({\n  images,\n  onRemoveImage,\n  className = \"\",\n}) => {\n  if (images.length === 0) return null;\n\n  return (\n    <div\n      className={`copilotKitImageUploadQueue ${className}`}\n      style={{\n        display: \"flex\",\n        flexWrap: \"wrap\",\n        gap: \"8px\",\n        margin: \"8px\",\n        padding: \"8px\",\n      }}\n    >\n      {images.map((image, index) => (\n        <div\n          key={index}\n          className=\"copilotKitImageUploadQueueItem\"\n          style={{\n            position: \"relative\",\n            display: \"inline-block\",\n            width: \"60px\",\n            height: \"60px\",\n            borderRadius: \"4px\",\n            overflow: \"hidden\",\n          }}\n        >\n          {/* eslint-disable-next-line @next/next/no-img-element */}\n          <img\n            src={`data:${image.contentType};base64,${image.bytes}`}\n            alt={`Selected image ${index + 1}`}\n            style={{\n              width: \"100%\",\n              height: \"100%\",\n              objectFit: \"cover\",\n            }}\n          />\n          <button\n            onClick={() => onRemoveImage(index)}\n            className=\"copilotKitImageUploadQueueRemoveButton\"\n            style={{\n              position: \"absolute\",\n              top: \"2px\",\n              right: \"2px\",\n              background: \"rgba(0,0,0,0.6)\",\n              color: \"white\",\n              border: \"none\",\n              borderRadius: \"50%\",\n              width: \"18px\",\n              height: \"18px\",\n              display: \"flex\",\n              alignItems: \"center\",\n              justifyContent: \"center\",\n              cursor: \"pointer\",\n              fontSize: \"10px\",\n              padding: 0,\n            }}\n          >\n            ✕\n          </button>\n        </div>\n      ))}\n    </div>\n  );\n};\n", "import { Suggestion } from \"./Suggestion\";\nimport React from \"react\";\nimport { RenderSuggestionsListProps } from \"./props\";\n\nexport function Suggestions({ suggestions, onSuggestionClick }: RenderSuggestionsListProps) {\n  return (\n    <div className=\"suggestions\">\n      {suggestions.map((suggestion, index) => (\n        <Suggestion\n          key={index}\n          title={suggestion.title}\n          message={suggestion.message}\n          partial={suggestion.partial}\n          className={suggestion.className}\n          onClick={() => onSuggestionClick(suggestion.message)}\n        />\n      ))}\n    </div>\n  );\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,IAAAA,iBAAkB;;;ACAlB,mBAAyC;;;ACWnC;AATC,IAAM,WACX;AAAA,EAAC;AAAA;AAAA,IACC,OAAM;AAAA,IACN,SAAQ;AAAA,IACR,MAAK;AAAA,IACL,OAAM;AAAA,IACN,QAAO;AAAA,IAEP,sDAAC,OAAE,WAAU,iCACX;AAAA,MAAC;AAAA;AAAA,QACC,UAAS;AAAA,QACT,GAAE;AAAA,QACF,UAAS;AAAA;AAAA,IACX,GACF;AAAA;AACF;AAGK,IAAM,YACX;AAAA,EAAC;AAAA;AAAA,IACC,OAAM;AAAA,IACN,MAAK;AAAA,IACL,SAAQ;AAAA,IACR,aAAY;AAAA,IACZ,QAAO;AAAA,IACP,OAAM;AAAA,IACN,QAAO;AAAA,IAEP,sDAAC,UAAK,eAAc,SAAQ,gBAAe,SAAQ,GAAE,+BAA8B;AAAA;AACrF;AAGK,IAAM,kBACX;AAAA,EAAC;AAAA;AAAA,IACC,OAAM;AAAA,IACN,MAAK;AAAA,IACL,SAAQ;AAAA,IACR,aAAY;AAAA,IACZ,QAAO;AAAA,IACP,OAAM;AAAA,IACN,QAAO;AAAA,IAEP,sDAAC,UAAK,eAAc,SAAQ,gBAAe,SAAQ,GAAE,wBAAuB;AAAA;AAC9E;AAGK,IAAM,WACX;AAAA,EAAC;AAAA;AAAA,IACC,OAAM;AAAA,IACN,MAAK;AAAA,IACL,SAAQ;AAAA,IACR,aAAY;AAAA,IACZ,QAAO;AAAA,IACP,OAAM;AAAA,IACN,QAAO;AAAA,IAEP,sDAAC,UAAK,eAAc,SAAQ,gBAAe,SAAQ,GAAE,6BAA4B;AAAA;AACnF;AAGK,IAAM,iBACX;AAAA,EAAC;AAAA;AAAA,IACC,OAAM;AAAA,IACN,MAAK;AAAA,IACL,SAAQ;AAAA,IACR,aAAY;AAAA,IACZ,QAAO;AAAA,IACP,OAAM;AAAA,IACN,QAAO;AAAA,IAEP;AAAA,MAAC;AAAA;AAAA,QACC,eAAc;AAAA,QACd,gBAAe;AAAA,QACf,GAAE;AAAA;AAAA,IACJ;AAAA;AACF;AAGK,IAAM,WACX;AAAA,EAAC;AAAA;AAAA,IACC,OAAM;AAAA,IACN,MAAK;AAAA,IACL,SAAQ;AAAA,IACR,aAAY;AAAA,IACZ,QAAO;AAAA,IACP,OAAM;AAAA,IACN,QAAO;AAAA,IAEP;AAAA,MAAC;AAAA;AAAA,QACC,eAAc;AAAA,QACd,gBAAe;AAAA,QACf,GAAE;AAAA;AAAA,IACJ;AAAA;AACF;AAGK,IAAM,iBACX;AAAA,EAAC;AAAA;AAAA,IACC,OAAM;AAAA,IACN,MAAK;AAAA,IACL,SAAQ;AAAA,IACR,aAAY;AAAA,IACZ,QAAO;AAAA,IACP,OAAM;AAAA,IACN,QAAO;AAAA,IACP,OAAO,EAAE,UAAU,QAAQ,WAAW,OAAO;AAAA,IAE7C;AAAA,MAAC;AAAA;AAAA,QACC,eAAc;AAAA,QACd,gBAAe;AAAA,QACf,GAAE;AAAA;AAAA,IACJ;AAAA;AACF;AAGK,IAAM,WACX;AAAA,EAAC;AAAA;AAAA,IACC,OAAM;AAAA,IACN,MAAK;AAAA,IACL,SAAQ;AAAA,IACR,aAAY;AAAA,IACZ,QAAO;AAAA,IACP,OAAM;AAAA,IACN,QAAO;AAAA,IACP,OAAO,EAAE,UAAU,QAAQ,WAAW,OAAO;AAAA,IAE7C;AAAA,MAAC;AAAA;AAAA,QACC,eAAc;AAAA,QACd,gBAAe;AAAA,QACf,GAAE;AAAA;AAAA,IACJ;AAAA;AACF;AAGK,IAAM,mBACX,4CAAC,UAAK,WAAU,qBAAoB,OAAO,EAAE,OAAO,QAAQ,QAAQ,OAAO,GAAG;AAGzE,IAAM,cACX,4CAAC,UAAK,WAAU,qBAAoB,OAAO,EAAE,OAAO,QAAQ,QAAQ,OAAO,GAAG;AAGzE,IAAM,eACX,6CAAC,SAAI,OAAO,EAAE,SAAS,QAAQ,YAAY,UAAU,KAAK,MAAM,GAC9D;AAAA,8CAAC,UAAK,WAAU,yBAAwB,OAAO,EAAE,gBAAgB,KAAK,GAAG;AAAA,EACzE,4CAAC,UAAK,WAAU,yBAAwB,OAAO,EAAE,gBAAgB,OAAO,GAAG;AAAA,EAC3E,4CAAC,UAAK,WAAU,yBAAwB,OAAO,EAAE,gBAAgB,OAAO,GAAG;AAAA,GAC7E;AAGK,IAAM,eACX;AAAA,EAAC;AAAA;AAAA,IACC,OAAM;AAAA,IACN,MAAK;AAAA,IACL,SAAQ;AAAA,IACR,aAAY;AAAA,IACZ,QAAO;AAAA,IACP,OAAM;AAAA,IACN,QAAO;AAAA,IACP,OAAO,EAAE,UAAU,QAAQ,WAAW,OAAO;AAAA,IAE7C;AAAA,MAAC;AAAA;AAAA,QACC,eAAc;AAAA,QACd,gBAAe;AAAA,QACf,GAAE;AAAA;AAAA,IACJ;AAAA;AACF;AAGK,IAAM,iBACX;AAAA,EAAC;AAAA;AAAA,IACC,OAAM;AAAA,IACN,MAAK;AAAA,IACL,SAAQ;AAAA,IACR,aAAY;AAAA,IACZ,QAAO;AAAA,IACP,OAAM;AAAA,IACN,QAAO;AAAA,IACP,OAAO,EAAE,UAAU,QAAQ,WAAW,OAAO;AAAA,IAE7C;AAAA,MAAC;AAAA;AAAA,QACC,eAAc;AAAA,QACd,gBAAe;AAAA,QACf,GAAE;AAAA;AAAA,IACJ;AAAA;AACF;AAGK,IAAM,eACX;AAAA,EAAC;AAAA;AAAA,IACC,OAAM;AAAA,IACN,MAAK;AAAA,IACL,SAAQ;AAAA,IACR,aAAY;AAAA,IACZ,QAAO;AAAA,IACP,OAAM;AAAA,IACN,QAAO;AAAA,IACP,OAAO,EAAE,UAAU,QAAQ,WAAW,OAAO;AAAA,IAE7C;AAAA,MAAC;AAAA;AAAA,QACC,eAAc;AAAA,QACd,gBAAe;AAAA,QACf,GAAE;AAAA;AAAA,IACJ;AAAA;AACF;AAGK,IAAM,aACX;AAAA,EAAC;AAAA;AAAA,IACC,OAAM;AAAA,IACN,MAAK;AAAA,IACL,SAAQ;AAAA,IACR,aAAY;AAAA,IACZ,QAAO;AAAA,IACP,OAAM;AAAA,IACN,QAAO;AAAA,IAEP,sDAAC,UAAK,eAAc,SAAQ,gBAAe,SAAQ,GAAE,0BAAyB;AAAA;AAChF;AAGK,IAAM,YACX;AAAA,EAAC;AAAA;AAAA,IACC,OAAM;AAAA,IACN,MAAK;AAAA,IACL,SAAQ;AAAA,IACR,aAAY;AAAA,IACZ,QAAO;AAAA,IACP,OAAM;AAAA,IACN,QAAO;AAAA,IACP,OAAO,EAAE,UAAU,QAAQ,WAAW,OAAO;AAAA,IAE7C,sDAAC,UAAK,eAAc,SAAQ,gBAAe,SAAQ,GAAE,yBAAwB;AAAA;AAC/E;;;ADWO,IAAAC,sBAAA;AApFF,IAAM,cAAc,aAAAC,QAAM,cAAuC,MAAS;AAE1E,SAAS,iBAA8B;AAC5C,QAAM,UAAU,aAAAA,QAAM,WAAW,WAAW;AAC5C,MAAI,YAAY,QAAW;AACzB,UAAM,IAAI;AAAA,MACR;AAAA,IACF;AAAA,EACF;AACA,SAAO;AACT;AAaO,IAAM,sBAAsB,CAAC;AAAA;AAAA;AAAA;AAAA,EAIlC;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF,MAAwB;AACtB,QAAM,qBAAiB;AAAA,IACrB,MAAO,kCACF;AAAA,MACD,SAAS;AAAA,MACT,OAAO;AAAA,MACP,aAAa;AAAA,MACb,OAAO;AAAA,MACP,gBAAgB;AAAA,MAChB,oBAAoB;AAAA,MACpB,iBAAiB;AAAA,MACjB,UAAU;AAAA,MACV,YAAY;AAAA,MACZ,QAAQ;AAAA,IACV,IACG;AAAA,IAEL,CAAC,MAAM;AAAA,EACT;AAEA,QAAM,oBAAgB;AAAA,IACpB,MAAO,kCACF;AAAA,MACD,UAAuB;AAAA,MACvB,WAAwB;AAAA,MACxB,iBAA8B;AAAA,MAC9B,UAAuB;AAAA,MACvB,cAA2B;AAAA,MAC3B,aAA0B;AAAA,MAC1B,UAAuB;AAAA,MACvB,gBAA6B;AAAA,MAC7B,gBAA6B;AAAA,MAC7B,UAAuB;AAAA,MACvB,cAA2B;AAAA,MAC3B,gBAA6B;AAAA,MAC7B,YAAyB;AAAA,IAC3B,IACG;AAAA,IAEL,CAAC,KAAK;AAAA,EACR;AAEA,QAAM,cAAU;AAAA,IACd,OAAO;AAAA,MACL,QAAQ;AAAA,MACR,OAAO;AAAA,MACP;AAAA,MACA;AAAA,IACF;AAAA,IACA,CAAC,gBAAgB,eAAe,MAAM,OAAO;AAAA,EAC/C;AAEA,SAAO,6CAAC,YAAY,UAAZ,EAAqB,OAAO,SAAU,UAAS;AACzD;;;AEvPA,IAAAC,gBAA8C;AAG9C,wBAAkC;AA2H9B,IAAAC,sBAAA;AAzHG,IAAM,SAAS,CAAC;AAAA,EACrB;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF,MAAmB;AACjB,QAAM,YAAY,cAAAC,QAAM,OAAuB,IAAI;AACnD,QAAM,cAAU,qCAAkB;AAElC,QAAM,EAAE,MAAM,QAAQ,IAAI,eAAe;AAEzC,QAAM,yBAAqB;AAAA,IACzB,CAAC,UAAsB;AAjB3B;AAkBM,UAAI,CAAC,qBAAqB;AACxB;AAAA,MACF;AAEA,YAAM,iBAAgB,eAAU,YAAV,mBAAmB;AAEzC,UAAI,YAAY;AAChB,UAAI,MAAM,kBAAkB,aAAa;AACvC,oBAAY,MAAM,OAAO;AAAA,MAC3B;AAEA,UACE,QACA,iBACA,CAAC,cAAc,SAAS,MAAM,MAAa;AAAA,MAE3C,CAAC,UAAU,SAAS,qBAAqB,GACzC;AACA,gBAAQ,KAAK;AAAA,MACf;AAAA,IACF;AAAA,IACA,CAAC,qBAAqB,MAAM,OAAO;AAAA,EACrC;AAEA,QAAM,oBAAgB;AAAA,IACpB,CAAC,UAAyB;AA3C9B;AA4CM,YAAM,SAAS,MAAM;AACrB,YAAM,UACJ,OAAO,YAAY,WACnB,OAAO,YAAY,YACnB,OAAO,YAAY,cACnB,OAAO;AAET,YAAM,yBAAwB,eAAU,YAAV,mBAAmB,SAAS;AAE1D,UACE,QACA,MAAM,QAAQ,aACb,CAAC,WAAW,0BACb,kBACA;AACA,gBAAQ,KAAK;AAAA,MACf,WACE,MAAM,QAAQ,aACZ,QAAQ,KAAK,MAAM,WAAa,CAAC,QAAQ,KAAK,MAAM,aACrD,CAAC,WAAW,wBACb;AACA,gBAAQ,CAAC,IAAI;AAAA,MACf;AAAA,IACF;AAAA,IACA,CAAC,kBAAkB,UAAU,MAAM,OAAO;AAAA,EAC5C;AAEA,QAAM,sBAAkB,2BAAY,MAAM;AACxC,UAAM,mBAAmB,UAAU;AACnC,UAAM,KAAK,OAAO;AAClB,QAAI,CAAC,oBAAoB,CAAC,IAAI;AAC5B;AAAA,IACF;AAEA,QAAI,OAAO,aAAa,OAAO,MAAM;AACnC,uBAAiB,MAAM,SAAS,GAAG,GAAG;AACtC,uBAAiB,MAAM,OAAO,GAAG,GAAG;AACpC,uBAAiB,MAAM,MAAM,GAAG,GAAG;AAEnC,eAAS,KAAK,MAAM,WAAW;AAC/B,eAAS,KAAK,MAAM,QAAQ;AAC5B,eAAS,KAAK,MAAM,SAAS,GAAG,OAAO;AACvC,eAAS,KAAK,MAAM,WAAW;AAC/B,eAAS,KAAK,MAAM,cAAc;AAGlC,eAAS,KAAK,iBAAiB,aAAa,eAAe;AAAA,QACzD,SAAS;AAAA,MACX,CAAC;AAAA,IACH,OAAO;AACL,uBAAiB,MAAM,SAAS;AAChC,uBAAiB,MAAM,OAAO;AAC9B,uBAAiB,MAAM,MAAM;AAC7B,eAAS,KAAK,MAAM,WAAW;AAC/B,eAAS,KAAK,MAAM,SAAS;AAC7B,eAAS,KAAK,MAAM,QAAQ;AAC5B,eAAS,KAAK,MAAM,WAAW;AAC/B,eAAS,KAAK,MAAM,MAAM;AAC1B,eAAS,KAAK,MAAM,cAAc;AAElC,eAAS,KAAK,oBAAoB,aAAa,aAAa;AAAA,IAC9D;AAAA,EACF,GAAG,CAAC,IAAI,CAAC;AAET,+BAAU,MAAM;AACd,aAAS,iBAAiB,aAAa,kBAAkB;AACzD,aAAS,iBAAiB,WAAW,aAAa;AAClD,QAAI,OAAO,gBAAgB;AACzB,aAAO,eAAe,iBAAiB,UAAU,eAAe;AAChE,sBAAgB;AAAA,IAClB;AAEA,WAAO,MAAM;AACX,eAAS,oBAAoB,aAAa,kBAAkB;AAC5D,eAAS,oBAAoB,WAAW,aAAa;AACrD,UAAI,OAAO,gBAAgB;AACzB,eAAO,eAAe,oBAAoB,UAAU,eAAe;AAAA,MACrE;AAAA,IACF;AAAA,EACF,GAAG,CAAC,iBAAiB,oBAAoB,aAAa,CAAC;AAEvD,SACE,6CAAC,SAAI,WAAW,oBAAoB,OAAO,UAAU,MAAM,KAAK,WAC7D,UACH;AAEJ;AAEA,IAAM,gBAAgB,CAAC,UAA4B;AACjD,MAAI,gBAAgB,MAAM;AAG1B,QAAM,qBAAqB,CAAC,SAAkB,cAA+B;AAC3E,WAAO,WAAW,YAAY,SAAS,MAAM;AAC3C,UAAI,QAAQ,UAAU,SAAS,SAAS,GAAG;AACzC,eAAO;AAAA,MACT;AACA,gBAAU,QAAQ;AAAA,IACpB;AACA,WAAO;AAAA,EACT;AAGA,MAAI,CAAC,mBAAmB,eAAe,oBAAoB,GAAG;AAC5D,UAAM,eAAe;AAAA,EACvB;AACF;AAEA,SAAS,UAAU;AACjB,SAAO,sBAAsB,KAAK,UAAU,SAAS;AACvD;;;AClJM,IAAAC,sBAAA;AALC,IAAM,SAAS,CAAC,CAAC,MAAmB;AACzC,QAAM,EAAE,MAAM,SAAS,MAAM,IAAI,eAAe;AAEhD,SACE,6CAAC,SAAI,SAAS,MAAM,QAAQ,CAAC,IAAI,GAC/B;AAAA,IAAC;AAAA;AAAA,MACC,WAAW,oBAAoB,OAAO,SAAS;AAAA,MAC/C,cAAY,OAAO,eAAe;AAAA,MAElC;AAAA,qDAAC,SAAI,WAAU,iDAAiD,gBAAM,UAAS;AAAA,QAC/E,6CAAC,SAAI,WAAU,kDAAkD,gBAAM,WAAU;AAAA;AAAA;AAAA,EACnF,GACF;AAEJ;;;ACjBA,IAAAC,qBAIO;AAKA,SAAS,qBAAqB,gBAA2C;AAC9E,MAAI,OAAO,mBAAmB,WAAW;AACvC,WAAO;AAAA,EACT;AACA,SACE,YAAY,MAAM,eAClB,YAAY,MAAM,eAClB,YAAY,MAAM,aAClB,YAAY,MAAM;AAEtB;AAEA,SAAS,cAAsB;AAC7B,MAAI,OAAO,WAAW,eAAe,OAAO,UAAU;AACpD,WAAO,OAAO,SAAS;AAAA,EACzB;AACA,SAAO;AACT;AAEA,SAAsB,8BACpB,SACA,aAAsB,OACM;AAAA;AAC5B,UAAM,oBAAoB;AAC1B,UAAM,oBAAoB,aAAa,QAAQ,iBAAiB;AAChE,QAAI,qBAAqB,CAAC,YAAY;AACpC,UAAI;AACF,cAAM,gBAAmC,KAAK,MAAM,iBAAiB;AACrE,cAAM,UAAU,KAAK,KAAK;AAC1B,cAAM,OAAM,oBAAI,KAAK,GAAE,QAAQ;AAE/B,YACE,cAAc,YAAY,WAC1B,MAAM,IAAI,KAAK,cAAc,WAAW,EAAE,QAAQ,IAAI,SACtD;AACA,iBAAO;AAAA,QACT;AAAA,MACF,SAAS,OAAP;AACA,gBAAQ,MAAM,uDAAuD,KAAK;AAAA,MAC5E;AAAA,IACF;AAEA,QAAI;AACF,YAAM,WAAW,MAAM,MAAM,qDAAqD;AAAA,QAChF,QAAQ;AAAA,QACR,SAAS;AAAA,UACP,gBAAgB;AAAA,QAClB;AAAA,QACA,MAAM,KAAK,UAAU;AAAA,UACnB,UAAU;AAAA,YACR;AAAA,cACE,aAAa;AAAA,cACb,gBAAgB;AAAA,YAClB;AAAA,UACF;AAAA,QACF,CAAC;AAAA,MACH,CAAC;AAED,YAAM,OAAO,MAAM,SAAS,KAAK;AAEjC,YAAM,UAA6B;AAAA,QACjC;AAAA,QACA,cAAa,oBAAI,KAAK,GAAE,QAAQ;AAAA,QAChC,QAAQ,KAAK,SAAS,CAAC,EAAE;AAAA,QACzB,UAAU,KAAK,SAAS,CAAC,EAAE;AAAA,QAC3B,UAAU,KAAK,SAAS,CAAC,EAAE,YAAY;AAAA,MACzC;AAEA,mBAAa,QAAQ,mBAAmB,KAAK,UAAU,OAAO,CAAC;AAC/D,aAAO;AAAA,IACT,SAAS,OAAP;AACA,cAAQ,MAAM,+BAA+B,KAAK;AAClD,YAAM;AAAA,IACR;AAAA,EACF;AAAA;AAEO,SAAS,aAAa,SAA+B;AAC1D,UAAQ,IAAI,wBAAwB,qCAAqC;AAEzE,QAAM,YAAY,QAAQ,iBAAiB,CAAC,GAAG,kDAA+B,EAAE,KAAK;AACrF,MAAI,UAAU,WAAW,GAAG;AAC1B,YAAQ,IAAI,oBAAoB;AAChC;AAAA,EACF;AACA,UAAQ,IAAI,SAAS;AACvB;AAEO,SAAS,WAAW,SAA+B;AACxD,UAAQ,IAAI,sBAAsB,qCAAqC;AAEvE,MAAI,OAAO,OAAO,QAAQ,OAAO,EAAE,WAAW,GAAG;AAC/C,YAAQ,IAAI,kBAAkB;AAC9B;AAAA,EACF;AACA,aAAW,UAAU,OAAO,OAAO,QAAQ,OAAO,GAAG;AACnD,YAAQ,MAAM,OAAO,IAAI;AACzB,YAAQ,IAAI,QAAQ,OAAO,IAAI;AAC/B,YAAQ,IAAI,eAAe,OAAO,WAAW;AAC7C,YAAQ,IAAI,cAAc,OAAO,UAAU;AAE3C,YAAQ,SAAS;AAAA,EACnB;AACF;AAEO,SAAS,YAAY,SAAuC;AACjE,UAAQ,IAAI,uBAAuB,qCAAqC;AAExE,MAAI,QAAQ,SAAS,WAAW,GAAG;AACjC,YAAQ,IAAI,mBAAmB;AAC/B;AAAA,EACF;AAEA,QAAM,YAAY,QAAQ,SAAS,IAAI,CAAC,YAAY;AAClD,QAAI,QAAQ,cAAc,GAAG;AAC3B,aAAO;AAAA,QACL,IAAI,QAAQ;AAAA,QACZ,MAAM;AAAA,QACN,MAAM,QAAQ;AAAA,QACd,MAAM;AAAA,QACN,OAAO;AAAA,QACP,SAAS,QAAQ;AAAA,MACnB;AAAA,IACF,WAAW,QAAQ,yBAAyB,GAAG;AAC7C,aAAO;AAAA,QACL,IAAI,QAAQ;AAAA,QACZ,MAAM;AAAA,QACN,MAAM;AAAA,QACN,MAAM,QAAQ;AAAA,QACd,OAAO,QAAQ;AAAA,QACf,SAAS,QAAQ;AAAA,MACnB;AAAA,IACF,WAAW,QAAQ,gBAAgB,GAAG;AACpC,aAAO;AAAA,QACL,IAAI,QAAQ;AAAA,QACZ,MAAM;AAAA,QACN,MAAM;AAAA,QACN,MAAM,QAAQ;AAAA,QACd,OAAO,QAAQ;AAAA,QACf,SAAS,QAAQ;AAAA,MACnB;AAAA,IACF,WAAW,QAAQ,oBAAoB,GAAG;AACxC,aAAO;AAAA,QACL,IAAI,QAAQ;AAAA,QACZ,MAAM,+BAA+B,QAAQ;AAAA,QAC7C,MAAM,QAAQ;AAAA,QACd,MAAM;AAAA,QACN,OAAO,QAAQ;AAAA,QACf,SAAS,QAAQ;AAAA,MACnB;AAAA,IACF;AAAA,EACF,CAAC;AACD,UAAQ,MAAM,SAAS;AACzB;;;AC/JA,IAAAC,qBAA6D;AAQ7D,IAAAC,gBAAmD;;;ACA3C,IAAAC,sBAAA;AAVD,IAAM,8BACX;AAAA,EAAC;AAAA;AAAA,IACC,OAAM;AAAA,IACN,QAAO;AAAA,IACP,SAAQ;AAAA,IACR,SAAQ;AAAA,IACR,OAAM;AAAA,IAEN,uDAAC,OAAE,IAAG,UAAS,QAAO,QAAO,aAAY,KAAI,MAAK,QAAO,UAAS,WAChE,uDAAC,OAAE,IAAG,wBAAuB,MAAK,WAChC;AAAA,MAAC;AAAA;AAAA,QACC,GAAE;AAAA,QACF,IAAG;AAAA;AAAA,IACJ,GACH,GACF;AAAA;AACF;AAGK,IAAM,sBACX;AAAA,EAAC;AAAA;AAAA,IACC,OAAM;AAAA,IACN,QAAO;AAAA,IACP,SAAQ;AAAA,IACR,SAAQ;AAAA,IACR,OAAM;AAAA,IAEN,uDAAC,OAAE,IAAG,UAAS,QAAO,QAAO,aAAY,KAAI,MAAK,QAAO,UAAS,WAChE,uDAAC,OAAE,IAAG,sBAAqB,MAAK,WAC9B;AAAA,MAAC;AAAA;AAAA,QACC,GAAE;AAAA,QACF,IAAG;AAAA;AAAA,IACJ,GACH,GACF;AAAA;AACF;AAGK,IAAM,kBACX;AAAA,EAAC;AAAA;AAAA,IACC,OAAM;AAAA,IACN,QAAO;AAAA,IACP,SAAQ;AAAA,IACR,SAAQ;AAAA,IACR,OAAM;AAAA,IACN,MAAK;AAAA,IAEL,uDAAC,OAAE,IAAG,UAAS,QAAO,QAAO,aAAY,KAAI,MAAK,QAAO,UAAS,WAChE,uDAAC,OAAE,IAAG,SAAQ,MAAK,gBAAe,UAAS,WACzC;AAAA,MAAC;AAAA;AAAA,QACC,GAAE;AAAA,QACF,IAAG;AAAA;AAAA,IACJ,GACH,GACF;AAAA;AACF;AAGK,IAAMC,aACX;AAAA,EAAC;AAAA;AAAA,IACC,OAAM;AAAA,IACN,QAAO;AAAA,IACP,SAAQ;AAAA,IACR,SAAQ;AAAA,IACR,OAAM;AAAA,IAEN,uDAAC,OAAE,IAAG,UAAS,QAAO,QAAO,aAAY,KAAI,MAAK,QAAO,UAAS,WAChE,uDAAC,OAAE,IAAG,WAAU,WAAU,sBAAqB,MAAK,WAAU,UAAS,WACrE,uDAAC,OAAE,IAAG,SAAQ,WAAU,qBACtB;AAAA,MAAC;AAAA;AAAA,QACC,GAAE;AAAA,QACF,IAAG;AAAA;AAAA,IACJ,GACH,GACF,GACF;AAAA;AACF;;;AD1DF,IAAAC,gBAAsD;AACtD,oBAAmC;;;AEnBnC,IAAAC,gBAA4D;;;ACexD,IAAAC,sBAAA;AAYG,IAAMC,aAAY,MACvB;AAAA,EAAC;AAAA;AAAA,IACC,OAAM;AAAA,IACN,MAAK;AAAA,IACL,SAAQ;AAAA,IACR,aAAY;AAAA,IACZ,QAAO;AAAA,IACP,OAAM;AAAA,IACN,QAAO;AAAA,IAEP,uDAAC,UAAK,eAAc,SAAQ,gBAAe,SAAQ,GAAE,wBAAuB;AAAA;AAC9E;;;ADPE,IAAAC,sBAAA;AA5BG,SAAS,sBAAsB;AACpC,QAAM,CAAC,eAAe,gBAAgB,QAAI,wBAAS,KAAK;AACxD,QAAM,gBAAY,sBAA0B,IAAI;AAChD,QAAM,iBAAa,sBAAuB,IAAI;AAG9C,+BAAU,MAAM;AACd,UAAM,qBAAqB,CAAC,UAAsB;AAChD,UACE,WAAW,WACX,CAAC,WAAW,QAAQ,SAAS,MAAM,MAAc,KACjD,UAAU,WACV,CAAC,UAAU,QAAQ,SAAS,MAAM,MAAc,GAChD;AACA,yBAAiB,KAAK;AAAA,MACxB;AAAA,IACF;AAEA,QAAI,eAAe;AACjB,eAAS,iBAAiB,aAAa,kBAAkB;AAAA,IAC3D;AAEA,WAAO,MAAM;AACX,eAAS,oBAAoB,aAAa,kBAAkB;AAAA,IAC9D;AAAA,EACF,GAAG,CAAC,aAAa,CAAC;AAElB,QAAM,aAAa,MACjB;AAAA,IAAC;AAAA;AAAA,MACC,KAAK;AAAA,MACL,SAAS,MAAM,iBAAiB,CAAC,aAAa;AAAA,MAC9C,WAAU;AAAA,MACV,cAAW;AAAA,MACZ;AAAA;AAAA,EAED;AAGF,SACE,8CAAC,SAAI,WAAU,YACb;AAAA,iDAAC,cAAW;AAAA,IACX,iBACC;AAAA,MAAC;AAAA;AAAA,QACC,KAAK;AAAA,QACL,WAAU;AAAA,QACV,OAAO;AAAA,UACL,KAAK;AAAA,UACL,OAAO;AAAA,UACP,OAAO;AAAA,QACT;AAAA,QAEA,wDAAC,SAAI,WAAU,yEACb;AAAA;AAAA,YAAC;AAAA;AAAA,cACC,WAAU;AAAA,cACV,OAAO,EAAE,KAAK,QAAQ,OAAO,OAAO;AAAA,cACpC,SAAS,MAAM,iBAAiB,KAAK;AAAA,cACrC,cAAW;AAAA,cAEX,uDAACC,YAAA,EAAU;AAAA;AAAA,UACb;AAAA,UACA,6CAAC,SAAI,WAAU,mCACb,uDAAC,QAAG,WAAU,sBAAqB,0BAAY,GACjD;AAAA,UACA,8CAAC,SAAI,WAAU,kBACb;AAAA,yDAAC,SAAI,WAAU,4BACb;AAAA,cAAC;AAAA;AAAA,gBACC,MAAK;AAAA,gBACL,QAAO;AAAA,gBACP,KAAI;AAAA,gBACL;AAAA;AAAA,YAED,GACF;AAAA,YACA,6CAAC,SAAI,WAAU,4BACb;AAAA,cAAC;AAAA;AAAA,gBACC,MAAK;AAAA,gBACL,QAAO;AAAA,gBACP,KAAI;AAAA,gBACL;AAAA;AAAA,YAED,GACF;AAAA,YACA,6CAAC,SAAI,WAAU,4BACb;AAAA,cAAC;AAAA;AAAA,gBACC,MAAK;AAAA,gBACL,QAAO;AAAA,gBACP,KAAI;AAAA,gBACL;AAAA;AAAA,YAED,GACF;AAAA,aACF;AAAA,WACF;AAAA;AAAA,IACF;AAAA,KAEJ;AAEJ;;;AFZI,IAAAC,sBAAA;AA/DG,SAAS,oBAAoB;AAClC,QAAM,iBAAiB;AACvB,QAAM,cAAU,sCAAkB;AAOlC,QAAM,CAAC,gBAAgB,iBAAiB,QAAI,wBAAS,KAAK;AAE1D,+BAAU,MAAM;AACd,sBAAkB,qBAAqB,QAAQ,cAAc,CAAC;AAAA,EAChE,GAAG,CAAC,QAAQ,cAAc,CAAC;AAE3B,QAAM,4BAAwB,sBAAO,KAAK;AAC1C,QAAM,CAAC,eAAe,gBAAgB,QAAI,wBAAwB,SAAS;AAC3E,QAAM,CAAC,eAAe,gBAAgB,QAAI,wBAAiB,EAAE;AAC7D,QAAM,iBAAa,sBAAuB,IAAI;AAC9C,QAAM,CAAC,iBAAiB,kBAAkB,QAAI,wBAA6B,MAAM;AAEjF,QAAM,kBAAkB,CAAC,QAAiB,UAAU;AAClD,qBAAiB,UAAU;AAE3B,kCAA8B,gBAAgB,KAAK,EAChD,KAAK,CAAC,MAAM;AACX,uBAAiB,EAAE,MAAM;AACzB,UAAI,YAAY;AAGhB,UAAI,EAAE,YAAY,EAAE,QAAQ;AAC1B,oBAAY;AAAA,MACd,WAAW,WAAW,KAAK,EAAE,OAAO,GAAG;AACrC,oBAAY;AAAA,MACd;AAEA,UAAI,WAAW;AACb,yBAAiB,QAAQ;AAAA,MAC3B,WAAW,EAAE,aAAa,OAAO;AAC/B,yBAAiB,UAAU;AAAA,MAC7B,OAAO;AACL,yBAAiB,kBAAkB;AAAA,MACrC;AAAA,IACF,CAAC,EACA,MAAM,CAAC,MAAM;AACZ,cAAQ,MAAM,CAAC;AACf,uBAAiB,SAAS;AAAA,IAC5B,CAAC;AAAA,EACL;AAEA,+BAAU,MAAM;AACd,QAAI,sBAAsB,YAAY,MAAM;AAC1C;AAAA,IACF;AACA,0BAAsB,UAAU;AAEhC,oBAAgB;AAAA,EAClB,GAAG,CAAC,CAAC;AAEL,MAAI,CAAC,gBAAgB;AACnB,WAAO;AAAA,EACT;AACA,SACE;AAAA,IAAC;AAAA;AAAA,MACC,KAAK;AAAA,MACL,WACE,2BACC,kBAAkB,qBAAqB,gCAAgC,OACvE,kBAAkB,aAAa,qCAAqC;AAAA,MAGvE;AAAA;AAAA,UAAC;AAAA;AAAA,YACC,gBAAgB,QAAQ;AAAA,YACxB;AAAA,YACA;AAAA,YACA;AAAA;AAAA,QACF;AAAA,QAEA,6CAAC,uBAAoB;AAAA,QAErB;AAAA,UAAC;AAAA;AAAA,YACC;AAAA,YACA;AAAA,YACA,MAAM;AAAA;AAAA,QACR;AAAA;AAAA;AAAA,EACF;AAEJ;AAEA,SAAS,YAAY;AAAA,EACnB;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF,GAKG;AACD,QAAM,CAAC,YAAY,aAAa,QAAI,wBAAiB,EAAE;AAEvD,MAAI,eAAe;AACnB,MAAI,cAAmB;AACvB,MAAI,sBAAsB;AAE1B,MAAI,kBAAkB,UAAU;AAC9B,mBAAe;AACf,kBAAcC;AAAA,EAChB,WAAW,kBAAkB,YAAY;AACvC,mBAAe;AACf,kBAAc;AAAA,EAChB,WAAW,kBAAkB,oBAAoB;AAC/C,mBAAe;AACf,kBAAc;AACd,0BAAsB,GAAG,yBAAoB;AAAA,EAC/C,WAAW,kBAAkB,YAAY;AACvC,mBAAe;AACf,kBAAc;AACd,0BAAsB,GAAG,yBAAoB;AAAA,EAC/C;AAEA,MAAI,aAAa;AACjB,MAAI,mBAAmB,QAAQ;AAC7B,iBAAa;AAAA,EACf,WAAW,mBAAmB,MAAM;AAClC,iBAAa;AAAA,EACf;AAEA,QAAM,iBAAiB;AAAA,IACrB;AAAA,IACA,0BAA0B;AAAA,IAC1B,wBAAwB;AAAA,IACxB,8BAA8B;AAAA,IAC9B,sCAAsC;AAAA,EACxC,EAAE,KAAK,GAAG;AAEV,QAAM,kBAAkB,MAAM;AAC5B,cAAU,UAAU,UAAU,eAAe,KAAK,CAAC,EAAE,KAAK,MAAM;AAC9D,oBAAc,8BAA8B;AAC5C,iBAAW,MAAM,cAAc,EAAE,GAAG,GAAI;AAAA,IAC1C,CAAC;AAAA,EACH;AAEA,MAAI,kBAAkB,sBAAsB,kBAAkB,YAAY;AACxE,WACE,8CAAC,SAAI,WAAU,yBACb;AAAA,oDAAC,OACE;AAAA;AAAA,QAAoB;AAAA,QAAE;AAAA,SACzB;AAAA,MACA,6CAAC,YAAO,SAAS,iBAAkB,wBAAc,gBAAe;AAAA,OAClE;AAAA,EAEJ;AAEA,SAAO;AACT;AAEe,SAAR,gBAAiC;AAAA,EACtC;AAAA,EACA;AAAA,EACA;AACF,GAIG;AACD,QAAM,cAAU,sCAAkB;AAClC,QAAM,sBAAkB,8CAA0B;AAElD,SACE,6EACE,wDAAC,sBACC;AAAA;AAAA,MAAC;AAAA;AAAA,QACC,WAAW,oCAAoC,SAAS,YAAY,YAAY;AAAA,QAE/E,kBAAQ,YAAY,UAAU,8EAAE;AAAA;AAAA,UAAO;AAAA,WAAgB;AAAA;AAAA,IAC1D;AAAA,IAEA;AAAA,MAAC;AAAA;AAAA,QACC,YAAU;AAAA,QACV,QAAO;AAAA,QACP,WAAU;AAAA,QACV,OAAO,EAAE,QAAQ,GAAG;AAAA,QAEpB;AAAA,uDAAC,0BACC,uDAAC,YAAO,WAAU,2BAA0B,SAAS,MAAM,aAAa,OAAO,GAAG,2BAElF,GACF;AAAA,UACA,6CAAC,0BACC,uDAAC,YAAO,WAAU,2BAA0B,SAAS,MAAM,WAAW,OAAO,GAAG,yBAEhF,GACF;AAAA,UACA,6CAAC,0BACC;AAAA,YAAC;AAAA;AAAA,cACC,WAAU;AAAA,cACV,SAAS,MAAM,YAAY,eAAe;AAAA,cAC3C;AAAA;AAAA,UAED,GACF;AAAA,UACA,6CAAC,0BACC,uDAAC,YAAO,WAAU,2BAA0B,SAAS,MAAM,gBAAgB,IAAI,GAAG,+BAElF,GACF;AAAA,UACA,6CAAC,QAAG;AAAA,UACJ,6CAAC,0BACC,uDAAC,YAAO,WAAU,2BAA0B,SAAS,MAAM,kBAAkB,KAAK,GAAG,8BAErF,GACF;AAAA;AAAA;AAAA,IACF;AAAA,KACF,GACF;AAEJ;;;AIzOM,IAAAC,sBAAA;AALC,IAAM,SAAS,CAAC,CAAC,MAAmB;AACzC,QAAM,EAAE,SAAS,OAAO,OAAO,IAAI,eAAe;AAElD,SACE,8CAAC,SAAI,WAAU,oBACb;AAAA,iDAAC,SAAK,iBAAO,OAAM;AAAA,IACnB,8CAAC,SAAI,WAAU,4BACb;AAAA,mDAAC,qBAAkB;AAAA,MACnB;AAAA,QAAC;AAAA;AAAA,UACC,SAAS,MAAM,QAAQ,KAAK;AAAA,UAC5B,cAAW;AAAA,UACX,WAAU;AAAA,UAET,gBAAM;AAAA;AAAA,MACT;AAAA,OACF;AAAA,KACF;AAEJ;;;ACvBA,IAAAC,gBAA2C;AAG3C,gCAA0D;AAC1D,IAAAC,qBAA4C;AAgDtC,IAAAC,uBAAA;AA9CC,IAAM,WAAW,CAAC;AAAA,EACvB;AAAA,EACA;AAAA,EACA;AAAA,EACA,mBAAAC;AAAA,EACA,8BAAAC;AAAA,EACA,yBAAAC;AAAA,EACA,qBAAAC;AAAA,EACA,oBAAAC;AAAA,EACA,kBAAAC;AAAA,EACA,aAAAC;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF,MAAqB;AACnB,QAAM,UAAU,eAAe;AAC/B,QAAM,sBAAkB;AAAA,IACtB,MAAM,oBAAoB,QAAQ,OAAO,OAAO;AAAA,IAChD,CAAC,QAAQ,OAAO,OAAO;AAAA,EACzB;AAEA,aAAW,CAAC,GAAG,iBAAiB,GAAG,QAAQ;AAE3C,QAAM,gBAAwC,CAAC;AAE/C,WAAS,IAAI,GAAG,IAAI,SAAS,QAAQ,KAAK;AACxC,QAAI,SAAS,CAAC,EAAE,yBAAyB,GAAG;AAC1C,YAAM,KAAK,SAAS,CAAC,EAAE;AACvB,YAAM,gBAA2C,SAAS;AAAA,QACxD,CAAC,YAAY,QAAQ,gBAAgB,KAAK,QAAQ,sBAAsB;AAAA,MAC1E;AAEA,UAAI,eAAe;AACjB,sBAAc,EAAE,IAAI,wCAAc,aAAa,cAAc,UAAU,EAAE;AAAA,MAC3E;AAAA,IACF;AAAA,EACF;AAEA,QAAM,EAAE,sBAAsB,eAAe,IAAI,kBAAkB,QAAQ;AAE3E,QAAM,gBAAY,gDAA4B;AAE9C,SACE,+CAAC,SAAI,WAAU,sBAAqB,KAAK,sBACvC;AAAA,mDAAC,SAAI,WAAU,+BACZ;AAAA,eAAS,IAAI,CAAC,SAAS,UAAU;AAChC,cAAM,mBAAmB,UAAU,SAAS,SAAS;AAErD,YAAI,QAAQ,cAAc,GAAG;AAC3B,iBACE;AAAA,YAACN;AAAA,YAAA;AAAA,cAEC;AAAA,cACA;AAAA,cACA;AAAA,cACA;AAAA,cACA,kBAAkBK;AAAA,cAClB,aAAaC;AAAA,cACb;AAAA,cACA;AAAA,cACA;AAAA,cACA;AAAA,cACA;AAAA;AAAA,YAXK;AAAA,UAYP;AAAA,QAEJ,WAAW,QAAQ,yBAAyB,GAAG;AAC7C,iBACE;AAAA,YAACL;AAAA,YAAA;AAAA,cAEC;AAAA,cACA;AAAA,cACA;AAAA,cACA;AAAA,cACA,cAAc,cAAc,QAAQ,EAAE;AAAA,cACtC,kBAAkBI;AAAA,cAClB,aAAaC;AAAA;AAAA,YAPR;AAAA,UAQP;AAAA,QAEJ,WAAW,QAAQ,oBAAoB,GAAG;AACxC,iBACE;AAAA,YAACJ;AAAA,YAAA;AAAA,cAEC;AAAA,cACA;AAAA,cACA;AAAA,cACA;AAAA,cACA,kBAAkBG;AAAA,cAClB,aAAaC;AAAA;AAAA,YANR;AAAA,UAOP;AAAA,QAEJ,WAAW,QAAQ,gBAAgB,GAAG;AACpC,iBACE;AAAA,YAACH;AAAA,YAAA;AAAA,cAEC;AAAA,cACA;AAAA,cACA;AAAA,cACA;AAAA,cACA,kBAAkBE;AAAA,cAClB,aAAaC;AAAA;AAAA,YANR;AAAA,UAOP;AAAA,QAEJ,WAAW,QAAQ,kBAAkB,QAAQ,eAAe,GAAG;AAC7D,iBACE;AAAA,YAACF;AAAA,YAAA;AAAA,cAEC;AAAA,cACA;AAAA,cACA;AAAA,cACA;AAAA,cACA,kBAAkBC;AAAA,cAClB,aAAaC;AAAA,cACb;AAAA,cACA;AAAA,cACA;AAAA,cACA;AAAA;AAAA,YAVK;AAAA,UAWP;AAAA,QAEJ;AAAA,MACF,CAAC;AAAA,MACA;AAAA,OACH;AAAA,IACA,8CAAC,YAAO,WAAU,4BAA2B,KAAK,gBAC/C,UACH;AAAA,KACF;AAEJ;AAEA,SAAS,oBAAoB,SAAwC;AACnE,MAAI,eAAyB,CAAC;AAC9B,MAAI,SAAS;AACX,QAAI,MAAM,QAAQ,OAAO,GAAG;AAC1B,mBAAa,KAAK,GAAG,OAAO;AAAA,IAC9B,OAAO;AACL,mBAAa,KAAK,OAAO;AAAA,IAC3B;AAAA,EACF;AAEA,SAAO,aAAa;AAAA,IAClB,CAAC,YACC,IAAI,sCAAY;AAAA,MACd,MAAM,+BAAK;AAAA,MACX,SAAS;AAAA,IACX,CAAC;AAAA,EACL;AACF;AAEO,SAAS,kBAAkB,UAAiB;AACjD,QAAM,qBAAiB,sBAAuB,IAAI;AAClD,QAAM,2BAAuB,sBAA8B,IAAI;AAC/D,QAAM,8BAA0B,sBAAO,KAAK;AAC5C,QAAM,wBAAoB,sBAAO,KAAK;AAEtC,QAAM,iBAAiB,MAAM;AAC3B,QAAI,qBAAqB,WAAW,eAAe,SAAS;AAC1D,8BAAwB,UAAU;AAClC,2BAAqB,QAAQ,YAAY,qBAAqB,QAAQ;AAAA,IACxE;AAAA,EACF;AAEA,QAAM,eAAe,MAAM;AACzB,QAAI,wBAAwB,SAAS;AACnC,8BAAwB,UAAU;AAClC;AAAA,IACF;AAEA,QAAI,qBAAqB,SAAS;AAChC,YAAM,EAAE,WAAW,cAAc,aAAa,IAAI,qBAAqB;AACvE,wBAAkB,UAAU,YAAY,eAAe;AAAA,IACzD;AAAA,EACF;AAEA,+BAAU,MAAM;AACd,UAAM,YAAY,qBAAqB;AACvC,QAAI,WAAW;AACb,gBAAU,iBAAiB,UAAU,YAAY;AAAA,IACnD;AACA,WAAO,MAAM;AACX,UAAI,WAAW;AACb,kBAAU,oBAAoB,UAAU,YAAY;AAAA,MACtD;AAAA,IACF;AAAA,EACF,GAAG,CAAC,CAAC;AAEL,+BAAU,MAAM;AACd,UAAM,YAAY,qBAAqB;AACvC,QAAI,CAAC,WAAW;AACd;AAAA,IACF;AAEA,UAAM,mBAAmB,IAAI,iBAAiB,MAAM;AAClD,UAAI,CAAC,kBAAkB,SAAS;AAC9B,uBAAe;AAAA,MACjB;AAAA,IACF,CAAC;AAED,qBAAiB,QAAQ,WAAW;AAAA,MAClC,WAAW;AAAA,MACX,SAAS;AAAA,MACT,eAAe;AAAA,IACjB,CAAC;AAED,WAAO,MAAM;AACX,uBAAiB,WAAW;AAAA,IAC9B;AAAA,EACF,GAAG,CAAC,CAAC;AAEL,+BAAU,MAAM;AACd,sBAAkB,UAAU;AAC5B,mBAAe;AAAA,EACjB,GAAG,CAAC,SAAS,OAAO,CAAC,MAAM,EAAE,cAAc,KAAK,EAAE,SAAS,+BAAK,IAAI,EAAE,MAAM,CAAC;AAE7E,SAAO,EAAE,gBAAgB,qBAAqB;AAChD;;;AC9NA,IAAAC,gBAAwC;;;ACAxC,IAAAC,gBAAoF;AA2C9E,IAAAC,uBAAA;AAhCN,IAAM,2BAAuB;AAAA,EAC3B,CAAC,EAAE,UAAU,GAAG,aAAa,OAAO,UAAU,WAAW,UAAU,GAAG,QAAQ;AAC5E,UAAM,0BAAsB,sBAA4B,IAAI;AAC5D,UAAM,CAAC,WAAW,YAAY,QAAI,wBAAiB,CAAC;AAEpD,2CAAoB,KAAK,MAAM,oBAAoB,OAA8B;AAEjF,iCAAU,MAAM;AACd,YAAM,qBAAqB,MAAM;AAC/B,cAAM,WAAW,oBAAoB;AACrC,YAAI,UAAU;AACZ,mBAAS,MAAM,SAAS;AACxB,gBAAM,kBAAkB,SAAS;AACjC,uBAAa,kBAAkB,OAAO;AACtC,cAAI,WAAW;AACb,qBAAS,MAAM;AAAA,UACjB;AAAA,QACF;AAAA,MACF;AAEA,yBAAmB;AAAA,IACrB,GAAG,CAAC,OAAO,CAAC;AAEZ,iCAAU,MAAM;AACd,YAAM,WAAW,oBAAoB;AACrC,UAAI,UAAU;AACZ,iBAAS,MAAM,SAAS;AACxB,iBAAS,MAAM,SAAS,GAAG,KAAK,IAAI,SAAS,cAAc,SAAS;AAAA,MACtE;AAAA,IACF,GAAG,CAAC,OAAO,SAAS,CAAC;AAErB,WACE;AAAA,MAAC;AAAA;AAAA,QACC,KAAK;AAAA,QACL;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA,OAAO;AAAA,UACL,UAAU;AAAA,UACV,QAAQ;AAAA,UACR,WAAW,GAAG;AAAA,QAChB;AAAA,QACA,MAAM;AAAA;AAAA,IACR;AAAA,EAEJ;AACF;AAEA,IAAO,mBAAQ;;;AC5Df,IAAAC,qBAA6D;AAE7D,IAAAC,gBAA8D;AA6B9D,IAAM,iBAAiB,CACrB,gBACA,kBACA,iBACA,gBACA,WACG;AACH,MAAI,CAAC,eAAe,WAAW,CAAC,gBAAgB,SAAS;AACvD,mBAAe,UAAU,MAAM,UAAU,aAAa,aAAa,EAAE,OAAO,KAAK,CAAC;AAClF,oBAAgB,UAAU,IAAI,OAAO,aAAa;AAClD,UAAM,gBAAgB,QAAQ,OAAO;AAAA,EACvC;AAEA,mBAAiB,UAAU,IAAI,cAAc,eAAe,OAAQ;AACpE,mBAAiB,QAAQ,MAAM,GAAI;AACnC,mBAAiB,QAAQ,kBAAkB,CAAC,UAAU;AACpD,mBAAe,KAAK,MAAM,IAAI;AAAA,EAChC;AACA,mBAAiB,QAAQ,SAAS;AACpC;AAEA,IAAM,gBAAgB,CAAC,qBAA6D;AAClF,MAAI,iBAAiB,WAAW,iBAAiB,QAAQ,UAAU,YAAY;AAC7E,qBAAiB,QAAQ,KAAK;AAAA,EAChC;AACF;AAEA,IAAM,kBAAkB,CAAO,gBAAwB,uBAA+B;AACpF,QAAM,eAAe,IAAI,KAAK,gBAAgB,EAAE,MAAM,YAAY,CAAC;AACnE,QAAM,WAAW,IAAI,SAAS;AAC9B,WAAS,OAAO,QAAQ,cAAc,eAAe;AAErD,QAAM,WAAW,MAAM,MAAM,oBAAoB;AAAA,IAC/C,QAAQ;AAAA,IACR,MAAM;AAAA,EACR,CAAC;AAED,MAAI,CAAC,SAAS,IAAI;AAChB,UAAM,IAAI,MAAM,UAAU,SAAS,YAAY;AAAA,EACjD;AAEA,QAAM,gBAAgB,MAAM,SAAS,KAAK;AAC1C,SAAO,cAAc;AACvB;AAEA,IAAM,oBAAoB,CAAC,MAAc,iBAAyB,iBAA+B;AAC/F,QAAM,cAAc,mBAAmB,IAAI;AAC3C,QAAM,MAAM,GAAG,wBAAwB;AAEvC,QAAM,GAAG,EACN,KAAK,CAAC,aAAa,SAAS,YAAY,CAAC,EACzC,KAAK,CAAC,gBAAgB,aAAa,gBAAgB,WAAW,CAAC,EAC/D,KAAK,CAAC,gBAAgB;AACrB,UAAM,SAAS,aAAa,mBAAmB;AAC/C,WAAO,SAAS;AAChB,WAAO,QAAQ,aAAa,WAAW;AACvC,WAAO,MAAM,CAAC;AAAA,EAChB,CAAC,EACA,MAAM,CAAC,UAAU;AAChB,YAAQ,MAAM,kCAAkC,KAAK;AAAA,EACvD,CAAC;AACL;AAMO,IAAM,gBAAgB,CAAC;AAAA,EAC5B;AAAA,EACA;AACF,MAGM;AACJ,QAAM,CAAC,iBAAiB,kBAAkB,QAAI,wBAA0B,MAAM;AAC9E,QAAM,qBAAiB,sBAA2B,IAAI;AACtD,QAAM,sBAAkB,sBAA4B,IAAI;AACxD,QAAM,uBAAmB,sBAA6B,IAAI;AAC1D,QAAM,qBAAiB,sBAAe,CAAC,CAAC;AACxC,QAAM,qBAAiB,sCAAkB;AACzC,QAAM,sBAAkB,8CAA0B;AAClD,QAAM,UAAU,kCAAK,iBAAmB;AACxC,QAAM,CAAC,2BAA2B,4BAA4B,QAAI,wBAAwB,IAAI;AAE9F,+BAAU,MAAM;AACd,QAAI,oBAAoB,aAAa;AACnC;AAAA,QACE;AAAA,QACA;AAAA,QACA;AAAA,QACA,eAAe;AAAA,QACf,MAAM;AACJ,6BAAmB,cAAc;AAAA,QACnC;AAAA,MACF;AAAA,IACF,OAAO;AACL,oBAAc,gBAAgB;AAC9B,UAAI,oBAAoB,gBAAgB;AACtC,wBAAgB,eAAe,SAAS,QAAQ,iBAAiB,kBAAmB,EAAE;AAAA,UACpF,CAAO,kBAAkB;AACvB,2BAAe,UAAU,CAAC;AAC1B,+BAAmB,MAAM;AACzB,kBAAM,UAAU,MAAM,aAAa,aAAa;AAChD,yCAA6B,QAAQ,EAAE;AAAA,UACzC;AAAA,QACF;AAAA,MACF;AAAA,IACF;AAEA,WAAO,MAAM;AACX,oBAAc,gBAAgB;AAAA,IAChC;AAAA,EACF,GAAG,CAAC,eAAe,CAAC;AAEpB,+BAAU,MAAM;AACd,QAAI,eAAe,SAAS,2BAA2B;AACrD,YAAM,mBAAmB,QAAQ,SAAS;AAAA,QACxC,CAAC,YAAY,QAAQ,OAAO;AAAA,MAC9B;AAEA,YAAM,oBAAoB,QAAQ,SAC/B,MAAM,mBAAmB,CAAC,EAC1B;AAAA,QACC,CAAC,YAAY,QAAQ,cAAc,KAAK,QAAQ,SAAS;AAAA,MAC3D;AAEF,YAAM,OAAO,kBAAkB,IAAI,CAAC,YAAY,QAAQ,OAAO,EAAE,KAAK,IAAI;AAC1E,wBAAkB,MAAM,QAAQ,iBAAiB,iBAAkB,gBAAgB,OAAQ;AAE3F,mCAA6B,IAAI;AAAA,IACnC;AAAA,EACF,GAAG,CAAC,2BAA2B,UAAU,CAAC;AAE1C,SAAO,EAAE,iBAAiB,mBAAmB;AAC/C;;;AFhKA,IAAAC,qBAAkC;;;AGL3B,IAAM,cAAc,MAAM;AAC/B,MAAI,OAAO,WAAW;AAAa,WAAO;AAC1C,SACE,SAAS,gBAAgB,UAAU,SAAS,MAAM,KAClD,SAAS,KAAK,UAAU,SAAS,MAAM,KACvC,SAAS,gBAAgB,aAAa,YAAY,MAAM,UACxD,SAAS,KAAK,aAAa,YAAY,MAAM,UAC7C,OAAO,WAAW,8BAA8B,EAAE;AAEtD;;;ACcM,IAAAC,uBAAA;AApBC,SAAS,aAAa,EAAE,gBAAgB,KAAK,GAAgC;AAClF,QAAM,SAAS,YAAY;AAE3B,MAAI,CAAC,eAAe;AAClB,WAAO;AAAA,EACT;AAEA,QAAM,iBAAiB;AAAA,IACrB,YAAY;AAAA,IACZ,SAAS;AAAA,IACT,UAAU;AAAA,IACV,WAAW;AAAA,IACX,UAAU;AAAA,IACV,SAAS;AAAA,IACT,OAAO,SAAS,oBAAoB;AAAA,EACtC;AAEA,SACE,8CAAC,SAEC,wDAAC,OAAE,WAAU,aAAY,OAAO,gBAAgB,mCAEhD,GACF;AAEJ;;;AJ4DQ,IAAAC,uBAAA;AAhFR,IAAM,eAAe;AAEd,IAAM,QAAQ,CAAC,EAAE,YAAY,QAAQ,YAAY,OAAO,QAAQ,SAAS,MAAkB;AAVlG;AAWE,QAAM,UAAU,eAAe;AAC/B,QAAM,qBAAiB,sCAAkB;AAEzC,QAAM,gBAAgB,GAAC,oBAAe,qBAAf,mBAAiC;AAExD,QAAM,uBACJ,eAAe,iBAAiB,oBAAoB,UACpD,eAAe,iBAAiB,uBAAuB;AAEzD,QAAM,kBAAc,sBAA4B,IAAI;AAEpD,QAAM,iBAAiB,CAAC,UAA4C;AAtBtE,QAAAC;AAuBI,UAAM,SAAS,MAAM;AAGrB,QAAI,OAAO,QAAQ,QAAQ;AAAG;AAG9B,QAAI,OAAO,YAAY;AAAY;AAGnC,KAAAA,MAAA,YAAY,YAAZ,gBAAAA,IAAqB;AAAA,EACvB;AAEA,QAAM,CAAC,MAAM,OAAO,QAAI,wBAAS,EAAE;AACnC,QAAM,OAAO,MAAM;AApCrB,QAAAA;AAqCI,QAAI;AAAY;AAChB,WAAO,IAAI;AACX,YAAQ,EAAE;AAEV,KAAAA,MAAA,YAAY,YAAZ,gBAAAA,IAAqB;AAAA,EACvB;AAiBA,QAAM,EAAE,iBAAiB,mBAAmB,IAAI,cAAc;AAAA,IAC5D,cAAc;AAAA,IACd;AAAA,EACF,CAAC;AAED,QAAM,eAAe,cAAc,oBAAoB;AACvD,QAAM,aAAa,eAAe,QAAQ,MAAM,WAAW,QAAQ,MAAM;AACzE,QAAM,iBACJ,yBACC,oBAAoB,UAAU,oBAAoB,gBACnD,CAAC;AAEH,QAAM,UAAU,MAAM;AAvExB,QAAAA;AAwEI,UAAM,kBAAiBA,MAAA,eAAe,6BAAf,gBAAAA,IAAyC;AAChE,UAAM,uBACJ,iDAAgB,UAAS,6BAA6B,EAAC,iDAAgB;AAEzE,YACG,gBAAiB,CAAC,gBAAgB,KAAK,KAAK,EAAE,SAAS,MACxD,oBAAoB,UACpB,CAAC;AAAA,EAEL;AAEA,QAAM,eAAe,CAAC,QAAQ;AAE9B,SACE,+CAAC,SAAI,WAAW,4BAA4B,gBAAgB,uBAAuB,MACjF;AAAA,mDAAC,SAAI,WAAU,mBAAkB,SAAS,gBACxC;AAAA;AAAA,QAAC;AAAA;AAAA,UACC,KAAK;AAAA,UACL,aAAa,QAAQ,OAAO;AAAA,UAC5B,WAAW;AAAA,UACX,SAAS;AAAA,UACT,OAAO;AAAA,UACP,UAAU,CAAC,UAAU,QAAQ,MAAM,OAAO,KAAK;AAAA,UAC/C,WAAW,CAAC,UAAU;AACpB,gBAAI,MAAM,QAAQ,WAAW,CAAC,MAAM,UAAU;AAC5C,oBAAM,eAAe;AACrB,kBAAI,QAAQ,GAAG;AACb,qBAAK;AAAA,cACP;AAAA,YACF;AAAA,UACF;AAAA;AAAA,MACF;AAAA,MACA,+CAAC,SAAI,WAAU,2BACZ;AAAA,oBACC,8CAAC,YAAO,SAAS,UAAU,WAAU,gCAClC,kBAAQ,MAAM,YACjB;AAAA,QAGF,8CAAC,SAAI,OAAO,EAAE,UAAU,EAAE,GAAG;AAAA,QAE5B,kBACC;AAAA,UAAC;AAAA;AAAA,YACC,SAAS,MACP,mBAAmB,oBAAoB,SAAS,cAAc,cAAc;AAAA,YAE9E,WACE,oBAAoB,cAChB,+DACA;AAAA,YAGL,kBAAQ,MAAM;AAAA;AAAA,QACjB;AAAA,QAEF;AAAA,UAAC;AAAA;AAAA,YACC,UAAU;AAAA,YACV,SAAS,eAAe,SAAS;AAAA,YACjC,+BAA6B;AAAA,YAC7B,gBAAc,aAAa,qCAAqC;AAAA,YAChE,WAAU;AAAA,YAET;AAAA;AAAA,QACH;AAAA,SACF;AAAA,OACF;AAAA,IACA,8CAAC,gBAAa,eAA8B;AAAA,KAC9C;AAEJ;;;AKzII,IAAAC,uBAAA;AAFG,IAAM,cAAc,CAAC,UAA4B;AACtD,SACE,8CAAC,SAAI,WAAU,2CACZ,gBAAM,gBAAgB,MAAM,SAC/B;AAEJ;;;ACRA,IAAAC,iBAAyB;AACzB,4BAAmD;;;ACDnD,IAAAC,iBAAyB;AACzB,sCAA2C;;;ACD3C,IAAAC,SAAuB;AAMhB,SAAS,mBAAmB,EAAE,UAAU,IAAK,GAA4B;AAC9E,QAAM,CAAC,UAAU,WAAW,IAAU,gBAAkB,KAAK;AAE7D,QAAM,kBAAkB,CAAC,UAAkB;AAT7C;AAUI,QAAI,OAAO,WAAW,eAAe,GAAC,eAAU,cAAV,mBAAqB,YAAW;AACpE;AAAA,IACF;AAEA,QAAI,CAAC,OAAO;AACV;AAAA,IACF;AAEA,cAAU,UAAU,UAAU,KAAK,EAAE,KAAK,MAAM;AAC9C,kBAAY,IAAI;AAEhB,iBAAW,MAAM;AACf,oBAAY,KAAK;AAAA,MACnB,GAAG,OAAO;AAAA,IACZ,CAAC;AAAA,EACH;AAEA,SAAO,EAAE,UAAU,gBAAgB;AACrC;;;AD+DQ,IAAAC,uBAAA;AAxED,IAAM,uBAAoC;AAAA,EAC/C,YAAY;AAAA,EACZ,QAAQ;AAAA,EACR,MAAM;AAAA,EACN,GAAG;AAAA,EACH,KAAK;AAAA,EACL,OAAO;AAAA,EACP,MAAM;AAAA,EACN,MAAM;AAAA,EACN,KAAK;AAAA,EACL,OAAO;AAAA,EACP,eAAe;AAAA,EACf,QAAQ;AAAA,EACR,YAAY;AAAA,EACZ,IAAI;AAAA,EACJ,MAAM;AAAA,EACN,MAAM;AAAA,EACN,OAAO;AAAA,EACP,SAAS;AAAA,EACT,KAAK;AAAA,EACL,OAAO;AAAA,EACP,KAAK;AAAA,EACL,MAAM;AAAA,EACN,KAAK;AAAA;AAEP;AAEO,IAAM,uBAAuB,CAAC,QAAgB,YAAY,UAAU;AACzE,QAAM,QAAQ;AACd,MAAI,SAAS;AACb,WAAS,IAAI,GAAG,IAAI,QAAQ,KAAK;AAC/B,cAAU,MAAM,OAAO,KAAK,MAAM,KAAK,OAAO,IAAI,MAAM,MAAM,CAAC;AAAA,EACjE;AACA,SAAO,YAAY,OAAO,YAAY,IAAI;AAC5C;AAEA,IAAM,gBAAuB,qBAAK,CAAC,EAAE,UAAU,MAAM,MAAM;AACzD,QAAM,EAAE,UAAU,gBAAgB,IAAI,mBAAmB,EAAE,SAAS,IAAK,CAAC;AAE1E,QAAM,iBAAiB,MAAM;AAC3B,QAAI,OAAO,WAAW,aAAa;AACjC;AAAA,IACF;AACA,UAAM,gBAAgB,qBAAqB,QAAQ,KAAK;AACxD,UAAM,oBAAoB,QAAQ,qBAAqB,GAAG,IAAI,IAAI;AAClE,UAAM,WAAW,OAAO,OAAO,mBAAyB,iBAAiB;AAEzE,QAAI,CAAC,UAAU;AAEb;AAAA,IACF;AAEA,UAAM,OAAO,IAAI,KAAK,CAAC,KAAK,GAAG,EAAE,MAAM,aAAa,CAAC;AACrD,UAAM,MAAM,IAAI,gBAAgB,IAAI;AACpC,UAAM,OAAO,SAAS,cAAc,GAAG;AACvC,SAAK,WAAW;AAChB,SAAK,OAAO;AACZ,SAAK,MAAM,UAAU;AACrB,aAAS,KAAK,YAAY,IAAI;AAC9B,SAAK,MAAM;AACX,aAAS,KAAK,YAAY,IAAI;AAC9B,QAAI,gBAAgB,GAAG;AAAA,EACzB;AAEA,QAAM,SAAS,MAAM;AACnB,QAAI;AAAU;AACd,oBAAgB,KAAK;AAAA,EACvB;AAEA,SACE,+CAAC,SAAI,WAAU,uBACb;AAAA,mDAAC,SAAI,WAAU,8BACb;AAAA,oDAAC,UAAK,WAAU,sCAAsC,oBAAS;AAAA,MAC/D,+CAAC,SAAI,WAAU,qCACb;AAAA,sDAAC,YAAO,WAAU,oCAAmC,SAAS,gBAC3D,wBACH;AAAA,QACA,8CAAC,YAAO,WAAU,oCAAmC,SAAS,QAC3D,qBAAW,YAAY,UAC1B;AAAA,SACF;AAAA,OACF;AAAA,IACA;AAAA,MAAC,gCAAAC;AAAA,MAAA;AAAA,QACC;AAAA,QACA,OAAO;AAAA,QACP,QAAO;AAAA,QACP,aAAa;AAAA,UACX,QAAQ;AAAA,UACR,wBAAwB;AAAA,UACxB,yBAAyB;AAAA,QAC3B;AAAA,QAEC;AAAA;AAAA,IACH;AAAA,KACF;AAEJ,CAAC;AACD,UAAU,cAAc;AAQxB,IAAM,iBAAsB;AAAA,EAC1B,2BAA2B;AAAA,IACzB,OAAO;AAAA,IACP,UAAU;AAAA,IACV,YAAY;AAAA,IACZ,YAAY;AAAA,IACZ,WAAW;AAAA,IACX,WAAW;AAAA,IACX,YAAY;AAAA,IACZ,aAAa;AAAA,IACb,WAAW;AAAA,IACX,YAAY;AAAA,IACZ,YAAY;AAAA,IACZ,UAAU;AAAA,IACV,SAAS;AAAA,IACT,eAAe;AAAA,IACf,YAAY;AAAA,IACZ,WAAW;AAAA,IACX,SAAS;AAAA,IACT,SAAS;AAAA,IACT,QAAQ;AAAA,IACR,UAAU;AAAA,IACV,YAAY;AAAA,EACd;AAAA,EACA,4BAA4B;AAAA,IAC1B,OAAO;AAAA,IACP,UAAU;AAAA,IACV,YAAY;AAAA,IACZ,YAAY;AAAA,IACZ,WAAW;AAAA,IACX,WAAW;AAAA,IACX,YAAY;AAAA,IACZ,aAAa;AAAA,IACb,WAAW;AAAA,IACX,YAAY;AAAA,IACZ,YAAY;AAAA,IACZ,UAAU;AAAA,IACV,SAAS;AAAA,IACT,eAAe;AAAA,IACf,YAAY;AAAA,IACZ,WAAW;AAAA,IACX,SAAS;AAAA,EACX;AAAA,EACA,sCAAsC;AAAA,IACpC,YAAY;AAAA,IACZ,YAAY;AAAA,EACd;AAAA,EACA,uCAAuC;AAAA,IACrC,YAAY;AAAA,IACZ,YAAY;AAAA,EACd;AAAA,EACA,wCAAwC;AAAA,IACtC,YAAY;AAAA,IACZ,YAAY;AAAA,EACd;AAAA,EACA,yCAAyC;AAAA,IACvC,YAAY;AAAA,IACZ,YAAY;AAAA,EACd;AAAA,EACA,wCAAwC;AAAA,IACtC,SAAS;AAAA,IACT,cAAc;AAAA,IACd,OAAO;AAAA,IACP,YAAY;AAAA,EACd;AAAA,EACA,cAAc;AAAA,IACZ,SAAS;AAAA,EACX;AAAA,EACA,uBAAuB;AAAA,IACrB,OAAO;AAAA,EACT;AAAA,EACA,gBAAgB;AAAA,IACd,OAAO;AAAA,EACT;AAAA,EACA,SAAS;AAAA,IACP,OAAO;AAAA,EACT;AAAA,EACA,QAAQ;AAAA,IACN,OAAO;AAAA,EACT;AAAA,EACA,aAAa;AAAA,IACX,OAAO;AAAA,EACT;AAAA,EACA,mDAAmD;AAAA,IACjD,OAAO;AAAA,EACT;AAAA,EACA,0DAA0D;AAAA,IACxD,OAAO;AAAA,EACT;AAAA,EACA,UAAU;AAAA,IACR,OAAO;AAAA,EACT;AAAA,EACA,KAAK;AAAA,IACH,OAAO;AAAA,EACT;AAAA,EACA,SAAS;AAAA,IACP,OAAO;AAAA,EACT;AAAA,EACA,QAAQ;AAAA,IACN,OAAO;AAAA,EACT;AAAA,EACA,UAAU;AAAA,IACR,OAAO;AAAA,EACT;AAAA,EACA,QAAQ;AAAA,IACN,OAAO;AAAA,EACT;AAAA,EACA,UAAU;AAAA,IACR,OAAO;AAAA,EACT;AAAA,EACA,MAAM;AAAA,IACJ,OAAO;AAAA,EACT;AAAA,EACA,UAAU;AAAA,IACR,OAAO;AAAA,EACT;AAAA,EACA,aAAa;AAAA,IACX,OAAO;AAAA,EACT;AAAA,EACA,QAAQ;AAAA,IACN,OAAO;AAAA,EACT;AAAA,EACA,MAAM;AAAA,IACJ,OAAO;AAAA,EACT;AAAA,EACA,SAAS;AAAA,IACP,OAAO;AAAA,EACT;AAAA,EACA,SAAS;AAAA,IACP,OAAO;AAAA,EACT;AAAA,EACA,mCAAmC;AAAA,IACjC,gBAAgB;AAAA,EAClB;AAAA,EACA,UAAU;AAAA,IACR,OAAO;AAAA,EACT;AAAA,EACA,QAAQ;AAAA,IACN,OAAO;AAAA,EACT;AAAA,EACA,kBAAkB;AAAA,IAChB,OAAO;AAAA,EACT;AAAA,EACA,QAAQ;AAAA,IACN,OAAO;AAAA,EACT;AAAA,EACA,eAAe;AAAA,IACb,OAAO;AAAA,EACT;AAAA,EACA,cAAc;AAAA,IACZ,OAAO;AAAA,EACT;AAAA,EACA,uBAAuB;AAAA,IACrB,OAAO;AAAA,EACT;AAAA,EACA,0BAA0B;AAAA,IACxB,OAAO;AAAA,EACT;AAAA,EACA,SAAS;AAAA,IACP,OAAO;AAAA,EACT;AAAA,EACA,kBAAkB;AAAA,IAChB,OAAO;AAAA,EACT;AAAA,EACA,wBAAwB;AAAA,IACtB,OAAO;AAAA,EACT;AAAA,EACA,UAAU;AAAA,IACR,OAAO;AAAA,EACT;AAAA,EACA,6BAA6B;AAAA,IAC3B,OAAO;AAAA,EACT;AAAA,EACA,OAAO;AAAA,IACL,OAAO;AAAA,EACT;AAAA,EACA,WAAW;AAAA,IACT,OAAO;AAAA,EACT;AAAA,EACA,QAAQ;AAAA,IACN,WAAW;AAAA,EACb;AAAA,EACA,cAAc;AAAA,IACZ,OAAO;AAAA,EACT;AAAA,EACA,oBAAoB;AAAA,IAClB,OAAO;AAAA,EACT;AAAA,EACA,SAAS;AAAA,IACP,OAAO;AAAA,EACT;AAAA,EACA,WAAW;AAAA,IACT,OAAO;AAAA,EACT;AAAA,EACA,eAAe;AAAA,IACb,OAAO;AAAA,EACT;AAAA,EACA,yCAAyC;AAAA,IACvC,OAAO;AAAA,EACT;AAAA,EACA,UAAU;AAAA,IACR,OAAO;AAAA,EACT;AAAA,EACA,4BAA4B;AAAA,IAC1B,OAAO;AAAA,EACT;AAAA,EACA,4BAA4B;AAAA,IAC1B,OAAO;AAAA,EACT;AAAA,EACA,QAAQ;AAAA,IACN,OAAO;AAAA,EACT;AAAA,EACA,mBAAmB;AAAA,IACjB,OAAO;AAAA,EACT;AAAA,EACA,OAAO;AAAA,IACL,OAAO;AAAA,EACT;AAAA,EACA,cAAc;AAAA,IACZ,OAAO;AAAA,EACT;AAAA,EACA,0BAA0B;AAAA,IACxB,OAAO;AAAA,EACT;AAAA,EACA,sCAAsC;AAAA,IACpC,OAAO;AAAA,EACT;AAAA,EACA,WAAW;AAAA,IACT,OAAO;AAAA,EACT;AAAA,EACA,qCAAqC;AAAA,IACnC,OAAO;AAAA,EACT;AAAA,EACA,sCAAsC;AAAA,IACpC,OAAO;AAAA,EACT;AAAA,EACA,8BAA8B;AAAA,IAC5B,OAAO;AAAA,EACT;AAAA,EACA,+BAA+B;AAAA,IAC7B,OAAO;AAAA,EACT;AAAA,EACA,qCAAqC;AAAA,IACnC,OAAO;AAAA,EACT;AAAA,EACA,sCAAsC;AAAA,IACpC,OAAO;AAAA,EACT;AAAA,EACA,8BAA8B;AAAA,IAC5B,OAAO;AAAA,EACT;AAAA,EACA,+BAA+B;AAAA,IAC7B,OAAO;AAAA,EACT;AAAA,EACA,8BAA8B;AAAA,IAC5B,OAAO;AAAA,EACT;AAAA,EACA,+BAA+B;AAAA,IAC7B,OAAO;AAAA,EACT;AAAA,EACA,+BAA+B;AAAA,IAC7B,OAAO;AAAA,EACT;AAAA,EACA,gCAAgC;AAAA,IAC9B,OAAO;AAAA,EACT;AAAA,EACA,iCAAiC;AAAA,IAC/B,OAAO;AAAA,EACT;AAAA,EACA,qCAAqC;AAAA,IACnC,OAAO;AAAA,EACT;AAAA,EACA,sDAAsD;AAAA,IACpD,UAAU;AAAA,IACV,QAAQ;AAAA,EACV;AAAA,EACA,kCAAkC;AAAA,IAChC,YAAY;AAAA,IACZ,WAAW;AAAA,IACX,QAAQ;AAAA,EACV;AACF;;;ADlZA,wBAAsB;AACtB,yBAAuB;AACvB,wBAAsB;AAKhB,IAAAC,uBAAA;AAHN,IAAM,oBAAgC;AAAA,EACpC,EAAE,IAAwB;AAAxB,iBAAE,WARN,IAQI,IAAe,kBAAf,IAAe,CAAb;AACF,WACE,8CAAC,oCAAE,WAAU,+BAAgC,QAA5C,EAAmD,QAAO,UAAS,KAAI,uBACrE,WACH;AAAA,EAEJ;AAAA;AAAA,EAEA,KAAK,IAA2C;AAA3C,iBAAE,YAAU,WAAW,OAhB9B,IAgBO,IAAkC,kBAAlC,IAAkC,CAAhC,YAAU,aAAW;AAC1B,QAAI,MAAM,QAAQ,QAAQ,KAAK,SAAS,QAAQ;AAC9C,UAAI,SAAS,CAAC,KAAK,UAAK;AACtB,eACE;AAAA,UAAC;AAAA;AAAA,YACC,OAAO;AAAA,cACL,WAAW;AAAA,cACX,WAAW;AAAA,YACb;AAAA,YACD;AAAA;AAAA,QAED;AAAA,MAEJ;AAEA,eAAS,CAAC,KAAK,qCAAW,IAAc,QAAQ,YAAO,QAAG;AAAA,IAC5D;AAEA,UAAM,QAAQ,iBAAiB,KAAK,aAAa,EAAE;AAEnD,QAAI,QAAQ;AACV,aACE,8CAAC,uCAAK,aAA0B,QAA/B,EACE,WACH;AAAA,IAEJ;AAEA,WACE;AAAA,MAAC;AAAA;AAAA,QAEC,UAAW,SAAS,MAAM,CAAC,KAAM;AAAA,QACjC,OAAO,OAAO,QAAQ,EAAE,QAAQ,OAAO,EAAE;AAAA,SACrC;AAAA,MAHC,KAAK,OAAO;AAAA,IAInB;AAAA,EAEJ;AAAA,EACA,IAAI,CAAC,OAAwB;AAAxB,iBAAE,WArDT,IAqDO,IAAe,kBAAf,IAAe,CAAb;AACL,yDAAC,qCAAG,WAAU,+BAAgC,QAA7C,EACE,WACH;AAAA;AAAA,EAEF,IAAI,CAAC,OAAwB;AAAxB,iBAAE,WA1DT,IA0DO,IAAe,kBAAf,IAAe,CAAb;AACL,yDAAC,qCAAG,WAAU,+BAAgC,QAA7C,EACE,WACH;AAAA;AAAA,EAEF,IAAI,CAAC,OAAwB;AAAxB,iBAAE,WA/DT,IA+DO,IAAe,kBAAf,IAAe,CAAb;AACL,yDAAC,qCAAG,WAAU,+BAAgC,QAA7C,EACE,WACH;AAAA;AAAA,EAEF,IAAI,CAAC,OAAwB;AAAxB,iBAAE,WApET,IAoEO,IAAe,kBAAf,IAAe,CAAb;AACL,yDAAC,qCAAG,WAAU,+BAAgC,QAA7C,EACE,WACH;AAAA;AAAA,EAEF,IAAI,CAAC,OAAwB;AAAxB,iBAAE,WAzET,IAyEO,IAAe,kBAAf,IAAe,CAAb;AACL,yDAAC,qCAAG,WAAU,+BAAgC,QAA7C,EACE,WACH;AAAA;AAAA,EAEF,IAAI,CAAC,OAAwB;AAAxB,iBAAE,WA9ET,IA8EO,IAAe,kBAAf,IAAe,CAAb;AACL,yDAAC,qCAAG,WAAU,+BAAgC,QAA7C,EACE,WACH;AAAA;AAAA,EAEF,GAAG,CAAC,OAAwB;AAAxB,iBAAE,WAnFR,IAmFM,IAAe,kBAAf,IAAe,CAAb;AACJ,yDAAC,oCAAE,WAAU,+BAAgC,QAA5C,EACE,WACH;AAAA;AAAA,EAEF,KAAK,CAAC,OAAwB;AAAxB,iBAAE,WAxFV,IAwFQ,IAAe,kBAAf,IAAe,CAAb;AACN,yDAAC,sCAAI,WAAU,+BAAgC,QAA9C,EACE,WACH;AAAA;AAAA,EAEF,YAAY,CAAC,OAAwB;AAAxB,iBAAE,WA7FjB,IA6Fe,IAAe,kBAAf,IAAe,CAAb;AACb,yDAAC,6CAAW,WAAU,+BAAgC,QAArD,EACE,WACH;AAAA;AAAA,EAEF,IAAI,CAAC,OAAwB;AAAxB,iBAAE,WAlGT,IAkGO,IAAe,kBAAf,IAAe,CAAb;AACL,yDAAC,qCAAG,WAAU,+BAAgC,QAA7C,EACE,WACH;AAAA;AAAA,EAEF,IAAI,CAAC,OAAwB;AAAxB,iBAAE,WAvGT,IAuGO,IAAe,kBAAf,IAAe,CAAb;AACL,yDAAC,qCAAG,WAAU,+BAAgC,QAA7C,EACE,WACH;AAAA;AAEJ;AAEA,IAAM,4BAAqC;AAAA,EACzC,sBAAAC;AAAA,EACA,CAAC,WAAW,cACV,UAAU,aAAa,UAAU,YAAY,UAAU,eAAe,UAAU;AACpF;AAOO,IAAM,WAAW,CAAC,EAAE,SAAS,WAAW,MAAqB;AAClE,SACE,8CAAC,SAAI,WAAU,sBACb;AAAA,IAAC;AAAA;AAAA,MACC,YAAY,kCAAK,oBAAsB;AAAA,MACvC,eAAe,CAAC,kBAAAC,SAAW,mBAAAC,OAAU;AAAA,MACrC,eAAe,CAAC,kBAAAC,OAAS;AAAA,MAExB;AAAA;AAAA,EACH,GACF;AAEJ;;;AGlIA,IAAAC,iBAAyB;AAgDG,IAAAC,uBAAA;AA9CrB,IAAM,mBAAmB,CAAC,UAAiC;AAChE,QAAM,EAAE,OAAO,OAAO,IAAI,eAAe;AACzC,QAAM;AAAA,IACJ;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF,IAAI;AACJ,QAAM,CAAC,QAAQ,SAAS,QAAI,yBAAS,KAAK;AAE1C,QAAM,aAAa,MAAM;AACvB,QAAI,WAAW,QAAQ;AACrB,gBAAU,UAAU,UAAU,OAAO;AACrC,gBAAU,IAAI;AACd,aAAO,OAAO;AACd,iBAAW,MAAM,UAAU,KAAK,GAAG,GAAI;AAAA,IACzC,WAAW,SAAS;AAClB,gBAAU,UAAU,UAAU,OAAO;AACrC,gBAAU,IAAI;AACd,iBAAW,MAAM,UAAU,KAAK,GAAG,GAAI;AAAA,IACzC;AAAA,EACF;AAEA,QAAM,mBAAmB,MAAM;AAC7B,QAAI,cAAc;AAChB,mBAAa;AAAA,IACf;AAAA,EACF;AAEA,QAAM,iBAAiB,MAAM;AAC3B,QAAI,cAAc,SAAS;AACzB,iBAAW,OAAO;AAAA,IACpB;AAAA,EACF;AAEA,QAAM,mBAAmB,MAAM;AAC7B,QAAI,gBAAgB,SAAS;AAC3B,mBAAa,OAAO;AAAA,IACtB;AAAA,EACF;AAEA,QAAM,cAAc,MAAM,8CAAC,UAAM,gBAAM,cAAa;AAEpD,SACE,gFACI;AAAA,gBAAW,cACX,+CAAC,SAAI,WAAU,gDACZ;AAAA,iBAAW,8CAAC,YAAS,SAAS,WAAW,IAAI,YAAY,sBAAsB;AAAA,MAC/E,aAAa,8CAAC,eAAY;AAAA,MAE1B,WAAW,CAAC,aACX;AAAA,QAAC;AAAA;AAAA,UACC,WAAW,6BAA6B,mBAAmB,mBAAmB;AAAA,UAE9E;AAAA;AAAA,cAAC;AAAA;AAAA,gBACC,WAAU;AAAA,gBACV,SAAS;AAAA,gBACT,cAAY,OAAO;AAAA,gBACnB,OAAO,OAAO;AAAA,gBAEb,gBAAM;AAAA;AAAA,YACT;AAAA,YACA;AAAA,cAAC;AAAA;AAAA,gBACC,WAAU;AAAA,gBACV,SAAS;AAAA,gBACT,cAAY,OAAO;AAAA,gBACnB,OAAO,OAAO;AAAA,gBAEb,mBACC,8CAAC,UAAK,OAAO,EAAE,UAAU,QAAQ,YAAY,OAAO,GAAG,oBAAC,IAExD,MAAM;AAAA;AAAA,YAEV;AAAA,YACC,cACC;AAAA,cAAC;AAAA;AAAA,gBACC,WAAU;AAAA,gBACV,SAAS;AAAA,gBACT,cAAY,OAAO;AAAA,gBACnB,OAAO,OAAO;AAAA,gBAEb,gBAAM;AAAA;AAAA,YACT;AAAA,YAED,gBACC;AAAA,cAAC;AAAA;AAAA,gBACC,WAAU;AAAA,gBACV,SAAS;AAAA,gBACT,cAAY,OAAO;AAAA,gBACnB,OAAO,OAAO;AAAA,gBAEb,gBAAM;AAAA;AAAA,YACT;AAAA;AAAA;AAAA,MAEJ;AAAA,OAEJ;AAAA,IAEF,8CAAC,SAAI,OAAO,EAAE,cAAc,SAAS,GAAI,wBAAa;AAAA,KACxD;AAEJ;;;ACvFQ,IAAAC,uBAAA;AApBD,SAAS,kBAAkB,IAIX;AAJW,eAChC;AAAA,iBAAAC,eAAc;AAAA,IACd,kBAAAC,oBAAmB;AAAA,EANrB,IAIkC,IAG7B,kBAH6B,IAG7B;AAAA,IAFH;AAAA,IACA;AAAA;AAGA,QAAM;AAAA,IACJ;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF,IAAI;AAEJ,MAAI,QAAQ,cAAc,GAAG;AAC3B,QAAI,QAAQ,SAAS,QAAQ;AAC3B,aACE;AAAA,QAACD;AAAA,QAAA;AAAA,UAEC,qBAAkB;AAAA,UAClB,SAAS,QAAQ;AAAA,UACjB,SAAS;AAAA;AAAA,QAHJ;AAAA,MAIP;AAAA,IAEJ,WAAW,QAAQ,QAAQ,aAAa;AACtC,aACE;AAAA,QAACC;AAAA,QAAA;AAAA,UAEC,qBAAkB;AAAA,UAClB,SAAS,QAAQ;AAAA,UACjB,SAAS;AAAA,UACT,WAAW,cAAc,oBAAoB,CAAC,QAAQ;AAAA,UACtD,cAAc,cAAc,oBAAoB,CAAC,CAAC,QAAQ;AAAA,UAC1D;AAAA,UACA,cAAc,MAAM,6CAAe,QAAQ;AAAA,UAC3C;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA;AAAA,QAXK;AAAA,MAYP;AAAA,IAEJ;AAAA,EACF;AACF;;;AClDA,IAAAC,6BAAkC;AAElC,IAAAC,qBAAwD;AAwB5C,IAAAC,uBAAA;AArBL,SAAS,6BAA6B,IAGtB;AAHsB,eAC3C;AAAA,sBAAAC,oBAAmB;AAAA,EANrB,IAK6C,IAExC,kBAFwC,IAExC;AAAA,IADH;AAAA;AAGA,QAAM,EAAE,oBAAoB,QAAI,sCAAkB;AAClD,QAAM,EAAE,SAAS,YAAY,OAAO,kBAAkB,aAAa,IAAI;AAEvE,MAAI,QAAQ,yBAAyB,GAAG;AACtC,QACE,oBAAoB,YAAY,SAC/B,oBAAoB,QAAQ,QAAQ,QAAQ,IAAI,KAC/C,oBAAoB,QAAQ,QAAQ,GAAG,IACzC;AACA,YAAM,SACJ,oBAAoB,QAAQ,QAAQ,QAAQ,IAAI,KAChD,oBAAoB,QAAQ,QAAQ,GAAG;AAEzC,UAAI,OAAO,WAAW,UAAU;AAE9B,YAAI,oBAAoB,YAAY;AAClC,iBACE;AAAA,YAACA;AAAA,YAAA;AAAA,cACC,SAAS;AAAA,cAET,qBAAkB;AAAA,cAClB,WAAW;AAAA,cACX,cAAc;AAAA,cACd,SAAS;AAAA;AAAA,YAJJ;AAAA,UAKP;AAAA,QAEJ,OAEK;AACH,iBAAO;AAAA,QACT;AAAA,MACF,OAEK;AACH,cAAM,OAAO,QAAQ;AAErB,YAAI,SAA+B;AAEnC,YAAI,iBAAiB,QAAW;AAC9B,mBAAS;AAAA,QACX,WAAW,QAAQ,OAAO,SAAS,6CAAkB,SAAS;AAC5D,mBAAS;AAAA,QACX;AAEA,YAAI;AACF,gBAAM,WAAW,OAAO;AAAA,YACtB;AAAA,YACA;AAAA,YACA,QAAQ;AAAA,YACR,MAAM,QAAQ;AAAA,UAChB,CAAC;AAED,cAAI,CAAC,YAAY,WAAW,YAAY;AACtC,mBAAO;AAAA,UACT;AACA,cAAI,OAAO,aAAa,UAAU;AAChC,mBACE;AAAA,cAACA;AAAA,cAAA;AAAA,gBACC,SAAS;AAAA,gBACT,qBAAkB;AAAA,gBAElB,WAAW;AAAA,gBACX,cAAc;AAAA,gBACd,SAAS;AAAA;AAAA,cAHJ;AAAA,YAIP;AAAA,UAEJ,OAAO;AACL,mBACE;AAAA,cAACA;AAAA,cAAA;AAAA,gBACC,SAAS;AAAA,gBACT,qBAAkB;AAAA,gBAElB,WAAW;AAAA,gBACX,cAAc;AAAA,gBACd,cAAc;AAAA;AAAA,cAHT;AAAA,YAIP;AAAA,UAEJ;AAAA,QACF,SAAS,GAAP;AACA,kBAAQ,MAAM,8CAA8C,QAAQ,SAAS,GAAG;AAChF,iBACE;AAAA,YAACA;AAAA,YAAA;AAAA,cACC,SAAS;AAAA,cACT,qBAAkB;AAAA,cAElB,WAAW;AAAA,cACX,cAAc;AAAA,cACd,cACE,+CAAC,SAAI,WAAU,gDACb;AAAA,+DAAC,OAAE;AAAA;AAAA,kBAA8C,QAAQ;AAAA,kBAAK;AAAA,mBAAC;AAAA,gBAC/D,8CAAC,SAAK,uBAAa,QAAQ,EAAE,UAAU,OAAO,CAAC,GAAE;AAAA,iBACnD;AAAA;AAAA,YAPG;AAAA,UASP;AAAA,QAEJ;AAAA,MACF;AAAA,IACF,WAES,CAAC,cAAc,CAAC,kBAAkB;AAEzC,aAAO;AAAA,IACT,OAAO;AAEL,aACE;AAAA,QAACA;AAAA,QAAA;AAAA,UACC,SAAS;AAAA,UAET,qBAAkB;AAAA,UAClB,WAAW;AAAA,UACX,cAAc;AAAA;AAAA,QAHT;AAAA,MAIP;AAAA,IAEJ;AAAA,EACF;AACF;;;ACjHM,IAAAC,uBAAA;AARC,SAAS,oBAAoB,IAGb;AAHa,eAClC;AAAA,sBAAAC,oBAAmB;AAAA,EAJrB,IAGoC,IAE/B,kBAF+B,IAE/B;AAAA,IADH;AAAA;AAGA,QAAM,EAAE,SAAS,YAAY,OAAO,iBAAiB,IAAI;AAEzD,MAAI,QAAQ,gBAAgB,KAAK,cAAc,kBAAkB;AAC/D,WACE;AAAA,MAACA;AAAA,MAAA;AAAA,QAEC,qBAAkB;AAAA,QAClB,SAAS;AAAA,QACT,WAAW;AAAA,QACX,cAAc;AAAA;AAAA,MAJT;AAAA,IAKP;AAAA,EAEJ,OAGK;AACH,WAAO;AAAA,EACT;AACF;;;ACxBA,IAAAC,qBAA+D;AA0BnD,IAAAC,uBAAA;AAvBL,SAAS,wBAAwB,IAGjB;AAHiB,eACtC;AAAA,sBAAAC,oBAAmB;AAAA,EALrB,IAIwC,IAEnC,kBAFmC,IAEnC;AAAA,IADH;AAAA;AAGA,QAAM,EAAE,oBAAoB,QAAI,sCAAkB;AAClD,QAAM,EAAE,SAAS,YAAY,OAAO,iBAAiB,IAAI;AAEzD,MAAI,QAAQ,oBAAoB,GAAG;AACjC,QAAI;AAEJ,QAAI,oBAAoB,YAAY,MAAM;AACxC,eACE,oBAAoB,QAAQ,oBAC1B,GAAG,QAAQ,aAAa,QAAQ,UAClC,KAAK,oBAAoB,QAAQ,oBAAoB,GAAG,QAAQ,kBAAkB;AAAA,IACtF;AAEA,QAAI,QAAQ;AAEV,UAAI,OAAO,WAAW,UAAU;AAE9B,YAAI,oBAAoB,YAAY;AAClC,iBACE;AAAA,YAACA;AAAA,YAAA;AAAA,cACC,SAAS;AAAA,cACT,SAAS;AAAA,cACT,qBAAkB;AAAA,cAElB,WAAW;AAAA,cACX,cAAc;AAAA;AAAA,YAFT;AAAA,UAGP;AAAA,QAEJ,OAEK;AACH,iBAAO;AAAA,QACT;AAAA,MACF,OAEK;AACH,cAAM,QAAQ,QAAQ;AAEtB,YAAI,SAAS,QAAQ,SAAS,eAAe;AAE7C,cAAM,WAAW,OAAO;AAAA,UACtB;AAAA,UACA;AAAA,UACA,UAAU,QAAQ;AAAA,QACpB,CAAC;AAGD,YAAI,CAAC,YAAY,WAAW,YAAY;AACtC,iBAAO;AAAA,QACT;AAEA,YAAI,CAAC,YAAY,oBAAoB,YAAY;AAC/C,iBACE;AAAA,YAACA;AAAA,YAAA;AAAA,cACC,qBAAkB;AAAA,cAElB,SAAS;AAAA,cACT,WAAW;AAAA,cACX,cAAc;AAAA;AAAA,YAHT;AAAA,UAIP;AAAA,QAEJ,WAAW,CAAC,UAAU;AACpB,iBAAO;AAAA,QACT;AAEA,YAAI,OAAO,aAAa,UAAU;AAChC,iBACE;AAAA,YAACA;AAAA,YAAA;AAAA,cACC,SAAS;AAAA,cACT,SAAS;AAAA,cACT,WAAW;AAAA,cACX,cAAc;AAAA,cACd,qBAAkB;AAAA;AAAA,YACb;AAAA,UACP;AAAA,QAEJ,OAAO;AACL,iBACE;AAAA,YAACA;AAAA,YAAA;AAAA,cACC,SAAS;AAAA,cACT,qBAAkB;AAAA,cAElB,WAAW;AAAA,cACX,cAAc;AAAA,cACd,cAAc;AAAA;AAAA,YAHT;AAAA,UAIP;AAAA,QAEJ;AAAA,MACF;AAAA,IACF,WAES,CAAC,cAAc,CAAC,kBAAkB;AAEzC,aAAO;AAAA,IACT,OAAO;AAEL,aACE;AAAA,QAACA;AAAA,QAAA;AAAA,UACC,SAAS;AAAA,UACT,WAAW;AAAA,UACX,cAAc;AAAA,UACd,qBAAkB;AAAA;AAAA,QACb;AAAA,MACP;AAAA,IAEJ;AAAA,EACF;AACF;;;AC3FQ,IAAAC,uBAAA;AApBD,SAAS,mBAAmB,IAIZ;AAJY,eACjC;AAAA,iBAAAC,eAAc;AAAA,IACd,kBAAAC,oBAAmB;AAAA,EANrB,IAImC,IAG9B,kBAH8B,IAG9B;AAAA,IAFH;AAAA,IACA;AAAA;AAGA,QAAM;AAAA,IACJ;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF,IAAI;AAEJ,MAAI,QAAQ,eAAe,GAAG;AAC5B,UAAM,YAAY,QAAQ,QAAQ,iBAAiB,QAAQ;AAC3D,UAAM,iBACJ,8CAAC,SAAI,WAAU,mBACb;AAAA,MAAC;AAAA;AAAA,QACC,KAAK;AAAA,QACL,KAAI;AAAA,QACJ,OAAO,EAAE,UAAU,QAAQ,WAAW,SAAS,cAAc,MAAM;AAAA;AAAA,IACrE,GACF;AAGF,QAAI,QAAQ,SAAS,QAAQ;AAC3B,aACE;AAAA,QAACD;AAAA,QAAA;AAAA,UAEC,qBAAkB;AAAA,UAClB,SAAQ;AAAA,UACR,SAAS;AAAA,UACT,cAAc;AAAA;AAAA,QAJT;AAAA,MAKP;AAAA,IAEJ,WAAW,QAAQ,SAAS,aAAa;AACvC,aACE;AAAA,QAACC;AAAA,QAAA;AAAA,UAEC,qBAAkB;AAAA,UAClB,SAAQ;AAAA,UACR,SAAS;AAAA,UACT,cAAc;AAAA,UACd,WAAW,cAAc,oBAAoB,CAAC,QAAQ;AAAA,UACtD,cAAc,cAAc,oBAAoB,CAAC,CAAC,QAAQ;AAAA,UAC1D;AAAA,UACA,cAAc,MAAM,6CAAe,QAAQ;AAAA,UAC3C;AAAA,UACA;AAAA,UACA;AAAA;AAAA,QAXK;AAAA,MAYP;AAAA,IAEJ;AAAA,EACF;AAEA,SAAO;AACT;;;ACDA,IAAAC,iBAAmD;AACnD,IAAAC,sBAKO;;;ACpEP,IAAAC,qBAKO;AAGP,IAAAC,iBAA6C;AAC7C,IAAAC,6BAAmC;AAqBC,IAAAC,uBAAA;AAX7B,SAAS,WAAW,EAAE,OAAO,SAAS,SAAS,UAAU,GAAqB;AACnF,SACE;AAAA,IAAC;AAAA;AAAA,MACC,UAAU;AAAA,MACV,SAAS,CAAC,MAAM;AACd,UAAE,eAAe;AACjB,gBAAQ;AAAA,MACV;AAAA,MACA,WAAW,cAAc,UAAU,uBAAuB;AAAA,MAC1D,gBAAa;AAAA,MAEZ,oBAAU,mBAAmB,8CAAC,UAAM,iBAAM;AAAA;AAAA,EAC7C;AAEJ;AAEO,IAAM,oBAAoB,CAC/B,SACA,6BACA,uBACA,uBACG;AACH,QAAM,kBAAkB,mBAAmB;AAE3C,QAAM,QAAQ,KAAK;AAAA,IACjB,OAAO,OAAO,QAAQ,OAAO,EAAE,IAAI,CAAC,YAAY;AAAA,MAC9C,MAAM,OAAO;AAAA,MACb,aAAa,OAAO;AAAA,MACpB,YAAY,KAAK,cAAU,6CAA6B,OAAO,UAAU,CAAC;AAAA,IAC5E,EAAE;AAAA,EACJ;AAEA,QAAM,iBAA0C,CAAC;AAEjD,aAAW,UAAU,OAAO,OAAO,2BAA2B,GAAG;AAC/D,QAAI;AACF,YAAM,+BACJ,OAAO,mBAAmB,IACtB,iBAAiB,OAAO,sHAExB,mBAAmB,OAAO,sBAAsB,OAAO;AAE7D,YAAM,SAAS,UAAM,4BAAQ;AAAA,QAC3B;AAAA,QACA,cACE;AAAA,QACF,MACE,OAAO,eACP,SACA,+BACA,0BAEA,QACA;AAAA,QACF,aAAa,8CAAmB;AAAA,QAChC,YAAY;AAAA,UACV;AAAA,YACE,MAAM;AAAA,YACN,MAAM;AAAA,YACN,YAAY;AAAA,cACV;AAAA,gBACE,MAAM;AAAA,gBACN,aACE;AAAA,gBACF,MAAM;AAAA,cACR;AAAA,cACA;AAAA,gBACE,MAAM;AAAA,gBACN,aACE;AAAA,gBACF,MAAM;AAAA,cACR;AAAA,YACF;AAAA,UACF;AAAA,QACF;AAAA,QACA,SAAS;AAAA,UACP,UAAU;AAAA,UACV,UAAU;AAAA,QACZ;AAAA,QACA,aAAa,mDAAiB;AAAA,QAC9B,QAAQ,CAAC,EAAE,QAAQ,KAAK,MAAM;AAC5B,gBAAM,cAAc,KAAK,eAAe,CAAC;AACzC,gBAAM,iBAA0C,CAAC;AACjD,mBAAS,IAAI,GAAG,IAAI,YAAY,QAAQ,KAAK;AAE3C,gBAAI,OAAO,mBAAmB,UAAa,KAAK,OAAO,gBAAgB;AACrE;AAAA,YACF;AACA,kBAAM,EAAE,OAAO,QAAQ,IAAI,YAAY,CAAC;AAGxC,kBAAM,UAAU,KAAK,YAAY,SAAS,KAAK,WAAW;AAE1D,2BAAe,KAAK;AAAA,cAClB;AAAA,cACA;AAAA,cACA;AAAA,cACA,WAAW,OAAO;AAAA,YACpB,CAAC;AAAA,UACH;AACA,gCAAsB,CAAC,GAAG,gBAAgB,GAAG,cAAc,CAAC;AAAA,QAC9D;AAAA,MACF,CAAC;AACD,qBAAe,KAAK,GAAG,OAAO,WAAW;AAAA,IAC3C,SAAS,OAAP;AACA,cAAQ,MAAM,6BAA6B,KAAK;AAAA,IAClD;AAAA,EACF;AAEA,MAAI,mBAAmB,YAAY,iBAAiB;AAClD,uBAAmB,UAAU;AAAA,EAC/B;AACF;;;AD5DA,IAAAC,6BAAyD;AACzD,IAAAC,iBAAyB;AAWzB,IAAAC,sBAAkD;;;AExD1C,IAAAC,uBAAA;AAnBD,IAAM,mBAAoD,CAAC;AAAA,EAChE;AAAA,EACA;AAAA,EACA,YAAY;AACd,MAAM;AACJ,MAAI,OAAO,WAAW;AAAG,WAAO;AAEhC,SACE;AAAA,IAAC;AAAA;AAAA,MACC,WAAW,8BAA8B;AAAA,MACzC,OAAO;AAAA,QACL,SAAS;AAAA,QACT,UAAU;AAAA,QACV,KAAK;AAAA,QACL,QAAQ;AAAA,QACR,SAAS;AAAA,MACX;AAAA,MAEC,iBAAO,IAAI,CAAC,OAAO,UAClB;AAAA,QAAC;AAAA;AAAA,UAEC,WAAU;AAAA,UACV,OAAO;AAAA,YACL,UAAU;AAAA,YACV,SAAS;AAAA,YACT,OAAO;AAAA,YACP,QAAQ;AAAA,YACR,cAAc;AAAA,YACd,UAAU;AAAA,UACZ;AAAA,UAGA;AAAA;AAAA,cAAC;AAAA;AAAA,gBACC,KAAK,QAAQ,MAAM,sBAAsB,MAAM;AAAA,gBAC/C,KAAK,kBAAkB,QAAQ;AAAA,gBAC/B,OAAO;AAAA,kBACL,OAAO;AAAA,kBACP,QAAQ;AAAA,kBACR,WAAW;AAAA,gBACb;AAAA;AAAA,YACF;AAAA,YACA;AAAA,cAAC;AAAA;AAAA,gBACC,SAAS,MAAM,cAAc,KAAK;AAAA,gBAClC,WAAU;AAAA,gBACV,OAAO;AAAA,kBACL,UAAU;AAAA,kBACV,KAAK;AAAA,kBACL,OAAO;AAAA,kBACP,YAAY;AAAA,kBACZ,OAAO;AAAA,kBACP,QAAQ;AAAA,kBACR,cAAc;AAAA,kBACd,OAAO;AAAA,kBACP,QAAQ;AAAA,kBACR,SAAS;AAAA,kBACT,YAAY;AAAA,kBACZ,gBAAgB;AAAA,kBAChB,QAAQ;AAAA,kBACR,UAAU;AAAA,kBACV,SAAS;AAAA,gBACX;AAAA,gBACD;AAAA;AAAA,YAED;AAAA;AAAA;AAAA,QA3CK;AAAA,MA4CP,CACD;AAAA;AAAA,EACH;AAEJ;;;ACpEQ,IAAAC,uBAAA;AAJD,SAAS,YAAY,EAAE,aAAa,kBAAkB,GAA+B;AAC1F,SACE,8CAAC,SAAI,WAAU,eACZ,sBAAY,IAAI,CAAC,YAAY,UAC5B;AAAA,IAAC;AAAA;AAAA,MAEC,OAAO,WAAW;AAAA,MAClB,SAAS,WAAW;AAAA,MACpB,SAAS,WAAW;AAAA,MACpB,WAAW,WAAW;AAAA,MACtB,SAAS,MAAM,kBAAkB,WAAW,OAAO;AAAA;AAAA,IAL9C;AAAA,EAMP,CACD,GACH;AAEJ;;;AHmeU,IAAAC,uBAAA;AA/MH,SAAS,YAAY;AAAA,EAC1B;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA,UAAAC,YAAW;AAAA,EACX,mBAAAC,qBAAoB;AAAA,EACpB,8BAAAC,gCAA+B;AAAA,EAC/B,yBAAAC,2BAA0B;AAAA,EAC1B,qBAAAC,uBAAsB;AAAA,EACtB,oBAAAC,sBAAqB;AAAA,EACrB,wBAAwB;AAAA,EACxB,OAAAC,SAAQ;AAAA,EACR;AAAA,EACA;AAAA,EACA;AAAA,EACA,kBAAAC,oBAAmB;AAAA,EACnB,aAAAC,eAAc;AAAA,EACd;AAAA,EACA,kBAAkB;AACpB,GAAqB;AACnB,QAAM,EAAE,wBAAwB,oBAAoB,QAAI,uCAAkB;AAC1E,QAAM,CAAC,gBAAgB,iBAAiB,QAAI,yBAA6B,CAAC,CAAC;AAC3E,QAAM,mBAAe,uBAAyB,IAAI;AAGlD,gCAAU,MAAM;AACd,QAAI,CAAC;AAAqB;AAE1B,UAAM,cAAc,CAAO,MAAsB;AA3UrD;AA4UM,YAAM,SAAS,EAAE;AACjB,UAAI,GAAC,YAAO,kBAAP,mBAAsB,UAAU,SAAS;AAAoB;AAElE,YAAM,QAAQ,MAAM,OAAK,OAAE,kBAAF,mBAAiB,UAAS,CAAC,CAAC;AACrD,YAAM,aAAa,MAAM,OAAO,CAAC,SAAS,KAAK,KAAK,WAAW,QAAQ,CAAC;AAExE,UAAI,WAAW,WAAW;AAAG;AAE7B,QAAE,eAAe;AAEjB,YAAM,gBAA+C,WAAW,IAAI,CAAC,SAAS;AAC5E,cAAM,OAAO,KAAK,UAAU;AAC5B,YAAI,CAAC;AAAM,iBAAO,QAAQ,QAAQ,IAAI;AAEtC,eAAO,IAAI,QAA4B,CAAC,SAAS,WAAW;AAC1D,gBAAM,SAAS,IAAI,WAAW;AAC9B,iBAAO,SAAS,CAACC,OAAM;AA5VjC,gBAAAC,KAAAC;AA6VY,kBAAM,gBAAgBA,OAAAD,MAAAD,GAAE,WAAF,gBAAAC,IAAU,WAAV,gBAAAC,IAA6B,MAAM,KAAK;AAC9D,gBAAI,cAAc;AAChB,sBAAQ;AAAA,gBACN,aAAa,KAAK;AAAA,gBAClB,OAAO;AAAA,cACT,CAAC;AAAA,YACH,OAAO;AACL,sBAAQ,IAAI;AAAA,YACd;AAAA,UACF;AACA,iBAAO,UAAU;AACjB,iBAAO,cAAc,IAAI;AAAA,QAC3B,CAAC;AAAA,MACH,CAAC;AAED,UAAI;AACF,cAAM,gBAAgB,MAAM,QAAQ,IAAI,aAAa,GAAG,OAAO,CAAC,QAAQ,QAAQ,IAAI;AACpF,0BAAkB,CAAC,SAAS,CAAC,GAAG,MAAM,GAAG,YAAY,CAAC;AAAA,MACxD,SAAS,OAAP;AAEA,gBAAQ,MAAM,mCAAmC,KAAK;AAAA,MACxD;AAAA,IACF;AAEA,aAAS,iBAAiB,SAAS,WAAW;AAC9C,WAAO,MAAM,SAAS,oBAAoB,SAAS,WAAW;AAAA,EAChE,GAAG,CAAC,mBAAmB,CAAC;AAExB,gCAAU,MAAM;AACd,QAAI,EAAC,iEAAwB,SAAQ;AACnC,0BAAoB,gBAAgB,EAAE;AACtC;AAAA,IACF;AAUA,UAAM,iCAAiC;AAAA,MACrC;AAAA,MACA;AAAA,MACA,GAAG,uBAAuB,IAAI,CAAC,gBAAgB,KAAK,aAAa;AAAA,IACnE;AAEA,YAAQ,IAAI,kCAAkC,8BAA8B;AAE5E,wBAAoB,+BAA+B,KAAK,IAAI,KAAK,EAAE;AAAA,EACrE,GAAG,CAAC,cAAc,sBAAsB,CAAC;AAEzC,QAAM;AAAA,IACJ;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF,IAAI;AAAA,IACF;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF;AAGA,QAAM,oBAAoB,CAAC,SAAiB;AAC1C,UAAM,SAAS;AACf,sBAAkB,CAAC,CAAC;AACpB,QAAI,aAAa,SAAS;AACxB,mBAAa,QAAQ,QAAQ;AAAA,IAC/B;AAEA,WAAO,YAAY,MAAM,MAAM;AAAA,EACjC;AAEA,QAAM,cAAc,eAAAC,QAAM,WAAW,WAAW;AAChD,QAAM,YAAY,cAAc,YAAY,OAAO;AAEnD,QAAM,mBAAmB,CAAC,cAAsB;AAC9C,QAAI,cAAc;AAChB,mBAAa,SAAS;AAAA,IACxB;AAEA,mBAAe,SAAS;AAAA,EAC1B;AAEA,QAAM,aAAa,CAAC,YAAoB;AACtC,QAAI,QAAQ;AACV,aAAO,OAAO;AAAA,IAChB;AAAA,EACF;AAEA,QAAM,oBAAoB,CAAO,UAA+C;AAC9E,QAAI,CAAC,MAAM,OAAO,SAAS,MAAM,OAAO,MAAM,WAAW,GAAG;AAC1D;AAAA,IACF;AAEA,UAAM,QAAQ,MAAM,KAAK,MAAM,OAAO,KAAK,EAAE,OAAO,CAAC,SAAS,KAAK,KAAK,WAAW,QAAQ,CAAC;AAC5F,QAAI,MAAM,WAAW;AAAG;AAExB,UAAM,mBAAmB,MAAM,IAAI,CAAC,SAAS;AAC3C,aAAO,IAAI,QAAgD,CAAC,SAAS,WAAW;AAC9E,cAAM,SAAS,IAAI,WAAW;AAC9B,eAAO,SAAS,CAAC,MAAM;AAxc/B;AAycU,gBAAM,iBAAgB,aAAE,WAAF,mBAAU,WAAV,mBAA6B,MAAM,KAAK,OAAM;AACpE,cAAI,cAAc;AAChB,oBAAQ;AAAA,cACN,aAAa,KAAK;AAAA,cAClB,OAAO;AAAA,YACT,CAAC;AAAA,UACH;AAAA,QACF;AACA,eAAO,UAAU;AACjB,eAAO,cAAc,IAAI;AAAA,MAC3B,CAAC;AAAA,IACH,CAAC;AAED,QAAI;AACF,YAAM,eAAe,MAAM,QAAQ,IAAI,gBAAgB;AACvD,wBAAkB,CAAC,SAAS,CAAC,GAAG,MAAM,GAAG,YAAY,CAAC;AAAA,IACxD,SAAS,OAAP;AAEA,cAAQ,MAAM,wBAAwB,KAAK;AAAA,IAC7C;AAAA,EACF;AAEA,QAAM,sBAAsB,CAAC,UAAkB;AAC7C,sBAAkB,CAAC,SAAS,KAAK,OAAO,CAAC,GAAG,MAAM,MAAM,KAAK,CAAC;AAAA,EAChE;AAEA,SACE,+CAAC,sBAAmB,OAAc,QAAgB,WAChD;AAAA;AAAA,MAACZ;AAAA,MAAA;AAAA,QACC,kBAAkBO;AAAA,QAClB,aAAaC;AAAA,QACb,mBAAmBP;AAAA,QACnB,8BAA8BC;AAAA,QAC9B,yBAAyBC;AAAA,QACzB,qBAAqBC;AAAA,QACrB,oBAAoBC;AAAA,QACpB,UAAU;AAAA,QACV,YAAY;AAAA,QACZ,cAAc;AAAA,QACd,QAAQ;AAAA,QACR;AAAA,QACA;AAAA,QACA;AAAA,QAEC,6BAAmB,SAAS,KAC3B;AAAA,UAAC;AAAA;AAAA,YACC,mBAAmB;AAAA,YACnB,aAAa;AAAA;AAAA,QACf;AAAA;AAAA,IAEJ;AAAA,IAEC,uBACC,gFACE;AAAA,oDAAC,oBAAiB,QAAQ,gBAAgB,eAAe,qBAAqB;AAAA,MAC9E;AAAA,QAAC;AAAA;AAAA,UACC,MAAK;AAAA,UACL,UAAQ;AAAA,UACR,KAAK;AAAA,UACL,UAAU;AAAA,UACV,QAAQ;AAAA,UACR,OAAO,EAAE,SAAS,OAAO;AAAA;AAAA,MAC3B;AAAA,OACF;AAAA,IAGF;AAAA,MAACC;AAAA,MAAA;AAAA,QACC,YAAY;AAAA,QACZ,QAAQ;AAAA,QACR;AAAA,QACA,QAAQ;AAAA,QACR,UAAU,sBAAsB,MAAG;AAhhB3C;AAghB8C,oCAAa,YAAb,mBAAsB;AAAA,YAAU;AAAA;AAAA,IACxE;AAAA,KACF;AAEJ;AAEO,SAAS,mBAAmB;AAAA,EACjC;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF,GAKG;AACD,QAAM,cAAc,eAAAM,QAAM,WAAW,WAAW;AAChD,MAAI,CAAC,aAAa;AAChB,WACE,8CAAC,uBAAoB,OAAc,QAAgB,MAAM,MAAM,SAAS,MAAM;AAAA,IAAC,GAC7E,wDAAC,SAAI,WAAW,kBAAkB,gCAAa,MAAO,UAAS,GACjE;AAAA,EAEJ;AACA,SAAO,+EAAG,UAAS;AACrB;AAEA,IAAM,+BAA+B;AAE9B,IAAM,sBAAsB,CACjC,mBACA,cACA,iBACA,kBACA,qBACG;AApjBL;AAqjBE,QAAM;AAAA,IACJ;AAAA,IACA;AAAA,IACA,gBAAgB;AAAA,IAChB,gBAAgB;AAAA,IAChB;AAAA,IACA;AAAA,EACF,QAAI,oCAAe;AAAA,IACjB,QAAI,yBAAS;AAAA,IACb;AAAA,EACF,CAAC;AAED,QAAM,CAAC,oBAAoB,qBAAqB,QAAI,yBAAkC,CAAC,CAAC;AACxF,QAAM,oCAAgC,uBAA+B,IAAI;AACzE,QAAM,uBAAmB,uBAAY;AAErC,QAAM,mBAAmB,MAAM;AArkBjC,QAAAF;AAskBI,KAAAA,MAAA,8BAA8B,YAA9B,gBAAAA,IAAuC;AACvC,kCAA8B,UAAU;AAAA,EAC1C;AAEA,QAAM,qBAAiB,uCAAkB;AACzC,QAAM,sBAAkB,+CAA0B;AAClD,QAAM,UAAU,kCAAK,iBAAmB;AAExC,gCAAU,MAAM;AACd,iDAAe;AAEf,qBAAiB;AAEjB,qBAAiB,UAAU;AAAA,MACzB,MAAM;AACJ,YAAI,CAAC,aAAa,OAAO,KAAK,QAAQ,2BAA2B,EAAE,WAAW,GAAG;AAC/E,wCAA8B,UAAU,IAAI,gBAAgB;AAC5D;AAAA,YACE;AAAA,YACA,QAAQ;AAAA,YACR;AAAA,YACA;AAAA,UACF;AAAA,QACF;AAAA,MACF;AAAA,MACA,mBAAmB,UAAU,IAAI,IAAI;AAAA,IACvC;AAEA,WAAO,MAAM;AACX,mBAAa,iBAAiB,OAAO;AAAA,IACvC;AAAA,EACF,GAAG;AAAA,IACD;AAAA,IACA,QAAQ;AAAA;AAAA;AAAA,IAGR,gBAAgB,UAAU;AAAA,EAC5B,CAAC;AAED,QAAM,cAAc,CAClB,gBACA,gBACG;AAEH,UAAM,SAAS,eAAe,CAAC;AAE/B,qBAAiB;AACjB,0BAAsB,CAAC,CAAC;AAExB,QAAI,eAA+B;AAGnC,QAAI,eAAe,KAAK,EAAE,SAAS,GAAG;AACpC,YAAM,cAAc,IAAI,uCAAY;AAAA,QAClC,SAAS;AAAA,QACT,MAAM,gCAAK;AAAA,MACb,CAAC;AAED,UAAI,iBAAiB;AACnB,YAAI;AAEF,gBAAM,gBAAgB,cAAc;AAAA,QACtC,SAAS,OAAP;AACA,kBAAQ,MAAM,6BAA6B,KAAK;AAAA,QAClD;AAAA,MACF;AAEA,YAAM,cAAc,aAAa,EAAE,UAAU,OAAO,WAAW,EAAE,CAAC;AAElE,UAAI,CAAC,cAAc;AACjB,uBAAe;AAAA,MACjB;AAAA,IACF;AAGA,QAAI,OAAO,SAAS,GAAG;AACrB,eAAS,IAAI,GAAG,IAAI,OAAO,QAAQ,KAAK;AACtC,cAAM,eAAe,IAAI,wCAAa;AAAA,UACpC,QAAQ,OAAO,CAAC,EAAE,YAAY,QAAQ,UAAU,EAAE;AAAA,UAClD,OAAO,OAAO,CAAC,EAAE;AAAA,UACjB,MAAM,gCAAK;AAAA,QACb,CAAC;AACD,cAAM,cAAc,cAAc,EAAE,UAAU,MAAM,OAAO,SAAS,EAAE,CAAC;AACvE,YAAI,CAAC,cAAc;AACjB,yBAAe;AAAA,QACjB;AAAA,MACF;AAAA,IACF;AAEA,QAAI,CAAC,cAAc;AAEjB,aAAO,IAAI,uCAAY,EAAE,SAAS,IAAI,MAAM,gCAAK,KAAK,CAAC;AAAA,IACzD;AAIA,WAAO;AAAA,EACT;AAEA,QAAM,WAAW;AACjB,QAAM,EAAE,YAAY,IAAI;AACxB,QAAM,oBAAmB,oBAAe,iBAAf,mBAA6B;AACtD,QAAM,sBAAsB,CAAO,SAAwB;AACzD,QAAI,eAAe,cAAc;AAC/B,qBAAe,gBAAgB,iCAC1B,eAAe,eADW;AAAA,QAE7B,UAAU;AAAA,QACV,UAAU;AAAA,MACZ,EAAC;AACD,qBAAe,iBAAiB,CAAC,oBAAoB;AACnD,eAAO,iCACF,kBADE;AAAA,UAEL,CAAC,eAAe,aAAc,SAAS,GAAG,iCACrC,gBAAgB,eAAe,aAAc,SAAS,IADjB;AAAA,YAExC,UAAU;AAAA,YACV,UAAU;AAAA,YACV,OAAO;AAAA,UACT;AAAA,QACF;AAAA,MACF,CAAC;AAAA,IACH;AAAA,EACF;AACA,QAAM,kBAAkB,CAAO,SAAwB;AACrD,QAAI,eAAe,cAAc;AAC/B,gBAAM;AAAA,QACJ,eAAe,aAAa;AAAA,QAC5B;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACF;AAAA,IACF;AAAA,EACF;AACA,QAAM,mBAAmB,MAAM;AAC7B,QAAI,eAAe,cAAc;AAC/B,yCAAU,eAAe,aAAa,WAAW,OAAO;AAAA,IAC1D;AAAA,EACF;AACA,QAAM,uBAAuB,CAAC,UAAe;AAC3C,QAAI,eAAe,cAAc;AAC/B,qBAAe,iBAAiB,CAAC,oBAAoB;AACnD,eAAO,iCACF,kBADE;AAAA,UAEL,CAAC,eAAe,aAAc,SAAS,GAAG;AAAA,YACxC;AAAA,UACF;AAAA,QACF;AAAA,MACF,CAAC;AAAA,IACH;AAAA,EACF;AAEA,WAAS,iBAAiB;AACxB,QAAI,kBAAkB;AACpB,uBAAiB;AAAA,QACf;AAAA,QACA;AAAA,QACA,gBAAgB;AAAA,QAChB;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACF,CAAC;AAAA,IACH,OAAO;AACL,4BAAsB;AAAA,IACxB;AAAA,EACF;AACA,WAAS,eAAe,WAAmB;AACzC,QAAI,kBAAkB;AACpB,uBAAiB;AAAA,QACf;AAAA,QACA;AAAA,QACA,gBAAgB;AAAA,QAChB;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACF,CAAC;AAAA,IACH,OAAO;AACL,4BAAsB,SAAS;AAAA,IACjC;AAAA,EACF;AAEA,SAAO;AAAA,IACL;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF;AACF;;;A3BnqBQ,IAAAG,uBAAA;AAzCD,IAAM,eAAe,CAAC,OA6BJ;AA7BI,eAC3B;AAAA;AAAA,IACA,cAAc;AAAA,IACd,sBAAsB;AAAA,IACtB,mBAAmB;AAAA,IACnB;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA,WAAW;AAAA,IACX;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA,QAAAC,UAAS;AAAA,IACT,QAAAC,UAAS;AAAA,IACT,QAAAC,UAAS;AAAA,IACT,UAAAC,YAAW;AAAA,IACX,OAAAC,SAAQ;AAAA,IACR,kBAAAC,oBAAmB;AAAA,IACnB,aAAAC,eAAc;AAAA,IACd;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EAtFF,IA2D6B,IA4BxB,kBA5BwB,IA4BxB;AAAA,IA3BH;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA;AAGA,QAAM,CAAC,WAAW,YAAY,IAAI,eAAAC,QAAM,SAAS,WAAW;AAE5D,QAAM,UAAU,CAAC,SAAkB;AACjC,2CAAY;AACZ,iBAAa,IAAI;AAAA,EACnB;AAEA,SACE,+CAAC,uBAAoB,OAAc,QAAgB,MAAM,WAAW,SACjE;AAAA;AAAA,IACD,+CAAC,SAAI,WACH;AAAA,oDAACN,SAAA,EAAO;AAAA,MACR;AAAA,QAACD;AAAA,QAAA;AAAA,UACC;AAAA,UACA;AAAA,UACA;AAAA,UAEA;AAAA,0DAACE,SAAA,EAAO;AAAA,YACR;AAAA,cAAC;AAAA,+CACK,QADL;AAAA,gBAEC;AAAA,gBACA;AAAA,gBACA;AAAA,gBACA;AAAA,gBACA;AAAA,gBACA;AAAA,gBACA,UAAUC;AAAA,gBACV,OAAOC;AAAA,gBACP,kBAAkBC;AAAA,gBAClB,aAAaC;AAAA,gBACb;AAAA,gBACA;AAAA,gBACA;AAAA,gBACA;AAAA,gBACA;AAAA;AAAA,YACF;AAAA;AAAA;AAAA,MACF;AAAA,OACF;AAAA,KACF;AAEJ;", "names": ["import_react", "import_jsx_runtime", "React", "import_react", "import_jsx_runtime", "React", "import_jsx_runtime", "import_react_core", "import_react_core", "import_react", "import_jsx_runtime", "CheckIcon", "import_react", "import_react", "import_jsx_runtime", "CloseIcon", "import_jsx_runtime", "CloseIcon", "import_jsx_runtime", "CheckIcon", "import_jsx_runtime", "import_react", "import_react_core", "import_jsx_runtime", "RenderTextMessage", "RenderActionExecutionMessage", "RenderAgentStateMessage", "RenderResultMessage", "RenderImageMessage", "AssistantM<PERSON><PERSON>", "UserMessage", "import_react", "import_react", "import_jsx_runtime", "import_react_core", "import_react", "import_react_core", "import_jsx_runtime", "import_jsx_runtime", "_a", "import_jsx_runtime", "import_react", "import_react", "React", "import_jsx_runtime", "Syntax<PERSON><PERSON><PERSON><PERSON>", "import_jsx_runtime", "ReactMarkdown", "remarkGfm", "remarkMath", "rehypeRaw", "import_react", "import_jsx_runtime", "import_jsx_runtime", "UserMessage", "AssistantM<PERSON><PERSON>", "import_runtime_client_gql", "import_react_core", "import_jsx_runtime", "AssistantM<PERSON><PERSON>", "import_jsx_runtime", "AssistantM<PERSON><PERSON>", "import_react_core", "import_jsx_runtime", "AssistantM<PERSON><PERSON>", "import_jsx_runtime", "UserMessage", "AssistantM<PERSON><PERSON>", "import_react", "import_react_core", "import_react_core", "import_shared", "import_runtime_client_gql", "import_jsx_runtime", "import_runtime_client_gql", "import_shared", "import_react_core", "import_jsx_runtime", "import_jsx_runtime", "import_jsx_runtime", "Messages", "RenderTextMessage", "RenderActionExecutionMessage", "RenderAgentStateMessage", "RenderResultMessage", "RenderImageMessage", "Input", "AssistantM<PERSON><PERSON>", "UserMessage", "e", "_a", "_b", "React", "import_jsx_runtime", "Window", "<PERSON><PERSON>", "Header", "Messages", "Input", "AssistantM<PERSON><PERSON>", "UserMessage", "React"]}