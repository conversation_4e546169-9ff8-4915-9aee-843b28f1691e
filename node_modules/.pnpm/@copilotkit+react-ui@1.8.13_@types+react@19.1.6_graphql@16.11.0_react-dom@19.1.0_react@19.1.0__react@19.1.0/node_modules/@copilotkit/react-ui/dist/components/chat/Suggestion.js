"use strict";
var __defProp = Object.defineProperty;
var __getOwnPropDesc = Object.getOwnPropertyDescriptor;
var __getOwnPropNames = Object.getOwnPropertyNames;
var __hasOwnProp = Object.prototype.hasOwnProperty;
var __export = (target, all) => {
  for (var name in all)
    __defProp(target, name, { get: all[name], enumerable: true });
};
var __copyProps = (to, from, except, desc) => {
  if (from && typeof from === "object" || typeof from === "function") {
    for (let key of __getOwnPropNames(from))
      if (!__hasOwnProp.call(to, key) && key !== except)
        __defProp(to, key, { get: () => from[key], enumerable: !(desc = __getOwnPropDesc(from, key)) || desc.enumerable });
  }
  return to;
};
var __toCommonJS = (mod) => __copyProps(__defProp({}, "__esModule", { value: true }), mod);
var __async = (__this, __arguments, generator) => {
  return new Promise((resolve, reject) => {
    var fulfilled = (value) => {
      try {
        step(generator.next(value));
      } catch (e) {
        reject(e);
      }
    };
    var rejected = (value) => {
      try {
        step(generator.throw(value));
      } catch (e) {
        reject(e);
      }
    };
    var step = (x) => x.done ? resolve(x.value) : Promise.resolve(x.value).then(fulfilled, rejected);
    step((generator = generator.apply(__this, __arguments)).next());
  });
};

// src/components/chat/Suggestion.tsx
var Suggestion_exports = {};
__export(Suggestion_exports, {
  Suggestion: () => Suggestion,
  reloadSuggestions: () => reloadSuggestions
});
module.exports = __toCommonJS(Suggestion_exports);
var import_react_core = require("@copilotkit/react-core");

// src/components/chat/Icons.tsx
var import_jsx_runtime = require("react/jsx-runtime");
var SmallSpinnerIcon = /* @__PURE__ */ (0, import_jsx_runtime.jsx)("span", { className: "copilotKitSpinner", style: { width: "13px", height: "13px" } });

// src/components/chat/Suggestion.tsx
var import_shared = require("@copilotkit/shared");
var import_runtime_client_gql = require("@copilotkit/runtime-client-gql");
var import_jsx_runtime2 = require("react/jsx-runtime");
function Suggestion({ title, onClick, partial, className }) {
  return /* @__PURE__ */ (0, import_jsx_runtime2.jsx)(
    "button",
    {
      disabled: partial,
      onClick: (e) => {
        e.preventDefault();
        onClick();
      },
      className: className || (partial ? "suggestion loading" : "suggestion"),
      "data-test-id": "suggestion",
      children: partial ? SmallSpinnerIcon : /* @__PURE__ */ (0, import_jsx_runtime2.jsx)("span", { children: title })
    }
  );
}
var reloadSuggestions = (context, chatSuggestionConfiguration, setCurrentSuggestions, abortControllerRef) => __async(void 0, null, function* () {
  const abortController = abortControllerRef.current;
  const tools = JSON.stringify(
    Object.values(context.actions).map((action) => ({
      name: action.name,
      description: action.description,
      jsonSchema: JSON.stringify((0, import_shared.actionParametersToJsonSchema)(action.parameters))
    }))
  );
  const allSuggestions = [];
  for (const config of Object.values(chatSuggestionConfiguration)) {
    try {
      const numOfSuggestionsInstructions = config.minSuggestions === 0 ? `Produce up to ${config.maxSuggestions} suggestions. If there are no highly relevant suggestions you can think of, provide an empty array.` : `Produce between ${config.minSuggestions} and ${config.maxSuggestions} suggestions.`;
      const result = yield (0, import_react_core.extract)({
        context,
        instructions: "Suggest what the user could say next. Provide clear, highly relevant suggestions. Do not literally suggest function calls. ",
        data: config.instructions + "\n\n" + numOfSuggestionsInstructions + "\n\nAvailable tools: " + tools + "\n\n",
        requestType: import_runtime_client_gql.CopilotRequestType.Task,
        parameters: [
          {
            name: "suggestions",
            type: "object[]",
            attributes: [
              {
                name: "title",
                description: "The title of the suggestion. This is shown as a button and should be short.",
                type: "string"
              },
              {
                name: "message",
                description: "The message to send when the suggestion is clicked. This should be a clear, complete sentence and will be sent as an instruction to the AI.",
                type: "string"
              }
            ]
          }
        ],
        include: {
          messages: true,
          readable: true
        },
        abortSignal: abortController == null ? void 0 : abortController.signal,
        stream: ({ status, args }) => {
          const suggestions = args.suggestions || [];
          const newSuggestions = [];
          for (let i = 0; i < suggestions.length; i++) {
            if (config.maxSuggestions !== void 0 && i >= config.maxSuggestions) {
              break;
            }
            const { title, message } = suggestions[i];
            const partial = i == suggestions.length - 1 && status !== "complete";
            newSuggestions.push({
              title,
              message,
              partial,
              className: config.className
            });
          }
          setCurrentSuggestions([...allSuggestions, ...newSuggestions]);
        }
      });
      allSuggestions.push(...result.suggestions);
    } catch (error) {
      console.error("Error loading suggestions", error);
    }
  }
  if (abortControllerRef.current === abortController) {
    abortControllerRef.current = null;
  }
});
// Annotate the CommonJS export names for ESM import in node:
0 && (module.exports = {
  Suggestion,
  reloadSuggestions
});
//# sourceMappingURL=Suggestion.js.map