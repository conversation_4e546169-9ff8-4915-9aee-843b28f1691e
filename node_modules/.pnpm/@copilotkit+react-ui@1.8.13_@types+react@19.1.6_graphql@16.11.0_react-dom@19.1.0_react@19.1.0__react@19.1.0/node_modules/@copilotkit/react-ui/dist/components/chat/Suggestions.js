"use strict";
var __defProp = Object.defineProperty;
var __getOwnPropDesc = Object.getOwnPropertyDescriptor;
var __getOwnPropNames = Object.getOwnPropertyNames;
var __hasOwnProp = Object.prototype.hasOwnProperty;
var __export = (target, all) => {
  for (var name in all)
    __defProp(target, name, { get: all[name], enumerable: true });
};
var __copyProps = (to, from, except, desc) => {
  if (from && typeof from === "object" || typeof from === "function") {
    for (let key of __getOwnPropNames(from))
      if (!__hasOwnProp.call(to, key) && key !== except)
        __defProp(to, key, { get: () => from[key], enumerable: !(desc = __getOwnPropDesc(from, key)) || desc.enumerable });
  }
  return to;
};
var __toCommonJS = (mod) => __copyProps(__defProp({}, "__esModule", { value: true }), mod);

// src/components/chat/Suggestions.tsx
var Suggestions_exports = {};
__export(Suggestions_exports, {
  Suggestions: () => Suggestions
});
module.exports = __toCommonJS(Suggestions_exports);

// src/components/chat/Suggestion.tsx
var import_react_core = require("@copilotkit/react-core");

// src/components/chat/Icons.tsx
var import_jsx_runtime = require("react/jsx-runtime");
var SmallSpinnerIcon = /* @__PURE__ */ (0, import_jsx_runtime.jsx)("span", { className: "copilotKitSpinner", style: { width: "13px", height: "13px" } });

// src/components/chat/Suggestion.tsx
var import_shared = require("@copilotkit/shared");
var import_runtime_client_gql = require("@copilotkit/runtime-client-gql");
var import_jsx_runtime2 = require("react/jsx-runtime");
function Suggestion({ title, onClick, partial, className }) {
  return /* @__PURE__ */ (0, import_jsx_runtime2.jsx)(
    "button",
    {
      disabled: partial,
      onClick: (e) => {
        e.preventDefault();
        onClick();
      },
      className: className || (partial ? "suggestion loading" : "suggestion"),
      "data-test-id": "suggestion",
      children: partial ? SmallSpinnerIcon : /* @__PURE__ */ (0, import_jsx_runtime2.jsx)("span", { children: title })
    }
  );
}

// src/components/chat/Suggestions.tsx
var import_jsx_runtime3 = require("react/jsx-runtime");
function Suggestions({ suggestions, onSuggestionClick }) {
  return /* @__PURE__ */ (0, import_jsx_runtime3.jsx)("div", { className: "suggestions", children: suggestions.map((suggestion, index) => /* @__PURE__ */ (0, import_jsx_runtime3.jsx)(
    Suggestion,
    {
      title: suggestion.title,
      message: suggestion.message,
      partial: suggestion.partial,
      className: suggestion.className,
      onClick: () => onSuggestionClick(suggestion.message)
    },
    index
  )) });
}
// Annotate the CommonJS export names for ESM import in node:
0 && (module.exports = {
  Suggestions
});
//# sourceMappingURL=Suggestions.js.map