{"version": 3, "sources": ["../src/types.ts", "../src/events.ts"], "sourcesContent": ["import { z } from \"zod\";\n\nexport const FunctionCallSchema = z.object({\n  name: z.string(),\n  arguments: z.string(),\n});\n\nexport const ToolCallSchema = z.object({\n  id: z.string(),\n  type: z.literal(\"function\"),\n  function: FunctionCallSchema,\n});\n\nexport const BaseMessageSchema = z.object({\n  id: z.string(),\n  role: z.string(),\n  content: z.string().optional(),\n  name: z.string().optional(),\n});\n\nexport const DeveloperMessageSchema = BaseMessageSchema.extend({\n  role: z.literal(\"developer\"),\n  content: z.string(),\n});\n\nexport const SystemMessageSchema = BaseMessageSchema.extend({\n  role: z.literal(\"system\"),\n  content: z.string(),\n});\n\nexport const AssistantMessageSchema = BaseMessageSchema.extend({\n  role: z.literal(\"assistant\"),\n  content: z.string().optional(),\n  toolCalls: z.array(ToolCallSchema).optional(),\n});\n\nexport const UserMessageSchema = BaseMessageSchema.extend({\n  role: z.literal(\"user\"),\n  content: z.string(),\n});\n\nexport const ToolMessageSchema = z.object({\n  id: z.string(),\n  content: z.string(),\n  role: z.literal(\"tool\"),\n  toolCallId: z.string(),\n});\n\nexport const MessageSchema = z.discriminatedUnion(\"role\", [\n  DeveloperMessageSchema,\n  SystemMessageSchema,\n  AssistantMessageSchema,\n  UserMessageSchema,\n  ToolMessageSchema,\n]);\n\nexport const RoleSchema = z.union([\n  z.literal(\"developer\"),\n  z.literal(\"system\"),\n  z.literal(\"assistant\"),\n  z.literal(\"user\"),\n  z.literal(\"tool\"),\n]);\n\nexport const ContextSchema = z.object({\n  description: z.string(),\n  value: z.string(),\n});\n\nexport const ToolSchema = z.object({\n  name: z.string(),\n  description: z.string(),\n  parameters: z.any(), // JSON Schema for the tool parameters\n});\n\nexport const RunAgentInputSchema = z.object({\n  threadId: z.string(),\n  runId: z.string(),\n  state: z.any(),\n  messages: z.array(MessageSchema),\n  tools: z.array(ToolSchema),\n  context: z.array(ContextSchema),\n  forwardedProps: z.any(),\n});\n\nexport const StateSchema = z.any();\n\nexport type ToolCall = z.infer<typeof ToolCallSchema>;\nexport type FunctionCall = z.infer<typeof FunctionCallSchema>;\nexport type DeveloperMessage = z.infer<typeof DeveloperMessageSchema>;\nexport type SystemMessage = z.infer<typeof SystemMessageSchema>;\nexport type AssistantMessage = z.infer<typeof AssistantMessageSchema>;\nexport type UserMessage = z.infer<typeof UserMessageSchema>;\nexport type ToolMessage = z.infer<typeof ToolMessageSchema>;\nexport type Message = z.infer<typeof MessageSchema>;\nexport type Context = z.infer<typeof ContextSchema>;\nexport type Tool = z.infer<typeof ToolSchema>;\nexport type RunAgentInput = z.infer<typeof RunAgentInputSchema>;\nexport type State = z.infer<typeof StateSchema>;\nexport type Role = z.infer<typeof RoleSchema>;\n\nexport class AGUIError extends Error {\n  constructor(message: string) {\n    super(message);\n  }\n}\n", "import { z } from \"zod\";\nimport { MessageSchema, StateSchema } from \"./types\";\n\nexport enum EventType {\n  TEXT_MESSAGE_START = \"TEXT_MESSAGE_START\",\n  TEXT_MESSAGE_CONTENT = \"TEXT_MESSAGE_CONTENT\",\n  TEXT_MESSAGE_END = \"TEXT_MESSAGE_END\",\n  TEXT_MESSAGE_CHUNK = \"TEXT_MESSAGE_CHUNK\",\n  TOOL_CALL_START = \"TOOL_CALL_START\",\n  TOOL_CALL_ARGS = \"TOOL_CALL_ARGS\",\n  TOOL_CALL_END = \"TOOL_CALL_END\",\n  TOOL_CALL_CHUNK = \"TOOL_CALL_CHUNK\",\n  STATE_SNAPSHOT = \"STATE_SNAPSHOT\",\n  STATE_DELTA = \"STATE_DELTA\",\n  MESSAGES_SNAPSHOT = \"MESSAGES_SNAPSHOT\",\n  RAW = \"RAW\",\n  CUSTOM = \"CUSTOM\",\n  RUN_STARTED = \"RUN_STARTED\",\n  RUN_FINISHED = \"RUN_FINISHED\",\n  RUN_ERROR = \"RUN_ERROR\",\n  STEP_STARTED = \"STEP_STARTED\",\n  STEP_FINISHED = \"STEP_FINISHED\",\n}\n\nconst BaseEventSchema = z.object({\n  type: z.nativeEnum(EventType),\n  timestamp: z.number().optional(),\n  rawEvent: z.any().optional(),\n});\n\nexport const RunStartedSchema = BaseEventSchema.extend({\n  type: z.literal(EventType.RUN_STARTED),\n  threadId: z.string(),\n  runId: z.string(),\n});\n\nexport const RunFinishedSchema = BaseEventSchema.extend({\n  type: z.literal(EventType.RUN_FINISHED),\n  threadId: z.string(),\n  runId: z.string(),\n});\n\nexport const RunErrorSchema = BaseEventSchema.extend({\n  type: z.literal(EventType.RUN_ERROR),\n  message: z.string(),\n  code: z.string().optional(),\n});\n\nexport const StepStartedSchema = BaseEventSchema.extend({\n  type: z.literal(EventType.STEP_STARTED),\n  stepName: z.string(),\n});\n\nexport const StepFinishedSchema = BaseEventSchema.extend({\n  type: z.literal(EventType.STEP_FINISHED),\n  stepName: z.string(),\n});\n\nexport const TextMessageStartEventSchema = BaseEventSchema.extend({\n  type: z.literal(EventType.TEXT_MESSAGE_START),\n  messageId: z.string(),\n  role: z.literal(\"assistant\"),\n});\n\nexport const TextMessageContentEventSchema = BaseEventSchema.extend({\n  type: z.literal(EventType.TEXT_MESSAGE_CONTENT),\n  messageId: z.string(),\n  delta: z.string().refine((s) => s.length > 0, \"Delta must not be an empty string\"),\n});\n\nexport const TextMessageEndEventSchema = BaseEventSchema.extend({\n  type: z.literal(EventType.TEXT_MESSAGE_END),\n  messageId: z.string(),\n});\n\nexport const TextMessageChunkEventSchema = BaseEventSchema.extend({\n  type: z.literal(EventType.TEXT_MESSAGE_CHUNK),\n  messageId: z.string().optional(),\n  role: z.literal(\"assistant\").optional(),\n  delta: z.string().optional(),\n});\n\nexport const ToolCallStartEventSchema = BaseEventSchema.extend({\n  type: z.literal(EventType.TOOL_CALL_START),\n  toolCallId: z.string(),\n  toolCallName: z.string(),\n  parentMessageId: z.string().optional(),\n});\n\nexport const ToolCallArgsEventSchema = BaseEventSchema.extend({\n  type: z.literal(EventType.TOOL_CALL_ARGS),\n  toolCallId: z.string(),\n  delta: z.string(),\n});\n\nexport const ToolCallEndEventSchema = BaseEventSchema.extend({\n  type: z.literal(EventType.TOOL_CALL_END),\n  toolCallId: z.string(),\n});\n\nexport const ToolCallChunkEventSchema = BaseEventSchema.extend({\n  type: z.literal(EventType.TOOL_CALL_CHUNK),\n  toolCallId: z.string().optional(),\n  toolCallName: z.string().optional(),\n  parentMessageId: z.string().optional(),\n  delta: z.string().optional(),\n});\n\nexport const StateSnapshotEventSchema = BaseEventSchema.extend({\n  type: z.literal(EventType.STATE_SNAPSHOT),\n  snapshot: StateSchema,\n});\n\nexport const StateDeltaEventSchema = BaseEventSchema.extend({\n  type: z.literal(EventType.STATE_DELTA),\n  delta: z.array(z.any()), // JSON Patch (RFC 6902)\n});\n\nexport const MessagesSnapshotEventSchema = BaseEventSchema.extend({\n  type: z.literal(EventType.MESSAGES_SNAPSHOT),\n  messages: z.array(MessageSchema),\n});\n\nexport const RawEventSchema = BaseEventSchema.extend({\n  type: z.literal(EventType.RAW),\n  event: z.any(),\n  source: z.string().optional(),\n});\n\nexport const CustomEventSchema = BaseEventSchema.extend({\n  type: z.literal(EventType.CUSTOM),\n  name: z.string(),\n  value: z.any(),\n});\n\nexport const RunStartedEventSchema = BaseEventSchema.extend({\n  type: z.literal(EventType.RUN_STARTED),\n  threadId: z.string(),\n  runId: z.string(),\n});\n\nexport const RunFinishedEventSchema = BaseEventSchema.extend({\n  type: z.literal(EventType.RUN_FINISHED),\n  threadId: z.string(),\n  runId: z.string(),\n});\n\nexport const RunErrorEventSchema = BaseEventSchema.extend({\n  type: z.literal(EventType.RUN_ERROR),\n  message: z.string(),\n  code: z.string().optional(),\n});\n\nexport const StepStartedEventSchema = BaseEventSchema.extend({\n  type: z.literal(EventType.STEP_STARTED),\n  stepName: z.string(),\n});\n\nexport const StepFinishedEventSchema = BaseEventSchema.extend({\n  type: z.literal(EventType.STEP_FINISHED),\n  stepName: z.string(),\n});\n\nexport const EventSchemas = z.discriminatedUnion(\"type\", [\n  TextMessageStartEventSchema,\n  TextMessageContentEventSchema,\n  TextMessageEndEventSchema,\n  TextMessageChunkEventSchema,\n  ToolCallStartEventSchema,\n  ToolCallArgsEventSchema,\n  ToolCallEndEventSchema,\n  ToolCallChunkEventSchema,\n  StateSnapshotEventSchema,\n  StateDeltaEventSchema,\n  MessagesSnapshotEventSchema,\n  RawEventSchema,\n  CustomEventSchema,\n  RunStartedEventSchema,\n  RunFinishedEventSchema,\n  RunErrorEventSchema,\n  StepStartedEventSchema,\n  StepFinishedEventSchema,\n]);\n\nexport type BaseEvent = z.infer<typeof BaseEventSchema>;\nexport type TextMessageStartEvent = z.infer<typeof TextMessageStartEventSchema>;\nexport type TextMessageContentEvent = z.infer<typeof TextMessageContentEventSchema>;\nexport type TextMessageEndEvent = z.infer<typeof TextMessageEndEventSchema>;\nexport type TextMessageChunkEvent = z.infer<typeof TextMessageChunkEventSchema>;\nexport type ToolCallStartEvent = z.infer<typeof ToolCallStartEventSchema>;\nexport type ToolCallArgsEvent = z.infer<typeof ToolCallArgsEventSchema>;\nexport type ToolCallEndEvent = z.infer<typeof ToolCallEndEventSchema>;\nexport type ToolCallChunkEvent = z.infer<typeof ToolCallChunkEventSchema>;\nexport type StateSnapshotEvent = z.infer<typeof StateSnapshotEventSchema>;\nexport type StateDeltaEvent = z.infer<typeof StateDeltaEventSchema>;\nexport type MessagesSnapshotEvent = z.infer<typeof MessagesSnapshotEventSchema>;\nexport type RawEvent = z.infer<typeof RawEventSchema>;\nexport type CustomEvent = z.infer<typeof CustomEventSchema>;\nexport type RunStartedEvent = z.infer<typeof RunStartedEventSchema>;\nexport type RunFinishedEvent = z.infer<typeof RunFinishedEventSchema>;\nexport type RunErrorEvent = z.infer<typeof RunErrorEventSchema>;\nexport type StepStartedEvent = z.infer<typeof StepStartedEventSchema>;\nexport type StepFinishedEvent = z.infer<typeof StepFinishedEventSchema>;\n"], "mappings": ";AAAA,SAAS,SAAS;AAEX,IAAM,qBAAqB,EAAE,OAAO;AAAA,EACzC,MAAM,EAAE,OAAO;AAAA,EACf,WAAW,EAAE,OAAO;AACtB,CAAC;AAEM,IAAM,iBAAiB,EAAE,OAAO;AAAA,EACrC,IAAI,EAAE,OAAO;AAAA,EACb,MAAM,EAAE,QAAQ,UAAU;AAAA,EAC1B,UAAU;AACZ,CAAC;AAEM,IAAM,oBAAoB,EAAE,OAAO;AAAA,EACxC,IAAI,EAAE,OAAO;AAAA,EACb,MAAM,EAAE,OAAO;AAAA,EACf,SAAS,EAAE,OAAO,EAAE,SAAS;AAAA,EAC7B,MAAM,EAAE,OAAO,EAAE,SAAS;AAC5B,CAAC;AAEM,IAAM,yBAAyB,kBAAkB,OAAO;AAAA,EAC7D,MAAM,EAAE,QAAQ,WAAW;AAAA,EAC3B,SAAS,EAAE,OAAO;AACpB,CAAC;AAEM,IAAM,sBAAsB,kBAAkB,OAAO;AAAA,EAC1D,MAAM,EAAE,QAAQ,QAAQ;AAAA,EACxB,SAAS,EAAE,OAAO;AACpB,CAAC;AAEM,IAAM,yBAAyB,kBAAkB,OAAO;AAAA,EAC7D,MAAM,EAAE,QAAQ,WAAW;AAAA,EAC3B,SAAS,EAAE,OAAO,EAAE,SAAS;AAAA,EAC7B,WAAW,EAAE,MAAM,cAAc,EAAE,SAAS;AAC9C,CAAC;AAEM,IAAM,oBAAoB,kBAAkB,OAAO;AAAA,EACxD,MAAM,EAAE,QAAQ,MAAM;AAAA,EACtB,SAAS,EAAE,OAAO;AACpB,CAAC;AAEM,IAAM,oBAAoB,EAAE,OAAO;AAAA,EACxC,IAAI,EAAE,OAAO;AAAA,EACb,SAAS,EAAE,OAAO;AAAA,EAClB,MAAM,EAAE,QAAQ,MAAM;AAAA,EACtB,YAAY,EAAE,OAAO;AACvB,CAAC;AAEM,IAAM,gBAAgB,EAAE,mBAAmB,QAAQ;AAAA,EACxD;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF,CAAC;AAEM,IAAM,aAAa,EAAE,MAAM;AAAA,EAChC,EAAE,QAAQ,WAAW;AAAA,EACrB,EAAE,QAAQ,QAAQ;AAAA,EAClB,EAAE,QAAQ,WAAW;AAAA,EACrB,EAAE,QAAQ,MAAM;AAAA,EAChB,EAAE,QAAQ,MAAM;AAClB,CAAC;AAEM,IAAM,gBAAgB,EAAE,OAAO;AAAA,EACpC,aAAa,EAAE,OAAO;AAAA,EACtB,OAAO,EAAE,OAAO;AAClB,CAAC;AAEM,IAAM,aAAa,EAAE,OAAO;AAAA,EACjC,MAAM,EAAE,OAAO;AAAA,EACf,aAAa,EAAE,OAAO;AAAA,EACtB,YAAY,EAAE,IAAI;AAAA;AACpB,CAAC;AAEM,IAAM,sBAAsB,EAAE,OAAO;AAAA,EAC1C,UAAU,EAAE,OAAO;AAAA,EACnB,OAAO,EAAE,OAAO;AAAA,EAChB,OAAO,EAAE,IAAI;AAAA,EACb,UAAU,EAAE,MAAM,aAAa;AAAA,EAC/B,OAAO,EAAE,MAAM,UAAU;AAAA,EACzB,SAAS,EAAE,MAAM,aAAa;AAAA,EAC9B,gBAAgB,EAAE,IAAI;AACxB,CAAC;AAEM,IAAM,cAAc,EAAE,IAAI;AAgB1B,IAAM,YAAN,cAAwB,MAAM;AAAA,EACnC,YAAY,SAAiB;AAC3B,UAAM,OAAO;AAAA,EACf;AACF;;;ACzGA,SAAS,KAAAA,UAAS;AAGX,IAAK,YAAL,kBAAKC,eAAL;AACL,EAAAA,WAAA,wBAAqB;AACrB,EAAAA,WAAA,0BAAuB;AACvB,EAAAA,WAAA,sBAAmB;AACnB,EAAAA,WAAA,wBAAqB;AACrB,EAAAA,WAAA,qBAAkB;AAClB,EAAAA,WAAA,oBAAiB;AACjB,EAAAA,WAAA,mBAAgB;AAChB,EAAAA,WAAA,qBAAkB;AAClB,EAAAA,WAAA,oBAAiB;AACjB,EAAAA,WAAA,iBAAc;AACd,EAAAA,WAAA,uBAAoB;AACpB,EAAAA,WAAA,SAAM;AACN,EAAAA,WAAA,YAAS;AACT,EAAAA,WAAA,iBAAc;AACd,EAAAA,WAAA,kBAAe;AACf,EAAAA,WAAA,eAAY;AACZ,EAAAA,WAAA,kBAAe;AACf,EAAAA,WAAA,mBAAgB;AAlBN,SAAAA;AAAA,GAAA;AAqBZ,IAAM,kBAAkBC,GAAE,OAAO;AAAA,EAC/B,MAAMA,GAAE,WAAW,SAAS;AAAA,EAC5B,WAAWA,GAAE,OAAO,EAAE,SAAS;AAAA,EAC/B,UAAUA,GAAE,IAAI,EAAE,SAAS;AAC7B,CAAC;AAEM,IAAM,mBAAmB,gBAAgB,OAAO;AAAA,EACrD,MAAMA,GAAE,QAAQ,+BAAqB;AAAA,EACrC,UAAUA,GAAE,OAAO;AAAA,EACnB,OAAOA,GAAE,OAAO;AAClB,CAAC;AAEM,IAAM,oBAAoB,gBAAgB,OAAO;AAAA,EACtD,MAAMA,GAAE,QAAQ,iCAAsB;AAAA,EACtC,UAAUA,GAAE,OAAO;AAAA,EACnB,OAAOA,GAAE,OAAO;AAClB,CAAC;AAEM,IAAM,iBAAiB,gBAAgB,OAAO;AAAA,EACnD,MAAMA,GAAE,QAAQ,2BAAmB;AAAA,EACnC,SAASA,GAAE,OAAO;AAAA,EAClB,MAAMA,GAAE,OAAO,EAAE,SAAS;AAC5B,CAAC;AAEM,IAAM,oBAAoB,gBAAgB,OAAO;AAAA,EACtD,MAAMA,GAAE,QAAQ,iCAAsB;AAAA,EACtC,UAAUA,GAAE,OAAO;AACrB,CAAC;AAEM,IAAM,qBAAqB,gBAAgB,OAAO;AAAA,EACvD,MAAMA,GAAE,QAAQ,mCAAuB;AAAA,EACvC,UAAUA,GAAE,OAAO;AACrB,CAAC;AAEM,IAAM,8BAA8B,gBAAgB,OAAO;AAAA,EAChE,MAAMA,GAAE,QAAQ,6CAA4B;AAAA,EAC5C,WAAWA,GAAE,OAAO;AAAA,EACpB,MAAMA,GAAE,QAAQ,WAAW;AAC7B,CAAC;AAEM,IAAM,gCAAgC,gBAAgB,OAAO;AAAA,EAClE,MAAMA,GAAE,QAAQ,iDAA8B;AAAA,EAC9C,WAAWA,GAAE,OAAO;AAAA,EACpB,OAAOA,GAAE,OAAO,EAAE,OAAO,CAAC,MAAM,EAAE,SAAS,GAAG,mCAAmC;AACnF,CAAC;AAEM,IAAM,4BAA4B,gBAAgB,OAAO;AAAA,EAC9D,MAAMA,GAAE,QAAQ,yCAA0B;AAAA,EAC1C,WAAWA,GAAE,OAAO;AACtB,CAAC;AAEM,IAAM,8BAA8B,gBAAgB,OAAO;AAAA,EAChE,MAAMA,GAAE,QAAQ,6CAA4B;AAAA,EAC5C,WAAWA,GAAE,OAAO,EAAE,SAAS;AAAA,EAC/B,MAAMA,GAAE,QAAQ,WAAW,EAAE,SAAS;AAAA,EACtC,OAAOA,GAAE,OAAO,EAAE,SAAS;AAC7B,CAAC;AAEM,IAAM,2BAA2B,gBAAgB,OAAO;AAAA,EAC7D,MAAMA,GAAE,QAAQ,uCAAyB;AAAA,EACzC,YAAYA,GAAE,OAAO;AAAA,EACrB,cAAcA,GAAE,OAAO;AAAA,EACvB,iBAAiBA,GAAE,OAAO,EAAE,SAAS;AACvC,CAAC;AAEM,IAAM,0BAA0B,gBAAgB,OAAO;AAAA,EAC5D,MAAMA,GAAE,QAAQ,qCAAwB;AAAA,EACxC,YAAYA,GAAE,OAAO;AAAA,EACrB,OAAOA,GAAE,OAAO;AAClB,CAAC;AAEM,IAAM,yBAAyB,gBAAgB,OAAO;AAAA,EAC3D,MAAMA,GAAE,QAAQ,mCAAuB;AAAA,EACvC,YAAYA,GAAE,OAAO;AACvB,CAAC;AAEM,IAAM,2BAA2B,gBAAgB,OAAO;AAAA,EAC7D,MAAMA,GAAE,QAAQ,uCAAyB;AAAA,EACzC,YAAYA,GAAE,OAAO,EAAE,SAAS;AAAA,EAChC,cAAcA,GAAE,OAAO,EAAE,SAAS;AAAA,EAClC,iBAAiBA,GAAE,OAAO,EAAE,SAAS;AAAA,EACrC,OAAOA,GAAE,OAAO,EAAE,SAAS;AAC7B,CAAC;AAEM,IAAM,2BAA2B,gBAAgB,OAAO;AAAA,EAC7D,MAAMA,GAAE,QAAQ,qCAAwB;AAAA,EACxC,UAAU;AACZ,CAAC;AAEM,IAAM,wBAAwB,gBAAgB,OAAO;AAAA,EAC1D,MAAMA,GAAE,QAAQ,+BAAqB;AAAA,EACrC,OAAOA,GAAE,MAAMA,GAAE,IAAI,CAAC;AAAA;AACxB,CAAC;AAEM,IAAM,8BAA8B,gBAAgB,OAAO;AAAA,EAChE,MAAMA,GAAE,QAAQ,2CAA2B;AAAA,EAC3C,UAAUA,GAAE,MAAM,aAAa;AACjC,CAAC;AAEM,IAAM,iBAAiB,gBAAgB,OAAO;AAAA,EACnD,MAAMA,GAAE,QAAQ,eAAa;AAAA,EAC7B,OAAOA,GAAE,IAAI;AAAA,EACb,QAAQA,GAAE,OAAO,EAAE,SAAS;AAC9B,CAAC;AAEM,IAAM,oBAAoB,gBAAgB,OAAO;AAAA,EACtD,MAAMA,GAAE,QAAQ,qBAAgB;AAAA,EAChC,MAAMA,GAAE,OAAO;AAAA,EACf,OAAOA,GAAE,IAAI;AACf,CAAC;AAEM,IAAM,wBAAwB,gBAAgB,OAAO;AAAA,EAC1D,MAAMA,GAAE,QAAQ,+BAAqB;AAAA,EACrC,UAAUA,GAAE,OAAO;AAAA,EACnB,OAAOA,GAAE,OAAO;AAClB,CAAC;AAEM,IAAM,yBAAyB,gBAAgB,OAAO;AAAA,EAC3D,MAAMA,GAAE,QAAQ,iCAAsB;AAAA,EACtC,UAAUA,GAAE,OAAO;AAAA,EACnB,OAAOA,GAAE,OAAO;AAClB,CAAC;AAEM,IAAM,sBAAsB,gBAAgB,OAAO;AAAA,EACxD,MAAMA,GAAE,QAAQ,2BAAmB;AAAA,EACnC,SAASA,GAAE,OAAO;AAAA,EAClB,MAAMA,GAAE,OAAO,EAAE,SAAS;AAC5B,CAAC;AAEM,IAAM,yBAAyB,gBAAgB,OAAO;AAAA,EAC3D,MAAMA,GAAE,QAAQ,iCAAsB;AAAA,EACtC,UAAUA,GAAE,OAAO;AACrB,CAAC;AAEM,IAAM,0BAA0B,gBAAgB,OAAO;AAAA,EAC5D,MAAMA,GAAE,QAAQ,mCAAuB;AAAA,EACvC,UAAUA,GAAE,OAAO;AACrB,CAAC;AAEM,IAAM,eAAeA,GAAE,mBAAmB,QAAQ;AAAA,EACvD;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF,CAAC;", "names": ["z", "EventType", "z"]}