// src/proto.ts
import { EventSchemas, EventType as EventType2 } from "@ag-ui/core";

// src/generated/events.ts
import { BinaryReader as BinaryReader4, BinaryWriter as BinaryWriter4 } from "@bufbuild/protobuf/wire";

// src/generated/google/protobuf/struct.ts
import { BinaryReader, BinaryWriter } from "@bufbuild/protobuf/wire";
function createBaseStruct() {
  return { fields: {} };
}
var Struct = {
  encode(message, writer = new BinaryWriter()) {
    Object.entries(message.fields).forEach(([key, value]) => {
      if (value !== void 0) {
        Struct_FieldsEntry.encode({ key, value }, writer.uint32(10).fork()).join();
      }
    });
    return writer;
  },
  decode(input, length) {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === void 0 ? reader.len : reader.pos + length;
    const message = createBaseStruct();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }
          const entry1 = Struct_FieldsEntry.decode(reader, reader.uint32());
          if (entry1.value !== void 0) {
            message.fields[entry1.key] = entry1.value;
          }
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },
  create(base) {
    return Struct.fromPartial(base != null ? base : {});
  },
  fromPartial(object) {
    var _a;
    const message = createBaseStruct();
    message.fields = Object.entries((_a = object.fields) != null ? _a : {}).reduce(
      (acc, [key, value]) => {
        if (value !== void 0) {
          acc[key] = value;
        }
        return acc;
      },
      {}
    );
    return message;
  },
  wrap(object) {
    const struct = createBaseStruct();
    if (object !== void 0) {
      for (const key of Object.keys(object)) {
        struct.fields[key] = object[key];
      }
    }
    return struct;
  },
  unwrap(message) {
    const object = {};
    if (message.fields) {
      for (const key of Object.keys(message.fields)) {
        object[key] = message.fields[key];
      }
    }
    return object;
  }
};
function createBaseStruct_FieldsEntry() {
  return { key: "", value: void 0 };
}
var Struct_FieldsEntry = {
  encode(message, writer = new BinaryWriter()) {
    if (message.key !== "") {
      writer.uint32(10).string(message.key);
    }
    if (message.value !== void 0) {
      Value.encode(Value.wrap(message.value), writer.uint32(18).fork()).join();
    }
    return writer;
  },
  decode(input, length) {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === void 0 ? reader.len : reader.pos + length;
    const message = createBaseStruct_FieldsEntry();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }
          message.key = reader.string();
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }
          message.value = Value.unwrap(Value.decode(reader, reader.uint32()));
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },
  create(base) {
    return Struct_FieldsEntry.fromPartial(base != null ? base : {});
  },
  fromPartial(object) {
    var _a, _b;
    const message = createBaseStruct_FieldsEntry();
    message.key = (_a = object.key) != null ? _a : "";
    message.value = (_b = object.value) != null ? _b : void 0;
    return message;
  }
};
function createBaseValue() {
  return {
    nullValue: void 0,
    numberValue: void 0,
    stringValue: void 0,
    boolValue: void 0,
    structValue: void 0,
    listValue: void 0
  };
}
var Value = {
  encode(message, writer = new BinaryWriter()) {
    if (message.nullValue !== void 0) {
      writer.uint32(8).int32(message.nullValue);
    }
    if (message.numberValue !== void 0) {
      writer.uint32(17).double(message.numberValue);
    }
    if (message.stringValue !== void 0) {
      writer.uint32(26).string(message.stringValue);
    }
    if (message.boolValue !== void 0) {
      writer.uint32(32).bool(message.boolValue);
    }
    if (message.structValue !== void 0) {
      Struct.encode(Struct.wrap(message.structValue), writer.uint32(42).fork()).join();
    }
    if (message.listValue !== void 0) {
      ListValue.encode(ListValue.wrap(message.listValue), writer.uint32(50).fork()).join();
    }
    return writer;
  },
  decode(input, length) {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === void 0 ? reader.len : reader.pos + length;
    const message = createBaseValue();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }
          message.nullValue = reader.int32();
          continue;
        }
        case 2: {
          if (tag !== 17) {
            break;
          }
          message.numberValue = reader.double();
          continue;
        }
        case 3: {
          if (tag !== 26) {
            break;
          }
          message.stringValue = reader.string();
          continue;
        }
        case 4: {
          if (tag !== 32) {
            break;
          }
          message.boolValue = reader.bool();
          continue;
        }
        case 5: {
          if (tag !== 42) {
            break;
          }
          message.structValue = Struct.unwrap(Struct.decode(reader, reader.uint32()));
          continue;
        }
        case 6: {
          if (tag !== 50) {
            break;
          }
          message.listValue = ListValue.unwrap(ListValue.decode(reader, reader.uint32()));
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },
  create(base) {
    return Value.fromPartial(base != null ? base : {});
  },
  fromPartial(object) {
    var _a, _b, _c, _d, _e, _f;
    const message = createBaseValue();
    message.nullValue = (_a = object.nullValue) != null ? _a : void 0;
    message.numberValue = (_b = object.numberValue) != null ? _b : void 0;
    message.stringValue = (_c = object.stringValue) != null ? _c : void 0;
    message.boolValue = (_d = object.boolValue) != null ? _d : void 0;
    message.structValue = (_e = object.structValue) != null ? _e : void 0;
    message.listValue = (_f = object.listValue) != null ? _f : void 0;
    return message;
  },
  wrap(value) {
    const result = createBaseValue();
    if (value === null) {
      result.nullValue = 0 /* NULL_VALUE */;
    } else if (typeof value === "boolean") {
      result.boolValue = value;
    } else if (typeof value === "number") {
      result.numberValue = value;
    } else if (typeof value === "string") {
      result.stringValue = value;
    } else if (globalThis.Array.isArray(value)) {
      result.listValue = value;
    } else if (typeof value === "object") {
      result.structValue = value;
    } else if (typeof value !== "undefined") {
      throw new globalThis.Error("Unsupported any value type: " + typeof value);
    }
    return result;
  },
  unwrap(message) {
    if (message.stringValue !== void 0) {
      return message.stringValue;
    } else if ((message == null ? void 0 : message.numberValue) !== void 0) {
      return message.numberValue;
    } else if ((message == null ? void 0 : message.boolValue) !== void 0) {
      return message.boolValue;
    } else if ((message == null ? void 0 : message.structValue) !== void 0) {
      return message.structValue;
    } else if ((message == null ? void 0 : message.listValue) !== void 0) {
      return message.listValue;
    } else if ((message == null ? void 0 : message.nullValue) !== void 0) {
      return null;
    }
    return void 0;
  }
};
function createBaseListValue() {
  return { values: [] };
}
var ListValue = {
  encode(message, writer = new BinaryWriter()) {
    for (const v of message.values) {
      Value.encode(Value.wrap(v), writer.uint32(10).fork()).join();
    }
    return writer;
  },
  decode(input, length) {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === void 0 ? reader.len : reader.pos + length;
    const message = createBaseListValue();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }
          message.values.push(Value.unwrap(Value.decode(reader, reader.uint32())));
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },
  create(base) {
    return ListValue.fromPartial(base != null ? base : {});
  },
  fromPartial(object) {
    var _a;
    const message = createBaseListValue();
    message.values = ((_a = object.values) == null ? void 0 : _a.map((e) => e)) || [];
    return message;
  },
  wrap(array) {
    const result = createBaseListValue();
    result.values = array != null ? array : [];
    return result;
  },
  unwrap(message) {
    if ((message == null ? void 0 : message.hasOwnProperty("values")) && globalThis.Array.isArray(message.values)) {
      return message.values;
    } else {
      return message;
    }
  }
};

// src/generated/patch.ts
import { BinaryReader as BinaryReader2, BinaryWriter as BinaryWriter2 } from "@bufbuild/protobuf/wire";
var JsonPatchOperationType = /* @__PURE__ */ ((JsonPatchOperationType2) => {
  JsonPatchOperationType2[JsonPatchOperationType2["ADD"] = 0] = "ADD";
  JsonPatchOperationType2[JsonPatchOperationType2["REMOVE"] = 1] = "REMOVE";
  JsonPatchOperationType2[JsonPatchOperationType2["REPLACE"] = 2] = "REPLACE";
  JsonPatchOperationType2[JsonPatchOperationType2["MOVE"] = 3] = "MOVE";
  JsonPatchOperationType2[JsonPatchOperationType2["COPY"] = 4] = "COPY";
  JsonPatchOperationType2[JsonPatchOperationType2["TEST"] = 5] = "TEST";
  JsonPatchOperationType2[JsonPatchOperationType2["UNRECOGNIZED"] = -1] = "UNRECOGNIZED";
  return JsonPatchOperationType2;
})(JsonPatchOperationType || {});
function createBaseJsonPatchOperation() {
  return { op: 0, path: "", from: void 0, value: void 0 };
}
var JsonPatchOperation = {
  encode(message, writer = new BinaryWriter2()) {
    if (message.op !== 0) {
      writer.uint32(8).int32(message.op);
    }
    if (message.path !== "") {
      writer.uint32(18).string(message.path);
    }
    if (message.from !== void 0) {
      writer.uint32(26).string(message.from);
    }
    if (message.value !== void 0) {
      Value.encode(Value.wrap(message.value), writer.uint32(34).fork()).join();
    }
    return writer;
  },
  decode(input, length) {
    const reader = input instanceof BinaryReader2 ? input : new BinaryReader2(input);
    let end = length === void 0 ? reader.len : reader.pos + length;
    const message = createBaseJsonPatchOperation();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }
          message.op = reader.int32();
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }
          message.path = reader.string();
          continue;
        }
        case 3: {
          if (tag !== 26) {
            break;
          }
          message.from = reader.string();
          continue;
        }
        case 4: {
          if (tag !== 34) {
            break;
          }
          message.value = Value.unwrap(Value.decode(reader, reader.uint32()));
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },
  create(base) {
    return JsonPatchOperation.fromPartial(base != null ? base : {});
  },
  fromPartial(object) {
    var _a, _b, _c, _d;
    const message = createBaseJsonPatchOperation();
    message.op = (_a = object.op) != null ? _a : 0;
    message.path = (_b = object.path) != null ? _b : "";
    message.from = (_c = object.from) != null ? _c : void 0;
    message.value = (_d = object.value) != null ? _d : void 0;
    return message;
  }
};

// src/generated/types.ts
import { BinaryReader as BinaryReader3, BinaryWriter as BinaryWriter3 } from "@bufbuild/protobuf/wire";
function createBaseToolCall() {
  return { id: "", type: "", function: void 0 };
}
var ToolCall = {
  encode(message, writer = new BinaryWriter3()) {
    if (message.id !== "") {
      writer.uint32(10).string(message.id);
    }
    if (message.type !== "") {
      writer.uint32(18).string(message.type);
    }
    if (message.function !== void 0) {
      ToolCall_Function.encode(message.function, writer.uint32(26).fork()).join();
    }
    return writer;
  },
  decode(input, length) {
    const reader = input instanceof BinaryReader3 ? input : new BinaryReader3(input);
    let end = length === void 0 ? reader.len : reader.pos + length;
    const message = createBaseToolCall();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }
          message.id = reader.string();
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }
          message.type = reader.string();
          continue;
        }
        case 3: {
          if (tag !== 26) {
            break;
          }
          message.function = ToolCall_Function.decode(reader, reader.uint32());
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },
  create(base) {
    return ToolCall.fromPartial(base != null ? base : {});
  },
  fromPartial(object) {
    var _a, _b;
    const message = createBaseToolCall();
    message.id = (_a = object.id) != null ? _a : "";
    message.type = (_b = object.type) != null ? _b : "";
    message.function = object.function !== void 0 && object.function !== null ? ToolCall_Function.fromPartial(object.function) : void 0;
    return message;
  }
};
function createBaseToolCall_Function() {
  return { name: "", arguments: "" };
}
var ToolCall_Function = {
  encode(message, writer = new BinaryWriter3()) {
    if (message.name !== "") {
      writer.uint32(10).string(message.name);
    }
    if (message.arguments !== "") {
      writer.uint32(18).string(message.arguments);
    }
    return writer;
  },
  decode(input, length) {
    const reader = input instanceof BinaryReader3 ? input : new BinaryReader3(input);
    let end = length === void 0 ? reader.len : reader.pos + length;
    const message = createBaseToolCall_Function();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }
          message.name = reader.string();
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }
          message.arguments = reader.string();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },
  create(base) {
    return ToolCall_Function.fromPartial(base != null ? base : {});
  },
  fromPartial(object) {
    var _a, _b;
    const message = createBaseToolCall_Function();
    message.name = (_a = object.name) != null ? _a : "";
    message.arguments = (_b = object.arguments) != null ? _b : "";
    return message;
  }
};
function createBaseMessage() {
  return { id: "", role: "", content: void 0, name: void 0, toolCalls: [], toolCallId: void 0 };
}
var Message = {
  encode(message, writer = new BinaryWriter3()) {
    if (message.id !== "") {
      writer.uint32(10).string(message.id);
    }
    if (message.role !== "") {
      writer.uint32(18).string(message.role);
    }
    if (message.content !== void 0) {
      writer.uint32(26).string(message.content);
    }
    if (message.name !== void 0) {
      writer.uint32(34).string(message.name);
    }
    for (const v of message.toolCalls) {
      ToolCall.encode(v, writer.uint32(42).fork()).join();
    }
    if (message.toolCallId !== void 0) {
      writer.uint32(50).string(message.toolCallId);
    }
    return writer;
  },
  decode(input, length) {
    const reader = input instanceof BinaryReader3 ? input : new BinaryReader3(input);
    let end = length === void 0 ? reader.len : reader.pos + length;
    const message = createBaseMessage();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }
          message.id = reader.string();
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }
          message.role = reader.string();
          continue;
        }
        case 3: {
          if (tag !== 26) {
            break;
          }
          message.content = reader.string();
          continue;
        }
        case 4: {
          if (tag !== 34) {
            break;
          }
          message.name = reader.string();
          continue;
        }
        case 5: {
          if (tag !== 42) {
            break;
          }
          message.toolCalls.push(ToolCall.decode(reader, reader.uint32()));
          continue;
        }
        case 6: {
          if (tag !== 50) {
            break;
          }
          message.toolCallId = reader.string();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },
  create(base) {
    return Message.fromPartial(base != null ? base : {});
  },
  fromPartial(object) {
    var _a, _b, _c, _d, _e, _f;
    const message = createBaseMessage();
    message.id = (_a = object.id) != null ? _a : "";
    message.role = (_b = object.role) != null ? _b : "";
    message.content = (_c = object.content) != null ? _c : void 0;
    message.name = (_d = object.name) != null ? _d : void 0;
    message.toolCalls = ((_e = object.toolCalls) == null ? void 0 : _e.map((e) => ToolCall.fromPartial(e))) || [];
    message.toolCallId = (_f = object.toolCallId) != null ? _f : void 0;
    return message;
  }
};

// src/generated/events.ts
var EventType = /* @__PURE__ */ ((EventType3) => {
  EventType3[EventType3["TEXT_MESSAGE_START"] = 0] = "TEXT_MESSAGE_START";
  EventType3[EventType3["TEXT_MESSAGE_CONTENT"] = 1] = "TEXT_MESSAGE_CONTENT";
  EventType3[EventType3["TEXT_MESSAGE_END"] = 2] = "TEXT_MESSAGE_END";
  EventType3[EventType3["TOOL_CALL_START"] = 3] = "TOOL_CALL_START";
  EventType3[EventType3["TOOL_CALL_ARGS"] = 4] = "TOOL_CALL_ARGS";
  EventType3[EventType3["TOOL_CALL_END"] = 5] = "TOOL_CALL_END";
  EventType3[EventType3["STATE_SNAPSHOT"] = 6] = "STATE_SNAPSHOT";
  EventType3[EventType3["STATE_DELTA"] = 7] = "STATE_DELTA";
  EventType3[EventType3["MESSAGES_SNAPSHOT"] = 8] = "MESSAGES_SNAPSHOT";
  EventType3[EventType3["RAW"] = 9] = "RAW";
  EventType3[EventType3["CUSTOM"] = 10] = "CUSTOM";
  EventType3[EventType3["RUN_STARTED"] = 11] = "RUN_STARTED";
  EventType3[EventType3["RUN_FINISHED"] = 12] = "RUN_FINISHED";
  EventType3[EventType3["RUN_ERROR"] = 13] = "RUN_ERROR";
  EventType3[EventType3["STEP_STARTED"] = 14] = "STEP_STARTED";
  EventType3[EventType3["STEP_FINISHED"] = 15] = "STEP_FINISHED";
  EventType3[EventType3["UNRECOGNIZED"] = -1] = "UNRECOGNIZED";
  return EventType3;
})(EventType || {});
function createBaseBaseEvent() {
  return { type: 0, timestamp: void 0, rawEvent: void 0 };
}
var BaseEvent = {
  encode(message, writer = new BinaryWriter4()) {
    if (message.type !== 0) {
      writer.uint32(8).int32(message.type);
    }
    if (message.timestamp !== void 0) {
      writer.uint32(16).int64(message.timestamp);
    }
    if (message.rawEvent !== void 0) {
      Value.encode(Value.wrap(message.rawEvent), writer.uint32(26).fork()).join();
    }
    return writer;
  },
  decode(input, length) {
    const reader = input instanceof BinaryReader4 ? input : new BinaryReader4(input);
    let end = length === void 0 ? reader.len : reader.pos + length;
    const message = createBaseBaseEvent();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }
          message.type = reader.int32();
          continue;
        }
        case 2: {
          if (tag !== 16) {
            break;
          }
          message.timestamp = longToNumber(reader.int64());
          continue;
        }
        case 3: {
          if (tag !== 26) {
            break;
          }
          message.rawEvent = Value.unwrap(Value.decode(reader, reader.uint32()));
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },
  create(base) {
    return BaseEvent.fromPartial(base != null ? base : {});
  },
  fromPartial(object) {
    var _a, _b, _c;
    const message = createBaseBaseEvent();
    message.type = (_a = object.type) != null ? _a : 0;
    message.timestamp = (_b = object.timestamp) != null ? _b : void 0;
    message.rawEvent = (_c = object.rawEvent) != null ? _c : void 0;
    return message;
  }
};
function createBaseTextMessageStartEvent() {
  return { baseEvent: void 0, messageId: "", role: void 0 };
}
var TextMessageStartEvent = {
  encode(message, writer = new BinaryWriter4()) {
    if (message.baseEvent !== void 0) {
      BaseEvent.encode(message.baseEvent, writer.uint32(10).fork()).join();
    }
    if (message.messageId !== "") {
      writer.uint32(18).string(message.messageId);
    }
    if (message.role !== void 0) {
      writer.uint32(26).string(message.role);
    }
    return writer;
  },
  decode(input, length) {
    const reader = input instanceof BinaryReader4 ? input : new BinaryReader4(input);
    let end = length === void 0 ? reader.len : reader.pos + length;
    const message = createBaseTextMessageStartEvent();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }
          message.baseEvent = BaseEvent.decode(reader, reader.uint32());
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }
          message.messageId = reader.string();
          continue;
        }
        case 3: {
          if (tag !== 26) {
            break;
          }
          message.role = reader.string();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },
  create(base) {
    return TextMessageStartEvent.fromPartial(base != null ? base : {});
  },
  fromPartial(object) {
    var _a, _b;
    const message = createBaseTextMessageStartEvent();
    message.baseEvent = object.baseEvent !== void 0 && object.baseEvent !== null ? BaseEvent.fromPartial(object.baseEvent) : void 0;
    message.messageId = (_a = object.messageId) != null ? _a : "";
    message.role = (_b = object.role) != null ? _b : void 0;
    return message;
  }
};
function createBaseTextMessageContentEvent() {
  return { baseEvent: void 0, messageId: "", delta: "" };
}
var TextMessageContentEvent = {
  encode(message, writer = new BinaryWriter4()) {
    if (message.baseEvent !== void 0) {
      BaseEvent.encode(message.baseEvent, writer.uint32(10).fork()).join();
    }
    if (message.messageId !== "") {
      writer.uint32(18).string(message.messageId);
    }
    if (message.delta !== "") {
      writer.uint32(26).string(message.delta);
    }
    return writer;
  },
  decode(input, length) {
    const reader = input instanceof BinaryReader4 ? input : new BinaryReader4(input);
    let end = length === void 0 ? reader.len : reader.pos + length;
    const message = createBaseTextMessageContentEvent();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }
          message.baseEvent = BaseEvent.decode(reader, reader.uint32());
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }
          message.messageId = reader.string();
          continue;
        }
        case 3: {
          if (tag !== 26) {
            break;
          }
          message.delta = reader.string();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },
  create(base) {
    return TextMessageContentEvent.fromPartial(base != null ? base : {});
  },
  fromPartial(object) {
    var _a, _b;
    const message = createBaseTextMessageContentEvent();
    message.baseEvent = object.baseEvent !== void 0 && object.baseEvent !== null ? BaseEvent.fromPartial(object.baseEvent) : void 0;
    message.messageId = (_a = object.messageId) != null ? _a : "";
    message.delta = (_b = object.delta) != null ? _b : "";
    return message;
  }
};
function createBaseTextMessageEndEvent() {
  return { baseEvent: void 0, messageId: "" };
}
var TextMessageEndEvent = {
  encode(message, writer = new BinaryWriter4()) {
    if (message.baseEvent !== void 0) {
      BaseEvent.encode(message.baseEvent, writer.uint32(10).fork()).join();
    }
    if (message.messageId !== "") {
      writer.uint32(18).string(message.messageId);
    }
    return writer;
  },
  decode(input, length) {
    const reader = input instanceof BinaryReader4 ? input : new BinaryReader4(input);
    let end = length === void 0 ? reader.len : reader.pos + length;
    const message = createBaseTextMessageEndEvent();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }
          message.baseEvent = BaseEvent.decode(reader, reader.uint32());
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }
          message.messageId = reader.string();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },
  create(base) {
    return TextMessageEndEvent.fromPartial(base != null ? base : {});
  },
  fromPartial(object) {
    var _a;
    const message = createBaseTextMessageEndEvent();
    message.baseEvent = object.baseEvent !== void 0 && object.baseEvent !== null ? BaseEvent.fromPartial(object.baseEvent) : void 0;
    message.messageId = (_a = object.messageId) != null ? _a : "";
    return message;
  }
};
function createBaseToolCallStartEvent() {
  return { baseEvent: void 0, toolCallId: "", toolCallName: "", parentMessageId: void 0 };
}
var ToolCallStartEvent = {
  encode(message, writer = new BinaryWriter4()) {
    if (message.baseEvent !== void 0) {
      BaseEvent.encode(message.baseEvent, writer.uint32(10).fork()).join();
    }
    if (message.toolCallId !== "") {
      writer.uint32(18).string(message.toolCallId);
    }
    if (message.toolCallName !== "") {
      writer.uint32(26).string(message.toolCallName);
    }
    if (message.parentMessageId !== void 0) {
      writer.uint32(34).string(message.parentMessageId);
    }
    return writer;
  },
  decode(input, length) {
    const reader = input instanceof BinaryReader4 ? input : new BinaryReader4(input);
    let end = length === void 0 ? reader.len : reader.pos + length;
    const message = createBaseToolCallStartEvent();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }
          message.baseEvent = BaseEvent.decode(reader, reader.uint32());
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }
          message.toolCallId = reader.string();
          continue;
        }
        case 3: {
          if (tag !== 26) {
            break;
          }
          message.toolCallName = reader.string();
          continue;
        }
        case 4: {
          if (tag !== 34) {
            break;
          }
          message.parentMessageId = reader.string();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },
  create(base) {
    return ToolCallStartEvent.fromPartial(base != null ? base : {});
  },
  fromPartial(object) {
    var _a, _b, _c;
    const message = createBaseToolCallStartEvent();
    message.baseEvent = object.baseEvent !== void 0 && object.baseEvent !== null ? BaseEvent.fromPartial(object.baseEvent) : void 0;
    message.toolCallId = (_a = object.toolCallId) != null ? _a : "";
    message.toolCallName = (_b = object.toolCallName) != null ? _b : "";
    message.parentMessageId = (_c = object.parentMessageId) != null ? _c : void 0;
    return message;
  }
};
function createBaseToolCallArgsEvent() {
  return { baseEvent: void 0, toolCallId: "", delta: "" };
}
var ToolCallArgsEvent = {
  encode(message, writer = new BinaryWriter4()) {
    if (message.baseEvent !== void 0) {
      BaseEvent.encode(message.baseEvent, writer.uint32(10).fork()).join();
    }
    if (message.toolCallId !== "") {
      writer.uint32(18).string(message.toolCallId);
    }
    if (message.delta !== "") {
      writer.uint32(26).string(message.delta);
    }
    return writer;
  },
  decode(input, length) {
    const reader = input instanceof BinaryReader4 ? input : new BinaryReader4(input);
    let end = length === void 0 ? reader.len : reader.pos + length;
    const message = createBaseToolCallArgsEvent();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }
          message.baseEvent = BaseEvent.decode(reader, reader.uint32());
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }
          message.toolCallId = reader.string();
          continue;
        }
        case 3: {
          if (tag !== 26) {
            break;
          }
          message.delta = reader.string();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },
  create(base) {
    return ToolCallArgsEvent.fromPartial(base != null ? base : {});
  },
  fromPartial(object) {
    var _a, _b;
    const message = createBaseToolCallArgsEvent();
    message.baseEvent = object.baseEvent !== void 0 && object.baseEvent !== null ? BaseEvent.fromPartial(object.baseEvent) : void 0;
    message.toolCallId = (_a = object.toolCallId) != null ? _a : "";
    message.delta = (_b = object.delta) != null ? _b : "";
    return message;
  }
};
function createBaseToolCallEndEvent() {
  return { baseEvent: void 0, toolCallId: "" };
}
var ToolCallEndEvent = {
  encode(message, writer = new BinaryWriter4()) {
    if (message.baseEvent !== void 0) {
      BaseEvent.encode(message.baseEvent, writer.uint32(10).fork()).join();
    }
    if (message.toolCallId !== "") {
      writer.uint32(18).string(message.toolCallId);
    }
    return writer;
  },
  decode(input, length) {
    const reader = input instanceof BinaryReader4 ? input : new BinaryReader4(input);
    let end = length === void 0 ? reader.len : reader.pos + length;
    const message = createBaseToolCallEndEvent();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }
          message.baseEvent = BaseEvent.decode(reader, reader.uint32());
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }
          message.toolCallId = reader.string();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },
  create(base) {
    return ToolCallEndEvent.fromPartial(base != null ? base : {});
  },
  fromPartial(object) {
    var _a;
    const message = createBaseToolCallEndEvent();
    message.baseEvent = object.baseEvent !== void 0 && object.baseEvent !== null ? BaseEvent.fromPartial(object.baseEvent) : void 0;
    message.toolCallId = (_a = object.toolCallId) != null ? _a : "";
    return message;
  }
};
function createBaseStateSnapshotEvent() {
  return { baseEvent: void 0, snapshot: void 0 };
}
var StateSnapshotEvent = {
  encode(message, writer = new BinaryWriter4()) {
    if (message.baseEvent !== void 0) {
      BaseEvent.encode(message.baseEvent, writer.uint32(10).fork()).join();
    }
    if (message.snapshot !== void 0) {
      Value.encode(Value.wrap(message.snapshot), writer.uint32(18).fork()).join();
    }
    return writer;
  },
  decode(input, length) {
    const reader = input instanceof BinaryReader4 ? input : new BinaryReader4(input);
    let end = length === void 0 ? reader.len : reader.pos + length;
    const message = createBaseStateSnapshotEvent();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }
          message.baseEvent = BaseEvent.decode(reader, reader.uint32());
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }
          message.snapshot = Value.unwrap(Value.decode(reader, reader.uint32()));
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },
  create(base) {
    return StateSnapshotEvent.fromPartial(base != null ? base : {});
  },
  fromPartial(object) {
    var _a;
    const message = createBaseStateSnapshotEvent();
    message.baseEvent = object.baseEvent !== void 0 && object.baseEvent !== null ? BaseEvent.fromPartial(object.baseEvent) : void 0;
    message.snapshot = (_a = object.snapshot) != null ? _a : void 0;
    return message;
  }
};
function createBaseStateDeltaEvent() {
  return { baseEvent: void 0, delta: [] };
}
var StateDeltaEvent = {
  encode(message, writer = new BinaryWriter4()) {
    if (message.baseEvent !== void 0) {
      BaseEvent.encode(message.baseEvent, writer.uint32(10).fork()).join();
    }
    for (const v of message.delta) {
      JsonPatchOperation.encode(v, writer.uint32(18).fork()).join();
    }
    return writer;
  },
  decode(input, length) {
    const reader = input instanceof BinaryReader4 ? input : new BinaryReader4(input);
    let end = length === void 0 ? reader.len : reader.pos + length;
    const message = createBaseStateDeltaEvent();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }
          message.baseEvent = BaseEvent.decode(reader, reader.uint32());
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }
          message.delta.push(JsonPatchOperation.decode(reader, reader.uint32()));
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },
  create(base) {
    return StateDeltaEvent.fromPartial(base != null ? base : {});
  },
  fromPartial(object) {
    var _a;
    const message = createBaseStateDeltaEvent();
    message.baseEvent = object.baseEvent !== void 0 && object.baseEvent !== null ? BaseEvent.fromPartial(object.baseEvent) : void 0;
    message.delta = ((_a = object.delta) == null ? void 0 : _a.map((e) => JsonPatchOperation.fromPartial(e))) || [];
    return message;
  }
};
function createBaseMessagesSnapshotEvent() {
  return { baseEvent: void 0, messages: [] };
}
var MessagesSnapshotEvent = {
  encode(message, writer = new BinaryWriter4()) {
    if (message.baseEvent !== void 0) {
      BaseEvent.encode(message.baseEvent, writer.uint32(10).fork()).join();
    }
    for (const v of message.messages) {
      Message.encode(v, writer.uint32(18).fork()).join();
    }
    return writer;
  },
  decode(input, length) {
    const reader = input instanceof BinaryReader4 ? input : new BinaryReader4(input);
    let end = length === void 0 ? reader.len : reader.pos + length;
    const message = createBaseMessagesSnapshotEvent();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }
          message.baseEvent = BaseEvent.decode(reader, reader.uint32());
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }
          message.messages.push(Message.decode(reader, reader.uint32()));
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },
  create(base) {
    return MessagesSnapshotEvent.fromPartial(base != null ? base : {});
  },
  fromPartial(object) {
    var _a;
    const message = createBaseMessagesSnapshotEvent();
    message.baseEvent = object.baseEvent !== void 0 && object.baseEvent !== null ? BaseEvent.fromPartial(object.baseEvent) : void 0;
    message.messages = ((_a = object.messages) == null ? void 0 : _a.map((e) => Message.fromPartial(e))) || [];
    return message;
  }
};
function createBaseRawEvent() {
  return { baseEvent: void 0, event: void 0, source: void 0 };
}
var RawEvent = {
  encode(message, writer = new BinaryWriter4()) {
    if (message.baseEvent !== void 0) {
      BaseEvent.encode(message.baseEvent, writer.uint32(10).fork()).join();
    }
    if (message.event !== void 0) {
      Value.encode(Value.wrap(message.event), writer.uint32(18).fork()).join();
    }
    if (message.source !== void 0) {
      writer.uint32(26).string(message.source);
    }
    return writer;
  },
  decode(input, length) {
    const reader = input instanceof BinaryReader4 ? input : new BinaryReader4(input);
    let end = length === void 0 ? reader.len : reader.pos + length;
    const message = createBaseRawEvent();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }
          message.baseEvent = BaseEvent.decode(reader, reader.uint32());
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }
          message.event = Value.unwrap(Value.decode(reader, reader.uint32()));
          continue;
        }
        case 3: {
          if (tag !== 26) {
            break;
          }
          message.source = reader.string();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },
  create(base) {
    return RawEvent.fromPartial(base != null ? base : {});
  },
  fromPartial(object) {
    var _a, _b;
    const message = createBaseRawEvent();
    message.baseEvent = object.baseEvent !== void 0 && object.baseEvent !== null ? BaseEvent.fromPartial(object.baseEvent) : void 0;
    message.event = (_a = object.event) != null ? _a : void 0;
    message.source = (_b = object.source) != null ? _b : void 0;
    return message;
  }
};
function createBaseCustomEvent() {
  return { baseEvent: void 0, name: "", value: void 0 };
}
var CustomEvent = {
  encode(message, writer = new BinaryWriter4()) {
    if (message.baseEvent !== void 0) {
      BaseEvent.encode(message.baseEvent, writer.uint32(10).fork()).join();
    }
    if (message.name !== "") {
      writer.uint32(18).string(message.name);
    }
    if (message.value !== void 0) {
      Value.encode(Value.wrap(message.value), writer.uint32(26).fork()).join();
    }
    return writer;
  },
  decode(input, length) {
    const reader = input instanceof BinaryReader4 ? input : new BinaryReader4(input);
    let end = length === void 0 ? reader.len : reader.pos + length;
    const message = createBaseCustomEvent();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }
          message.baseEvent = BaseEvent.decode(reader, reader.uint32());
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }
          message.name = reader.string();
          continue;
        }
        case 3: {
          if (tag !== 26) {
            break;
          }
          message.value = Value.unwrap(Value.decode(reader, reader.uint32()));
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },
  create(base) {
    return CustomEvent.fromPartial(base != null ? base : {});
  },
  fromPartial(object) {
    var _a, _b;
    const message = createBaseCustomEvent();
    message.baseEvent = object.baseEvent !== void 0 && object.baseEvent !== null ? BaseEvent.fromPartial(object.baseEvent) : void 0;
    message.name = (_a = object.name) != null ? _a : "";
    message.value = (_b = object.value) != null ? _b : void 0;
    return message;
  }
};
function createBaseRunStartedEvent() {
  return { baseEvent: void 0, threadId: "", runId: "" };
}
var RunStartedEvent = {
  encode(message, writer = new BinaryWriter4()) {
    if (message.baseEvent !== void 0) {
      BaseEvent.encode(message.baseEvent, writer.uint32(10).fork()).join();
    }
    if (message.threadId !== "") {
      writer.uint32(18).string(message.threadId);
    }
    if (message.runId !== "") {
      writer.uint32(26).string(message.runId);
    }
    return writer;
  },
  decode(input, length) {
    const reader = input instanceof BinaryReader4 ? input : new BinaryReader4(input);
    let end = length === void 0 ? reader.len : reader.pos + length;
    const message = createBaseRunStartedEvent();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }
          message.baseEvent = BaseEvent.decode(reader, reader.uint32());
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }
          message.threadId = reader.string();
          continue;
        }
        case 3: {
          if (tag !== 26) {
            break;
          }
          message.runId = reader.string();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },
  create(base) {
    return RunStartedEvent.fromPartial(base != null ? base : {});
  },
  fromPartial(object) {
    var _a, _b;
    const message = createBaseRunStartedEvent();
    message.baseEvent = object.baseEvent !== void 0 && object.baseEvent !== null ? BaseEvent.fromPartial(object.baseEvent) : void 0;
    message.threadId = (_a = object.threadId) != null ? _a : "";
    message.runId = (_b = object.runId) != null ? _b : "";
    return message;
  }
};
function createBaseRunFinishedEvent() {
  return { baseEvent: void 0, threadId: "", runId: "" };
}
var RunFinishedEvent = {
  encode(message, writer = new BinaryWriter4()) {
    if (message.baseEvent !== void 0) {
      BaseEvent.encode(message.baseEvent, writer.uint32(10).fork()).join();
    }
    if (message.threadId !== "") {
      writer.uint32(18).string(message.threadId);
    }
    if (message.runId !== "") {
      writer.uint32(26).string(message.runId);
    }
    return writer;
  },
  decode(input, length) {
    const reader = input instanceof BinaryReader4 ? input : new BinaryReader4(input);
    let end = length === void 0 ? reader.len : reader.pos + length;
    const message = createBaseRunFinishedEvent();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }
          message.baseEvent = BaseEvent.decode(reader, reader.uint32());
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }
          message.threadId = reader.string();
          continue;
        }
        case 3: {
          if (tag !== 26) {
            break;
          }
          message.runId = reader.string();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },
  create(base) {
    return RunFinishedEvent.fromPartial(base != null ? base : {});
  },
  fromPartial(object) {
    var _a, _b;
    const message = createBaseRunFinishedEvent();
    message.baseEvent = object.baseEvent !== void 0 && object.baseEvent !== null ? BaseEvent.fromPartial(object.baseEvent) : void 0;
    message.threadId = (_a = object.threadId) != null ? _a : "";
    message.runId = (_b = object.runId) != null ? _b : "";
    return message;
  }
};
function createBaseRunErrorEvent() {
  return { baseEvent: void 0, code: void 0, message: "" };
}
var RunErrorEvent = {
  encode(message, writer = new BinaryWriter4()) {
    if (message.baseEvent !== void 0) {
      BaseEvent.encode(message.baseEvent, writer.uint32(10).fork()).join();
    }
    if (message.code !== void 0) {
      writer.uint32(18).string(message.code);
    }
    if (message.message !== "") {
      writer.uint32(26).string(message.message);
    }
    return writer;
  },
  decode(input, length) {
    const reader = input instanceof BinaryReader4 ? input : new BinaryReader4(input);
    let end = length === void 0 ? reader.len : reader.pos + length;
    const message = createBaseRunErrorEvent();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }
          message.baseEvent = BaseEvent.decode(reader, reader.uint32());
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }
          message.code = reader.string();
          continue;
        }
        case 3: {
          if (tag !== 26) {
            break;
          }
          message.message = reader.string();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },
  create(base) {
    return RunErrorEvent.fromPartial(base != null ? base : {});
  },
  fromPartial(object) {
    var _a, _b;
    const message = createBaseRunErrorEvent();
    message.baseEvent = object.baseEvent !== void 0 && object.baseEvent !== null ? BaseEvent.fromPartial(object.baseEvent) : void 0;
    message.code = (_a = object.code) != null ? _a : void 0;
    message.message = (_b = object.message) != null ? _b : "";
    return message;
  }
};
function createBaseStepStartedEvent() {
  return { baseEvent: void 0, stepName: "" };
}
var StepStartedEvent = {
  encode(message, writer = new BinaryWriter4()) {
    if (message.baseEvent !== void 0) {
      BaseEvent.encode(message.baseEvent, writer.uint32(10).fork()).join();
    }
    if (message.stepName !== "") {
      writer.uint32(18).string(message.stepName);
    }
    return writer;
  },
  decode(input, length) {
    const reader = input instanceof BinaryReader4 ? input : new BinaryReader4(input);
    let end = length === void 0 ? reader.len : reader.pos + length;
    const message = createBaseStepStartedEvent();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }
          message.baseEvent = BaseEvent.decode(reader, reader.uint32());
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }
          message.stepName = reader.string();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },
  create(base) {
    return StepStartedEvent.fromPartial(base != null ? base : {});
  },
  fromPartial(object) {
    var _a;
    const message = createBaseStepStartedEvent();
    message.baseEvent = object.baseEvent !== void 0 && object.baseEvent !== null ? BaseEvent.fromPartial(object.baseEvent) : void 0;
    message.stepName = (_a = object.stepName) != null ? _a : "";
    return message;
  }
};
function createBaseStepFinishedEvent() {
  return { baseEvent: void 0, stepName: "" };
}
var StepFinishedEvent = {
  encode(message, writer = new BinaryWriter4()) {
    if (message.baseEvent !== void 0) {
      BaseEvent.encode(message.baseEvent, writer.uint32(10).fork()).join();
    }
    if (message.stepName !== "") {
      writer.uint32(18).string(message.stepName);
    }
    return writer;
  },
  decode(input, length) {
    const reader = input instanceof BinaryReader4 ? input : new BinaryReader4(input);
    let end = length === void 0 ? reader.len : reader.pos + length;
    const message = createBaseStepFinishedEvent();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }
          message.baseEvent = BaseEvent.decode(reader, reader.uint32());
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }
          message.stepName = reader.string();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },
  create(base) {
    return StepFinishedEvent.fromPartial(base != null ? base : {});
  },
  fromPartial(object) {
    var _a;
    const message = createBaseStepFinishedEvent();
    message.baseEvent = object.baseEvent !== void 0 && object.baseEvent !== null ? BaseEvent.fromPartial(object.baseEvent) : void 0;
    message.stepName = (_a = object.stepName) != null ? _a : "";
    return message;
  }
};
function createBaseTextMessageChunkEvent() {
  return { baseEvent: void 0, messageId: void 0, role: void 0, delta: void 0 };
}
var TextMessageChunkEvent = {
  encode(message, writer = new BinaryWriter4()) {
    if (message.baseEvent !== void 0) {
      BaseEvent.encode(message.baseEvent, writer.uint32(10).fork()).join();
    }
    if (message.messageId !== void 0) {
      writer.uint32(18).string(message.messageId);
    }
    if (message.role !== void 0) {
      writer.uint32(26).string(message.role);
    }
    if (message.delta !== void 0) {
      writer.uint32(34).string(message.delta);
    }
    return writer;
  },
  decode(input, length) {
    const reader = input instanceof BinaryReader4 ? input : new BinaryReader4(input);
    let end = length === void 0 ? reader.len : reader.pos + length;
    const message = createBaseTextMessageChunkEvent();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }
          message.baseEvent = BaseEvent.decode(reader, reader.uint32());
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }
          message.messageId = reader.string();
          continue;
        }
        case 3: {
          if (tag !== 26) {
            break;
          }
          message.role = reader.string();
          continue;
        }
        case 4: {
          if (tag !== 34) {
            break;
          }
          message.delta = reader.string();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },
  create(base) {
    return TextMessageChunkEvent.fromPartial(base != null ? base : {});
  },
  fromPartial(object) {
    var _a, _b, _c;
    const message = createBaseTextMessageChunkEvent();
    message.baseEvent = object.baseEvent !== void 0 && object.baseEvent !== null ? BaseEvent.fromPartial(object.baseEvent) : void 0;
    message.messageId = (_a = object.messageId) != null ? _a : void 0;
    message.role = (_b = object.role) != null ? _b : void 0;
    message.delta = (_c = object.delta) != null ? _c : void 0;
    return message;
  }
};
function createBaseToolCallChunkEvent() {
  return {
    baseEvent: void 0,
    toolCallId: void 0,
    toolCallName: void 0,
    parentMessageId: void 0,
    delta: void 0
  };
}
var ToolCallChunkEvent = {
  encode(message, writer = new BinaryWriter4()) {
    if (message.baseEvent !== void 0) {
      BaseEvent.encode(message.baseEvent, writer.uint32(10).fork()).join();
    }
    if (message.toolCallId !== void 0) {
      writer.uint32(18).string(message.toolCallId);
    }
    if (message.toolCallName !== void 0) {
      writer.uint32(26).string(message.toolCallName);
    }
    if (message.parentMessageId !== void 0) {
      writer.uint32(34).string(message.parentMessageId);
    }
    if (message.delta !== void 0) {
      writer.uint32(42).string(message.delta);
    }
    return writer;
  },
  decode(input, length) {
    const reader = input instanceof BinaryReader4 ? input : new BinaryReader4(input);
    let end = length === void 0 ? reader.len : reader.pos + length;
    const message = createBaseToolCallChunkEvent();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }
          message.baseEvent = BaseEvent.decode(reader, reader.uint32());
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }
          message.toolCallId = reader.string();
          continue;
        }
        case 3: {
          if (tag !== 26) {
            break;
          }
          message.toolCallName = reader.string();
          continue;
        }
        case 4: {
          if (tag !== 34) {
            break;
          }
          message.parentMessageId = reader.string();
          continue;
        }
        case 5: {
          if (tag !== 42) {
            break;
          }
          message.delta = reader.string();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },
  create(base) {
    return ToolCallChunkEvent.fromPartial(base != null ? base : {});
  },
  fromPartial(object) {
    var _a, _b, _c, _d;
    const message = createBaseToolCallChunkEvent();
    message.baseEvent = object.baseEvent !== void 0 && object.baseEvent !== null ? BaseEvent.fromPartial(object.baseEvent) : void 0;
    message.toolCallId = (_a = object.toolCallId) != null ? _a : void 0;
    message.toolCallName = (_b = object.toolCallName) != null ? _b : void 0;
    message.parentMessageId = (_c = object.parentMessageId) != null ? _c : void 0;
    message.delta = (_d = object.delta) != null ? _d : void 0;
    return message;
  }
};
function createBaseEvent() {
  return {
    textMessageStart: void 0,
    textMessageContent: void 0,
    textMessageEnd: void 0,
    toolCallStart: void 0,
    toolCallArgs: void 0,
    toolCallEnd: void 0,
    stateSnapshot: void 0,
    stateDelta: void 0,
    messagesSnapshot: void 0,
    raw: void 0,
    custom: void 0,
    runStarted: void 0,
    runFinished: void 0,
    runError: void 0,
    stepStarted: void 0,
    stepFinished: void 0,
    textMessageChunk: void 0,
    toolCallChunk: void 0
  };
}
var Event = {
  encode(message, writer = new BinaryWriter4()) {
    if (message.textMessageStart !== void 0) {
      TextMessageStartEvent.encode(message.textMessageStart, writer.uint32(10).fork()).join();
    }
    if (message.textMessageContent !== void 0) {
      TextMessageContentEvent.encode(message.textMessageContent, writer.uint32(18).fork()).join();
    }
    if (message.textMessageEnd !== void 0) {
      TextMessageEndEvent.encode(message.textMessageEnd, writer.uint32(26).fork()).join();
    }
    if (message.toolCallStart !== void 0) {
      ToolCallStartEvent.encode(message.toolCallStart, writer.uint32(34).fork()).join();
    }
    if (message.toolCallArgs !== void 0) {
      ToolCallArgsEvent.encode(message.toolCallArgs, writer.uint32(42).fork()).join();
    }
    if (message.toolCallEnd !== void 0) {
      ToolCallEndEvent.encode(message.toolCallEnd, writer.uint32(50).fork()).join();
    }
    if (message.stateSnapshot !== void 0) {
      StateSnapshotEvent.encode(message.stateSnapshot, writer.uint32(58).fork()).join();
    }
    if (message.stateDelta !== void 0) {
      StateDeltaEvent.encode(message.stateDelta, writer.uint32(66).fork()).join();
    }
    if (message.messagesSnapshot !== void 0) {
      MessagesSnapshotEvent.encode(message.messagesSnapshot, writer.uint32(74).fork()).join();
    }
    if (message.raw !== void 0) {
      RawEvent.encode(message.raw, writer.uint32(82).fork()).join();
    }
    if (message.custom !== void 0) {
      CustomEvent.encode(message.custom, writer.uint32(90).fork()).join();
    }
    if (message.runStarted !== void 0) {
      RunStartedEvent.encode(message.runStarted, writer.uint32(98).fork()).join();
    }
    if (message.runFinished !== void 0) {
      RunFinishedEvent.encode(message.runFinished, writer.uint32(106).fork()).join();
    }
    if (message.runError !== void 0) {
      RunErrorEvent.encode(message.runError, writer.uint32(114).fork()).join();
    }
    if (message.stepStarted !== void 0) {
      StepStartedEvent.encode(message.stepStarted, writer.uint32(122).fork()).join();
    }
    if (message.stepFinished !== void 0) {
      StepFinishedEvent.encode(message.stepFinished, writer.uint32(130).fork()).join();
    }
    if (message.textMessageChunk !== void 0) {
      TextMessageChunkEvent.encode(message.textMessageChunk, writer.uint32(138).fork()).join();
    }
    if (message.toolCallChunk !== void 0) {
      ToolCallChunkEvent.encode(message.toolCallChunk, writer.uint32(146).fork()).join();
    }
    return writer;
  },
  decode(input, length) {
    const reader = input instanceof BinaryReader4 ? input : new BinaryReader4(input);
    let end = length === void 0 ? reader.len : reader.pos + length;
    const message = createBaseEvent();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }
          message.textMessageStart = TextMessageStartEvent.decode(reader, reader.uint32());
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }
          message.textMessageContent = TextMessageContentEvent.decode(reader, reader.uint32());
          continue;
        }
        case 3: {
          if (tag !== 26) {
            break;
          }
          message.textMessageEnd = TextMessageEndEvent.decode(reader, reader.uint32());
          continue;
        }
        case 4: {
          if (tag !== 34) {
            break;
          }
          message.toolCallStart = ToolCallStartEvent.decode(reader, reader.uint32());
          continue;
        }
        case 5: {
          if (tag !== 42) {
            break;
          }
          message.toolCallArgs = ToolCallArgsEvent.decode(reader, reader.uint32());
          continue;
        }
        case 6: {
          if (tag !== 50) {
            break;
          }
          message.toolCallEnd = ToolCallEndEvent.decode(reader, reader.uint32());
          continue;
        }
        case 7: {
          if (tag !== 58) {
            break;
          }
          message.stateSnapshot = StateSnapshotEvent.decode(reader, reader.uint32());
          continue;
        }
        case 8: {
          if (tag !== 66) {
            break;
          }
          message.stateDelta = StateDeltaEvent.decode(reader, reader.uint32());
          continue;
        }
        case 9: {
          if (tag !== 74) {
            break;
          }
          message.messagesSnapshot = MessagesSnapshotEvent.decode(reader, reader.uint32());
          continue;
        }
        case 10: {
          if (tag !== 82) {
            break;
          }
          message.raw = RawEvent.decode(reader, reader.uint32());
          continue;
        }
        case 11: {
          if (tag !== 90) {
            break;
          }
          message.custom = CustomEvent.decode(reader, reader.uint32());
          continue;
        }
        case 12: {
          if (tag !== 98) {
            break;
          }
          message.runStarted = RunStartedEvent.decode(reader, reader.uint32());
          continue;
        }
        case 13: {
          if (tag !== 106) {
            break;
          }
          message.runFinished = RunFinishedEvent.decode(reader, reader.uint32());
          continue;
        }
        case 14: {
          if (tag !== 114) {
            break;
          }
          message.runError = RunErrorEvent.decode(reader, reader.uint32());
          continue;
        }
        case 15: {
          if (tag !== 122) {
            break;
          }
          message.stepStarted = StepStartedEvent.decode(reader, reader.uint32());
          continue;
        }
        case 16: {
          if (tag !== 130) {
            break;
          }
          message.stepFinished = StepFinishedEvent.decode(reader, reader.uint32());
          continue;
        }
        case 17: {
          if (tag !== 138) {
            break;
          }
          message.textMessageChunk = TextMessageChunkEvent.decode(reader, reader.uint32());
          continue;
        }
        case 18: {
          if (tag !== 146) {
            break;
          }
          message.toolCallChunk = ToolCallChunkEvent.decode(reader, reader.uint32());
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },
  create(base) {
    return Event.fromPartial(base != null ? base : {});
  },
  fromPartial(object) {
    const message = createBaseEvent();
    message.textMessageStart = object.textMessageStart !== void 0 && object.textMessageStart !== null ? TextMessageStartEvent.fromPartial(object.textMessageStart) : void 0;
    message.textMessageContent = object.textMessageContent !== void 0 && object.textMessageContent !== null ? TextMessageContentEvent.fromPartial(object.textMessageContent) : void 0;
    message.textMessageEnd = object.textMessageEnd !== void 0 && object.textMessageEnd !== null ? TextMessageEndEvent.fromPartial(object.textMessageEnd) : void 0;
    message.toolCallStart = object.toolCallStart !== void 0 && object.toolCallStart !== null ? ToolCallStartEvent.fromPartial(object.toolCallStart) : void 0;
    message.toolCallArgs = object.toolCallArgs !== void 0 && object.toolCallArgs !== null ? ToolCallArgsEvent.fromPartial(object.toolCallArgs) : void 0;
    message.toolCallEnd = object.toolCallEnd !== void 0 && object.toolCallEnd !== null ? ToolCallEndEvent.fromPartial(object.toolCallEnd) : void 0;
    message.stateSnapshot = object.stateSnapshot !== void 0 && object.stateSnapshot !== null ? StateSnapshotEvent.fromPartial(object.stateSnapshot) : void 0;
    message.stateDelta = object.stateDelta !== void 0 && object.stateDelta !== null ? StateDeltaEvent.fromPartial(object.stateDelta) : void 0;
    message.messagesSnapshot = object.messagesSnapshot !== void 0 && object.messagesSnapshot !== null ? MessagesSnapshotEvent.fromPartial(object.messagesSnapshot) : void 0;
    message.raw = object.raw !== void 0 && object.raw !== null ? RawEvent.fromPartial(object.raw) : void 0;
    message.custom = object.custom !== void 0 && object.custom !== null ? CustomEvent.fromPartial(object.custom) : void 0;
    message.runStarted = object.runStarted !== void 0 && object.runStarted !== null ? RunStartedEvent.fromPartial(object.runStarted) : void 0;
    message.runFinished = object.runFinished !== void 0 && object.runFinished !== null ? RunFinishedEvent.fromPartial(object.runFinished) : void 0;
    message.runError = object.runError !== void 0 && object.runError !== null ? RunErrorEvent.fromPartial(object.runError) : void 0;
    message.stepStarted = object.stepStarted !== void 0 && object.stepStarted !== null ? StepStartedEvent.fromPartial(object.stepStarted) : void 0;
    message.stepFinished = object.stepFinished !== void 0 && object.stepFinished !== null ? StepFinishedEvent.fromPartial(object.stepFinished) : void 0;
    message.textMessageChunk = object.textMessageChunk !== void 0 && object.textMessageChunk !== null ? TextMessageChunkEvent.fromPartial(object.textMessageChunk) : void 0;
    message.toolCallChunk = object.toolCallChunk !== void 0 && object.toolCallChunk !== null ? ToolCallChunkEvent.fromPartial(object.toolCallChunk) : void 0;
    return message;
  }
};
function longToNumber(int64) {
  const num = globalThis.Number(int64.toString());
  if (num > globalThis.Number.MAX_SAFE_INTEGER) {
    throw new globalThis.Error("Value is larger than Number.MAX_SAFE_INTEGER");
  }
  if (num < globalThis.Number.MIN_SAFE_INTEGER) {
    throw new globalThis.Error("Value is smaller than Number.MIN_SAFE_INTEGER");
  }
  return num;
}

// src/proto.ts
function toCamelCase(str) {
  return str.toLowerCase().replace(/_([a-z])/g, (_, letter) => letter.toUpperCase());
}
function encode(event) {
  const oneofField = toCamelCase(event.type);
  const { type, timestamp, rawEvent, ...rest } = event;
  if (type === EventType2.MESSAGES_SNAPSHOT) {
    rest.messages = rest.messages.map((message) => {
      const untypedMessage = message;
      if (untypedMessage.toolCalls === void 0) {
        return { ...message, toolCalls: [] };
      }
      return message;
    });
  }
  if (type === EventType2.STATE_DELTA) {
    rest.delta = rest.delta.map((operation) => ({
      ...operation,
      op: JsonPatchOperationType[operation.op.toUpperCase()]
    }));
  }
  const eventMessage = {
    [oneofField]: {
      baseEvent: {
        type: EventType[event.type],
        timestamp,
        rawEvent
      },
      ...rest
    }
  };
  return Event.encode(eventMessage).finish();
}
function decode(data) {
  var _a;
  const event = Event.decode(data);
  const decoded = Object.values(event).find((value) => value !== void 0);
  if (!decoded) {
    throw new Error("Invalid event");
  }
  decoded.type = EventType[decoded.baseEvent.type];
  decoded.timestamp = decoded.baseEvent.timestamp;
  decoded.rawEvent = decoded.baseEvent.rawEvent;
  if (decoded.type === EventType2.MESSAGES_SNAPSHOT) {
    for (const message of decoded.messages) {
      const untypedMessage = message;
      if (((_a = untypedMessage.toolCalls) == null ? void 0 : _a.length) === 0) {
        untypedMessage.toolCalls = void 0;
      }
    }
  }
  if (decoded.type === EventType2.STATE_DELTA) {
    for (const operation of decoded.delta) {
      operation.op = JsonPatchOperationType[operation.op].toLowerCase();
      Object.keys(operation).forEach((key) => {
        if (operation[key] === void 0) {
          delete operation[key];
        }
      });
    }
  }
  Object.keys(decoded).forEach((key) => {
    if (decoded[key] === void 0) {
      delete decoded[key];
    }
  });
  return EventSchemas.parse(decoded);
}

// src/index.ts
var AGUI_MEDIA_TYPE = "application/vnd.ag-ui.event+proto";
export {
  AGUI_MEDIA_TYPE,
  decode,
  encode
};
//# sourceMappingURL=index.mjs.map