{"version": 3, "sources": ["../src/index.ts", "../src/proto.ts", "../src/generated/events.ts", "../src/generated/google/protobuf/struct.ts", "../src/generated/patch.ts", "../src/generated/types.ts"], "sourcesContent": ["export { encode, decode } from \"./proto\";\n\nexport const AGUI_MEDIA_TYPE = \"application/vnd.ag-ui.event+proto\";\n", "import { BaseEvent, EventSchemas, EventType, Message } from \"@ag-ui/core\";\nimport * as protoEvents from \"./generated/events\";\nimport * as protoPatch from \"./generated/patch\";\n\nfunction toCamelCase(str: string): string {\n  return str.toLowerCase().replace(/_([a-z])/g, (_, letter) => letter.toUpperCase());\n}\n\n/**\n * Encodes an event message to a protocol buffer binary format.\n */\nexport function encode(event: BaseEvent): Uint8Array {\n  const oneofField = toCamelCase(event.type);\n  const { type, timestamp, rawEvent, ...rest } = event as any;\n\n  // since protobuf does not support optional arrays, we need to ensure that the toolCalls array is always present\n  if (type === EventType.MESSAGES_SNAPSHOT) {\n    rest.messages = rest.messages.map((message: Message) => {\n      const untypedMessage = message as any;\n      if (untypedMessage.toolCalls === undefined) {\n        return { ...message, toolCalls: [] };\n      }\n      return message;\n    });\n  }\n\n  // custom mapping for json patch operations\n  if (type === EventType.STATE_DELTA) {\n    rest.delta = rest.delta.map((operation: any) => ({\n      ...operation,\n      op: protoPatch.JsonPatchOperationType[operation.op.toUpperCase()],\n    }));\n  }\n\n  const eventMessage = {\n    [oneofField]: {\n      baseEvent: {\n        type: protoEvents.EventType[event.type as keyof typeof protoEvents.EventType],\n        timestamp,\n        rawEvent,\n      },\n      ...rest,\n    },\n  };\n  return protoEvents.Event.encode(eventMessage).finish();\n}\n\n/**\n * Decodes a protocol buffer binary format to an event message.\n * The format includes a 4-byte length prefix followed by the message.\n */\nexport function decode(data: Uint8Array): BaseEvent {\n  const event = protoEvents.Event.decode(data);\n  const decoded = Object.values(event).find((value) => value !== undefined);\n  if (!decoded) {\n    throw new Error(\"Invalid event\");\n  }\n  decoded.type = protoEvents.EventType[decoded.baseEvent.type];\n  decoded.timestamp = decoded.baseEvent.timestamp;\n  decoded.rawEvent = decoded.baseEvent.rawEvent;\n\n  // we want tool calls to be optional, so we need to remove them if they are empty\n  if (decoded.type === EventType.MESSAGES_SNAPSHOT) {\n    for (const message of (decoded as any).messages as Message[]) {\n      const untypedMessage = message as any;\n      if (untypedMessage.toolCalls?.length === 0) {\n        untypedMessage.toolCalls = undefined;\n      }\n    }\n  }\n\n  // custom mapping for json patch operations\n  if (decoded.type === EventType.STATE_DELTA) {\n    for (const operation of (decoded as any).delta) {\n      operation.op = protoPatch.JsonPatchOperationType[operation.op].toLowerCase();\n      Object.keys(operation).forEach((key) => {\n        if (operation[key] === undefined) {\n          delete operation[key];\n        }\n      });\n    }\n  }\n\n  Object.keys(decoded).forEach((key) => {\n    if (decoded[key] === undefined) {\n      delete decoded[key];\n    }\n  });\n\n  return EventSchemas.parse(decoded);\n}\n", "// Code generated by protoc-gen-ts_proto. DO NOT EDIT.\n// versions:\n//   protoc-gen-ts_proto  v2.7.0\n//   protoc               v5.29.3\n// source: events.proto\n\n/* eslint-disable */\nimport { BinaryReader, BinaryWriter } from \"@bufbuild/protobuf/wire\";\nimport { Value } from \"./google/protobuf/struct\";\nimport { JsonPatchOperation } from \"./patch\";\nimport { Message } from \"./types\";\n\nexport const protobufPackage = \"ag_ui\";\n\nexport enum EventType {\n  TEXT_MESSAGE_START = 0,\n  TEXT_MESSAGE_CONTENT = 1,\n  TEXT_MESSAGE_END = 2,\n  TOOL_CALL_START = 3,\n  TOOL_CALL_ARGS = 4,\n  TOOL_CALL_END = 5,\n  STATE_SNAPSHOT = 6,\n  STATE_DELTA = 7,\n  MESSAGES_SNAPSHOT = 8,\n  RAW = 9,\n  CUSTOM = 10,\n  RUN_STARTED = 11,\n  RUN_FINISHED = 12,\n  RUN_ERROR = 13,\n  STEP_STARTED = 14,\n  STEP_FINISHED = 15,\n  UNRECOGNIZED = -1,\n}\n\nexport interface BaseEvent {\n  type: EventType;\n  timestamp?: number | undefined;\n  rawEvent?: any | undefined;\n}\n\nexport interface TextMessageStartEvent {\n  baseEvent: BaseEvent | undefined;\n  messageId: string;\n  role?: string | undefined;\n}\n\nexport interface TextMessageContentEvent {\n  baseEvent: BaseEvent | undefined;\n  messageId: string;\n  delta: string;\n}\n\nexport interface TextMessageEndEvent {\n  baseEvent: BaseEvent | undefined;\n  messageId: string;\n}\n\nexport interface ToolCallStartEvent {\n  baseEvent: BaseEvent | undefined;\n  toolCallId: string;\n  toolCallName: string;\n  parentMessageId?: string | undefined;\n}\n\nexport interface ToolCallArgsEvent {\n  baseEvent: BaseEvent | undefined;\n  toolCallId: string;\n  delta: string;\n}\n\nexport interface ToolCallEndEvent {\n  baseEvent: BaseEvent | undefined;\n  toolCallId: string;\n}\n\nexport interface StateSnapshotEvent {\n  baseEvent: BaseEvent | undefined;\n  snapshot: any | undefined;\n}\n\nexport interface StateDeltaEvent {\n  baseEvent: BaseEvent | undefined;\n  delta: JsonPatchOperation[];\n}\n\nexport interface MessagesSnapshotEvent {\n  baseEvent: BaseEvent | undefined;\n  messages: Message[];\n}\n\nexport interface RawEvent {\n  baseEvent: BaseEvent | undefined;\n  event: any | undefined;\n  source?: string | undefined;\n}\n\nexport interface CustomEvent {\n  baseEvent: BaseEvent | undefined;\n  name: string;\n  value?: any | undefined;\n}\n\nexport interface RunStartedEvent {\n  baseEvent: BaseEvent | undefined;\n  threadId: string;\n  runId: string;\n}\n\nexport interface RunFinishedEvent {\n  baseEvent: BaseEvent | undefined;\n  threadId: string;\n  runId: string;\n}\n\nexport interface RunErrorEvent {\n  baseEvent: BaseEvent | undefined;\n  code?: string | undefined;\n  message: string;\n}\n\nexport interface StepStartedEvent {\n  baseEvent: BaseEvent | undefined;\n  stepName: string;\n}\n\nexport interface StepFinishedEvent {\n  baseEvent: BaseEvent | undefined;\n  stepName: string;\n}\n\nexport interface TextMessageChunkEvent {\n  baseEvent: BaseEvent | undefined;\n  messageId?: string | undefined;\n  role?: string | undefined;\n  delta?: string | undefined;\n}\n\nexport interface ToolCallChunkEvent {\n  baseEvent: BaseEvent | undefined;\n  toolCallId?: string | undefined;\n  toolCallName?: string | undefined;\n  parentMessageId?: string | undefined;\n  delta?: string | undefined;\n}\n\nexport interface Event {\n  textMessageStart?: TextMessageStartEvent | undefined;\n  textMessageContent?: TextMessageContentEvent | undefined;\n  textMessageEnd?: TextMessageEndEvent | undefined;\n  toolCallStart?: ToolCallStartEvent | undefined;\n  toolCallArgs?: ToolCallArgsEvent | undefined;\n  toolCallEnd?: ToolCallEndEvent | undefined;\n  stateSnapshot?: StateSnapshotEvent | undefined;\n  stateDelta?: StateDeltaEvent | undefined;\n  messagesSnapshot?: MessagesSnapshotEvent | undefined;\n  raw?: RawEvent | undefined;\n  custom?: CustomEvent | undefined;\n  runStarted?: RunStartedEvent | undefined;\n  runFinished?: RunFinishedEvent | undefined;\n  runError?: RunErrorEvent | undefined;\n  stepStarted?: StepStartedEvent | undefined;\n  stepFinished?: StepFinishedEvent | undefined;\n  textMessageChunk?: TextMessageChunkEvent | undefined;\n  toolCallChunk?: ToolCallChunkEvent | undefined;\n}\n\nfunction createBaseBaseEvent(): BaseEvent {\n  return { type: 0, timestamp: undefined, rawEvent: undefined };\n}\n\nexport const BaseEvent: MessageFns<BaseEvent> = {\n  encode(message: BaseEvent, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {\n    if (message.type !== 0) {\n      writer.uint32(8).int32(message.type);\n    }\n    if (message.timestamp !== undefined) {\n      writer.uint32(16).int64(message.timestamp);\n    }\n    if (message.rawEvent !== undefined) {\n      Value.encode(Value.wrap(message.rawEvent), writer.uint32(26).fork()).join();\n    }\n    return writer;\n  },\n\n  decode(input: BinaryReader | Uint8Array, length?: number): BaseEvent {\n    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);\n    let end = length === undefined ? reader.len : reader.pos + length;\n    const message = createBaseBaseEvent();\n    while (reader.pos < end) {\n      const tag = reader.uint32();\n      switch (tag >>> 3) {\n        case 1: {\n          if (tag !== 8) {\n            break;\n          }\n\n          message.type = reader.int32() as any;\n          continue;\n        }\n        case 2: {\n          if (tag !== 16) {\n            break;\n          }\n\n          message.timestamp = longToNumber(reader.int64());\n          continue;\n        }\n        case 3: {\n          if (tag !== 26) {\n            break;\n          }\n\n          message.rawEvent = Value.unwrap(Value.decode(reader, reader.uint32()));\n          continue;\n        }\n      }\n      if ((tag & 7) === 4 || tag === 0) {\n        break;\n      }\n      reader.skip(tag & 7);\n    }\n    return message;\n  },\n\n  create<I extends Exact<DeepPartial<BaseEvent>, I>>(base?: I): BaseEvent {\n    return BaseEvent.fromPartial(base ?? ({} as any));\n  },\n  fromPartial<I extends Exact<DeepPartial<BaseEvent>, I>>(object: I): BaseEvent {\n    const message = createBaseBaseEvent();\n    message.type = object.type ?? 0;\n    message.timestamp = object.timestamp ?? undefined;\n    message.rawEvent = object.rawEvent ?? undefined;\n    return message;\n  },\n};\n\nfunction createBaseTextMessageStartEvent(): TextMessageStartEvent {\n  return { baseEvent: undefined, messageId: \"\", role: undefined };\n}\n\nexport const TextMessageStartEvent: MessageFns<TextMessageStartEvent> = {\n  encode(message: TextMessageStartEvent, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {\n    if (message.baseEvent !== undefined) {\n      BaseEvent.encode(message.baseEvent, writer.uint32(10).fork()).join();\n    }\n    if (message.messageId !== \"\") {\n      writer.uint32(18).string(message.messageId);\n    }\n    if (message.role !== undefined) {\n      writer.uint32(26).string(message.role);\n    }\n    return writer;\n  },\n\n  decode(input: BinaryReader | Uint8Array, length?: number): TextMessageStartEvent {\n    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);\n    let end = length === undefined ? reader.len : reader.pos + length;\n    const message = createBaseTextMessageStartEvent();\n    while (reader.pos < end) {\n      const tag = reader.uint32();\n      switch (tag >>> 3) {\n        case 1: {\n          if (tag !== 10) {\n            break;\n          }\n\n          message.baseEvent = BaseEvent.decode(reader, reader.uint32());\n          continue;\n        }\n        case 2: {\n          if (tag !== 18) {\n            break;\n          }\n\n          message.messageId = reader.string();\n          continue;\n        }\n        case 3: {\n          if (tag !== 26) {\n            break;\n          }\n\n          message.role = reader.string();\n          continue;\n        }\n      }\n      if ((tag & 7) === 4 || tag === 0) {\n        break;\n      }\n      reader.skip(tag & 7);\n    }\n    return message;\n  },\n\n  create<I extends Exact<DeepPartial<TextMessageStartEvent>, I>>(base?: I): TextMessageStartEvent {\n    return TextMessageStartEvent.fromPartial(base ?? ({} as any));\n  },\n  fromPartial<I extends Exact<DeepPartial<TextMessageStartEvent>, I>>(object: I): TextMessageStartEvent {\n    const message = createBaseTextMessageStartEvent();\n    message.baseEvent = (object.baseEvent !== undefined && object.baseEvent !== null)\n      ? BaseEvent.fromPartial(object.baseEvent)\n      : undefined;\n    message.messageId = object.messageId ?? \"\";\n    message.role = object.role ?? undefined;\n    return message;\n  },\n};\n\nfunction createBaseTextMessageContentEvent(): TextMessageContentEvent {\n  return { baseEvent: undefined, messageId: \"\", delta: \"\" };\n}\n\nexport const TextMessageContentEvent: MessageFns<TextMessageContentEvent> = {\n  encode(message: TextMessageContentEvent, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {\n    if (message.baseEvent !== undefined) {\n      BaseEvent.encode(message.baseEvent, writer.uint32(10).fork()).join();\n    }\n    if (message.messageId !== \"\") {\n      writer.uint32(18).string(message.messageId);\n    }\n    if (message.delta !== \"\") {\n      writer.uint32(26).string(message.delta);\n    }\n    return writer;\n  },\n\n  decode(input: BinaryReader | Uint8Array, length?: number): TextMessageContentEvent {\n    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);\n    let end = length === undefined ? reader.len : reader.pos + length;\n    const message = createBaseTextMessageContentEvent();\n    while (reader.pos < end) {\n      const tag = reader.uint32();\n      switch (tag >>> 3) {\n        case 1: {\n          if (tag !== 10) {\n            break;\n          }\n\n          message.baseEvent = BaseEvent.decode(reader, reader.uint32());\n          continue;\n        }\n        case 2: {\n          if (tag !== 18) {\n            break;\n          }\n\n          message.messageId = reader.string();\n          continue;\n        }\n        case 3: {\n          if (tag !== 26) {\n            break;\n          }\n\n          message.delta = reader.string();\n          continue;\n        }\n      }\n      if ((tag & 7) === 4 || tag === 0) {\n        break;\n      }\n      reader.skip(tag & 7);\n    }\n    return message;\n  },\n\n  create<I extends Exact<DeepPartial<TextMessageContentEvent>, I>>(base?: I): TextMessageContentEvent {\n    return TextMessageContentEvent.fromPartial(base ?? ({} as any));\n  },\n  fromPartial<I extends Exact<DeepPartial<TextMessageContentEvent>, I>>(object: I): TextMessageContentEvent {\n    const message = createBaseTextMessageContentEvent();\n    message.baseEvent = (object.baseEvent !== undefined && object.baseEvent !== null)\n      ? BaseEvent.fromPartial(object.baseEvent)\n      : undefined;\n    message.messageId = object.messageId ?? \"\";\n    message.delta = object.delta ?? \"\";\n    return message;\n  },\n};\n\nfunction createBaseTextMessageEndEvent(): TextMessageEndEvent {\n  return { baseEvent: undefined, messageId: \"\" };\n}\n\nexport const TextMessageEndEvent: MessageFns<TextMessageEndEvent> = {\n  encode(message: TextMessageEndEvent, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {\n    if (message.baseEvent !== undefined) {\n      BaseEvent.encode(message.baseEvent, writer.uint32(10).fork()).join();\n    }\n    if (message.messageId !== \"\") {\n      writer.uint32(18).string(message.messageId);\n    }\n    return writer;\n  },\n\n  decode(input: BinaryReader | Uint8Array, length?: number): TextMessageEndEvent {\n    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);\n    let end = length === undefined ? reader.len : reader.pos + length;\n    const message = createBaseTextMessageEndEvent();\n    while (reader.pos < end) {\n      const tag = reader.uint32();\n      switch (tag >>> 3) {\n        case 1: {\n          if (tag !== 10) {\n            break;\n          }\n\n          message.baseEvent = BaseEvent.decode(reader, reader.uint32());\n          continue;\n        }\n        case 2: {\n          if (tag !== 18) {\n            break;\n          }\n\n          message.messageId = reader.string();\n          continue;\n        }\n      }\n      if ((tag & 7) === 4 || tag === 0) {\n        break;\n      }\n      reader.skip(tag & 7);\n    }\n    return message;\n  },\n\n  create<I extends Exact<DeepPartial<TextMessageEndEvent>, I>>(base?: I): TextMessageEndEvent {\n    return TextMessageEndEvent.fromPartial(base ?? ({} as any));\n  },\n  fromPartial<I extends Exact<DeepPartial<TextMessageEndEvent>, I>>(object: I): TextMessageEndEvent {\n    const message = createBaseTextMessageEndEvent();\n    message.baseEvent = (object.baseEvent !== undefined && object.baseEvent !== null)\n      ? BaseEvent.fromPartial(object.baseEvent)\n      : undefined;\n    message.messageId = object.messageId ?? \"\";\n    return message;\n  },\n};\n\nfunction createBaseToolCallStartEvent(): ToolCallStartEvent {\n  return { baseEvent: undefined, toolCallId: \"\", toolCallName: \"\", parentMessageId: undefined };\n}\n\nexport const ToolCallStartEvent: MessageFns<ToolCallStartEvent> = {\n  encode(message: ToolCallStartEvent, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {\n    if (message.baseEvent !== undefined) {\n      BaseEvent.encode(message.baseEvent, writer.uint32(10).fork()).join();\n    }\n    if (message.toolCallId !== \"\") {\n      writer.uint32(18).string(message.toolCallId);\n    }\n    if (message.toolCallName !== \"\") {\n      writer.uint32(26).string(message.toolCallName);\n    }\n    if (message.parentMessageId !== undefined) {\n      writer.uint32(34).string(message.parentMessageId);\n    }\n    return writer;\n  },\n\n  decode(input: BinaryReader | Uint8Array, length?: number): ToolCallStartEvent {\n    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);\n    let end = length === undefined ? reader.len : reader.pos + length;\n    const message = createBaseToolCallStartEvent();\n    while (reader.pos < end) {\n      const tag = reader.uint32();\n      switch (tag >>> 3) {\n        case 1: {\n          if (tag !== 10) {\n            break;\n          }\n\n          message.baseEvent = BaseEvent.decode(reader, reader.uint32());\n          continue;\n        }\n        case 2: {\n          if (tag !== 18) {\n            break;\n          }\n\n          message.toolCallId = reader.string();\n          continue;\n        }\n        case 3: {\n          if (tag !== 26) {\n            break;\n          }\n\n          message.toolCallName = reader.string();\n          continue;\n        }\n        case 4: {\n          if (tag !== 34) {\n            break;\n          }\n\n          message.parentMessageId = reader.string();\n          continue;\n        }\n      }\n      if ((tag & 7) === 4 || tag === 0) {\n        break;\n      }\n      reader.skip(tag & 7);\n    }\n    return message;\n  },\n\n  create<I extends Exact<DeepPartial<ToolCallStartEvent>, I>>(base?: I): ToolCallStartEvent {\n    return ToolCallStartEvent.fromPartial(base ?? ({} as any));\n  },\n  fromPartial<I extends Exact<DeepPartial<ToolCallStartEvent>, I>>(object: I): ToolCallStartEvent {\n    const message = createBaseToolCallStartEvent();\n    message.baseEvent = (object.baseEvent !== undefined && object.baseEvent !== null)\n      ? BaseEvent.fromPartial(object.baseEvent)\n      : undefined;\n    message.toolCallId = object.toolCallId ?? \"\";\n    message.toolCallName = object.toolCallName ?? \"\";\n    message.parentMessageId = object.parentMessageId ?? undefined;\n    return message;\n  },\n};\n\nfunction createBaseToolCallArgsEvent(): ToolCallArgsEvent {\n  return { baseEvent: undefined, toolCallId: \"\", delta: \"\" };\n}\n\nexport const ToolCallArgsEvent: MessageFns<ToolCallArgsEvent> = {\n  encode(message: ToolCallArgsEvent, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {\n    if (message.baseEvent !== undefined) {\n      BaseEvent.encode(message.baseEvent, writer.uint32(10).fork()).join();\n    }\n    if (message.toolCallId !== \"\") {\n      writer.uint32(18).string(message.toolCallId);\n    }\n    if (message.delta !== \"\") {\n      writer.uint32(26).string(message.delta);\n    }\n    return writer;\n  },\n\n  decode(input: BinaryReader | Uint8Array, length?: number): ToolCallArgsEvent {\n    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);\n    let end = length === undefined ? reader.len : reader.pos + length;\n    const message = createBaseToolCallArgsEvent();\n    while (reader.pos < end) {\n      const tag = reader.uint32();\n      switch (tag >>> 3) {\n        case 1: {\n          if (tag !== 10) {\n            break;\n          }\n\n          message.baseEvent = BaseEvent.decode(reader, reader.uint32());\n          continue;\n        }\n        case 2: {\n          if (tag !== 18) {\n            break;\n          }\n\n          message.toolCallId = reader.string();\n          continue;\n        }\n        case 3: {\n          if (tag !== 26) {\n            break;\n          }\n\n          message.delta = reader.string();\n          continue;\n        }\n      }\n      if ((tag & 7) === 4 || tag === 0) {\n        break;\n      }\n      reader.skip(tag & 7);\n    }\n    return message;\n  },\n\n  create<I extends Exact<DeepPartial<ToolCallArgsEvent>, I>>(base?: I): ToolCallArgsEvent {\n    return ToolCallArgsEvent.fromPartial(base ?? ({} as any));\n  },\n  fromPartial<I extends Exact<DeepPartial<ToolCallArgsEvent>, I>>(object: I): ToolCallArgsEvent {\n    const message = createBaseToolCallArgsEvent();\n    message.baseEvent = (object.baseEvent !== undefined && object.baseEvent !== null)\n      ? BaseEvent.fromPartial(object.baseEvent)\n      : undefined;\n    message.toolCallId = object.toolCallId ?? \"\";\n    message.delta = object.delta ?? \"\";\n    return message;\n  },\n};\n\nfunction createBaseToolCallEndEvent(): ToolCallEndEvent {\n  return { baseEvent: undefined, toolCallId: \"\" };\n}\n\nexport const ToolCallEndEvent: MessageFns<ToolCallEndEvent> = {\n  encode(message: ToolCallEndEvent, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {\n    if (message.baseEvent !== undefined) {\n      BaseEvent.encode(message.baseEvent, writer.uint32(10).fork()).join();\n    }\n    if (message.toolCallId !== \"\") {\n      writer.uint32(18).string(message.toolCallId);\n    }\n    return writer;\n  },\n\n  decode(input: BinaryReader | Uint8Array, length?: number): ToolCallEndEvent {\n    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);\n    let end = length === undefined ? reader.len : reader.pos + length;\n    const message = createBaseToolCallEndEvent();\n    while (reader.pos < end) {\n      const tag = reader.uint32();\n      switch (tag >>> 3) {\n        case 1: {\n          if (tag !== 10) {\n            break;\n          }\n\n          message.baseEvent = BaseEvent.decode(reader, reader.uint32());\n          continue;\n        }\n        case 2: {\n          if (tag !== 18) {\n            break;\n          }\n\n          message.toolCallId = reader.string();\n          continue;\n        }\n      }\n      if ((tag & 7) === 4 || tag === 0) {\n        break;\n      }\n      reader.skip(tag & 7);\n    }\n    return message;\n  },\n\n  create<I extends Exact<DeepPartial<ToolCallEndEvent>, I>>(base?: I): ToolCallEndEvent {\n    return ToolCallEndEvent.fromPartial(base ?? ({} as any));\n  },\n  fromPartial<I extends Exact<DeepPartial<ToolCallEndEvent>, I>>(object: I): ToolCallEndEvent {\n    const message = createBaseToolCallEndEvent();\n    message.baseEvent = (object.baseEvent !== undefined && object.baseEvent !== null)\n      ? BaseEvent.fromPartial(object.baseEvent)\n      : undefined;\n    message.toolCallId = object.toolCallId ?? \"\";\n    return message;\n  },\n};\n\nfunction createBaseStateSnapshotEvent(): StateSnapshotEvent {\n  return { baseEvent: undefined, snapshot: undefined };\n}\n\nexport const StateSnapshotEvent: MessageFns<StateSnapshotEvent> = {\n  encode(message: StateSnapshotEvent, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {\n    if (message.baseEvent !== undefined) {\n      BaseEvent.encode(message.baseEvent, writer.uint32(10).fork()).join();\n    }\n    if (message.snapshot !== undefined) {\n      Value.encode(Value.wrap(message.snapshot), writer.uint32(18).fork()).join();\n    }\n    return writer;\n  },\n\n  decode(input: BinaryReader | Uint8Array, length?: number): StateSnapshotEvent {\n    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);\n    let end = length === undefined ? reader.len : reader.pos + length;\n    const message = createBaseStateSnapshotEvent();\n    while (reader.pos < end) {\n      const tag = reader.uint32();\n      switch (tag >>> 3) {\n        case 1: {\n          if (tag !== 10) {\n            break;\n          }\n\n          message.baseEvent = BaseEvent.decode(reader, reader.uint32());\n          continue;\n        }\n        case 2: {\n          if (tag !== 18) {\n            break;\n          }\n\n          message.snapshot = Value.unwrap(Value.decode(reader, reader.uint32()));\n          continue;\n        }\n      }\n      if ((tag & 7) === 4 || tag === 0) {\n        break;\n      }\n      reader.skip(tag & 7);\n    }\n    return message;\n  },\n\n  create<I extends Exact<DeepPartial<StateSnapshotEvent>, I>>(base?: I): StateSnapshotEvent {\n    return StateSnapshotEvent.fromPartial(base ?? ({} as any));\n  },\n  fromPartial<I extends Exact<DeepPartial<StateSnapshotEvent>, I>>(object: I): StateSnapshotEvent {\n    const message = createBaseStateSnapshotEvent();\n    message.baseEvent = (object.baseEvent !== undefined && object.baseEvent !== null)\n      ? BaseEvent.fromPartial(object.baseEvent)\n      : undefined;\n    message.snapshot = object.snapshot ?? undefined;\n    return message;\n  },\n};\n\nfunction createBaseStateDeltaEvent(): StateDeltaEvent {\n  return { baseEvent: undefined, delta: [] };\n}\n\nexport const StateDeltaEvent: MessageFns<StateDeltaEvent> = {\n  encode(message: StateDeltaEvent, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {\n    if (message.baseEvent !== undefined) {\n      BaseEvent.encode(message.baseEvent, writer.uint32(10).fork()).join();\n    }\n    for (const v of message.delta) {\n      JsonPatchOperation.encode(v!, writer.uint32(18).fork()).join();\n    }\n    return writer;\n  },\n\n  decode(input: BinaryReader | Uint8Array, length?: number): StateDeltaEvent {\n    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);\n    let end = length === undefined ? reader.len : reader.pos + length;\n    const message = createBaseStateDeltaEvent();\n    while (reader.pos < end) {\n      const tag = reader.uint32();\n      switch (tag >>> 3) {\n        case 1: {\n          if (tag !== 10) {\n            break;\n          }\n\n          message.baseEvent = BaseEvent.decode(reader, reader.uint32());\n          continue;\n        }\n        case 2: {\n          if (tag !== 18) {\n            break;\n          }\n\n          message.delta.push(JsonPatchOperation.decode(reader, reader.uint32()));\n          continue;\n        }\n      }\n      if ((tag & 7) === 4 || tag === 0) {\n        break;\n      }\n      reader.skip(tag & 7);\n    }\n    return message;\n  },\n\n  create<I extends Exact<DeepPartial<StateDeltaEvent>, I>>(base?: I): StateDeltaEvent {\n    return StateDeltaEvent.fromPartial(base ?? ({} as any));\n  },\n  fromPartial<I extends Exact<DeepPartial<StateDeltaEvent>, I>>(object: I): StateDeltaEvent {\n    const message = createBaseStateDeltaEvent();\n    message.baseEvent = (object.baseEvent !== undefined && object.baseEvent !== null)\n      ? BaseEvent.fromPartial(object.baseEvent)\n      : undefined;\n    message.delta = object.delta?.map((e) => JsonPatchOperation.fromPartial(e)) || [];\n    return message;\n  },\n};\n\nfunction createBaseMessagesSnapshotEvent(): MessagesSnapshotEvent {\n  return { baseEvent: undefined, messages: [] };\n}\n\nexport const MessagesSnapshotEvent: MessageFns<MessagesSnapshotEvent> = {\n  encode(message: MessagesSnapshotEvent, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {\n    if (message.baseEvent !== undefined) {\n      BaseEvent.encode(message.baseEvent, writer.uint32(10).fork()).join();\n    }\n    for (const v of message.messages) {\n      Message.encode(v!, writer.uint32(18).fork()).join();\n    }\n    return writer;\n  },\n\n  decode(input: BinaryReader | Uint8Array, length?: number): MessagesSnapshotEvent {\n    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);\n    let end = length === undefined ? reader.len : reader.pos + length;\n    const message = createBaseMessagesSnapshotEvent();\n    while (reader.pos < end) {\n      const tag = reader.uint32();\n      switch (tag >>> 3) {\n        case 1: {\n          if (tag !== 10) {\n            break;\n          }\n\n          message.baseEvent = BaseEvent.decode(reader, reader.uint32());\n          continue;\n        }\n        case 2: {\n          if (tag !== 18) {\n            break;\n          }\n\n          message.messages.push(Message.decode(reader, reader.uint32()));\n          continue;\n        }\n      }\n      if ((tag & 7) === 4 || tag === 0) {\n        break;\n      }\n      reader.skip(tag & 7);\n    }\n    return message;\n  },\n\n  create<I extends Exact<DeepPartial<MessagesSnapshotEvent>, I>>(base?: I): MessagesSnapshotEvent {\n    return MessagesSnapshotEvent.fromPartial(base ?? ({} as any));\n  },\n  fromPartial<I extends Exact<DeepPartial<MessagesSnapshotEvent>, I>>(object: I): MessagesSnapshotEvent {\n    const message = createBaseMessagesSnapshotEvent();\n    message.baseEvent = (object.baseEvent !== undefined && object.baseEvent !== null)\n      ? BaseEvent.fromPartial(object.baseEvent)\n      : undefined;\n    message.messages = object.messages?.map((e) => Message.fromPartial(e)) || [];\n    return message;\n  },\n};\n\nfunction createBaseRawEvent(): RawEvent {\n  return { baseEvent: undefined, event: undefined, source: undefined };\n}\n\nexport const RawEvent: MessageFns<RawEvent> = {\n  encode(message: RawEvent, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {\n    if (message.baseEvent !== undefined) {\n      BaseEvent.encode(message.baseEvent, writer.uint32(10).fork()).join();\n    }\n    if (message.event !== undefined) {\n      Value.encode(Value.wrap(message.event), writer.uint32(18).fork()).join();\n    }\n    if (message.source !== undefined) {\n      writer.uint32(26).string(message.source);\n    }\n    return writer;\n  },\n\n  decode(input: BinaryReader | Uint8Array, length?: number): RawEvent {\n    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);\n    let end = length === undefined ? reader.len : reader.pos + length;\n    const message = createBaseRawEvent();\n    while (reader.pos < end) {\n      const tag = reader.uint32();\n      switch (tag >>> 3) {\n        case 1: {\n          if (tag !== 10) {\n            break;\n          }\n\n          message.baseEvent = BaseEvent.decode(reader, reader.uint32());\n          continue;\n        }\n        case 2: {\n          if (tag !== 18) {\n            break;\n          }\n\n          message.event = Value.unwrap(Value.decode(reader, reader.uint32()));\n          continue;\n        }\n        case 3: {\n          if (tag !== 26) {\n            break;\n          }\n\n          message.source = reader.string();\n          continue;\n        }\n      }\n      if ((tag & 7) === 4 || tag === 0) {\n        break;\n      }\n      reader.skip(tag & 7);\n    }\n    return message;\n  },\n\n  create<I extends Exact<DeepPartial<RawEvent>, I>>(base?: I): RawEvent {\n    return RawEvent.fromPartial(base ?? ({} as any));\n  },\n  fromPartial<I extends Exact<DeepPartial<RawEvent>, I>>(object: I): RawEvent {\n    const message = createBaseRawEvent();\n    message.baseEvent = (object.baseEvent !== undefined && object.baseEvent !== null)\n      ? BaseEvent.fromPartial(object.baseEvent)\n      : undefined;\n    message.event = object.event ?? undefined;\n    message.source = object.source ?? undefined;\n    return message;\n  },\n};\n\nfunction createBaseCustomEvent(): CustomEvent {\n  return { baseEvent: undefined, name: \"\", value: undefined };\n}\n\nexport const CustomEvent: MessageFns<CustomEvent> = {\n  encode(message: CustomEvent, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {\n    if (message.baseEvent !== undefined) {\n      BaseEvent.encode(message.baseEvent, writer.uint32(10).fork()).join();\n    }\n    if (message.name !== \"\") {\n      writer.uint32(18).string(message.name);\n    }\n    if (message.value !== undefined) {\n      Value.encode(Value.wrap(message.value), writer.uint32(26).fork()).join();\n    }\n    return writer;\n  },\n\n  decode(input: BinaryReader | Uint8Array, length?: number): CustomEvent {\n    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);\n    let end = length === undefined ? reader.len : reader.pos + length;\n    const message = createBaseCustomEvent();\n    while (reader.pos < end) {\n      const tag = reader.uint32();\n      switch (tag >>> 3) {\n        case 1: {\n          if (tag !== 10) {\n            break;\n          }\n\n          message.baseEvent = BaseEvent.decode(reader, reader.uint32());\n          continue;\n        }\n        case 2: {\n          if (tag !== 18) {\n            break;\n          }\n\n          message.name = reader.string();\n          continue;\n        }\n        case 3: {\n          if (tag !== 26) {\n            break;\n          }\n\n          message.value = Value.unwrap(Value.decode(reader, reader.uint32()));\n          continue;\n        }\n      }\n      if ((tag & 7) === 4 || tag === 0) {\n        break;\n      }\n      reader.skip(tag & 7);\n    }\n    return message;\n  },\n\n  create<I extends Exact<DeepPartial<CustomEvent>, I>>(base?: I): CustomEvent {\n    return CustomEvent.fromPartial(base ?? ({} as any));\n  },\n  fromPartial<I extends Exact<DeepPartial<CustomEvent>, I>>(object: I): CustomEvent {\n    const message = createBaseCustomEvent();\n    message.baseEvent = (object.baseEvent !== undefined && object.baseEvent !== null)\n      ? BaseEvent.fromPartial(object.baseEvent)\n      : undefined;\n    message.name = object.name ?? \"\";\n    message.value = object.value ?? undefined;\n    return message;\n  },\n};\n\nfunction createBaseRunStartedEvent(): RunStartedEvent {\n  return { baseEvent: undefined, threadId: \"\", runId: \"\" };\n}\n\nexport const RunStartedEvent: MessageFns<RunStartedEvent> = {\n  encode(message: RunStartedEvent, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {\n    if (message.baseEvent !== undefined) {\n      BaseEvent.encode(message.baseEvent, writer.uint32(10).fork()).join();\n    }\n    if (message.threadId !== \"\") {\n      writer.uint32(18).string(message.threadId);\n    }\n    if (message.runId !== \"\") {\n      writer.uint32(26).string(message.runId);\n    }\n    return writer;\n  },\n\n  decode(input: BinaryReader | Uint8Array, length?: number): RunStartedEvent {\n    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);\n    let end = length === undefined ? reader.len : reader.pos + length;\n    const message = createBaseRunStartedEvent();\n    while (reader.pos < end) {\n      const tag = reader.uint32();\n      switch (tag >>> 3) {\n        case 1: {\n          if (tag !== 10) {\n            break;\n          }\n\n          message.baseEvent = BaseEvent.decode(reader, reader.uint32());\n          continue;\n        }\n        case 2: {\n          if (tag !== 18) {\n            break;\n          }\n\n          message.threadId = reader.string();\n          continue;\n        }\n        case 3: {\n          if (tag !== 26) {\n            break;\n          }\n\n          message.runId = reader.string();\n          continue;\n        }\n      }\n      if ((tag & 7) === 4 || tag === 0) {\n        break;\n      }\n      reader.skip(tag & 7);\n    }\n    return message;\n  },\n\n  create<I extends Exact<DeepPartial<RunStartedEvent>, I>>(base?: I): RunStartedEvent {\n    return RunStartedEvent.fromPartial(base ?? ({} as any));\n  },\n  fromPartial<I extends Exact<DeepPartial<RunStartedEvent>, I>>(object: I): RunStartedEvent {\n    const message = createBaseRunStartedEvent();\n    message.baseEvent = (object.baseEvent !== undefined && object.baseEvent !== null)\n      ? BaseEvent.fromPartial(object.baseEvent)\n      : undefined;\n    message.threadId = object.threadId ?? \"\";\n    message.runId = object.runId ?? \"\";\n    return message;\n  },\n};\n\nfunction createBaseRunFinishedEvent(): RunFinishedEvent {\n  return { baseEvent: undefined, threadId: \"\", runId: \"\" };\n}\n\nexport const RunFinishedEvent: MessageFns<RunFinishedEvent> = {\n  encode(message: RunFinishedEvent, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {\n    if (message.baseEvent !== undefined) {\n      BaseEvent.encode(message.baseEvent, writer.uint32(10).fork()).join();\n    }\n    if (message.threadId !== \"\") {\n      writer.uint32(18).string(message.threadId);\n    }\n    if (message.runId !== \"\") {\n      writer.uint32(26).string(message.runId);\n    }\n    return writer;\n  },\n\n  decode(input: BinaryReader | Uint8Array, length?: number): RunFinishedEvent {\n    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);\n    let end = length === undefined ? reader.len : reader.pos + length;\n    const message = createBaseRunFinishedEvent();\n    while (reader.pos < end) {\n      const tag = reader.uint32();\n      switch (tag >>> 3) {\n        case 1: {\n          if (tag !== 10) {\n            break;\n          }\n\n          message.baseEvent = BaseEvent.decode(reader, reader.uint32());\n          continue;\n        }\n        case 2: {\n          if (tag !== 18) {\n            break;\n          }\n\n          message.threadId = reader.string();\n          continue;\n        }\n        case 3: {\n          if (tag !== 26) {\n            break;\n          }\n\n          message.runId = reader.string();\n          continue;\n        }\n      }\n      if ((tag & 7) === 4 || tag === 0) {\n        break;\n      }\n      reader.skip(tag & 7);\n    }\n    return message;\n  },\n\n  create<I extends Exact<DeepPartial<RunFinishedEvent>, I>>(base?: I): RunFinishedEvent {\n    return RunFinishedEvent.fromPartial(base ?? ({} as any));\n  },\n  fromPartial<I extends Exact<DeepPartial<RunFinishedEvent>, I>>(object: I): RunFinishedEvent {\n    const message = createBaseRunFinishedEvent();\n    message.baseEvent = (object.baseEvent !== undefined && object.baseEvent !== null)\n      ? BaseEvent.fromPartial(object.baseEvent)\n      : undefined;\n    message.threadId = object.threadId ?? \"\";\n    message.runId = object.runId ?? \"\";\n    return message;\n  },\n};\n\nfunction createBaseRunErrorEvent(): RunErrorEvent {\n  return { baseEvent: undefined, code: undefined, message: \"\" };\n}\n\nexport const RunErrorEvent: MessageFns<RunErrorEvent> = {\n  encode(message: RunErrorEvent, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {\n    if (message.baseEvent !== undefined) {\n      BaseEvent.encode(message.baseEvent, writer.uint32(10).fork()).join();\n    }\n    if (message.code !== undefined) {\n      writer.uint32(18).string(message.code);\n    }\n    if (message.message !== \"\") {\n      writer.uint32(26).string(message.message);\n    }\n    return writer;\n  },\n\n  decode(input: BinaryReader | Uint8Array, length?: number): RunErrorEvent {\n    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);\n    let end = length === undefined ? reader.len : reader.pos + length;\n    const message = createBaseRunErrorEvent();\n    while (reader.pos < end) {\n      const tag = reader.uint32();\n      switch (tag >>> 3) {\n        case 1: {\n          if (tag !== 10) {\n            break;\n          }\n\n          message.baseEvent = BaseEvent.decode(reader, reader.uint32());\n          continue;\n        }\n        case 2: {\n          if (tag !== 18) {\n            break;\n          }\n\n          message.code = reader.string();\n          continue;\n        }\n        case 3: {\n          if (tag !== 26) {\n            break;\n          }\n\n          message.message = reader.string();\n          continue;\n        }\n      }\n      if ((tag & 7) === 4 || tag === 0) {\n        break;\n      }\n      reader.skip(tag & 7);\n    }\n    return message;\n  },\n\n  create<I extends Exact<DeepPartial<RunErrorEvent>, I>>(base?: I): RunErrorEvent {\n    return RunErrorEvent.fromPartial(base ?? ({} as any));\n  },\n  fromPartial<I extends Exact<DeepPartial<RunErrorEvent>, I>>(object: I): RunErrorEvent {\n    const message = createBaseRunErrorEvent();\n    message.baseEvent = (object.baseEvent !== undefined && object.baseEvent !== null)\n      ? BaseEvent.fromPartial(object.baseEvent)\n      : undefined;\n    message.code = object.code ?? undefined;\n    message.message = object.message ?? \"\";\n    return message;\n  },\n};\n\nfunction createBaseStepStartedEvent(): StepStartedEvent {\n  return { baseEvent: undefined, stepName: \"\" };\n}\n\nexport const StepStartedEvent: MessageFns<StepStartedEvent> = {\n  encode(message: StepStartedEvent, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {\n    if (message.baseEvent !== undefined) {\n      BaseEvent.encode(message.baseEvent, writer.uint32(10).fork()).join();\n    }\n    if (message.stepName !== \"\") {\n      writer.uint32(18).string(message.stepName);\n    }\n    return writer;\n  },\n\n  decode(input: BinaryReader | Uint8Array, length?: number): StepStartedEvent {\n    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);\n    let end = length === undefined ? reader.len : reader.pos + length;\n    const message = createBaseStepStartedEvent();\n    while (reader.pos < end) {\n      const tag = reader.uint32();\n      switch (tag >>> 3) {\n        case 1: {\n          if (tag !== 10) {\n            break;\n          }\n\n          message.baseEvent = BaseEvent.decode(reader, reader.uint32());\n          continue;\n        }\n        case 2: {\n          if (tag !== 18) {\n            break;\n          }\n\n          message.stepName = reader.string();\n          continue;\n        }\n      }\n      if ((tag & 7) === 4 || tag === 0) {\n        break;\n      }\n      reader.skip(tag & 7);\n    }\n    return message;\n  },\n\n  create<I extends Exact<DeepPartial<StepStartedEvent>, I>>(base?: I): StepStartedEvent {\n    return StepStartedEvent.fromPartial(base ?? ({} as any));\n  },\n  fromPartial<I extends Exact<DeepPartial<StepStartedEvent>, I>>(object: I): StepStartedEvent {\n    const message = createBaseStepStartedEvent();\n    message.baseEvent = (object.baseEvent !== undefined && object.baseEvent !== null)\n      ? BaseEvent.fromPartial(object.baseEvent)\n      : undefined;\n    message.stepName = object.stepName ?? \"\";\n    return message;\n  },\n};\n\nfunction createBaseStepFinishedEvent(): StepFinishedEvent {\n  return { baseEvent: undefined, stepName: \"\" };\n}\n\nexport const StepFinishedEvent: MessageFns<StepFinishedEvent> = {\n  encode(message: StepFinishedEvent, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {\n    if (message.baseEvent !== undefined) {\n      BaseEvent.encode(message.baseEvent, writer.uint32(10).fork()).join();\n    }\n    if (message.stepName !== \"\") {\n      writer.uint32(18).string(message.stepName);\n    }\n    return writer;\n  },\n\n  decode(input: BinaryReader | Uint8Array, length?: number): StepFinishedEvent {\n    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);\n    let end = length === undefined ? reader.len : reader.pos + length;\n    const message = createBaseStepFinishedEvent();\n    while (reader.pos < end) {\n      const tag = reader.uint32();\n      switch (tag >>> 3) {\n        case 1: {\n          if (tag !== 10) {\n            break;\n          }\n\n          message.baseEvent = BaseEvent.decode(reader, reader.uint32());\n          continue;\n        }\n        case 2: {\n          if (tag !== 18) {\n            break;\n          }\n\n          message.stepName = reader.string();\n          continue;\n        }\n      }\n      if ((tag & 7) === 4 || tag === 0) {\n        break;\n      }\n      reader.skip(tag & 7);\n    }\n    return message;\n  },\n\n  create<I extends Exact<DeepPartial<StepFinishedEvent>, I>>(base?: I): StepFinishedEvent {\n    return StepFinishedEvent.fromPartial(base ?? ({} as any));\n  },\n  fromPartial<I extends Exact<DeepPartial<StepFinishedEvent>, I>>(object: I): StepFinishedEvent {\n    const message = createBaseStepFinishedEvent();\n    message.baseEvent = (object.baseEvent !== undefined && object.baseEvent !== null)\n      ? BaseEvent.fromPartial(object.baseEvent)\n      : undefined;\n    message.stepName = object.stepName ?? \"\";\n    return message;\n  },\n};\n\nfunction createBaseTextMessageChunkEvent(): TextMessageChunkEvent {\n  return { baseEvent: undefined, messageId: undefined, role: undefined, delta: undefined };\n}\n\nexport const TextMessageChunkEvent: MessageFns<TextMessageChunkEvent> = {\n  encode(message: TextMessageChunkEvent, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {\n    if (message.baseEvent !== undefined) {\n      BaseEvent.encode(message.baseEvent, writer.uint32(10).fork()).join();\n    }\n    if (message.messageId !== undefined) {\n      writer.uint32(18).string(message.messageId);\n    }\n    if (message.role !== undefined) {\n      writer.uint32(26).string(message.role);\n    }\n    if (message.delta !== undefined) {\n      writer.uint32(34).string(message.delta);\n    }\n    return writer;\n  },\n\n  decode(input: BinaryReader | Uint8Array, length?: number): TextMessageChunkEvent {\n    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);\n    let end = length === undefined ? reader.len : reader.pos + length;\n    const message = createBaseTextMessageChunkEvent();\n    while (reader.pos < end) {\n      const tag = reader.uint32();\n      switch (tag >>> 3) {\n        case 1: {\n          if (tag !== 10) {\n            break;\n          }\n\n          message.baseEvent = BaseEvent.decode(reader, reader.uint32());\n          continue;\n        }\n        case 2: {\n          if (tag !== 18) {\n            break;\n          }\n\n          message.messageId = reader.string();\n          continue;\n        }\n        case 3: {\n          if (tag !== 26) {\n            break;\n          }\n\n          message.role = reader.string();\n          continue;\n        }\n        case 4: {\n          if (tag !== 34) {\n            break;\n          }\n\n          message.delta = reader.string();\n          continue;\n        }\n      }\n      if ((tag & 7) === 4 || tag === 0) {\n        break;\n      }\n      reader.skip(tag & 7);\n    }\n    return message;\n  },\n\n  create<I extends Exact<DeepPartial<TextMessageChunkEvent>, I>>(base?: I): TextMessageChunkEvent {\n    return TextMessageChunkEvent.fromPartial(base ?? ({} as any));\n  },\n  fromPartial<I extends Exact<DeepPartial<TextMessageChunkEvent>, I>>(object: I): TextMessageChunkEvent {\n    const message = createBaseTextMessageChunkEvent();\n    message.baseEvent = (object.baseEvent !== undefined && object.baseEvent !== null)\n      ? BaseEvent.fromPartial(object.baseEvent)\n      : undefined;\n    message.messageId = object.messageId ?? undefined;\n    message.role = object.role ?? undefined;\n    message.delta = object.delta ?? undefined;\n    return message;\n  },\n};\n\nfunction createBaseToolCallChunkEvent(): ToolCallChunkEvent {\n  return {\n    baseEvent: undefined,\n    toolCallId: undefined,\n    toolCallName: undefined,\n    parentMessageId: undefined,\n    delta: undefined,\n  };\n}\n\nexport const ToolCallChunkEvent: MessageFns<ToolCallChunkEvent> = {\n  encode(message: ToolCallChunkEvent, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {\n    if (message.baseEvent !== undefined) {\n      BaseEvent.encode(message.baseEvent, writer.uint32(10).fork()).join();\n    }\n    if (message.toolCallId !== undefined) {\n      writer.uint32(18).string(message.toolCallId);\n    }\n    if (message.toolCallName !== undefined) {\n      writer.uint32(26).string(message.toolCallName);\n    }\n    if (message.parentMessageId !== undefined) {\n      writer.uint32(34).string(message.parentMessageId);\n    }\n    if (message.delta !== undefined) {\n      writer.uint32(42).string(message.delta);\n    }\n    return writer;\n  },\n\n  decode(input: BinaryReader | Uint8Array, length?: number): ToolCallChunkEvent {\n    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);\n    let end = length === undefined ? reader.len : reader.pos + length;\n    const message = createBaseToolCallChunkEvent();\n    while (reader.pos < end) {\n      const tag = reader.uint32();\n      switch (tag >>> 3) {\n        case 1: {\n          if (tag !== 10) {\n            break;\n          }\n\n          message.baseEvent = BaseEvent.decode(reader, reader.uint32());\n          continue;\n        }\n        case 2: {\n          if (tag !== 18) {\n            break;\n          }\n\n          message.toolCallId = reader.string();\n          continue;\n        }\n        case 3: {\n          if (tag !== 26) {\n            break;\n          }\n\n          message.toolCallName = reader.string();\n          continue;\n        }\n        case 4: {\n          if (tag !== 34) {\n            break;\n          }\n\n          message.parentMessageId = reader.string();\n          continue;\n        }\n        case 5: {\n          if (tag !== 42) {\n            break;\n          }\n\n          message.delta = reader.string();\n          continue;\n        }\n      }\n      if ((tag & 7) === 4 || tag === 0) {\n        break;\n      }\n      reader.skip(tag & 7);\n    }\n    return message;\n  },\n\n  create<I extends Exact<DeepPartial<ToolCallChunkEvent>, I>>(base?: I): ToolCallChunkEvent {\n    return ToolCallChunkEvent.fromPartial(base ?? ({} as any));\n  },\n  fromPartial<I extends Exact<DeepPartial<ToolCallChunkEvent>, I>>(object: I): ToolCallChunkEvent {\n    const message = createBaseToolCallChunkEvent();\n    message.baseEvent = (object.baseEvent !== undefined && object.baseEvent !== null)\n      ? BaseEvent.fromPartial(object.baseEvent)\n      : undefined;\n    message.toolCallId = object.toolCallId ?? undefined;\n    message.toolCallName = object.toolCallName ?? undefined;\n    message.parentMessageId = object.parentMessageId ?? undefined;\n    message.delta = object.delta ?? undefined;\n    return message;\n  },\n};\n\nfunction createBaseEvent(): Event {\n  return {\n    textMessageStart: undefined,\n    textMessageContent: undefined,\n    textMessageEnd: undefined,\n    toolCallStart: undefined,\n    toolCallArgs: undefined,\n    toolCallEnd: undefined,\n    stateSnapshot: undefined,\n    stateDelta: undefined,\n    messagesSnapshot: undefined,\n    raw: undefined,\n    custom: undefined,\n    runStarted: undefined,\n    runFinished: undefined,\n    runError: undefined,\n    stepStarted: undefined,\n    stepFinished: undefined,\n    textMessageChunk: undefined,\n    toolCallChunk: undefined,\n  };\n}\n\nexport const Event: MessageFns<Event> = {\n  encode(message: Event, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {\n    if (message.textMessageStart !== undefined) {\n      TextMessageStartEvent.encode(message.textMessageStart, writer.uint32(10).fork()).join();\n    }\n    if (message.textMessageContent !== undefined) {\n      TextMessageContentEvent.encode(message.textMessageContent, writer.uint32(18).fork()).join();\n    }\n    if (message.textMessageEnd !== undefined) {\n      TextMessageEndEvent.encode(message.textMessageEnd, writer.uint32(26).fork()).join();\n    }\n    if (message.toolCallStart !== undefined) {\n      ToolCallStartEvent.encode(message.toolCallStart, writer.uint32(34).fork()).join();\n    }\n    if (message.toolCallArgs !== undefined) {\n      ToolCallArgsEvent.encode(message.toolCallArgs, writer.uint32(42).fork()).join();\n    }\n    if (message.toolCallEnd !== undefined) {\n      ToolCallEndEvent.encode(message.toolCallEnd, writer.uint32(50).fork()).join();\n    }\n    if (message.stateSnapshot !== undefined) {\n      StateSnapshotEvent.encode(message.stateSnapshot, writer.uint32(58).fork()).join();\n    }\n    if (message.stateDelta !== undefined) {\n      StateDeltaEvent.encode(message.stateDelta, writer.uint32(66).fork()).join();\n    }\n    if (message.messagesSnapshot !== undefined) {\n      MessagesSnapshotEvent.encode(message.messagesSnapshot, writer.uint32(74).fork()).join();\n    }\n    if (message.raw !== undefined) {\n      RawEvent.encode(message.raw, writer.uint32(82).fork()).join();\n    }\n    if (message.custom !== undefined) {\n      CustomEvent.encode(message.custom, writer.uint32(90).fork()).join();\n    }\n    if (message.runStarted !== undefined) {\n      RunStartedEvent.encode(message.runStarted, writer.uint32(98).fork()).join();\n    }\n    if (message.runFinished !== undefined) {\n      RunFinishedEvent.encode(message.runFinished, writer.uint32(106).fork()).join();\n    }\n    if (message.runError !== undefined) {\n      RunErrorEvent.encode(message.runError, writer.uint32(114).fork()).join();\n    }\n    if (message.stepStarted !== undefined) {\n      StepStartedEvent.encode(message.stepStarted, writer.uint32(122).fork()).join();\n    }\n    if (message.stepFinished !== undefined) {\n      StepFinishedEvent.encode(message.stepFinished, writer.uint32(130).fork()).join();\n    }\n    if (message.textMessageChunk !== undefined) {\n      TextMessageChunkEvent.encode(message.textMessageChunk, writer.uint32(138).fork()).join();\n    }\n    if (message.toolCallChunk !== undefined) {\n      ToolCallChunkEvent.encode(message.toolCallChunk, writer.uint32(146).fork()).join();\n    }\n    return writer;\n  },\n\n  decode(input: BinaryReader | Uint8Array, length?: number): Event {\n    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);\n    let end = length === undefined ? reader.len : reader.pos + length;\n    const message = createBaseEvent();\n    while (reader.pos < end) {\n      const tag = reader.uint32();\n      switch (tag >>> 3) {\n        case 1: {\n          if (tag !== 10) {\n            break;\n          }\n\n          message.textMessageStart = TextMessageStartEvent.decode(reader, reader.uint32());\n          continue;\n        }\n        case 2: {\n          if (tag !== 18) {\n            break;\n          }\n\n          message.textMessageContent = TextMessageContentEvent.decode(reader, reader.uint32());\n          continue;\n        }\n        case 3: {\n          if (tag !== 26) {\n            break;\n          }\n\n          message.textMessageEnd = TextMessageEndEvent.decode(reader, reader.uint32());\n          continue;\n        }\n        case 4: {\n          if (tag !== 34) {\n            break;\n          }\n\n          message.toolCallStart = ToolCallStartEvent.decode(reader, reader.uint32());\n          continue;\n        }\n        case 5: {\n          if (tag !== 42) {\n            break;\n          }\n\n          message.toolCallArgs = ToolCallArgsEvent.decode(reader, reader.uint32());\n          continue;\n        }\n        case 6: {\n          if (tag !== 50) {\n            break;\n          }\n\n          message.toolCallEnd = ToolCallEndEvent.decode(reader, reader.uint32());\n          continue;\n        }\n        case 7: {\n          if (tag !== 58) {\n            break;\n          }\n\n          message.stateSnapshot = StateSnapshotEvent.decode(reader, reader.uint32());\n          continue;\n        }\n        case 8: {\n          if (tag !== 66) {\n            break;\n          }\n\n          message.stateDelta = StateDeltaEvent.decode(reader, reader.uint32());\n          continue;\n        }\n        case 9: {\n          if (tag !== 74) {\n            break;\n          }\n\n          message.messagesSnapshot = MessagesSnapshotEvent.decode(reader, reader.uint32());\n          continue;\n        }\n        case 10: {\n          if (tag !== 82) {\n            break;\n          }\n\n          message.raw = RawEvent.decode(reader, reader.uint32());\n          continue;\n        }\n        case 11: {\n          if (tag !== 90) {\n            break;\n          }\n\n          message.custom = CustomEvent.decode(reader, reader.uint32());\n          continue;\n        }\n        case 12: {\n          if (tag !== 98) {\n            break;\n          }\n\n          message.runStarted = RunStartedEvent.decode(reader, reader.uint32());\n          continue;\n        }\n        case 13: {\n          if (tag !== 106) {\n            break;\n          }\n\n          message.runFinished = RunFinishedEvent.decode(reader, reader.uint32());\n          continue;\n        }\n        case 14: {\n          if (tag !== 114) {\n            break;\n          }\n\n          message.runError = RunErrorEvent.decode(reader, reader.uint32());\n          continue;\n        }\n        case 15: {\n          if (tag !== 122) {\n            break;\n          }\n\n          message.stepStarted = StepStartedEvent.decode(reader, reader.uint32());\n          continue;\n        }\n        case 16: {\n          if (tag !== 130) {\n            break;\n          }\n\n          message.stepFinished = StepFinishedEvent.decode(reader, reader.uint32());\n          continue;\n        }\n        case 17: {\n          if (tag !== 138) {\n            break;\n          }\n\n          message.textMessageChunk = TextMessageChunkEvent.decode(reader, reader.uint32());\n          continue;\n        }\n        case 18: {\n          if (tag !== 146) {\n            break;\n          }\n\n          message.toolCallChunk = ToolCallChunkEvent.decode(reader, reader.uint32());\n          continue;\n        }\n      }\n      if ((tag & 7) === 4 || tag === 0) {\n        break;\n      }\n      reader.skip(tag & 7);\n    }\n    return message;\n  },\n\n  create<I extends Exact<DeepPartial<Event>, I>>(base?: I): Event {\n    return Event.fromPartial(base ?? ({} as any));\n  },\n  fromPartial<I extends Exact<DeepPartial<Event>, I>>(object: I): Event {\n    const message = createBaseEvent();\n    message.textMessageStart = (object.textMessageStart !== undefined && object.textMessageStart !== null)\n      ? TextMessageStartEvent.fromPartial(object.textMessageStart)\n      : undefined;\n    message.textMessageContent = (object.textMessageContent !== undefined && object.textMessageContent !== null)\n      ? TextMessageContentEvent.fromPartial(object.textMessageContent)\n      : undefined;\n    message.textMessageEnd = (object.textMessageEnd !== undefined && object.textMessageEnd !== null)\n      ? TextMessageEndEvent.fromPartial(object.textMessageEnd)\n      : undefined;\n    message.toolCallStart = (object.toolCallStart !== undefined && object.toolCallStart !== null)\n      ? ToolCallStartEvent.fromPartial(object.toolCallStart)\n      : undefined;\n    message.toolCallArgs = (object.toolCallArgs !== undefined && object.toolCallArgs !== null)\n      ? ToolCallArgsEvent.fromPartial(object.toolCallArgs)\n      : undefined;\n    message.toolCallEnd = (object.toolCallEnd !== undefined && object.toolCallEnd !== null)\n      ? ToolCallEndEvent.fromPartial(object.toolCallEnd)\n      : undefined;\n    message.stateSnapshot = (object.stateSnapshot !== undefined && object.stateSnapshot !== null)\n      ? StateSnapshotEvent.fromPartial(object.stateSnapshot)\n      : undefined;\n    message.stateDelta = (object.stateDelta !== undefined && object.stateDelta !== null)\n      ? StateDeltaEvent.fromPartial(object.stateDelta)\n      : undefined;\n    message.messagesSnapshot = (object.messagesSnapshot !== undefined && object.messagesSnapshot !== null)\n      ? MessagesSnapshotEvent.fromPartial(object.messagesSnapshot)\n      : undefined;\n    message.raw = (object.raw !== undefined && object.raw !== null) ? RawEvent.fromPartial(object.raw) : undefined;\n    message.custom = (object.custom !== undefined && object.custom !== null)\n      ? CustomEvent.fromPartial(object.custom)\n      : undefined;\n    message.runStarted = (object.runStarted !== undefined && object.runStarted !== null)\n      ? RunStartedEvent.fromPartial(object.runStarted)\n      : undefined;\n    message.runFinished = (object.runFinished !== undefined && object.runFinished !== null)\n      ? RunFinishedEvent.fromPartial(object.runFinished)\n      : undefined;\n    message.runError = (object.runError !== undefined && object.runError !== null)\n      ? RunErrorEvent.fromPartial(object.runError)\n      : undefined;\n    message.stepStarted = (object.stepStarted !== undefined && object.stepStarted !== null)\n      ? StepStartedEvent.fromPartial(object.stepStarted)\n      : undefined;\n    message.stepFinished = (object.stepFinished !== undefined && object.stepFinished !== null)\n      ? StepFinishedEvent.fromPartial(object.stepFinished)\n      : undefined;\n    message.textMessageChunk = (object.textMessageChunk !== undefined && object.textMessageChunk !== null)\n      ? TextMessageChunkEvent.fromPartial(object.textMessageChunk)\n      : undefined;\n    message.toolCallChunk = (object.toolCallChunk !== undefined && object.toolCallChunk !== null)\n      ? ToolCallChunkEvent.fromPartial(object.toolCallChunk)\n      : undefined;\n    return message;\n  },\n};\n\ntype Builtin = Date | Function | Uint8Array | string | number | boolean | undefined;\n\nexport type DeepPartial<T> = T extends Builtin ? T\n  : T extends globalThis.Array<infer U> ? globalThis.Array<DeepPartial<U>>\n  : T extends ReadonlyArray<infer U> ? ReadonlyArray<DeepPartial<U>>\n  : T extends {} ? { [K in keyof T]?: DeepPartial<T[K]> }\n  : Partial<T>;\n\ntype KeysOfUnion<T> = T extends T ? keyof T : never;\nexport type Exact<P, I extends P> = P extends Builtin ? P\n  : P & { [K in keyof P]: Exact<P[K], I[K]> } & { [K in Exclude<keyof I, KeysOfUnion<P>>]: never };\n\nfunction longToNumber(int64: { toString(): string }): number {\n  const num = globalThis.Number(int64.toString());\n  if (num > globalThis.Number.MAX_SAFE_INTEGER) {\n    throw new globalThis.Error(\"Value is larger than Number.MAX_SAFE_INTEGER\");\n  }\n  if (num < globalThis.Number.MIN_SAFE_INTEGER) {\n    throw new globalThis.Error(\"Value is smaller than Number.MIN_SAFE_INTEGER\");\n  }\n  return num;\n}\n\nexport interface MessageFns<T> {\n  encode(message: T, writer?: BinaryWriter): BinaryWriter;\n  decode(input: BinaryReader | Uint8Array, length?: number): T;\n  create<I extends Exact<DeepPartial<T>, I>>(base?: I): T;\n  fromPartial<I extends Exact<DeepPartial<T>, I>>(object: I): T;\n}\n", "// Code generated by protoc-gen-ts_proto. DO NOT EDIT.\n// versions:\n//   protoc-gen-ts_proto  v2.7.0\n//   protoc               v5.29.3\n// source: google/protobuf/struct.proto\n\n/* eslint-disable */\nimport { <PERSON>ary<PERSON><PERSON>er, BinaryWriter } from \"@bufbuild/protobuf/wire\";\n\nexport const protobufPackage = \"google.protobuf\";\n\n/**\n * `NullValue` is a singleton enumeration to represent the null value for the\n * `Value` type union.\n *\n * The JSON representation for `NullValue` is JSON `null`.\n */\nexport enum NullValue {\n  /** NULL_VALUE - Null value. */\n  NULL_VALUE = 0,\n  UNRECOGNIZED = -1,\n}\n\n/**\n * `Struct` represents a structured data value, consisting of fields\n * which map to dynamically typed values. In some languages, `Struct`\n * might be supported by a native representation. For example, in\n * scripting languages like JS a struct is represented as an\n * object. The details of that representation are described together\n * with the proto support for the language.\n *\n * The JSON representation for `Struct` is JSON object.\n */\nexport interface Struct {\n  /** Unordered map of dynamically typed values. */\n  fields: { [key: string]: any | undefined };\n}\n\nexport interface Struct_FieldsEntry {\n  key: string;\n  value: any | undefined;\n}\n\n/**\n * `Value` represents a dynamically typed value which can be either\n * null, a number, a string, a boolean, a recursive struct value, or a\n * list of values. A producer of value is expected to set one of these\n * variants. Absence of any variant indicates an error.\n *\n * The JSON representation for `Value` is JSON value.\n */\nexport interface Value {\n  /** Represents a null value. */\n  nullValue?:\n    | NullValue\n    | undefined;\n  /** Represents a double value. */\n  numberValue?:\n    | number\n    | undefined;\n  /** Represents a string value. */\n  stringValue?:\n    | string\n    | undefined;\n  /** Represents a boolean value. */\n  boolValue?:\n    | boolean\n    | undefined;\n  /** Represents a structured value. */\n  structValue?:\n    | { [key: string]: any }\n    | undefined;\n  /** Represents a repeated `Value`. */\n  listValue?: Array<any> | undefined;\n}\n\n/**\n * `ListValue` is a wrapper around a repeated field of values.\n *\n * The JSON representation for `ListValue` is JSON array.\n */\nexport interface ListValue {\n  /** Repeated field of dynamically typed values. */\n  values: any[];\n}\n\nfunction createBaseStruct(): Struct {\n  return { fields: {} };\n}\n\nexport const Struct: MessageFns<Struct> & StructWrapperFns = {\n  encode(message: Struct, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {\n    Object.entries(message.fields).forEach(([key, value]) => {\n      if (value !== undefined) {\n        Struct_FieldsEntry.encode({ key: key as any, value }, writer.uint32(10).fork()).join();\n      }\n    });\n    return writer;\n  },\n\n  decode(input: BinaryReader | Uint8Array, length?: number): Struct {\n    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);\n    let end = length === undefined ? reader.len : reader.pos + length;\n    const message = createBaseStruct();\n    while (reader.pos < end) {\n      const tag = reader.uint32();\n      switch (tag >>> 3) {\n        case 1: {\n          if (tag !== 10) {\n            break;\n          }\n\n          const entry1 = Struct_FieldsEntry.decode(reader, reader.uint32());\n          if (entry1.value !== undefined) {\n            message.fields[entry1.key] = entry1.value;\n          }\n          continue;\n        }\n      }\n      if ((tag & 7) === 4 || tag === 0) {\n        break;\n      }\n      reader.skip(tag & 7);\n    }\n    return message;\n  },\n\n  create<I extends Exact<DeepPartial<Struct>, I>>(base?: I): Struct {\n    return Struct.fromPartial(base ?? ({} as any));\n  },\n  fromPartial<I extends Exact<DeepPartial<Struct>, I>>(object: I): Struct {\n    const message = createBaseStruct();\n    message.fields = Object.entries(object.fields ?? {}).reduce<{ [key: string]: any | undefined }>(\n      (acc, [key, value]) => {\n        if (value !== undefined) {\n          acc[key] = value;\n        }\n        return acc;\n      },\n      {},\n    );\n    return message;\n  },\n\n  wrap(object: { [key: string]: any } | undefined): Struct {\n    const struct = createBaseStruct();\n\n    if (object !== undefined) {\n      for (const key of Object.keys(object)) {\n        struct.fields[key] = object[key];\n      }\n    }\n    return struct;\n  },\n\n  unwrap(message: Struct): { [key: string]: any } {\n    const object: { [key: string]: any } = {};\n    if (message.fields) {\n      for (const key of Object.keys(message.fields)) {\n        object[key] = message.fields[key];\n      }\n    }\n    return object;\n  },\n};\n\nfunction createBaseStruct_FieldsEntry(): Struct_FieldsEntry {\n  return { key: \"\", value: undefined };\n}\n\nexport const Struct_FieldsEntry: MessageFns<Struct_FieldsEntry> = {\n  encode(message: Struct_FieldsEntry, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {\n    if (message.key !== \"\") {\n      writer.uint32(10).string(message.key);\n    }\n    if (message.value !== undefined) {\n      Value.encode(Value.wrap(message.value), writer.uint32(18).fork()).join();\n    }\n    return writer;\n  },\n\n  decode(input: BinaryReader | Uint8Array, length?: number): Struct_FieldsEntry {\n    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);\n    let end = length === undefined ? reader.len : reader.pos + length;\n    const message = createBaseStruct_FieldsEntry();\n    while (reader.pos < end) {\n      const tag = reader.uint32();\n      switch (tag >>> 3) {\n        case 1: {\n          if (tag !== 10) {\n            break;\n          }\n\n          message.key = reader.string();\n          continue;\n        }\n        case 2: {\n          if (tag !== 18) {\n            break;\n          }\n\n          message.value = Value.unwrap(Value.decode(reader, reader.uint32()));\n          continue;\n        }\n      }\n      if ((tag & 7) === 4 || tag === 0) {\n        break;\n      }\n      reader.skip(tag & 7);\n    }\n    return message;\n  },\n\n  create<I extends Exact<DeepPartial<Struct_FieldsEntry>, I>>(base?: I): Struct_FieldsEntry {\n    return Struct_FieldsEntry.fromPartial(base ?? ({} as any));\n  },\n  fromPartial<I extends Exact<DeepPartial<Struct_FieldsEntry>, I>>(object: I): Struct_FieldsEntry {\n    const message = createBaseStruct_FieldsEntry();\n    message.key = object.key ?? \"\";\n    message.value = object.value ?? undefined;\n    return message;\n  },\n};\n\nfunction createBaseValue(): Value {\n  return {\n    nullValue: undefined,\n    numberValue: undefined,\n    stringValue: undefined,\n    boolValue: undefined,\n    structValue: undefined,\n    listValue: undefined,\n  };\n}\n\nexport const Value: MessageFns<Value> & AnyValueWrapperFns = {\n  encode(message: Value, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {\n    if (message.nullValue !== undefined) {\n      writer.uint32(8).int32(message.nullValue);\n    }\n    if (message.numberValue !== undefined) {\n      writer.uint32(17).double(message.numberValue);\n    }\n    if (message.stringValue !== undefined) {\n      writer.uint32(26).string(message.stringValue);\n    }\n    if (message.boolValue !== undefined) {\n      writer.uint32(32).bool(message.boolValue);\n    }\n    if (message.structValue !== undefined) {\n      Struct.encode(Struct.wrap(message.structValue), writer.uint32(42).fork()).join();\n    }\n    if (message.listValue !== undefined) {\n      ListValue.encode(ListValue.wrap(message.listValue), writer.uint32(50).fork()).join();\n    }\n    return writer;\n  },\n\n  decode(input: BinaryReader | Uint8Array, length?: number): Value {\n    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);\n    let end = length === undefined ? reader.len : reader.pos + length;\n    const message = createBaseValue();\n    while (reader.pos < end) {\n      const tag = reader.uint32();\n      switch (tag >>> 3) {\n        case 1: {\n          if (tag !== 8) {\n            break;\n          }\n\n          message.nullValue = reader.int32() as any;\n          continue;\n        }\n        case 2: {\n          if (tag !== 17) {\n            break;\n          }\n\n          message.numberValue = reader.double();\n          continue;\n        }\n        case 3: {\n          if (tag !== 26) {\n            break;\n          }\n\n          message.stringValue = reader.string();\n          continue;\n        }\n        case 4: {\n          if (tag !== 32) {\n            break;\n          }\n\n          message.boolValue = reader.bool();\n          continue;\n        }\n        case 5: {\n          if (tag !== 42) {\n            break;\n          }\n\n          message.structValue = Struct.unwrap(Struct.decode(reader, reader.uint32()));\n          continue;\n        }\n        case 6: {\n          if (tag !== 50) {\n            break;\n          }\n\n          message.listValue = ListValue.unwrap(ListValue.decode(reader, reader.uint32()));\n          continue;\n        }\n      }\n      if ((tag & 7) === 4 || tag === 0) {\n        break;\n      }\n      reader.skip(tag & 7);\n    }\n    return message;\n  },\n\n  create<I extends Exact<DeepPartial<Value>, I>>(base?: I): Value {\n    return Value.fromPartial(base ?? ({} as any));\n  },\n  fromPartial<I extends Exact<DeepPartial<Value>, I>>(object: I): Value {\n    const message = createBaseValue();\n    message.nullValue = object.nullValue ?? undefined;\n    message.numberValue = object.numberValue ?? undefined;\n    message.stringValue = object.stringValue ?? undefined;\n    message.boolValue = object.boolValue ?? undefined;\n    message.structValue = object.structValue ?? undefined;\n    message.listValue = object.listValue ?? undefined;\n    return message;\n  },\n\n  wrap(value: any): Value {\n    const result = createBaseValue();\n    if (value === null) {\n      result.nullValue = NullValue.NULL_VALUE;\n    } else if (typeof value === \"boolean\") {\n      result.boolValue = value;\n    } else if (typeof value === \"number\") {\n      result.numberValue = value;\n    } else if (typeof value === \"string\") {\n      result.stringValue = value;\n    } else if (globalThis.Array.isArray(value)) {\n      result.listValue = value;\n    } else if (typeof value === \"object\") {\n      result.structValue = value;\n    } else if (typeof value !== \"undefined\") {\n      throw new globalThis.Error(\"Unsupported any value type: \" + typeof value);\n    }\n    return result;\n  },\n\n  unwrap(message: any): string | number | boolean | Object | null | Array<any> | undefined {\n    if (message.stringValue !== undefined) {\n      return message.stringValue;\n    } else if (message?.numberValue !== undefined) {\n      return message.numberValue;\n    } else if (message?.boolValue !== undefined) {\n      return message.boolValue;\n    } else if (message?.structValue !== undefined) {\n      return message.structValue as any;\n    } else if (message?.listValue !== undefined) {\n      return message.listValue;\n    } else if (message?.nullValue !== undefined) {\n      return null;\n    }\n    return undefined;\n  },\n};\n\nfunction createBaseListValue(): ListValue {\n  return { values: [] };\n}\n\nexport const ListValue: MessageFns<ListValue> & ListValueWrapperFns = {\n  encode(message: ListValue, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {\n    for (const v of message.values) {\n      Value.encode(Value.wrap(v!), writer.uint32(10).fork()).join();\n    }\n    return writer;\n  },\n\n  decode(input: BinaryReader | Uint8Array, length?: number): ListValue {\n    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);\n    let end = length === undefined ? reader.len : reader.pos + length;\n    const message = createBaseListValue();\n    while (reader.pos < end) {\n      const tag = reader.uint32();\n      switch (tag >>> 3) {\n        case 1: {\n          if (tag !== 10) {\n            break;\n          }\n\n          message.values.push(Value.unwrap(Value.decode(reader, reader.uint32())));\n          continue;\n        }\n      }\n      if ((tag & 7) === 4 || tag === 0) {\n        break;\n      }\n      reader.skip(tag & 7);\n    }\n    return message;\n  },\n\n  create<I extends Exact<DeepPartial<ListValue>, I>>(base?: I): ListValue {\n    return ListValue.fromPartial(base ?? ({} as any));\n  },\n  fromPartial<I extends Exact<DeepPartial<ListValue>, I>>(object: I): ListValue {\n    const message = createBaseListValue();\n    message.values = object.values?.map((e) => e) || [];\n    return message;\n  },\n\n  wrap(array: Array<any> | undefined): ListValue {\n    const result = createBaseListValue();\n    result.values = array ?? [];\n    return result;\n  },\n\n  unwrap(message: ListValue): Array<any> {\n    if (message?.hasOwnProperty(\"values\") && globalThis.Array.isArray(message.values)) {\n      return message.values;\n    } else {\n      return message as any;\n    }\n  },\n};\n\ntype Builtin = Date | Function | Uint8Array | string | number | boolean | undefined;\n\nexport type DeepPartial<T> = T extends Builtin ? T\n  : T extends globalThis.Array<infer U> ? globalThis.Array<DeepPartial<U>>\n  : T extends ReadonlyArray<infer U> ? ReadonlyArray<DeepPartial<U>>\n  : T extends {} ? { [K in keyof T]?: DeepPartial<T[K]> }\n  : Partial<T>;\n\ntype KeysOfUnion<T> = T extends T ? keyof T : never;\nexport type Exact<P, I extends P> = P extends Builtin ? P\n  : P & { [K in keyof P]: Exact<P[K], I[K]> } & { [K in Exclude<keyof I, KeysOfUnion<P>>]: never };\n\nexport interface MessageFns<T> {\n  encode(message: T, writer?: BinaryWriter): BinaryWriter;\n  decode(input: BinaryReader | Uint8Array, length?: number): T;\n  create<I extends Exact<DeepPartial<T>, I>>(base?: I): T;\n  fromPartial<I extends Exact<DeepPartial<T>, I>>(object: I): T;\n}\n\nexport interface StructWrapperFns {\n  wrap(object: { [key: string]: any } | undefined): Struct;\n  unwrap(message: Struct): { [key: string]: any };\n}\n\nexport interface AnyValueWrapperFns {\n  wrap(value: any): Value;\n  unwrap(message: any): string | number | boolean | Object | null | Array<any> | undefined;\n}\n\nexport interface ListValueWrapperFns {\n  wrap(array: Array<any> | undefined): ListValue;\n  unwrap(message: ListValue): Array<any>;\n}\n", "// Code generated by protoc-gen-ts_proto. DO NOT EDIT.\n// versions:\n//   protoc-gen-ts_proto  v2.7.0\n//   protoc               v5.29.3\n// source: patch.proto\n\n/* eslint-disable */\nimport { BinaryReader, BinaryWriter } from \"@bufbuild/protobuf/wire\";\nimport { Value } from \"./google/protobuf/struct\";\n\nexport const protobufPackage = \"ag_ui\";\n\nexport enum JsonPatchOperationType {\n  ADD = 0,\n  REMOVE = 1,\n  REPLACE = 2,\n  MOVE = 3,\n  COPY = 4,\n  TEST = 5,\n  UNRECOGNIZED = -1,\n}\n\nexport interface JsonPatchOperation {\n  op: JsonPatchOperationType;\n  path: string;\n  from?: string | undefined;\n  value?: any | undefined;\n}\n\nfunction createBaseJsonPatchOperation(): JsonPatchOperation {\n  return { op: 0, path: \"\", from: undefined, value: undefined };\n}\n\nexport const JsonPatchOperation: MessageFns<JsonPatchOperation> = {\n  encode(message: Json<PERSON>atchOperation, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {\n    if (message.op !== 0) {\n      writer.uint32(8).int32(message.op);\n    }\n    if (message.path !== \"\") {\n      writer.uint32(18).string(message.path);\n    }\n    if (message.from !== undefined) {\n      writer.uint32(26).string(message.from);\n    }\n    if (message.value !== undefined) {\n      Value.encode(Value.wrap(message.value), writer.uint32(34).fork()).join();\n    }\n    return writer;\n  },\n\n  decode(input: BinaryReader | Uint8Array, length?: number): JsonPatchOperation {\n    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);\n    let end = length === undefined ? reader.len : reader.pos + length;\n    const message = createBaseJsonPatchOperation();\n    while (reader.pos < end) {\n      const tag = reader.uint32();\n      switch (tag >>> 3) {\n        case 1: {\n          if (tag !== 8) {\n            break;\n          }\n\n          message.op = reader.int32() as any;\n          continue;\n        }\n        case 2: {\n          if (tag !== 18) {\n            break;\n          }\n\n          message.path = reader.string();\n          continue;\n        }\n        case 3: {\n          if (tag !== 26) {\n            break;\n          }\n\n          message.from = reader.string();\n          continue;\n        }\n        case 4: {\n          if (tag !== 34) {\n            break;\n          }\n\n          message.value = Value.unwrap(Value.decode(reader, reader.uint32()));\n          continue;\n        }\n      }\n      if ((tag & 7) === 4 || tag === 0) {\n        break;\n      }\n      reader.skip(tag & 7);\n    }\n    return message;\n  },\n\n  create<I extends Exact<DeepPartial<JsonPatchOperation>, I>>(base?: I): JsonPatchOperation {\n    return JsonPatchOperation.fromPartial(base ?? ({} as any));\n  },\n  fromPartial<I extends Exact<DeepPartial<JsonPatchOperation>, I>>(object: I): JsonPatchOperation {\n    const message = createBaseJsonPatchOperation();\n    message.op = object.op ?? 0;\n    message.path = object.path ?? \"\";\n    message.from = object.from ?? undefined;\n    message.value = object.value ?? undefined;\n    return message;\n  },\n};\n\ntype Builtin = Date | Function | Uint8Array | string | number | boolean | undefined;\n\nexport type DeepPartial<T> = T extends Builtin ? T\n  : T extends globalThis.Array<infer U> ? globalThis.Array<DeepPartial<U>>\n  : T extends ReadonlyArray<infer U> ? ReadonlyArray<DeepPartial<U>>\n  : T extends {} ? { [K in keyof T]?: DeepPartial<T[K]> }\n  : Partial<T>;\n\ntype KeysOfUnion<T> = T extends T ? keyof T : never;\nexport type Exact<P, I extends P> = P extends Builtin ? P\n  : P & { [K in keyof P]: Exact<P[K], I[K]> } & { [K in Exclude<keyof I, KeysOfUnion<P>>]: never };\n\nexport interface MessageFns<T> {\n  encode(message: T, writer?: BinaryWriter): BinaryWriter;\n  decode(input: BinaryReader | Uint8Array, length?: number): T;\n  create<I extends Exact<DeepPartial<T>, I>>(base?: I): T;\n  fromPartial<I extends Exact<DeepPartial<T>, I>>(object: I): T;\n}\n", "// Code generated by protoc-gen-ts_proto. DO NOT EDIT.\n// versions:\n//   protoc-gen-ts_proto  v2.7.0\n//   protoc               v5.29.3\n// source: types.proto\n\n/* eslint-disable */\nimport { BinaryReader, BinaryWriter } from \"@bufbuild/protobuf/wire\";\n\nexport const protobufPackage = \"ag_ui\";\n\nexport interface ToolCall {\n  id: string;\n  type: string;\n  function: ToolCall_Function | undefined;\n}\n\nexport interface ToolCall_Function {\n  name: string;\n  arguments: string;\n}\n\nexport interface Message {\n  id: string;\n  role: string;\n  content?: string | undefined;\n  name?: string | undefined;\n  toolCalls: ToolCall[];\n  toolCallId?: string | undefined;\n}\n\nfunction createBaseToolCall(): ToolCall {\n  return { id: \"\", type: \"\", function: undefined };\n}\n\nexport const ToolCall: MessageFns<ToolCall> = {\n  encode(message: ToolCall, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {\n    if (message.id !== \"\") {\n      writer.uint32(10).string(message.id);\n    }\n    if (message.type !== \"\") {\n      writer.uint32(18).string(message.type);\n    }\n    if (message.function !== undefined) {\n      ToolCall_Function.encode(message.function, writer.uint32(26).fork()).join();\n    }\n    return writer;\n  },\n\n  decode(input: BinaryReader | Uint8Array, length?: number): ToolCall {\n    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);\n    let end = length === undefined ? reader.len : reader.pos + length;\n    const message = createBaseToolCall();\n    while (reader.pos < end) {\n      const tag = reader.uint32();\n      switch (tag >>> 3) {\n        case 1: {\n          if (tag !== 10) {\n            break;\n          }\n\n          message.id = reader.string();\n          continue;\n        }\n        case 2: {\n          if (tag !== 18) {\n            break;\n          }\n\n          message.type = reader.string();\n          continue;\n        }\n        case 3: {\n          if (tag !== 26) {\n            break;\n          }\n\n          message.function = ToolCall_Function.decode(reader, reader.uint32());\n          continue;\n        }\n      }\n      if ((tag & 7) === 4 || tag === 0) {\n        break;\n      }\n      reader.skip(tag & 7);\n    }\n    return message;\n  },\n\n  create<I extends Exact<DeepPartial<ToolCall>, I>>(base?: I): ToolCall {\n    return ToolCall.fromPartial(base ?? ({} as any));\n  },\n  fromPartial<I extends Exact<DeepPartial<ToolCall>, I>>(object: I): ToolCall {\n    const message = createBaseToolCall();\n    message.id = object.id ?? \"\";\n    message.type = object.type ?? \"\";\n    message.function = (object.function !== undefined && object.function !== null)\n      ? ToolCall_Function.fromPartial(object.function)\n      : undefined;\n    return message;\n  },\n};\n\nfunction createBaseToolCall_Function(): ToolCall_Function {\n  return { name: \"\", arguments: \"\" };\n}\n\nexport const ToolCall_Function: MessageFns<ToolCall_Function> = {\n  encode(message: ToolCall_Function, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {\n    if (message.name !== \"\") {\n      writer.uint32(10).string(message.name);\n    }\n    if (message.arguments !== \"\") {\n      writer.uint32(18).string(message.arguments);\n    }\n    return writer;\n  },\n\n  decode(input: BinaryReader | Uint8Array, length?: number): ToolCall_Function {\n    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);\n    let end = length === undefined ? reader.len : reader.pos + length;\n    const message = createBaseToolCall_Function();\n    while (reader.pos < end) {\n      const tag = reader.uint32();\n      switch (tag >>> 3) {\n        case 1: {\n          if (tag !== 10) {\n            break;\n          }\n\n          message.name = reader.string();\n          continue;\n        }\n        case 2: {\n          if (tag !== 18) {\n            break;\n          }\n\n          message.arguments = reader.string();\n          continue;\n        }\n      }\n      if ((tag & 7) === 4 || tag === 0) {\n        break;\n      }\n      reader.skip(tag & 7);\n    }\n    return message;\n  },\n\n  create<I extends Exact<DeepPartial<ToolCall_Function>, I>>(base?: I): ToolCall_Function {\n    return ToolCall_Function.fromPartial(base ?? ({} as any));\n  },\n  fromPartial<I extends Exact<DeepPartial<ToolCall_Function>, I>>(object: I): ToolCall_Function {\n    const message = createBaseToolCall_Function();\n    message.name = object.name ?? \"\";\n    message.arguments = object.arguments ?? \"\";\n    return message;\n  },\n};\n\nfunction createBaseMessage(): Message {\n  return { id: \"\", role: \"\", content: undefined, name: undefined, toolCalls: [], toolCallId: undefined };\n}\n\nexport const Message: MessageFns<Message> = {\n  encode(message: Message, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {\n    if (message.id !== \"\") {\n      writer.uint32(10).string(message.id);\n    }\n    if (message.role !== \"\") {\n      writer.uint32(18).string(message.role);\n    }\n    if (message.content !== undefined) {\n      writer.uint32(26).string(message.content);\n    }\n    if (message.name !== undefined) {\n      writer.uint32(34).string(message.name);\n    }\n    for (const v of message.toolCalls) {\n      ToolCall.encode(v!, writer.uint32(42).fork()).join();\n    }\n    if (message.toolCallId !== undefined) {\n      writer.uint32(50).string(message.toolCallId);\n    }\n    return writer;\n  },\n\n  decode(input: BinaryReader | Uint8Array, length?: number): Message {\n    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);\n    let end = length === undefined ? reader.len : reader.pos + length;\n    const message = createBaseMessage();\n    while (reader.pos < end) {\n      const tag = reader.uint32();\n      switch (tag >>> 3) {\n        case 1: {\n          if (tag !== 10) {\n            break;\n          }\n\n          message.id = reader.string();\n          continue;\n        }\n        case 2: {\n          if (tag !== 18) {\n            break;\n          }\n\n          message.role = reader.string();\n          continue;\n        }\n        case 3: {\n          if (tag !== 26) {\n            break;\n          }\n\n          message.content = reader.string();\n          continue;\n        }\n        case 4: {\n          if (tag !== 34) {\n            break;\n          }\n\n          message.name = reader.string();\n          continue;\n        }\n        case 5: {\n          if (tag !== 42) {\n            break;\n          }\n\n          message.toolCalls.push(ToolCall.decode(reader, reader.uint32()));\n          continue;\n        }\n        case 6: {\n          if (tag !== 50) {\n            break;\n          }\n\n          message.toolCallId = reader.string();\n          continue;\n        }\n      }\n      if ((tag & 7) === 4 || tag === 0) {\n        break;\n      }\n      reader.skip(tag & 7);\n    }\n    return message;\n  },\n\n  create<I extends Exact<DeepPartial<Message>, I>>(base?: I): Message {\n    return Message.fromPartial(base ?? ({} as any));\n  },\n  fromPartial<I extends Exact<DeepPartial<Message>, I>>(object: I): Message {\n    const message = createBaseMessage();\n    message.id = object.id ?? \"\";\n    message.role = object.role ?? \"\";\n    message.content = object.content ?? undefined;\n    message.name = object.name ?? undefined;\n    message.toolCalls = object.toolCalls?.map((e) => ToolCall.fromPartial(e)) || [];\n    message.toolCallId = object.toolCallId ?? undefined;\n    return message;\n  },\n};\n\ntype Builtin = Date | Function | Uint8Array | string | number | boolean | undefined;\n\nexport type DeepPartial<T> = T extends Builtin ? T\n  : T extends globalThis.Array<infer U> ? globalThis.Array<DeepPartial<U>>\n  : T extends ReadonlyArray<infer U> ? ReadonlyArray<DeepPartial<U>>\n  : T extends {} ? { [K in keyof T]?: DeepPartial<T[K]> }\n  : Partial<T>;\n\ntype KeysOfUnion<T> = T extends T ? keyof T : never;\nexport type Exact<P, I extends P> = P extends Builtin ? P\n  : P & { [K in keyof P]: Exact<P[K], I[K]> } & { [K in Exclude<keyof I, KeysOfUnion<P>>]: never };\n\nexport interface MessageFns<T> {\n  encode(message: T, writer?: BinaryWriter): BinaryWriter;\n  decode(input: BinaryReader | Uint8Array, length?: number): T;\n  create<I extends Exact<DeepPartial<T>, I>>(base?: I): T;\n  fromPartial<I extends Exact<DeepPartial<T>, I>>(object: I): T;\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;ACAA,kBAA4D;;;ACO5D,IAAAA,eAA2C;;;ACA3C,kBAA2C;AA+E3C,SAAS,mBAA2B;AAClC,SAAO,EAAE,QAAQ,CAAC,EAAE;AACtB;AAEO,IAAM,SAAgD;AAAA,EAC3D,OAAO,SAAiB,SAAuB,IAAI,yBAAa,GAAiB;AAC/E,WAAO,QAAQ,QAAQ,MAAM,EAAE,QAAQ,CAAC,CAAC,KAAK,KAAK,MAAM;AACvD,UAAI,UAAU,QAAW;AACvB,2BAAmB,OAAO,EAAE,KAAiB,MAAM,GAAG,OAAO,OAAO,EAAE,EAAE,KAAK,CAAC,EAAE,KAAK;AAAA,MACvF;AAAA,IACF,CAAC;AACD,WAAO;AAAA,EACT;AAAA,EAEA,OAAO,OAAkC,QAAyB;AAChE,UAAM,SAAS,iBAAiB,2BAAe,QAAQ,IAAI,yBAAa,KAAK;AAC7E,QAAI,MAAM,WAAW,SAAY,OAAO,MAAM,OAAO,MAAM;AAC3D,UAAM,UAAU,iBAAiB;AACjC,WAAO,OAAO,MAAM,KAAK;AACvB,YAAM,MAAM,OAAO,OAAO;AAC1B,cAAQ,QAAQ,GAAG;AAAA,QACjB,KAAK,GAAG;AACN,cAAI,QAAQ,IAAI;AACd;AAAA,UACF;AAEA,gBAAM,SAAS,mBAAmB,OAAO,QAAQ,OAAO,OAAO,CAAC;AAChE,cAAI,OAAO,UAAU,QAAW;AAC9B,oBAAQ,OAAO,OAAO,GAAG,IAAI,OAAO;AAAA,UACtC;AACA;AAAA,QACF;AAAA,MACF;AACA,WAAK,MAAM,OAAO,KAAK,QAAQ,GAAG;AAChC;AAAA,MACF;AACA,aAAO,KAAK,MAAM,CAAC;AAAA,IACrB;AACA,WAAO;AAAA,EACT;AAAA,EAEA,OAAgD,MAAkB;AAChE,WAAO,OAAO,YAAY,sBAAS,CAAC,CAAS;AAAA,EAC/C;AAAA,EACA,YAAqD,QAAmB;AAlI1E;AAmII,UAAM,UAAU,iBAAiB;AACjC,YAAQ,SAAS,OAAO,SAAQ,YAAO,WAAP,YAAiB,CAAC,CAAC,EAAE;AAAA,MACnD,CAAC,KAAK,CAAC,KAAK,KAAK,MAAM;AACrB,YAAI,UAAU,QAAW;AACvB,cAAI,GAAG,IAAI;AAAA,QACb;AACA,eAAO;AAAA,MACT;AAAA,MACA,CAAC;AAAA,IACH;AACA,WAAO;AAAA,EACT;AAAA,EAEA,KAAK,QAAoD;AACvD,UAAM,SAAS,iBAAiB;AAEhC,QAAI,WAAW,QAAW;AACxB,iBAAW,OAAO,OAAO,KAAK,MAAM,GAAG;AACrC,eAAO,OAAO,GAAG,IAAI,OAAO,GAAG;AAAA,MACjC;AAAA,IACF;AACA,WAAO;AAAA,EACT;AAAA,EAEA,OAAO,SAAyC;AAC9C,UAAM,SAAiC,CAAC;AACxC,QAAI,QAAQ,QAAQ;AAClB,iBAAW,OAAO,OAAO,KAAK,QAAQ,MAAM,GAAG;AAC7C,eAAO,GAAG,IAAI,QAAQ,OAAO,GAAG;AAAA,MAClC;AAAA,IACF;AACA,WAAO;AAAA,EACT;AACF;AAEA,SAAS,+BAAmD;AAC1D,SAAO,EAAE,KAAK,IAAI,OAAO,OAAU;AACrC;AAEO,IAAM,qBAAqD;AAAA,EAChE,OAAO,SAA6B,SAAuB,IAAI,yBAAa,GAAiB;AAC3F,QAAI,QAAQ,QAAQ,IAAI;AACtB,aAAO,OAAO,EAAE,EAAE,OAAO,QAAQ,GAAG;AAAA,IACtC;AACA,QAAI,QAAQ,UAAU,QAAW;AAC/B,YAAM,OAAO,MAAM,KAAK,QAAQ,KAAK,GAAG,OAAO,OAAO,EAAE,EAAE,KAAK,CAAC,EAAE,KAAK;AAAA,IACzE;AACA,WAAO;AAAA,EACT;AAAA,EAEA,OAAO,OAAkC,QAAqC;AAC5E,UAAM,SAAS,iBAAiB,2BAAe,QAAQ,IAAI,yBAAa,KAAK;AAC7E,QAAI,MAAM,WAAW,SAAY,OAAO,MAAM,OAAO,MAAM;AAC3D,UAAM,UAAU,6BAA6B;AAC7C,WAAO,OAAO,MAAM,KAAK;AACvB,YAAM,MAAM,OAAO,OAAO;AAC1B,cAAQ,QAAQ,GAAG;AAAA,QACjB,KAAK,GAAG;AACN,cAAI,QAAQ,IAAI;AACd;AAAA,UACF;AAEA,kBAAQ,MAAM,OAAO,OAAO;AAC5B;AAAA,QACF;AAAA,QACA,KAAK,GAAG;AACN,cAAI,QAAQ,IAAI;AACd;AAAA,UACF;AAEA,kBAAQ,QAAQ,MAAM,OAAO,MAAM,OAAO,QAAQ,OAAO,OAAO,CAAC,CAAC;AAClE;AAAA,QACF;AAAA,MACF;AACA,WAAK,MAAM,OAAO,KAAK,QAAQ,GAAG;AAChC;AAAA,MACF;AACA,aAAO,KAAK,MAAM,CAAC;AAAA,IACrB;AACA,WAAO;AAAA,EACT;AAAA,EAEA,OAA4D,MAA8B;AACxF,WAAO,mBAAmB,YAAY,sBAAS,CAAC,CAAS;AAAA,EAC3D;AAAA,EACA,YAAiE,QAA+B;AAxNlG;AAyNI,UAAM,UAAU,6BAA6B;AAC7C,YAAQ,OAAM,YAAO,QAAP,YAAc;AAC5B,YAAQ,SAAQ,YAAO,UAAP,YAAgB;AAChC,WAAO;AAAA,EACT;AACF;AAEA,SAAS,kBAAyB;AAChC,SAAO;AAAA,IACL,WAAW;AAAA,IACX,aAAa;AAAA,IACb,aAAa;AAAA,IACb,WAAW;AAAA,IACX,aAAa;AAAA,IACb,WAAW;AAAA,EACb;AACF;AAEO,IAAM,QAAgD;AAAA,EAC3D,OAAO,SAAgB,SAAuB,IAAI,yBAAa,GAAiB;AAC9E,QAAI,QAAQ,cAAc,QAAW;AACnC,aAAO,OAAO,CAAC,EAAE,MAAM,QAAQ,SAAS;AAAA,IAC1C;AACA,QAAI,QAAQ,gBAAgB,QAAW;AACrC,aAAO,OAAO,EAAE,EAAE,OAAO,QAAQ,WAAW;AAAA,IAC9C;AACA,QAAI,QAAQ,gBAAgB,QAAW;AACrC,aAAO,OAAO,EAAE,EAAE,OAAO,QAAQ,WAAW;AAAA,IAC9C;AACA,QAAI,QAAQ,cAAc,QAAW;AACnC,aAAO,OAAO,EAAE,EAAE,KAAK,QAAQ,SAAS;AAAA,IAC1C;AACA,QAAI,QAAQ,gBAAgB,QAAW;AACrC,aAAO,OAAO,OAAO,KAAK,QAAQ,WAAW,GAAG,OAAO,OAAO,EAAE,EAAE,KAAK,CAAC,EAAE,KAAK;AAAA,IACjF;AACA,QAAI,QAAQ,cAAc,QAAW;AACnC,gBAAU,OAAO,UAAU,KAAK,QAAQ,SAAS,GAAG,OAAO,OAAO,EAAE,EAAE,KAAK,CAAC,EAAE,KAAK;AAAA,IACrF;AACA,WAAO;AAAA,EACT;AAAA,EAEA,OAAO,OAAkC,QAAwB;AAC/D,UAAM,SAAS,iBAAiB,2BAAe,QAAQ,IAAI,yBAAa,KAAK;AAC7E,QAAI,MAAM,WAAW,SAAY,OAAO,MAAM,OAAO,MAAM;AAC3D,UAAM,UAAU,gBAAgB;AAChC,WAAO,OAAO,MAAM,KAAK;AACvB,YAAM,MAAM,OAAO,OAAO;AAC1B,cAAQ,QAAQ,GAAG;AAAA,QACjB,KAAK,GAAG;AACN,cAAI,QAAQ,GAAG;AACb;AAAA,UACF;AAEA,kBAAQ,YAAY,OAAO,MAAM;AACjC;AAAA,QACF;AAAA,QACA,KAAK,GAAG;AACN,cAAI,QAAQ,IAAI;AACd;AAAA,UACF;AAEA,kBAAQ,cAAc,OAAO,OAAO;AACpC;AAAA,QACF;AAAA,QACA,KAAK,GAAG;AACN,cAAI,QAAQ,IAAI;AACd;AAAA,UACF;AAEA,kBAAQ,cAAc,OAAO,OAAO;AACpC;AAAA,QACF;AAAA,QACA,KAAK,GAAG;AACN,cAAI,QAAQ,IAAI;AACd;AAAA,UACF;AAEA,kBAAQ,YAAY,OAAO,KAAK;AAChC;AAAA,QACF;AAAA,QACA,KAAK,GAAG;AACN,cAAI,QAAQ,IAAI;AACd;AAAA,UACF;AAEA,kBAAQ,cAAc,OAAO,OAAO,OAAO,OAAO,QAAQ,OAAO,OAAO,CAAC,CAAC;AAC1E;AAAA,QACF;AAAA,QACA,KAAK,GAAG;AACN,cAAI,QAAQ,IAAI;AACd;AAAA,UACF;AAEA,kBAAQ,YAAY,UAAU,OAAO,UAAU,OAAO,QAAQ,OAAO,OAAO,CAAC,CAAC;AAC9E;AAAA,QACF;AAAA,MACF;AACA,WAAK,MAAM,OAAO,KAAK,QAAQ,GAAG;AAChC;AAAA,MACF;AACA,aAAO,KAAK,MAAM,CAAC;AAAA,IACrB;AACA,WAAO;AAAA,EACT;AAAA,EAEA,OAA+C,MAAiB;AAC9D,WAAO,MAAM,YAAY,sBAAS,CAAC,CAAS;AAAA,EAC9C;AAAA,EACA,YAAoD,QAAkB;AArUxE;AAsUI,UAAM,UAAU,gBAAgB;AAChC,YAAQ,aAAY,YAAO,cAAP,YAAoB;AACxC,YAAQ,eAAc,YAAO,gBAAP,YAAsB;AAC5C,YAAQ,eAAc,YAAO,gBAAP,YAAsB;AAC5C,YAAQ,aAAY,YAAO,cAAP,YAAoB;AACxC,YAAQ,eAAc,YAAO,gBAAP,YAAsB;AAC5C,YAAQ,aAAY,YAAO,cAAP,YAAoB;AACxC,WAAO;AAAA,EACT;AAAA,EAEA,KAAK,OAAmB;AACtB,UAAM,SAAS,gBAAgB;AAC/B,QAAI,UAAU,MAAM;AAClB,aAAO,YAAY;AAAA,IACrB,WAAW,OAAO,UAAU,WAAW;AACrC,aAAO,YAAY;AAAA,IACrB,WAAW,OAAO,UAAU,UAAU;AACpC,aAAO,cAAc;AAAA,IACvB,WAAW,OAAO,UAAU,UAAU;AACpC,aAAO,cAAc;AAAA,IACvB,WAAW,WAAW,MAAM,QAAQ,KAAK,GAAG;AAC1C,aAAO,YAAY;AAAA,IACrB,WAAW,OAAO,UAAU,UAAU;AACpC,aAAO,cAAc;AAAA,IACvB,WAAW,OAAO,UAAU,aAAa;AACvC,YAAM,IAAI,WAAW,MAAM,iCAAiC,OAAO,KAAK;AAAA,IAC1E;AACA,WAAO;AAAA,EACT;AAAA,EAEA,OAAO,SAAkF;AACvF,QAAI,QAAQ,gBAAgB,QAAW;AACrC,aAAO,QAAQ;AAAA,IACjB,YAAW,mCAAS,iBAAgB,QAAW;AAC7C,aAAO,QAAQ;AAAA,IACjB,YAAW,mCAAS,eAAc,QAAW;AAC3C,aAAO,QAAQ;AAAA,IACjB,YAAW,mCAAS,iBAAgB,QAAW;AAC7C,aAAO,QAAQ;AAAA,IACjB,YAAW,mCAAS,eAAc,QAAW;AAC3C,aAAO,QAAQ;AAAA,IACjB,YAAW,mCAAS,eAAc,QAAW;AAC3C,aAAO;AAAA,IACT;AACA,WAAO;AAAA,EACT;AACF;AAEA,SAAS,sBAAiC;AACxC,SAAO,EAAE,QAAQ,CAAC,EAAE;AACtB;AAEO,IAAM,YAAyD;AAAA,EACpE,OAAO,SAAoB,SAAuB,IAAI,yBAAa,GAAiB;AAClF,eAAW,KAAK,QAAQ,QAAQ;AAC9B,YAAM,OAAO,MAAM,KAAK,CAAE,GAAG,OAAO,OAAO,EAAE,EAAE,KAAK,CAAC,EAAE,KAAK;AAAA,IAC9D;AACA,WAAO;AAAA,EACT;AAAA,EAEA,OAAO,OAAkC,QAA4B;AACnE,UAAM,SAAS,iBAAiB,2BAAe,QAAQ,IAAI,yBAAa,KAAK;AAC7E,QAAI,MAAM,WAAW,SAAY,OAAO,MAAM,OAAO,MAAM;AAC3D,UAAM,UAAU,oBAAoB;AACpC,WAAO,OAAO,MAAM,KAAK;AACvB,YAAM,MAAM,OAAO,OAAO;AAC1B,cAAQ,QAAQ,GAAG;AAAA,QACjB,KAAK,GAAG;AACN,cAAI,QAAQ,IAAI;AACd;AAAA,UACF;AAEA,kBAAQ,OAAO,KAAK,MAAM,OAAO,MAAM,OAAO,QAAQ,OAAO,OAAO,CAAC,CAAC,CAAC;AACvE;AAAA,QACF;AAAA,MACF;AACA,WAAK,MAAM,OAAO,KAAK,QAAQ,GAAG;AAChC;AAAA,MACF;AACA,aAAO,KAAK,MAAM,CAAC;AAAA,IACrB;AACA,WAAO;AAAA,EACT;AAAA,EAEA,OAAmD,MAAqB;AACtE,WAAO,UAAU,YAAY,sBAAS,CAAC,CAAS;AAAA,EAClD;AAAA,EACA,YAAwD,QAAsB;AA7ZhF;AA8ZI,UAAM,UAAU,oBAAoB;AACpC,YAAQ,WAAS,YAAO,WAAP,mBAAe,IAAI,CAAC,MAAM,OAAM,CAAC;AAClD,WAAO;AAAA,EACT;AAAA,EAEA,KAAK,OAA0C;AAC7C,UAAM,SAAS,oBAAoB;AACnC,WAAO,SAAS,wBAAS,CAAC;AAC1B,WAAO;AAAA,EACT;AAAA,EAEA,OAAO,SAAgC;AACrC,SAAI,mCAAS,eAAe,cAAa,WAAW,MAAM,QAAQ,QAAQ,MAAM,GAAG;AACjF,aAAO,QAAQ;AAAA,IACjB,OAAO;AACL,aAAO;AAAA,IACT;AAAA,EACF;AACF;;;ACzaA,IAAAC,eAA2C;AAKpC,IAAK,yBAAL,kBAAKC,4BAAL;AACL,EAAAA,gDAAA,SAAM,KAAN;AACA,EAAAA,gDAAA,YAAS,KAAT;AACA,EAAAA,gDAAA,aAAU,KAAV;AACA,EAAAA,gDAAA,UAAO,KAAP;AACA,EAAAA,gDAAA,UAAO,KAAP;AACA,EAAAA,gDAAA,UAAO,KAAP;AACA,EAAAA,gDAAA,kBAAe,MAAf;AAPU,SAAAA;AAAA,GAAA;AAiBZ,SAAS,+BAAmD;AAC1D,SAAO,EAAE,IAAI,GAAG,MAAM,IAAI,MAAM,QAAW,OAAO,OAAU;AAC9D;AAEO,IAAM,qBAAqD;AAAA,EAChE,OAAO,SAA6B,SAAuB,IAAI,0BAAa,GAAiB;AAC3F,QAAI,QAAQ,OAAO,GAAG;AACpB,aAAO,OAAO,CAAC,EAAE,MAAM,QAAQ,EAAE;AAAA,IACnC;AACA,QAAI,QAAQ,SAAS,IAAI;AACvB,aAAO,OAAO,EAAE,EAAE,OAAO,QAAQ,IAAI;AAAA,IACvC;AACA,QAAI,QAAQ,SAAS,QAAW;AAC9B,aAAO,OAAO,EAAE,EAAE,OAAO,QAAQ,IAAI;AAAA,IACvC;AACA,QAAI,QAAQ,UAAU,QAAW;AAC/B,YAAM,OAAO,MAAM,KAAK,QAAQ,KAAK,GAAG,OAAO,OAAO,EAAE,EAAE,KAAK,CAAC,EAAE,KAAK;AAAA,IACzE;AACA,WAAO;AAAA,EACT;AAAA,EAEA,OAAO,OAAkC,QAAqC;AAC5E,UAAM,SAAS,iBAAiB,4BAAe,QAAQ,IAAI,0BAAa,KAAK;AAC7E,QAAI,MAAM,WAAW,SAAY,OAAO,MAAM,OAAO,MAAM;AAC3D,UAAM,UAAU,6BAA6B;AAC7C,WAAO,OAAO,MAAM,KAAK;AACvB,YAAM,MAAM,OAAO,OAAO;AAC1B,cAAQ,QAAQ,GAAG;AAAA,QACjB,KAAK,GAAG;AACN,cAAI,QAAQ,GAAG;AACb;AAAA,UACF;AAEA,kBAAQ,KAAK,OAAO,MAAM;AAC1B;AAAA,QACF;AAAA,QACA,KAAK,GAAG;AACN,cAAI,QAAQ,IAAI;AACd;AAAA,UACF;AAEA,kBAAQ,OAAO,OAAO,OAAO;AAC7B;AAAA,QACF;AAAA,QACA,KAAK,GAAG;AACN,cAAI,QAAQ,IAAI;AACd;AAAA,UACF;AAEA,kBAAQ,OAAO,OAAO,OAAO;AAC7B;AAAA,QACF;AAAA,QACA,KAAK,GAAG;AACN,cAAI,QAAQ,IAAI;AACd;AAAA,UACF;AAEA,kBAAQ,QAAQ,MAAM,OAAO,MAAM,OAAO,QAAQ,OAAO,OAAO,CAAC,CAAC;AAClE;AAAA,QACF;AAAA,MACF;AACA,WAAK,MAAM,OAAO,KAAK,QAAQ,GAAG;AAChC;AAAA,MACF;AACA,aAAO,KAAK,MAAM,CAAC;AAAA,IACrB;AACA,WAAO;AAAA,EACT;AAAA,EAEA,OAA4D,MAA8B;AACxF,WAAO,mBAAmB,YAAY,sBAAS,CAAC,CAAS;AAAA,EAC3D;AAAA,EACA,YAAiE,QAA+B;AArGlG;AAsGI,UAAM,UAAU,6BAA6B;AAC7C,YAAQ,MAAK,YAAO,OAAP,YAAa;AAC1B,YAAQ,QAAO,YAAO,SAAP,YAAe;AAC9B,YAAQ,QAAO,YAAO,SAAP,YAAe;AAC9B,YAAQ,SAAQ,YAAO,UAAP,YAAgB;AAChC,WAAO;AAAA,EACT;AACF;;;ACtGA,IAAAC,eAA2C;AAwB3C,SAAS,qBAA+B;AACtC,SAAO,EAAE,IAAI,IAAI,MAAM,IAAI,UAAU,OAAU;AACjD;AAEO,IAAM,WAAiC;AAAA,EAC5C,OAAO,SAAmB,SAAuB,IAAI,0BAAa,GAAiB;AACjF,QAAI,QAAQ,OAAO,IAAI;AACrB,aAAO,OAAO,EAAE,EAAE,OAAO,QAAQ,EAAE;AAAA,IACrC;AACA,QAAI,QAAQ,SAAS,IAAI;AACvB,aAAO,OAAO,EAAE,EAAE,OAAO,QAAQ,IAAI;AAAA,IACvC;AACA,QAAI,QAAQ,aAAa,QAAW;AAClC,wBAAkB,OAAO,QAAQ,UAAU,OAAO,OAAO,EAAE,EAAE,KAAK,CAAC,EAAE,KAAK;AAAA,IAC5E;AACA,WAAO;AAAA,EACT;AAAA,EAEA,OAAO,OAAkC,QAA2B;AAClE,UAAM,SAAS,iBAAiB,4BAAe,QAAQ,IAAI,0BAAa,KAAK;AAC7E,QAAI,MAAM,WAAW,SAAY,OAAO,MAAM,OAAO,MAAM;AAC3D,UAAM,UAAU,mBAAmB;AACnC,WAAO,OAAO,MAAM,KAAK;AACvB,YAAM,MAAM,OAAO,OAAO;AAC1B,cAAQ,QAAQ,GAAG;AAAA,QACjB,KAAK,GAAG;AACN,cAAI,QAAQ,IAAI;AACd;AAAA,UACF;AAEA,kBAAQ,KAAK,OAAO,OAAO;AAC3B;AAAA,QACF;AAAA,QACA,KAAK,GAAG;AACN,cAAI,QAAQ,IAAI;AACd;AAAA,UACF;AAEA,kBAAQ,OAAO,OAAO,OAAO;AAC7B;AAAA,QACF;AAAA,QACA,KAAK,GAAG;AACN,cAAI,QAAQ,IAAI;AACd;AAAA,UACF;AAEA,kBAAQ,WAAW,kBAAkB,OAAO,QAAQ,OAAO,OAAO,CAAC;AACnE;AAAA,QACF;AAAA,MACF;AACA,WAAK,MAAM,OAAO,KAAK,QAAQ,GAAG;AAChC;AAAA,MACF;AACA,aAAO,KAAK,MAAM,CAAC;AAAA,IACrB;AACA,WAAO;AAAA,EACT;AAAA,EAEA,OAAkD,MAAoB;AACpE,WAAO,SAAS,YAAY,sBAAS,CAAC,CAAS;AAAA,EACjD;AAAA,EACA,YAAuD,QAAqB;AA5F9E;AA6FI,UAAM,UAAU,mBAAmB;AACnC,YAAQ,MAAK,YAAO,OAAP,YAAa;AAC1B,YAAQ,QAAO,YAAO,SAAP,YAAe;AAC9B,YAAQ,WAAY,OAAO,aAAa,UAAa,OAAO,aAAa,OACrE,kBAAkB,YAAY,OAAO,QAAQ,IAC7C;AACJ,WAAO;AAAA,EACT;AACF;AAEA,SAAS,8BAAiD;AACxD,SAAO,EAAE,MAAM,IAAI,WAAW,GAAG;AACnC;AAEO,IAAM,oBAAmD;AAAA,EAC9D,OAAO,SAA4B,SAAuB,IAAI,0BAAa,GAAiB;AAC1F,QAAI,QAAQ,SAAS,IAAI;AACvB,aAAO,OAAO,EAAE,EAAE,OAAO,QAAQ,IAAI;AAAA,IACvC;AACA,QAAI,QAAQ,cAAc,IAAI;AAC5B,aAAO,OAAO,EAAE,EAAE,OAAO,QAAQ,SAAS;AAAA,IAC5C;AACA,WAAO;AAAA,EACT;AAAA,EAEA,OAAO,OAAkC,QAAoC;AAC3E,UAAM,SAAS,iBAAiB,4BAAe,QAAQ,IAAI,0BAAa,KAAK;AAC7E,QAAI,MAAM,WAAW,SAAY,OAAO,MAAM,OAAO,MAAM;AAC3D,UAAM,UAAU,4BAA4B;AAC5C,WAAO,OAAO,MAAM,KAAK;AACvB,YAAM,MAAM,OAAO,OAAO;AAC1B,cAAQ,QAAQ,GAAG;AAAA,QACjB,KAAK,GAAG;AACN,cAAI,QAAQ,IAAI;AACd;AAAA,UACF;AAEA,kBAAQ,OAAO,OAAO,OAAO;AAC7B;AAAA,QACF;AAAA,QACA,KAAK,GAAG;AACN,cAAI,QAAQ,IAAI;AACd;AAAA,UACF;AAEA,kBAAQ,YAAY,OAAO,OAAO;AAClC;AAAA,QACF;AAAA,MACF;AACA,WAAK,MAAM,OAAO,KAAK,QAAQ,GAAG;AAChC;AAAA,MACF;AACA,aAAO,KAAK,MAAM,CAAC;AAAA,IACrB;AACA,WAAO;AAAA,EACT;AAAA,EAEA,OAA2D,MAA6B;AACtF,WAAO,kBAAkB,YAAY,sBAAS,CAAC,CAAS;AAAA,EAC1D;AAAA,EACA,YAAgE,QAA8B;AAzJhG;AA0JI,UAAM,UAAU,4BAA4B;AAC5C,YAAQ,QAAO,YAAO,SAAP,YAAe;AAC9B,YAAQ,aAAY,YAAO,cAAP,YAAoB;AACxC,WAAO;AAAA,EACT;AACF;AAEA,SAAS,oBAA6B;AACpC,SAAO,EAAE,IAAI,IAAI,MAAM,IAAI,SAAS,QAAW,MAAM,QAAW,WAAW,CAAC,GAAG,YAAY,OAAU;AACvG;AAEO,IAAM,UAA+B;AAAA,EAC1C,OAAO,SAAkB,SAAuB,IAAI,0BAAa,GAAiB;AAChF,QAAI,QAAQ,OAAO,IAAI;AACrB,aAAO,OAAO,EAAE,EAAE,OAAO,QAAQ,EAAE;AAAA,IACrC;AACA,QAAI,QAAQ,SAAS,IAAI;AACvB,aAAO,OAAO,EAAE,EAAE,OAAO,QAAQ,IAAI;AAAA,IACvC;AACA,QAAI,QAAQ,YAAY,QAAW;AACjC,aAAO,OAAO,EAAE,EAAE,OAAO,QAAQ,OAAO;AAAA,IAC1C;AACA,QAAI,QAAQ,SAAS,QAAW;AAC9B,aAAO,OAAO,EAAE,EAAE,OAAO,QAAQ,IAAI;AAAA,IACvC;AACA,eAAW,KAAK,QAAQ,WAAW;AACjC,eAAS,OAAO,GAAI,OAAO,OAAO,EAAE,EAAE,KAAK,CAAC,EAAE,KAAK;AAAA,IACrD;AACA,QAAI,QAAQ,eAAe,QAAW;AACpC,aAAO,OAAO,EAAE,EAAE,OAAO,QAAQ,UAAU;AAAA,IAC7C;AACA,WAAO;AAAA,EACT;AAAA,EAEA,OAAO,OAAkC,QAA0B;AACjE,UAAM,SAAS,iBAAiB,4BAAe,QAAQ,IAAI,0BAAa,KAAK;AAC7E,QAAI,MAAM,WAAW,SAAY,OAAO,MAAM,OAAO,MAAM;AAC3D,UAAM,UAAU,kBAAkB;AAClC,WAAO,OAAO,MAAM,KAAK;AACvB,YAAM,MAAM,OAAO,OAAO;AAC1B,cAAQ,QAAQ,GAAG;AAAA,QACjB,KAAK,GAAG;AACN,cAAI,QAAQ,IAAI;AACd;AAAA,UACF;AAEA,kBAAQ,KAAK,OAAO,OAAO;AAC3B;AAAA,QACF;AAAA,QACA,KAAK,GAAG;AACN,cAAI,QAAQ,IAAI;AACd;AAAA,UACF;AAEA,kBAAQ,OAAO,OAAO,OAAO;AAC7B;AAAA,QACF;AAAA,QACA,KAAK,GAAG;AACN,cAAI,QAAQ,IAAI;AACd;AAAA,UACF;AAEA,kBAAQ,UAAU,OAAO,OAAO;AAChC;AAAA,QACF;AAAA,QACA,KAAK,GAAG;AACN,cAAI,QAAQ,IAAI;AACd;AAAA,UACF;AAEA,kBAAQ,OAAO,OAAO,OAAO;AAC7B;AAAA,QACF;AAAA,QACA,KAAK,GAAG;AACN,cAAI,QAAQ,IAAI;AACd;AAAA,UACF;AAEA,kBAAQ,UAAU,KAAK,SAAS,OAAO,QAAQ,OAAO,OAAO,CAAC,CAAC;AAC/D;AAAA,QACF;AAAA,QACA,KAAK,GAAG;AACN,cAAI,QAAQ,IAAI;AACd;AAAA,UACF;AAEA,kBAAQ,aAAa,OAAO,OAAO;AACnC;AAAA,QACF;AAAA,MACF;AACA,WAAK,MAAM,OAAO,KAAK,QAAQ,GAAG;AAChC;AAAA,MACF;AACA,aAAO,KAAK,MAAM,CAAC;AAAA,IACrB;AACA,WAAO;AAAA,EACT;AAAA,EAEA,OAAiD,MAAmB;AAClE,WAAO,QAAQ,YAAY,sBAAS,CAAC,CAAS;AAAA,EAChD;AAAA,EACA,YAAsD,QAAoB;AA/P5E;AAgQI,UAAM,UAAU,kBAAkB;AAClC,YAAQ,MAAK,YAAO,OAAP,YAAa;AAC1B,YAAQ,QAAO,YAAO,SAAP,YAAe;AAC9B,YAAQ,WAAU,YAAO,YAAP,YAAkB;AACpC,YAAQ,QAAO,YAAO,SAAP,YAAe;AAC9B,YAAQ,cAAY,YAAO,cAAP,mBAAkB,IAAI,CAAC,MAAM,SAAS,YAAY,CAAC,OAAM,CAAC;AAC9E,YAAQ,cAAa,YAAO,eAAP,YAAqB;AAC1C,WAAO;AAAA,EACT;AACF;;;AH3PO,IAAK,YAAL,kBAAKC,eAAL;AACL,EAAAA,sBAAA,wBAAqB,KAArB;AACA,EAAAA,sBAAA,0BAAuB,KAAvB;AACA,EAAAA,sBAAA,sBAAmB,KAAnB;AACA,EAAAA,sBAAA,qBAAkB,KAAlB;AACA,EAAAA,sBAAA,oBAAiB,KAAjB;AACA,EAAAA,sBAAA,mBAAgB,KAAhB;AACA,EAAAA,sBAAA,oBAAiB,KAAjB;AACA,EAAAA,sBAAA,iBAAc,KAAd;AACA,EAAAA,sBAAA,uBAAoB,KAApB;AACA,EAAAA,sBAAA,SAAM,KAAN;AACA,EAAAA,sBAAA,YAAS,MAAT;AACA,EAAAA,sBAAA,iBAAc,MAAd;AACA,EAAAA,sBAAA,kBAAe,MAAf;AACA,EAAAA,sBAAA,eAAY,MAAZ;AACA,EAAAA,sBAAA,kBAAe,MAAf;AACA,EAAAA,sBAAA,mBAAgB,MAAhB;AACA,EAAAA,sBAAA,kBAAe,MAAf;AAjBU,SAAAA;AAAA,GAAA;AAwJZ,SAAS,sBAAiC;AACxC,SAAO,EAAE,MAAM,GAAG,WAAW,QAAW,UAAU,OAAU;AAC9D;AAEO,IAAM,YAAmC;AAAA,EAC9C,OAAO,SAAoB,SAAuB,IAAI,0BAAa,GAAiB;AAClF,QAAI,QAAQ,SAAS,GAAG;AACtB,aAAO,OAAO,CAAC,EAAE,MAAM,QAAQ,IAAI;AAAA,IACrC;AACA,QAAI,QAAQ,cAAc,QAAW;AACnC,aAAO,OAAO,EAAE,EAAE,MAAM,QAAQ,SAAS;AAAA,IAC3C;AACA,QAAI,QAAQ,aAAa,QAAW;AAClC,YAAM,OAAO,MAAM,KAAK,QAAQ,QAAQ,GAAG,OAAO,OAAO,EAAE,EAAE,KAAK,CAAC,EAAE,KAAK;AAAA,IAC5E;AACA,WAAO;AAAA,EACT;AAAA,EAEA,OAAO,OAAkC,QAA4B;AACnE,UAAM,SAAS,iBAAiB,4BAAe,QAAQ,IAAI,0BAAa,KAAK;AAC7E,QAAI,MAAM,WAAW,SAAY,OAAO,MAAM,OAAO,MAAM;AAC3D,UAAM,UAAU,oBAAoB;AACpC,WAAO,OAAO,MAAM,KAAK;AACvB,YAAM,MAAM,OAAO,OAAO;AAC1B,cAAQ,QAAQ,GAAG;AAAA,QACjB,KAAK,GAAG;AACN,cAAI,QAAQ,GAAG;AACb;AAAA,UACF;AAEA,kBAAQ,OAAO,OAAO,MAAM;AAC5B;AAAA,QACF;AAAA,QACA,KAAK,GAAG;AACN,cAAI,QAAQ,IAAI;AACd;AAAA,UACF;AAEA,kBAAQ,YAAY,aAAa,OAAO,MAAM,CAAC;AAC/C;AAAA,QACF;AAAA,QACA,KAAK,GAAG;AACN,cAAI,QAAQ,IAAI;AACd;AAAA,UACF;AAEA,kBAAQ,WAAW,MAAM,OAAO,MAAM,OAAO,QAAQ,OAAO,OAAO,CAAC,CAAC;AACrE;AAAA,QACF;AAAA,MACF;AACA,WAAK,MAAM,OAAO,KAAK,QAAQ,GAAG;AAChC;AAAA,MACF;AACA,aAAO,KAAK,MAAM,CAAC;AAAA,IACrB;AACA,WAAO;AAAA,EACT;AAAA,EAEA,OAAmD,MAAqB;AACtE,WAAO,UAAU,YAAY,sBAAS,CAAC,CAAS;AAAA,EAClD;AAAA,EACA,YAAwD,QAAsB;AAnOhF;AAoOI,UAAM,UAAU,oBAAoB;AACpC,YAAQ,QAAO,YAAO,SAAP,YAAe;AAC9B,YAAQ,aAAY,YAAO,cAAP,YAAoB;AACxC,YAAQ,YAAW,YAAO,aAAP,YAAmB;AACtC,WAAO;AAAA,EACT;AACF;AAEA,SAAS,kCAAyD;AAChE,SAAO,EAAE,WAAW,QAAW,WAAW,IAAI,MAAM,OAAU;AAChE;AAEO,IAAM,wBAA2D;AAAA,EACtE,OAAO,SAAgC,SAAuB,IAAI,0BAAa,GAAiB;AAC9F,QAAI,QAAQ,cAAc,QAAW;AACnC,gBAAU,OAAO,QAAQ,WAAW,OAAO,OAAO,EAAE,EAAE,KAAK,CAAC,EAAE,KAAK;AAAA,IACrE;AACA,QAAI,QAAQ,cAAc,IAAI;AAC5B,aAAO,OAAO,EAAE,EAAE,OAAO,QAAQ,SAAS;AAAA,IAC5C;AACA,QAAI,QAAQ,SAAS,QAAW;AAC9B,aAAO,OAAO,EAAE,EAAE,OAAO,QAAQ,IAAI;AAAA,IACvC;AACA,WAAO;AAAA,EACT;AAAA,EAEA,OAAO,OAAkC,QAAwC;AAC/E,UAAM,SAAS,iBAAiB,4BAAe,QAAQ,IAAI,0BAAa,KAAK;AAC7E,QAAI,MAAM,WAAW,SAAY,OAAO,MAAM,OAAO,MAAM;AAC3D,UAAM,UAAU,gCAAgC;AAChD,WAAO,OAAO,MAAM,KAAK;AACvB,YAAM,MAAM,OAAO,OAAO;AAC1B,cAAQ,QAAQ,GAAG;AAAA,QACjB,KAAK,GAAG;AACN,cAAI,QAAQ,IAAI;AACd;AAAA,UACF;AAEA,kBAAQ,YAAY,UAAU,OAAO,QAAQ,OAAO,OAAO,CAAC;AAC5D;AAAA,QACF;AAAA,QACA,KAAK,GAAG;AACN,cAAI,QAAQ,IAAI;AACd;AAAA,UACF;AAEA,kBAAQ,YAAY,OAAO,OAAO;AAClC;AAAA,QACF;AAAA,QACA,KAAK,GAAG;AACN,cAAI,QAAQ,IAAI;AACd;AAAA,UACF;AAEA,kBAAQ,OAAO,OAAO,OAAO;AAC7B;AAAA,QACF;AAAA,MACF;AACA,WAAK,MAAM,OAAO,KAAK,QAAQ,GAAG;AAChC;AAAA,MACF;AACA,aAAO,KAAK,MAAM,CAAC;AAAA,IACrB;AACA,WAAO;AAAA,EACT;AAAA,EAEA,OAA+D,MAAiC;AAC9F,WAAO,sBAAsB,YAAY,sBAAS,CAAC,CAAS;AAAA,EAC9D;AAAA,EACA,YAAoE,QAAkC;AAzSxG;AA0SI,UAAM,UAAU,gCAAgC;AAChD,YAAQ,YAAa,OAAO,cAAc,UAAa,OAAO,cAAc,OACxE,UAAU,YAAY,OAAO,SAAS,IACtC;AACJ,YAAQ,aAAY,YAAO,cAAP,YAAoB;AACxC,YAAQ,QAAO,YAAO,SAAP,YAAe;AAC9B,WAAO;AAAA,EACT;AACF;AAEA,SAAS,oCAA6D;AACpE,SAAO,EAAE,WAAW,QAAW,WAAW,IAAI,OAAO,GAAG;AAC1D;AAEO,IAAM,0BAA+D;AAAA,EAC1E,OAAO,SAAkC,SAAuB,IAAI,0BAAa,GAAiB;AAChG,QAAI,QAAQ,cAAc,QAAW;AACnC,gBAAU,OAAO,QAAQ,WAAW,OAAO,OAAO,EAAE,EAAE,KAAK,CAAC,EAAE,KAAK;AAAA,IACrE;AACA,QAAI,QAAQ,cAAc,IAAI;AAC5B,aAAO,OAAO,EAAE,EAAE,OAAO,QAAQ,SAAS;AAAA,IAC5C;AACA,QAAI,QAAQ,UAAU,IAAI;AACxB,aAAO,OAAO,EAAE,EAAE,OAAO,QAAQ,KAAK;AAAA,IACxC;AACA,WAAO;AAAA,EACT;AAAA,EAEA,OAAO,OAAkC,QAA0C;AACjF,UAAM,SAAS,iBAAiB,4BAAe,QAAQ,IAAI,0BAAa,KAAK;AAC7E,QAAI,MAAM,WAAW,SAAY,OAAO,MAAM,OAAO,MAAM;AAC3D,UAAM,UAAU,kCAAkC;AAClD,WAAO,OAAO,MAAM,KAAK;AACvB,YAAM,MAAM,OAAO,OAAO;AAC1B,cAAQ,QAAQ,GAAG;AAAA,QACjB,KAAK,GAAG;AACN,cAAI,QAAQ,IAAI;AACd;AAAA,UACF;AAEA,kBAAQ,YAAY,UAAU,OAAO,QAAQ,OAAO,OAAO,CAAC;AAC5D;AAAA,QACF;AAAA,QACA,KAAK,GAAG;AACN,cAAI,QAAQ,IAAI;AACd;AAAA,UACF;AAEA,kBAAQ,YAAY,OAAO,OAAO;AAClC;AAAA,QACF;AAAA,QACA,KAAK,GAAG;AACN,cAAI,QAAQ,IAAI;AACd;AAAA,UACF;AAEA,kBAAQ,QAAQ,OAAO,OAAO;AAC9B;AAAA,QACF;AAAA,MACF;AACA,WAAK,MAAM,OAAO,KAAK,QAAQ,GAAG;AAChC;AAAA,MACF;AACA,aAAO,KAAK,MAAM,CAAC;AAAA,IACrB;AACA,WAAO;AAAA,EACT;AAAA,EAEA,OAAiE,MAAmC;AAClG,WAAO,wBAAwB,YAAY,sBAAS,CAAC,CAAS;AAAA,EAChE;AAAA,EACA,YAAsE,QAAoC;AAjX5G;AAkXI,UAAM,UAAU,kCAAkC;AAClD,YAAQ,YAAa,OAAO,cAAc,UAAa,OAAO,cAAc,OACxE,UAAU,YAAY,OAAO,SAAS,IACtC;AACJ,YAAQ,aAAY,YAAO,cAAP,YAAoB;AACxC,YAAQ,SAAQ,YAAO,UAAP,YAAgB;AAChC,WAAO;AAAA,EACT;AACF;AAEA,SAAS,gCAAqD;AAC5D,SAAO,EAAE,WAAW,QAAW,WAAW,GAAG;AAC/C;AAEO,IAAM,sBAAuD;AAAA,EAClE,OAAO,SAA8B,SAAuB,IAAI,0BAAa,GAAiB;AAC5F,QAAI,QAAQ,cAAc,QAAW;AACnC,gBAAU,OAAO,QAAQ,WAAW,OAAO,OAAO,EAAE,EAAE,KAAK,CAAC,EAAE,KAAK;AAAA,IACrE;AACA,QAAI,QAAQ,cAAc,IAAI;AAC5B,aAAO,OAAO,EAAE,EAAE,OAAO,QAAQ,SAAS;AAAA,IAC5C;AACA,WAAO;AAAA,EACT;AAAA,EAEA,OAAO,OAAkC,QAAsC;AAC7E,UAAM,SAAS,iBAAiB,4BAAe,QAAQ,IAAI,0BAAa,KAAK;AAC7E,QAAI,MAAM,WAAW,SAAY,OAAO,MAAM,OAAO,MAAM;AAC3D,UAAM,UAAU,8BAA8B;AAC9C,WAAO,OAAO,MAAM,KAAK;AACvB,YAAM,MAAM,OAAO,OAAO;AAC1B,cAAQ,QAAQ,GAAG;AAAA,QACjB,KAAK,GAAG;AACN,cAAI,QAAQ,IAAI;AACd;AAAA,UACF;AAEA,kBAAQ,YAAY,UAAU,OAAO,QAAQ,OAAO,OAAO,CAAC;AAC5D;AAAA,QACF;AAAA,QACA,KAAK,GAAG;AACN,cAAI,QAAQ,IAAI;AACd;AAAA,UACF;AAEA,kBAAQ,YAAY,OAAO,OAAO;AAClC;AAAA,QACF;AAAA,MACF;AACA,WAAK,MAAM,OAAO,KAAK,QAAQ,GAAG;AAChC;AAAA,MACF;AACA,aAAO,KAAK,MAAM,CAAC;AAAA,IACrB;AACA,WAAO;AAAA,EACT;AAAA,EAEA,OAA6D,MAA+B;AAC1F,WAAO,oBAAoB,YAAY,sBAAS,CAAC,CAAS;AAAA,EAC5D;AAAA,EACA,YAAkE,QAAgC;AA9apG;AA+aI,UAAM,UAAU,8BAA8B;AAC9C,YAAQ,YAAa,OAAO,cAAc,UAAa,OAAO,cAAc,OACxE,UAAU,YAAY,OAAO,SAAS,IACtC;AACJ,YAAQ,aAAY,YAAO,cAAP,YAAoB;AACxC,WAAO;AAAA,EACT;AACF;AAEA,SAAS,+BAAmD;AAC1D,SAAO,EAAE,WAAW,QAAW,YAAY,IAAI,cAAc,IAAI,iBAAiB,OAAU;AAC9F;AAEO,IAAM,qBAAqD;AAAA,EAChE,OAAO,SAA6B,SAAuB,IAAI,0BAAa,GAAiB;AAC3F,QAAI,QAAQ,cAAc,QAAW;AACnC,gBAAU,OAAO,QAAQ,WAAW,OAAO,OAAO,EAAE,EAAE,KAAK,CAAC,EAAE,KAAK;AAAA,IACrE;AACA,QAAI,QAAQ,eAAe,IAAI;AAC7B,aAAO,OAAO,EAAE,EAAE,OAAO,QAAQ,UAAU;AAAA,IAC7C;AACA,QAAI,QAAQ,iBAAiB,IAAI;AAC/B,aAAO,OAAO,EAAE,EAAE,OAAO,QAAQ,YAAY;AAAA,IAC/C;AACA,QAAI,QAAQ,oBAAoB,QAAW;AACzC,aAAO,OAAO,EAAE,EAAE,OAAO,QAAQ,eAAe;AAAA,IAClD;AACA,WAAO;AAAA,EACT;AAAA,EAEA,OAAO,OAAkC,QAAqC;AAC5E,UAAM,SAAS,iBAAiB,4BAAe,QAAQ,IAAI,0BAAa,KAAK;AAC7E,QAAI,MAAM,WAAW,SAAY,OAAO,MAAM,OAAO,MAAM;AAC3D,UAAM,UAAU,6BAA6B;AAC7C,WAAO,OAAO,MAAM,KAAK;AACvB,YAAM,MAAM,OAAO,OAAO;AAC1B,cAAQ,QAAQ,GAAG;AAAA,QACjB,KAAK,GAAG;AACN,cAAI,QAAQ,IAAI;AACd;AAAA,UACF;AAEA,kBAAQ,YAAY,UAAU,OAAO,QAAQ,OAAO,OAAO,CAAC;AAC5D;AAAA,QACF;AAAA,QACA,KAAK,GAAG;AACN,cAAI,QAAQ,IAAI;AACd;AAAA,UACF;AAEA,kBAAQ,aAAa,OAAO,OAAO;AACnC;AAAA,QACF;AAAA,QACA,KAAK,GAAG;AACN,cAAI,QAAQ,IAAI;AACd;AAAA,UACF;AAEA,kBAAQ,eAAe,OAAO,OAAO;AACrC;AAAA,QACF;AAAA,QACA,KAAK,GAAG;AACN,cAAI,QAAQ,IAAI;AACd;AAAA,UACF;AAEA,kBAAQ,kBAAkB,OAAO,OAAO;AACxC;AAAA,QACF;AAAA,MACF;AACA,WAAK,MAAM,OAAO,KAAK,QAAQ,GAAG;AAChC;AAAA,MACF;AACA,aAAO,KAAK,MAAM,CAAC;AAAA,IACrB;AACA,WAAO;AAAA,EACT;AAAA,EAEA,OAA4D,MAA8B;AACxF,WAAO,mBAAmB,YAAY,sBAAS,CAAC,CAAS;AAAA,EAC3D;AAAA,EACA,YAAiE,QAA+B;AAhgBlG;AAigBI,UAAM,UAAU,6BAA6B;AAC7C,YAAQ,YAAa,OAAO,cAAc,UAAa,OAAO,cAAc,OACxE,UAAU,YAAY,OAAO,SAAS,IACtC;AACJ,YAAQ,cAAa,YAAO,eAAP,YAAqB;AAC1C,YAAQ,gBAAe,YAAO,iBAAP,YAAuB;AAC9C,YAAQ,mBAAkB,YAAO,oBAAP,YAA0B;AACpD,WAAO;AAAA,EACT;AACF;AAEA,SAAS,8BAAiD;AACxD,SAAO,EAAE,WAAW,QAAW,YAAY,IAAI,OAAO,GAAG;AAC3D;AAEO,IAAM,oBAAmD;AAAA,EAC9D,OAAO,SAA4B,SAAuB,IAAI,0BAAa,GAAiB;AAC1F,QAAI,QAAQ,cAAc,QAAW;AACnC,gBAAU,OAAO,QAAQ,WAAW,OAAO,OAAO,EAAE,EAAE,KAAK,CAAC,EAAE,KAAK;AAAA,IACrE;AACA,QAAI,QAAQ,eAAe,IAAI;AAC7B,aAAO,OAAO,EAAE,EAAE,OAAO,QAAQ,UAAU;AAAA,IAC7C;AACA,QAAI,QAAQ,UAAU,IAAI;AACxB,aAAO,OAAO,EAAE,EAAE,OAAO,QAAQ,KAAK;AAAA,IACxC;AACA,WAAO;AAAA,EACT;AAAA,EAEA,OAAO,OAAkC,QAAoC;AAC3E,UAAM,SAAS,iBAAiB,4BAAe,QAAQ,IAAI,0BAAa,KAAK;AAC7E,QAAI,MAAM,WAAW,SAAY,OAAO,MAAM,OAAO,MAAM;AAC3D,UAAM,UAAU,4BAA4B;AAC5C,WAAO,OAAO,MAAM,KAAK;AACvB,YAAM,MAAM,OAAO,OAAO;AAC1B,cAAQ,QAAQ,GAAG;AAAA,QACjB,KAAK,GAAG;AACN,cAAI,QAAQ,IAAI;AACd;AAAA,UACF;AAEA,kBAAQ,YAAY,UAAU,OAAO,QAAQ,OAAO,OAAO,CAAC;AAC5D;AAAA,QACF;AAAA,QACA,KAAK,GAAG;AACN,cAAI,QAAQ,IAAI;AACd;AAAA,UACF;AAEA,kBAAQ,aAAa,OAAO,OAAO;AACnC;AAAA,QACF;AAAA,QACA,KAAK,GAAG;AACN,cAAI,QAAQ,IAAI;AACd;AAAA,UACF;AAEA,kBAAQ,QAAQ,OAAO,OAAO;AAC9B;AAAA,QACF;AAAA,MACF;AACA,WAAK,MAAM,OAAO,KAAK,QAAQ,GAAG;AAChC;AAAA,MACF;AACA,aAAO,KAAK,MAAM,CAAC;AAAA,IACrB;AACA,WAAO;AAAA,EACT;AAAA,EAEA,OAA2D,MAA6B;AACtF,WAAO,kBAAkB,YAAY,sBAAS,CAAC,CAAS;AAAA,EAC1D;AAAA,EACA,YAAgE,QAA8B;AAzkBhG;AA0kBI,UAAM,UAAU,4BAA4B;AAC5C,YAAQ,YAAa,OAAO,cAAc,UAAa,OAAO,cAAc,OACxE,UAAU,YAAY,OAAO,SAAS,IACtC;AACJ,YAAQ,cAAa,YAAO,eAAP,YAAqB;AAC1C,YAAQ,SAAQ,YAAO,UAAP,YAAgB;AAChC,WAAO;AAAA,EACT;AACF;AAEA,SAAS,6BAA+C;AACtD,SAAO,EAAE,WAAW,QAAW,YAAY,GAAG;AAChD;AAEO,IAAM,mBAAiD;AAAA,EAC5D,OAAO,SAA2B,SAAuB,IAAI,0BAAa,GAAiB;AACzF,QAAI,QAAQ,cAAc,QAAW;AACnC,gBAAU,OAAO,QAAQ,WAAW,OAAO,OAAO,EAAE,EAAE,KAAK,CAAC,EAAE,KAAK;AAAA,IACrE;AACA,QAAI,QAAQ,eAAe,IAAI;AAC7B,aAAO,OAAO,EAAE,EAAE,OAAO,QAAQ,UAAU;AAAA,IAC7C;AACA,WAAO;AAAA,EACT;AAAA,EAEA,OAAO,OAAkC,QAAmC;AAC1E,UAAM,SAAS,iBAAiB,4BAAe,QAAQ,IAAI,0BAAa,KAAK;AAC7E,QAAI,MAAM,WAAW,SAAY,OAAO,MAAM,OAAO,MAAM;AAC3D,UAAM,UAAU,2BAA2B;AAC3C,WAAO,OAAO,MAAM,KAAK;AACvB,YAAM,MAAM,OAAO,OAAO;AAC1B,cAAQ,QAAQ,GAAG;AAAA,QACjB,KAAK,GAAG;AACN,cAAI,QAAQ,IAAI;AACd;AAAA,UACF;AAEA,kBAAQ,YAAY,UAAU,OAAO,QAAQ,OAAO,OAAO,CAAC;AAC5D;AAAA,QACF;AAAA,QACA,KAAK,GAAG;AACN,cAAI,QAAQ,IAAI;AACd;AAAA,UACF;AAEA,kBAAQ,aAAa,OAAO,OAAO;AACnC;AAAA,QACF;AAAA,MACF;AACA,WAAK,MAAM,OAAO,KAAK,QAAQ,GAAG;AAChC;AAAA,MACF;AACA,aAAO,KAAK,MAAM,CAAC;AAAA,IACrB;AACA,WAAO;AAAA,EACT;AAAA,EAEA,OAA0D,MAA4B;AACpF,WAAO,iBAAiB,YAAY,sBAAS,CAAC,CAAS;AAAA,EACzD;AAAA,EACA,YAA+D,QAA6B;AAtoB9F;AAuoBI,UAAM,UAAU,2BAA2B;AAC3C,YAAQ,YAAa,OAAO,cAAc,UAAa,OAAO,cAAc,OACxE,UAAU,YAAY,OAAO,SAAS,IACtC;AACJ,YAAQ,cAAa,YAAO,eAAP,YAAqB;AAC1C,WAAO;AAAA,EACT;AACF;AAEA,SAAS,+BAAmD;AAC1D,SAAO,EAAE,WAAW,QAAW,UAAU,OAAU;AACrD;AAEO,IAAM,qBAAqD;AAAA,EAChE,OAAO,SAA6B,SAAuB,IAAI,0BAAa,GAAiB;AAC3F,QAAI,QAAQ,cAAc,QAAW;AACnC,gBAAU,OAAO,QAAQ,WAAW,OAAO,OAAO,EAAE,EAAE,KAAK,CAAC,EAAE,KAAK;AAAA,IACrE;AACA,QAAI,QAAQ,aAAa,QAAW;AAClC,YAAM,OAAO,MAAM,KAAK,QAAQ,QAAQ,GAAG,OAAO,OAAO,EAAE,EAAE,KAAK,CAAC,EAAE,KAAK;AAAA,IAC5E;AACA,WAAO;AAAA,EACT;AAAA,EAEA,OAAO,OAAkC,QAAqC;AAC5E,UAAM,SAAS,iBAAiB,4BAAe,QAAQ,IAAI,0BAAa,KAAK;AAC7E,QAAI,MAAM,WAAW,SAAY,OAAO,MAAM,OAAO,MAAM;AAC3D,UAAM,UAAU,6BAA6B;AAC7C,WAAO,OAAO,MAAM,KAAK;AACvB,YAAM,MAAM,OAAO,OAAO;AAC1B,cAAQ,QAAQ,GAAG;AAAA,QACjB,KAAK,GAAG;AACN,cAAI,QAAQ,IAAI;AACd;AAAA,UACF;AAEA,kBAAQ,YAAY,UAAU,OAAO,QAAQ,OAAO,OAAO,CAAC;AAC5D;AAAA,QACF;AAAA,QACA,KAAK,GAAG;AACN,cAAI,QAAQ,IAAI;AACd;AAAA,UACF;AAEA,kBAAQ,WAAW,MAAM,OAAO,MAAM,OAAO,QAAQ,OAAO,OAAO,CAAC,CAAC;AACrE;AAAA,QACF;AAAA,MACF;AACA,WAAK,MAAM,OAAO,KAAK,QAAQ,GAAG;AAChC;AAAA,MACF;AACA,aAAO,KAAK,MAAM,CAAC;AAAA,IACrB;AACA,WAAO;AAAA,EACT;AAAA,EAEA,OAA4D,MAA8B;AACxF,WAAO,mBAAmB,YAAY,sBAAS,CAAC,CAAS;AAAA,EAC3D;AAAA,EACA,YAAiE,QAA+B;AAlsBlG;AAmsBI,UAAM,UAAU,6BAA6B;AAC7C,YAAQ,YAAa,OAAO,cAAc,UAAa,OAAO,cAAc,OACxE,UAAU,YAAY,OAAO,SAAS,IACtC;AACJ,YAAQ,YAAW,YAAO,aAAP,YAAmB;AACtC,WAAO;AAAA,EACT;AACF;AAEA,SAAS,4BAA6C;AACpD,SAAO,EAAE,WAAW,QAAW,OAAO,CAAC,EAAE;AAC3C;AAEO,IAAM,kBAA+C;AAAA,EAC1D,OAAO,SAA0B,SAAuB,IAAI,0BAAa,GAAiB;AACxF,QAAI,QAAQ,cAAc,QAAW;AACnC,gBAAU,OAAO,QAAQ,WAAW,OAAO,OAAO,EAAE,EAAE,KAAK,CAAC,EAAE,KAAK;AAAA,IACrE;AACA,eAAW,KAAK,QAAQ,OAAO;AAC7B,yBAAmB,OAAO,GAAI,OAAO,OAAO,EAAE,EAAE,KAAK,CAAC,EAAE,KAAK;AAAA,IAC/D;AACA,WAAO;AAAA,EACT;AAAA,EAEA,OAAO,OAAkC,QAAkC;AACzE,UAAM,SAAS,iBAAiB,4BAAe,QAAQ,IAAI,0BAAa,KAAK;AAC7E,QAAI,MAAM,WAAW,SAAY,OAAO,MAAM,OAAO,MAAM;AAC3D,UAAM,UAAU,0BAA0B;AAC1C,WAAO,OAAO,MAAM,KAAK;AACvB,YAAM,MAAM,OAAO,OAAO;AAC1B,cAAQ,QAAQ,GAAG;AAAA,QACjB,KAAK,GAAG;AACN,cAAI,QAAQ,IAAI;AACd;AAAA,UACF;AAEA,kBAAQ,YAAY,UAAU,OAAO,QAAQ,OAAO,OAAO,CAAC;AAC5D;AAAA,QACF;AAAA,QACA,KAAK,GAAG;AACN,cAAI,QAAQ,IAAI;AACd;AAAA,UACF;AAEA,kBAAQ,MAAM,KAAK,mBAAmB,OAAO,QAAQ,OAAO,OAAO,CAAC,CAAC;AACrE;AAAA,QACF;AAAA,MACF;AACA,WAAK,MAAM,OAAO,KAAK,QAAQ,GAAG;AAChC;AAAA,MACF;AACA,aAAO,KAAK,MAAM,CAAC;AAAA,IACrB;AACA,WAAO;AAAA,EACT;AAAA,EAEA,OAAyD,MAA2B;AAClF,WAAO,gBAAgB,YAAY,sBAAS,CAAC,CAAS;AAAA,EACxD;AAAA,EACA,YAA8D,QAA4B;AA9vB5F;AA+vBI,UAAM,UAAU,0BAA0B;AAC1C,YAAQ,YAAa,OAAO,cAAc,UAAa,OAAO,cAAc,OACxE,UAAU,YAAY,OAAO,SAAS,IACtC;AACJ,YAAQ,UAAQ,YAAO,UAAP,mBAAc,IAAI,CAAC,MAAM,mBAAmB,YAAY,CAAC,OAAM,CAAC;AAChF,WAAO;AAAA,EACT;AACF;AAEA,SAAS,kCAAyD;AAChE,SAAO,EAAE,WAAW,QAAW,UAAU,CAAC,EAAE;AAC9C;AAEO,IAAM,wBAA2D;AAAA,EACtE,OAAO,SAAgC,SAAuB,IAAI,0BAAa,GAAiB;AAC9F,QAAI,QAAQ,cAAc,QAAW;AACnC,gBAAU,OAAO,QAAQ,WAAW,OAAO,OAAO,EAAE,EAAE,KAAK,CAAC,EAAE,KAAK;AAAA,IACrE;AACA,eAAW,KAAK,QAAQ,UAAU;AAChC,cAAQ,OAAO,GAAI,OAAO,OAAO,EAAE,EAAE,KAAK,CAAC,EAAE,KAAK;AAAA,IACpD;AACA,WAAO;AAAA,EACT;AAAA,EAEA,OAAO,OAAkC,QAAwC;AAC/E,UAAM,SAAS,iBAAiB,4BAAe,QAAQ,IAAI,0BAAa,KAAK;AAC7E,QAAI,MAAM,WAAW,SAAY,OAAO,MAAM,OAAO,MAAM;AAC3D,UAAM,UAAU,gCAAgC;AAChD,WAAO,OAAO,MAAM,KAAK;AACvB,YAAM,MAAM,OAAO,OAAO;AAC1B,cAAQ,QAAQ,GAAG;AAAA,QACjB,KAAK,GAAG;AACN,cAAI,QAAQ,IAAI;AACd;AAAA,UACF;AAEA,kBAAQ,YAAY,UAAU,OAAO,QAAQ,OAAO,OAAO,CAAC;AAC5D;AAAA,QACF;AAAA,QACA,KAAK,GAAG;AACN,cAAI,QAAQ,IAAI;AACd;AAAA,UACF;AAEA,kBAAQ,SAAS,KAAK,QAAQ,OAAO,QAAQ,OAAO,OAAO,CAAC,CAAC;AAC7D;AAAA,QACF;AAAA,MACF;AACA,WAAK,MAAM,OAAO,KAAK,QAAQ,GAAG;AAChC;AAAA,MACF;AACA,aAAO,KAAK,MAAM,CAAC;AAAA,IACrB;AACA,WAAO;AAAA,EACT;AAAA,EAEA,OAA+D,MAAiC;AAC9F,WAAO,sBAAsB,YAAY,sBAAS,CAAC,CAAS;AAAA,EAC9D;AAAA,EACA,YAAoE,QAAkC;AA1zBxG;AA2zBI,UAAM,UAAU,gCAAgC;AAChD,YAAQ,YAAa,OAAO,cAAc,UAAa,OAAO,cAAc,OACxE,UAAU,YAAY,OAAO,SAAS,IACtC;AACJ,YAAQ,aAAW,YAAO,aAAP,mBAAiB,IAAI,CAAC,MAAM,QAAQ,YAAY,CAAC,OAAM,CAAC;AAC3E,WAAO;AAAA,EACT;AACF;AAEA,SAAS,qBAA+B;AACtC,SAAO,EAAE,WAAW,QAAW,OAAO,QAAW,QAAQ,OAAU;AACrE;AAEO,IAAM,WAAiC;AAAA,EAC5C,OAAO,SAAmB,SAAuB,IAAI,0BAAa,GAAiB;AACjF,QAAI,QAAQ,cAAc,QAAW;AACnC,gBAAU,OAAO,QAAQ,WAAW,OAAO,OAAO,EAAE,EAAE,KAAK,CAAC,EAAE,KAAK;AAAA,IACrE;AACA,QAAI,QAAQ,UAAU,QAAW;AAC/B,YAAM,OAAO,MAAM,KAAK,QAAQ,KAAK,GAAG,OAAO,OAAO,EAAE,EAAE,KAAK,CAAC,EAAE,KAAK;AAAA,IACzE;AACA,QAAI,QAAQ,WAAW,QAAW;AAChC,aAAO,OAAO,EAAE,EAAE,OAAO,QAAQ,MAAM;AAAA,IACzC;AACA,WAAO;AAAA,EACT;AAAA,EAEA,OAAO,OAAkC,QAA2B;AAClE,UAAM,SAAS,iBAAiB,4BAAe,QAAQ,IAAI,0BAAa,KAAK;AAC7E,QAAI,MAAM,WAAW,SAAY,OAAO,MAAM,OAAO,MAAM;AAC3D,UAAM,UAAU,mBAAmB;AACnC,WAAO,OAAO,MAAM,KAAK;AACvB,YAAM,MAAM,OAAO,OAAO;AAC1B,cAAQ,QAAQ,GAAG;AAAA,QACjB,KAAK,GAAG;AACN,cAAI,QAAQ,IAAI;AACd;AAAA,UACF;AAEA,kBAAQ,YAAY,UAAU,OAAO,QAAQ,OAAO,OAAO,CAAC;AAC5D;AAAA,QACF;AAAA,QACA,KAAK,GAAG;AACN,cAAI,QAAQ,IAAI;AACd;AAAA,UACF;AAEA,kBAAQ,QAAQ,MAAM,OAAO,MAAM,OAAO,QAAQ,OAAO,OAAO,CAAC,CAAC;AAClE;AAAA,QACF;AAAA,QACA,KAAK,GAAG;AACN,cAAI,QAAQ,IAAI;AACd;AAAA,UACF;AAEA,kBAAQ,SAAS,OAAO,OAAO;AAC/B;AAAA,QACF;AAAA,MACF;AACA,WAAK,MAAM,OAAO,KAAK,QAAQ,GAAG;AAChC;AAAA,MACF;AACA,aAAO,KAAK,MAAM,CAAC;AAAA,IACrB;AACA,WAAO;AAAA,EACT;AAAA,EAEA,OAAkD,MAAoB;AACpE,WAAO,SAAS,YAAY,sBAAS,CAAC,CAAS;AAAA,EACjD;AAAA,EACA,YAAuD,QAAqB;AAj4B9E;AAk4BI,UAAM,UAAU,mBAAmB;AACnC,YAAQ,YAAa,OAAO,cAAc,UAAa,OAAO,cAAc,OACxE,UAAU,YAAY,OAAO,SAAS,IACtC;AACJ,YAAQ,SAAQ,YAAO,UAAP,YAAgB;AAChC,YAAQ,UAAS,YAAO,WAAP,YAAiB;AAClC,WAAO;AAAA,EACT;AACF;AAEA,SAAS,wBAAqC;AAC5C,SAAO,EAAE,WAAW,QAAW,MAAM,IAAI,OAAO,OAAU;AAC5D;AAEO,IAAM,cAAuC;AAAA,EAClD,OAAO,SAAsB,SAAuB,IAAI,0BAAa,GAAiB;AACpF,QAAI,QAAQ,cAAc,QAAW;AACnC,gBAAU,OAAO,QAAQ,WAAW,OAAO,OAAO,EAAE,EAAE,KAAK,CAAC,EAAE,KAAK;AAAA,IACrE;AACA,QAAI,QAAQ,SAAS,IAAI;AACvB,aAAO,OAAO,EAAE,EAAE,OAAO,QAAQ,IAAI;AAAA,IACvC;AACA,QAAI,QAAQ,UAAU,QAAW;AAC/B,YAAM,OAAO,MAAM,KAAK,QAAQ,KAAK,GAAG,OAAO,OAAO,EAAE,EAAE,KAAK,CAAC,EAAE,KAAK;AAAA,IACzE;AACA,WAAO;AAAA,EACT;AAAA,EAEA,OAAO,OAAkC,QAA8B;AACrE,UAAM,SAAS,iBAAiB,4BAAe,QAAQ,IAAI,0BAAa,KAAK;AAC7E,QAAI,MAAM,WAAW,SAAY,OAAO,MAAM,OAAO,MAAM;AAC3D,UAAM,UAAU,sBAAsB;AACtC,WAAO,OAAO,MAAM,KAAK;AACvB,YAAM,MAAM,OAAO,OAAO;AAC1B,cAAQ,QAAQ,GAAG;AAAA,QACjB,KAAK,GAAG;AACN,cAAI,QAAQ,IAAI;AACd;AAAA,UACF;AAEA,kBAAQ,YAAY,UAAU,OAAO,QAAQ,OAAO,OAAO,CAAC;AAC5D;AAAA,QACF;AAAA,QACA,KAAK,GAAG;AACN,cAAI,QAAQ,IAAI;AACd;AAAA,UACF;AAEA,kBAAQ,OAAO,OAAO,OAAO;AAC7B;AAAA,QACF;AAAA,QACA,KAAK,GAAG;AACN,cAAI,QAAQ,IAAI;AACd;AAAA,UACF;AAEA,kBAAQ,QAAQ,MAAM,OAAO,MAAM,OAAO,QAAQ,OAAO,OAAO,CAAC,CAAC;AAClE;AAAA,QACF;AAAA,MACF;AACA,WAAK,MAAM,OAAO,KAAK,QAAQ,GAAG;AAChC;AAAA,MACF;AACA,aAAO,KAAK,MAAM,CAAC;AAAA,IACrB;AACA,WAAO;AAAA,EACT;AAAA,EAEA,OAAqD,MAAuB;AAC1E,WAAO,YAAY,YAAY,sBAAS,CAAC,CAAS;AAAA,EACpD;AAAA,EACA,YAA0D,QAAwB;AAz8BpF;AA08BI,UAAM,UAAU,sBAAsB;AACtC,YAAQ,YAAa,OAAO,cAAc,UAAa,OAAO,cAAc,OACxE,UAAU,YAAY,OAAO,SAAS,IACtC;AACJ,YAAQ,QAAO,YAAO,SAAP,YAAe;AAC9B,YAAQ,SAAQ,YAAO,UAAP,YAAgB;AAChC,WAAO;AAAA,EACT;AACF;AAEA,SAAS,4BAA6C;AACpD,SAAO,EAAE,WAAW,QAAW,UAAU,IAAI,OAAO,GAAG;AACzD;AAEO,IAAM,kBAA+C;AAAA,EAC1D,OAAO,SAA0B,SAAuB,IAAI,0BAAa,GAAiB;AACxF,QAAI,QAAQ,cAAc,QAAW;AACnC,gBAAU,OAAO,QAAQ,WAAW,OAAO,OAAO,EAAE,EAAE,KAAK,CAAC,EAAE,KAAK;AAAA,IACrE;AACA,QAAI,QAAQ,aAAa,IAAI;AAC3B,aAAO,OAAO,EAAE,EAAE,OAAO,QAAQ,QAAQ;AAAA,IAC3C;AACA,QAAI,QAAQ,UAAU,IAAI;AACxB,aAAO,OAAO,EAAE,EAAE,OAAO,QAAQ,KAAK;AAAA,IACxC;AACA,WAAO;AAAA,EACT;AAAA,EAEA,OAAO,OAAkC,QAAkC;AACzE,UAAM,SAAS,iBAAiB,4BAAe,QAAQ,IAAI,0BAAa,KAAK;AAC7E,QAAI,MAAM,WAAW,SAAY,OAAO,MAAM,OAAO,MAAM;AAC3D,UAAM,UAAU,0BAA0B;AAC1C,WAAO,OAAO,MAAM,KAAK;AACvB,YAAM,MAAM,OAAO,OAAO;AAC1B,cAAQ,QAAQ,GAAG;AAAA,QACjB,KAAK,GAAG;AACN,cAAI,QAAQ,IAAI;AACd;AAAA,UACF;AAEA,kBAAQ,YAAY,UAAU,OAAO,QAAQ,OAAO,OAAO,CAAC;AAC5D;AAAA,QACF;AAAA,QACA,KAAK,GAAG;AACN,cAAI,QAAQ,IAAI;AACd;AAAA,UACF;AAEA,kBAAQ,WAAW,OAAO,OAAO;AACjC;AAAA,QACF;AAAA,QACA,KAAK,GAAG;AACN,cAAI,QAAQ,IAAI;AACd;AAAA,UACF;AAEA,kBAAQ,QAAQ,OAAO,OAAO;AAC9B;AAAA,QACF;AAAA,MACF;AACA,WAAK,MAAM,OAAO,KAAK,QAAQ,GAAG;AAChC;AAAA,MACF;AACA,aAAO,KAAK,MAAM,CAAC;AAAA,IACrB;AACA,WAAO;AAAA,EACT;AAAA,EAEA,OAAyD,MAA2B;AAClF,WAAO,gBAAgB,YAAY,sBAAS,CAAC,CAAS;AAAA,EACxD;AAAA,EACA,YAA8D,QAA4B;AAjhC5F;AAkhCI,UAAM,UAAU,0BAA0B;AAC1C,YAAQ,YAAa,OAAO,cAAc,UAAa,OAAO,cAAc,OACxE,UAAU,YAAY,OAAO,SAAS,IACtC;AACJ,YAAQ,YAAW,YAAO,aAAP,YAAmB;AACtC,YAAQ,SAAQ,YAAO,UAAP,YAAgB;AAChC,WAAO;AAAA,EACT;AACF;AAEA,SAAS,6BAA+C;AACtD,SAAO,EAAE,WAAW,QAAW,UAAU,IAAI,OAAO,GAAG;AACzD;AAEO,IAAM,mBAAiD;AAAA,EAC5D,OAAO,SAA2B,SAAuB,IAAI,0BAAa,GAAiB;AACzF,QAAI,QAAQ,cAAc,QAAW;AACnC,gBAAU,OAAO,QAAQ,WAAW,OAAO,OAAO,EAAE,EAAE,KAAK,CAAC,EAAE,KAAK;AAAA,IACrE;AACA,QAAI,QAAQ,aAAa,IAAI;AAC3B,aAAO,OAAO,EAAE,EAAE,OAAO,QAAQ,QAAQ;AAAA,IAC3C;AACA,QAAI,QAAQ,UAAU,IAAI;AACxB,aAAO,OAAO,EAAE,EAAE,OAAO,QAAQ,KAAK;AAAA,IACxC;AACA,WAAO;AAAA,EACT;AAAA,EAEA,OAAO,OAAkC,QAAmC;AAC1E,UAAM,SAAS,iBAAiB,4BAAe,QAAQ,IAAI,0BAAa,KAAK;AAC7E,QAAI,MAAM,WAAW,SAAY,OAAO,MAAM,OAAO,MAAM;AAC3D,UAAM,UAAU,2BAA2B;AAC3C,WAAO,OAAO,MAAM,KAAK;AACvB,YAAM,MAAM,OAAO,OAAO;AAC1B,cAAQ,QAAQ,GAAG;AAAA,QACjB,KAAK,GAAG;AACN,cAAI,QAAQ,IAAI;AACd;AAAA,UACF;AAEA,kBAAQ,YAAY,UAAU,OAAO,QAAQ,OAAO,OAAO,CAAC;AAC5D;AAAA,QACF;AAAA,QACA,KAAK,GAAG;AACN,cAAI,QAAQ,IAAI;AACd;AAAA,UACF;AAEA,kBAAQ,WAAW,OAAO,OAAO;AACjC;AAAA,QACF;AAAA,QACA,KAAK,GAAG;AACN,cAAI,QAAQ,IAAI;AACd;AAAA,UACF;AAEA,kBAAQ,QAAQ,OAAO,OAAO;AAC9B;AAAA,QACF;AAAA,MACF;AACA,WAAK,MAAM,OAAO,KAAK,QAAQ,GAAG;AAChC;AAAA,MACF;AACA,aAAO,KAAK,MAAM,CAAC;AAAA,IACrB;AACA,WAAO;AAAA,EACT;AAAA,EAEA,OAA0D,MAA4B;AACpF,WAAO,iBAAiB,YAAY,sBAAS,CAAC,CAAS;AAAA,EACzD;AAAA,EACA,YAA+D,QAA6B;AAzlC9F;AA0lCI,UAAM,UAAU,2BAA2B;AAC3C,YAAQ,YAAa,OAAO,cAAc,UAAa,OAAO,cAAc,OACxE,UAAU,YAAY,OAAO,SAAS,IACtC;AACJ,YAAQ,YAAW,YAAO,aAAP,YAAmB;AACtC,YAAQ,SAAQ,YAAO,UAAP,YAAgB;AAChC,WAAO;AAAA,EACT;AACF;AAEA,SAAS,0BAAyC;AAChD,SAAO,EAAE,WAAW,QAAW,MAAM,QAAW,SAAS,GAAG;AAC9D;AAEO,IAAM,gBAA2C;AAAA,EACtD,OAAO,SAAwB,SAAuB,IAAI,0BAAa,GAAiB;AACtF,QAAI,QAAQ,cAAc,QAAW;AACnC,gBAAU,OAAO,QAAQ,WAAW,OAAO,OAAO,EAAE,EAAE,KAAK,CAAC,EAAE,KAAK;AAAA,IACrE;AACA,QAAI,QAAQ,SAAS,QAAW;AAC9B,aAAO,OAAO,EAAE,EAAE,OAAO,QAAQ,IAAI;AAAA,IACvC;AACA,QAAI,QAAQ,YAAY,IAAI;AAC1B,aAAO,OAAO,EAAE,EAAE,OAAO,QAAQ,OAAO;AAAA,IAC1C;AACA,WAAO;AAAA,EACT;AAAA,EAEA,OAAO,OAAkC,QAAgC;AACvE,UAAM,SAAS,iBAAiB,4BAAe,QAAQ,IAAI,0BAAa,KAAK;AAC7E,QAAI,MAAM,WAAW,SAAY,OAAO,MAAM,OAAO,MAAM;AAC3D,UAAM,UAAU,wBAAwB;AACxC,WAAO,OAAO,MAAM,KAAK;AACvB,YAAM,MAAM,OAAO,OAAO;AAC1B,cAAQ,QAAQ,GAAG;AAAA,QACjB,KAAK,GAAG;AACN,cAAI,QAAQ,IAAI;AACd;AAAA,UACF;AAEA,kBAAQ,YAAY,UAAU,OAAO,QAAQ,OAAO,OAAO,CAAC;AAC5D;AAAA,QACF;AAAA,QACA,KAAK,GAAG;AACN,cAAI,QAAQ,IAAI;AACd;AAAA,UACF;AAEA,kBAAQ,OAAO,OAAO,OAAO;AAC7B;AAAA,QACF;AAAA,QACA,KAAK,GAAG;AACN,cAAI,QAAQ,IAAI;AACd;AAAA,UACF;AAEA,kBAAQ,UAAU,OAAO,OAAO;AAChC;AAAA,QACF;AAAA,MACF;AACA,WAAK,MAAM,OAAO,KAAK,QAAQ,GAAG;AAChC;AAAA,MACF;AACA,aAAO,KAAK,MAAM,CAAC;AAAA,IACrB;AACA,WAAO;AAAA,EACT;AAAA,EAEA,OAAuD,MAAyB;AAC9E,WAAO,cAAc,YAAY,sBAAS,CAAC,CAAS;AAAA,EACtD;AAAA,EACA,YAA4D,QAA0B;AAjqCxF;AAkqCI,UAAM,UAAU,wBAAwB;AACxC,YAAQ,YAAa,OAAO,cAAc,UAAa,OAAO,cAAc,OACxE,UAAU,YAAY,OAAO,SAAS,IACtC;AACJ,YAAQ,QAAO,YAAO,SAAP,YAAe;AAC9B,YAAQ,WAAU,YAAO,YAAP,YAAkB;AACpC,WAAO;AAAA,EACT;AACF;AAEA,SAAS,6BAA+C;AACtD,SAAO,EAAE,WAAW,QAAW,UAAU,GAAG;AAC9C;AAEO,IAAM,mBAAiD;AAAA,EAC5D,OAAO,SAA2B,SAAuB,IAAI,0BAAa,GAAiB;AACzF,QAAI,QAAQ,cAAc,QAAW;AACnC,gBAAU,OAAO,QAAQ,WAAW,OAAO,OAAO,EAAE,EAAE,KAAK,CAAC,EAAE,KAAK;AAAA,IACrE;AACA,QAAI,QAAQ,aAAa,IAAI;AAC3B,aAAO,OAAO,EAAE,EAAE,OAAO,QAAQ,QAAQ;AAAA,IAC3C;AACA,WAAO;AAAA,EACT;AAAA,EAEA,OAAO,OAAkC,QAAmC;AAC1E,UAAM,SAAS,iBAAiB,4BAAe,QAAQ,IAAI,0BAAa,KAAK;AAC7E,QAAI,MAAM,WAAW,SAAY,OAAO,MAAM,OAAO,MAAM;AAC3D,UAAM,UAAU,2BAA2B;AAC3C,WAAO,OAAO,MAAM,KAAK;AACvB,YAAM,MAAM,OAAO,OAAO;AAC1B,cAAQ,QAAQ,GAAG;AAAA,QACjB,KAAK,GAAG;AACN,cAAI,QAAQ,IAAI;AACd;AAAA,UACF;AAEA,kBAAQ,YAAY,UAAU,OAAO,QAAQ,OAAO,OAAO,CAAC;AAC5D;AAAA,QACF;AAAA,QACA,KAAK,GAAG;AACN,cAAI,QAAQ,IAAI;AACd;AAAA,UACF;AAEA,kBAAQ,WAAW,OAAO,OAAO;AACjC;AAAA,QACF;AAAA,MACF;AACA,WAAK,MAAM,OAAO,KAAK,QAAQ,GAAG;AAChC;AAAA,MACF;AACA,aAAO,KAAK,MAAM,CAAC;AAAA,IACrB;AACA,WAAO;AAAA,EACT;AAAA,EAEA,OAA0D,MAA4B;AACpF,WAAO,iBAAiB,YAAY,sBAAS,CAAC,CAAS;AAAA,EACzD;AAAA,EACA,YAA+D,QAA6B;AA9tC9F;AA+tCI,UAAM,UAAU,2BAA2B;AAC3C,YAAQ,YAAa,OAAO,cAAc,UAAa,OAAO,cAAc,OACxE,UAAU,YAAY,OAAO,SAAS,IACtC;AACJ,YAAQ,YAAW,YAAO,aAAP,YAAmB;AACtC,WAAO;AAAA,EACT;AACF;AAEA,SAAS,8BAAiD;AACxD,SAAO,EAAE,WAAW,QAAW,UAAU,GAAG;AAC9C;AAEO,IAAM,oBAAmD;AAAA,EAC9D,OAAO,SAA4B,SAAuB,IAAI,0BAAa,GAAiB;AAC1F,QAAI,QAAQ,cAAc,QAAW;AACnC,gBAAU,OAAO,QAAQ,WAAW,OAAO,OAAO,EAAE,EAAE,KAAK,CAAC,EAAE,KAAK;AAAA,IACrE;AACA,QAAI,QAAQ,aAAa,IAAI;AAC3B,aAAO,OAAO,EAAE,EAAE,OAAO,QAAQ,QAAQ;AAAA,IAC3C;AACA,WAAO;AAAA,EACT;AAAA,EAEA,OAAO,OAAkC,QAAoC;AAC3E,UAAM,SAAS,iBAAiB,4BAAe,QAAQ,IAAI,0BAAa,KAAK;AAC7E,QAAI,MAAM,WAAW,SAAY,OAAO,MAAM,OAAO,MAAM;AAC3D,UAAM,UAAU,4BAA4B;AAC5C,WAAO,OAAO,MAAM,KAAK;AACvB,YAAM,MAAM,OAAO,OAAO;AAC1B,cAAQ,QAAQ,GAAG;AAAA,QACjB,KAAK,GAAG;AACN,cAAI,QAAQ,IAAI;AACd;AAAA,UACF;AAEA,kBAAQ,YAAY,UAAU,OAAO,QAAQ,OAAO,OAAO,CAAC;AAC5D;AAAA,QACF;AAAA,QACA,KAAK,GAAG;AACN,cAAI,QAAQ,IAAI;AACd;AAAA,UACF;AAEA,kBAAQ,WAAW,OAAO,OAAO;AACjC;AAAA,QACF;AAAA,MACF;AACA,WAAK,MAAM,OAAO,KAAK,QAAQ,GAAG;AAChC;AAAA,MACF;AACA,aAAO,KAAK,MAAM,CAAC;AAAA,IACrB;AACA,WAAO;AAAA,EACT;AAAA,EAEA,OAA2D,MAA6B;AACtF,WAAO,kBAAkB,YAAY,sBAAS,CAAC,CAAS;AAAA,EAC1D;AAAA,EACA,YAAgE,QAA8B;AA1xChG;AA2xCI,UAAM,UAAU,4BAA4B;AAC5C,YAAQ,YAAa,OAAO,cAAc,UAAa,OAAO,cAAc,OACxE,UAAU,YAAY,OAAO,SAAS,IACtC;AACJ,YAAQ,YAAW,YAAO,aAAP,YAAmB;AACtC,WAAO;AAAA,EACT;AACF;AAEA,SAAS,kCAAyD;AAChE,SAAO,EAAE,WAAW,QAAW,WAAW,QAAW,MAAM,QAAW,OAAO,OAAU;AACzF;AAEO,IAAM,wBAA2D;AAAA,EACtE,OAAO,SAAgC,SAAuB,IAAI,0BAAa,GAAiB;AAC9F,QAAI,QAAQ,cAAc,QAAW;AACnC,gBAAU,OAAO,QAAQ,WAAW,OAAO,OAAO,EAAE,EAAE,KAAK,CAAC,EAAE,KAAK;AAAA,IACrE;AACA,QAAI,QAAQ,cAAc,QAAW;AACnC,aAAO,OAAO,EAAE,EAAE,OAAO,QAAQ,SAAS;AAAA,IAC5C;AACA,QAAI,QAAQ,SAAS,QAAW;AAC9B,aAAO,OAAO,EAAE,EAAE,OAAO,QAAQ,IAAI;AAAA,IACvC;AACA,QAAI,QAAQ,UAAU,QAAW;AAC/B,aAAO,OAAO,EAAE,EAAE,OAAO,QAAQ,KAAK;AAAA,IACxC;AACA,WAAO;AAAA,EACT;AAAA,EAEA,OAAO,OAAkC,QAAwC;AAC/E,UAAM,SAAS,iBAAiB,4BAAe,QAAQ,IAAI,0BAAa,KAAK;AAC7E,QAAI,MAAM,WAAW,SAAY,OAAO,MAAM,OAAO,MAAM;AAC3D,UAAM,UAAU,gCAAgC;AAChD,WAAO,OAAO,MAAM,KAAK;AACvB,YAAM,MAAM,OAAO,OAAO;AAC1B,cAAQ,QAAQ,GAAG;AAAA,QACjB,KAAK,GAAG;AACN,cAAI,QAAQ,IAAI;AACd;AAAA,UACF;AAEA,kBAAQ,YAAY,UAAU,OAAO,QAAQ,OAAO,OAAO,CAAC;AAC5D;AAAA,QACF;AAAA,QACA,KAAK,GAAG;AACN,cAAI,QAAQ,IAAI;AACd;AAAA,UACF;AAEA,kBAAQ,YAAY,OAAO,OAAO;AAClC;AAAA,QACF;AAAA,QACA,KAAK,GAAG;AACN,cAAI,QAAQ,IAAI;AACd;AAAA,UACF;AAEA,kBAAQ,OAAO,OAAO,OAAO;AAC7B;AAAA,QACF;AAAA,QACA,KAAK,GAAG;AACN,cAAI,QAAQ,IAAI;AACd;AAAA,UACF;AAEA,kBAAQ,QAAQ,OAAO,OAAO;AAC9B;AAAA,QACF;AAAA,MACF;AACA,WAAK,MAAM,OAAO,KAAK,QAAQ,GAAG;AAChC;AAAA,MACF;AACA,aAAO,KAAK,MAAM,CAAC;AAAA,IACrB;AACA,WAAO;AAAA,EACT;AAAA,EAEA,OAA+D,MAAiC;AAC9F,WAAO,sBAAsB,YAAY,sBAAS,CAAC,CAAS;AAAA,EAC9D;AAAA,EACA,YAAoE,QAAkC;AA52CxG;AA62CI,UAAM,UAAU,gCAAgC;AAChD,YAAQ,YAAa,OAAO,cAAc,UAAa,OAAO,cAAc,OACxE,UAAU,YAAY,OAAO,SAAS,IACtC;AACJ,YAAQ,aAAY,YAAO,cAAP,YAAoB;AACxC,YAAQ,QAAO,YAAO,SAAP,YAAe;AAC9B,YAAQ,SAAQ,YAAO,UAAP,YAAgB;AAChC,WAAO;AAAA,EACT;AACF;AAEA,SAAS,+BAAmD;AAC1D,SAAO;AAAA,IACL,WAAW;AAAA,IACX,YAAY;AAAA,IACZ,cAAc;AAAA,IACd,iBAAiB;AAAA,IACjB,OAAO;AAAA,EACT;AACF;AAEO,IAAM,qBAAqD;AAAA,EAChE,OAAO,SAA6B,SAAuB,IAAI,0BAAa,GAAiB;AAC3F,QAAI,QAAQ,cAAc,QAAW;AACnC,gBAAU,OAAO,QAAQ,WAAW,OAAO,OAAO,EAAE,EAAE,KAAK,CAAC,EAAE,KAAK;AAAA,IACrE;AACA,QAAI,QAAQ,eAAe,QAAW;AACpC,aAAO,OAAO,EAAE,EAAE,OAAO,QAAQ,UAAU;AAAA,IAC7C;AACA,QAAI,QAAQ,iBAAiB,QAAW;AACtC,aAAO,OAAO,EAAE,EAAE,OAAO,QAAQ,YAAY;AAAA,IAC/C;AACA,QAAI,QAAQ,oBAAoB,QAAW;AACzC,aAAO,OAAO,EAAE,EAAE,OAAO,QAAQ,eAAe;AAAA,IAClD;AACA,QAAI,QAAQ,UAAU,QAAW;AAC/B,aAAO,OAAO,EAAE,EAAE,OAAO,QAAQ,KAAK;AAAA,IACxC;AACA,WAAO;AAAA,EACT;AAAA,EAEA,OAAO,OAAkC,QAAqC;AAC5E,UAAM,SAAS,iBAAiB,4BAAe,QAAQ,IAAI,0BAAa,KAAK;AAC7E,QAAI,MAAM,WAAW,SAAY,OAAO,MAAM,OAAO,MAAM;AAC3D,UAAM,UAAU,6BAA6B;AAC7C,WAAO,OAAO,MAAM,KAAK;AACvB,YAAM,MAAM,OAAO,OAAO;AAC1B,cAAQ,QAAQ,GAAG;AAAA,QACjB,KAAK,GAAG;AACN,cAAI,QAAQ,IAAI;AACd;AAAA,UACF;AAEA,kBAAQ,YAAY,UAAU,OAAO,QAAQ,OAAO,OAAO,CAAC;AAC5D;AAAA,QACF;AAAA,QACA,KAAK,GAAG;AACN,cAAI,QAAQ,IAAI;AACd;AAAA,UACF;AAEA,kBAAQ,aAAa,OAAO,OAAO;AACnC;AAAA,QACF;AAAA,QACA,KAAK,GAAG;AACN,cAAI,QAAQ,IAAI;AACd;AAAA,UACF;AAEA,kBAAQ,eAAe,OAAO,OAAO;AACrC;AAAA,QACF;AAAA,QACA,KAAK,GAAG;AACN,cAAI,QAAQ,IAAI;AACd;AAAA,UACF;AAEA,kBAAQ,kBAAkB,OAAO,OAAO;AACxC;AAAA,QACF;AAAA,QACA,KAAK,GAAG;AACN,cAAI,QAAQ,IAAI;AACd;AAAA,UACF;AAEA,kBAAQ,QAAQ,OAAO,OAAO;AAC9B;AAAA,QACF;AAAA,MACF;AACA,WAAK,MAAM,OAAO,KAAK,QAAQ,GAAG;AAChC;AAAA,MACF;AACA,aAAO,KAAK,MAAM,CAAC;AAAA,IACrB;AACA,WAAO;AAAA,EACT;AAAA,EAEA,OAA4D,MAA8B;AACxF,WAAO,mBAAmB,YAAY,sBAAS,CAAC,CAAS;AAAA,EAC3D;AAAA,EACA,YAAiE,QAA+B;AAj9ClG;AAk9CI,UAAM,UAAU,6BAA6B;AAC7C,YAAQ,YAAa,OAAO,cAAc,UAAa,OAAO,cAAc,OACxE,UAAU,YAAY,OAAO,SAAS,IACtC;AACJ,YAAQ,cAAa,YAAO,eAAP,YAAqB;AAC1C,YAAQ,gBAAe,YAAO,iBAAP,YAAuB;AAC9C,YAAQ,mBAAkB,YAAO,oBAAP,YAA0B;AACpD,YAAQ,SAAQ,YAAO,UAAP,YAAgB;AAChC,WAAO;AAAA,EACT;AACF;AAEA,SAAS,kBAAyB;AAChC,SAAO;AAAA,IACL,kBAAkB;AAAA,IAClB,oBAAoB;AAAA,IACpB,gBAAgB;AAAA,IAChB,eAAe;AAAA,IACf,cAAc;AAAA,IACd,aAAa;AAAA,IACb,eAAe;AAAA,IACf,YAAY;AAAA,IACZ,kBAAkB;AAAA,IAClB,KAAK;AAAA,IACL,QAAQ;AAAA,IACR,YAAY;AAAA,IACZ,aAAa;AAAA,IACb,UAAU;AAAA,IACV,aAAa;AAAA,IACb,cAAc;AAAA,IACd,kBAAkB;AAAA,IAClB,eAAe;AAAA,EACjB;AACF;AAEO,IAAM,QAA2B;AAAA,EACtC,OAAO,SAAgB,SAAuB,IAAI,0BAAa,GAAiB;AAC9E,QAAI,QAAQ,qBAAqB,QAAW;AAC1C,4BAAsB,OAAO,QAAQ,kBAAkB,OAAO,OAAO,EAAE,EAAE,KAAK,CAAC,EAAE,KAAK;AAAA,IACxF;AACA,QAAI,QAAQ,uBAAuB,QAAW;AAC5C,8BAAwB,OAAO,QAAQ,oBAAoB,OAAO,OAAO,EAAE,EAAE,KAAK,CAAC,EAAE,KAAK;AAAA,IAC5F;AACA,QAAI,QAAQ,mBAAmB,QAAW;AACxC,0BAAoB,OAAO,QAAQ,gBAAgB,OAAO,OAAO,EAAE,EAAE,KAAK,CAAC,EAAE,KAAK;AAAA,IACpF;AACA,QAAI,QAAQ,kBAAkB,QAAW;AACvC,yBAAmB,OAAO,QAAQ,eAAe,OAAO,OAAO,EAAE,EAAE,KAAK,CAAC,EAAE,KAAK;AAAA,IAClF;AACA,QAAI,QAAQ,iBAAiB,QAAW;AACtC,wBAAkB,OAAO,QAAQ,cAAc,OAAO,OAAO,EAAE,EAAE,KAAK,CAAC,EAAE,KAAK;AAAA,IAChF;AACA,QAAI,QAAQ,gBAAgB,QAAW;AACrC,uBAAiB,OAAO,QAAQ,aAAa,OAAO,OAAO,EAAE,EAAE,KAAK,CAAC,EAAE,KAAK;AAAA,IAC9E;AACA,QAAI,QAAQ,kBAAkB,QAAW;AACvC,yBAAmB,OAAO,QAAQ,eAAe,OAAO,OAAO,EAAE,EAAE,KAAK,CAAC,EAAE,KAAK;AAAA,IAClF;AACA,QAAI,QAAQ,eAAe,QAAW;AACpC,sBAAgB,OAAO,QAAQ,YAAY,OAAO,OAAO,EAAE,EAAE,KAAK,CAAC,EAAE,KAAK;AAAA,IAC5E;AACA,QAAI,QAAQ,qBAAqB,QAAW;AAC1C,4BAAsB,OAAO,QAAQ,kBAAkB,OAAO,OAAO,EAAE,EAAE,KAAK,CAAC,EAAE,KAAK;AAAA,IACxF;AACA,QAAI,QAAQ,QAAQ,QAAW;AAC7B,eAAS,OAAO,QAAQ,KAAK,OAAO,OAAO,EAAE,EAAE,KAAK,CAAC,EAAE,KAAK;AAAA,IAC9D;AACA,QAAI,QAAQ,WAAW,QAAW;AAChC,kBAAY,OAAO,QAAQ,QAAQ,OAAO,OAAO,EAAE,EAAE,KAAK,CAAC,EAAE,KAAK;AAAA,IACpE;AACA,QAAI,QAAQ,eAAe,QAAW;AACpC,sBAAgB,OAAO,QAAQ,YAAY,OAAO,OAAO,EAAE,EAAE,KAAK,CAAC,EAAE,KAAK;AAAA,IAC5E;AACA,QAAI,QAAQ,gBAAgB,QAAW;AACrC,uBAAiB,OAAO,QAAQ,aAAa,OAAO,OAAO,GAAG,EAAE,KAAK,CAAC,EAAE,KAAK;AAAA,IAC/E;AACA,QAAI,QAAQ,aAAa,QAAW;AAClC,oBAAc,OAAO,QAAQ,UAAU,OAAO,OAAO,GAAG,EAAE,KAAK,CAAC,EAAE,KAAK;AAAA,IACzE;AACA,QAAI,QAAQ,gBAAgB,QAAW;AACrC,uBAAiB,OAAO,QAAQ,aAAa,OAAO,OAAO,GAAG,EAAE,KAAK,CAAC,EAAE,KAAK;AAAA,IAC/E;AACA,QAAI,QAAQ,iBAAiB,QAAW;AACtC,wBAAkB,OAAO,QAAQ,cAAc,OAAO,OAAO,GAAG,EAAE,KAAK,CAAC,EAAE,KAAK;AAAA,IACjF;AACA,QAAI,QAAQ,qBAAqB,QAAW;AAC1C,4BAAsB,OAAO,QAAQ,kBAAkB,OAAO,OAAO,GAAG,EAAE,KAAK,CAAC,EAAE,KAAK;AAAA,IACzF;AACA,QAAI,QAAQ,kBAAkB,QAAW;AACvC,yBAAmB,OAAO,QAAQ,eAAe,OAAO,OAAO,GAAG,EAAE,KAAK,CAAC,EAAE,KAAK;AAAA,IACnF;AACA,WAAO;AAAA,EACT;AAAA,EAEA,OAAO,OAAkC,QAAwB;AAC/D,UAAM,SAAS,iBAAiB,4BAAe,QAAQ,IAAI,0BAAa,KAAK;AAC7E,QAAI,MAAM,WAAW,SAAY,OAAO,MAAM,OAAO,MAAM;AAC3D,UAAM,UAAU,gBAAgB;AAChC,WAAO,OAAO,MAAM,KAAK;AACvB,YAAM,MAAM,OAAO,OAAO;AAC1B,cAAQ,QAAQ,GAAG;AAAA,QACjB,KAAK,GAAG;AACN,cAAI,QAAQ,IAAI;AACd;AAAA,UACF;AAEA,kBAAQ,mBAAmB,sBAAsB,OAAO,QAAQ,OAAO,OAAO,CAAC;AAC/E;AAAA,QACF;AAAA,QACA,KAAK,GAAG;AACN,cAAI,QAAQ,IAAI;AACd;AAAA,UACF;AAEA,kBAAQ,qBAAqB,wBAAwB,OAAO,QAAQ,OAAO,OAAO,CAAC;AACnF;AAAA,QACF;AAAA,QACA,KAAK,GAAG;AACN,cAAI,QAAQ,IAAI;AACd;AAAA,UACF;AAEA,kBAAQ,iBAAiB,oBAAoB,OAAO,QAAQ,OAAO,OAAO,CAAC;AAC3E;AAAA,QACF;AAAA,QACA,KAAK,GAAG;AACN,cAAI,QAAQ,IAAI;AACd;AAAA,UACF;AAEA,kBAAQ,gBAAgB,mBAAmB,OAAO,QAAQ,OAAO,OAAO,CAAC;AACzE;AAAA,QACF;AAAA,QACA,KAAK,GAAG;AACN,cAAI,QAAQ,IAAI;AACd;AAAA,UACF;AAEA,kBAAQ,eAAe,kBAAkB,OAAO,QAAQ,OAAO,OAAO,CAAC;AACvE;AAAA,QACF;AAAA,QACA,KAAK,GAAG;AACN,cAAI,QAAQ,IAAI;AACd;AAAA,UACF;AAEA,kBAAQ,cAAc,iBAAiB,OAAO,QAAQ,OAAO,OAAO,CAAC;AACrE;AAAA,QACF;AAAA,QACA,KAAK,GAAG;AACN,cAAI,QAAQ,IAAI;AACd;AAAA,UACF;AAEA,kBAAQ,gBAAgB,mBAAmB,OAAO,QAAQ,OAAO,OAAO,CAAC;AACzE;AAAA,QACF;AAAA,QACA,KAAK,GAAG;AACN,cAAI,QAAQ,IAAI;AACd;AAAA,UACF;AAEA,kBAAQ,aAAa,gBAAgB,OAAO,QAAQ,OAAO,OAAO,CAAC;AACnE;AAAA,QACF;AAAA,QACA,KAAK,GAAG;AACN,cAAI,QAAQ,IAAI;AACd;AAAA,UACF;AAEA,kBAAQ,mBAAmB,sBAAsB,OAAO,QAAQ,OAAO,OAAO,CAAC;AAC/E;AAAA,QACF;AAAA,QACA,KAAK,IAAI;AACP,cAAI,QAAQ,IAAI;AACd;AAAA,UACF;AAEA,kBAAQ,MAAM,SAAS,OAAO,QAAQ,OAAO,OAAO,CAAC;AACrD;AAAA,QACF;AAAA,QACA,KAAK,IAAI;AACP,cAAI,QAAQ,IAAI;AACd;AAAA,UACF;AAEA,kBAAQ,SAAS,YAAY,OAAO,QAAQ,OAAO,OAAO,CAAC;AAC3D;AAAA,QACF;AAAA,QACA,KAAK,IAAI;AACP,cAAI,QAAQ,IAAI;AACd;AAAA,UACF;AAEA,kBAAQ,aAAa,gBAAgB,OAAO,QAAQ,OAAO,OAAO,CAAC;AACnE;AAAA,QACF;AAAA,QACA,KAAK,IAAI;AACP,cAAI,QAAQ,KAAK;AACf;AAAA,UACF;AAEA,kBAAQ,cAAc,iBAAiB,OAAO,QAAQ,OAAO,OAAO,CAAC;AACrE;AAAA,QACF;AAAA,QACA,KAAK,IAAI;AACP,cAAI,QAAQ,KAAK;AACf;AAAA,UACF;AAEA,kBAAQ,WAAW,cAAc,OAAO,QAAQ,OAAO,OAAO,CAAC;AAC/D;AAAA,QACF;AAAA,QACA,KAAK,IAAI;AACP,cAAI,QAAQ,KAAK;AACf;AAAA,UACF;AAEA,kBAAQ,cAAc,iBAAiB,OAAO,QAAQ,OAAO,OAAO,CAAC;AACrE;AAAA,QACF;AAAA,QACA,KAAK,IAAI;AACP,cAAI,QAAQ,KAAK;AACf;AAAA,UACF;AAEA,kBAAQ,eAAe,kBAAkB,OAAO,QAAQ,OAAO,OAAO,CAAC;AACvE;AAAA,QACF;AAAA,QACA,KAAK,IAAI;AACP,cAAI,QAAQ,KAAK;AACf;AAAA,UACF;AAEA,kBAAQ,mBAAmB,sBAAsB,OAAO,QAAQ,OAAO,OAAO,CAAC;AAC/E;AAAA,QACF;AAAA,QACA,KAAK,IAAI;AACP,cAAI,QAAQ,KAAK;AACf;AAAA,UACF;AAEA,kBAAQ,gBAAgB,mBAAmB,OAAO,QAAQ,OAAO,OAAO,CAAC;AACzE;AAAA,QACF;AAAA,MACF;AACA,WAAK,MAAM,OAAO,KAAK,QAAQ,GAAG;AAChC;AAAA,MACF;AACA,aAAO,KAAK,MAAM,CAAC;AAAA,IACrB;AACA,WAAO;AAAA,EACT;AAAA,EAEA,OAA+C,MAAiB;AAC9D,WAAO,MAAM,YAAY,sBAAS,CAAC,CAAS;AAAA,EAC9C;AAAA,EACA,YAAoD,QAAkB;AACpE,UAAM,UAAU,gBAAgB;AAChC,YAAQ,mBAAoB,OAAO,qBAAqB,UAAa,OAAO,qBAAqB,OAC7F,sBAAsB,YAAY,OAAO,gBAAgB,IACzD;AACJ,YAAQ,qBAAsB,OAAO,uBAAuB,UAAa,OAAO,uBAAuB,OACnG,wBAAwB,YAAY,OAAO,kBAAkB,IAC7D;AACJ,YAAQ,iBAAkB,OAAO,mBAAmB,UAAa,OAAO,mBAAmB,OACvF,oBAAoB,YAAY,OAAO,cAAc,IACrD;AACJ,YAAQ,gBAAiB,OAAO,kBAAkB,UAAa,OAAO,kBAAkB,OACpF,mBAAmB,YAAY,OAAO,aAAa,IACnD;AACJ,YAAQ,eAAgB,OAAO,iBAAiB,UAAa,OAAO,iBAAiB,OACjF,kBAAkB,YAAY,OAAO,YAAY,IACjD;AACJ,YAAQ,cAAe,OAAO,gBAAgB,UAAa,OAAO,gBAAgB,OAC9E,iBAAiB,YAAY,OAAO,WAAW,IAC/C;AACJ,YAAQ,gBAAiB,OAAO,kBAAkB,UAAa,OAAO,kBAAkB,OACpF,mBAAmB,YAAY,OAAO,aAAa,IACnD;AACJ,YAAQ,aAAc,OAAO,eAAe,UAAa,OAAO,eAAe,OAC3E,gBAAgB,YAAY,OAAO,UAAU,IAC7C;AACJ,YAAQ,mBAAoB,OAAO,qBAAqB,UAAa,OAAO,qBAAqB,OAC7F,sBAAsB,YAAY,OAAO,gBAAgB,IACzD;AACJ,YAAQ,MAAO,OAAO,QAAQ,UAAa,OAAO,QAAQ,OAAQ,SAAS,YAAY,OAAO,GAAG,IAAI;AACrG,YAAQ,SAAU,OAAO,WAAW,UAAa,OAAO,WAAW,OAC/D,YAAY,YAAY,OAAO,MAAM,IACrC;AACJ,YAAQ,aAAc,OAAO,eAAe,UAAa,OAAO,eAAe,OAC3E,gBAAgB,YAAY,OAAO,UAAU,IAC7C;AACJ,YAAQ,cAAe,OAAO,gBAAgB,UAAa,OAAO,gBAAgB,OAC9E,iBAAiB,YAAY,OAAO,WAAW,IAC/C;AACJ,YAAQ,WAAY,OAAO,aAAa,UAAa,OAAO,aAAa,OACrE,cAAc,YAAY,OAAO,QAAQ,IACzC;AACJ,YAAQ,cAAe,OAAO,gBAAgB,UAAa,OAAO,gBAAgB,OAC9E,iBAAiB,YAAY,OAAO,WAAW,IAC/C;AACJ,YAAQ,eAAgB,OAAO,iBAAiB,UAAa,OAAO,iBAAiB,OACjF,kBAAkB,YAAY,OAAO,YAAY,IACjD;AACJ,YAAQ,mBAAoB,OAAO,qBAAqB,UAAa,OAAO,qBAAqB,OAC7F,sBAAsB,YAAY,OAAO,gBAAgB,IACzD;AACJ,YAAQ,gBAAiB,OAAO,kBAAkB,UAAa,OAAO,kBAAkB,OACpF,mBAAmB,YAAY,OAAO,aAAa,IACnD;AACJ,WAAO;AAAA,EACT;AACF;AAcA,SAAS,aAAa,OAAuC;AAC3D,QAAM,MAAM,WAAW,OAAO,MAAM,SAAS,CAAC;AAC9C,MAAI,MAAM,WAAW,OAAO,kBAAkB;AAC5C,UAAM,IAAI,WAAW,MAAM,8CAA8C;AAAA,EAC3E;AACA,MAAI,MAAM,WAAW,OAAO,kBAAkB;AAC5C,UAAM,IAAI,WAAW,MAAM,+CAA+C;AAAA,EAC5E;AACA,SAAO;AACT;;;AD9xDA,SAAS,YAAY,KAAqB;AACxC,SAAO,IAAI,YAAY,EAAE,QAAQ,aAAa,CAAC,GAAG,WAAW,OAAO,YAAY,CAAC;AACnF;AAKO,SAAS,OAAO,OAA8B;AACnD,QAAM,aAAa,YAAY,MAAM,IAAI;AACzC,QAAM,EAAE,MAAM,WAAW,UAAU,GAAG,KAAK,IAAI;AAG/C,MAAI,SAAS,sBAAU,mBAAmB;AACxC,SAAK,WAAW,KAAK,SAAS,IAAI,CAAC,YAAqB;AACtD,YAAM,iBAAiB;AACvB,UAAI,eAAe,cAAc,QAAW;AAC1C,eAAO,EAAE,GAAG,SAAS,WAAW,CAAC,EAAE;AAAA,MACrC;AACA,aAAO;AAAA,IACT,CAAC;AAAA,EACH;AAGA,MAAI,SAAS,sBAAU,aAAa;AAClC,SAAK,QAAQ,KAAK,MAAM,IAAI,CAAC,eAAoB;AAAA,MAC/C,GAAG;AAAA,MACH,IAAe,uBAAuB,UAAU,GAAG,YAAY,CAAC;AAAA,IAClE,EAAE;AAAA,EACJ;AAEA,QAAM,eAAe;AAAA,IACnB,CAAC,UAAU,GAAG;AAAA,MACZ,WAAW;AAAA,QACT,MAAkB,UAAU,MAAM,IAA0C;AAAA,QAC5E;AAAA,QACA;AAAA,MACF;AAAA,MACA,GAAG;AAAA,IACL;AAAA,EACF;AACA,SAAmB,MAAM,OAAO,YAAY,EAAE,OAAO;AACvD;AAMO,SAAS,OAAO,MAA6B;AAnDpD;AAoDE,QAAM,QAAoB,MAAM,OAAO,IAAI;AAC3C,QAAM,UAAU,OAAO,OAAO,KAAK,EAAE,KAAK,CAAC,UAAU,UAAU,MAAS;AACxE,MAAI,CAAC,SAAS;AACZ,UAAM,IAAI,MAAM,eAAe;AAAA,EACjC;AACA,UAAQ,OAAmB,UAAU,QAAQ,UAAU,IAAI;AAC3D,UAAQ,YAAY,QAAQ,UAAU;AACtC,UAAQ,WAAW,QAAQ,UAAU;AAGrC,MAAI,QAAQ,SAAS,sBAAU,mBAAmB;AAChD,eAAW,WAAY,QAAgB,UAAuB;AAC5D,YAAM,iBAAiB;AACvB,YAAI,oBAAe,cAAf,mBAA0B,YAAW,GAAG;AAC1C,uBAAe,YAAY;AAAA,MAC7B;AAAA,IACF;AAAA,EACF;AAGA,MAAI,QAAQ,SAAS,sBAAU,aAAa;AAC1C,eAAW,aAAc,QAAgB,OAAO;AAC9C,gBAAU,KAAgB,uBAAuB,UAAU,EAAE,EAAE,YAAY;AAC3E,aAAO,KAAK,SAAS,EAAE,QAAQ,CAAC,QAAQ;AACtC,YAAI,UAAU,GAAG,MAAM,QAAW;AAChC,iBAAO,UAAU,GAAG;AAAA,QACtB;AAAA,MACF,CAAC;AAAA,IACH;AAAA,EACF;AAEA,SAAO,KAAK,OAAO,EAAE,QAAQ,CAAC,QAAQ;AACpC,QAAI,QAAQ,GAAG,MAAM,QAAW;AAC9B,aAAO,QAAQ,GAAG;AAAA,IACpB;AAAA,EACF,CAAC;AAED,SAAO,yBAAa,MAAM,OAAO;AACnC;;;ADxFO,IAAM,kBAAkB;", "names": ["import_wire", "import_wire", "JsonPatchOperationType", "import_wire", "EventType"]}