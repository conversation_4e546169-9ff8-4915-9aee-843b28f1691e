{"name": "vfile", "version": "5.3.7", "description": "Virtual file format for text processing", "license": "MIT", "keywords": ["vfile", "virtual", "file", "text", "processing", "message", "warning", "error", "remark", "retext", "rehype"], "repository": "vfile/vfile", "bugs": "https://github.com/vfile/vfile/issues", "funding": {"type": "opencollective", "url": "https://opencollective.com/unified"}, "author": "<PERSON> <<EMAIL>> (https://wooorm.com)", "contributors": ["<PERSON> <<EMAIL>> (https://wooorm.com)", "<PERSON> <<EMAIL>>", "<PERSON><PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON><PERSON><PERSON> <<EMAIL>>", "<PERSON><PERSON> Sorhus <<EMAIL>>"], "sideEffects": false, "type": "module", "main": "index.js", "types": "index.d.ts", "browser": {"./lib/minpath.js": "./lib/minpath.browser.js", "./lib/minproc.js": "./lib/minproc.browser.js", "./lib/minurl.js": "./lib/minurl.browser.js"}, "react-native": {"./lib/minpath.js": "./lib/minpath.browser.js", "./lib/minproc.js": "./lib/minproc.browser.js", "./lib/minurl.js": "./lib/minurl.browser.js"}, "files": ["lib/", "index.d.ts", "index.js"], "dependencies": {"@types/unist": "^2.0.0", "is-buffer": "^2.0.0", "unist-util-stringify-position": "^3.0.0", "vfile-message": "^3.0.0"}, "devDependencies": {"@types/node": "^18.0.0", "c8": "^7.0.0", "prettier": "^2.0.0", "remark-cli": "^11.0.0", "remark-preset-wooorm": "^9.0.0", "type-coverage": "^2.0.0", "typescript": "^4.0.0", "xo": "^0.53.0"}, "scripts": {"prepack": "npm run build && npm run format", "build": "tsc --build --clean && tsc --build && type-coverage", "format": "remark . -qfo && prettier . -w --loglevel warn && xo --fix", "test-api": "node --conditions development test.js", "test-coverage": "c8 --check-coverage --100 --reporter lcov npm run test-api", "test": "npm run build && npm run format && npm run test-coverage"}, "prettier": {"tabWidth": 2, "useTabs": false, "singleQuote": true, "bracketSpacing": false, "semi": false, "trailingComma": "none"}, "xo": {"prettier": true, "rules": {"unicorn/prefer-code-point": "off", "unicorn/prefer-node-protocol": "off"}}, "remarkConfig": {"plugins": ["preset-wooorm", ["lint-no-html", false]]}, "typeCoverage": {"atLeast": 100, "detail": true, "strict": true, "ignoreCatch": true, "#": "needed `any`s", "ignoreFiles": ["lib/minurl.shared.d.ts"]}}