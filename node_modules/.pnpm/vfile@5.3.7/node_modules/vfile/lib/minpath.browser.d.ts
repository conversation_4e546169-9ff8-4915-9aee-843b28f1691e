export namespace path {
  export {basename}
  export {dirname}
  export {extname}
  export {join}
  export const sep: string
}
/**
 * Get the basename from a path.
 *
 * @param {string} path
 *   File path.
 * @param {string | undefined} [ext]
 *   Extension to strip.
 * @returns {string}
 *   Stem or basename.
 */
declare function basename(path: string, ext?: string | undefined): string
/**
 * Get the dirname from a path.
 *
 * @param {string} path
 *   File path.
 * @returns {string}
 *   File path.
 */
declare function dirname(path: string): string
/**
 * Get an extname from a path.
 *
 * @param {string} path
 *   File path.
 * @returns {string}
 *   Extname.
 */
declare function extname(path: string): string
/**
 * Join segments from a path.
 *
 * @param {Array<string>} segments
 *   Path segments.
 * @returns {string}
 *   File path.
 */
declare function join(...segments: Array<string>): string
export {}
