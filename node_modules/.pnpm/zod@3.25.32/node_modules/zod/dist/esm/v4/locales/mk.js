import * as util from "../core/util.js";
const Sizable = {
    string: { unit: "знаци", verb: "да имаат" },
    file: { unit: "бајти", verb: "да имаат" },
    array: { unit: "ставки", verb: "да имаат" },
    set: { unit: "ставки", verb: "да имаат" },
};
function getSizing(origin) {
    return Sizable[origin] ?? null;
}
export const parsedType = (data) => {
    const t = typeof data;
    switch (t) {
        case "number": {
            return Number.isNaN(data) ? "NaN" : "број";
        }
        case "object": {
            if (Array.isArray(data)) {
                return "низа";
            }
            if (data === null) {
                return "null";
            }
            if (Object.getPrototypeOf(data) !== Object.prototype && data.constructor) {
                return data.constructor.name;
            }
        }
    }
    return t;
};
const Nouns = {
    regex: "внес",
    email: "адреса на е-пошта",
    url: "URL",
    emoji: "емоџи",
    uuid: "UUID",
    uuidv4: "UUIDv4",
    uuidv6: "UUIDv6",
    nanoid: "nanoid",
    guid: "GUID",
    cuid: "cuid",
    cuid2: "cuid2",
    ulid: "ULID",
    xid: "XID",
    ksuid: "KSUID",
    datetime: "ISO датум и време",
    date: "ISO датум",
    time: "ISO време",
    duration: "ISO времетраење",
    ipv4: "IPv4 адреса",
    ipv6: "IPv6 адреса",
    cidrv4: "IPv4 опсег",
    cidrv6: "IPv6 опсег",
    base64: "base64-енкодирана низа",
    base64url: "base64url-енкодирана низа",
    json_string: "JSON низа",
    e164: "E.164 број",
    jwt: "JWT",
    template_literal: "внес",
};
const error = (issue) => {
    switch (issue.code) {
        case "invalid_type":
            return `Грешен внес: се очекува ${issue.expected}, примено ${parsedType(issue.input)}`;
        // return `Invalid input: expected ${issue.expected}, received ${util.getParsedType(issue.input)}`;
        case "invalid_value":
            if (issue.values.length === 1)
                return `Invalid input: expected ${util.stringifyPrimitive(issue.values[0])}`;
            return `Грешана опција: се очекува една ${util.joinValues(issue.values, "|")}`;
        case "too_big": {
            const adj = issue.inclusive ? "<=" : "<";
            const sizing = getSizing(issue.origin);
            if (sizing)
                return `Премногу голем: се очекува ${issue.origin ?? "вредноста"} да има ${adj}${issue.maximum.toString()} ${sizing.unit ?? "елементи"}`;
            return `Премногу голем: се очекува ${issue.origin ?? "вредноста"} да биде ${adj}${issue.maximum.toString()}`;
        }
        case "too_small": {
            const adj = issue.inclusive ? ">=" : ">";
            const sizing = getSizing(issue.origin);
            if (sizing) {
                return `Премногу мал: се очекува ${issue.origin} да има ${adj}${issue.minimum.toString()} ${sizing.unit}`;
            }
            return `Премногу мал: се очекува ${issue.origin} да биде ${adj}${issue.minimum.toString()}`;
        }
        case "invalid_format": {
            const _issue = issue;
            if (_issue.format === "starts_with") {
                return `Неважечка низа: мора да започнува со "${_issue.prefix}"`;
            }
            if (_issue.format === "ends_with")
                return `Неважечка низа: мора да завршува со "${_issue.suffix}"`;
            if (_issue.format === "includes")
                return `Неважечка низа: мора да вклучува "${_issue.includes}"`;
            if (_issue.format === "regex")
                return `Неважечка низа: мора да одгоара на патернот ${_issue.pattern}`;
            return `Invalid ${Nouns[_issue.format] ?? issue.format}`;
        }
        case "not_multiple_of":
            return `Грешен број: мора да биде делив со ${issue.divisor}`;
        case "unrecognized_keys":
            return `${issue.keys.length > 1 ? "Непрепознаени клучеви" : "Непрепознаен клуч"}: ${util.joinValues(issue.keys, ", ")}`;
        case "invalid_key":
            return `Грешен клуч во ${issue.origin}`;
        case "invalid_union":
            return "Грешен внес";
        case "invalid_element":
            return `Грешна вредност во ${issue.origin}`;
        default:
            return `Грешен внес`;
    }
};
export { error };
export default function () {
    return {
        localeError: error,
    };
}
