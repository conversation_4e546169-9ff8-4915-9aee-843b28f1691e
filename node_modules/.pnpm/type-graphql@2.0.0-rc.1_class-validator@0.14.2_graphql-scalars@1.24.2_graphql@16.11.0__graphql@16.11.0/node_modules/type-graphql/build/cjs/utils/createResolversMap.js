"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.createResolversMap = void 0;
const graphql_1 = require("graphql");
function generateTypeResolver(abstractType, schema) {
    if (abstractType.resolveType) {
        return abstractType.resolveType;
    }
    const possibleObjectTypes = schema.getPossibleTypes(abstractType);
    return async (source, context, info) => {
        for (const objectType of possibleObjectTypes) {
            if (objectType.isTypeOf && (await objectType.isTypeOf(source, context, info))) {
                return objectType.name;
            }
        }
        return undefined;
    };
}
function generateFieldsResolvers(fields) {
    return Object.keys(fields).reduce((fieldsMap, fieldName) => {
        const field = fields[fieldName];
        if (field.subscribe) {
            fieldsMap[fieldName] = {
                subscribe: field.subscribe,
                resolve: field.resolve,
            };
        }
        else if (field.resolve) {
            fieldsMap[fieldName] = field.resolve;
        }
        return fieldsMap;
    }, {});
}
function createResolversMap(schema) {
    const typeMap = schema.getTypeMap();
    return Object.keys(typeMap)
        .filter(typeName => !typeName.startsWith("__"))
        .reduce((resolversMap, typeName) => {
        const type = typeMap[typeName];
        if (type instanceof graphql_1.GraphQLObjectType) {
            resolversMap[typeName] = {
                ...(type.isTypeOf && {
                    __isTypeOf: type.isTypeOf,
                }),
                ...generateFieldsResolvers(type.getFields()),
            };
        }
        if (type instanceof graphql_1.GraphQLInterfaceType) {
            resolversMap[typeName] = {
                __resolveType: generateTypeResolver(type, schema),
                ...generateFieldsResolvers(type.getFields()),
            };
        }
        if (type instanceof graphql_1.GraphQLScalarType) {
            resolversMap[typeName] = type;
        }
        if (type instanceof graphql_1.GraphQLEnumType) {
            const enumValues = type.getValues();
            resolversMap[typeName] = enumValues.reduce((enumMap, { name, value }) => {
                enumMap[name] = value;
                return enumMap;
            }, {});
        }
        if (type instanceof graphql_1.GraphQLUnionType) {
            resolversMap[typeName] = {
                __resolveType: generateTypeResolver(type, schema),
            };
        }
        return resolversMap;
    }, {});
}
exports.createResolversMap = createResolversMap;
