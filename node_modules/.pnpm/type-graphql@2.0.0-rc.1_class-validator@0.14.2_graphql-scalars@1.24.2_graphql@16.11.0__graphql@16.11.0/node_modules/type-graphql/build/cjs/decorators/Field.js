"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.Field = void 0;
const errors_1 = require("../errors");
const decorators_1 = require("../helpers/decorators");
const findType_1 = require("../helpers/findType");
const getMetadataStorage_1 = require("../metadata/getMetadataStorage");
function Field(returnTypeFuncOrOptions, maybeOptions) {
    return (prototype, propertyKey, descriptor) => {
        if (typeof propertyKey === "symbol") {
            throw new errors_1.SymbolKeysNotSupportedError();
        }
        const { options, returnTypeFunc } = (0, decorators_1.getTypeDecoratorParams)(returnTypeFuncOrOptions, maybeOptions);
        const isResolver = Boolean(descriptor);
        const isResolverMethod = Boolean(descriptor && descriptor.value);
        const { getType, typeOptions } = (0, findType_1.findType)({
            metadataKey: isResolverMethod ? "design:returntype" : "design:type",
            prototype,
            propertyKey,
            returnTypeFunc,
            typeOptions: options,
        });
        (0, getMetadataStorage_1.getMetadataStorage)().collectClassFieldMetadata({
            name: propertyKey,
            schemaName: options.name || propertyKey,
            getType,
            typeOptions,
            complexity: options.complexity,
            target: prototype.constructor,
            description: options.description,
            deprecationReason: options.deprecationReason,
            simple: options.simple,
        });
        if (isResolver) {
            (0, getMetadataStorage_1.getMetadataStorage)().collectFieldResolverMetadata({
                kind: "internal",
                methodName: propertyKey,
                schemaName: options.name || propertyKey,
                target: prototype.constructor,
                complexity: options.complexity,
            });
        }
    };
}
exports.Field = Field;
