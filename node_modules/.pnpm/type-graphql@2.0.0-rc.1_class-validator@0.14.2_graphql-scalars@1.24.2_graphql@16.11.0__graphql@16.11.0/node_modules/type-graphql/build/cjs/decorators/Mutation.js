"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.Mutation = void 0;
const decorators_1 = require("../helpers/decorators");
const resolver_metadata_1 = require("../helpers/resolver-metadata");
const getMetadataStorage_1 = require("../metadata/getMetadataStorage");
function Mutation(returnTypeFuncOrOptions, maybeOptions) {
    const { options, returnTypeFunc } = (0, decorators_1.getTypeDecoratorParams)(returnTypeFuncOrOptions, maybeOptions);
    return (prototype, methodName) => {
        const metadata = (0, resolver_metadata_1.getResolverMetadata)(prototype, methodName, returnTypeFunc, options);
        (0, getMetadataStorage_1.getMetadataStorage)().collectMutationHandlerMetadata(metadata);
    };
}
exports.Mutation = Mutation;
