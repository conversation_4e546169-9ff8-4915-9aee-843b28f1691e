"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.MetadataStorage = void 0;
const errors_1 = require("../errors");
const utils_1 = require("./utils");
class MetadataStorage {
    constructor() {
        this.queries = [];
        this.mutations = [];
        this.subscriptions = [];
        this.fieldResolvers = [];
        this.objectTypes = [];
        this.inputTypes = [];
        this.argumentTypes = [];
        this.interfaceTypes = [];
        this.authorizedFields = [];
        this.enums = [];
        this.unions = [];
        this.middlewares = [];
        this.classDirectives = [];
        this.fieldDirectives = [];
        this.argumentDirectives = [];
        this.classExtensions = [];
        this.fieldExtensions = [];
        this.resolverClasses = [];
        this.fields = [];
        this.params = [];
    }
    collectQueryHandlerMetadata(definition) {
        this.queries.push(definition);
    }
    collectMutationHandlerMetadata(definition) {
        this.mutations.push(definition);
    }
    collectSubscriptionHandlerMetadata(definition) {
        this.subscriptions.push(definition);
    }
    collectFieldResolverMetadata(definition) {
        this.fieldResolvers.push(definition);
    }
    collectObjectMetadata(definition) {
        this.objectTypes.push(definition);
    }
    collectInputMetadata(definition) {
        this.inputTypes.push(definition);
    }
    collectArgsMetadata(definition) {
        this.argumentTypes.push(definition);
    }
    collectInterfaceMetadata(definition) {
        this.interfaceTypes.push(definition);
    }
    collectAuthorizedFieldMetadata(definition) {
        this.authorizedFields.push(definition);
    }
    collectEnumMetadata(definition) {
        this.enums.push(definition);
    }
    collectUnionMetadata(definition) {
        const unionSymbol = Symbol(definition.name);
        this.unions.push({
            ...definition,
            symbol: unionSymbol,
        });
        return unionSymbol;
    }
    collectMiddlewareMetadata(definition) {
        this.middlewares.push(definition);
    }
    collectResolverClassMetadata(definition) {
        this.resolverClasses.push(definition);
    }
    collectClassFieldMetadata(definition) {
        this.fields.push(definition);
    }
    collectHandlerParamMetadata(definition) {
        this.params.push(definition);
    }
    collectDirectiveClassMetadata(definition) {
        this.classDirectives.push(definition);
    }
    collectDirectiveFieldMetadata(definition) {
        this.fieldDirectives.push(definition);
    }
    collectDirectiveArgumentMetadata(definition) {
        this.argumentDirectives.push(definition);
    }
    collectExtensionsClassMetadata(definition) {
        this.classExtensions.push(definition);
    }
    collectExtensionsFieldMetadata(definition) {
        this.fieldExtensions.push(definition);
    }
    build(options) {
        this.classDirectives.reverse();
        this.fieldDirectives.reverse();
        this.argumentDirectives.reverse();
        this.classExtensions.reverse();
        this.fieldExtensions.reverse();
        this.buildClassMetadata(this.objectTypes);
        this.buildClassMetadata(this.inputTypes);
        this.buildClassMetadata(this.argumentTypes);
        this.buildClassMetadata(this.interfaceTypes);
        this.buildFieldResolverMetadata(this.fieldResolvers, options);
        this.buildResolversMetadata(this.queries);
        this.buildResolversMetadata(this.mutations);
        this.buildResolversMetadata(this.subscriptions);
        this.buildExtendedResolversMetadata();
    }
    clear() {
        this.queries = [];
        this.mutations = [];
        this.subscriptions = [];
        this.fieldResolvers = [];
        this.objectTypes = [];
        this.inputTypes = [];
        this.argumentTypes = [];
        this.interfaceTypes = [];
        this.authorizedFields = [];
        this.enums = [];
        this.unions = [];
        this.middlewares = [];
        this.classDirectives = [];
        this.fieldDirectives = [];
        this.argumentDirectives = [];
        this.classExtensions = [];
        this.fieldExtensions = [];
        this.resolverClasses = [];
        this.fields = [];
        this.params = [];
    }
    buildClassMetadata(definitions) {
        definitions.forEach(def => {
            if (!def.fields) {
                const fields = this.fields.filter(field => field.target === def.target);
                fields.forEach(field => {
                    field.roles = this.findFieldRoles(field.target, field.name);
                    field.params = this.params.filter(param => param.target === field.target && field.name === param.methodName);
                    field.middlewares = (0, utils_1.mapMiddlewareMetadataToArray)(this.middlewares.filter(middleware => middleware.target === field.target && middleware.fieldName === field.name));
                    field.directives = this.fieldDirectives
                        .filter(it => it.target === field.target && it.fieldName === field.name)
                        .map(it => it.directive);
                    field.extensions = this.findExtensions(field.target, field.name);
                });
                def.fields = fields;
            }
            if (!def.directives) {
                def.directives = this.classDirectives
                    .filter(it => it.target === def.target)
                    .map(it => it.directive);
            }
            if (!def.extensions) {
                def.extensions = this.findExtensions(def.target);
            }
        });
    }
    buildResolversMetadata(definitions) {
        definitions.forEach(def => {
            const resolverClassMetadata = this.resolverClasses.find(resolver => resolver.target === def.target);
            def.resolverClassMetadata = resolverClassMetadata;
            def.params = this.params.filter(param => param.target === def.target && def.methodName === param.methodName);
            def.roles = this.findFieldRoles(def.target, def.methodName);
            def.middlewares = (0, utils_1.mapMiddlewareMetadataToArray)(this.middlewares.filter(middleware => middleware.target === def.target && def.methodName === middleware.fieldName));
            def.directives = this.fieldDirectives
                .filter(it => it.target === def.target && it.fieldName === def.methodName)
                .map(it => it.directive);
            def.extensions = this.findExtensions(def.target, def.methodName);
        });
    }
    buildFieldResolverMetadata(definitions, options) {
        this.buildResolversMetadata(definitions);
        definitions.forEach(def => {
            def.roles = this.findFieldRoles(def.target, def.methodName);
            def.directives = this.fieldDirectives
                .filter(it => it.target === def.target && it.fieldName === def.methodName)
                .map(it => it.directive);
            def.extensions = this.findExtensions(def.target, def.methodName);
            def.getObjectType =
                def.kind === "external"
                    ? this.resolverClasses.find(resolver => resolver.target === def.target).getObjectType
                    : () => def.target;
            if (def.kind === "external") {
                const typeClass = this.resolverClasses.find(resolver => resolver.target === def.target)
                    .getObjectType();
                const typeMetadata = this.objectTypes.find(objTypeDef => objTypeDef.target === typeClass) ||
                    this.interfaceTypes.find(interfaceTypeDef => interfaceTypeDef.target === typeClass);
                if (!typeMetadata) {
                    throw new Error(`Unable to find type metadata for input type or object type named '${typeClass.name}'`);
                }
                const typeField = typeMetadata.fields.find(fieldDef => fieldDef.schemaName === def.schemaName);
                if (!typeField) {
                    const shouldCollectFieldMetadata = !options.resolvers ||
                        options.resolvers.some(resolverCls => resolverCls === def.target ||
                            Object.prototype.isPrototypeOf.call(def.target, resolverCls));
                    if (!def.getType || !def.typeOptions) {
                        throw new errors_1.NoExplicitTypeError(def.target.name, def.methodName);
                    }
                    if (shouldCollectFieldMetadata) {
                        const fieldMetadata = {
                            name: def.methodName,
                            schemaName: def.schemaName,
                            getType: def.getType,
                            target: typeClass,
                            typeOptions: def.typeOptions,
                            deprecationReason: def.deprecationReason,
                            description: def.description,
                            complexity: def.complexity,
                            roles: def.roles,
                            middlewares: def.middlewares,
                            params: def.params,
                            directives: def.directives,
                            extensions: def.extensions,
                        };
                        this.collectClassFieldMetadata(fieldMetadata);
                        typeMetadata.fields.push(fieldMetadata);
                    }
                }
                else {
                    typeField.complexity = def.complexity;
                    if (typeField.params.length === 0) {
                        typeField.params = def.params;
                    }
                    if (def.roles) {
                        typeField.roles = def.roles;
                    }
                    else if (typeField.roles) {
                        def.roles = typeField.roles;
                    }
                }
            }
        });
    }
    buildExtendedResolversMetadata() {
        this.resolverClasses.forEach(def => {
            let superResolver = Object.getPrototypeOf(def.target);
            while (superResolver.prototype) {
                const superResolverMetadata = this.resolverClasses.find(it => it.target === superResolver);
                if (superResolverMetadata) {
                    this.queries = (0, utils_1.mapSuperResolverHandlers)(this.queries, superResolver, def);
                    this.mutations = (0, utils_1.mapSuperResolverHandlers)(this.mutations, superResolver, def);
                    this.subscriptions = (0, utils_1.mapSuperResolverHandlers)(this.subscriptions, superResolver, def);
                    this.fieldResolvers = (0, utils_1.mapSuperFieldResolverHandlers)(this.fieldResolvers, superResolver, def);
                }
                superResolver = Object.getPrototypeOf(superResolver);
            }
        });
    }
    findFieldRoles(target, fieldName) {
        const authorizedField = this.authorizedFields.find(authField => authField.target === target && authField.fieldName === fieldName);
        if (!authorizedField) {
            return undefined;
        }
        return authorizedField.roles;
    }
    findExtensions(target, fieldName) {
        const storedExtensions = fieldName
            ? this.fieldExtensions
            : this.classExtensions;
        return storedExtensions
            .filter(entry => (entry.target === target || Object.prototype.isPrototypeOf.call(entry.target, target)) &&
            (!("fieldName" in entry) || entry.fieldName === fieldName))
            .reduce((extensions, entry) => ({ ...extensions, ...entry.extensions }), {});
    }
}
exports.MetadataStorage = MetadataStorage;
