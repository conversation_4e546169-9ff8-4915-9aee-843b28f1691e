"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.getMetadataStorage = void 0;
const metadata_storage_1 = require("./metadata-storage");
function getMetadataStorage() {
    if (!global.TypeGraphQLMetadataStorage) {
        global.TypeGraphQLMetadataStorage = new metadata_storage_1.MetadataStorage();
    }
    return global.TypeGraphQLMetadataStorage;
}
exports.getMetadataStorage = getMetadataStorage;
