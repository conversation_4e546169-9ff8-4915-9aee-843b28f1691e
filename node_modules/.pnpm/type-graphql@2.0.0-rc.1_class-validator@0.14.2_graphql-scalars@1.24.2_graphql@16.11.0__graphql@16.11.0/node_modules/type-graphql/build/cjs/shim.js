"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.GraphQLTimestamp = exports.GraphQLISODateTime = exports.ID = exports.Float = exports.Int = exports.UseMiddleware = exports.createUnionType = exports.Subscription = exports.Root = exports.Resolver = exports.Query = exports.ObjectType = exports.Mutation = exports.InterfaceType = exports.InputType = exports.Info = exports.FieldResolver = exports.Field = exports.registerEnumType = exports.Extensions = exports.Directive = exports.Ctx = exports.createMethodDecorator = exports.createParamDecorator = exports.Authorized = exports.ArgsType = exports.Args = exports.Arg = exports.dummyDecorator = exports.dummyFn = exports.dummyValue = void 0;
exports.dummyValue = "";
function dummyFn() { }
exports.dummyFn = dummyFn;
function dummyDecorator() {
    return dummyFn;
}
exports.dummyDecorator = dummyDecorator;
exports.Arg = dummyDecorator;
exports.Args = dummyDecorator;
exports.ArgsType = dummyDecorator;
exports.Authorized = dummyDecorator;
exports.createParamDecorator = dummyFn;
exports.createMethodDecorator = dummyFn;
exports.Ctx = dummyDecorator;
exports.Directive = dummyDecorator;
exports.Extensions = dummyDecorator;
exports.registerEnumType = dummyFn;
exports.Field = dummyDecorator;
exports.FieldResolver = dummyDecorator;
exports.Info = dummyDecorator;
exports.InputType = dummyDecorator;
exports.InterfaceType = dummyDecorator;
exports.Mutation = dummyDecorator;
exports.ObjectType = dummyDecorator;
exports.Query = dummyDecorator;
exports.Resolver = dummyDecorator;
exports.Root = dummyDecorator;
exports.Subscription = dummyDecorator;
exports.createUnionType = dummyFn;
exports.UseMiddleware = dummyDecorator;
exports.Int = exports.dummyValue;
exports.Float = exports.dummyValue;
exports.ID = exports.dummyValue;
exports.GraphQLISODateTime = exports.dummyValue;
exports.GraphQLTimestamp = exports.dummyValue;
