export const dummyValue = "";
export function dummyFn() { }
export function dummyDecorator() {
    return dummyFn;
}
export const Arg = dummyDecorator;
export const Args = dummyDecorator;
export const ArgsType = dummyDecorator;
export const Authorized = dummyDecorator;
export const createParamDecorator = dummyFn;
export const createMethodDecorator = dummyFn;
export const Ctx = dummyDecorator;
export const Directive = dummyDecorator;
export const Extensions = dummyDecorator;
export const registerEnumType = dummyFn;
export const Field = dummyDecorator;
export const FieldResolver = dummyDecorator;
export const Info = dummyDecorator;
export const InputType = dummyDecorator;
export const InterfaceType = dummyDecorator;
export const Mutation = dummyDecorator;
export const ObjectType = dummyDecorator;
export const Query = dummyDecorator;
export const Resolver = dummyDecorator;
export const Root = dummyDecorator;
export const Subscription = dummyDecorator;
export const createUnionType = dummyFn;
export const UseMiddleware = dummyDecorator;
export const Int = dummyValue;
export const Float = dummyValue;
export const ID = dummyValue;
export const GraphQLISODateTime = dummyValue;
export const GraphQLTimestamp = dummyValue;
