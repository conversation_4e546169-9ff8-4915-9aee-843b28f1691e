import { SymbolKeysNotSupportedError } from "../errors/index.js";
import { getArrayFromOverloadedRest } from "../helpers/decorators.js";
import { getMetadataStorage } from "../metadata/getMetadataStorage.js";
export function UseMiddleware(...middlewaresOrMiddlewareArray) {
    const middlewares = getArrayFromOverloadedRest(middlewaresOrMiddlewareArray);
    return (prototype, propertyKey, _descriptor) => {
        if (typeof propertyKey === "symbol") {
            throw new SymbolKeysNotSupportedError();
        }
        getMetadataStorage().collectMiddlewareMetadata({
            target: prototype.constructor,
            fieldName: propertyKey,
            middlewares,
        });
    };
}
