import { SymbolKeysNotSupportedError } from "../errors/index.js";
import { getArrayFromOverloadedRest } from "../helpers/decorators.js";
import { getMetadataStorage } from "../metadata/getMetadataStorage.js";
export function Authorized(...rolesOrRolesArray) {
    const roles = getArrayFromOverloadedRest(rolesOrRolesArray);
    return (prototype, propertyKey, _descriptor) => {
        if (typeof propertyKey === "symbol") {
            throw new SymbolKeysNotSupportedError();
        }
        getMetadataStorage().collectAuthorizedFieldMetadata({
            target: prototype.constructor,
            fieldName: propertyKey,
            roles,
        });
    };
}
