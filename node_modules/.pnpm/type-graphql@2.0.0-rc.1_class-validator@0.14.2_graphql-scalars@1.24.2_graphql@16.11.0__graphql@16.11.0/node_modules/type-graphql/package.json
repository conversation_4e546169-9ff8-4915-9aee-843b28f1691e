{"name": "type-graphql", "version": "2.0.0-rc.1", "private": false, "description": "Create GraphQL schema and resolvers with TypeScript, using classes and decorators!", "keywords": ["typescript", "graphql", "schema", "resolvers", "api", "decorators", "controllers", "apollo"], "homepage": "https://typegraphql.com", "bugs": {"url": "https://github.com/MichalLytek/type-graphql/issues"}, "repository": {"type": "git", "url": "git+https://github.com/MichalLytek/type-graphql.git"}, "funding": [{"type": "github", "url": "https://github.com/sponsors/TypeGraphQL"}, {"type": "opencollective", "url": "https://opencollective.com/typegraphql"}], "license": "MIT", "author": "<PERSON><PERSON><PERSON> (https://github.com/MichalLytek)", "exports": {".": {"require": "./build/cjs/index.js", "import": "./build/esm/index.js", "types": "./build/typings/index.d.ts"}, "./shim": {"require": "./build/cjs/shim.js", "import": "./build/esm/shim.js", "types": "./build/typings/shim.ts"}}, "main": "./build/cjs/index.js", "module": "./build/esm/index.js", "types": "./build/typings/index.d.ts", "files": ["./build"], "scripts": {"prebuild": "npm run clean:build && npm run check:version", "build": "npx tsc --build ./tsconfig.cjs.json ./tsconfig.esm.json ./tsconfig.typings.json", "postbuild": "npx shx rm ./build/typings/shim.d.ts && npx shx cp ./src/shim.ts ./build/typings && npx ts-node ./scripts/package.json.ts", "prebuild:benchmarks": "npm run clean:build:benchmarks", "build:benchmarks": "npx tsc --build ./benchmarks/tsconfig.json", "check": "npx npm-run-all --npm-path npm \"check:*\"", "check:benchmarks": "npx tsc --project ./benchmarks/tsconfig.json --noEmit", "check:examples": "npx tsc --project ./examples/tsconfig.json --noEmit", "check:format": "npx prettier --check .", "check:lint": "npx eslint .", "check:markdown": "npx markdownlint \"**/*.md\"", "check:script": "npx shellcheck ./.husky/pre-commit", "check:scripts": "npx tsc --project ./scripts/tsconfig.json --noEmit", "check:spell": "npx cspell lint --config cspell.json --no-progress --show-context \"**\"", "check:type": "npx npm-run-all --npm-path npm \"check:type:*\"", "check:type:cjs": "npx tsc --project ./tsconfig.cjs.json --noEmit", "check:type:esm": "npx tsc --project ./tsconfig.esm.json --noEmit", "check:type:tests": "npx tsc --project ./tests/tsconfig.json --noEmit", "check:type:typings": "npx tsc --project ./tsconfig.typings.json --noEmit --emitDeclarationOnly false", "check:version": "npx ts-node ./scripts/version.ts", "clean": "npx npm-run-all --npm-path npm \"clean:*\"", "clean:build": "npx shx rm -rf ./build", "clean:build:benchmarks": "npx shx rm -rf ./benchmarks/build", "clean:coverage": "npx shx rm -rf ./coverage", "prepublishOnly": "npm run build && npm run gen:readme", "pretest": "npm run clean:coverage", "test": "npx jest --verbose --coverage", "test:watch": "npx jest --watch", "pretest:ci": "npm run clean:coverage", "test:ci": "npx jest --verbose --coverage --ci --forceExit --runInBand", "release": "npm version --message \"release: %s\"", "version": "npm run --prefix ./website new-release --release=$npm_package_version && git add -A .", "docs": "npm run start --prefix website", "fix": "npx npm-run-all --npm-path npm \"fix:*\"", "fix:format": "npx prettier --write .", "fix:lint": "npx eslint --fix .", "fix:markdown": "npx markdownlint --fix \"**/*.md\"", "gen:docs": "npx ts-node ./scripts/markdown.ts --on docs", "gen:readme": "npx ts-node ./scripts/markdown.ts --on readme", "gen:sponsorkit": "npx sponsorkit --width=320 --dir=./images --name=github-sponsors", "postgen:sponsorkit": "npx shx cp ./images/github-sponsors.svg ./website/static/img/github-sponsors.svg", "prepare": "npx ts-patch install -s && npx husky install"}, "peerDependencies": {"class-validator": ">=0.14.0", "graphql": "^16.8.1", "graphql-scalars": "^1.22.4"}, "peerDependenciesMeta": {"class-validator": {"optional": true}}, "dependencies": {"@graphql-yoga/subscription": "^5.0.0", "@types/node": "*", "@types/semver": "^7.5.6", "graphql-query-complexity": "^0.12.0", "semver": "^7.5.4", "tslib": "^2.6.2"}, "devDependencies": {"@apollo/cache-control-types": "^1.0.3", "@apollo/gateway": "^2.7.1", "@apollo/server": "^4.10.0", "@apollo/server-plugin-response-cache": "^4.1.3", "@apollo/subgraph": "^2.7.1", "@cspell/dict-node": "^4.0.3", "@cspell/dict-npm": "^5.0.14", "@cspell/dict-shell": "^1.0.6", "@cspell/dict-typescript": "^3.1.2", "@cspell/eslint-plugin": "^8.3.2", "@graphql-tools/schema": "^10.0.2", "@graphql-tools/utils": "^10.1.2", "@graphql-yoga/redis-event-target": "^3.0.0", "@mikro-orm/core": "^6.0.2", "@mikro-orm/postgresql": "^6.2.1", "@typegoose/typegoose": "^12.2.0", "@types/jest": "^29.5.11", "@types/lodash.merge": "^4.6.9", "@types/node": "^20.11.24", "@types/shelljs": "^0.8.15", "@types/yargs": "^17.0.32", "@typescript-eslint/eslint-plugin": "^6.18.1", "@typescript-eslint/parser": "^6.18.1", "class-validator": "^0.14.0", "cspell": "^8.7.0", "dotenv": "^16.4.5", "eslint": "^8.56.0", "eslint-config-airbnb-base": "^15.0.0", "eslint-config-airbnb-typescript": "^17.1.0", "eslint-config-prettier": "^9.1.0", "eslint-import-resolver-typescript": "^3.6.1", "eslint-plugin-import": "^2.29.1", "eslint-plugin-jest": "^27.6.2", "eslint-plugin-tsdoc": "^0.2.17", "expect": "^29.7.0", "glob": "^10.3.10", "graphql": "^16.8.1", "graphql-scalars": "^1.22.4", "graphql-tag": "^2.12.6", "graphql-yoga": "^5.1.1", "husky": "^8.0.3", "ioredis": "^5.3.2", "jest": "^29.7.0", "joiful": "^3.0.2", "lint-staged": "^15.2.0", "lodash.merge": "^4.6.2", "markdownlint": "^0.33.0", "markdownlint-cli": "^0.38.0", "mongoose": "^8.0.4", "npm-run-all": "^4.1.5", "pg": "^8.11.3", "prettier": "^3.2.5", "prettier-plugin-sh": "^0.14.0", "reflect-metadata": "^0.1.13", "shellcheck": "^2.2.0", "shelljs": "^0.8.5", "shx": "^0.3.4", "sponsorkit": "^0.9.1", "ts-jest": "^29.1.1", "ts-node": "^10.9.2", "ts-patch": "^3.1.1", "tsconfig-paths": "^4.2.0", "tsyringe": "^4.8.0", "typedi": "^0.10.0", "typeorm": "^0.3.19", "typescript": "^5.3.3", "typescript-transform-paths": "^3.4.6", "typescript-transformer-esm": "^1.1.0", "yargs": "^17.7.2"}, "engines": {"node": ">= 18.12.0"}, "publishConfig": {"access": "public", "provenance": true}}