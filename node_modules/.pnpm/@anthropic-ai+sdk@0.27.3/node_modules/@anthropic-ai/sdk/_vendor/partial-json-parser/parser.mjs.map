{"version": 3, "file": "parser.mjs", "sourceRoot": "", "sources": ["../../src/_vendor/partial-json-parser/parser.ts"], "names": [], "mappings": "AAKA,MAAM,QAAQ,GAAG,CAAC,KAAa,EAAW,EAAE;IACxC,IAAI,OAAO,GAAG,CAAC,CAAC;IAChB,IAAI,MAAM,GAAY,EAAE,CAAC;IAEzB,OAAO,OAAO,GAAG,KAAK,CAAC,MAAM,EAAE;QAC7B,IAAI,IAAI,GAAG,KAAK,CAAC,OAAO,CAAC,CAAC;QAE1B,IAAI,IAAI,KAAK,IAAI,EAAE;YACjB,OAAO,EAAE,CAAC;YACV,SAAS;SACV;QAED,IAAI,IAAI,KAAK,GAAG,EAAE;YAChB,MAAM,CAAC,IAAI,CAAC;gBACV,IAAI,EAAE,OAAO;gBACb,KAAK,EAAE,GAAG;aACX,CAAC,CAAC;YAEH,OAAO,EAAE,CAAC;YACV,SAAS;SACV;QAED,IAAI,IAAI,KAAK,GAAG,EAAE;YAChB,MAAM,CAAC,IAAI,CAAC;gBACV,IAAI,EAAE,OAAO;gBACb,KAAK,EAAE,GAAG;aACX,CAAC,CAAC;YAEH,OAAO,EAAE,CAAC;YACV,SAAS;SACV;QAED,IAAI,IAAI,KAAK,GAAG,EAAE;YAChB,MAAM,CAAC,IAAI,CAAC;gBACV,IAAI,EAAE,OAAO;gBACb,KAAK,EAAE,GAAG;aACX,CAAC,CAAC;YAEH,OAAO,EAAE,CAAC;YACV,SAAS;SACV;QAED,IAAI,IAAI,KAAK,GAAG,EAAE;YAChB,MAAM,CAAC,IAAI,CAAC;gBACV,IAAI,EAAE,OAAO;gBACb,KAAK,EAAE,GAAG;aACX,CAAC,CAAC;YAEH,OAAO,EAAE,CAAC;YACV,SAAS;SACV;QAED,IAAI,IAAI,KAAK,GAAG,EAAE;YAChB,MAAM,CAAC,IAAI,CAAC;gBACV,IAAI,EAAE,WAAW;gBACjB,KAAK,EAAE,GAAG;aACX,CAAC,CAAC;YAEH,OAAO,EAAE,CAAC;YACV,SAAS;SACV;QAED,IAAI,IAAI,KAAK,GAAG,EAAE;YAChB,MAAM,CAAC,IAAI,CAAC;gBACV,IAAI,EAAE,WAAW;gBACjB,KAAK,EAAE,GAAG;aACX,CAAC,CAAC;YAEH,OAAO,EAAE,CAAC;YACV,SAAS;SACV;QAED,IAAI,IAAI,KAAK,GAAG,EAAE;YAChB,IAAI,KAAK,GAAG,EAAE,CAAC;YACf,IAAI,aAAa,GAAG,KAAK,CAAC;YAE1B,IAAI,GAAG,KAAK,CAAC,EAAE,OAAO,CAAC,CAAC;YAExB,OAAO,IAAI,KAAK,GAAG,EAAE;gBACnB,IAAI,OAAO,KAAK,KAAK,CAAC,MAAM,EAAE;oBAC5B,aAAa,GAAG,IAAI,CAAC;oBACrB,MAAM;iBACP;gBAED,IAAI,IAAI,KAAK,IAAI,EAAE;oBACjB,OAAO,EAAE,CAAC;oBACV,IAAI,OAAO,KAAK,KAAK,CAAC,MAAM,EAAE;wBAC5B,aAAa,GAAG,IAAI,CAAC;wBACrB,MAAM;qBACP;oBACD,KAAK,IAAI,IAAI,GAAG,KAAK,CAAC,OAAO,CAAC,CAAC;oBAC/B,IAAI,GAAG,KAAK,CAAC,EAAE,OAAO,CAAC,CAAC;iBACzB;qBAAM;oBACL,KAAK,IAAI,IAAI,CAAC;oBACd,IAAI,GAAG,KAAK,CAAC,EAAE,OAAO,CAAC,CAAC;iBACzB;aACF;YAED,IAAI,GAAG,KAAK,CAAC,EAAE,OAAO,CAAC,CAAC;YAExB,IAAI,CAAC,aAAa,EAAE;gBAClB,MAAM,CAAC,IAAI,CAAC;oBACV,IAAI,EAAE,QAAQ;oBACd,KAAK;iBACN,CAAC,CAAC;aACJ;YACD,SAAS;SACV;QAED,IAAI,UAAU,GAAG,IAAI,CAAC;QACtB,IAAI,IAAI,IAAI,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE;YACjC,OAAO,EAAE,CAAC;YACV,SAAS;SACV;QAED,IAAI,OAAO,GAAG,OAAO,CAAC;QACtB,IAAI,CAAC,IAAI,IAAI,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,IAAI,IAAI,KAAK,GAAG,IAAI,IAAI,KAAK,GAAG,EAAE;YAChE,IAAI,KAAK,GAAG,EAAE,CAAC;YAEf,IAAI,IAAI,KAAK,GAAG,EAAE;gBAChB,KAAK,IAAI,IAAI,CAAC;gBACd,IAAI,GAAG,KAAK,CAAC,EAAE,OAAO,CAAC,CAAC;aACzB;YAED,OAAO,CAAC,IAAI,IAAI,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,IAAI,IAAI,KAAK,GAAG,EAAE;gBACnD,KAAK,IAAI,IAAI,CAAC;gBACd,IAAI,GAAG,KAAK,CAAC,EAAE,OAAO,CAAC,CAAC;aACzB;YAED,MAAM,CAAC,IAAI,CAAC;gBACV,IAAI,EAAE,QAAQ;gBACd,KAAK;aACN,CAAC,CAAC;YACH,SAAS;SACV;QAED,IAAI,OAAO,GAAG,QAAQ,CAAC;QACvB,IAAI,IAAI,IAAI,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE;YAC9B,IAAI,KAAK,GAAG,EAAE,CAAC;YAEf,OAAO,IAAI,IAAI,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE;gBACjC,IAAI,OAAO,KAAK,KAAK,CAAC,MAAM,EAAE;oBAC5B,MAAM;iBACP;gBACD,KAAK,IAAI,IAAI,CAAC;gBACd,IAAI,GAAG,KAAK,CAAC,EAAE,OAAO,CAAC,CAAC;aACzB;YAED,IAAI,KAAK,IAAI,MAAM,IAAI,KAAK,IAAI,OAAO,IAAI,KAAK,KAAK,MAAM,EAAE;gBAC3D,MAAM,CAAC,IAAI,CAAC;oBACV,IAAI,EAAE,MAAM;oBACZ,KAAK;iBACN,CAAC,CAAC;aACJ;iBAAM;gBACL,qDAAqD;gBACrD,OAAO,EAAE,CAAC;gBACV,SAAS;aACV;YACD,SAAS;SACV;QAED,OAAO,EAAE,CAAC;KACX;IAED,OAAO,MAAM,CAAC;AAChB,CAAC,EACD,KAAK,GAAG,CAAC,MAAe,EAAW,EAAE;IACnC,IAAI,MAAM,CAAC,MAAM,KAAK,CAAC,EAAE;QACvB,OAAO,MAAM,CAAC;KACf;IAED,IAAI,SAAS,GAAG,MAAM,CAAC,MAAM,CAAC,MAAM,GAAG,CAAC,CAAE,CAAC;IAE3C,QAAQ,SAAS,CAAC,IAAI,EAAE;QACtB,KAAK,WAAW;YACd,MAAM,GAAG,MAAM,CAAC,KAAK,CAAC,CAAC,EAAE,MAAM,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;YAC5C,OAAO,KAAK,CAAC,MAAM,CAAC,CAAC;YACrB,MAAM;QACR,KAAK,QAAQ;YACX,IAAI,wBAAwB,GAAG,SAAS,CAAC,KAAK,CAAC,SAAS,CAAC,KAAK,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;YAC3E,IAAI,wBAAwB,KAAK,GAAG,IAAI,wBAAwB,KAAK,GAAG,EAAE;gBACxE,MAAM,GAAG,MAAM,CAAC,KAAK,CAAC,CAAC,EAAE,MAAM,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;gBAC5C,OAAO,KAAK,CAAC,MAAM,CAAC,CAAC;aACtB;QACH,KAAK,QAAQ;YACX,IAAI,uBAAuB,GAAG,MAAM,CAAC,MAAM,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;YACxD,IAAI,uBAAuB,EAAE,IAAI,KAAK,WAAW,EAAE;gBACjD,MAAM,GAAG,MAAM,CAAC,KAAK,CAAC,CAAC,EAAE,MAAM,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;gBAC5C,OAAO,KAAK,CAAC,MAAM,CAAC,CAAC;aACtB;iBAAM,IAAI,uBAAuB,EAAE,IAAI,KAAK,OAAO,IAAI,uBAAuB,CAAC,KAAK,KAAK,GAAG,EAAE;gBAC7F,MAAM,GAAG,MAAM,CAAC,KAAK,CAAC,CAAC,EAAE,MAAM,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;gBAC5C,OAAO,KAAK,CAAC,MAAM,CAAC,CAAC;aACtB;YACD,MAAM;QACR,KAAK,WAAW;YACd,MAAM,GAAG,MAAM,CAAC,KAAK,CAAC,CAAC,EAAE,MAAM,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;YAC5C,OAAO,KAAK,CAAC,MAAM,CAAC,CAAC;YACrB,MAAM;KACT;IAED,OAAO,MAAM,CAAC;AAChB,CAAC,EACD,OAAO,GAAG,CAAC,MAAe,EAAW,EAAE;IACrC,IAAI,IAAI,GAAa,EAAE,CAAC;IAExB,MAAM,CAAC,GAAG,CAAC,CAAC,KAAK,EAAE,EAAE;QACnB,IAAI,KAAK,CAAC,IAAI,KAAK,OAAO,EAAE;YAC1B,IAAI,KAAK,CAAC,KAAK,KAAK,GAAG,EAAE;gBACvB,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;aAChB;iBAAM;gBACL,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC,CAAC;aACvC;SACF;QACD,IAAI,KAAK,CAAC,IAAI,KAAK,OAAO,EAAE;YAC1B,IAAI,KAAK,CAAC,KAAK,KAAK,GAAG,EAAE;gBACvB,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;aAChB;iBAAM;gBACL,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC,CAAC;aACvC;SACF;IACH,CAAC,CAAC,CAAC;IAEH,IAAI,IAAI,CAAC,MAAM,GAAG,CAAC,EAAE;QACnB,IAAI,CAAC,OAAO,EAAE,CAAC,GAAG,CAAC,CAAC,IAAI,EAAE,EAAE;YAC1B,IAAI,IAAI,KAAK,GAAG,EAAE;gBAChB,MAAM,CAAC,IAAI,CAAC;oBACV,IAAI,EAAE,OAAO;oBACb,KAAK,EAAE,GAAG;iBACX,CAAC,CAAC;aACJ;iBAAM,IAAI,IAAI,KAAK,GAAG,EAAE;gBACvB,MAAM,CAAC,IAAI,CAAC;oBACV,IAAI,EAAE,OAAO;oBACb,KAAK,EAAE,GAAG;iBACX,CAAC,CAAC;aACJ;QACH,CAAC,CAAC,CAAC;KACJ;IAED,OAAO,MAAM,CAAC;AAChB,CAAC,EACD,QAAQ,GAAG,CAAC,MAAe,EAAU,EAAE;IACrC,IAAI,MAAM,GAAG,EAAE,CAAC;IAEhB,MAAM,CAAC,GAAG,CAAC,CAAC,KAAK,EAAE,EAAE;QACnB,QAAQ,KAAK,CAAC,IAAI,EAAE;YAClB,KAAK,QAAQ;gBACX,MAAM,IAAI,GAAG,GAAG,KAAK,CAAC,KAAK,GAAG,GAAG,CAAC;gBAClC,MAAM;YACR;gBACE,MAAM,IAAI,KAAK,CAAC,KAAK,CAAC;gBACtB,MAAM;SACT;IACH,CAAC,CAAC,CAAC;IAEH,OAAO,MAAM,CAAC;AAChB,CAAC,EACD,YAAY,GAAG,CAAC,KAAa,EAAW,EAAE,CAAC,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,OAAO,CAAC,KAAK,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AAEnG,OAAO,EAAE,YAAY,EAAE,CAAC"}