{"version": 3, "file": "error.mjs", "sourceRoot": "", "sources": ["src/error.ts"], "names": [], "mappings": "AAAA,sFAAsF;OAE/E,EAAE,WAAW,EAAW;AAE/B,MAAM,OAAO,cAAe,SAAQ,KAAK;CAAG;AAE5C,MAAM,OAAO,QAAS,SAAQ,cAAc;IAO1C,YACE,MAA0B,EAC1B,KAAyB,EACzB,OAA2B,EAC3B,OAA4B;QAE5B,KAAK,CAAC,GAAG,QAAQ,CAAC,WAAW,CAAC,MAAM,EAAE,KAAK,EAAE,OAAO,CAAC,EAAE,CAAC,CAAC;QACzD,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC;QACrB,IAAI,CAAC,OAAO,GAAG,OAAO,CAAC;QACvB,IAAI,CAAC,UAAU,GAAG,OAAO,EAAE,CAAC,YAAY,CAAC,CAAC;QAC1C,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC;IACrB,CAAC;IAEO,MAAM,CAAC,WAAW,CAAC,MAA0B,EAAE,KAAU,EAAE,OAA2B;QAC5F,MAAM,GAAG,GACP,KAAK,EAAE,OAAO,CAAC,CAAC;YACd,OAAO,KAAK,CAAC,OAAO,KAAK,QAAQ,CAAC,CAAC;gBACjC,KAAK,CAAC,OAAO;gBACf,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,OAAO,CAAC;YACjC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC;gBAC/B,CAAC,CAAC,OAAO,CAAC;QAEZ,IAAI,MAAM,IAAI,GAAG,EAAE;YACjB,OAAO,GAAG,MAAM,IAAI,GAAG,EAAE,CAAC;SAC3B;QACD,IAAI,MAAM,EAAE;YACV,OAAO,GAAG,MAAM,wBAAwB,CAAC;SAC1C;QACD,IAAI,GAAG,EAAE;YACP,OAAO,GAAG,CAAC;SACZ;QACD,OAAO,0BAA0B,CAAC;IACpC,CAAC;IAED,MAAM,CAAC,QAAQ,CACb,MAA0B,EAC1B,aAAiC,EACjC,OAA2B,EAC3B,OAA4B;QAE5B,IAAI,CAAC,MAAM,EAAE;YACX,OAAO,IAAI,kBAAkB,CAAC,EAAE,OAAO,EAAE,KAAK,EAAE,WAAW,CAAC,aAAa,CAAC,EAAE,CAAC,CAAC;SAC/E;QAED,MAAM,KAAK,GAAG,aAAoC,CAAC;QAEnD,IAAI,MAAM,KAAK,GAAG,EAAE;YAClB,OAAO,IAAI,eAAe,CAAC,MAAM,EAAE,KAAK,EAAE,OAAO,EAAE,OAAO,CAAC,CAAC;SAC7D;QAED,IAAI,MAAM,KAAK,GAAG,EAAE;YAClB,OAAO,IAAI,mBAAmB,CAAC,MAAM,EAAE,KAAK,EAAE,OAAO,EAAE,OAAO,CAAC,CAAC;SACjE;QAED,IAAI,MAAM,KAAK,GAAG,EAAE;YAClB,OAAO,IAAI,qBAAqB,CAAC,MAAM,EAAE,KAAK,EAAE,OAAO,EAAE,OAAO,CAAC,CAAC;SACnE;QAED,IAAI,MAAM,KAAK,GAAG,EAAE;YAClB,OAAO,IAAI,aAAa,CAAC,MAAM,EAAE,KAAK,EAAE,OAAO,EAAE,OAAO,CAAC,CAAC;SAC3D;QAED,IAAI,MAAM,KAAK,GAAG,EAAE;YAClB,OAAO,IAAI,aAAa,CAAC,MAAM,EAAE,KAAK,EAAE,OAAO,EAAE,OAAO,CAAC,CAAC;SAC3D;QAED,IAAI,MAAM,KAAK,GAAG,EAAE;YAClB,OAAO,IAAI,wBAAwB,CAAC,MAAM,EAAE,KAAK,EAAE,OAAO,EAAE,OAAO,CAAC,CAAC;SACtE;QAED,IAAI,MAAM,KAAK,GAAG,EAAE;YAClB,OAAO,IAAI,cAAc,CAAC,MAAM,EAAE,KAAK,EAAE,OAAO,EAAE,OAAO,CAAC,CAAC;SAC5D;QAED,IAAI,MAAM,IAAI,GAAG,EAAE;YACjB,OAAO,IAAI,mBAAmB,CAAC,MAAM,EAAE,KAAK,EAAE,OAAO,EAAE,OAAO,CAAC,CAAC;SACjE;QAED,OAAO,IAAI,QAAQ,CAAC,MAAM,EAAE,KAAK,EAAE,OAAO,EAAE,OAAO,CAAC,CAAC;IACvD,CAAC;CACF;AAED,MAAM,OAAO,iBAAkB,SAAQ,QAAQ;IAG7C,YAAY,EAAE,OAAO,KAA2B,EAAE;QAChD,KAAK,CAAC,SAAS,EAAE,SAAS,EAAE,OAAO,IAAI,sBAAsB,EAAE,SAAS,CAAC,CAAC;QAH1D,WAAM,GAAc,SAAS,CAAC;IAIhD,CAAC;CACF;AAED,MAAM,OAAO,kBAAmB,SAAQ,QAAQ;IAG9C,YAAY,EAAE,OAAO,EAAE,KAAK,EAA+D;QACzF,KAAK,CAAC,SAAS,EAAE,SAAS,EAAE,OAAO,IAAI,mBAAmB,EAAE,SAAS,CAAC,CAAC;QAHvD,WAAM,GAAc,SAAS,CAAC;QAI9C,gEAAgE;QAChE,aAAa;QACb,IAAI,KAAK;YAAE,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC;IAChC,CAAC;CACF;AAED,MAAM,OAAO,yBAA0B,SAAQ,kBAAkB;IAC/D,YAAY,EAAE,OAAO,KAA2B,EAAE;QAChD,KAAK,CAAC,EAAE,OAAO,EAAE,OAAO,IAAI,oBAAoB,EAAE,CAAC,CAAC;IACtD,CAAC;CACF;AAED,MAAM,OAAO,eAAgB,SAAQ,QAAQ;IAA7C;;QACoB,WAAM,GAAQ,GAAG,CAAC;IACtC,CAAC;CAAA;AAED,MAAM,OAAO,mBAAoB,SAAQ,QAAQ;IAAjD;;QACoB,WAAM,GAAQ,GAAG,CAAC;IACtC,CAAC;CAAA;AAED,MAAM,OAAO,qBAAsB,SAAQ,QAAQ;IAAnD;;QACoB,WAAM,GAAQ,GAAG,CAAC;IACtC,CAAC;CAAA;AAED,MAAM,OAAO,aAAc,SAAQ,QAAQ;IAA3C;;QACoB,WAAM,GAAQ,GAAG,CAAC;IACtC,CAAC;CAAA;AAED,MAAM,OAAO,aAAc,SAAQ,QAAQ;IAA3C;;QACoB,WAAM,GAAQ,GAAG,CAAC;IACtC,CAAC;CAAA;AAED,MAAM,OAAO,wBAAyB,SAAQ,QAAQ;IAAtD;;QACoB,WAAM,GAAQ,GAAG,CAAC;IACtC,CAAC;CAAA;AAED,MAAM,OAAO,cAAe,SAAQ,QAAQ;IAA5C;;QACoB,WAAM,GAAQ,GAAG,CAAC;IACtC,CAAC;CAAA;AAED,MAAM,OAAO,mBAAoB,SAAQ,QAAQ;CAAG"}