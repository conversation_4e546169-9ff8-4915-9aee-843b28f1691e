{"version": 3, "file": "PromptCachingBetaMessageStream.d.ts", "sourceRoot": "", "sources": ["../src/lib/PromptCachingBetaMessageStream.ts"], "names": [], "mappings": ";AAAA,OAAO,KAAK,IAAI,MAAM,wBAAwB,CAAC;AAC/C,OAAO,EAAE,cAAc,EAAE,iBAAiB,EAAE,MAAM,yBAAyB,CAAC;AAC5E,OAAO,EAAE,KAAK,YAAY,EAAkB,MAAM,sCAAsC,CAAC;AACzF,OAAO,EACL,QAAQ,EACR,KAAK,wBAAwB,EAC7B,KAAK,sCAAsC,EAC3C,KAAK,6BAA6B,EAClC,KAAK,mBAAmB,EACxB,KAAK,uBAAuB,EAC7B,MAAM,0DAA0D,CAAC;AAClE,OAAO,EAAE,KAAK,cAAc,EAAE,MAAM,gCAAgC,CAAC;AAIrE,MAAM,WAAW,oCAAoC;IACnD,OAAO,EAAE,MAAM,IAAI,CAAC;IACpB,WAAW,EAAE,CAAC,KAAK,EAAE,sCAAsC,EAAE,QAAQ,EAAE,wBAAwB,KAAK,IAAI,CAAC;IACzG,IAAI,EAAE,CAAC,SAAS,EAAE,MAAM,EAAE,YAAY,EAAE,MAAM,KAAK,IAAI,CAAC;IACxD,SAAS,EAAE,CAAC,WAAW,EAAE,MAAM,EAAE,YAAY,EAAE,OAAO,KAAK,IAAI,CAAC;IAChE,OAAO,EAAE,CAAC,OAAO,EAAE,wBAAwB,KAAK,IAAI,CAAC;IACrD,YAAY,EAAE,CAAC,OAAO,EAAE,YAAY,KAAK,IAAI,CAAC;IAC9C,6BAA6B,EAAE,CAAC,OAAO,EAAE,wBAAwB,KAAK,IAAI,CAAC;IAC3E,KAAK,EAAE,CAAC,KAAK,EAAE,cAAc,KAAK,IAAI,CAAC;IACvC,KAAK,EAAE,CAAC,KAAK,EAAE,iBAAiB,KAAK,IAAI,CAAC;IAC1C,GAAG,EAAE,MAAM,IAAI,CAAC;CACjB;AAUD,qBAAa,8BAA+B,YAAW,aAAa,CAAC,sCAAsC,CAAC;;IAC1G,QAAQ,EAAE,6BAA6B,EAAE,CAAM;IAC/C,gBAAgB,EAAE,wBAAwB,EAAE,CAAM;IAGlD,UAAU,EAAE,eAAe,CAAyB;;IAsCpD;;;;;;OAMG;IACH,MAAM,CAAC,kBAAkB,CAAC,MAAM,EAAE,cAAc,GAAG,8BAA8B;IAMjF,MAAM,CAAC,aAAa,CAClB,QAAQ,EAAE,QAAQ,EAClB,MAAM,EAAE,uBAAuB,EAC/B,OAAO,CAAC,EAAE,IAAI,CAAC,cAAc,GAC5B,8BAA8B;IAejC,SAAS,CAAC,IAAI,CAAC,QAAQ,EAAE,MAAM,OAAO,CAAC,GAAG,CAAC;IAO3C,SAAS,CAAC,iCAAiC,CAAC,OAAO,EAAE,6BAA6B;IAIlF,SAAS,CAAC,4BAA4B,CAAC,OAAO,EAAE,wBAAwB,EAAE,IAAI,UAAO;cAOrE,+BAA+B,CAC7C,QAAQ,EAAE,QAAQ,EAClB,MAAM,EAAE,mBAAmB,EAC3B,OAAO,CAAC,EAAE,IAAI,CAAC,cAAc,GAC5B,OAAO,CAAC,IAAI,CAAC;IAqBhB,SAAS,CAAC,UAAU;IAMpB,IAAI,KAAK,IAAI,OAAO,CAEnB;IAED,IAAI,OAAO,IAAI,OAAO,CAErB;IAED,IAAI,OAAO,IAAI,OAAO,CAErB;IAED,KAAK;IAIL;;;;;;OAMG;IACH,EAAE,CAAC,KAAK,SAAS,MAAM,oCAAoC,EACzD,KAAK,EAAE,KAAK,EACZ,QAAQ,EAAE,oCAAoC,CAAC,KAAK,CAAC,GACpD,IAAI;IAOP;;;;;;OAMG;IACH,GAAG,CAAC,KAAK,SAAS,MAAM,oCAAoC,EAC1D,KAAK,EAAE,KAAK,EACZ,QAAQ,EAAE,oCAAoC,CAAC,KAAK,CAAC,GACpD,IAAI;IAQP;;;;OAIG;IACH,IAAI,CAAC,KAAK,SAAS,MAAM,oCAAoC,EAC3D,KAAK,EAAE,KAAK,EACZ,QAAQ,EAAE,oCAAoC,CAAC,KAAK,CAAC,GACpD,IAAI;IAOP;;;;;;;;;;OAUG;IACH,OAAO,CAAC,KAAK,SAAS,MAAM,oCAAoC,EAC9D,KAAK,EAAE,KAAK,GACX,OAAO,CACR,UAAU,CAAC,oCAAoC,CAAC,KAAK,CAAC,CAAC,SAAS,CAAC,MAAM,KAAK,CAAC,GAAG,KAAK,GACnF,UAAU,CAAC,oCAAoC,CAAC,KAAK,CAAC,CAAC,SAAS,EAAE,GAAG,IAAI,GACzE,UAAU,CAAC,oCAAoC,CAAC,KAAK,CAAC,CAAC,CAC1D;IAQK,IAAI,IAAI,OAAO,CAAC,IAAI,CAAC;IAK3B,IAAI,cAAc,IAAI,wBAAwB,GAAG,SAAS,CAEzD;IAWD;;;OAGG;IACG,YAAY,IAAI,OAAO,CAAC,wBAAwB,CAAC;IAqBvD;;;;OAIG;IACG,SAAS,IAAI,OAAO,CAAC,MAAM,CAAC;IA0BlC,SAAS,CAAC,KAAK,CAAC,KAAK,SAAS,MAAM,oCAAoC,EACtE,KAAK,EAAE,KAAK,EACZ,GAAG,IAAI,EAAE,UAAU,CAAC,oCAAoC,CAAC,KAAK,CAAC,CAAC;IA8ClE,SAAS,CAAC,UAAU;cA0DJ,mBAAmB,CACjC,cAAc,EAAE,cAAc,EAC9B,OAAO,CAAC,EAAE,IAAI,CAAC,cAAc,GAC5B,OAAO,CAAC,IAAI,CAAC;IA+EhB,CAAC,MAAM,CAAC,aAAa,CAAC,IAAI,aAAa,CAAC,sCAAsC,CAAC;IA6D/E,gBAAgB,IAAI,cAAc;CAInC"}