// File generated from our OpenAPI spec by Stainless. See CONTRIBUTING.md for details.
import { APIResource } from "../../../resource.mjs";
import * as MessagesAPI from "./messages.mjs";
export class PromptCaching extends APIResource {
    constructor() {
        super(...arguments);
        this.messages = new MessagesAPI.Messages(this._client);
    }
}
(function (PromptCaching) {
    PromptCaching.Messages = MessagesAPI.Messages;
})(PromptCaching || (PromptCaching = {}));
//# sourceMappingURL=prompt-caching.mjs.map