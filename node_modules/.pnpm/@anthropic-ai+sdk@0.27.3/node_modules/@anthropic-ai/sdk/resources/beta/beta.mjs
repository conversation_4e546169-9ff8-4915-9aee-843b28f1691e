// File generated from our OpenAPI spec by Stainless. See CONTRIBUTING.md for details.
import { APIResource } from "../../resource.mjs";
import * as PromptCachingAPI from "./prompt-caching/prompt-caching.mjs";
export class Beta extends APIResource {
    constructor() {
        super(...arguments);
        this.promptCaching = new PromptCachingAPI.PromptCaching(this._client);
    }
}
(function (Beta) {
    Beta.PromptCaching = PromptCachingAPI.PromptCaching;
})(Beta || (Beta = {}));
//# sourceMappingURL=beta.mjs.map