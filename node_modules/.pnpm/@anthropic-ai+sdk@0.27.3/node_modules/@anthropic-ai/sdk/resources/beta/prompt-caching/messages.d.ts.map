{"version": 3, "file": "messages.d.ts", "sourceRoot": "", "sources": ["../../../src/resources/beta/prompt-caching/messages.ts"], "names": [], "mappings": "AAEA,OAAO,EAAE,WAAW,EAAE,MAAM,mBAAmB,CAAC;AAChD,OAAO,EAAE,UAAU,EAAE,MAAM,eAAe,CAAC;AAC3C,OAAO,KAAK,IAAI,MAAM,eAAe,CAAC;AACtC,OAAO,KAAK,wBAAwB,MAAM,YAAY,CAAC;AACvD,OAAO,KAAK,WAAW,MAAM,gBAAgB,CAAC;AAC9C,OAAO,EAAE,MAAM,EAAE,MAAM,oBAAoB,CAAC;AAC5C,OAAO,EAAE,8BAA8B,EAAE,MAAM,6CAA6C,CAAC;AAE7F,qBAAa,QAAS,SAAQ,WAAW;IACvC;;;;;;;;OAQG;IACH,MAAM,CACJ,IAAI,EAAE,+BAA+B,EACrC,OAAO,CAAC,EAAE,IAAI,CAAC,cAAc,GAC5B,UAAU,CAAC,wBAAwB,CAAC;IACvC,MAAM,CACJ,IAAI,EAAE,4BAA4B,EAClC,OAAO,CAAC,EAAE,IAAI,CAAC,cAAc,GAC5B,UAAU,CAAC,MAAM,CAAC,sCAAsC,CAAC,CAAC;IAC7D,MAAM,CACJ,IAAI,EAAE,uBAAuB,EAC7B,OAAO,CAAC,EAAE,IAAI,CAAC,cAAc,GAC5B,UAAU,CAAC,MAAM,CAAC,sCAAsC,CAAC,GAAG,wBAAwB,CAAC;IAcxF;;OAEG;IACH,MAAM,CAAC,IAAI,EAAE,mBAAmB,EAAE,OAAO,CAAC,EAAE,IAAI,CAAC,cAAc,GAAG,8BAA8B;CAGjG;AAED,MAAM,MAAM,mBAAmB,GAAG,uBAAuB,CAAC;AAE1D,MAAM,WAAW,sCAAsC;IACrD,IAAI,EAAE,WAAW,CAAC;CACnB;AAED,MAAM,WAAW,gCAAgC;IAC/C,MAAM,EAAE,gCAAgC,CAAC,MAAM,CAAC;IAEhD,IAAI,EAAE,OAAO,CAAC;IAEd,aAAa,CAAC,EAAE,sCAAsC,GAAG,IAAI,CAAC;CAC/D;AAED,yBAAiB,gCAAgC,CAAC;IAChD,UAAiB,MAAM;QACrB,IAAI,EAAE,MAAM,CAAC;QAEb,UAAU,EAAE,YAAY,GAAG,WAAW,GAAG,WAAW,GAAG,YAAY,CAAC;QAEpE,IAAI,EAAE,QAAQ,CAAC;KAChB;CACF;AAED,MAAM,WAAW,wBAAwB;IACvC;;;;OAIG;IACH,EAAE,EAAE,MAAM,CAAC;IAEX;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;OAiCG;IACH,OAAO,EAAE,KAAK,CAAC,WAAW,CAAC,YAAY,CAAC,CAAC;IAEzC;;;;OAIG;IACH,KAAK,EAAE,WAAW,CAAC,KAAK,CAAC;IAEzB;;;;OAIG;IACH,IAAI,EAAE,WAAW,CAAC;IAElB;;;;;;;;;;;;OAYG;IACH,WAAW,EAAE,UAAU,GAAG,YAAY,GAAG,eAAe,GAAG,UAAU,GAAG,IAAI,CAAC;IAE7E;;;;;OAKG;IACH,aAAa,EAAE,MAAM,GAAG,IAAI,CAAC;IAE7B;;;;OAIG;IACH,IAAI,EAAE,SAAS,CAAC;IAEhB;;;;;;;;;;;;;OAaG;IACH,KAAK,EAAE,sBAAsB,CAAC;CAC/B;AAED,MAAM,WAAW,6BAA6B;IAC5C,OAAO,EACH,MAAM,GACN,KAAK,CACD,+BAA+B,GAC/B,gCAAgC,GAChC,kCAAkC,GAClC,qCAAqC,CACxC,CAAC;IAEN,IAAI,EAAE,MAAM,GAAG,WAAW,CAAC;CAC5B;AAED,MAAM,WAAW,+BAA+B;IAC9C,IAAI,EAAE,MAAM,CAAC;IAEb,IAAI,EAAE,MAAM,CAAC;IAEb,aAAa,CAAC,EAAE,sCAAsC,GAAG,IAAI,CAAC;CAC/D;AAED,MAAM,WAAW,qBAAqB;IACpC;;;;;OAKG;IACH,YAAY,EAAE,qBAAqB,CAAC,WAAW,CAAC;IAEhD,IAAI,EAAE,MAAM,CAAC;IAEb,aAAa,CAAC,EAAE,sCAAsC,GAAG,IAAI,CAAC;IAE9D;;;;;;;OAOG;IACH,WAAW,CAAC,EAAE,MAAM,CAAC;CACtB;AAED,yBAAiB,qBAAqB,CAAC;IACrC;;;;;OAKG;IACH,UAAiB,WAAW;QAC1B,IAAI,EAAE,QAAQ,CAAC;QAEf,UAAU,CAAC,EAAE,OAAO,GAAG,IAAI,CAAC;QAC5B,CAAC,CAAC,EAAE,MAAM,GAAG,OAAO,CAAC;KACtB;CACF;AAED,MAAM,WAAW,qCAAqC;IACpD,WAAW,EAAE,MAAM,CAAC;IAEpB,IAAI,EAAE,aAAa,CAAC;IAEpB,aAAa,CAAC,EAAE,sCAAsC,GAAG,IAAI,CAAC;IAE9D,OAAO,CAAC,EAAE,MAAM,GAAG,KAAK,CAAC,+BAA+B,GAAG,gCAAgC,CAAC,CAAC;IAE7F,QAAQ,CAAC,EAAE,OAAO,CAAC;CACpB;AAED,MAAM,WAAW,kCAAkC;IACjD,EAAE,EAAE,MAAM,CAAC;IAEX,KAAK,EAAE,OAAO,CAAC;IAEf,IAAI,EAAE,MAAM,CAAC;IAEb,IAAI,EAAE,UAAU,CAAC;IAEjB,aAAa,CAAC,EAAE,sCAAsC,GAAG,IAAI,CAAC;CAC/D;AAED,MAAM,WAAW,sBAAsB;IACrC;;OAEG;IACH,2BAA2B,EAAE,MAAM,GAAG,IAAI,CAAC;IAE3C;;OAEG;IACH,uBAAuB,EAAE,MAAM,GAAG,IAAI,CAAC;IAEvC;;OAEG;IACH,YAAY,EAAE,MAAM,CAAC;IAErB;;OAEG;IACH,aAAa,EAAE,MAAM,CAAC;CACvB;AAED,MAAM,WAAW,qCAAqC;IACpD,OAAO,EAAE,wBAAwB,CAAC;IAElC,IAAI,EAAE,eAAe,CAAC;CACvB;AAED,MAAM,MAAM,sCAAsC,GAC9C,qCAAqC,GACrC,WAAW,CAAC,oBAAoB,GAChC,WAAW,CAAC,mBAAmB,GAC/B,WAAW,CAAC,yBAAyB,GACrC,WAAW,CAAC,yBAAyB,GACrC,WAAW,CAAC,wBAAwB,CAAC;AAEzC,MAAM,MAAM,mBAAmB,GAAG,+BAA+B,GAAG,4BAA4B,CAAC;AAEjG,MAAM,WAAW,uBAAuB;IACtC;;;;;;;;OAQG;IACH,UAAU,EAAE,MAAM,CAAC;IAEnB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;OAsFG;IACH,QAAQ,EAAE,KAAK,CAAC,6BAA6B,CAAC,CAAC;IAE/C;;;;OAIG;IACH,KAAK,EAAE,WAAW,CAAC,KAAK,CAAC;IAEzB;;OAEG;IACH,QAAQ,CAAC,EAAE,mBAAmB,CAAC,QAAQ,CAAC;IAExC;;;;;;;;;;OAUG;IACH,cAAc,CAAC,EAAE,KAAK,CAAC,MAAM,CAAC,CAAC;IAE/B;;;;;OAKG;IACH,MAAM,CAAC,EAAE,OAAO,CAAC;IAEjB;;;;;;OAMG;IACH,MAAM,CAAC,EAAE,MAAM,GAAG,KAAK,CAAC,+BAA+B,CAAC,CAAC;IAEzD;;;;;;;;;OASG;IACH,WAAW,CAAC,EAAE,MAAM,CAAC;IAErB;;;OAGG;IACH,WAAW,CAAC,EACR,mBAAmB,CAAC,cAAc,GAClC,mBAAmB,CAAC,aAAa,GACjC,mBAAmB,CAAC,cAAc,CAAC;IAEvC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;OAqEG;IACH,KAAK,CAAC,EAAE,KAAK,CAAC,qBAAqB,CAAC,CAAC;IAErC;;;;;;;;OAQG;IACH,KAAK,CAAC,EAAE,MAAM,CAAC;IAEf;;;;;;;;;;OAUG;IACH,KAAK,CAAC,EAAE,MAAM,CAAC;CAChB;AAED,yBAAiB,mBAAmB,CAAC;IACnC;;OAEG;IACH,UAAiB,QAAQ;QACvB;;;;;;WAMG;QACH,OAAO,CAAC,EAAE,MAAM,GAAG,IAAI,CAAC;KACzB;IAED;;OAEG;IACH,UAAiB,cAAc;QAC7B,IAAI,EAAE,MAAM,CAAC;KACd;IAED;;OAEG;IACH,UAAiB,aAAa;QAC5B,IAAI,EAAE,KAAK,CAAC;KACb;IAED;;OAEG;IACH,UAAiB,cAAc;QAC7B;;WAEG;QACH,IAAI,EAAE,MAAM,CAAC;QAEb,IAAI,EAAE,MAAM,CAAC;KACd;IAED,KAAY,+BAA+B,GAAG,wBAAwB,CAAC,+BAA+B,CAAC;IACvG,KAAY,4BAA4B,GAAG,wBAAwB,CAAC,4BAA4B,CAAC;CAClG;AAED,MAAM,WAAW,+BAAgC,SAAQ,uBAAuB;IAC9E;;;;;OAKG;IACH,MAAM,CAAC,EAAE,KAAK,CAAC;CAChB;AAED,MAAM,WAAW,4BAA6B,SAAQ,uBAAuB;IAC3E;;;;;OAKG;IACH,MAAM,EAAE,IAAI,CAAC;CACd;AAED,yBAAiB,QAAQ,CAAC;IACxB,MAAM,QAAQ,sCAAsC,GAAG,wBAAwB,CAAC,sCAAsC,CAAC;IACvH,MAAM,QAAQ,gCAAgC,GAAG,wBAAwB,CAAC,gCAAgC,CAAC;IAC3G,MAAM,QAAQ,wBAAwB,GAAG,wBAAwB,CAAC,wBAAwB,CAAC;IAC3F,MAAM,QAAQ,6BAA6B,GAAG,wBAAwB,CAAC,6BAA6B,CAAC;IACrG,MAAM,QAAQ,+BAA+B,GAAG,wBAAwB,CAAC,+BAA+B,CAAC;IACzG,MAAM,QAAQ,qBAAqB,GAAG,wBAAwB,CAAC,qBAAqB,CAAC;IACrF,MAAM,QAAQ,qCAAqC,GAAG,wBAAwB,CAAC,qCAAqC,CAAC;IACrH,MAAM,QAAQ,kCAAkC,GAAG,wBAAwB,CAAC,kCAAkC,CAAC;IAC/G,MAAM,QAAQ,sBAAsB,GAAG,wBAAwB,CAAC,sBAAsB,CAAC;IACvF,MAAM,QAAQ,qCAAqC,GAAG,wBAAwB,CAAC,qCAAqC,CAAC;IACrH,MAAM,QAAQ,sCAAsC,GAAG,wBAAwB,CAAC,sCAAsC,CAAC;IACvH,MAAM,QAAQ,mBAAmB,GAAG,wBAAwB,CAAC,mBAAmB,CAAC;IACjF,MAAM,QAAQ,+BAA+B,GAAG,wBAAwB,CAAC,+BAA+B,CAAC;IACzG,MAAM,QAAQ,4BAA4B,GAAG,wBAAwB,CAAC,4BAA4B,CAAC;CACpG"}