import { APIResource } from "../../../resource.js";
import * as MessagesAPI from "./messages.js";
export declare class PromptCaching extends APIResource {
    messages: MessagesAPI.Messages;
}
export declare namespace PromptCaching {
    export import Messages = MessagesAPI.Messages;
    export import PromptCachingBetaCacheControlEphemeral = MessagesAPI.PromptCachingBetaCacheControlEphemeral;
    export import PromptCachingBetaImageBlockParam = MessagesAPI.PromptCachingBetaImageBlockParam;
    export import PromptCachingBetaMessage = MessagesAPI.PromptCachingBetaMessage;
    export import PromptCachingBetaMessageParam = MessagesAPI.PromptCachingBetaMessageParam;
    export import PromptCachingBetaTextBlockParam = MessagesAPI.PromptCachingBetaTextBlockParam;
    export import PromptCachingBetaTool = MessagesAPI.PromptCachingBetaTool;
    export import PromptCachingBetaToolResultBlockParam = MessagesAPI.PromptCachingBetaToolResultBlockParam;
    export import PromptCachingBetaToolUseBlockParam = MessagesAPI.PromptCachingBetaToolUseBlockParam;
    export import PromptCachingBetaUsage = MessagesAPI.PromptCachingBetaUsage;
    export import RawPromptCachingBetaMessageStartEvent = MessagesAPI.RawPromptCachingBetaMessageStartEvent;
    export import RawPromptCachingBetaMessageStreamEvent = MessagesAPI.RawPromptCachingBetaMessageStreamEvent;
    export import MessageCreateParams = MessagesAPI.MessageCreateParams;
    export import MessageCreateParamsNonStreaming = MessagesAPI.MessageCreateParamsNonStreaming;
    export import MessageCreateParamsStreaming = MessagesAPI.MessageCreateParamsStreaming;
}
//# sourceMappingURL=prompt-caching.d.ts.map