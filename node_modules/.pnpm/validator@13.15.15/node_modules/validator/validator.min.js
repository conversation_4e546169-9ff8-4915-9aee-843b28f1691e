/*!
 * Copyright (c) 2018 <PERSON> <<EMAIL>>
 * 
 * Permission is hereby granted, free of charge, to any person obtaining
 * a copy of this software and associated documentation files (the
 * "Software"), to deal in the Software without restriction, including
 * without limitation the rights to use, copy, modify, merge, publish,
 * distribute, sublicense, and/or sell copies of the Software, and to
 * permit persons to whom the Software is furnished to do so, subject to
 * the following conditions:
 * 
 * The above copyright notice and this permission notice shall be
 * included in all copies or substantial portions of the Software.
 * 
 * THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND,
 * EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF
 * MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND
 * NONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE
 * LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION
 * OF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION
 * WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.
 */
((t,e)=>{"object"==typeof exports&&"undefined"!=typeof module?module.exports=e():"function"==typeof define&&define.amd?define(e):t.validator=e()})(this,function(){function u(t){if(null==t)throw new TypeError("Expected a string but received a ".concat(t));if("String"!==t.constructor.name)throw new TypeError("Expected a string but received a ".concat(t.constructor.name))}function r(t){return u(t),t=Date.parse(t),isNaN(t)?null:new Date(t)}function o(t){return null==t}for(var t,n={"en-US":/^[A-Z]+$/i,"az-AZ":/^[A-VXYZÇƏĞİıÖŞÜ]+$/i,"bg-BG":/^[А-Я]+$/i,"cs-CZ":/^[A-ZÁČĎÉĚÍŇÓŘŠŤÚŮÝŽ]+$/i,"da-DK":/^[A-ZÆØÅ]+$/i,"de-DE":/^[A-ZÄÖÜß]+$/i,"el-GR":/^[Α-ώ]+$/i,"es-ES":/^[A-ZÁÉÍÑÓÚÜ]+$/i,"fa-IR":/^[ابپتثجچحخدذرزژسشصضطظعغفقکگلمنوهی]+$/i,"fi-FI":/^[A-ZÅÄÖ]+$/i,"fr-FR":/^[A-ZÀÂÆÇÉÈÊËÏÎÔŒÙÛÜŸ]+$/i,"it-IT":/^[A-ZÀÉÈÌÎÓÒÙ]+$/i,"ja-JP":/^[ぁ-んァ-ヶｦ-ﾟ一-龠ー・。、]+$/i,"nb-NO":/^[A-ZÆØÅ]+$/i,"nl-NL":/^[A-ZÁÉËÏÓÖÜÚ]+$/i,"nn-NO":/^[A-ZÆØÅ]+$/i,"hu-HU":/^[A-ZÁÉÍÓÖŐÚÜŰ]+$/i,"pl-PL":/^[A-ZĄĆĘŚŁŃÓŻŹ]+$/i,"pt-PT":/^[A-ZÃÁÀÂÄÇÉÊËÍÏÕÓÔÖÚÜ]+$/i,"ru-RU":/^[А-ЯЁ]+$/i,"kk-KZ":/^[А-ЯЁ\u04D8\u04B0\u0406\u04A2\u0492\u04AE\u049A\u04E8\u04BA]+$/i,"sl-SI":/^[A-ZČĆĐŠŽ]+$/i,"sk-SK":/^[A-ZÁČĎÉÍŇÓŠŤÚÝŽĹŔĽÄÔ]+$/i,"sr-RS@latin":/^[A-ZČĆŽŠĐ]+$/i,"sr-RS":/^[А-ЯЂЈЉЊЋЏ]+$/i,"sv-SE":/^[A-ZÅÄÖ]+$/i,"th-TH":/^[ก-๐\s]+$/i,"tr-TR":/^[A-ZÇĞİıÖŞÜ]+$/i,"uk-UA":/^[А-ЩЬЮЯЄIЇҐі]+$/i,"vi-VN":/^[A-ZÀÁẠẢÃÂẦẤẬẨẪĂẰẮẶẲẴĐÈÉẸẺẼÊỀẾỆỂỄÌÍỊỈĨÒÓỌỎÕÔỒỐỘỔỖƠỜỚỢỞỠÙÚỤỦŨƯỪỨỰỬỮỲÝỴỶỸ]+$/i,"ko-KR":/^[ㄱ-ㅎㅏ-ㅣ가-힣]*$/,"ku-IQ":/^[ئابپتجچحخدرڕزژسشعغفڤقکگلڵمنوۆھەیێيطؤثآإأكضصةظذ]+$/i,ar:/^[ءآأؤإئابةتثجحخدذرزسشصضطظعغفقكلمنهوىيًٌٍَُِّْٰ]+$/,he:/^[א-ת]+$/,fa:/^['آاءأؤئبپتثجچحخدذرزژسشصضطظعغفقکگلمنوهةی']+$/i,bn:/^['ঀঁংঃঅআইঈউঊঋঌএঐওঔকখগঘঙচছজঝঞটঠডঢণতথদধনপফবভমযরলশষসহ়ঽািীুূৃৄেৈোৌ্ৎৗড়ঢ়য়ৠৡৢৣৰৱ৲৳৴৵৶৷৸৹৺৻']+$/,eo:/^[ABCĈD-GĜHĤIJĴK-PRSŜTUŬVZ]+$/i,"hi-IN":/^[\u0900-\u0961]+[\u0972-\u097F]*$/i,"si-LK":/^[\u0D80-\u0DFF]+$/},a={"en-US":/^[0-9A-Z]+$/i,"az-AZ":/^[0-9A-VXYZÇƏĞİıÖŞÜ]+$/i,"bg-BG":/^[0-9А-Я]+$/i,"cs-CZ":/^[0-9A-ZÁČĎÉĚÍŇÓŘŠŤÚŮÝŽ]+$/i,"da-DK":/^[0-9A-ZÆØÅ]+$/i,"de-DE":/^[0-9A-ZÄÖÜß]+$/i,"el-GR":/^[0-9Α-ω]+$/i,"es-ES":/^[0-9A-ZÁÉÍÑÓÚÜ]+$/i,"fi-FI":/^[0-9A-ZÅÄÖ]+$/i,"fr-FR":/^[0-9A-ZÀÂÆÇÉÈÊËÏÎÔŒÙÛÜŸ]+$/i,"it-IT":/^[0-9A-ZÀÉÈÌÎÓÒÙ]+$/i,"ja-JP":/^[0-9０-９ぁ-んァ-ヶｦ-ﾟ一-龠ー・。、]+$/i,"hu-HU":/^[0-9A-ZÁÉÍÓÖŐÚÜŰ]+$/i,"nb-NO":/^[0-9A-ZÆØÅ]+$/i,"nl-NL":/^[0-9A-ZÁÉËÏÓÖÜÚ]+$/i,"nn-NO":/^[0-9A-ZÆØÅ]+$/i,"pl-PL":/^[0-9A-ZĄĆĘŚŁŃÓŻŹ]+$/i,"pt-PT":/^[0-9A-ZÃÁÀÂÄÇÉÊËÍÏÕÓÔÖÚÜ]+$/i,"ru-RU":/^[0-9А-ЯЁ]+$/i,"kk-KZ":/^[0-9А-ЯЁ\u04D8\u04B0\u0406\u04A2\u0492\u04AE\u049A\u04E8\u04BA]+$/i,"sl-SI":/^[0-9A-ZČĆĐŠŽ]+$/i,"sk-SK":/^[0-9A-ZÁČĎÉÍŇÓŠŤÚÝŽĹŔĽÄÔ]+$/i,"sr-RS@latin":/^[0-9A-ZČĆŽŠĐ]+$/i,"sr-RS":/^[0-9А-ЯЂЈЉЊЋЏ]+$/i,"sv-SE":/^[0-9A-ZÅÄÖ]+$/i,"th-TH":/^[ก-๙\s]+$/i,"tr-TR":/^[0-9A-ZÇĞİıÖŞÜ]+$/i,"uk-UA":/^[0-9А-ЩЬЮЯЄIЇҐі]+$/i,"ko-KR":/^[0-9ㄱ-ㅎㅏ-ㅣ가-힣]*$/,"ku-IQ":/^[٠١٢٣٤٥٦٧٨٩0-9ئابپتجچحخدرڕزژسشعغفڤقکگلڵمنوۆھەیێيطؤثآإأكضصةظذ]+$/i,"vi-VN":/^[0-9A-ZÀÁẠẢÃÂẦẤẬẨẪĂẰẮẶẲẴĐÈÉẸẺẼÊỀẾỆỂỄÌÍỊỈĨÒÓỌỎÕÔỒỐỘỔỖƠỜỚỢỞỠÙÚỤỦŨƯỪỨỰỬỮỲÝỴỶỸ]+$/i,ar:/^[٠١٢٣٤٥٦٧٨٩0-9ءآأؤإئابةتثجحخدذرزسشصضطظعغفقكلمنهوىيًٌٍَُِّْٰ]+$/,he:/^[0-9א-ת]+$/,fa:/^['0-9آاءأؤئبپتثجچحخدذرزژسشصضطظعغفقکگلمنوهةی۱۲۳۴۵۶۷۸۹۰']+$/i,bn:/^['ঀঁংঃঅআইঈউঊঋঌএঐওঔকখগঘঙচছজঝঞটঠডঢণতথদধনপফবভমযরলশষসহ়ঽািীুূৃৄেৈোৌ্ৎৗড়ঢ়য়ৠৡৢৣ০১২৩৪৫৬৭৮৯ৰৱ৲৳৴৵৶৷৸৹৺৻']+$/,eo:/^[0-9ABCĈD-GĜHĤIJĴK-PRSŜTUŬVZ]+$/i,"hi-IN":/^[\u0900-\u0963]+[\u0966-\u097F]*$/i,"si-LK":/^[0-9\u0D80-\u0DFF]+$/},i={"en-US":".",ar:"٫"},O=["AU","GB","HK","IN","NZ","ZA","ZM"],e=0;e<O.length;e++)t="en-".concat(O[e]),n[t]=n["en-US"],a[t]=a["en-US"],i[t]=i["en-US"];for(var s,H=["AE","BH","DZ","EG","IQ","JO","KW","LB","LY","MA","QM","QA","SA","SD","SY","TN","YE"],_=0;_<H.length;_++)s="ar-".concat(H[_]),n[s]=n.ar,a[s]=a.ar,i[s]=i.ar;for(var U,w=["IR","AF"],K=0;K<w.length;K++)U="fa-".concat(w[K]),a[U]=a.fa,i[U]=i.ar;for(var c,y=["BD","IN"],W=0;W<y.length;W++)c="bn-".concat(y[W]),n[c]=n.bn,a[c]=a.bn,i[c]=i["en-US"];for(var x=["ar-EG","ar-LB","ar-LY"],k=["bg-BG","cs-CZ","da-DK","de-DE","el-GR","en-ZM","eo","es-ES","fr-CA","fr-FR","id-ID","it-IT","ku-IQ","hi-IN","hu-HU","nb-NO","nn-NO","nl-NL","pl-PL","pt-PT","ru-RU","kk-KZ","si-LK","sl-SI","sr-RS@latin","sr-RS","sv-SE","tr-TR","uk-UA","vi-VN"],Y=0;Y<x.length;Y++)i[x[Y]]=i["en-US"];for(var V=0;V<k.length;V++)i[k[V]]=",";function z(t,e){u(t),e=e||{};var r,n=new RegExp("^(?:[-+])?(?:[0-9]+)?(?:\\".concat(e.locale?i[e.locale]:".","[0-9]*)?(?:[eE][\\+\\-]?(?:[0-9]+))?$"));return""!==t&&"."!==t&&","!==t&&"-"!==t&&"+"!==t&&(r=parseFloat(t.replace(",",".")),n.test(t))&&(!e.hasOwnProperty("min")||o(e.min)||r>=e.min)&&(!e.hasOwnProperty("max")||o(e.max)||r<=e.max)&&(!e.hasOwnProperty("lt")||o(e.lt)||r<e.lt)&&(!e.hasOwnProperty("gt")||o(e.gt)||r>e.gt)}n["fr-CA"]=n["fr-FR"],a["fr-CA"]=a["fr-FR"],n["pt-BR"]=n["pt-PT"],a["pt-BR"]=a["pt-PT"],i["pt-BR"]=i["pt-PT"],n["pl-Pl"]=n["pl-PL"],a["pl-Pl"]=a["pl-PL"],i["pl-Pl"]=i["pl-PL"],n["fa-AF"]=n.fa;var Q=Object.keys(i);function j(t){return z(t)?parseFloat(t):NaN}function J(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=Array(e);r<e;r++)n[r]=t[r];return n}function X(t,e){var r,n,a,i,o="undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(o)return a=!(n=!0),{s:function(){o=o.call(t)},n:function(){var t=o.next();return n=t.done,t},e:function(t){a=!0,r=t},f:function(){try{n||null==o.return||o.return()}finally{if(a)throw r}}};if(Array.isArray(t)||(o=tt(t))||e&&t&&"number"==typeof t.length)return o&&(t=o),i=0,{s:e=function(){},n:function(){return i>=t.length?{done:!0}:{done:!1,value:t[i++]}},e:function(t){throw t},f:e};throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function d(t,e){return(t=>{if(Array.isArray(t))return t})(t)||((t,e)=>{var r=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=r){var n,a,i,o,s=[],c=!0,u=!1;try{if(i=(r=r.call(t)).next,0===e){if(Object(r)!==r)return;c=!1}else for(;!(c=(n=i.call(r)).done)&&(s.push(n.value),s.length!==e);c=!0);}catch(t){u=!0,a=t}finally{try{if(!c&&null!=r.return&&(o=r.return(),Object(o)!==o))return}finally{if(u)throw a}}return s}})(t,e)||tt(t,e)||(()=>{throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")})()}function q(t){return(t=>{if(Array.isArray(t))return J(t)})(t)||(t=>{if("undefined"!=typeof Symbol&&null!=t[Symbol.iterator]||null!=t["@@iterator"])return Array.from(t)})(t)||tt(t)||(()=>{throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")})()}function l(t){return(l="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function tt(t,e){var r;if(t)return"string"==typeof t?J(t,e):"Map"===(r="Object"===(r={}.toString.call(t).slice(8,-1))&&t.constructor?t.constructor.name:r)||"Set"===r?Array.from(t):"Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?J(t,e):void 0}function et(t){return"object"===l(t)&&null!==t?t="function"==typeof t.toString?t.toString():"[object Object]":(null==t||isNaN(t)&&!t.length)&&(t=""),String(t)}function f(t,e){var r,n=0<arguments.length&&void 0!==t?t:{},a=1<arguments.length?e:void 0;for(r in a)void 0===n[r]&&(n[r]=a[r]);return n}var rt={ignoreCase:!1,minOccurrences:1};function A(t,e){for(var r=0;r<e.length;r++){var n=e[r];if(t===n||"[object RegExp]"===Object.prototype.toString.call(n)&&n.test(t))return!0}return!1}function $(t,e){u(t),e="object"===l(e)?(r=e.min||0,e.max):(r=e,arguments[2]);var r,t=encodeURI(t).split(/%..|./).length-1;return r<=t&&(void 0===e||t<=e)}var nt={require_tld:!0,allow_underscores:!1,allow_trailing_dot:!1,allow_numeric_tld:!1,allow_wildcard:!1,ignore_max_length:!1};function at(t,e){u(t),(e=f(e,nt)).allow_trailing_dot&&"."===t[t.length-1]&&(t=t.substring(0,t.length-1));var t=(t=!0===e.allow_wildcard&&0===t.indexOf("*.")?t.substring(2):t).split("."),r=t[t.length-1];if(e.require_tld){if(t.length<2)return!1;if(!e.allow_numeric_tld&&!/^([a-z\u00A1-\u00A8\u00AA-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF]{2,}|xn[a-z0-9-]{2,})$/i.test(r))return!1;if(/\s/.test(r))return!1}return!(!e.allow_numeric_tld&&/^\d+$/.test(r))&&t.every(function(t){return!(63<t.length&&!e.ignore_max_length||!/^[a-z_\u00a1-\uffff0-9-]+$/i.test(t)||/[\uff01-\uff5e]/.test(t)||/^-|-$/.test(t)||!e.allow_underscores&&/_/.test(t))})}var p="(?:[0-9]|[1-9][0-9]|1[0-9][0-9]|2[0-4][0-9]|25[0-5])",p="(".concat(p,"[.]){3}").concat(p),it=new RegExp("^".concat(p,"$")),h="(?:[0-9a-fA-F]{1,4})",ot=new RegExp("^("+"(?:".concat(h,":){7}(?:").concat(h,"|:)|")+"(?:".concat(h,":){6}(?:").concat(p,"|:").concat(h,"|:)|")+"(?:".concat(h,":){5}(?::").concat(p,"|(:").concat(h,"){1,2}|:)|")+"(?:".concat(h,":){4}(?:(:").concat(h,"){0,1}:").concat(p,"|(:").concat(h,"){1,3}|:)|")+"(?:".concat(h,":){3}(?:(:").concat(h,"){0,2}:").concat(p,"|(:").concat(h,"){1,4}|:)|")+"(?:".concat(h,":){2}(?:(:").concat(h,"){0,3}:").concat(p,"|(:").concat(h,"){1,5}|:)|")+"(?:".concat(h,":){1}(?:(:").concat(h,"){0,4}:").concat(p,"|(:").concat(h,"){1,6}|:)|")+"(?::((?::".concat(h,"){0,5}:").concat(p,"|(?::").concat(h,"){1,7}|:))")+")(%[0-9a-zA-Z.]{1,})?$");function g(t){var e=1<arguments.length&&void 0!==arguments[1]?arguments[1]:{},e=(u(t),("object"===l(e)?e.version:arguments[1])||"");return e?"4"===e.toString()?it.test(t):"6"===e.toString()&&ot.test(t):g(t,{version:4})||g(t,{version:6})}var st={allow_display_name:!1,allow_underscores:!1,require_display_name:!1,allow_utf8_local_part:!0,require_tld:!0,blacklisted_chars:"",ignore_max_length:!1,host_blacklist:[],host_whitelist:[]},ct=/^([^\x00-\x1F\x7F-\x9F\cX]+)</i,ut=/^[a-z\d!#\$%&'\*\+\-\/=\?\^_`{\|}~]+$/i,lt=/^[a-z\d]+$/,dt=/^([\s\x01-\x08\x0b\x0c\x0e-\x1f\x7f\x21\x23-\x5b\x5d-\x7e]|(\\[\x01-\x09\x0b\x0c\x0d-\x7f]))*$/i,ft=/^[a-z\d!#\$%&'\*\+\-\/=\?\^_`{\|}~\u00A1-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF]+$/i,At=/^([\s\x01-\x08\x0b\x0c\x0e-\x1f\x7f\x21\x23-\x5b\x5d-\x7e\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF]|(\\[\x01-\x09\x0b\x0c\x0d-\x7f\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF]))*$/i;function $t(t,e){if(u(t),(e=f(e,st)).require_display_name||e.allow_display_name){var r=t.match(ct);if(r){r=r[1];if(t=t.replace(r,"").replace(/(^<|>$)/g,""),!(t=>{var e=t.replace(/^"(.+)"$/,"$1");if(e.trim()){if(/[\.";<>]/.test(e)){if(e===t)return;if(!(e.split('"').length===e.split('\\"').length))return}return 1}})(r=r.endsWith(" ")?r.slice(0,-1):r))return!1}else if(e.require_display_name)return!1}if(!e.ignore_max_length&&254<t.length)return!1;var r=t.split("@"),t=r.pop(),n=t.toLowerCase();if(0<e.host_blacklist.length&&A(n,e.host_blacklist))return!1;if(0<e.host_whitelist.length&&!A(n,e.host_whitelist))return!1;r=r.join("@");if(e.domain_specific_validation&&("gmail.com"===n||"googlemail.com"===n)){n=(r=r.toLowerCase()).split("+")[0];if(!$(n.replace(/\./g,""),{min:6,max:30}))return!1;for(var a=n.split("."),i=0;i<a.length;i++)if(!lt.test(a[i]))return!1}if(!(!1!==e.ignore_max_length||$(r,{max:64})&&$(t,{max:254})))return!1;if(!at(t,{require_tld:e.require_tld,ignore_max_length:e.ignore_max_length,allow_underscores:e.allow_underscores})){if(!e.allow_ip_domain)return!1;if(!g(t)){if(!t.startsWith("[")||!t.endsWith("]"))return!1;n=t.slice(1,-1);if(0===n.length||!g(n))return!1}}if(e.blacklisted_chars&&-1!==r.search(new RegExp("[".concat(e.blacklisted_chars,"]+"),"g")))return!1;if('"'===r[0]&&'"'===r[r.length-1])return r=r.slice(1,r.length-1),(e.allow_utf8_local_part?At:dt).test(r);for(var o=e.allow_utf8_local_part?ft:ut,s=r.split("."),c=0;c<s.length;c++)if(!o.test(s[c]))return!1;return!0}function S(t,e){return-1!==t.indexOf(e)}var pt={protocols:["http","https","ftp"],require_tld:!0,require_protocol:!1,require_host:!0,require_port:!1,require_valid_protocol:!0,allow_underscores:!1,allow_trailing_dot:!1,allow_protocol_relative_urls:!1,allow_fragments:!0,allow_query_components:!0,validate_length:!0,max_allowed_length:2084},ht=/^\[([^\]]+)\](?::([0-9]+))?$/;var gt=/^(?:[0-9a-fA-F]{2}([-:\s]))([0-9a-fA-F]{2}\1){4}([0-9a-fA-F]{2})$/,St=/^([0-9a-fA-F]){12}$/,mt=/^([0-9a-fA-F]{4}\.){2}([0-9a-fA-F]{4})$/,Zt=/^(?:[0-9a-fA-F]{2}([-:\s]))([0-9a-fA-F]{2}\1){6}([0-9a-fA-F]{2})$/,Et=/^([0-9a-fA-F]){16}$/,It=/^([0-9a-fA-F]{4}\.){3}([0-9a-fA-F]{4})$/;var Rt=/^\d{1,3}$/;var vt={format:"YYYY/MM/DD",delimiters:["/","-"],strictMode:!1};function m(e,r){if(r=f("string"==typeof r?{format:r}:r,vt),"string"==typeof e&&/(^(y{4}|y{2})[.\/-](m{1,2})[.\/-](d{1,2})$)|(^(m{1,2})[.\/-](d{1,2})[.\/-]((y{4}|y{2})$))|(^(d{1,2})[.\/-](m{1,2})[.\/-]((y{4}|y{2})$))/gi.test(r.format)){if(r.strictMode&&e.length!==r.format.length)return!1;var t,n=r.delimiters.find(function(t){return-1!==r.format.indexOf(t)}),a=r.strictMode?n:r.delimiters.find(function(t){return-1!==e.indexOf(t)}),i={},o=X(((t,e)=>{for(var r=[],n=Math.max(t.length,e.length),a=0;a<n;a++)r.push([t[a],e[a]]);return r})(e.split(a),r.format.toLowerCase().split(n)));try{for(o.s();!(t=o.n()).done;){var s=d(t.value,2),c=s[0],u=s[1];if(!c||!u||c.length!==u.length)return!1;i[u.charAt(0)]=c}}catch(t){o.e(t)}finally{o.f()}if((a=i.y).startsWith("-"))return!1;if(2===i.y.length){n=parseInt(i.y,10);if(isNaN(n))return!1;a=(n<(new Date).getFullYear()%100?"20":"19").concat(i.y)}var n=i.m,l=(1===i.m.length&&(n="0".concat(i.m)),i.d);return 1===i.d.length&&(l="0".concat(i.d)),new Date("".concat(a,"-").concat(n,"-").concat(l,"T00:00:00.000Z")).getUTCDate()===+i.d}return!r.strictMode&&"[object Date]"===Object.prototype.toString.call(e)&&isFinite(e)}var Lt={hourFormat:"hour24",mode:"default"},Mt={hour24:{default:/^([01]?[0-9]|2[0-3]):([0-5][0-9])$/,withSeconds:/^([01]?[0-9]|2[0-3]):([0-5][0-9]):([0-5][0-9])$/,withOptionalSeconds:/^([01]?[0-9]|2[0-3]):([0-5][0-9])(?::([0-5][0-9]))?$/},hour12:{default:/^(0?[1-9]|1[0-2]):([0-5][0-9]) (A|P)M$/,withSeconds:/^(0?[1-9]|1[0-2]):([0-5][0-9]):([0-5][0-9]) (A|P)M$/,withOptionalSeconds:/^(0?[1-9]|1[0-2]):([0-5][0-9])(?::([0-5][0-9]))? (A|P)M$/}};var Z=function(t,e){return t.some(function(t){return e===t})},Bt={loose:!1},Ct=["true","false","1","0"],Nt=[].concat(Ct,["yes","no"]);var p="(([a-zA-Z]{2,3}(-".concat("([A-Za-z]{3}(-[A-Za-z]{3}){0,2})",")?)|([a-zA-Z]{5,8}))"),h="(".concat("(\\d|[A-W]|[Y-Z]|[a-w]|[y-z])","(-[A-Za-z0-9]{2,8})+)"),Ft="(x(-[A-Za-z0-9]{1,8})+)",E="(".concat("((en-GB-oed)|(i-ami)|(i-bnn)|(i-default)|(i-enochian)|(i-hak)|(i-klingon)|(i-lux)|(i-mingo)|(i-navajo)|(i-pwn)|(i-tao)|(i-tay)|(i-tsu)|(sgn-BE-FR)|(sgn-BE-NL)|(sgn-CH-DE))","|").concat("((art-lojban)|(cel-gaulish)|(no-bok)|(no-nyn)|(zh-guoyu)|(zh-hakka)|(zh-min)|(zh-min-nan)|(zh-xiang))",")"),I="(-|_)",p="".concat(p,"(").concat(I).concat("([A-Za-z]{4})",")?(").concat(I).concat("([A-Za-z]{2}|\\d{3})",")?(").concat(I).concat("([A-Za-z0-9]{5,8}|(\\d[A-Z-a-z0-9]{3}))",")*(").concat(I).concat(h,")*(").concat(I).concat(Ft,")?"),Dt=new RegExp("(^".concat(Ft,"$)|(^").concat(E,"$)|(^").concat(p,"$)"));var Tt=/^(?!(1[3-9])|(20)|(3[3-9])|(4[0-9])|(5[0-9])|(60)|(7[3-9])|(8[1-9])|(9[0-2])|(9[3-9]))[0-9]{9}$/;h=Object.keys(n);var I=Object.keys(a),bt=/^[0-9]+$/;var Gt={AM:/^[A-Z]{2}\d{7}$/,AR:/^[A-Z]{3}\d{6}$/,AT:/^[A-Z]\d{7}$/,AU:/^[A-Z]\d{7}$/,AZ:/^[A-Z]{1}\d{8}$/,BE:/^[A-Z]{2}\d{6}$/,BG:/^\d{9}$/,BR:/^[A-Z]{2}\d{6}$/,BY:/^[A-Z]{2}\d{7}$/,CA:/^[A-Z]{2}\d{6}$|^[A-Z]\d{6}[A-Z]{2}$/,CH:/^[A-Z]\d{7}$/,CN:/^G\d{8}$|^E(?![IO])[A-Z0-9]\d{7}$/,CY:/^[A-Z](\d{6}|\d{8})$/,CZ:/^\d{8}$/,DE:/^[CFGHJKLMNPRTVWXYZ0-9]{9}$/,DK:/^\d{9}$/,DZ:/^\d{9}$/,EE:/^([A-Z]\d{7}|[A-Z]{2}\d{7})$/,ES:/^[A-Z0-9]{2}([A-Z0-9]?)\d{6}$/,FI:/^[A-Z]{2}\d{7}$/,FR:/^\d{2}[A-Z]{2}\d{5}$/,GB:/^\d{9}$/,GR:/^[A-Z]{2}\d{7}$/,HR:/^\d{9}$/,HU:/^[A-Z]{2}(\d{6}|\d{7})$/,IE:/^[A-Z0-9]{2}\d{7}$/,IN:/^[A-Z]{1}-?\d{7}$/,ID:/^[A-C]\d{7}$/,IR:/^[A-Z]\d{8}$/,IS:/^(A)\d{7}$/,IT:/^[A-Z0-9]{2}\d{7}$/,JM:/^[Aa]\d{7}$/,JP:/^[A-Z]{2}\d{7}$/,KR:/^[MS]\d{8}$/,KZ:/^[a-zA-Z]\d{7}$/,LI:/^[a-zA-Z]\d{5}$/,LT:/^[A-Z0-9]{8}$/,LU:/^[A-Z0-9]{8}$/,LV:/^[A-Z0-9]{2}\d{7}$/,LY:/^[A-Z0-9]{8}$/,MT:/^\d{7}$/,MZ:/^([A-Z]{2}\d{7})|(\d{2}[A-Z]{2}\d{5})$/,MY:/^[AHK]\d{8}$/,MX:/^\d{10,11}$/,NL:/^[A-Z]{2}[A-Z0-9]{6}\d$/,NZ:/^([Ll]([Aa]|[Dd]|[Ff]|[Hh])|[Ee]([Aa]|[Pp])|[Nn])\d{6}$/,PH:/^([A-Z](\d{6}|\d{7}[A-Z]))|([A-Z]{2}(\d{6}|\d{7}))$/,PK:/^[A-Z]{2}\d{7}$/,PL:/^[A-Z]{2}\d{7}$/,PT:/^[A-Z]\d{6}$/,RO:/^\d{8,9}$/,RU:/^\d{9}$/,SE:/^\d{8}$/,SL:/^(P)[A-Z]\d{7}$/,SK:/^[0-9A-Z]\d{7}$/,TH:/^[A-Z]{1,2}\d{6,7}$/,TR:/^[A-Z]\d{8}$/,UA:/^[A-Z]{2}\d{6}$/,US:/^\d{9}$|^[A-Z]\d{8}$/,ZA:/^[TAMD]\d{8}$/},Ft=Object.keys(Gt);var Pt=/^(?:[-+]?(?:0|[1-9][0-9]*))$/,Ot=/^[-+]?[0-9]+$/;function Ht(t,e){u(t);var r=!1===(e=e||{}).allow_leading_zeroes?Pt:Ot,n=!e.hasOwnProperty("min")||o(e.min)||t>=e.min,a=!e.hasOwnProperty("max")||o(e.max)||t<=e.max,i=!e.hasOwnProperty("lt")||o(e.lt)||t<e.lt,e=!e.hasOwnProperty("gt")||o(e.gt)||t>e.gt;return r.test(t)&&n&&a&&i&&e}var _t=/^[0-9]{15}$/,Ut=/^\d{2}-\d{6}-\d{6}-\d{1}$/;var wt=/^[\x00-\x7F]+$/;var Kt=/[^\u0020-\u007E\uFF61-\uFF9F\uFFA0-\uFFDC\uFFE8-\uFFEE0-9a-zA-Z]/;var yt=/[\u0020-\u007E\uFF61-\uFF9F\uFFA0-\uFFDC\uFFE8-\uFFEE0-9a-zA-Z]/;var Wt=/[^\x00-\x7F]/;E="i",p=(p=["^(0|[1-9]\\d*)\\.(0|[1-9]\\d*)\\.(0|[1-9]\\d*)","(?:-((?:0|[1-9]\\d*|\\d*[a-z-][0-9a-z-]*)(?:\\.(?:0|[1-9]\\d*|\\d*[a-z-][0-9a-z-]*))*))","?(?:\\+([0-9a-z-]+(?:\\.[0-9a-z-]+)*))?$"]).join("");var xt=new RegExp(p,E);var kt=/[\uD800-\uDBFF][\uDC00-\uDFFF]/;var Yt={force_decimal:!1,decimal_digits:"1,",locale:"en-US"},Vt=["","-","+"];var zt=/^(0x|0h)?[0-9A-F]+$/i;function Qt(t){return u(t),zt.test(t)}var jt=/^(0o)?[0-7]+$/i;var Jt=/^#?([0-9A-F]{3}|[0-9A-F]{4}|[0-9A-F]{6}|[0-9A-F]{8})$/i;var Xt=/^rgb\((([0-9]|[1-9][0-9]|1[0-9][0-9]|2[0-4][0-9]|25[0-5]),){2}([0-9]|[1-9][0-9]|1[0-9][0-9]|2[0-4][0-9]|25[0-5])\)$/,qt=/^rgba\((([0-9]|[1-9][0-9]|1[0-9][0-9]|2[0-4][0-9]|25[0-5]),){3}(0?\.\d\d?|1(\.0)?|0(\.0)?)\)$/,te=/^rgb\((([0-9]%|[1-9][0-9]%|100%),){2}([0-9]%|[1-9][0-9]%|100%)\)$/,ee=/^rgba\((([0-9]%|[1-9][0-9]%|100%),){3}(0?\.\d\d?|1(\.0)?|0(\.0)?)\)$/,re=/^rgba?/;var ne=/^hsla?\(((\+|\-)?([0-9]+(\.[0-9]+)?(e(\+|\-)?[0-9]+)?|\.[0-9]+(e(\+|\-)?[0-9]+)?))(deg|grad|rad|turn)?(,(\+|\-)?([0-9]+(\.[0-9]+)?(e(\+|\-)?[0-9]+)?|\.[0-9]+(e(\+|\-)?[0-9]+)?)%){2}(,((\+|\-)?([0-9]+(\.[0-9]+)?(e(\+|\-)?[0-9]+)?|\.[0-9]+(e(\+|\-)?[0-9]+)?)%?))?\)$/i,ae=/^hsla?\(((\+|\-)?([0-9]+(\.[0-9]+)?(e(\+|\-)?[0-9]+)?|\.[0-9]+(e(\+|\-)?[0-9]+)?))(deg|grad|rad|turn)?(\s(\+|\-)?([0-9]+(\.[0-9]+)?(e(\+|\-)?[0-9]+)?|\.[0-9]+(e(\+|\-)?[0-9]+)?)%){2}\s?(\/\s((\+|\-)?([0-9]+(\.[0-9]+)?(e(\+|\-)?[0-9]+)?|\.[0-9]+(e(\+|\-)?[0-9]+)?)%?)\s?)?\)$/i;var ie=/^[A-Z]{2}[0-9A-Z]{3}\d{2}\d{5}$/;var R={AD:/^(AD[0-9]{2})\d{8}[A-Z0-9]{12}$/,AE:/^(AE[0-9]{2})\d{3}\d{16}$/,AL:/^(AL[0-9]{2})\d{8}[A-Z0-9]{16}$/,AT:/^(AT[0-9]{2})\d{16}$/,AZ:/^(AZ[0-9]{2})[A-Z0-9]{4}\d{20}$/,BA:/^(BA[0-9]{2})\d{16}$/,BE:/^(BE[0-9]{2})\d{12}$/,BG:/^(BG[0-9]{2})[A-Z]{4}\d{6}[A-Z0-9]{8}$/,BH:/^(BH[0-9]{2})[A-Z]{4}[A-Z0-9]{14}$/,BR:/^(BR[0-9]{2})\d{23}[A-Z]{1}[A-Z0-9]{1}$/,BY:/^(BY[0-9]{2})[A-Z0-9]{4}\d{20}$/,CH:/^(CH[0-9]{2})\d{5}[A-Z0-9]{12}$/,CR:/^(CR[0-9]{2})\d{18}$/,CY:/^(CY[0-9]{2})\d{8}[A-Z0-9]{16}$/,CZ:/^(CZ[0-9]{2})\d{20}$/,DE:/^(DE[0-9]{2})\d{18}$/,DK:/^(DK[0-9]{2})\d{14}$/,DO:/^(DO[0-9]{2})[A-Z]{4}\d{20}$/,DZ:/^(DZ\d{24})$/,EE:/^(EE[0-9]{2})\d{16}$/,EG:/^(EG[0-9]{2})\d{25}$/,ES:/^(ES[0-9]{2})\d{20}$/,FI:/^(FI[0-9]{2})\d{14}$/,FO:/^(FO[0-9]{2})\d{14}$/,FR:/^(FR[0-9]{2})\d{10}[A-Z0-9]{11}\d{2}$/,GB:/^(GB[0-9]{2})[A-Z]{4}\d{14}$/,GE:/^(GE[0-9]{2})[A-Z0-9]{2}\d{16}$/,GI:/^(GI[0-9]{2})[A-Z]{4}[A-Z0-9]{15}$/,GL:/^(GL[0-9]{2})\d{14}$/,GR:/^(GR[0-9]{2})\d{7}[A-Z0-9]{16}$/,GT:/^(GT[0-9]{2})[A-Z0-9]{4}[A-Z0-9]{20}$/,HR:/^(HR[0-9]{2})\d{17}$/,HU:/^(HU[0-9]{2})\d{24}$/,IE:/^(IE[0-9]{2})[A-Z]{4}\d{14}$/,IL:/^(IL[0-9]{2})\d{19}$/,IQ:/^(IQ[0-9]{2})[A-Z]{4}\d{15}$/,IR:/^(IR[0-9]{2})0\d{2}0\d{18}$/,IS:/^(IS[0-9]{2})\d{22}$/,IT:/^(IT[0-9]{2})[A-Z]{1}\d{10}[A-Z0-9]{12}$/,JO:/^(JO[0-9]{2})[A-Z]{4}\d{22}$/,KW:/^(KW[0-9]{2})[A-Z]{4}[A-Z0-9]{22}$/,KZ:/^(KZ[0-9]{2})\d{3}[A-Z0-9]{13}$/,LB:/^(LB[0-9]{2})\d{4}[A-Z0-9]{20}$/,LC:/^(LC[0-9]{2})[A-Z]{4}[A-Z0-9]{24}$/,LI:/^(LI[0-9]{2})\d{5}[A-Z0-9]{12}$/,LT:/^(LT[0-9]{2})\d{16}$/,LU:/^(LU[0-9]{2})\d{3}[A-Z0-9]{13}$/,LV:/^(LV[0-9]{2})[A-Z]{4}[A-Z0-9]{13}$/,MA:/^(MA[0-9]{26})$/,MC:/^(MC[0-9]{2})\d{10}[A-Z0-9]{11}\d{2}$/,MD:/^(MD[0-9]{2})[A-Z0-9]{20}$/,ME:/^(ME[0-9]{2})\d{18}$/,MK:/^(MK[0-9]{2})\d{3}[A-Z0-9]{10}\d{2}$/,MR:/^(MR[0-9]{2})\d{23}$/,MT:/^(MT[0-9]{2})[A-Z]{4}\d{5}[A-Z0-9]{18}$/,MU:/^(MU[0-9]{2})[A-Z]{4}\d{19}[A-Z]{3}$/,MZ:/^(MZ[0-9]{2})\d{21}$/,NL:/^(NL[0-9]{2})[A-Z]{4}\d{10}$/,NO:/^(NO[0-9]{2})\d{11}$/,PK:/^(PK[0-9]{2})[A-Z0-9]{4}\d{16}$/,PL:/^(PL[0-9]{2})\d{24}$/,PS:/^(PS[0-9]{2})[A-Z]{4}[A-Z0-9]{21}$/,PT:/^(PT[0-9]{2})\d{21}$/,QA:/^(QA[0-9]{2})[A-Z]{4}[A-Z0-9]{21}$/,RO:/^(RO[0-9]{2})[A-Z]{4}[A-Z0-9]{16}$/,RS:/^(RS[0-9]{2})\d{18}$/,SA:/^(SA[0-9]{2})\d{2}[A-Z0-9]{18}$/,SC:/^(SC[0-9]{2})[A-Z]{4}\d{20}[A-Z]{3}$/,SE:/^(SE[0-9]{2})\d{20}$/,SI:/^(SI[0-9]{2})\d{15}$/,SK:/^(SK[0-9]{2})\d{20}$/,SM:/^(SM[0-9]{2})[A-Z]{1}\d{10}[A-Z0-9]{12}$/,SV:/^(SV[0-9]{2})[A-Z0-9]{4}\d{20}$/,TL:/^(TL[0-9]{2})\d{19}$/,TN:/^(TN[0-9]{2})\d{20}$/,TR:/^(TR[0-9]{2})\d{5}[A-Z0-9]{17}$/,UA:/^(UA[0-9]{2})\d{6}[A-Z0-9]{19}$/,VA:/^(VA[0-9]{2})\d{18}$/,VG:/^(VG[0-9]{2})[A-Z]{4}\d{16}$/,XK:/^(XK[0-9]{2})\d{16}$/};function oe(t,e){var t=t.replace(/[\s\-]+/gi,"").toUpperCase(),r=t.slice(0,2).toUpperCase(),n=r in R;if(e.whitelist){if(0<e.whitelist.filter(function(t){return!(t in R)}).length)return!1;if(!Z(e.whitelist,r))return!1}if(e.blacklist&&Z(e.blacklist,r))return!1;return n&&R[r].test(t)}var p=Object.keys(R),se=new Set(["AD","AE","AF","AG","AI","AL","AM","AO","AQ","AR","AS","AT","AU","AW","AX","AZ","BA","BB","BD","BE","BF","BG","BH","BI","BJ","BL","BM","BN","BO","BQ","BR","BS","BT","BV","BW","BY","BZ","CA","CC","CD","CF","CG","CH","CI","CK","CL","CM","CN","CO","CR","CU","CV","CW","CX","CY","CZ","DE","DJ","DK","DM","DO","DZ","EC","EE","EG","EH","ER","ES","ET","FI","FJ","FK","FM","FO","FR","GA","GB","GD","GE","GF","GG","GH","GI","GL","GM","GN","GP","GQ","GR","GS","GT","GU","GW","GY","HK","HM","HN","HR","HT","HU","ID","IE","IL","IM","IN","IO","IQ","IR","IS","IT","JE","JM","JO","JP","KE","KG","KH","KI","KM","KN","KP","KR","KW","KY","KZ","LA","LB","LC","LI","LK","LR","LS","LT","LU","LV","LY","MA","MC","MD","ME","MF","MG","MH","MK","ML","MM","MN","MO","MP","MQ","MR","MS","MT","MU","MV","MW","MX","MY","MZ","NA","NC","NE","NF","NG","NI","NL","NO","NP","NR","NU","NZ","OM","PA","PE","PF","PG","PH","PK","PL","PM","PN","PR","PS","PT","PW","PY","QA","RE","RO","RS","RU","RW","SA","SB","SC","SD","SE","SG","SH","SI","SJ","SK","SL","SM","SN","SO","SR","SS","ST","SV","SX","SY","SZ","TC","TD","TF","TG","TH","TJ","TK","TL","TM","TN","TO","TR","TT","TV","TW","TZ","UA","UG","UM","US","UY","UZ","VA","VC","VE","VG","VI","VN","VU","WF","WS","YE","YT","ZA","ZM","ZW"]);var ce=se,ue=/^[A-Za-z]{6}[A-Za-z0-9]{2}([A-Za-z0-9]{3})?$/;var le=/^[a-f0-9]{32}$/;var de={md5:32,md4:32,sha1:40,sha256:64,sha384:96,sha512:128,ripemd128:32,ripemd160:40,tiger128:32,tiger160:40,tiger192:48,crc32:8,crc32b:8};var fe=/^(?:[A-Za-z0-9+/]{4})*(?:[A-Za-z0-9+/]{2}==|[A-Za-z0-9+/]{3}=|[A-Za-z0-9+/]{4})$/,Ae=/^[A-Za-z0-9+/]+$/,$e=/^(?:[A-Za-z0-9_-]{4})*(?:[A-Za-z0-9_-]{2}==|[A-Za-z0-9_-]{3}=|[A-Za-z0-9_-]{4})$/,pe=/^[A-Za-z0-9_-]+$/;function he(t,e){var r;return u(t),e=f(e,{urlSafe:!1,padding:!(null!=(r=e)&&r.urlSafe)}),""===t||(r=e.urlSafe?e.padding?$e:pe:e.padding?fe:Ae,(!e.padding||t.length%4==0)&&r.test(t))}var ge={allow_primitives:!1};var Se={ignore_whitespace:!1};var me={1:/^[0-9A-F]{8}-[0-9A-F]{4}-1[0-9A-F]{3}-[89AB][0-9A-F]{3}-[0-9A-F]{12}$/i,2:/^[0-9A-F]{8}-[0-9A-F]{4}-2[0-9A-F]{3}-[89AB][0-9A-F]{3}-[0-9A-F]{12}$/i,3:/^[0-9A-F]{8}-[0-9A-F]{4}-3[0-9A-F]{3}-[89AB][0-9A-F]{3}-[0-9A-F]{12}$/i,4:/^[0-9A-F]{8}-[0-9A-F]{4}-4[0-9A-F]{3}-[89AB][0-9A-F]{3}-[0-9A-F]{12}$/i,5:/^[0-9A-F]{8}-[0-9A-F]{4}-5[0-9A-F]{3}-[89AB][0-9A-F]{3}-[0-9A-F]{12}$/i,6:/^[0-9A-F]{8}-[0-9A-F]{4}-6[0-9A-F]{3}-[89AB][0-9A-F]{3}-[0-9A-F]{12}$/i,7:/^[0-9A-F]{8}-[0-9A-F]{4}-7[0-9A-F]{3}-[89AB][0-9A-F]{3}-[0-9A-F]{12}$/i,8:/^[0-9A-F]{8}-[0-9A-F]{4}-8[0-9A-F]{3}-[89AB][0-9A-F]{3}-[0-9A-F]{12}$/i,nil:/^00000000-0000-0000-0000-000000000000$/i,max:/^ffffffff-ffff-ffff-ffff-ffffffffffff$/i,loose:/^[0-9A-F]{8}-[0-9A-F]{4}-[0-9A-F]{4}-[0-9A-F]{4}-[0-9A-F]{12}$/i,all:/^(?:[0-9a-f]{8}-[0-9a-f]{4}-[1-8][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}|00000000-0000-0000-0000-000000000000|ffffffff-ffff-ffff-ffff-ffffffffffff)$/i};function Ze(t){u(t);for(var e,r,n=t.replace(/[- ]+/g,""),a=0,i=n.length-1;0<=i;i--)e=n.substring(i,i+1),e=parseInt(e,10),a+=r&&10<=(e*=2)?e%10+1:e,r=!r;return!(a%10!=0||!n)}var v={amex:/^3[47][0-9]{13}$/,dinersclub:/^3(?:0[0-5]|[68][0-9])[0-9]{11}$/,discover:/^6(?:011|5[0-9][0-9])[0-9]{12,15}$/,jcb:/^(?:2131|1800|35\d{3})\d{11}$/,mastercard:/^5[1-5][0-9]{2}|(222[1-9]|22[3-9][0-9]|2[3-6][0-9]{2}|27[01][0-9]|2720)[0-9]{12}$/,unionpay:/^(6[27][0-9]{14}|^(81[0-9]{14,17}))$/,visa:/^(?:4[0-9]{12})(?:[0-9]{3,6})?$/},Ee=(()=>{var t,e=[];for(t in v)v.hasOwnProperty(t)&&e.push(v[t]);return e})();var L={PL:function(t){u(t);var n={1:1,2:3,3:7,4:9,5:1,6:3,7:7,8:9,9:1,10:3,11:0};if(null!=t&&11===t.length&&Ht(t,{allow_leading_zeroes:!0})){var e=t.split("").slice(0,-1).reduce(function(t,e,r){return t+Number(e)*n[r+1]},0)%10,t=Number(t.charAt(t.length-1));if(0==e&&0===t||t===10-e)return!0}return!1},ES:function(t){u(t);var e,r={X:0,Y:1,Z:2},t=t.trim().toUpperCase();return!!/^[0-9X-Z][0-9]{7}[TRWAGMYFPDXBNJZSQVHLCKE]$/.test(t)&&(e=t.slice(0,-1).replace(/[X,Y,Z]/g,function(t){return r[t]}),t.endsWith(["T","R","W","A","G","M","Y","F","P","D","X","B","N","J","Z","S","Q","V","H","L","C","K","E"][e%23]))},FI:function(t){return u(t),11===t.length&&!!t.match(/^\d{6}[\-A\+]\d{3}[0-9ABCDEFHJKLMNPRSTUVWXY]{1}$/)&&"0123456789ABCDEFHJKLMNPRSTUVWXY"[(1e3*parseInt(t.slice(0,6),10)+parseInt(t.slice(7,10),10))%31]===t.slice(10,11)},IN:function(t){var r,n=[[0,1,2,3,4,5,6,7,8,9],[1,2,3,4,0,6,7,8,9,5],[2,3,4,0,1,7,8,9,5,6],[3,4,0,1,2,8,9,5,6,7],[4,0,1,2,3,9,5,6,7,8],[5,9,8,7,6,0,4,3,2,1],[6,5,9,8,7,1,0,4,3,2],[7,6,5,9,8,2,1,0,4,3],[8,7,6,5,9,3,2,1,0,4],[9,8,7,6,5,4,3,2,1,0]],a=[[0,1,2,3,4,5,6,7,8,9],[1,5,7,6,2,8,3,0,9,4],[5,8,0,3,7,9,6,1,4,2],[8,9,1,6,0,4,3,5,2,7],[9,4,5,3,1,2,6,8,7,0],[4,2,8,6,5,7,3,9,0,1],[2,7,9,3,8,0,6,4,1,5],[7,0,4,6,9,1,3,2,5,8]],t=t.trim();return!!/^[1-9]\d{3}\s?\d{4}\s?\d{4}$/.test(t)&&(r=0,t.replace(/\s/g,"").split("").map(Number).reverse().forEach(function(t,e){r=n[r][a[e%8][t]]}),0===r)},IR:function(t){if(!t.match(/^\d{10}$/))return!1;if(t="0000".concat(t).slice(t.length-6),0===parseInt(t.slice(3,9),10))return!1;for(var e=parseInt(t.slice(9,10),10),r=0,n=0;n<9;n++)r+=parseInt(t.slice(n,n+1),10)*(10-n);return(r%=11)<2&&e===r||2<=r&&e===11-r},IT:function(t){return 9===t.length&&"CA00000AA"!==t&&-1<t.search(/C[A-Z]\d{5}[A-Z]{2}/i)},NO:function(t){var e,t=t.trim();return!isNaN(Number(t))&&11===t.length&&"00000000000"!==t&&(e=(11-(3*(t=t.split("").map(Number))[0]+7*t[1]+6*t[2]+ +t[3]+8*t[4]+9*t[5]+4*t[6]+5*t[7]+2*t[8])%11)%11)===t[9]&&(11-(5*t[0]+4*t[1]+3*t[2]+2*t[3]+7*t[4]+6*t[5]+5*t[6]+4*t[7]+3*t[8]+2*e)%11)%11===t[10]},TH:function(t){if(!t.match(/^[1-8]\d{12}$/))return!1;for(var e=0,r=0;r<12;r++)e+=parseInt(t[r],10)*(13-r);return t[12]===((11-e%11)%10).toString()},LK:function(t){return!(10!==t.length||!/^[1-9]\d{8}[vx]$/i.test(t))||!(12!==t.length||!/^[1-9]\d{11}$/i.test(t))},"he-IL":function(t){t=t.trim();if(!/^\d{9}$/.test(t))return!1;for(var e,r=t,n=0,a=0;a<r.length;a++)n+=9<(e=Number(r[a])*(a%2+1))?e-9:e;return n%10==0},"ar-LY":function(t){t=t.trim();return!!/^(1|2)\d{11}$/.test(t)},"ar-TN":function(t){t=t.trim();return!!/^\d{8}$/.test(t)},"zh-CN":function(t){var e,r,n=["11","12","13","14","15","21","22","23","31","32","33","34","35","36","37","41","42","43","44","45","46","50","51","52","53","54","61","62","63","64","65","71","81","82","91"],a=["7","9","10","5","8","4","2","1","6","3","7","9","10","5","8","4","2"],i=["1","0","X","9","8","7","6","5","4","3","2"],o=function(t){return Z(n,t)},s=function(t){var e=parseInt(t.substring(0,4),10),r=parseInt(t.substring(4,6),10),t=parseInt(t.substring(6),10),n=new Date(e,r-1,t);return!(n>new Date)&&n.getFullYear()===e&&n.getMonth()===r-1&&n.getDate()===t},c=function(t){for(var e=t.substring(0,17),r=0,n=0;n<17;n++)r+=parseInt(e.charAt(n),10)*parseInt(a[n],10);return i[r%11]},u=function(t){return c(t)===t.charAt(17).toUpperCase()};return t=t,!!/^\d{15}|(\d{17}(\d|x|X))$/.test(t)&&(15===t.length?!!/^[1-9]\d{7}((0[1-9])|(1[0-2]))((0[1-9])|([1-2][0-9])|(3[0-1]))\d{3}$/.test(e=t)&&(r=e.substring(0,2),!!o(r))&&(r="19".concat(e.substring(6,12)),!!s(r)):!!/^[1-9]\d{5}[1-9]\d{3}((0[1-9])|(1[0-2]))((0[1-9])|([1-2][0-9])|(3[0-1]))\d{3}(\d|x|X)$/.test(e=t)&&(r=e.substring(0,2),!!o(r))&&(r=e.substring(6,14),!!s(r))&&u(e))},"zh-HK":function(t){var e=/^[0-9]$/;if(t=(t=t.trim()).toUpperCase(),!/^[A-Z]{1,2}[0-9]{6}((\([0-9A]\))|(\[[0-9A]\])|([0-9A]))$/.test(t))return!1;8===(t=t.replace(/\[|\]|\(|\)/g,"")).length&&(t="3".concat(t));for(var r=0,n=0;n<=7;n++)r+=(e.test(t[n])?t[n]:(t[n].charCodeAt(0)-55)%11)*(9-n);return(0===(r%=11)?"0":1===r?"A":String(11-r))===t[t.length-1]},"zh-TW":function(t){var a={A:10,B:11,C:12,D:13,E:14,F:15,G:16,H:17,I:34,J:18,K:19,L:20,M:21,N:22,O:35,P:23,Q:24,R:25,S:26,T:27,U:28,V:29,W:32,X:30,Y:31,Z:33},t=t.trim().toUpperCase();return!!/^[A-Z][0-9]{9}$/.test(t)&&Array.from(t).reduce(function(t,e,r){var n;return 0===r?(n=a[e])%10*9+Math.floor(n/10):9===r?(10-t%10-Number(e))%10==0:t+Number(e)*(9-r)},0)},PK:function(t){t=t.trim();return/^[1-7][0-9]{4}-[0-9]{7}-[1-9]$/.test(t)}};var Ie=8,Re=14,ve=/^(\d{8}|\d{13}|\d{14})$/;function Le(r){var t=10-r.slice(0,-1).split("").map(function(t,e){return Number(t)*(t=r.length,e=e,t===Ie||t===Re?e%2==0?3:1:e%2==0?1:3)}).reduce(function(t,e){return t+e},0)%10;return t<10?t:0}var Me=/^[A-Z]{2}[0-9A-Z]{9}[0-9]$/;var Be=/^(?:[0-9]{9}X|[0-9]{10})$/,Ce=/^(?:[0-9]{13})$/,Ne=[1,3];function Fe(t){for(var e=10,r=0;r<t.length-1;r++)e=(parseInt(t[r],10)+e)%10==0?9:(parseInt(t[r],10)+e)%10*2%11;return(e=1===e?0:11-e)===parseInt(t[10],10)}function De(t){for(var e,r=0,n=!1,a=t.length-1;0<=a;a--)r+=n?9<(e=2*parseInt(t[a],10))?e.toString().split("").map(function(t){return parseInt(t,10)}).reduce(function(t,e){return t+e},0):e:parseInt(t[a],10),n=!n;return r%10==0}function M(t,e){for(var r=0,n=0;n<t.length;n++)r+=t[n]*(e-n);return r}var Te={andover:["10","12"],atlanta:["60","67"],austin:["50","53"],brookhaven:["01","02","03","04","05","06","11","13","14","16","21","22","23","25","34","51","52","54","55","56","57","58","59","65"],cincinnati:["30","32","35","36","37","38","61"],fresno:["15","24"],internet:["20","26","27","45","46","47"],kansas:["40","44"],memphis:["94","95"],ogden:["80","90"],philadelphia:["33","39","41","42","43","46","48","62","63","64","66","68","71","72","73","74","75","76","77","81","82","83","84","85","86","87","88","91","92","93","98","99"],sba:["31"]};function be(t){for(var e=!1,r=!1,n=0;n<3;n++)if(!e&&/[AEIOU]/.test(t[n]))e=!0;else if(!r&&e&&"X"===t[n])r=!0;else if(0<n){if(e&&!r&&!/[AEIOU]/.test(t[n]))return;if(r&&!/X/.test(t[n]))return}return 1}var B={"bg-BG":/^\d{10}$/,"cs-CZ":/^\d{6}\/{0,1}\d{3,4}$/,"de-AT":/^\d{9}$/,"de-DE":/^[1-9]\d{10}$/,"dk-DK":/^\d{6}-{0,1}\d{4}$/,"el-CY":/^[09]\d{7}[A-Z]$/,"el-GR":/^([0-4]|[7-9])\d{8}$/,"en-CA":/^\d{9}$/,"en-GB":/^\d{10}$|^(?!GB|NK|TN|ZZ)(?![DFIQUV])[A-Z](?![DFIQUVO])[A-Z]\d{6}[ABCD ]$/i,"en-IE":/^\d{7}[A-W][A-IW]{0,1}$/i,"en-US":/^\d{2}[- ]{0,1}\d{7}$/,"es-AR":/(20|23|24|27|30|33|34)[0-9]{8}[0-9]/,"es-ES":/^(\d{0,8}|[XYZKLM]\d{7})[A-HJ-NP-TV-Z]$/i,"et-EE":/^[1-6]\d{6}(00[1-9]|0[1-9][0-9]|[1-6][0-9]{2}|70[0-9]|710)\d$/,"fi-FI":/^\d{6}[-+A]\d{3}[0-9A-FHJ-NPR-Y]$/i,"fr-BE":/^\d{11}$/,"fr-FR":/^[0-3]\d{12}$|^[0-3]\d\s\d{2}(\s\d{3}){3}$/,"fr-LU":/^\d{13}$/,"hr-HR":/^\d{11}$/,"hu-HU":/^8\d{9}$/,"it-IT":/^[A-Z]{6}[L-NP-V0-9]{2}[A-EHLMPRST][L-NP-V0-9]{2}[A-ILMZ][L-NP-V0-9]{3}[A-Z]$/i,"lv-LV":/^\d{6}-{0,1}\d{5}$/,"mt-MT":/^\d{3,7}[APMGLHBZ]$|^([1-8])\1\d{7}$/i,"nl-NL":/^\d{9}$/,"pl-PL":/^\d{10,11}$/,"pt-BR":/(?:^\d{11}$)|(?:^\d{14}$)/,"pt-PT":/^\d{9}$/,"ro-RO":/^\d{13}$/,"sk-SK":/^\d{6}\/{0,1}\d{3,4}$/,"sl-SI":/^[1-9]\d{7}$/,"sv-SE":/^(\d{6}[-+]{0,1}\d{4}|(18|19|20)\d{6}[-+]{0,1}\d{4})$/,"uk-UA":/^\d{10}$/},C=(B["lb-LU"]=B["fr-LU"],B["lt-LT"]=B["et-EE"],B["nl-BE"]=B["fr-BE"],B["fr-CA"]=B["en-CA"],{"bg-BG":function(t){var e=t.slice(0,2),r=parseInt(t.slice(2,4),10),e=(40<r?(r-=40,"20"):20<r?(r-=20,"18"):"19").concat(e);if(r<10&&(r="0".concat(r)),!m("".concat(e,"/").concat(r,"/").concat(t.slice(4,6)),"YYYY/MM/DD"))return!1;for(var n=t.split("").map(function(t){return parseInt(t,10)}),a=[2,4,8,5,10,9,7,3,6],i=0,o=0;o<a.length;o++)i+=n[o]*a[o];return(i=i%11==10?0:i%11)===n[9]},"cs-CZ":function(t){t=t.replace(/\W/,"");var e=parseInt(t.slice(0,2),10);if(10===t.length)e=(e<54?"20":"19").concat(e);else{if("000"===t.slice(6))return!1;if(!(e<54))return!1;e="19".concat(e)}3===e.length&&(e=[e.slice(0,2),"0",e.slice(2)].join(""));var r=parseInt(t.slice(2,4),10);if(50<r&&(r-=50),20<r){if(parseInt(e,10)<2004)return!1;r-=20}if(r<10&&(r="0".concat(r)),!m("".concat(e,"/").concat(r,"/").concat(t.slice(4,6)),"YYYY/MM/DD"))return!1;if(10===t.length&&parseInt(t,10)%11!=0){r=parseInt(t.slice(0,9),10)%11;if(!(parseInt(e,10)<1986&&10==r))return!1;if(0!==parseInt(t.slice(9),10))return!1}return!0},"de-AT":De,"de-DE":function(t){for(var e=t.split("").map(function(t){return parseInt(t,10)}),r=[],n=0;n<e.length-1;n++){r.push("");for(var a=0;a<e.length-1;a++)e[n]===e[a]&&(r[n]+=a)}if(2!==(r=r.filter(function(t){return 1<t.length})).length&&3!==r.length)return!1;if(3===r[0].length){for(var i=r[0].split("").map(function(t){return parseInt(t,10)}),o=0,s=0;s<i.length-1;s++)i[s]+1===i[s+1]&&(o+=1);if(2===o)return!1}return Fe(t)},"dk-DK":function(t){t=t.replace(/\W/,"");var e=parseInt(t.slice(4,6),10);switch(t.slice(6,7)){case"0":case"1":case"2":case"3":e="19".concat(e);break;case"4":case"9":e=(e<37?"20":"19").concat(e);break;default:if(e<37)e="20".concat(e);else{if(!(58<e))return!1;e="18".concat(e)}}if(3===e.length&&(e=[e.slice(0,2),"0",e.slice(2)].join("")),!m("".concat(e,"/").concat(t.slice(2,4),"/").concat(t.slice(0,2)),"YYYY/MM/DD"))return!1;for(var r=t.split("").map(function(t){return parseInt(t,10)}),n=0,a=4,i=0;i<9;i++)n+=r[i]*a,1===--a&&(a=7);return 1!=(n%=11)&&(0===n?0===r[9]:r[9]===11-n)},"el-CY":function(t){for(var e=t.slice(0,8).split("").map(function(t){return parseInt(t,10)}),r=0,n=1;n<e.length;n+=2)r+=e[n];for(var a=0;a<e.length;a+=2)e[a]<2?r+=1-e[a]:(r+=2*(e[a]-2)+5,4<e[a]&&(r+=2));return String.fromCharCode(r%26+65)===t.charAt(8)},"el-GR":function(t){for(var e=t.split("").map(function(t){return parseInt(t,10)}),r=0,n=0;n<8;n++)r+=e[n]*Math.pow(2,8-n);return r%11%10===e[8]},"en-CA":function(t){var e=(t=t.split("")).filter(function(t,e){return e%2}).map(function(t){return 2*Number(t)}).join("").split("");return t.filter(function(t,e){return!(e%2)}).concat(e).map(function(t){return Number(t)}).reduce(function(t,e){return t+e})%10==0},"en-IE":function(t){var e=M(t.split("").slice(0,7).map(function(t){return parseInt(t,10)}),8);return 9===t.length&&"W"!==t[8]&&(e+=9*(t[8].charCodeAt(0)-64)),0===(e%=23)?"W"===t[7].toUpperCase():t[7].toUpperCase()===String.fromCharCode(64+e)},"en-US":function(t){return-1!==(()=>{var t,e=[];for(t in Te)Te.hasOwnProperty(t)&&e.push.apply(e,q(Te[t]));return e})().indexOf(t.slice(0,2))},"es-AR":function(t){for(var e=0,r=t.split(""),t=parseInt(r.pop(),10),n=0;n<r.length;n++)e+=r[9-n]*(2+n%6);var a=11-e%11;return 11===a?a=0:10===a&&(a=9),t===a},"es-ES":function(t){var e=t.toUpperCase().split("");if(isNaN(parseInt(e[0],10))&&1<e.length){var r=0;switch(e[0]){case"Y":r=1;break;case"Z":r=2}e.splice(0,1,r)}else for(;e.length<9;)e.unshift(0);return e=e.join(""),t=parseInt(e.slice(0,8),10)%23,e[8]===["T","R","W","A","G","M","Y","F","P","D","X","B","N","J","Z","S","Q","V","H","L","C","K","E"][t]},"et-EE":function(t){var e=t.slice(1,3);switch(t.slice(0,1)){case"1":case"2":e="18".concat(e);break;case"3":case"4":e="19".concat(e);break;default:e="20".concat(e)}if(!m("".concat(e,"/").concat(t.slice(3,5),"/").concat(t.slice(5,7)),"YYYY/MM/DD"))return!1;for(var r=t.split("").map(function(t){return parseInt(t,10)}),n=0,a=1,i=0;i<10;i++)n+=r[i]*a,10===(a+=1)&&(a=1);if(n%11==10){for(var n=0,a=3,o=0;o<10;o++)n+=r[o]*a,10===(a+=1)&&(a=1);if(n%11==10)return 0===r[10]}return n%11===r[10]},"fi-FI":function(t){var e,r=t.slice(4,6);switch(t.slice(6,7)){case"+":r="18".concat(r);break;case"-":r="19".concat(r);break;default:r="20".concat(r)}return!!m("".concat(r,"/").concat(t.slice(2,4),"/").concat(t.slice(0,2)),"YYYY/MM/DD")&&((e=parseInt(t.slice(0,6)+t.slice(7,10),10)%31)<10?e===parseInt(t.slice(10),10):["A","B","C","D","E","F","H","J","K","L","M","N","P","R","S","T","U","V","W","X","Y"][e-=10]===t.slice(10))},"fr-BE":function(t){var e,r;return!!("00"===t.slice(2,4)&&"00"===t.slice(4,6)||m("".concat(t.slice(0,2),"/").concat(t.slice(2,4),"/").concat(t.slice(4,6)),"YY/MM/DD"))&&(e=97-parseInt(t.slice(0,9),10)%97,r=parseInt(t.slice(9,11),10),e===r||97-parseInt("2".concat(t.slice(0,9)),10)%97===r)},"fr-FR":function(t){return t=t.replace(/\s/g,""),parseInt(t.slice(0,10),10)%511===parseInt(t.slice(10,13),10)},"fr-LU":function(t){if(m("".concat(t.slice(0,4),"/").concat(t.slice(4,6),"/").concat(t.slice(6,8)),"YYYY/MM/DD")&&De(t.slice(0,12))){for(var t="".concat(t.slice(0,11)).concat(t[12]),e=[[0,1,2,3,4,5,6,7,8,9],[1,2,3,4,0,6,7,8,9,5],[2,3,4,0,1,7,8,9,5,6],[3,4,0,1,2,8,9,5,6,7],[4,0,1,2,3,9,5,6,7,8],[5,9,8,7,6,0,4,3,2,1],[6,5,9,8,7,1,0,4,3,2],[7,6,5,9,8,2,1,0,4,3],[8,7,6,5,9,3,2,1,0,4],[9,8,7,6,5,4,3,2,1,0]],r=[[0,1,2,3,4,5,6,7,8,9],[1,5,7,6,2,8,3,0,9,4],[5,8,0,3,7,9,6,1,4,2],[8,9,1,6,0,4,3,5,2,7],[9,4,5,3,1,2,6,8,7,0],[4,2,8,6,5,7,3,9,0,1],[2,7,9,3,8,0,6,4,1,5],[7,0,4,6,9,1,3,2,5,8]],n=t.split("").reverse().join(""),a=0,i=0;i<n.length;i++)a=e[a][r[i%8][parseInt(n[i],10)]];return 0===a}return!1},"hr-HR":Fe,"hu-HU":function(t){for(var e=t.split("").map(function(t){return parseInt(t,10)}),r=8,n=1;n<9;n++)r+=e[n]*(n+1);return r%11===e[9]},"it-IT":function(t){var e=t.toUpperCase().split("");if(!be(e.slice(0,3)))return!1;if(!be(e.slice(3,6)))return!1;for(var r={L:"0",M:"1",N:"2",P:"3",Q:"4",R:"5",S:"6",T:"7",U:"8",V:"9"},n=0,a=[6,7,9,10,12,13,14];n<a.length;n++){var i=a[n];e[i]in r&&e.splice(i,1,r[e[i]])}var t={A:"01",B:"02",C:"03",D:"04",E:"05",H:"06",L:"07",M:"08",P:"09",R:"10",S:"11",T:"12"}[e[8]],o=parseInt(e[9]+e[10],10);if(40<o&&(o-=40),o<10&&(o="0".concat(o)),!m("".concat(e[6]).concat(e[7],"/").concat(t,"/").concat(o),"YY/MM/DD"))return!1;for(var s=0,c=1;c<e.length-1;c+=2){var u=parseInt(e[c],10);s+=u=isNaN(u)?e[c].charCodeAt(0)-65:u}for(var l={A:1,B:0,C:5,D:7,E:9,F:13,G:15,H:17,I:19,J:21,K:2,L:4,M:18,N:20,O:11,P:3,Q:6,R:8,S:12,T:14,U:16,V:10,W:22,X:25,Y:24,Z:23,0:1,1:0},d=0;d<e.length-1;d+=2){var f,A=0;e[d]in l?A=l[e[d]]:(A=2*(f=parseInt(e[d],10))+1,4<f&&(A+=2)),s+=A}return String.fromCharCode(65+s%26)===e[15]},"lv-LV":function(t){var e=(t=t.replace(/\W/,"")).slice(0,2);if("32"===e)return!0;if("00"!==t.slice(2,4)){var r=t.slice(4,6);switch(t[6]){case"0":r="18".concat(r);break;case"1":r="19".concat(r);break;default:r="20".concat(r)}if(!m("".concat(r,"/").concat(t.slice(2,4),"/").concat(e),"YYYY/MM/DD"))return!1}for(var n=1101,a=[1,6,3,7,9,10,5,8,4,2],i=0;i<t.length-1;i++)n-=parseInt(t[i],10)*a[i];return parseInt(t[10],10)===n%11},"mt-MT":function(t){if(9!==t.length){for(var e=t.toUpperCase().split("");e.length<8;)e.unshift(0);switch(t[7]){case"A":case"P":if(0===parseInt(e[6],10))return!1;break;default:var r=parseInt(e.join("").slice(0,5),10);if(32e3<r)return!1;if(r===parseInt(e.join("").slice(5,7),10))return!1}}return!0},"nl-NL":function(t){return M(t.split("").slice(0,8).map(function(t){return parseInt(t,10)}),9)%11===parseInt(t[8],10)},"pl-PL":function(t){if(10===t.length){for(var e=[6,5,7,2,3,4,5,6,7],r=0,n=0;n<e.length;n++)r+=parseInt(t[n],10)*e[n];return 10===(r%=11)?!1:r===parseInt(t[9],10)}var a=t.slice(0,2),i=parseInt(t.slice(2,4),10);if(80<i?(a="18".concat(a),i-=80):60<i?(a="22".concat(a),i-=60):40<i?(a="21".concat(a),i-=40):20<i?(a="20".concat(a),i-=20):a="19".concat(a),i<10&&(i="0".concat(i)),!m("".concat(a,"/").concat(i,"/").concat(t.slice(4,6)),"YYYY/MM/DD"))return!1;for(var o=0,s=1,c=0;c<t.length-1;c++)o+=parseInt(t[c],10)*s%10,10<(s+=2)?s=1:5===s&&(s+=2);return(o=10-o%10)===parseInt(t[10],10)},"pt-BR":function(t){if(11===t.length){var e=0;if("11111111111"===t||"22222222222"===t||"33333333333"===t||"44444444444"===t||"55555555555"===t||"66666666666"===t||"77777777777"===t||"88888888888"===t||"99999999999"===t||"00000000000"===t)return!1;for(var r=1;r<=9;r++)e+=parseInt(t.substring(r-1,r),10)*(11-r);if((o=10===(o=10*e%11)?0:o)!==parseInt(t.substring(9,10),10))return!1;e=0;for(var n=1;n<=10;n++)e+=parseInt(t.substring(n-1,n),10)*(12-n);return(o=10===(o=10*e%11)?0:o)!==parseInt(t.substring(10,11),10)?!1:!0}if("00000000000000"===t||"11111111111111"===t||"22222222222222"===t||"33333333333333"===t||"44444444444444"===t||"55555555555555"===t||"66666666666666"===t||"77777777777777"===t||"88888888888888"===t||"99999999999999"===t)return!1;for(var a=t.length-2,i=t.substring(0,a),o=t.substring(a),s=0,c=a-7,u=a;1<=u;u--)s+=i.charAt(a-u)*c,--c<2&&(c=9);if((s%11<2?0:11-s%11)!==parseInt(o.charAt(0),10))return!1;for(var i=t.substring(0,a+=1),s=0,c=a-7,l=a;1<=l;l--)s+=i.charAt(a-l)*c,--c<2&&(c=9);return(s%11<2?0:11-s%11)===parseInt(o.charAt(1),10)},"pt-PT":function(t){var e=11-M(t.split("").slice(0,8).map(function(t){return parseInt(t,10)}),9)%11;return 9<e?0===parseInt(t[8],10):e===parseInt(t[8],10)},"ro-RO":function(t){if("9000"===t.slice(0,4))return!0;var e=t.slice(1,3);switch(t[0]){case"1":case"2":e="19".concat(e);break;case"3":case"4":e="18".concat(e);break;case"5":case"6":e="20".concat(e)}var r="".concat(e,"/").concat(t.slice(3,5),"/").concat(t.slice(5,7));if(8===r.length){if(!m(r,"YY/MM/DD"))return!1}else if(!m(r,"YYYY/MM/DD"))return!1;for(var n=t.split("").map(function(t){return parseInt(t,10)}),a=[2,7,9,1,4,6,3,5,8,2,7,9],i=0,o=0;o<a.length;o++)i+=n[o]*a[o];return i%11==10?1===n[12]:n[12]===i%11},"sk-SK":function(t){if(9===t.length){if("000"===(t=t.replace(/\W/,"")).slice(6))return!1;if(53<(e=parseInt(t.slice(0,2),10)))return!1;var e=(e<10?"190":"19").concat(e),r=parseInt(t.slice(2,4),10);if(50<r&&(r-=50),r<10&&(r="0".concat(r)),!m("".concat(e,"/").concat(r,"/").concat(t.slice(4,6)),"YYYY/MM/DD"))return!1}return!0},"sl-SI":function(t){var e=11-M(t.split("").slice(0,7).map(function(t){return parseInt(t,10)}),8)%11;return 10==e?0===parseInt(t[7],10):e===parseInt(t[7],10)},"sv-SE":function(t){var e=t.slice(0),r="",n=(e=11<t.length?e.slice(2):e).slice(2,4),e=parseInt(e.slice(4,6),10);if(11<t.length)r=t.slice(0,4);else if(r=t.slice(0,2),11===t.length&&e<60){var a=(new Date).getFullYear().toString(),i=parseInt(a.slice(0,2),10),a=parseInt(a,10);if("-"===t[6])r=(parseInt("".concat(i).concat(r),10)>a?"".concat(i-1):"".concat(i)).concat(r);else if(r="".concat(i-1).concat(r),a-parseInt(r,10)<100)return!1}if(60<e&&(e-=60),e<10&&(e="0".concat(e)),8===(i="".concat(r,"/").concat(n,"/").concat(e)).length){if(!m(i,"YY/MM/DD"))return!1}else if(!m(i,"YYYY/MM/DD"))return!1;return De(t.replace(/\W/,""))},"uk-UA":function(t){for(var e=t.split("").map(function(t){return parseInt(t,10)}),r=[-1,5,7,9,4,6,10,5,7],n=0,a=0;a<r.length;a++)n+=e[a]*r[a];return n%11==10?0===e[9]:e[9]===n%11}}),E=(C["lb-LU"]=C["fr-LU"],C["lt-LT"]=C["et-EE"],C["nl-BE"]=C["fr-BE"],C["fr-CA"]=C["en-CA"],/[-\\\/!@#$%\^&\*\(\)\+\=\[\]]+/g),N={"de-AT":E,"de-DE":/[\/\\]/g,"fr-BE":E};N["nl-BE"]=N["fr-BE"];var F={"am-AM":/^(\+?374|0)(33|4[134]|55|77|88|9[13-689])\d{6}$/,"ar-AE":/^((\+?971)|0)?5[024568]\d{7}$/,"ar-BH":/^(\+?973)?(3|6)\d{7}$/,"ar-DZ":/^(\+?213|0)(5|6|7)\d{8}$/,"ar-LB":/^(\+?961)?((3|81)\d{6}|7\d{7})$/,"ar-EG":/^((\+?20)|0)?1[0125]\d{8}$/,"ar-IQ":/^(\+?964|0)?7[0-9]\d{8}$/,"ar-JO":/^(\+?962|0)?7[789]\d{7}$/,"ar-KW":/^(\+?965)([569]\d{7}|41\d{6})$/,"ar-LY":/^((\+?218)|0)?(9[1-6]\d{7}|[1-8]\d{7,9})$/,"ar-MA":/^(?:(?:\+|00)212|0)[5-7]\d{8}$/,"ar-OM":/^((\+|00)968)?([79][1-9])\d{6}$/,"ar-PS":/^(\+?970|0)5[6|9](\d{7})$/,"ar-SA":/^(!?(\+?966)|0)?5\d{8}$/,"ar-SD":/^((\+?249)|0)?(9[012369]|1[012])\d{7}$/,"ar-SY":/^(!?(\+?963)|0)?9\d{8}$/,"ar-TN":/^(\+?216)?[2459]\d{7}$/,"az-AZ":/^(\+994|0)(10|5[015]|7[07]|99)\d{7}$/,"bs-BA":/^((((\+|00)3876)|06))((([0-3]|[5-6])\d{6})|(4\d{7}))$/,"be-BY":/^(\+?375)?(24|25|29|33|44)\d{7}$/,"bg-BG":/^(\+?359|0)?8[789]\d{7}$/,"bn-BD":/^(\+?880|0)1[13456789][0-9]{8}$/,"ca-AD":/^(\+376)?[346]\d{5}$/,"cs-CZ":/^(\+?420)? ?[1-9][0-9]{2} ?[0-9]{3} ?[0-9]{3}$/,"da-DK":/^(\+?45)?\s?\d{2}\s?\d{2}\s?\d{2}\s?\d{2}$/,"de-DE":/^((\+49|0)1)(5[0-25-9]\d|6([23]|0\d?)|7([0-57-9]|6\d))\d{7,9}$/,"de-AT":/^(\+43|0)\d{1,4}\d{3,12}$/,"de-CH":/^(\+41|0)([1-9])\d{1,9}$/,"de-LU":/^(\+352)?((6\d1)\d{6})$/,"dv-MV":/^(\+?960)?(7[2-9]|9[1-9])\d{5}$/,"el-GR":/^(\+?30|0)?6(8[5-9]|9(?![26])[0-9])\d{7}$/,"el-CY":/^(\+?357?)?(9(9|7|6|5|4)\d{6})$/,"en-AI":/^(\+?1|0)264(?:2(35|92)|4(?:6[1-2]|76|97)|5(?:3[6-9]|8[1-4])|7(?:2(4|9)|72))\d{4}$/,"en-AU":/^(\+?61|0)4\d{8}$/,"en-AG":/^(?:\+1|1)268(?:464|7(?:1[3-9]|[28]\d|3[0246]|64|7[0-689]))\d{4}$/,"en-BM":/^(\+?1)?441(((3|7)\d{6}$)|(5[0-3][0-9]\d{4}$)|(59\d{5}$))/,"en-BS":/^(\+?1[-\s]?|0)?\(?242\)?[-\s]?\d{3}[-\s]?\d{4}$/,"en-GB":/^(\+?44|0)7[1-9]\d{8}$/,"en-GG":/^(\+?44|0)1481\d{6}$/,"en-GH":/^(\+233|0)(20|50|24|54|27|57|26|56|23|53|28|55|59)\d{7}$/,"en-GY":/^(\+592|0)6\d{6}$/,"en-HK":/^(\+?852[-\s]?)?[456789]\d{3}[-\s]?\d{4}$/,"en-MO":/^(\+?853[-\s]?)?[6]\d{3}[-\s]?\d{4}$/,"en-IE":/^(\+?353|0)8[356789]\d{7}$/,"en-IN":/^(\+?91|0)?[6789]\d{9}$/,"en-JM":/^(\+?876)?\d{7}$/,"en-KE":/^(\+?254|0)(7|1)\d{8}$/,"fr-CF":/^(\+?236| ?)(70|75|77|72|21|22)\d{6}$/,"en-SS":/^(\+?211|0)(9[1257])\d{7}$/,"en-KI":/^((\+686|686)?)?( )?((6|7)(2|3|8)[0-9]{6})$/,"en-KN":/^(?:\+1|1)869(?:46\d|48[89]|55[6-8]|66\d|76[02-7])\d{4}$/,"en-LS":/^(\+?266)(22|28|57|58|59|27|52)\d{6}$/,"en-MT":/^(\+?356|0)?(99|79|77|21|27|22|25)[0-9]{6}$/,"en-MU":/^(\+?230|0)?\d{8}$/,"en-MW":/^(\+?265|0)(((77|88|31|99|98|21)\d{7})|(((111)|1)\d{6})|(32000\d{4}))$/,"en-NA":/^(\+?264|0)(6|8)\d{7}$/,"en-NG":/^(\+?234|0)?[789]\d{9}$/,"en-NZ":/^(\+?64|0)[28]\d{7,9}$/,"en-PG":/^(\+?675|0)?(7\d|8[18])\d{6}$/,"en-PK":/^((00|\+)?92|0)3[0-6]\d{8}$/,"en-PH":/^(09|\+639)\d{9}$/,"en-RW":/^(\+?250|0)?[7]\d{8}$/,"en-SG":/^(\+65)?[3689]\d{7}$/,"en-SL":/^(\+?232|0)\d{8}$/,"en-TZ":/^(\+?255|0)?[67]\d{8}$/,"en-UG":/^(\+?256|0)?[7]\d{8}$/,"en-US":/^((\+1|1)?( |-)?)?(\([2-9][0-9]{2}\)|[2-9][0-9]{2})( |-)?([2-9][0-9]{2}( |-)?[0-9]{4})$/,"en-ZA":/^(\+?27|0)\d{9}$/,"en-ZM":/^(\+?26)?0[79][567]\d{7}$/,"en-ZW":/^(\+263)[0-9]{9}$/,"en-BW":/^(\+?267)?(7[1-8]{1})\d{6}$/,"es-AR":/^\+?549(11|[2368]\d)\d{8}$/,"es-BO":/^(\+?591)?(6|7)\d{7}$/,"es-CO":/^(\+?57)?3(0(0|1|2|4|5)|1\d|2[0-4]|5(0|1))\d{7}$/,"es-CL":/^(\+?56|0)[2-9]\d{1}\d{7}$/,"es-CR":/^(\+506)?[2-8]\d{7}$/,"es-CU":/^(\+53|0053)?5\d{7}$/,"es-DO":/^(\+?1)?8[024]9\d{7}$/,"es-HN":/^(\+?504)?[9|8|3|2]\d{7}$/,"es-EC":/^(\+?593|0)([2-7]|9[2-9])\d{7}$/,"es-ES":/^(\+?34)?[6|7]\d{8}$/,"es-GT":/^(\+?502)?[2|6|7]\d{7}$/,"es-PE":/^(\+?51)?9\d{8}$/,"es-MX":/^(\+?52)?(1|01)?\d{10,11}$/,"es-NI":/^(\+?505)\d{7,8}$/,"es-PA":/^(\+?507)\d{7,8}$/,"es-PY":/^(\+?595|0)9[9876]\d{7}$/,"es-SV":/^(\+?503)?[67]\d{7}$/,"es-UY":/^(\+598|0)9[1-9][\d]{6}$/,"es-VE":/^(\+?58)?(2|4)\d{9}$/,"et-EE":/^(\+?372)?\s?(5|8[1-4])\s?([0-9]\s?){6,7}$/,"fa-IR":/^(\+?98[\-\s]?|0)9[0-39]\d[\-\s]?\d{3}[\-\s]?\d{4}$/,"fi-FI":/^(\+?358|0)\s?(4[0-6]|50)\s?(\d\s?){4,8}$/,"fj-FJ":/^(\+?679)?\s?\d{3}\s?\d{4}$/,"fo-FO":/^(\+?298)?\s?\d{2}\s?\d{2}\s?\d{2}$/,"fr-BF":/^(\+226|0)[67]\d{7}$/,"fr-BJ":/^(\+229)\d{8}$/,"fr-CD":/^(\+?243|0)?(8|9)\d{8}$/,"fr-CM":/^(\+?237)6[0-9]{8}$/,"fr-FR":/^(\+?33|0)[67]\d{8}$/,"fr-GF":/^(\+?594|0|00594)[67]\d{8}$/,"fr-GP":/^(\+?590|0|00590)[67]\d{8}$/,"fr-MQ":/^(\+?596|0|00596)[67]\d{8}$/,"fr-PF":/^(\+?689)?8[789]\d{6}$/,"fr-RE":/^(\+?262|0|00262)[67]\d{8}$/,"fr-WF":/^(\+681)?\d{6}$/,"he-IL":/^(\+972|0)([23489]|5[012345689]|77)[1-9]\d{6}$/,"hu-HU":/^(\+?36|06)(20|30|31|50|70)\d{7}$/,"id-ID":/^(\+?62|0)8(1[123456789]|2[1238]|3[1238]|5[12356789]|7[78]|9[56789]|8[123456789])([\s?|\d]{5,11})$/,"ir-IR":/^(\+98|0)?9\d{9}$/,"it-IT":/^(\+?39)?\s?3\d{2} ?\d{6,7}$/,"it-SM":/^((\+378)|(0549)|(\+390549)|(\+3780549))?6\d{5,9}$/,"ja-JP":/^(\+81[ \-]?(\(0\))?|0)[6789]0[ \-]?\d{4}[ \-]?\d{4}$/,"ka-GE":/^(\+?995)?(79\d{7}|5\d{8})$/,"kk-KZ":/^(\+?7|8)?7\d{9}$/,"kl-GL":/^(\+?299)?\s?\d{2}\s?\d{2}\s?\d{2}$/,"ko-KR":/^((\+?82)[ \-]?)?0?1([0|1|6|7|8|9]{1})[ \-]?\d{3,4}[ \-]?\d{4}$/,"ky-KG":/^(\+996\s?)?(22[0-9]|50[0-9]|55[0-9]|70[0-9]|75[0-9]|77[0-9]|880|990|995|996|997|998)\s?\d{3}\s?\d{3}$/,"lt-LT":/^(\+370|8)\d{8}$/,"lv-LV":/^(\+?371)2\d{7}$/,"mg-MG":/^((\+?261|0)(2|3)\d)?\d{7}$/,"mn-MN":/^(\+|00|011)?976(77|81|88|91|94|95|96|99)\d{6}$/,"my-MM":/^(\+?959|09|9)(2[5-7]|3[1-2]|4[0-5]|6[6-9]|7[5-9]|9[6-9])[0-9]{7}$/,"ms-MY":/^(\+?60|0)1(([0145](-|\s)?\d{7,8})|([236-9](-|\s)?\d{7}))$/,"mz-MZ":/^(\+?258)?8[234567]\d{7}$/,"nb-NO":/^(\+?47)?[49]\d{7}$/,"ne-NP":/^(\+?977)?9[78]\d{8}$/,"nl-BE":/^(\+?32|0)4\d{8}$/,"nl-NL":/^(((\+|00)?31\(0\))|((\+|00)?31)|0)6{1}\d{8}$/,"nl-AW":/^(\+)?297(56|59|64|73|74|99)\d{5}$/,"nn-NO":/^(\+?47)?[49]\d{7}$/,"pl-PL":/^(\+?48)? ?([5-8]\d|45) ?\d{3} ?\d{2} ?\d{2}$/,"pt-BR":/^((\+?55\ ?[1-9]{2}\ ?)|(\+?55\ ?\([1-9]{2}\)\ ?)|(0[1-9]{2}\ ?)|(\([1-9]{2}\)\ ?)|([1-9]{2}\ ?))((\d{4}\-?\d{4})|(9[1-9]{1}\d{3}\-?\d{4}))$/,"pt-PT":/^(\+?351)?9[1236]\d{7}$/,"pt-AO":/^(\+?244)?9\d{8}$/,"ro-MD":/^(\+?373|0)((6(0|1|2|6|7|8|9))|(7(6|7|8|9)))\d{6}$/,"ro-RO":/^(\+?40|0)\s?7\d{2}(\/|\s|\.|-)?\d{3}(\s|\.|-)?\d{3}$/,"ru-RU":/^(\+?7|8)?9\d{9}$/,"si-LK":/^(?:0|94|\+94)?(7(0|1|2|4|5|6|7|8)( |-)?)\d{7}$/,"sl-SI":/^(\+386\s?|0)(\d{1}\s?\d{3}\s?\d{2}\s?\d{2}|\d{2}\s?\d{3}\s?\d{3})$/,"sk-SK":/^(\+?421)? ?[1-9][0-9]{2} ?[0-9]{3} ?[0-9]{3}$/,"so-SO":/^(\+?252|0)((6[0-9])\d{7}|(7[1-9])\d{7})$/,"sq-AL":/^(\+355|0)6[2-9]\d{7}$/,"sr-RS":/^(\+3816|06)[- \d]{5,9}$/,"sv-SE":/^(\+?46|0)[\s\-]?7[\s\-]?[02369]([\s\-]?\d){7}$/,"tg-TJ":/^(\+?992)?[5][5]\d{7}$/,"th-TH":/^(\+66|66|0)\d{9}$/,"tr-TR":/^(\+?90|0)?5\d{9}$/,"tk-TM":/^(\+993|993|8)\d{8}$/,"uk-UA":/^(\+?38)?0(50|6[36-8]|7[357]|9[1-9])\d{7}$/,"uz-UZ":/^(\+?998)?(6[125-79]|7[1-69]|88|9\d)\d{7}$/,"vi-VN":/^((\+?84)|0)((3([2-9]))|(5([25689]))|(7([0|6-9]))|(8([1-9]))|(9([0-9])))([0-9]{7})$/,"zh-CN":/^((\+|00)86)?(1[3-9]|9[28])\d{9}$/,"zh-TW":/^(\+?886\-?|0)?9\d{8}$/,"dz-BT":/^(\+?975|0)?(17|16|77|02)\d{6}$/,"ar-YE":/^(((\+|00)9677|0?7)[0137]\d{7}|((\+|00)967|0)[1-7]\d{6})$/,"ar-EH":/^(\+?212|0)[\s\-]?(5288|5289)[\s\-]?\d{5}$/,"fa-AF":/^(\+93|0)?(2{1}[0-8]{1}|[3-5]{1}[0-4]{1})(\d{7})$/,"mk-MK":/^(\+?389|0)?((?:2[2-9]\d{6}|(?:3[1-4]|4[2-8])\d{6}|500\d{5}|5[2-9]\d{6}|7[0-9][2-9]\d{5}|8[1-9]\d{6}|800\d{5}|8009\d{4}))$/};F["en-CA"]=F["en-US"],F["fr-CA"]=F["en-CA"],F["fr-BE"]=F["nl-BE"],F["zh-HK"]=F["en-HK"],F["zh-MO"]=F["en-MO"],F["ga-IE"]=F["en-IE"],F["fr-CH"]=F["de-CH"],F["it-CH"]=F["fr-CH"];var E=Object.keys(F),Ge=/^(0x)[0-9a-f]{40}$/i;var Pe={symbol:"$",require_symbol:!1,allow_space_after_symbol:!1,symbol_after_digits:!1,allow_negatives:!0,parens_for_negatives:!1,negative_sign_before_digits:!1,negative_sign_after_digits:!1,allow_negative_sign_placeholder:!1,thousands_separator:",",decimal_separator:".",allow_decimal:!0,require_decimal:!1,digits_after_decimal:[2],allow_space_after_digits:!1};var Oe=/^(bc1|tb1|bc1p|tb1p)[ac-hj-np-z02-9]{39,58}$/,He=/^(1|2|3|m)[A-HJ-NP-Za-km-z1-9]{25,39}$/;var _e=/^[A-Z]{3}(U[0-9]{7})|([J,Z][0-9]{6,7})$/,Ue=/^[0-9]$/;function we(t){if(u(t),t=t.toUpperCase(),!_e.test(t))return!1;if(11!==t.length)return!0;for(var e,r=0,n=0;n<t.length-1;n++)Ue.test(t[n])?r+=t[n]*Math.pow(2,n):r+=((e=t.charCodeAt(n)-55)<11?e:11<=e&&e<=20?12+e%11:21<=e&&e<=30?23+e%21:34+e%31)*Math.pow(2,n);var a=r%11;return 10===a&&(a=0),Number(t[t.length-1])===a}var Ke=we,ye=new Set(["aa","ab","ae","af","ak","am","an","ar","as","av","ay","az","az","ba","be","bg","bh","bi","bm","bn","bo","br","bs","ca","ce","ch","co","cr","cs","cu","cv","cy","da","de","dv","dz","ee","el","en","eo","es","et","eu","fa","ff","fi","fj","fo","fr","fy","ga","gd","gl","gn","gu","gv","ha","he","hi","ho","hr","ht","hu","hy","hz","ia","id","ie","ig","ii","ik","io","is","it","iu","ja","jv","ka","kg","ki","kj","kk","kl","km","kn","ko","kr","ks","ku","kv","kw","ky","la","lb","lg","li","ln","lo","lt","lu","lv","mg","mh","mi","mk","ml","mn","mr","ms","mt","my","na","nb","nd","ne","ng","nl","nn","no","nr","nv","ny","oc","oj","om","or","os","pa","pi","pl","ps","pt","qu","rm","rn","ro","ru","rw","sa","sc","sd","se","sg","si","sk","sl","sm","sn","so","sq","sr","ss","st","su","sv","sw","ta","te","tg","th","ti","tk","tl","tn","to","tr","ts","tt","tw","ty","ug","uk","ur","uz","ve","vi","vo","wa","wo","xh","yi","yo","za","zh","zu"]);var We=/^([\+-]?\d{4}(?!\d{2}\b))((-?)((0[1-9]|1[0-2])(\3([12]\d|0[1-9]|3[01]))?|W([0-4]\d|5[0-3])(-?[1-7])?|(00[1-9]|0[1-9]\d|[12]\d{2}|3([0-5]\d|6[1-6])))([T\s]((([01]\d|2[0-3])((:?)[0-5]\d)?|24:?00)([\.,]\d+(?!:))?)?(\17[0-5]\d([\.,]\d+)?)?([zZ]|([\+-])([01]\d|2[0-3]):?([0-5]\d)?)?)?)?$/,xe=/^([\+-]?\d{4}(?!\d{2}\b))((-?)((0[1-9]|1[0-2])(\3([12]\d|0[1-9]|3[01]))?|W([0-4]\d|5[0-3])(-?[1-7])?|(00[1-9]|0[1-9]\d|[12]\d{2}|3([0-5]\d|6[1-6])))([T]((([01]\d|2[0-3])((:?)[0-5]\d)?|24:?00)([\.,]\d+(?!:))?)?(\17[0-5]\d([\.,]\d+)?)?([zZ]|([\+-])([01]\d|2[0-3]):?([0-5]\d)?)?)?)?$/;var D=/([01][0-9]|2[0-3])/,T=/[0-5][0-9]/,b=new RegExp("[-+]".concat(D.source,":").concat(T.source)),b=new RegExp("([zZ]|".concat(b.source,")")),D=new RegExp("".concat(D.source,":").concat(T.source,":").concat(/([0-5][0-9]|60)/.source).concat(/(\.[0-9]+)?/.source)),T=new RegExp("".concat(/[0-9]{4}/.source,"-").concat(/(0[1-9]|1[0-2])/.source,"-").concat(/([12]\d|0[1-9]|3[01])/.source)),D=new RegExp("".concat(D.source).concat(b.source)),ke=new RegExp("^".concat(T.source,"[ tT]").concat(D.source,"$"));var Ye=new Set(["Adlm","Afak","Aghb","Ahom","Arab","Aran","Armi","Armn","Avst","Bali","Bamu","Bass","Batk","Beng","Bhks","Blis","Bopo","Brah","Brai","Bugi","Buhd","Cakm","Cans","Cari","Cham","Cher","Chis","Chrs","Cirt","Copt","Cpmn","Cprt","Cyrl","Cyrs","Deva","Diak","Dogr","Dsrt","Dupl","Egyd","Egyh","Egyp","Elba","Elym","Ethi","Gara","Geok","Geor","Glag","Gong","Gonm","Goth","Gran","Grek","Gujr","Gukh","Guru","Hanb","Hang","Hani","Hano","Hans","Hant","Hatr","Hebr","Hira","Hluw","Hmng","Hmnp","Hrkt","Hung","Inds","Ital","Jamo","Java","Jpan","Jurc","Kali","Kana","Kawi","Khar","Khmr","Khoj","Kitl","Kits","Knda","Kore","Kpel","Krai","Kthi","Lana","Laoo","Latf","Latg","Latn","Leke","Lepc","Limb","Lina","Linb","Lisu","Loma","Lyci","Lydi","Mahj","Maka","Mand","Mani","Marc","Maya","Medf","Mend","Merc","Mero","Mlym","Modi","Mong","Moon","Mroo","Mtei","Mult","Mymr","Nagm","Nand","Narb","Nbat","Newa","Nkdb","Nkgb","Nkoo","Nshu","Ogam","Olck","Onao","Orkh","Orya","Osge","Osma","Ougr","Palm","Pauc","Pcun","Pelm","Perm","Phag","Phli","Phlp","Phlv","Phnx","Plrd","Piqd","Prti","Psin","Qaaa","Qaab","Qaac","Qaad","Qaae","Qaaf","Qaag","Qaah","Qaai","Qaaj","Qaak","Qaal","Qaam","Qaan","Qaao","Qaap","Qaaq","Qaar","Qaas","Qaat","Qaau","Qaav","Qaaw","Qaax","Qaay","Qaaz","Qaba","Qabb","Qabc","Qabd","Qabe","Qabf","Qabg","Qabh","Qabi","Qabj","Qabk","Qabl","Qabm","Qabn","Qabo","Qabp","Qabq","Qabr","Qabs","Qabt","Qabu","Qabv","Qabw","Qabx","Ranj","Rjng","Rohg","Roro","Runr","Samr","Sara","Sarb","Saur","Sgnw","Shaw","Shrd","Shui","Sidd","Sidt","Sind","Sinh","Sogd","Sogo","Sora","Soyo","Sund","Sunu","Sylo","Syrc","Syre","Syrj","Syrn","Tagb","Takr","Tale","Talu","Taml","Tang","Tavt","Tayo","Telu","Teng","Tfng","Tglg","Thaa","Thai","Tibt","Tirh","Tnsa","Todr","Tols","Toto","Tutg","Ugar","Vaii","Visp","Vith","Wara","Wcho","Wole","Xpeo","Xsux","Yezi","Yiii","Zanb","Zinh","Zmth","Zsye","Zsym","Zxxx","Zyyy","Zzzz"]);var Ve=new Set(["AFG","ALA","ALB","DZA","ASM","AND","AGO","AIA","ATA","ATG","ARG","ARM","ABW","AUS","AUT","AZE","BHS","BHR","BGD","BRB","BLR","BEL","BLZ","BEN","BMU","BTN","BOL","BES","BIH","BWA","BVT","BRA","IOT","BRN","BGR","BFA","BDI","KHM","CMR","CAN","CPV","CYM","CAF","TCD","CHL","CHN","CXR","CCK","COL","COM","COG","COD","COK","CRI","CIV","HRV","CUB","CUW","CYP","CZE","DNK","DJI","DMA","DOM","ECU","EGY","SLV","GNQ","ERI","EST","ETH","FLK","FRO","FJI","FIN","FRA","GUF","PYF","ATF","GAB","GMB","GEO","DEU","GHA","GIB","GRC","GRL","GRD","GLP","GUM","GTM","GGY","GIN","GNB","GUY","HTI","HMD","VAT","HND","HKG","HUN","ISL","IND","IDN","IRN","IRQ","IRL","IMN","ISR","ITA","JAM","JPN","JEY","JOR","KAZ","KEN","KIR","PRK","KOR","KWT","KGZ","LAO","LVA","LBN","LSO","LBR","LBY","LIE","LTU","LUX","MAC","MKD","MDG","MWI","MYS","MDV","MLI","MLT","MHL","MTQ","MRT","MUS","MYT","MEX","FSM","MDA","MCO","MNG","MNE","MSR","MAR","MOZ","MMR","NAM","NRU","NPL","NLD","NCL","NZL","NIC","NER","NGA","NIU","NFK","MNP","NOR","OMN","PAK","PLW","PSE","PAN","PNG","PRY","PER","PHL","PCN","POL","PRT","PRI","QAT","REU","ROU","RUS","RWA","BLM","SHN","KNA","LCA","MAF","SPM","VCT","WSM","SMR","STP","SAU","SEN","SRB","SYC","SLE","SGP","SXM","SVK","SVN","SLB","SOM","ZAF","SGS","SSD","ESP","LKA","SDN","SUR","SJM","SWZ","SWE","CHE","SYR","TWN","TJK","TZA","THA","TLS","TGO","TKL","TON","TTO","TUN","TUR","TKM","TCA","TUV","UGA","UKR","ARE","GBR","USA","UMI","URY","UZB","VUT","VEN","VNM","VGB","VIR","WLF","ESH","YEM","ZMB","ZWE"]);var ze=new Set(["004","008","010","012","016","020","024","028","031","032","036","040","044","048","050","051","052","056","060","064","068","070","072","074","076","084","086","090","092","096","100","104","108","112","116","120","124","132","136","140","144","148","152","156","158","162","166","170","174","175","178","180","184","188","191","192","196","203","204","208","212","214","218","222","226","231","232","233","234","238","239","242","246","248","250","254","258","260","262","266","268","270","275","276","288","292","296","300","304","308","312","316","320","324","328","332","334","336","340","344","348","352","356","360","364","368","372","376","380","384","388","392","398","400","404","408","410","414","417","418","422","426","428","430","434","438","440","442","446","450","454","458","462","466","470","474","478","480","484","492","496","498","499","500","504","508","512","516","520","524","528","531","533","534","535","540","548","554","558","562","566","570","574","578","580","581","583","584","585","586","591","598","600","604","608","612","616","620","624","626","630","634","638","642","643","646","652","654","659","660","662","663","666","670","674","678","682","686","688","690","694","702","703","704","705","706","710","716","724","728","729","732","740","744","748","752","756","760","762","764","768","772","776","780","784","788","792","795","796","798","800","804","807","818","826","831","832","833","834","840","850","854","858","860","862","876","882","887","894"]);var Qe=new Set(["AED","AFN","ALL","AMD","ANG","AOA","ARS","AUD","AWG","AZN","BAM","BBD","BDT","BGN","BHD","BIF","BMD","BND","BOB","BOV","BRL","BSD","BTN","BWP","BYN","BZD","CAD","CDF","CHE","CHF","CHW","CLF","CLP","CNY","COP","COU","CRC","CUP","CVE","CZK","DJF","DKK","DOP","DZD","EGP","ERN","ETB","EUR","FJD","FKP","GBP","GEL","GHS","GIP","GMD","GNF","GTQ","GYD","HKD","HNL","HTG","HUF","IDR","ILS","INR","IQD","IRR","ISK","JMD","JOD","JPY","KES","KGS","KHR","KMF","KPW","KRW","KWD","KYD","KZT","LAK","LBP","LKR","LRD","LSL","LYD","MAD","MDL","MGA","MKD","MMK","MNT","MOP","MRU","MUR","MVR","MWK","MXN","MXV","MYR","MZN","NAD","NGN","NIO","NOK","NPR","NZD","OMR","PAB","PEN","PGK","PHP","PKR","PLN","PYG","QAR","RON","RSD","RUB","RWF","SAR","SBD","SCR","SDG","SEK","SGD","SHP","SLE","SLL","SOS","SRD","SSP","STN","SVC","SYP","SZL","THB","TJS","TMT","TND","TOP","TRY","TTD","TWD","TZS","UAH","UGX","USD","USN","UYI","UYU","UYW","UZS","VED","VES","VND","VUV","WST","XAF","XAG","XAU","XBA","XBB","XBC","XBD","XCD","XDR","XOF","XPD","XPF","XPT","XSU","XTS","XUA","XXX","YER","ZAR","ZMW","ZWL"]);var je=/^[A-Z2-7]+=*$/,Je=/^[A-HJKMNP-TV-Z0-9]+$/,Xe={crockford:!1};var qe=/^[A-HJ-NP-Za-km-z1-9]*$/;var tr=/^[a-z]+\/[a-z0-9\-\+\._]+$/i,er=/^[a-z\-]+=[a-z0-9\-]+$/i,rr=/^[a-z0-9!\$&'\(\)\*\+,;=\-\._~:@\/\?%\s]*$/i;var nr=/(?:^magnet:\?|[^?&]&)xt(?:\.1)?=urn:(?:(?:aich|bitprint|btih|ed2k|ed2khash|kzhash|md5|sha1|tree:tiger):[a-z0-9]{32}(?:[a-z0-9]{8})?|btmh:1220[a-z0-9]{64})(?:$|&)/i;function ar(t,e){if(u(t),e)return e=new RegExp("[".concat(e.replace(/[.*+?^${}()|[\]\\]/g,"\\$&"),"]+$"),"g"),t.replace(e,"");for(var r=t.length-1;/\s/.test(t.charAt(r));)--r;return t.slice(0,r+1)}function ir(t,e){u(t);e=e?new RegExp("^[".concat(e.replace(/[.*+?^${}()|[\]\\]/g,"\\$&"),"]+"),"g"):/^\s+/g;return t.replace(e,"")}function or(t,e){return ar(ir(t,e),e)}var sr=/^(application|audio|font|image|message|model|multipart|text|video)\/[a-zA-Z0-9\.\-\+_]{1,100}$/i,cr=/^text\/[a-zA-Z0-9\.\-\+]{1,100};\s?charset=("[a-zA-Z0-9\.\-\+\s]{0,70}"|[a-zA-Z0-9\.\-\+]{0,70})(\s?\([a-zA-Z0-9\.\-\+\s]{1,20}\))?$/i,ur=/^multipart\/[a-zA-Z0-9\.\-\+]{1,100}(;\s?(boundary|charset)=("[a-zA-Z0-9\.\-\+\s]{0,70}"|[a-zA-Z0-9\.\-\+]{0,70})(\s?\([a-zA-Z0-9\.\-\+\s]{1,20}\))?){0,2}$/i;var lr=/^\(?[+-]?(90(\.0+)?|[1-8]?\d(\.\d+)?)$/,dr=/^\s?[+-]?(180(\.0+)?|1[0-7]\d(\.\d+)?|\d{1,2}(\.\d+)?)\)?$/,fr=/^(([1-8]?\d)\D+([1-5]?\d|60)\D+([1-5]?\d|60)(\.\d+)?|90\D+0\D+0)\D+[NSns]?$/i,Ar=/^\s*([1-7]?\d{1,2}\D+([1-5]?\d|60)\D+([1-5]?\d|60)(\.\d+)?|180\D+0\D+0)\D+[EWew]?$/i,$r={checkDMS:!1};var b=/^\d{3}$/,T=/^\d{4}$/,D=/^\d{5}$/,pr=/^\d{6}$/,G={AD:/^AD\d{3}$/,AT:T,AU:T,AZ:/^AZ\d{4}$/,BA:/^([7-8]\d{4}$)/,BD:/^([1-8][0-9]{3}|9[0-4][0-9]{2})$/,BE:T,BG:T,BR:/^\d{5}-?\d{3}$/,BY:/^2[1-4]\d{4}$/,CA:/^[ABCEGHJKLMNPRSTVXY]\d[ABCEGHJ-NPRSTV-Z][\s\-]?\d[ABCEGHJ-NPRSTV-Z]\d$/i,CH:T,CN:/^(0[1-7]|1[012356]|2[0-7]|3[0-6]|4[0-7]|5[1-7]|6[1-7]|7[1-5]|8[1345]|9[09])\d{4}$/,CO:/^(05|08|11|13|15|17|18|19|20|23|25|27|41|44|47|50|52|54|63|66|68|70|73|76|81|85|86|88|91|94|95|97|99)(\d{4})$/,CZ:/^\d{3}\s?\d{2}$/,DE:D,DK:T,DO:D,DZ:D,EE:D,ES:/^(5[0-2]{1}|[0-4]{1}\d{1})\d{3}$/,FI:D,FR:/^(?:(?:0[1-9]|[1-8]\d|9[0-5])\d{3}|97[1-46]\d{2})$/,GB:/^(gir\s?0aa|[a-z]{1,2}\d[\da-z]?\s?(\d[a-z]{2})?)$/i,GR:/^\d{3}\s?\d{2}$/,HR:/^([1-5]\d{4}$)/,HT:/^HT\d{4}$/,HU:T,ID:D,IE:/^(?!.*(?:o))[A-Za-z]\d[\dw]\s\w{4}$/i,IL:/^(\d{5}|\d{7})$/,IN:/^((?!10|29|35|54|55|65|66|86|87|88|89)[1-9][0-9]{5})$/,IR:/^(?!(\d)\1{3})[13-9]{4}[1346-9][013-9]{5}$/,IS:b,IT:D,JP:/^\d{3}\-\d{4}$/,KE:D,KR:/^(\d{5}|\d{6})$/,LI:/^(948[5-9]|949[0-7])$/,LT:/^LT\-\d{5}$/,LU:T,LV:/^LV\-\d{4}$/,LK:D,MG:b,MX:D,MT:/^[A-Za-z]{3}\s{0,1}\d{4}$/,MY:D,NL:/^[1-9]\d{3}\s?(?!sa|sd|ss)[a-z]{2}$/i,NO:T,NP:/^(10|21|22|32|33|34|44|45|56|57)\d{3}$|^(977)$/i,NZ:T,PK:D,PL:/^\d{2}\-\d{3}$/,PR:/^00[679]\d{2}([ -]\d{4})?$/,PT:/^\d{4}\-\d{3}?$/,RO:pr,RU:pr,SA:D,SE:/^[1-9]\d{2}\s?\d{2}$/,SG:pr,SI:T,SK:/^\d{3}\s?\d{2}$/,TH:D,TN:T,TW:/^\d{3}(\d{2,3})?$/,UA:D,US:/^\d{5}(-\d{4})?$/,ZA:T,ZM:D},b=Object.keys(G);function hr(t,e){return u(t),t.replace(new RegExp("[".concat(e,"]+"),"g"),"")}var gr={all_lowercase:!0,gmail_lowercase:!0,gmail_remove_dots:!0,gmail_remove_subaddress:!0,gmail_convert_googlemaildotcom:!0,outlookdotcom_lowercase:!0,outlookdotcom_remove_subaddress:!0,yahoo_lowercase:!0,yahoo_remove_subaddress:!0,yandex_lowercase:!0,yandex_convert_yandexru:!0,icloud_lowercase:!0,icloud_remove_subaddress:!0},Sr=["icloud.com","me.com"],mr=["hotmail.at","hotmail.be","hotmail.ca","hotmail.cl","hotmail.co.il","hotmail.co.nz","hotmail.co.th","hotmail.co.uk","hotmail.com","hotmail.com.ar","hotmail.com.au","hotmail.com.br","hotmail.com.gr","hotmail.com.mx","hotmail.com.pe","hotmail.com.tr","hotmail.com.vn","hotmail.cz","hotmail.de","hotmail.dk","hotmail.es","hotmail.fr","hotmail.hu","hotmail.id","hotmail.ie","hotmail.in","hotmail.it","hotmail.jp","hotmail.kr","hotmail.lv","hotmail.my","hotmail.ph","hotmail.pt","hotmail.sa","hotmail.sg","hotmail.sk","live.be","live.co.uk","live.com","live.com.ar","live.com.mx","live.de","live.es","live.eu","live.fr","live.it","live.nl","msn.com","outlook.at","outlook.be","outlook.cl","outlook.co.il","outlook.co.nz","outlook.co.th","outlook.com","outlook.com.ar","outlook.com.au","outlook.com.br","outlook.com.gr","outlook.com.pe","outlook.com.tr","outlook.com.vn","outlook.cz","outlook.de","outlook.dk","outlook.es","outlook.fr","outlook.hu","outlook.id","outlook.ie","outlook.in","outlook.it","outlook.jp","outlook.kr","outlook.lv","outlook.my","outlook.ph","outlook.pt","outlook.sa","outlook.sg","outlook.sk","passport.com"],Zr=["rocketmail.com","yahoo.ca","yahoo.co.uk","yahoo.com","yahoo.de","yahoo.fr","yahoo.in","yahoo.it","ymail.com"],Er=["yandex.ru","yandex.ua","yandex.kz","yandex.com","yandex.by","ya.ru"];function Ir(t){return 1<t.length?t:""}var Rr=/^[^\s-_](?!.*?[-_]{2,})[a-z0-9-\\][^\s]*[^-_\s]$/;var P={"cs-CZ":function(t){return/^(([ABCDEFHIJKLMNPRSTUVXYZ]|[0-9])-?){5,8}$/.test(t)},"de-DE":function(t){return/^((A|AA|AB|AC|AE|AH|AK|AM|AN|AÖ|AP|AS|AT|AU|AW|AZ|B|BA|BB|BC|BE|BF|BH|BI|BK|BL|BM|BN|BO|BÖ|BS|BT|BZ|C|CA|CB|CE|CO|CR|CW|D|DA|DD|DE|DH|DI|DL|DM|DN|DO|DU|DW|DZ|E|EA|EB|ED|EE|EF|EG|EH|EI|EL|EM|EN|ER|ES|EU|EW|F|FB|FD|FF|FG|FI|FL|FN|FO|FR|FS|FT|FÜ|FW|FZ|G|GA|GC|GD|GE|GF|GG|GI|GK|GL|GM|GN|GÖ|GP|GR|GS|GT|GÜ|GV|GW|GZ|H|HA|HB|HC|HD|HE|HF|HG|HH|HI|HK|HL|HM|HN|HO|HP|HR|HS|HU|HV|HX|HY|HZ|IK|IL|IN|IZ|J|JE|JL|K|KA|KB|KC|KE|KF|KG|KH|KI|KK|KL|KM|KN|KO|KR|KS|KT|KU|KW|KY|L|LA|LB|LC|LD|LF|LG|LH|LI|LL|LM|LN|LÖ|LP|LR|LU|M|MA|MB|MC|MD|ME|MG|MH|MI|MK|ML|MM|MN|MO|MQ|MR|MS|MÜ|MW|MY|MZ|N|NB|ND|NE|NF|NH|NI|NK|NM|NÖ|NP|NR|NT|NU|NW|NY|NZ|OA|OB|OC|OD|OE|OF|OG|OH|OK|OL|OP|OS|OZ|P|PA|PB|PE|PF|PI|PL|PM|PN|PR|PS|PW|PZ|R|RA|RC|RD|RE|RG|RH|RI|RL|RM|RN|RO|RP|RS|RT|RU|RV|RW|RZ|S|SB|SC|SE|SG|SI|SK|SL|SM|SN|SO|SP|SR|ST|SU|SW|SY|SZ|TE|TF|TG|TO|TP|TR|TS|TT|TÜ|ÜB|UE|UH|UL|UM|UN|V|VB|VG|VK|VR|VS|W|WA|WB|WE|WF|WI|WK|WL|WM|WN|WO|WR|WS|WT|WÜ|WW|WZ|Z|ZE|ZI|ZP|ZR|ZW|ZZ)[- ]?[A-Z]{1,2}[- ]?\d{1,4}|(ABG|ABI|AIB|AIC|ALF|ALZ|ANA|ANG|ANK|APD|ARN|ART|ASL|ASZ|AUR|AZE|BAD|BAR|BBG|BCH|BED|BER|BGD|BGL|BID|BIN|BIR|BIT|BIW|BKS|BLB|BLK|BNA|BOG|BOH|BOR|BOT|BRA|BRB|BRG|BRK|BRL|BRV|BSB|BSK|BTF|BÜD|BUL|BÜR|BÜS|BÜZ|CAS|CHA|CLP|CLZ|COC|COE|CUX|DAH|DAN|DAU|DBR|DEG|DEL|DGF|DIL|DIN|DIZ|DKB|DLG|DON|DUD|DÜW|EBE|EBN|EBS|ECK|EIC|EIL|EIN|EIS|EMD|EMS|ERB|ERH|ERK|ERZ|ESB|ESW|FDB|FDS|FEU|FFB|FKB|FLÖ|FOR|FRG|FRI|FRW|FTL|FÜS|GAN|GAP|GDB|GEL|GEO|GER|GHA|GHC|GLA|GMN|GNT|GOA|GOH|GRA|GRH|GRI|GRM|GRZ|GTH|GUB|GUN|GVM|HAB|HAL|HAM|HAS|HBN|HBS|HCH|HDH|HDL|HEB|HEF|HEI|HER|HET|HGN|HGW|HHM|HIG|HIP|HMÜ|HOG|HOH|HOL|HOM|HOR|HÖS|HOT|HRO|HSK|HST|HVL|HWI|IGB|ILL|JÜL|KEH|KEL|KEM|KIB|KLE|KLZ|KÖN|KÖT|KÖZ|KRU|KÜN|KUS|KYF|LAN|LAU|LBS|LBZ|LDK|LDS|LEO|LER|LEV|LIB|LIF|LIP|LÖB|LOS|LRO|LSZ|LÜN|LUP|LWL|MAB|MAI|MAK|MAL|MED|MEG|MEI|MEK|MEL|MER|MET|MGH|MGN|MHL|MIL|MKK|MOD|MOL|MON|MOS|MSE|MSH|MSP|MST|MTK|MTL|MÜB|MÜR|MYK|MZG|NAB|NAI|NAU|NDH|NEA|NEB|NEC|NEN|NES|NEW|NMB|NMS|NOH|NOL|NOM|NOR|NVP|NWM|OAL|OBB|OBG|OCH|OHA|ÖHR|OHV|OHZ|OPR|OSL|OVI|OVL|OVP|PAF|PAN|PAR|PCH|PEG|PIR|PLÖ|PRÜ|QFT|QLB|RDG|REG|REH|REI|RID|RIE|ROD|ROF|ROK|ROL|ROS|ROT|ROW|RSL|RÜD|RÜG|SAB|SAD|SAN|SAW|SBG|SBK|SCZ|SDH|SDL|SDT|SEB|SEE|SEF|SEL|SFB|SFT|SGH|SHA|SHG|SHK|SHL|SIG|SIM|SLE|SLF|SLK|SLN|SLS|SLÜ|SLZ|SMÜ|SOB|SOG|SOK|SÖM|SON|SPB|SPN|SRB|SRO|STA|STB|STD|STE|STL|SUL|SÜW|SWA|SZB|TBB|TDO|TET|TIR|TÖL|TUT|UEM|UER|UFF|USI|VAI|VEC|VER|VIB|VIE|VIT|VOH|WAF|WAK|WAN|WAR|WAT|WBS|WDA|WEL|WEN|WER|WES|WHV|WIL|WIS|WIT|WIZ|WLG|WMS|WND|WOB|WOH|WOL|WOR|WOS|WRN|WSF|WST|WSW|WTL|WTM|WUG|WÜM|WUN|WUR|WZL|ZEL|ZIG)[- ]?(([A-Z][- ]?\d{1,4})|([A-Z]{2}[- ]?\d{1,3})))[- ]?(E|H)?$/.test(t)},"de-LI":function(t){return/^FL[- ]?\d{1,5}[UZ]?$/.test(t)},"en-IN":function(t){return/^[A-Z]{2}[ -]?[0-9]{1,2}(?:[ -]?[A-Z])(?:[ -]?[A-Z]*)?[ -]?[0-9]{4}$/.test(t)},"en-SG":function(t){return/^[A-Z]{3}[ -]?[\d]{4}[ -]?[A-Z]{1}$/.test(t)},"es-AR":function(t){return/^(([A-Z]{2} ?[0-9]{3} ?[A-Z]{2})|([A-Z]{3} ?[0-9]{3}))$/.test(t)},"fi-FI":function(t){return/^(?=.{4,7})(([A-Z]{1,3}|[0-9]{1,3})[\s-]?([A-Z]{1,3}|[0-9]{1,5}))$/.test(t)},"hu-HU":function(t){return/^((((?!AAA)(([A-NPRSTVZWXY]{1})([A-PR-Z]{1})([A-HJ-NPR-Z]))|(A[ABC]I)|A[ABC]O|A[A-W]Q|BPI|BPO|UCO|UDO|XAO)-(?!000)\d{3})|(M\d{6})|((CK|DT|CD|HC|H[ABEFIKLMNPRSTVX]|MA|OT|R[A-Z]) \d{2}-\d{2})|(CD \d{3}-\d{3})|(C-(C|X) \d{4})|(X-(A|B|C) \d{4})|(([EPVZ]-\d{5}))|(S A[A-Z]{2} \d{2})|(SP \d{2}-\d{2}))$/.test(t)},"pt-BR":function(t){return/^[A-Z]{3}[ -]?[0-9][A-Z][0-9]{2}|[A-Z]{3}[ -]?[0-9]{4}$/.test(t)},"pt-PT":function(t){return/^(([A-Z]{2}[ -·]?[0-9]{2}[ -·]?[0-9]{2})|([0-9]{2}[ -·]?[A-Z]{2}[ -·]?[0-9]{2})|([0-9]{2}[ -·]?[0-9]{2}[ -·]?[A-Z]{2})|([A-Z]{2}[ -·]?[0-9]{2}[ -·]?[A-Z]{2}))$/.test(t)},"sq-AL":function(t){return/^[A-Z]{2}[- ]?((\d{3}[- ]?(([A-Z]{2})|T))|(R[- ]?\d{3}))$/.test(t)},"sv-SE":function(t){return/^[A-HJ-PR-UW-Z]{3} ?[\d]{2}[A-HJ-PR-UW-Z1-9]$|(^[A-ZÅÄÖ ]{2,7}$)/.test(t.trim())},"en-PK":function(t){return/(^[A-Z]{2}((\s|-){0,1})[0-9]{3,4}((\s|-)[0-9]{2}){0,1}$)|(^[A-Z]{3}((\s|-){0,1})[0-9]{3,4}((\s|-)[0-9]{2}){0,1}$)|(^[A-Z]{4}((\s|-){0,1})[0-9]{3,4}((\s|-)[0-9]{2}){0,1}$)|(^[A-Z]((\s|-){0,1})[0-9]{4}((\s|-)[0-9]{2}){0,1}$)/.test(t.trim())}};var vr=/^[A-Z]$/,Lr=/^[a-z]$/,Mr=/^[0-9]$/,Br=/^[-#!$@£%^&*()_+|~=`{}\[\]:";'<>?,.\/\\ ]$/,Cr={minLength:8,minLowercase:1,minUppercase:1,minNumbers:1,minSymbols:1,returnScore:!1,pointsPerUnique:1,pointsPerRepeat:.5,pointsForContainingLower:10,pointsForContainingUpper:10,pointsForContainingNumber:10,pointsForContainingSymbol:10};function Nr(t){e={},Array.from(t).forEach(function(t){e[t]?e[t]+=1:e[t]=1});var e,r=e,n={length:t.length,uniqueChars:Object.keys(r).length,uppercaseCount:0,lowercaseCount:0,numberCount:0,symbolCount:0};return Object.keys(r).forEach(function(t){vr.test(t)?n.uppercaseCount+=r[t]:Lr.test(t)?n.lowercaseCount+=r[t]:Mr.test(t)?n.numberCount+=r[t]:Br.test(t)&&(n.symbolCount+=r[t])}),n}var Fr={AT:function(t){return/^(AT)?U\d{8}$/.test(t)},BE:function(t){return/^(BE)?\d{10}$/.test(t)},BG:function(t){return/^(BG)?\d{9,10}$/.test(t)},HR:function(t){return/^(HR)?\d{11}$/.test(t)},CY:function(t){return/^(CY)?\w{9}$/.test(t)},CZ:function(t){return/^(CZ)?\d{8,10}$/.test(t)},DK:function(t){return/^(DK)?\d{8}$/.test(t)},EE:function(t){return/^(EE)?\d{9}$/.test(t)},FI:function(t){return/^(FI)?\d{8}$/.test(t)},FR:function(t){return/^(FR)?\w{2}\d{9}$/.test(t)},DE:function(t){return/^(DE)?\d{9}$/.test(t)},EL:function(t){return/^(EL)?\d{9}$/.test(t)},HU:function(t){return/^(HU)?\d{8}$/.test(t)},IE:function(t){return/^(IE)?\d{7}\w{1}(W)?$/.test(t)},IT:function(t){return/^(IT)?\d{11}$/.test(t)},LV:function(t){return/^(LV)?\d{11}$/.test(t)},LT:function(t){return/^(LT)?\d{9,12}$/.test(t)},LU:function(t){return/^(LU)?\d{8}$/.test(t)},MT:function(t){return/^(MT)?\d{8}$/.test(t)},NL:function(t){return/^(NL)?\d{9}B\d{2}$/.test(t)},PL:function(t){return/^(PL)?(\d{10}|(\d{3}-\d{3}-\d{2}-\d{2})|(\d{3}-\d{2}-\d{2}-\d{3}))$/.test(t)},PT:function(t){var e,t=t.match(/^(PT)?(\d{9})$/);return!!t&&(9<(e=11-M((t=t[2]).split("").slice(0,8).map(function(t){return parseInt(t,10)}),9)%11)?0===parseInt(t[8],10):e===parseInt(t[8],10))},RO:function(t){return/^(RO)?\d{2,10}$/.test(t)},SK:function(t){return/^(SK)?\d{10}$/.test(t)},SI:function(t){return/^(SI)?\d{8}$/.test(t)},ES:function(t){return/^(ES)?\w\d{7}[A-Z]$/.test(t)},SE:function(t){return/^(SE)?\d{12}$/.test(t)},AL:function(t){return/^(AL)?\w{9}[A-Z]$/.test(t)},MK:function(t){return/^(MK)?\d{13}$/.test(t)},AU:function(t){if(!t.match(/^(AU)?(\d{11})$/))return!1;for(var e=[10,1,3,5,7,9,11,13,15,17,19],r=(t=t.replace(/^AU/,""),(parseInt(t.slice(0,1),10)-1).toString()+t.slice(1)),n=0,a=0;a<11;a++)n+=e[a]*r.charAt(a);return 0!==n&&n%89==0},BY:function(t){return/^(УНП )?\d{9}$/.test(t)},CA:function(t){return/^(CA)?\d{9}$/.test(t)},IS:function(t){return/^(IS)?\d{5,6}$/.test(t)},IN:function(t){return/^(IN)?\d{15}$/.test(t)},ID:function(t){return/^(ID)?(\d{15}|(\d{2}.\d{3}.\d{3}.\d{1}-\d{3}.\d{3}))$/.test(t)},IL:function(t){return/^(IL)?\d{9}$/.test(t)},KZ:function(t){return/^(KZ)?\d{12}$/.test(t)},NZ:function(t){return/^(NZ)?\d{9}$/.test(t)},NG:function(t){return/^(NG)?(\d{12}|(\d{8}-\d{4}))$/.test(t)},NO:function(t){return/^(NO)?\d{9}MVA$/.test(t)},PH:function(t){return/^(PH)?(\d{12}|\d{3} \d{3} \d{3} \d{3})$/.test(t)},RU:function(t){return/^(RU)?(\d{10}|\d{12})$/.test(t)},SM:function(t){return/^(SM)?\d{5}$/.test(t)},SA:function(t){return/^(SA)?\d{15}$/.test(t)},RS:function(t){return/^(RS)?\d{9}$/.test(t)},CH:function(t){var e,n;return/^(CHE[- ]?)?(\d{9}|(\d{3}\.\d{3}\.\d{3})|(\d{3} \d{3} \d{3})) ?(TVA|MWST|IVA)?$/.test(t)&&(t=t.match(/\d/g).map(function(t){return+t}),e=t.pop(),n=[5,4,3,2,7,6,5,4],e===(11-t.reduce(function(t,e,r){return t+e*n[r]},0)%11)%11)},TR:function(t){return/^(TR)?\d{10}$/.test(t)},UA:function(t){return/^(UA)?\d{12}$/.test(t)},GB:function(t){return/^GB((\d{3} \d{4} ([0-8][0-9]|9[0-6]))|(\d{9} \d{3})|(((GD[0-4])|(HA[5-9]))[0-9]{2}))$/.test(t)},UZ:function(t){return/^(UZ)?\d{9}$/.test(t)},AR:function(t){return/^(AR)?\d{11}$/.test(t)},BO:function(t){return/^(BO)?\d{7}$/.test(t)},BR:function(t){return/^(BR)?((\d{2}.\d{3}.\d{3}\/\d{4}-\d{2})|(\d{3}.\d{3}.\d{3}-\d{2}))$/.test(t)},CL:function(t){return/^(CL)?\d{8}-\d{1}$/.test(t)},CO:function(t){return/^(CO)?\d{10}$/.test(t)},CR:function(t){return/^(CR)?\d{9,12}$/.test(t)},EC:function(t){return/^(EC)?\d{13}$/.test(t)},SV:function(t){return/^(SV)?\d{4}-\d{6}-\d{3}-\d{1}$/.test(t)},GT:function(t){return/^(GT)?\d{7}-\d{1}$/.test(t)},HN:function(t){return/^(HN)?$/.test(t)},MX:function(t){return/^(MX)?\w{3,4}\d{6}\w{3}$/.test(t)},NI:function(t){return/^(NI)?\d{3}-\d{6}-\d{4}\w{1}$/.test(t)},PA:function(t){return/^(PA)?$/.test(t)},PY:function(t){return/^(PY)?\d{6,8}-\d{1}$/.test(t)},PE:function(t){return/^(PE)?\d{11}$/.test(t)},DO:function(t){return/^(DO)?(\d{11}|(\d{3}-\d{7}-\d{1})|[1,4,5]{1}\d{8}|([1,4,5]{1})-\d{2}-\d{5}-\d{1})$/.test(t)},UY:function(t){return/^(UY)?\d{12}$/.test(t)},VE:function(t){return/^(VE)?[J,G,V,E]{1}-(\d{9}|(\d{8}-\d{1}))$/.test(t)}};return{version:"13.15.15",toDate:r,toFloat:j,toInt:function(t,e){return u(t),parseInt(t,e||10)},toBoolean:function(t,e){return u(t),e?"1"===t||/^true$/i.test(t):"0"!==t&&!/^false$/i.test(t)&&""!==t},equals:function(t,e){return u(t),t===e},contains:function(t,e,r){return u(t),(r=f(r,rt)).ignoreCase?t.toLowerCase().split(et(e).toLowerCase()).length>r.minOccurrences:t.split(et(e)).length>r.minOccurrences},matches:function(t,e,r){return u(t),"[object RegExp]"!==Object.prototype.toString.call(e)&&(e=new RegExp(e,r)),!!t.match(e)},isEmail:$t,isURL:function(t,e){if(u(t),!t||/[\s<>]/.test(t))return!1;if(0===t.indexOf("mailto:"))return!1;if((e=f(e,pt)).validate_length&&t.length>e.max_allowed_length)return!1;if(!e.allow_fragments&&S(t,"#"))return!1;if(!e.allow_query_components&&(S(t,"?")||S(t,"&")))return!1;var r,n=t.split("#");if(1<(n=(t=(n=(t=n.shift()).split("?")).shift()).split("://")).length){if(i=n.shift().toLowerCase(),e.require_valid_protocol&&-1===e.protocols.indexOf(i))return!1}else{if(e.require_protocol)return!1;if("//"===t.slice(0,2)){if(!e.allow_protocol_relative_urls)return!1;n[0]=t.slice(2)}}if(""===(t=n.join("://")))return!1;if(""===(t=(n=t.split("/")).shift())&&!e.require_host)return!0;if(1<(n=t.split("@")).length){if(e.disallow_auth)return!1;if(""===n[0])return!1;if(0<=(i=n.shift()).indexOf(":")&&2<i.split(":").length)return!1;t=d(i.split(":"),2);if(""===t[0]&&""===t[1])return!1}var a,i=null,t=null,o=(a=n.join("@")).match(ht);if(o?(r="",t=o[1],i=o[2]||null):(r=(n=a.split(":")).shift(),n.length&&(i=n.join(":"))),null!==i&&0<i.length){if(o=parseInt(i,10),!/^[0-9]+$/.test(i)||o<=0||65535<o)return!1}else if(e.require_port)return!1;return e.host_whitelist?A(r,e.host_whitelist):""===r&&!e.require_host||!!(g(r)||at(r,e)||t&&g(t,6))&&(r=r||t,!e.host_blacklist||!A(r,e.host_blacklist))},isMACAddress:function t(e,r){return u(e),null!=r&&r.eui&&(r.eui=String(r.eui)),null!=r&&r.no_colons||null!=r&&r.no_separators?"48"===r.eui?St.test(e):"64"!==r.eui&&St.test(e)||Et.test(e):"48"===(null==r?void 0:r.eui)?gt.test(e)||mt.test(e):"64"===(null==r?void 0:r.eui)?Zt.test(e)||It.test(e):t(e,{eui:"48"})||t(e,{eui:"64"})},isIP:g,isIPRange:function(t){var e=1<arguments.length&&void 0!==arguments[1]?arguments[1]:"",r=(u(t),t.split("/"));if(2!==r.length)return!1;if(!Rt.test(r[1]))return!1;if(1<r[1].length&&r[1].startsWith("0"))return!1;if(!g(r[0],e))return!1;var n=null;switch(String(e)){case"4":n=32;break;case"6":n=128;break;default:n=g(r[0],"6")?128:32}return r[1]<=n&&0<=r[1]},isFQDN:at,isBoolean:function(t){var e=1<arguments.length&&void 0!==arguments[1]?arguments[1]:Bt;return u(t),e.loose?Z(Nt,t.toLowerCase()):Z(Ct,t)},isIBAN:function(t){var e=1<arguments.length&&void 0!==arguments[1]?arguments[1]:{};return u(t),oe(t,e)&&1===((e=(e=t).replace(/[^A-Z0-9]+/gi,"").toUpperCase()).slice(4)+e.slice(0,4)).replace(/[A-Z]/g,function(t){return t.charCodeAt(0)-55}).match(/\d{1,7}/g).reduce(function(t,e){return Number(t+e)%97},"")},isBIC:function(t){u(t);var e=t.slice(4,6).toUpperCase();return!(!ce.has(e)&&"XK"!==e)&&ue.test(t)},isAbaRouting:function(t){if(u(t),!Tt.test(t))return!1;for(var e=0,r=0;r<t.length;r++)e+=r%3==0?3*t[r]:r%3==1?7*t[r]:+t[r];return e%10==0},isAlpha:function(t){var e=1<arguments.length&&void 0!==arguments[1]?arguments[1]:"en-US",r=2<arguments.length&&void 0!==arguments[2]?arguments[2]:{};if(u(t),r=r.ignore)if(r instanceof RegExp)t=t.replace(r,"");else{if("string"!=typeof r)throw new Error("ignore should be instance of a String or RegExp");t=t.replace(new RegExp("[".concat(r.replace(/[-[\]{}()*+?.,\\^$|#\\s]/g,"\\$&"),"]"),"g"),"")}if(e in n)return n[e].test(t);throw new Error("Invalid locale '".concat(e,"'"))},isAlphaLocales:h,isAlphanumeric:function(t){var e=1<arguments.length&&void 0!==arguments[1]?arguments[1]:"en-US",r=2<arguments.length&&void 0!==arguments[2]?arguments[2]:{};if(u(t),r=r.ignore)if(r instanceof RegExp)t=t.replace(r,"");else{if("string"!=typeof r)throw new Error("ignore should be instance of a String or RegExp");t=t.replace(new RegExp("[".concat(r.replace(/[-[\]{}()*+?.,\\^$|#\\s]/g,"\\$&"),"]"),"g"),"")}if(e in a)return a[e].test(t);throw new Error("Invalid locale '".concat(e,"'"))},isAlphanumericLocales:I,isNumeric:function(t,e){return u(t),(e&&e.no_symbols?bt:new RegExp("^[+-]?([0-9]*[".concat((e||{}).locale?i[e.locale]:".","])?[0-9]+$"))).test(t)},isPassportNumber:function(t,e){return u(t),t=t.replace(/\s/g,"").toUpperCase(),e.toUpperCase()in Gt&&Gt[e].test(t)},passportNumberLocales:Ft,isPort:function(t){return Ht(t,{allow_leading_zeroes:!1,min:0,max:65535})},isLowercase:function(t){return u(t),t===t.toLowerCase()},isUppercase:function(t){return u(t),t===t.toUpperCase()},isAscii:function(t){return u(t),wt.test(t)},isFullWidth:function(t){return u(t),Kt.test(t)},isHalfWidth:function(t){return u(t),yt.test(t)},isVariableWidth:function(t){return u(t),Kt.test(t)&&yt.test(t)},isMultibyte:function(t){return u(t),Wt.test(t)},isSemVer:function(t){return u(t),xt.test(t)},isSurrogatePair:function(t){return u(t),kt.test(t)},isInt:Ht,isIMEI:function(t,e){u(t);var r=_t;if(!(r=(e=e||{}).allow_hyphens?Ut:r).test(t))return!1;t=t.replace(/-/g,"");for(var n=0,a=2,i=0;i<14;i++){var o=t.substring(14-i-1,14-i),o=parseInt(o,10)*a;n+=10<=o?o%10+1:o,1===a?a+=1:--a}return(10-n%10)%10===parseInt(t.substring(14,15),10)},isFloat:z,isFloatLocales:Q,isDecimal:function(t,e){if(u(t),(e=f(e,Yt)).locale in i)return!Z(Vt,t.replace(/ /g,""))&&(r=e,new RegExp("^[-+]?([0-9]+)?(\\".concat(i[r.locale],"[0-9]{").concat(r.decimal_digits,"})").concat(r.force_decimal?"":"?","$")).test(t));var r;throw new Error("Invalid locale '".concat(e.locale,"'"))},isHexadecimal:Qt,isOctal:function(t){return u(t),jt.test(t)},isDivisibleBy:function(t,e){return u(t),j(t)%parseInt(e,10)==0},isHexColor:function(t){return u(t),Jt.test(t)},isRgbColor:function(t,e){u(t);var r=!1,n=!0;if("object"!==l(e)?2<=arguments.length&&(n=e):(r=void 0!==e.allowSpaces?e.allowSpaces:r,n=void 0!==e.includePercentValues?e.includePercentValues:n),r){if(!re.test(t))return!1;t=t.replace(/\s/g,"")}return n?Xt.test(t)||qt.test(t)||te.test(t)||ee.test(t):Xt.test(t)||qt.test(t)},isHSL:function(t){return u(t),(-1!==(t=t.replace(/\s+/g," ").replace(/\s?(hsla?\(|\)|,)\s?/gi,"$1")).indexOf(",")?ne:ae).test(t)},isISRC:function(t){return u(t),ie.test(t)},isMD5:function(t){return u(t),le.test(t)},isHash:function(t,e){return u(t),new RegExp("^[a-fA-F0-9]{".concat(de[e],"}$")).test(t)},isJWT:function(t){return u(t),3===(t=t.split(".")).length&&t.reduce(function(t,e){return t&&he(e,{urlSafe:!0})},!0)},isJSON:function(t,e){u(t);try{e=f(e,ge);var r=[],n=(e.allow_primitives&&(r=[null,!1,!0]),JSON.parse(t));return Z(r,n)||!!n&&"object"===l(n)}catch(t){}return!1},isEmpty:function(t,e){return u(t),0===((e=f(e,Se)).ignore_whitespace?t.trim():t).length},isLength:function(t,e){u(t),n="object"===l(e)?(r=e.min||0,e.max):(r=e||0,arguments[2]);var r,n,a=t.match(/(\uFE0F|\uFE0E)/g)||[],i=t.match(/[\uD800-\uDBFF][\uDC00-\uDFFF]/g)||[],o=t.length-a.length-i.length,t=r<=o&&(void 0===n||o<=n);return t&&Array.isArray(null==e?void 0:e.discreteLengths)?e.discreteLengths.some(function(t){return t===o}):t},isLocale:function(t){return u(t),Dt.test(t)},isByteLength:$,isULID:function(t){return u(t),/^[0-7][0-9A-HJKMNP-TV-Z]{25}$/i.test(t)},isUUID:function(t,e){return u(t),(e=null==e?"all":e)in me&&me[e].test(t)},isMongoId:function(t){return u(t),Qt(t)&&24===t.length},isAfter:function(t,e){return e=r(("object"===l(e)?e.comparisonDate:e)||Date().toString()),!!((t=r(t))&&e&&e<t)},isBefore:function(t,e){return e=r(("object"===l(e)?e.comparisonDate:e)||Date().toString()),!!((t=r(t))&&e&&t<e)},isIn:function(t,e){if(u(t),"[object Array]"!==Object.prototype.toString.call(e))return"object"===l(e)?e.hasOwnProperty(t):!(!e||"function"!=typeof e.indexOf)&&0<=e.indexOf(t);var r,n=[];for(r in e)!{}.hasOwnProperty.call(e,r)||(n[r]=et(e[r]));return 0<=n.indexOf(t)},isLuhnNumber:Ze,isCreditCard:function(t){var e=1<arguments.length&&void 0!==arguments[1]?arguments[1]:{},e=(u(t),e.provider),r=t.replace(/[- ]+/g,"");if(e&&e.toLowerCase()in v){if(!v[e.toLowerCase()].test(r))return!1}else{if(e&&!(e.toLowerCase()in v))throw new Error("".concat(e," is not a valid credit card provider."));if(!Ee.some(function(t){return t.test(r)}))return!1}return Ze(t)},isIdentityCard:function(t,e){if(u(t),e in L)return L[e](t);if("any"!==e)throw new Error("Invalid locale '".concat(e,"'"));for(var r in L)if(L.hasOwnProperty(r))if((0,L[r])(t))return!0;return!1},isEAN:function(t){u(t);var e=Number(t.slice(-1));return ve.test(t)&&e===Le(t)},isISIN:function(t){if(u(t),!Me.test(t))return!1;for(var e=!0,r=0,n=t.length-2;0<=n;n--)if("A"<=t[n]&&t[n]<="Z")for(var a=t[n].charCodeAt(0)-55,i=0,o=[a%10,Math.trunc(a/10)];i<o.length;i++){var s=o[i];r+=e?5<=s?1+2*(s-5):2*s:s,e=!e}else{a=t[n].charCodeAt(0)-"0".charCodeAt(0);r+=e?5<=a?1+2*(a-5):2*a:a,e=!e}var c=10*Math.trunc((r+9)/10)-r;return+t[t.length-1]==c},isISBN:function t(e,r){u(e);var n=String((null==r?void 0:r.version)||r);if(!(null!=r&&r.version||r))return t(e,{version:10})||t(e,{version:13});var a=e.replace(/[\s-]+/g,""),i=0;if("10"===n){if(!Be.test(a))return!1;for(var o=0;o<n-1;o++)i+=(o+1)*a.charAt(o);if("X"===a.charAt(9)?i+=100:i+=10*a.charAt(9),i%11==0)return!0}else if("13"===n){if(!Ce.test(a))return!1;for(var s=0;s<12;s++)i+=Ne[s%2]*a.charAt(s);if(a.charAt(12)-(10-i%10)%10==0)return!0}return!1},isISSN:function(t){var e=1<arguments.length&&void 0!==arguments[1]?arguments[1]:{},r=(u(t),"^\\d{4}-?\\d{3}[\\dX]$"),r=e.require_hyphen?r.replace("?",""):r;if(!(r=e.case_sensitive?new RegExp(r):new RegExp(r,"i")).test(t))return!1;for(var n=t.replace("-","").toUpperCase(),a=0,i=0;i<n.length;i++){var o=n[i];a+=("X"===o?10:+o)*(8-i)}return a%11==0},isMobilePhone:function(e,t,r){if(u(e),!r||!r.strictMode||e.startsWith("+")){if(Array.isArray(t))return t.some(function(t){if(F.hasOwnProperty(t)&&F[t].test(e))return!0;return!1});if(t in F)return F[t].test(e);if(t&&"any"!==t)throw new Error("Invalid locale '".concat(t,"'"));for(var n in F)if(F.hasOwnProperty(n))if(F[n].test(e))return!0}return!1},isMobilePhoneLocales:E,isPostalCode:function(t,e){if(u(t),e in G)return G[e].test(t);if("any"!==e)throw new Error("Invalid locale '".concat(e,"'"));for(var r in G)if(G.hasOwnProperty(r))if(G[r].test(t))return!0;return!1},isPostalCodeLocales:b,isEthereumAddress:function(t){return u(t),Ge.test(t)},isCurrency:function(t,e){return u(t),e=e=f(e,Pe),r="\\d{".concat(e.digits_after_decimal[0],"}"),e.digits_after_decimal.forEach(function(t,e){0!==e&&(r="".concat(r,"|\\d{").concat(t,"}"))}),n="(".concat(e.symbol.replace(/\W/,function(t){return"\\".concat(t)}),")").concat(e.require_symbol?"":"?"),a=["0","[1-9]\\d*","[1-9]\\d{0,2}(\\".concat(e.thousands_separator,"\\d{3})*")],a="(".concat(a.join("|"),")?"),i="(\\".concat(e.decimal_separator,"(").concat(r,"))").concat(e.require_decimal?"":"?"),a+=e.allow_decimal||e.require_decimal?i:"",e.allow_negatives&&!e.parens_for_negatives&&(e.negative_sign_after_digits?a+="-?":e.negative_sign_before_digits&&(a="-?"+a)),e.allow_negative_sign_placeholder?a="( (?!\\-))?".concat(a):e.allow_space_after_symbol?a=" ?".concat(a):e.allow_space_after_digits&&(a+="( (?!$))?"),e.symbol_after_digits?a+=n:a=n+a,e.allow_negatives&&(e.parens_for_negatives?a="(\\(".concat(a,"\\)|").concat(a,")"):e.negative_sign_before_digits||e.negative_sign_after_digits||(a="-?"+a)),new RegExp("^(?!-? )(?=.*\\d)".concat(a,"$")).test(t);var r,n,a,i},isBtcAddress:function(t){return u(t),Oe.test(t)||He.test(t)},isISO6346:we,isFreightContainerID:Ke,isISO6391:function(t){return u(t),ye.has(t)},isISO8601:function(t){var e,r,n,a=1<arguments.length&&void 0!==arguments[1]?arguments[1]:{},i=(u(t),(a.strictSeparator?xe:We).test(t));return i&&a.strict?(t=(a=t).match(/^(\d{4})-?(\d{3})([ T]{1}\.*|$)/))?(e=Number(t[1]),t=Number(t[2]),e%4==0&&e%100!=0||e%400==0?t<=366:t<=365):(t=(e=a.match(/(\d{4})-?(\d{0,2})-?(\d*)/).map(Number))[1],a=e[3],n=(e=e[2])&&"0".concat(e).slice(-2),r=a&&"0".concat(a).slice(-2),n=new Date("".concat(t,"-").concat(n||"01","-").concat(r||"01")),!e||!a||n.getUTCFullYear()===t&&n.getUTCMonth()+1===e&&n.getUTCDate()===a):i},isISO15924:function(t){return u(t),Ye.has(t)},isRFC3339:function(t){return u(t),ke.test(t)},isISO31661Alpha2:function(t){return u(t),se.has(t.toUpperCase())},isISO31661Alpha3:function(t){return u(t),Ve.has(t.toUpperCase())},isISO31661Numeric:function(t){return u(t),ze.has(t)},isISO4217:function(t){return u(t),Qe.has(t.toUpperCase())},isBase32:function(t,e){return u(t),(e=f(e,Xe)).crockford?Je.test(t):!(t.length%8!=0||!je.test(t))},isBase58:function(t){return u(t),!!qe.test(t)},isBase64:he,isDataURI:function(t){u(t);var e=t.split(",");if(e.length<2)return!1;var r=e.shift().trim().split(";");if("data:"!==(t=r.shift()).slice(0,5))return!1;if(""!==(t=t.slice(5))&&!tr.test(t))return!1;for(var n=0;n<r.length;n++)if((n!==r.length-1||"base64"!==r[n].toLowerCase())&&!er.test(r[n]))return!1;for(var a=0;a<e.length;a++)if(!rr.test(e[a]))return!1;return!0},isMagnetURI:function(t){return u(t),0===t.indexOf("magnet:?")&&nr.test(t)},isMailtoURI:function(t,e){var r;return u(t),0===t.indexOf("mailto:")&&(r=(t=d(t.replace("mailto:","").split("?"),2))[0],t=void 0===(t=t[1])?"":t,!r&&!t||!!(t=(t=>{var e=new Set(["subject","body","cc","bcc"]),r={cc:"",bcc:""},n=!1;if(4<(t=t.split("&")).length)return!1;var a,i=X(t);try{for(i.s();!(a=i.n()).done;){var o=d(a.value.split("="),2),s=o[0],c=o[1];if(s&&!e.has(s)){n=!0;break}!c||"cc"!==s&&"bcc"!==s||(r[s]=c),s&&e.delete(s)}}catch(t){i.e(t)}finally{i.f()}return!n&&r})(t))&&"".concat(r,",").concat(t.cc,",").concat(t.bcc).split(",").every(function(t){return!(t=or(t," "))||$t(t,e)}))},isMimeType:function(t){return u(t),sr.test(t)||cr.test(t)||ur.test(t)},isLatLong:function(t,e){return u(t),e=f(e,$r),!!S(t,",")&&!((t=t.split(","))[0].startsWith("(")&&!t[1].endsWith(")")||t[1].endsWith(")")&&!t[0].startsWith("("))&&(e.checkDMS?fr.test(t[0])&&Ar.test(t[1]):lr.test(t[0])&&dr.test(t[1]))},ltrim:ir,rtrim:ar,trim:or,escape:function(t){return u(t),t.replace(/&/g,"&amp;").replace(/"/g,"&quot;").replace(/'/g,"&#x27;").replace(/</g,"&lt;").replace(/>/g,"&gt;").replace(/\//g,"&#x2F;").replace(/\\/g,"&#x5C;").replace(/`/g,"&#96;")},unescape:function(t){return u(t),t.replace(/&quot;/g,'"').replace(/&#x27;/g,"'").replace(/&lt;/g,"<").replace(/&gt;/g,">").replace(/&#x2F;/g,"/").replace(/&#x5C;/g,"\\").replace(/&#96;/g,"`").replace(/&amp;/g,"&")},stripLow:function(t,e){return u(t),hr(t,e?"\\x00-\\x09\\x0B\\x0C\\x0E-\\x1F\\x7F":"\\x00-\\x1F\\x7F")},whitelist:function(t,e){return u(t),t.replace(new RegExp("[^".concat(e,"]+"),"g"),"")},blacklist:hr,isWhitelisted:function(t,e){u(t);for(var r=t.length-1;0<=r;r--)if(-1===e.indexOf(t[r]))return!1;return!0},normalizeEmail:function(t,e){e=f(e,gr);var r=(t=t.split("@")).pop();if((t=[t.join("@"),r])[1]=t[1].toLowerCase(),"gmail.com"===t[1]||"googlemail.com"===t[1]){if(e.gmail_remove_subaddress&&(t[0]=t[0].split("+")[0]),e.gmail_remove_dots&&(t[0]=t[0].replace(/\.+/g,Ir)),!t[0].length)return!1;(e.all_lowercase||e.gmail_lowercase)&&(t[0]=t[0].toLowerCase()),t[1]=e.gmail_convert_googlemaildotcom?"gmail.com":t[1]}else if(0<=Sr.indexOf(t[1])){if(e.icloud_remove_subaddress&&(t[0]=t[0].split("+")[0]),!t[0].length)return!1;(e.all_lowercase||e.icloud_lowercase)&&(t[0]=t[0].toLowerCase())}else if(0<=mr.indexOf(t[1])){if(e.outlookdotcom_remove_subaddress&&(t[0]=t[0].split("+")[0]),!t[0].length)return!1;(e.all_lowercase||e.outlookdotcom_lowercase)&&(t[0]=t[0].toLowerCase())}else if(0<=Zr.indexOf(t[1])){if(e.yahoo_remove_subaddress&&(r=t[0].split("-"),t[0]=1<r.length?r.slice(0,-1).join("-"):r[0]),!t[0].length)return!1;(e.all_lowercase||e.yahoo_lowercase)&&(t[0]=t[0].toLowerCase())}else 0<=Er.indexOf(t[1])?((e.all_lowercase||e.yandex_lowercase)&&(t[0]=t[0].toLowerCase()),t[1]=e.yandex_convert_yandexru?"yandex.ru":t[1]):e.all_lowercase&&(t[0]=t[0].toLowerCase());return t.join("@")},toString:toString,isSlug:function(t){return u(t),Rr.test(t)},isStrongPassword:function(t){var e,r,n,a=1<arguments.length&&void 0!==arguments[1]?arguments[1]:null,t=(u(t),Nr(t));return(a=f(a||{},Cr)).returnScore?(r=a,n=0,n=(n+=(e=t).uniqueChars*r.pointsPerUnique)+(e.length-e.uniqueChars)*r.pointsPerRepeat,0<e.lowercaseCount&&(n+=r.pointsForContainingLower),0<e.uppercaseCount&&(n+=r.pointsForContainingUpper),0<e.numberCount&&(n+=r.pointsForContainingNumber),0<e.symbolCount&&(n+=r.pointsForContainingSymbol),n):t.length>=a.minLength&&t.lowercaseCount>=a.minLowercase&&t.uppercaseCount>=a.minUppercase&&t.numberCount>=a.minNumbers&&t.symbolCount>=a.minSymbols},isTaxID:function(t){var e=1<arguments.length&&void 0!==arguments[1]?arguments[1]:"en-US",t=(u(t),t.slice(0));if(e in B)return e in N&&(t=t.replace(N[e],"")),!!B[e].test(t)&&(!(e in C)||C[e](t));throw new Error("Invalid locale '".concat(e,"'"))},isDate:m,isTime:function(t,e){return e=f(e,Lt),"string"==typeof t&&Mt[e.hourFormat][e.mode].test(t)},isLicensePlate:function(t,e){if(u(t),e in P)return P[e](t);if("any"!==e)throw new Error("Invalid locale '".concat(e,"'"));for(var r in P)if((0,P[r])(t))return!0;return!1},isVAT:function(t,e){if(u(t),u(e),e in Fr)return Fr[e](t);throw new Error("Invalid country code: '".concat(e,"'"))},ibanLocales:p}});