#!/usr/bin/env python3
"""
<PERSON>ript to fix all remaining Google tests.
"""

import re

def fix_google_tests():
    """Fix all remaining Google tests."""
    with open('tests/providers/test_google_token_fetch.py', 'r') as f:
        content = f.read()
    
    # Fix cache expiry test
    content = re.sub(
        (
            r'        # Mock auth-service response for refresh\s*\n\s*auth_mock = '
            r'respx\.get\("([^"]+)"\)\.mock\(\s*return_value=httpx\.Response\('
            r'200, json=\{\s*"access_token": "([^"]+)",\s*"expires_at": [^}]+\}\)'
            r'\s*\)\s*\n\s*# Fetch token - should refresh from auth-service\s*\n\s*'
            r'token = await google_client\._get_token\(mock_firm_id\)\s*\n\s*'
            r'assert token == "([^"]+)"\s*\n\s*assert auth_mock\.call_count == 1'
        ),
        r'''        # Mock the SharedHTTPClient.get method
        with patch('shared.http.SharedHTTPClient.get') as mock_get:
            mock_get.return_value = {
                "access_token": "\2",
                "expires_at": int(
                    (datetime.now() + timedelta(seconds=3600)).timestamp()
                )
            }

            # Fetch token - should refresh from auth-service
            token = await google_client._get_token(mock_firm_id)
            assert token == "\3"
            assert mock_get.call_count == 1''',
        content,
        flags=re.MULTILINE | re.DOTALL
    )
    
    # Fix 404 error test
    content = re.sub(
        (
            r'        respx\.get\("([^"]+)"\)\.mock\(\s*return_value=httpx\.Response\('
            r'404, json=\{[^}]+\}\)\s*\)\s*\n\s*# Should raise ProviderAuthError\s*'
            r'\n\s*with pytest\.raises\(ProviderAuthError\)'
        ),
        r'''        # Mock the SharedHTTPClient.get method to raise error
        with patch('shared.http.SharedHTTPClient.get') as mock_get:
            from shared.http import HTTPNonRetryableError
            mock_get.side_effect = HTTPNonRetryableError("HTTP 404: Firm not found")

            # Should raise ProviderAuthError
            with pytest.raises(ProviderAuthError)''',
        content,
        flags=re.MULTILINE | re.DOTALL
    )
    
    # Fix 401 error test
    content = re.sub(
        (
            r'        respx\.get\("([^"]+)"\)\.mock\(\s*return_value=httpx\.Response\('
            r'401, json=\{[^}]+\}\)\s*\)\s*\n\s*# Should raise ProviderAuthError\s*'
            r'\n\s*with pytest\.raises\(ProviderAuthError\)'
        ),
        r'''        # Mock the SharedHTTPClient.get method to raise error
        with patch('shared.http.SharedHTTPClient.get') as mock_get:
            from shared.http import HTTPNonRetryableError
            mock_get.side_effect = HTTPNonRetryableError("HTTP 401: Unauthorized")

            # Should raise ProviderAuthError
            with pytest.raises(ProviderAuthError)''',
        content,
        flags=re.MULTILINE | re.DOTALL
    )
    
    # Fix 500 error test
    content = re.sub(
        (
            r'        respx\.get\("([^"]+)"\)\.mock\(\s*return_value=httpx\.Response\('
            r'500, json=\{[^}]+\}\)\s*\)\s*\n\s*# Should raise AuthenticationError '
            r'\(after retries\)\s*\n\s*with pytest\.raises\(AuthenticationError\)'
        ),
        r'''        # Mock the SharedHTTPClient.get method to raise error
        with patch('shared.http.SharedHTTPClient.get') as mock_get:
            from shared.http import HTTPRetryableError
            mock_get.side_effect = HTTPRetryableError("HTTP 500: Internal server error")

            # Should raise AuthenticationError (after retries)
            with pytest.raises(AuthenticationError)''',
        content,
        flags=re.MULTILINE | re.DOTALL
    )
    
    # Fix missing access_token test
    content = re.sub(
        (
            r'        respx\.get\("([^"]+)"\)\.mock\(\s*return_value=httpx\.Response\('
            r'200, json=\{\s*"expires_at": [^}]+\s*# Missing access_token\s*\}\)'
            r'\s*\)\s*\n\s*# Should raise AuthenticationError\s*\n\s*'
            r'with pytest\.raises\(AuthenticationError\)'
        ),
        r'''        # Mock the SharedHTTPClient.get method
        with patch('shared.http.SharedHTTPClient.get') as mock_get:
            mock_get.return_value = {
                "expires_at": int(
                    (datetime.now() + timedelta(seconds=3600)).timestamp()
                )
                # Missing access_token
            }

            # Should raise AuthenticationError
            with pytest.raises(AuthenticationError)''',
        content,
        flags=re.MULTILINE | re.DOTALL
    )
    
    # Fix default TTL test
    content = re.sub(
        (
            r'        respx\.get\("([^"]+)"\)\.mock\(\s*return_value=httpx\.Response\('
            r'200, json=\{\s*"access_token": "([^"]+)"\s*# Missing expires_at\s*\}\)'
            r'\s*\)'
        ),
        r'''        # Mock the SharedHTTPClient.get method
        with patch('shared.http.SharedHTTPClient.get') as mock_get:
            mock_get.return_value = {
                "access_token": "\2"
                # Missing expires_at
            }''',
        content,
        flags=re.MULTILINE | re.DOTALL
    )
    
    # Fix calendar list test
    content = re.sub(
        (
            r'        # Mock auth-service\s*\n\s*respx\.get\("([^"]+)"\)\.mock\('
            r'\s*return_value=httpx\.Response\(200, json=\{\s*"access_token": '
            r'"([^"]+)",\s*"expires_at": [^}]+\}\)\s*\)\s*\n\s*# Mock Google '
            r'Calendar API\s*\n\s*calendar_mock = respx\.get\("([^"]+)"\)\.mock\('
            r'\s*return_value=httpx\.Response\(200, json=\{[^}]+\}\)\s*\)'
        ),
        r'''        # Mock the SharedHTTPClient methods
        with patch('shared.http.SharedHTTPClient.get') as mock_get, \\
             patch('shared.http.SharedHTTPClient.request') as mock_request:

            # Mock auth-service response
            mock_get.return_value = {
                "access_token": "\2",
                "expires_at": int(
                    (datetime.now() + timedelta(seconds=3600)).timestamp()
                )
            }

            # Mock Google Calendar API response
            mock_request.return_value = {
                "items": [
                    {
                        "id": "primary",
                        "summary": "Test Calendar",
                        "primary": True
                    }
                ]
            }''',
        content,
        flags=re.MULTILINE | re.DOTALL
    )
    
    # Fix the calendar test verification
    content = re.sub(
        (
            r'        # Verify Authorization header was set\s*\n\s*request = '
            r'calendar_mock\.calls\[0\]\.request\s*\n\s*assert "Authorization" '
            r'in request\.headers\s*\n\s*assert request\.headers\["Authorization"\] '
            r'== "Bearer ([^"]+)"'
        ),
        r'''        # Verify Authorization header was set in the request call
        mock_request.assert_called_once()
        call_args = mock_request.call_args
        headers = call_args[1]["headers"]
        assert "Authorization" in headers
        assert headers["Authorization"] == "Bearer \1"''',
        content,
        flags=re.MULTILINE | re.DOTALL
    )
    
    with open('tests/providers/test_google_token_fetch.py', 'w') as f:
        f.write(content)
    
    print("✅ Fixed all Google tests!")

if __name__ == "__main__":
    fix_google_tests()
