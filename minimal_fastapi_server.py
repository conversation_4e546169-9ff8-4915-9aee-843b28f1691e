#!/usr/bin/env python
"""
Minimal FastAPI server for CopilotKit tunnel testing.

This script creates a minimal FastAPI server that exposes the CopilotKit endpoint
for testing the tunnel connection. It loads environment variables from the .env file
and uses the improved environment variable loading mechanism.
"""

# Load environment variables first, before any other imports
import os
import sys
from pathlib import Path
from dotenv import load_dotenv

# Find and load .env file explicitly
env_path = Path(__file__).parent / '.env'
print(f"Loading environment variables from: {env_path}")

# Parse .env file and set environment variables
if env_path.exists():
    # Load .env and override any existing vars to pick up updated values
    load_dotenv(dotenv_path=env_path, override=True)
    print("Environment variables loaded successfully")

    # Print loaded environment variables (excluding sensitive values)
    print("Loaded environment variables:")
    for key, value in os.environ.items():
        if key.startswith((
            'VOYAGE_', 'OPENAI_', 'PINECONE_', 'SUPABASE_', 'CPK_', 'LANGSMITH_'
        )):
            if any(
                sensitive in key.lower()
                for sensitive in ['key', 'password', 'secret', 'token']
            ):
                print(f"  {key}=****")
            else:
                print(f"  {key}={value}")
else:
    print(f"Warning: .env file not found at {env_path}")

# Now import other modules after environment variables are loaded
import uvicorn
import logging
import secrets
import json
from datetime import datetime
from fastapi import FastAPI, Request, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse, StreamingResponse

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
)
logger = logging.getLogger("minimal_fastapi")

app = FastAPI(
    title="Minimal FastAPI Server for CopilotKit",
    description="A minimal FastAPI server for testing CopilotKit tunnel connection",
    version="1.0.0",
)

# Configure CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["GET", "POST", "PUT", "DELETE", "OPTIONS", "PATCH"],
    allow_headers=[
        "Authorization", "Content-Type", "X-CPK-Endpoint-Key", "X-Client-Info"
    ],
    expose_headers=["Content-Length", "Content-Range"],
    max_age=86400,  # 24 hours
)

# CopilotKit Endpoint Key Verification Middleware
@app.middleware("http")
async def verify_endpoint_key(request: Request, call_next):
    """
    Middleware to verify the CopilotKit endpoint key for secure access.
    This protects the /copilotkit and /invoke endpoints used by CopilotKit Cloud.
    """
    # Only check auth for /copilotkit or /invoke paths, but exclude health check and
    # OPTIONS requests
    if ((request.url.path.startswith("/copilotkit") or
         request.url.path.startswith("/invoke")) and
        not request.url.path.endswith("/health") and
        request.method != "OPTIONS"):

        # Log the request path for debugging
        logger.info(f"Received request on protected path: {request.url.path}")

        # Get the endpoint secret from environment
        endpoint_secret = os.getenv("CPK_ENDPOINT_SECRET")

        # If no secret is set or in development mode with default secret, disable auth
        if (not endpoint_secret or
            endpoint_secret == "test-endpoint-secret-123456789"):
            logger.warning(
                "CopilotKit endpoint authentication disabled! "
                "This is insecure and should only be used in development."
            )
            request.state.cpk_auth_disabled = True
        else:
            # Check the header for the endpoint key
            auth_header = request.headers.get("X-CPK-Endpoint-Key")

            # Log attempt with masked key for security
            if not auth_header:
                masked_header = "None"
            elif len(auth_header) > 8:
                masked_header = f"{auth_header[:4]}...{auth_header[-4:]}"
            else:
                masked_header = "********"
            logger.debug(
                f"CopilotKit endpoint key verification attempt with key: "
                f"{masked_header}"
            )

            if not auth_header:
                logger.warning("No CopilotKit endpoint key provided")
                return JSONResponse(
                    status_code=403,
                    content={"detail": "CopilotKit endpoint key required"}
                )

            # Use constant-time comparison to prevent timing attacks
            if not secrets.compare_digest(auth_header, endpoint_secret):
                logger.warning("Invalid CopilotKit endpoint key provided")
                return JSONResponse(
                    status_code=403,
                    content={"detail": "Invalid CopilotKit endpoint key"}
                )

            # Store authentication status in request state
            request.state.cpk_authenticated = True
            logger.debug("CopilotKit endpoint key verification successful")

    # Proceed with the request
    response = await call_next(request)
    return response

@app.post("/copilotkit")
async def copilotkit_endpoint(request: Request):
    """
    CopilotKit endpoint for testing the tunnel connection.
    This endpoint implements the AG-UI protocol for streaming responses.
    """
    try:
        # Parse request body
        body = await request.json()

        # Extract request details
        agent_name = body.get("agent", "test_agent")
        messages = body.get("messages", [])
        stream = body.get("stream", True)

        # Log request
        logger.info(f"Received request on /copilotkit for agent: {agent_name}")
        logger.info(f"Stream: {stream}")
        logger.info(f"Messages: {messages}")

        # Return a simple response
        if stream:
            async def generate_stream():
                # Send a start event
                yield f"data: {json.dumps({'type': 'start'})}\n\n"

                # Send content events
                chunks = [
                    "Hello! ",
                    "I'm a ",
                    "test ",
                    "agent ",
                    "for ",
                    "CopilotKit ",
                    "tunnel ",
                    "testing. ",
                    "The tunnel is working correctly!"
                ]

                for chunk in chunks:
                    yield (
                        f"data: {json.dumps({'type': 'content', 'content': chunk})}\n\n"
                    )

                # Send a done event
                yield f"data: {json.dumps({'type': 'done'})}\n\n"

            return StreamingResponse(
                content=generate_stream(),
                media_type="text/event-stream"
            )
        else:
            return {
                "messages": [
                    {
                        "role": "assistant",
                        "content": (
                            "Hello! I'm a test agent for CopilotKit tunnel testing. "
                            "The tunnel is working correctly!"
                        )
                    }
                ],
                "done": True
            }
    except Exception as e:
        logger.error(
            f"Error processing request on /copilotkit endpoint: {str(e)}"
        )
        return JSONResponse(
            status_code=500,
            content={"error": str(e)}
        )

@app.options("/copilotkit")
async def copilotkit_options():
    """Options endpoint for CORS preflight requests to /copilotkit."""
    return {}

@app.post("/invoke")
async def invoke_endpoint(request: Request):
    """
    LangGraph invoke endpoint for CopilotKit Cloud integration.
    This endpoint implements the LangGraph Cloud API structure.
    """
    try:
        # Parse request body
        body = await request.json()

        # Extract request details
        agent_name = body.get("agent", "test_agent")
        messages = body.get("messages", [])
        stream = body.get("stream", True)

        # Log request
        logger.info(
            f"Received request on /invoke endpoint for agent: {agent_name}"
        )
        logger.info(f"Stream: {stream}")
        logger.info(f"Messages: {messages}")

        # Return a response in LangGraph Cloud format
        if stream:
            async def generate_stream():
                # Send a start event
                yield f"data: {json.dumps({'type': 'start'})}\n\n"

                # Send content events
                chunks = [
                    "Hello! ",
                    "I'm a ",
                    "test ",
                    "agent ",
                    "for ",
                    "CopilotKit ",
                    "LangGraph ",
                    "integration. ",
                    "The /invoke endpoint is working correctly!"
                ]

                for chunk in chunks:
                    yield (
                        f"data: {json.dumps({'type': 'content', 'content': chunk})}\n\n"
                    )

                # Send a done event
                yield f"data: {json.dumps({'type': 'done'})}\n\n"

            return StreamingResponse(
                content=generate_stream(),
                media_type="text/event-stream"
            )
        else:
            return JSONResponse(
                content={
                    "messages": [
                        {
                            "role": "assistant",
                            "content": (
                                "Hello! I'm a test agent for CopilotKit LangGraph "
                                "integration. The /invoke endpoint is working "
                                "correctly!"
                            )
                        }
                    ],
                    "done": True
                },
                media_type="application/json"
            )
    except Exception as e:
        logger.error(
            f"Error processing request on /invoke endpoint: {str(e)}"
        )
        return JSONResponse(
            status_code=500,
            content={"error": str(e)}
        )

@app.options("/invoke")
async def invoke_options():
    """Options endpoint for CORS preflight requests to /invoke."""
    return {}

@app.post("/invoke/assistants/search")
async def assistants_search(request: Request):
    """
    LangGraph assistants search endpoint for CopilotKit Cloud integration.
    This endpoint is required for the LangGraph Cloud API structure.
    """
    try:
        # Parse request body
        body = await request.json()

        # Log request
        logger.info(f"Received request on /invoke/assistants/search endpoint")
        logger.info(f"Request body: {body}")

        # Return a simple response with a list of assistants
        return JSONResponse(
            content={
                "assistants": [
                    {
                        "id": "test_agent",
                        "name": "Test Agent",
                        "description": (
                            "A test agent for CopilotKit LangGraph integration"
                        ),
                        "model": "test-model",
                        "created_at": datetime.now().isoformat()
                    }
                ]
            },
            media_type="application/json"
        )
    except Exception as e:
        logger.error(
            f"Error processing request on /invoke/assistants/search endpoint: {str(e)}"
        )
        return JSONResponse(
            status_code=500,
            content={"error": str(e)}
        )

@app.options("/invoke/assistants/search")
async def assistants_search_options():
    """Options endpoint for CORS preflight requests to /invoke/assistants/search."""
    return {}

@app.get("/health")
async def health_check():
    """
    Health check endpoint for monitoring and container health checks.
    """
    return {
        "status": "ok",
        "version": "1.0.0",
        "environment": os.environ.get("APP_ENV", "development"),
        "timestamp": datetime.now().isoformat(),
        "service": "minimal-fastapi-server"
    }

if __name__ == "__main__":
    port = int(os.environ.get("PORT", 8000))
    uvicorn.run(app, host="0.0.0.0", port=port)
